# Batch Selection DC Generation System - Implementation Plan

## 🎯 **Overview**
Based on the screenshots from the old project, we need to implement a sophisticated batch selection system for DC generation that includes:

1. **Batch Selection Interface** with FIFO/Manual allocation methods
2. **Multi-warehouse inventory allocation**
3. **Real-time batch tracking** with expiry date management
4. **Product-wise allocation** with progress tracking
5. **Integrated warehouse management** with direct DC generation

---

## 📋 **Phase 3: Database Schema Requirements**

### **New Tables Needed:**

#### 1. **batch_selections** table
```sql
CREATE TABLE IF NOT EXISTS batch_selections (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_id TEXT NOT NULL,
    product_id TEXT NOT NULL,
    batch_number TEXT NOT NULL,
    warehouse_id TEXT NOT NULL,
    allocated_quantity REAL NOT NULL,
    selection_method TEXT DEFAULT 'manual', -- 'fifo', 'lifo', 'manual'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    status TEXT DEFAULT 'pending' -- 'pending', 'confirmed', 'cancelled'
);
```

#### 2. **dc_generation_sessions** table
```sql
CREATE TABLE IF NOT EXISTS dc_generation_sessions (
    session_id TEXT PRIMARY KEY,
    order_id TEXT NOT NULL,
    selection_method TEXT NOT NULL,
    warehouse_filter TEXT,
    total_products INTEGER,
    allocated_products INTEGER,
    status TEXT DEFAULT 'in_progress', -- 'in_progress', 'completed', 'cancelled'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT
);
```

#### 3. Enhanced **inventory** table (add columns if missing)
```sql
-- Add these columns to existing inventory table if not present
ALTER TABLE inventory ADD COLUMN batch_number TEXT;
ALTER TABLE inventory ADD COLUMN manufacturing_date DATE;
ALTER TABLE inventory ADD COLUMN expiry_date DATE;
ALTER TABLE inventory ADD COLUMN allocated_quantity REAL DEFAULT 0;
```

---

## 🛠 **Phase 4: Backend Routes Implementation**

### **Core Routes:**

1. **`/orders/<order_id>/select-batch`** (GET/POST)
   - Main batch selection interface
   - Shows order details and product list
   - Handles allocation method selection

2. **`/orders/<order_id>/batch-allocation`** (POST)
   - Process batch allocations
   - Validate quantities and availability
   - Save allocation selections

3. **`/orders/<order_id>/auto-allocate`** (POST)
   - Automatic FIFO/LIFO allocation
   - Smart batch selection based on expiry dates

4. **`/orders/<order_id>/generate-dc`** (POST)
   - Final DC generation after batch selection
   - Create delivery challan with batch details
   - Generate PDF with batch information

5. **`/warehouses/generate-dc/<order_id>`** (GET)
   - Warehouse integration route
   - Redirect to batch selection interface

### **API Endpoints:**

1. **`/api/batches/<product_id>`** (GET)
   - Get available batches for a product
   - Filter by warehouse if specified
   - Return batch details with availability

2. **`/api/allocation-status/<order_id>`** (GET)
   - Get current allocation status
   - Return progress for each product

---

## 🎨 **Phase 5: Frontend Templates**

### **1. Batch Selection Main Page**
**File:** `templates/orders/select_batch.html`

**Features:**
- Order details header
- Batch selection method dropdown (FIFO, Manual)
- Warehouse filter dropdown
- Product-wise allocation interface
- Real-time progress tracking
- Apply/Reset buttons

### **2. Product Allocation Component**
**Features:**
- Product information display
- Available batches table
- Quantity input fields
- "Use Max" buttons
- Allocation progress indicators

### **3. Batch Details Table**
**Columns:**
- Batch Number
- Manufacturing Date
- Expiry Date
- Warehouse
- Available Quantity
- Quantity to Use (input)
- Actions (Use Max button)

### **4. Warehouse Integration**
**File:** `templates/warehouses/index.html` (enhancement)

**Features:**
- "Generate DC" button for approved orders
- Direct link to batch selection interface

---

## ⚙ **Phase 6: Business Logic Implementation**

### **1. Allocation Methods:**

#### **FIFO (First In, First Out):**
- Allocate from oldest batches first
- Consider manufacturing date
- Automatic quantity distribution

#### **Manual Selection:**
- User selects specific batches
- Manual quantity input
- Real-time validation

### **2. Validation Rules:**
- Cannot allocate more than available quantity
- Cannot allocate from expired batches
- Must allocate full order quantity before DC generation
- Warehouse-specific allocation if filter applied

### **3. Progress Tracking:**
- Real-time calculation of allocated vs required quantities
- Visual progress indicators
- Completion status for each product

---

## 🔄 **Implementation Workflow**

### **Step 1: User Access**
1. User clicks "Generate DC" from warehouses page
2. System redirects to `/orders/<order_id>/select-batch`
3. Load order details and product requirements

### **Step 2: Batch Selection**
1. User selects allocation method (FIFO/Manual)
2. Optionally filters by warehouse
3. System loads available batches for each product

### **Step 3: Allocation Process**
1. For FIFO: System auto-allocates using oldest batches
2. For Manual: User manually selects batches and quantities
3. Real-time validation and progress updates

### **Step 4: DC Generation**
1. Verify all products are fully allocated
2. Create delivery challan record
3. Generate PDF with batch details
4. Update inventory allocations
5. Redirect to DC confirmation page

---

## 📊 **Key Features to Implement**

### **1. Real-time Updates:**
- AJAX-based quantity updates
- Live progress calculation
- Instant validation feedback

### **2. Smart Allocation:**
- FIFO algorithm implementation
- Expiry date consideration
- Multi-warehouse optimization

### **3. User Experience:**
- Intuitive interface matching old project
- Clear progress indicators
- Error handling and validation messages

### **4. Integration:**
- Seamless warehouse management integration
- Consistent with existing order workflow
- PDF generation with batch details

---

## 🎯 **Success Criteria**

1. ✅ **Batch Selection Interface** - Matches old project functionality
2. ✅ **FIFO/Manual Allocation** - Working allocation methods
3. ✅ **Multi-warehouse Support** - Warehouse filtering and allocation
4. ✅ **Real-time Progress** - Live allocation tracking
5. ✅ **DC Generation** - Complete challan creation with batch details
6. ✅ **Warehouse Integration** - Direct access from warehouses page

---

**Next Steps:** Proceed to Phase 3 - Database Schema Migration
