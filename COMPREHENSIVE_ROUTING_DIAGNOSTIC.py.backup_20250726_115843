#!/usr/bin/env python3
"""
Comprehensive Flask Routing Diagnostic and Fix
Deep investigation of BuildError: select_batch
"""

import os
import sys
import re
import shutil
from datetime import datetime

def phase1_route_architecture_analysis():
    """Phase 1: Analyze complete routing structure"""
    
    print("🔍 PHASE 1: ROUTE ARCHITECTURE ANALYSIS")
    print("=" * 60)
    
    # Check if app.py has the correct routes
    print("\n1️⃣ Checking direct app routes...")
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            app_content = f.read()
        
        # Check for warehouse_generate_dc route
        if "@app.route('/warehouses/generate-dc/<order_id>')" in app_content:
            print("   ✅ warehouse_generate_dc route found")
        else:
            print("   ❌ warehouse_generate_dc route missing")
        
        # Check for select_batch route
        if "@app.route('/orders/<order_id>/select-batch'" in app_content:
            print("   ✅ select_batch route found")
        else:
            print("   ❌ select_batch route missing")
        
        # Check for problematic blueprint references
        if "select_batch" in app_content:
            print("   ❌ Found problematic blueprint reference in app.py")
            
            # Find the exact lines
            lines = app_content.split('\n')
            for i, line in enumerate(lines, 1):
                if "select_batch" in line:
                    print(f"      Line {i}: {line.strip()}")
        else:
            print("   ✅ No problematic blueprint references in app.py")
            
    except Exception as e:
        print(f"   ❌ Error reading app.py: {e}")
    
    # Check blueprint registration
    print("\n2️⃣ Checking blueprint registration...")
    
    if "app.register_blueprint(batch_selection_bp)" in app_content:
        print("   ❌ batch_selection_bp is registered (conflict!)")
    else:
        print("   ✅ batch_selection_bp is not registered")
    
    if "# Batch selection routes will be added directly to app.py" in app_content:
        print("   ✅ Comment confirms direct routes are used")
    else:
        print("   ⚠️ No comment about direct routes")

def phase2_database_integration_investigation():
    """Phase 2: Analyze database integration"""
    
    print("\n🔍 PHASE 2: DATABASE INTEGRATION INVESTIGATION")
    print("=" * 60)
    
    print("\n1️⃣ Checking database schema...")
    
    try:
        import sqlite3
        
        # Check if database exists
        if os.path.exists('instance/medivent.db'):
            print("   ✅ Database file exists")
            
            conn = sqlite3.connect('instance/medivent.db')
            cursor = conn.cursor()
            
            # Check for batch_selections table
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='batch_selections'")
            if cursor.fetchone():
                print("   ✅ batch_selections table exists")
                
                # Check table structure
                cursor.execute("PRAGMA table_info(batch_selections)")
                columns = cursor.fetchall()
                print(f"   📊 Table has {len(columns)} columns")
                
            else:
                print("   ❌ batch_selections table missing")
            
            conn.close()
            
        else:
            print("   ❌ Database file missing")
            
    except Exception as e:
        print(f"   ❌ Database check error: {e}")

def phase3_api_endpoints_mapping():
    """Phase 3: Map API endpoints and integration"""
    
    print("\n🔍 PHASE 3: API ENDPOINTS AND INTEGRATION MAPPING")
    print("=" * 60)
    
    print("\n1️⃣ Scanning for url_for() calls...")
    
    problematic_calls = []
    
    # Scan all Python and HTML files
    for root, dirs, files in os.walk('.'):
        if '__pycache__' in root or '.git' in root:
            continue
            
        for file in files:
            if file.endswith(('.py', '.html')):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        lines = content.split('\n')
                    
                    for i, line in enumerate(lines, 1):
                        if "select_batch" in line:
                            problematic_calls.append({
                                'file': file_path,
                                'line': i,
                                'content': line.strip()
                            })
                            
                except Exception:
                    continue
    
    if problematic_calls:
        print(f"   ❌ Found {len(problematic_calls)} problematic url_for() calls:")
        for call in problematic_calls:
            print(f"      {call['file']}:{call['line']} - {call['content']}")
    else:
        print("   ✅ No problematic url_for() calls found")
    
    return problematic_calls

def phase4_comprehensive_fix(problematic_calls):
    """Phase 4: Apply comprehensive fix"""
    
    print("\n🔍 PHASE 4: COMPREHENSIVE FIX STRATEGY")
    print("=" * 60)
    
    if not problematic_calls:
        print("   ✅ No fixes needed - all references are correct")
        return True
    
    print(f"\n1️⃣ Fixing {len(problematic_calls)} problematic references...")
    
    fixes_applied = 0
    
    for call in problematic_calls:
        file_path = call['file']
        
        # Skip test files and documentation
        if any(skip in file_path.lower() for skip in ['test', 'doc', '.md', 'readme']):
            print(f"   ⏭️ Skipping {file_path} (test/doc file)")
            continue
        
        try:
            # Create backup
            backup_path = f"{file_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            shutil.copy2(file_path, backup_path)
            
            # Read and fix content
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Apply fix
            fixed_content = content.replace(
                "select_batch",
                "select_batch"
            )
            
            # Write fixed content
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(fixed_content)
            
            print(f"   ✅ Fixed {file_path}")
            fixes_applied += 1
            
        except Exception as e:
            print(f"   ❌ Error fixing {file_path}: {e}")
    
    print(f"\n📊 Applied {fixes_applied} fixes")
    return fixes_applied > 0

def phase5_verification_and_testing():
    """Phase 5: Verify fixes and test routes"""
    
    print("\n🔍 PHASE 5: VERIFICATION AND TESTING")
    print("=" * 60)
    
    print("\n1️⃣ Verifying Flask route registration...")
    
    try:
        # Import app to check routes
        sys.path.insert(0, '.')
        from app import app
        
        with app.app_context():
            rules = list(app.url_map.iter_rules())
            endpoints = [rule.endpoint for rule in rules]
            
            required_routes = ['warehouse_generate_dc', 'select_batch']
            
            for route in required_routes:
                if route in endpoints:
                    print(f"   ✅ Route '{route}' is registered")
                else:
                    print(f"   ❌ Route '{route}' is missing")
            
            # Check for conflicting blueprint routes
            blueprint_routes = [ep for ep in endpoints if 'batch_selection.' in ep]
            if blueprint_routes:
                print(f"   ⚠️ Found blueprint routes: {blueprint_routes}")
            else:
                print("   ✅ No conflicting blueprint routes")
                
    except Exception as e:
        print(f"   ❌ Route verification error: {e}")
    
    print("\n2️⃣ Testing route accessibility...")
    
    try:
        import requests
        
        base_url = "http://localhost:3000"
        
        # Test warehouse page
        response = requests.get(f"{base_url}/warehouses", timeout=5)
        if response.status_code == 200:
            print("   ✅ Warehouse page accessible")
        else:
            print(f"   ⚠️ Warehouse page status: {response.status_code}")
        
        # Test redirect route
        test_order_id = "TEST001"
        response = requests.get(f"{base_url}/warehouses/generate-dc/{test_order_id}", 
                              allow_redirects=False, timeout=5)
        
        if response.status_code in [302, 301]:
            redirect_location = response.headers.get('Location', '')
            print(f"   ✅ Redirect route working: {response.status_code}")
            
            if 'select-batch' in redirect_location:
                print("   ✅ Redirects to batch selection correctly")
            elif 'login' in redirect_location:
                print("   ✅ Redirects to login (authentication required - normal)")
            else:
                print(f"   ⚠️ Unexpected redirect: {redirect_location}")
        else:
            print(f"   ❌ Redirect route failed: {response.status_code}")
            
    except ImportError:
        print("   ⚠️ Requests module not available for testing")
    except Exception as e:
        print(f"   ❌ Testing error: {e}")

def main():
    """Main diagnostic and fix function"""
    
    print("🚨 COMPREHENSIVE FLASK ROUTING DIAGNOSTIC")
    print("=" * 80)
    print("Deep investigation of BuildError: select_batch")
    print("=" * 80)
    
    # Phase 1: Route Architecture Analysis
    phase1_route_architecture_analysis()
    
    # Phase 2: Database Integration Investigation
    phase2_database_integration_investigation()
    
    # Phase 3: API Endpoints and Integration Mapping
    problematic_calls = phase3_api_endpoints_mapping()
    
    # Phase 4: Comprehensive Fix Strategy
    fixes_applied = phase4_comprehensive_fix(problematic_calls)
    
    # Phase 5: Verification and Testing
    phase5_verification_and_testing()
    
    # Summary
    print("\n📊 DIAGNOSTIC SUMMARY")
    print("=" * 40)
    
    if fixes_applied:
        print("✅ Issues found and fixed")
        print("🔄 Restart Flask application to apply changes")
    else:
        print("✅ No issues found - routing should be working")
    
    print("\n🎯 NEXT STEPS:")
    print("1. Restart Flask application")
    print("2. Clear browser cache")
    print("3. Test DC generation workflow")
    print("4. Verify no BuildError exceptions")

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
