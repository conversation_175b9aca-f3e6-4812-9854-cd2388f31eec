#!/usr/bin/env python3
"""
Comprehensive Fix for Flask Routing BuildError
Fixes all references to select_batch
"""

import os
import re
import shutil
from datetime import datetime

def backup_file(file_path):
    """Create backup of file before modification"""
    backup_path = f"{file_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    try:
        shutil.copy2(file_path, backup_path)
        print(f"   📁 Backup created: {backup_path}")
        return True
    except Exception as e:
        print(f"   ❌ Backup failed: {e}")
        return False

def fix_file_content(file_path, search_pattern, replacement):
    """Fix content in a file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if search_pattern in content:
            print(f"   🔧 Fixing: {file_path}")
            backup_file(file_path)
            
            new_content = content.replace(search_pattern, replacement)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"   ✅ Fixed: {file_path}")
            return True
        else:
            print(f"   ✅ Already correct: {file_path}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error fixing {file_path}: {e}")
        return False

def fix_routing_conflicts():
    """Fix all routing conflicts in the application"""
    
    print("🔧 COMPREHENSIVE ROUTING CONFLICT FIX")
    print("=" * 50)
    
    fixes_applied = 0
    
    # Define all potential problematic patterns and their fixes
    fixes = [
        {
            'pattern': "url_for('select_batch'",
            'replacement': "url_for('select_batch'",
            'description': "Blueprint route to direct route"
        },
        {
            'pattern': 'select_batch',
            'replacement': 'select_batch',
            'description': "Blueprint endpoint to direct endpoint"
        }
    ]
    
    # Files to check and fix
    files_to_check = [
        'app.py',
        'routes/batch_selection.py',
        'templates/warehouses/index.html',
        'templates/warehouse/dc_pending.html',
        'templates/warehouse/warehouses.html',
        'templates/orders/index.html',
        'templates/orders/select_batch.html'
    ]
    
    print("\n1️⃣ Checking and fixing Python files...")
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"\n📄 Processing: {file_path}")
            
            for fix in fixes:
                if fix_file_content(file_path, fix['pattern'], fix['replacement']):
                    fixes_applied += 1
                    print(f"   ✅ Applied fix: {fix['description']}")
        else:
            print(f"   ⚠️ File not found: {file_path}")
    
    # Check for any remaining references
    print("\n2️⃣ Scanning for remaining references...")
    remaining_refs = []
    
    for root, dirs, files in os.walk('.'):
        # Skip certain directories
        if any(skip in root for skip in ['__pycache__', '.git', 'node_modules', 'venv']):
            continue
            
        for file in files:
            if file.endswith(('.py', '.html')):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    if 'select_batch' in content:
                        remaining_refs.append(file_path)
                        
                except Exception:
                    continue
    
    if remaining_refs:
        print("   ⚠️ Found remaining references:")
        for ref in remaining_refs:
            print(f"     - {ref}")
    else:
        print("   ✅ No remaining references found")
    
    # Clear Python cache
    print("\n3️⃣ Clearing Python cache...")
    try:
        for root, dirs, files in os.walk('.'):
            if '__pycache__' in dirs:
                cache_path = os.path.join(root, '__pycache__')
                shutil.rmtree(cache_path)
                print(f"   🗑️ Removed: {cache_path}")
        print("   ✅ Python cache cleared")
    except Exception as e:
        print(f"   ⚠️ Cache clearing error: {e}")
    
    # Verify route registration
    print("\n4️⃣ Verifying route registration...")
    try:
        import sys
        sys.path.insert(0, '.')
        
        from app import app
        
        with app.app_context():
            rules = list(app.url_map.iter_rules())
            endpoints = [rule.endpoint for rule in rules]
            
            required_routes = ['warehouse_generate_dc', 'select_batch']
            missing_routes = []
            
            for route in required_routes:
                if route in endpoints:
                    print(f"   ✅ Route registered: {route}")
                else:
                    missing_routes.append(route)
                    print(f"   ❌ Route missing: {route}")
            
            if not missing_routes:
                print("   ✅ All required routes are registered")
            else:
                print(f"   ❌ Missing routes: {missing_routes}")
                
    except Exception as e:
        print(f"   ❌ Route verification error: {e}")
    
    print(f"\n📊 SUMMARY")
    print("=" * 30)
    print(f"Fixes applied: {fixes_applied}")
    print(f"Remaining references: {len(remaining_refs)}")
    
    if fixes_applied > 0:
        print("\n🎉 FIXES APPLIED SUCCESSFULLY!")
        print("Restart the Flask application to apply changes.")
    else:
        print("\n✅ NO FIXES NEEDED")
        print("All routing references are already correct.")
    
    return fixes_applied > 0

def test_routes():
    """Test if routes are working correctly"""
    
    print("\n🧪 TESTING ROUTES")
    print("=" * 30)
    
    try:
        import requests
        
        base_url = "http://localhost:3000"
        
        # Test warehouse page
        print("1️⃣ Testing warehouse page...")
        try:
            response = requests.get(f"{base_url}/warehouses", timeout=5)
            if response.status_code == 200:
                print("   ✅ Warehouse page accessible")
            else:
                print(f"   ⚠️ Warehouse page status: {response.status_code}")
        except Exception as e:
            print(f"   ❌ Warehouse page error: {e}")
        
        # Test redirect route
        print("2️⃣ Testing redirect route...")
        try:
            test_order_id = "TEST001"
            response = requests.get(f"{base_url}/warehouses/generate-dc/{test_order_id}", 
                                  allow_redirects=False, timeout=5)
            if response.status_code in [302, 301]:
                print("   ✅ Redirect route working")
                print(f"   📍 Redirects to: {response.headers.get('Location', 'Unknown')}")
            else:
                print(f"   ⚠️ Redirect route status: {response.status_code}")
        except Exception as e:
            print(f"   ❌ Redirect route error: {e}")
        
        print("\n✅ Route testing completed")
        
    except ImportError:
        print("   ⚠️ Requests module not available for testing")
    except Exception as e:
        print(f"   ❌ Testing error: {e}")

if __name__ == "__main__":
    print("🚨 FLASK ROUTING BUILDERROR FIX")
    print("=" * 50)
    print("This script will fix all routing conflicts in your ERP application")
    print("=" * 50)
    
    # Apply fixes
    fixes_applied = fix_routing_conflicts()
    
    if fixes_applied:
        print("\n🔄 NEXT STEPS:")
        print("1. Restart your Flask application")
        print("2. Test the DC generation workflow")
        print("3. Verify no BuildError exceptions occur")
        
        restart = input("\nWould you like to test the routes now? (y/n): ").lower().strip()
        if restart == 'y':
            test_routes()
    
    print("\n✅ ROUTING FIX COMPLETED")
    input("Press Enter to exit...")
