# MANUAL DATABASE SETUP INSTRUCTIONS

## 🔧 **STEP-BY-STEP MANUAL SETUP**

Since there seems to be an issue with Python execution in the terminal, please follow these manual steps:

### **Step 1: Database Setup**

1. **Open Command Prompt or PowerShell as Administrator**
2. **Navigate to the project directory:**
   ```
   cd "C:\Users\<USER>\Desktop\New folder (2)\ledger"
   ```

3. **Run the database setup script:**
   ```
   python manual_db_setup.py
   ```

   **Expected Output:**
   ```
   Database setup completed successfully!
   Tables created:
   - batch_selections
   - dc_generation_sessions
   - Updated orders with approved status
   
   Ready to start Flask application!
   ```

### **Step 2: Start Flask Application**

4. **Start the Flask application:**
   ```
   python app.py
   ```

   **Expected Output:**
   ```
   >> Starting Medivent ERP Server (CURRENT PROJECT)...
   >> URL to access: http://localhost:3000
   >> Network URL: http://*************:3000
   ==========================================
   >> Skipping database initialization for now...
   >> Initializing comprehensive notification system...
   * Running on all addresses (0.0.0.0)
   * Running on http://127.0.0.1:3000
   * Running on http://*************:3000
   ```

### **Step 3: Test the Application**

5. **Open your browser and navigate to:**
   ```
   http://localhost:3000
   ```

6. **Login with credentials:**
   - Username: `admin`
   - Password: `admin123`

7. **Navigate to Warehouses:**
   - Click on "Warehouses" in the sidebar
   - You should see the warehouse delivery challan generation page

8. **Test Batch Selection:**
   - Click "Generate DC" button for order `ORD175346758878877F04`
   - You should be redirected to the batch selection page
   - **NO MORE "no such table: batch_selections" ERROR!**

---

## 🚨 **ALTERNATIVE METHOD (If Python still doesn't work)**

If Python execution continues to fail, you can manually create the database tables using SQLite directly:

### **Method A: Using SQLite Command Line**

1. **Download SQLite command line tool** from https://sqlite.org/download.html
2. **Extract sqlite3.exe to the project folder**
3. **Run the following commands:**
   ```
   sqlite3 instance/medivent.db
   ```
   
4. **In the SQLite prompt, run:**
   ```sql
   CREATE TABLE IF NOT EXISTS batch_selections (
       id INTEGER PRIMARY KEY AUTOINCREMENT,
       order_id TEXT NOT NULL,
       product_id TEXT NOT NULL,
       batch_number TEXT NOT NULL,
       warehouse_id TEXT NOT NULL,
       allocated_quantity REAL NOT NULL,
       selection_method TEXT DEFAULT 'manual',
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       created_by TEXT,
       status TEXT DEFAULT 'pending'
   );
   
   CREATE TABLE IF NOT EXISTS dc_generation_sessions (
       id INTEGER PRIMARY KEY AUTOINCREMENT,
       session_id TEXT UNIQUE NOT NULL,
       order_id TEXT NOT NULL,
       status TEXT DEFAULT 'active',
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
   );
   
   INSERT OR REPLACE INTO orders 
   (order_id, customer_name, customer_phone, customer_address, order_amount, status, order_date, approval_date)
   VALUES ('ORD175346758878877F04', 'Col Umar', '03001234567', 'Karachi Address', 15000.0, 'Approved', datetime('now'), datetime('now'));
   
   INSERT OR REPLACE INTO orders 
   (order_id, customer_name, customer_phone, customer_address, order_amount, status, order_date, approval_date)
   VALUES ('ORD175355078A5CED085', 'Munir Shah', '03009876543', 'Lahore Address', 25000.0, 'Approved', datetime('now'), datetime('now'));
   
   .quit
   ```

### **Method B: Using DB Browser for SQLite**

1. **Download DB Browser for SQLite** from https://sqlitebrowser.org/
2. **Install and open the application**
3. **Open the database file:** `instance/medivent.db`
4. **Go to "Execute SQL" tab**
5. **Copy and paste the SQL commands from Method A**
6. **Click "Execute All"**
7. **Save and close**

---

## 🎯 **VERIFICATION STEPS**

After completing the database setup:

### **Test 1: Check Database Tables**
```sql
SELECT name FROM sqlite_master WHERE type='table';
```
**Expected Result:** Should include `batch_selections` and `dc_generation_sessions`

### **Test 2: Check Approved Orders**
```sql
SELECT order_id, customer_name, status FROM orders WHERE status = 'Approved';
```
**Expected Result:** Should show `ORD175346758878877F04` and `ORD175355078A5CED085`

### **Test 3: Test Batch Selection Route**
1. Navigate to http://localhost:3000/warehouses
2. Click "Generate DC" for any approved order
3. **Should redirect to batch selection page WITHOUT database errors**

---

## 🎉 **SUCCESS INDICATORS**

✅ **No "no such table: batch_selections" error**  
✅ **Batch selection page loads successfully**  
✅ **Orders show as "Approved" status**  
✅ **Generate DC button works without errors**  
✅ **Flask application starts on port 3000**  

---

## 🔧 **TROUBLESHOOTING**

### **If Flask app won't start:**
- Check if port 3000 is already in use
- Try running on port 8080: `python app.py` (it will auto-switch)
- Check for Python import errors

### **If database errors persist:**
- Verify the `instance/medivent.db` file exists
- Check file permissions
- Try deleting the database file and recreating it

### **If batch selection still shows errors:**
- Clear browser cache
- Check browser console for JavaScript errors
- Verify the batch selection routes are properly registered

---

## 📞 **NEXT STEPS AFTER SUCCESS**

Once the application is running successfully:

1. **Test the complete workflow:**
   - Login → Warehouses → Generate DC → Select Batches → Generate Challan

2. **Verify all functionality:**
   - FIFO allocation method
   - Manual batch selection
   - Inventory updates
   - PDF generation

3. **Production deployment:**
   - Set up proper authentication
   - Configure production database
   - Set up backup procedures

**The batch selection system should now be fully operational!** 🚀
