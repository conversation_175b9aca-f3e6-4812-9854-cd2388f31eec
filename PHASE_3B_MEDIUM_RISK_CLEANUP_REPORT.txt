PHASE 3B: MEDIUM-RISK CLEANUP REPORT
==================================================

Timestamp: 2025-07-17T12:37:59.139196
Backup Created: True

Duplicate Tables Consolidated: 2
  - Removed duplicate: bug_reports (kept ai_bug_reports)
  - Removed duplicate: error_patterns (kept ai_error_patterns)

Backup Templates Removed: 3
  - templates\finance_backup_20250713_033521 (32 files)
  - templates\orders\index_backup_before_ui_restore.html
  - templates\products\new_backup_original.html

Duplicate Utilities Cleaned: 0

Remaining Similar Files: 1
  - routes\__init__.py <-> utils\__init__.py

Cleanup Log:
  - Phase 3B backup created: instance/medivent_backup_phase3b_20250717_123759.db
  - Consolidated tables: removed bug_reports, kept ai_bug_reports
  - Consolidated tables: removed error_patterns, kept ai_error_patterns
  - Removed backup template directory: templates\finance_backup_20250713_033521
  - Removed backup template file: templates\orders\index_backup_before_ui_restore.html
  - Removed backup template file: templates\products\new_backup_original.html
  - Error removing templates\orders\index_backup_before_ui_restore.html: [WinError 2] The system cannot find the file specified: 'templates\\orders\\index_backup_before_ui_restore.html'
  - Error removing templates\products\new_backup_original.html: [WinError 2] The system cannot find the file specified: 'templates\\products\\new_backup_original.html'
