# TCS Express Tracking Integration Guide

## Overview

This guide documents the integration of TCS Express tracking functionality into your existing ERP system. The integration provides both web interface and API endpoints for tracking TCS packages without requiring database storage.

## 🚀 Quick Start

### 1. Install Dependencies

```bash
# Install Playwright for web scraping
pip install playwright

# Install browser binaries
playwright install chromium
```

### 2. Start the Server

```bash
# Option 1: Use the startup script
python start_tracking_server.py

# Option 2: Start manually
python app.py
```

### 3. Access Tracking

- **Web Interface**: http://localhost:5000/track
- **Navigation**: Look for "TCS Tracking" in the sidebar menu

## 📁 Files Added/Modified

### New Files Created:
- `routes/tracking.py` - Main tracking blueprint with all endpoints
- `templates/tracking/track_form.html` - Web form for tracking
- `templates/tracking/track_result.html` - Results display page
- `start_tracking_server.py` - Startup script with dependency checking
- `test_tracking.py` - Test script for validation

### Modified Files:
- `requirements.txt` - Added Playwright dependency
- `app.py` - Registered tracking blueprint
- `templates/base.html` - Added navigation menu item

## 🌐 API Endpoints

### 1. Web Form Interface
- **URL**: `/track`
- **Method**: GET
- **Description**: Display tracking form
- **Response**: HTML page with tracking form

### 2. Single Package Tracking (URL Parameter)
- **URL**: `/track/<tracking_number>`
- **Method**: GET
- **Description**: Track a single package via URL
- **Example**: `/track/TCS123456789`
- **Response**: JSON with tracking data

### 3. Single Package Tracking (Form/JSON)
- **URL**: `/track`
- **Method**: POST
- **Content-Type**: `application/json` or `application/x-www-form-urlencoded`
- **Body**: 
  ```json
  {
    "tracking_number": "TCS123456789"
  }
  ```
- **Response**: JSON or HTML based on request type

### 4. Bulk Tracking
- **URL**: `/api/track/bulk`
- **Method**: POST
- **Content-Type**: `application/json`
- **Body**:
  ```json
  {
    "tracking_numbers": ["TCS123456789", "TCS987654321"]
  }
  ```
- **Limit**: Maximum 10 tracking numbers per request
- **Response**: JSON with summary and individual results

### 5. Single PDF Generation
- **URL**: `/api/track/pdf`
- **Method**: POST
- **Content-Type**: `application/x-www-form-urlencoded`
- **Body**: `tracking_data=<JSON_DATA>`
- **Response**: PDF file download

### 6. Bulk PDF Generation
- **URL**: `/api/track/bulk/pdf`
- **Method**: POST
- **Content-Type**: `application/x-www-form-urlencoded`
- **Body**: `tracking_data=<JSON_DATA>`
- **Response**: PDF file download with summary and all results

## 📊 Response Format

### Success Response
```json
{
  "status": "success",
  "tracking_number": "TCS123456789",
  "scrape_timestamp": "2025-01-19T08:48:00.123456",
  "scrape_duration": "0:00:03.456789",
  "data": {
    "booking_details": {
      "tracking_number": "TCS123456789",
      "agent_reference": "REF123",
      "origin": "Karachi",
      "destination": "Lahore",
      "booking_date": "2025-01-18"
    },
    "track_summary": {
      "current_status": "In Transit",
      "delivered_on": "N/A",
      "received_by": "N/A"
    },
    "status_history": [
      {
        "date_time": "2025-01-18 10:30 AM",
        "status": "Package picked up",
        "timestamp": "2025-01-19T08:48:00.123456"
      }
    ]
  }
}
```

### Not Found Response
```json
{
  "status": "not_found",
  "tracking_number": "TCS123456789"
}
```

### Error Response
```json
{
  "status": "error",
  "error": "Error message details",
  "tracking_number": "TCS123456789",
  "timestamp": "2025-01-19T08:48:00.123456"
}
```

### Bulk Response
```json
{
  "status": "completed",
  "summary": {
    "total_processed": 3,
    "successful": 2,
    "not_found": 1,
    "errors": 0
  },
  "results": [
    // Array of individual tracking results
  ],
  "timestamp": "2025-01-19T08:48:00.123456"
}
```

## 🔧 Technical Implementation

### Core Function
The main scraping function `scrape_tcs_tracking()` in `routes/tracking.py`:
- Uses Playwright for web scraping
- Handles timeouts and errors gracefully
- Extracts booking details, current status, and history
- Returns structured JSON data

### Error Handling
- **Playwright not installed**: Returns error message with installation instructions
- **Invalid tracking number**: Returns "not_found" status
- **Network/timeout errors**: Returns error with details
- **Parsing errors**: Graceful fallback with partial data

### Rate Limiting
- Bulk requests limited to 10 tracking numbers
- 1-second delay between bulk requests to avoid rate limiting

## 🎨 User Interface

### Web Form Features
- Single tracking input with instant results
- **Enhanced bulk tracking with beautiful card-based display**
- **Clickable tracking numbers that open detailed view in new tabs**
- **Visual summary statistics with color-coded metrics**
- **PDF download for both single and bulk results**
- Individual JSON download for each tracking result
- Responsive design matching your ERP theme
- Error handling with user-friendly messages
- **"Not Available" display instead of "N/A" for better UX**

### Navigation Integration
- Added "TCS Tracking" menu item in sidebar
- Consistent styling with existing ERP interface
- Active state highlighting for tracking pages

## 🧪 Testing

### Run Tests
```bash
python test_tracking.py
```

### Manual Testing
1. Start the server: `python start_tracking_server.py`
2. Navigate to: http://localhost:5000/track
3. Test with sample tracking numbers
4. Test API endpoints with curl or Postman

### API Testing Examples
```bash
# Single tracking via URL
curl http://localhost:5000/track/TCS123456789

# Single tracking via POST
curl -X POST http://localhost:5000/track \
  -H "Content-Type: application/json" \
  -d '{"tracking_number": "TCS123456789"}'

# Bulk tracking
curl -X POST http://localhost:5000/api/track/bulk \
  -H "Content-Type: application/json" \
  -d '{"tracking_numbers": ["TCS123456789", "TCS987654321"]}'
```

## 🔒 Security Considerations

- No sensitive data stored in database
- Input validation for tracking numbers
- Rate limiting to prevent abuse
- Error messages don't expose internal details
- Headless browser execution for security

## 🚀 Performance

- Headless browser for faster execution
- Concurrent handling of multiple requests
- Efficient DOM parsing with specific selectors
- Timeout handling to prevent hanging requests
- Memory cleanup after each scraping session

## 🛠️ Troubleshooting

### Common Issues

1. **Playwright not installed**
   ```bash
   pip install playwright
   playwright install chromium
   ```

2. **Browser not found**
   ```bash
   playwright install chromium
   ```

3. **Permission errors**
   - Run as administrator on Windows
   - Check firewall settings

4. **Timeout errors**
   - Check internet connection
   - TCS website might be slow/down

### Debug Mode
Set `headless=False` in `scrape_tcs_tracking()` to see browser actions.

## 📈 Future Enhancements

- Support for other courier services
- Caching mechanism for recent tracking results
- Email notifications for status changes
- Integration with order management system
- Tracking history storage (optional)

## 📞 Support

For issues or questions:
1. Check the troubleshooting section
2. Review error messages in browser console
3. Test with known working tracking numbers
4. Verify Playwright installation

---

**Note**: This integration is stateless and doesn't store tracking data in your database. All data is fetched in real-time from TCS Express website.
