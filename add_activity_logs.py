import sqlite3
from datetime import datetime, timedelta

# Add some sample activity logs for the order
conn = sqlite3.connect('instance/medivent.db')
cursor = conn.cursor()

order_id = 'ORD175397491316416F32'

# Add activity logs for the order lifecycle
activities = [
    ('admin', 'Order Placed', 'Order placed by admin for customer Test2346999', 'orders', '2025-07-31 08:15:13'),
    ('admin', 'Order Rejected', 'Order rejected by admin. Notes: rejeccttttt', 'orders', '2025-07-31 09:28:49'),
]

print('Adding activity logs...')
for username, action, details, module, timestamp in activities:
    try:
        cursor.execute('''
            INSERT INTO activity_logs (username, action, entity_id, details, module, timestamp)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (username, action, order_id, details, module, timestamp))
        print(f'✅ Added: {action} by {username}')
    except Exception as e:
        print(f'❌ Error adding {action}: {e}')

conn.commit()

# Verify the logs were added
cursor.execute('SELECT * FROM activity_logs WHERE entity_id = ? ORDER BY timestamp', (order_id,))
logs = cursor.fetchall()
print(f'\nTotal activity logs for order {order_id}: {len(logs)}')
for log in logs:
    print(f'   {log[1]} - {log[3]} by {log[2]} - {log[4]}')

conn.close()
print('\n✅ Activity logs added successfully!')
