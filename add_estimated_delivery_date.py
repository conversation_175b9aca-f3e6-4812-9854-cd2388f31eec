#!/usr/bin/env python3
"""
Add estimated_delivery_date field to orders table
"""

import sqlite3
import sys
from datetime import datetime

def add_estimated_delivery_date_field():
    """Add estimated_delivery_date field to orders table"""
    try:
        # Connect to database
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        # Check if column already exists
        cursor.execute("PRAGMA table_info(orders)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'estimated_delivery_date' not in columns:
            print("Adding estimated_delivery_date column to orders table...")
            cursor.execute('''
                ALTER TABLE orders 
                ADD COLUMN estimated_delivery_date TIMESTAMP
            ''')
            
            # Set estimated delivery date for existing approved orders (3 days from approval)
            cursor.execute('''
                UPDATE orders 
                SET estimated_delivery_date = datetime(approval_date, '+3 days')
                WHERE status = 'Approved' AND approval_date IS NOT NULL
            ''')
            
            conn.commit()
            print("✅ Successfully added estimated_delivery_date column")
            
            # Show updated orders
            cursor.execute('''
                SELECT order_id, status, approval_date, estimated_delivery_date 
                FROM orders 
                WHERE estimated_delivery_date IS NOT NULL
                LIMIT 5
            ''')
            
            results = cursor.fetchall()
            if results:
                print("\n📋 Sample orders with estimated delivery dates:")
                for row in results:
                    print(f"  Order {row[0]}: Status={row[1]}, Approved={row[2]}, Est.Delivery={row[3]}")
            
        else:
            print("✅ estimated_delivery_date column already exists")
            
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error adding estimated_delivery_date field: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

if __name__ == "__main__":
    success = add_estimated_delivery_date_field()
    sys.exit(0 if success else 1)
