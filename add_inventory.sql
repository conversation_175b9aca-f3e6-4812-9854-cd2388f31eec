-- Add sample inventory for testing batch selection

-- First, check if inventory exists
SELECT 'Existing inventory:' as info;
SELECT product_id, warehouse_id, batch_number, stock_quantity, allocated_quantity, status 
FROM inventory 
WHERE product_id IN ('P001', 'P002');

-- Add inventory for P001 (Fulvestrant)
INSERT OR REPLACE INTO inventory 
(inventory_id, product_id, warehouse_id, batch_number, stock_quantity, allocated_quantity, 
 manufacturing_date, expiry_date, status, created_at, updated_at)
VALUES 
('INV001', 'P001', 'WH001', 'BATCH001', 100, 0, '2024-06-01', '2025-06-01', 'active', datetime('now'), datetime('now')),
('INV002', 'P001', 'WH001', 'BATCH002', 75, 0, '2024-06-15', '2025-06-15', 'active', datetime('now'), datetime('now')),
('INV003', 'P001', 'WH002', 'BATCH003', 50, 0, '2024-07-01', '2025-07-01', 'active', datetime('now'), datetime('now'));

-- Add inventory for P002 (Arimex)  
INSERT OR REPLACE INTO inventory 
(inventory_id, product_id, warehouse_id, batch_number, stock_quantity, allocated_quantity, 
 manufacturing_date, expiry_date, status, created_at, updated_at)
VALUES 
('INV004', 'P002', 'WH001', 'BATCH004', 80, 0, '2024-06-01', '2025-06-01', 'active', datetime('now'), datetime('now')),
('INV005', 'P002', 'WH002', 'BATCH005', 60, 0, '2024-06-15', '2025-06-15', 'active', datetime('now'), datetime('now'));

-- Verify the inventory was added
SELECT 'New inventory:' as info;
SELECT product_id, warehouse_id, batch_number, stock_quantity, allocated_quantity, status 
FROM inventory 
WHERE product_id IN ('P001', 'P002')
ORDER BY product_id, warehouse_id, batch_number;
