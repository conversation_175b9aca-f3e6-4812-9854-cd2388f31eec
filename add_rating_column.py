#!/usr/bin/env python3
"""
Add missing 'rating' column to orders table
"""

import sqlite3
import os

def add_rating_column():
    """Add rating column to orders table if it doesn't exist"""
    
    db_path = 'instance/medivent.db'
    
    if not os.path.exists(db_path):
        print(f"❌ Database not found: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if rating column already exists
        cursor.execute("PRAGMA table_info(orders)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        if 'rating' in column_names:
            print("✅ 'rating' column already exists in orders table")
            conn.close()
            return True
        
        print("🔧 Adding 'rating' column to orders table...")
        
        # Add the rating column with default value
        cursor.execute("""
            ALTER TABLE orders 
            ADD COLUMN rating REAL DEFAULT 4.0
        """)
        
        # Commit the changes
        conn.commit()
        
        # Verify the column was added
        cursor.execute("PRAGMA table_info(orders)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        if 'rating' in column_names:
            print("✅ Successfully added 'rating' column to orders table")
            print("   Default value: 4.0 (REAL type)")
            
            # Show updated schema
            print("\n📋 Updated orders table schema:")
            for col in columns:
                if col[1] == 'rating':
                    print(f"   ➕ {col[1]} ({col[2]}) - NEWLY ADDED")
                else:
                    print(f"   - {col[1]} ({col[2]})")
            
            conn.close()
            return True
        else:
            print("❌ Failed to add 'rating' column")
            conn.close()
            return False
            
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def verify_rating_queries():
    """Verify that rating queries will now work"""
    
    db_path = 'instance/medivent.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\n🧪 Testing rating column queries...")
        
        # Test the exact query from modern_riders.py
        test_query = """
            SELECT 
                ROUND(AVG(CASE WHEN o.rating THEN o.rating ELSE 4.0 END), 2) as avg_rating
            FROM orders o 
            LIMIT 1
        """
        
        cursor.execute(test_query)
        result = cursor.fetchone()
        
        if result:
            print(f"✅ Rating query successful! Average rating: {result[0]}")
        else:
            print("⚠️ Rating query returned no results (table might be empty)")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Rating query test failed: {e}")
        return False

def main():
    """Main function to add rating column and verify"""
    print("🚀 ADDING MISSING RATING COLUMN TO ORDERS TABLE")
    print("=" * 60)
    
    # Step 1: Add the rating column
    success = add_rating_column()
    
    if success:
        # Step 2: Verify the rating queries work
        verify_rating_queries()
        
        print("\n✅ DATABASE SCHEMA FIX COMPLETE")
        print("   - Added 'rating' column to orders table")
        print("   - Default value: 4.0")
        print("   - Type: REAL")
        print("   - Rating queries should now work without errors")
        
        return True
    else:
        print("\n❌ DATABASE SCHEMA FIX FAILED")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
