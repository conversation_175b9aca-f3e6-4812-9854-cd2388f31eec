#!/usr/bin/env python3
"""
Add sample payment data to the payments table
"""

def add_sample_payments():
    """Add sample payment data"""
    print("💰 ADDING SAMPLE PAYMENT DATA")
    print("=" * 60)
    
    try:
        import sqlite3
        from datetime import datetime, timedelta
        import random
        
        db = sqlite3.connect('instance/medivent.db')
        db.row_factory = sqlite3.Row
        
        # Get some orders to create sample payments
        orders = db.execute('''
            SELECT order_id, customer_name, order_amount, order_date, status
            FROM orders 
            ORDER BY order_date DESC
            LIMIT 20
        ''').fetchall()
        
        print(f"📊 Found {len(orders)} orders to create payments for")
        
        if not orders:
            print("❌ No orders found to create payments")
            return False
        
        sample_payments = []
        payment_methods = ['Cash', 'Bank Transfer', 'Online', 'Credit Card', 'Cheque']
        
        for i, order in enumerate(orders, 1):
            # Generate payment ID
            payment_id = f"PAY{str(i).zfill(6)}"
            
            # Random payment method
            payment_method = random.choice(payment_methods)
            
            # Payment date (same as order date or slightly after)
            try:
                # Try different datetime formats
                if '.' in order['order_date']:
                    # Format with microseconds
                    order_date = datetime.strptime(order['order_date'].split('.')[0], '%Y-%m-%d %H:%M:%S')
                else:
                    order_date = datetime.strptime(order['order_date'], '%Y-%m-%d %H:%M:%S')
            except ValueError:
                try:
                    order_date = datetime.strptime(order['order_date'], '%Y-%m-%d')
                except ValueError:
                    # Fallback to current date
                    order_date = datetime.now()

            payment_date = order_date + timedelta(days=random.randint(0, 3))
            
            # Status based on order status
            if order['status'] in ['Delivered', 'Completed']:
                status = 'Completed'
            elif order['status'] in ['Cancelled']:
                status = 'Cancelled'
            else:
                status = 'Pending'
            
            sample_payments.append((
                payment_id,
                None,  # invoice_id
                order['order_id'],
                order['customer_name'],  # Using customer_name as customer_id for now
                float(order['order_amount']),
                payment_date.strftime('%Y-%m-%d'),
                payment_method,
                f"REF{payment_id}",
                status,
                f'Payment for order {order["order_id"]} via {payment_method}',
                payment_date.strftime('%Y-%m-%d %H:%M:%S'),
                'system',
                None,  # division_id
                f'Bank details for {payment_method}' if payment_method in ['Bank Transfer', 'Cheque'] else None
            ))
        
        # Insert sample payments
        insert_sql = '''
            INSERT INTO payments (
                payment_id, invoice_id, order_id, customer_id, amount,
                payment_date, payment_method, reference_number, status, notes,
                created_at, created_by, division_id, bank_details
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        '''
        
        db.executemany(insert_sql, sample_payments)
        db.commit()
        
        print(f"✅ Added {len(sample_payments)} sample payment records")
        
        # Verify the data
        print("\n🔍 Verifying payment data...")
        
        # Check row count
        count = db.execute("SELECT COUNT(*) as count FROM payments").fetchone()[0]
        print(f"   📊 Total payments: {count}")
        
        # Check total amount
        total = db.execute("SELECT SUM(amount) as total FROM payments").fetchone()[0]
        print(f"   💰 Total amount: Rs. {total:.2f}")
        
        # Check payment methods
        methods = db.execute('''
            SELECT payment_method, COUNT(*) as count, SUM(amount) as total
            FROM payments 
            GROUP BY payment_method 
            ORDER BY total DESC
        ''').fetchall()
        
        print(f"   📋 Payment methods breakdown:")
        for method in methods:
            print(f"      • {method[0]}: {method[1]} payments, Rs. {method[2]:.2f}")
        
        # Test the query that was failing
        print("\n🧪 Testing financial report queries...")
        
        try:
            # Test the query from advanced_payment.py
            test_result = db.execute(
                "SELECT COALESCE(SUM(amount), 0) as total FROM payments WHERE DATE(payment_date) >= DATE('now', '-30 days')"
            ).fetchone()
            print(f"   ✅ Monthly payments query: Rs. {test_result[0]:.2f}")
            
            # Test payment methods query
            methods_result = db.execute(
                """SELECT payment_method, COUNT(*) as count, SUM(amount) as total
                   FROM payments
                   WHERE DATE(payment_date) >= DATE('now', '-30 days')
                   GROUP BY payment_method
                   ORDER BY total DESC"""
            ).fetchall()
            print(f"   ✅ Payment methods query: {len(methods_result)} methods found")
            
        except Exception as e:
            print(f"   ❌ Query test failed: {e}")
            return False
        
        db.close()
        print("\n🎉 SAMPLE PAYMENT DATA ADDED SUCCESSFULLY!")
        return True
        
    except Exception as e:
        print(f"❌ Error adding sample payments: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    add_sample_payments()
