#!/usr/bin/env python3
"""
Comprehensive analysis of database and routes
"""

import sqlite3
import sys
import os

def analyze_database():
    """Analyze database structure and data"""
    print("🗄️ DATABASE ANALYSIS")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Check table structure
        cursor.execute('PRAGMA table_info(products)')
        columns = cursor.fetchall()
        print("📋 Products table columns:")
        for col in columns:
            pk_marker = " (PRIMARY KEY)" if col[5] else ""
            print(f"   {col[1]} ({col[2]}){pk_marker}")
        
        # Check sample data
        print("\n📊 Sample data:")
        cursor.execute('SELECT id, product_id, name, status, is_active FROM products LIMIT 3')
        samples = cursor.fetchall()
        for row in samples:
            print(f"   ID: {row[0]}, Product_ID: {row[1]}, Name: {row[2][:20]}..., Status: {row[3]}, Is_Active: {row[4]}")
        
        # Check statistics
        print("\n📈 Statistics:")
        cursor.execute('SELECT COUNT(*) as total FROM products')
        total = cursor.fetchone()[0]
        cursor.execute('SELECT COUNT(*) as active FROM products WHERE is_active = 1')
        active = cursor.fetchone()[0]
        cursor.execute('SELECT COUNT(*) as inactive FROM products WHERE is_active = 0')
        inactive = cursor.fetchone()[0]
        print(f"   Total: {total}, Active: {active}, Inactive: {inactive}")
        
        # Check status distribution
        cursor.execute('SELECT status, COUNT(*) FROM products GROUP BY status')
        status_dist = cursor.fetchall()
        print("\n📊 Status distribution:")
        for row in status_dist:
            print(f"   {row[0]}: {row[1]} products")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database analysis failed: {e}")
        return False

def analyze_routes():
    """Analyze Flask routes"""
    print("\n🛣️ ROUTE ANALYSIS")
    print("=" * 50)
    
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from app import app
        
        with app.app_context():
            rules = list(app.url_map.iter_rules())
            
            # Find product management routes
            management_routes = []
            for rule in rules:
                if 'product_management' in rule.rule or 'product_management' in rule.endpoint:
                    management_routes.append({
                        'rule': rule.rule,
                        'endpoint': rule.endpoint,
                        'methods': list(rule.methods)
                    })
            
            print("📋 Product management routes:")
            for route in management_routes:
                print(f"   {route['rule']} → {route['endpoint']} {route['methods']}")
            
            # Find activation routes
            activation_routes = []
            for rule in rules:
                if 'activate' in rule.endpoint or 'deactivate' in rule.endpoint:
                    activation_routes.append({
                        'rule': rule.rule,
                        'endpoint': rule.endpoint,
                        'methods': list(rule.methods)
                    })
            
            print("\n🔧 Activation routes:")
            for route in activation_routes:
                print(f"   {route['rule']} → {route['endpoint']} {route['methods']}")
            
            # Check for conflicts
            all_product_routes = []
            for rule in rules:
                if 'products' in rule.endpoint or 'product' in rule.rule:
                    all_product_routes.append(rule.rule)
            
            print(f"\n📊 Total product-related routes: {len(all_product_routes)}")
            
        return True
        
    except Exception as e:
        print(f"❌ Route analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_route_access():
    """Test route access"""
    print("\n🧪 ROUTE ACCESS TEST")
    print("=" * 50)
    
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from app import app
        
        with app.test_client() as client:
            # Test main route
            response = client.get('/product_management')
            print(f"📍 /product_management: {response.status_code}")
            if response.status_code == 302:
                print(f"   → Redirects to: {response.location}")
            
            # Test blueprint route
            response = client.get('/products/product_management/')
            print(f"📍 /products/product_management/: {response.status_code}")
            if response.status_code == 302:
                print(f"   → Redirects to: {response.location}")
        
        return True
        
    except Exception as e:
        print(f"❌ Route access test failed: {e}")
        return False

if __name__ == "__main__":
    print("🔍 COMPREHENSIVE ANALYSIS")
    print("=" * 60)
    
    results = []
    results.append(analyze_database())
    results.append(analyze_routes())
    results.append(test_route_access())
    
    print(f"\n📋 SUMMARY: {sum(results)}/{len(results)} tests passed")
