#!/usr/bin/env python3
"""
Analyze database schema to understand table structures and identify issues
"""

import sqlite3
import sys

def analyze_database():
    """Analyze the database schema and identify issues"""
    try:
        print("🔍 ANALYZING DATABASE SCHEMA")
        print("=" * 60)
        
        db = sqlite3.connect('instance/medivent.db')
        db.row_factory = sqlite3.Row
        
        # 1. Check stock_movements table structure
        print("1️⃣ STOCK_MOVEMENTS TABLE STRUCTURE:")
        print("-" * 40)
        
        cursor = db.execute("PRAGMA table_info(stock_movements)")
        columns = cursor.fetchall()
        
        if columns:
            print("✅ stock_movements table exists")
            for col in columns:
                print(f"  • {col['name']}: {col['type']} {'(NOT NULL)' if col['notnull'] else ''}")
        else:
            print("❌ stock_movements table does not exist")
        
        # 2. Check inventory table structure
        print("\n2️⃣ INVENTORY TABLE STRUCTURE:")
        print("-" * 40)
        
        cursor = db.execute("PRAGMA table_info(inventory)")
        columns = cursor.fetchall()
        
        if columns:
            print("✅ inventory table exists")
            for col in columns:
                print(f"  • {col['name']}: {col['type']} {'(NOT NULL)' if col['notnull'] else ''}")
        else:
            print("❌ inventory table does not exist")
        
        # 3. Check products table structure
        print("\n3️⃣ PRODUCTS TABLE STRUCTURE:")
        print("-" * 40)
        
        cursor = db.execute("PRAGMA table_info(products)")
        columns = cursor.fetchall()
        
        if columns:
            print("✅ products table exists")
            for col in columns:
                print(f"  • {col['name']}: {col['type']} {'(NOT NULL)' if col['notnull'] else ''}")
        else:
            print("❌ products table does not exist")
        
        # 4. Test the problematic query from inventory history
        print("\n4️⃣ TESTING INVENTORY HISTORY QUERY:")
        print("-" * 40)
        
        try:
            cursor = db.execute('''
                SELECT m.*,
                       w1.name as from_warehouse_name,
                       w2.name as to_warehouse_name
                FROM stock_movements m
                LEFT JOIN warehouses w1 ON m.from_warehouse_id = w1.warehouse_id
                LEFT JOIN warehouses w2 ON m.to_warehouse_id = w2.warehouse_id
                WHERE m.inventory_id = ?
                ORDER BY m.movement_date DESC
                LIMIT 1
            ''', ('test',))
            
            result = cursor.fetchall()
            print("✅ Inventory history query works")
            
        except Exception as e:
            print(f"❌ Inventory history query failed: {str(e)}")
            
            # Check if from_warehouse_id column exists
            cursor = db.execute("PRAGMA table_info(stock_movements)")
            columns = [col['name'] for col in cursor.fetchall()]
            
            if 'from_warehouse_id' not in columns:
                print("🔍 Missing column: from_warehouse_id")
            if 'to_warehouse_id' not in columns:
                print("🔍 Missing column: to_warehouse_id")
            if 'movement_date' not in columns:
                print("🔍 Missing column: movement_date")
        
        # 5. Check for pagination utilities
        print("\n5️⃣ CHECKING SAMPLE DATA:")
        print("-" * 40)
        
        # Check products count
        cursor = db.execute("SELECT COUNT(*) as count FROM products")
        products_count = cursor.fetchone()['count']
        print(f"Products count: {products_count}")
        
        # Check inventory count
        cursor = db.execute("SELECT COUNT(*) as count FROM inventory")
        inventory_count = cursor.fetchone()['count']
        print(f"Inventory count: {inventory_count}")
        
        # Check stock_movements count
        try:
            cursor = db.execute("SELECT COUNT(*) as count FROM stock_movements")
            movements_count = cursor.fetchone()['count']
            print(f"Stock movements count: {movements_count}")
        except:
            print("Stock movements count: N/A (table issue)")
        
        # 6. Check product status column
        print("\n6️⃣ CHECKING PRODUCT STATUS:")
        print("-" * 40)
        
        cursor = db.execute("PRAGMA table_info(products)")
        columns = [col['name'] for col in cursor.fetchall()]
        
        if 'status' in columns:
            print("✅ products.status column exists")
            
            # Check status values
            cursor = db.execute("SELECT status, COUNT(*) as count FROM products GROUP BY status")
            status_counts = cursor.fetchall()
            
            for status in status_counts:
                print(f"  • {status['status']}: {status['count']} products")
        else:
            print("❌ products.status column missing")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Database analysis failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 DATABASE SCHEMA ANALYSIS")
    print("=" * 70)
    
    success = analyze_database()
    
    print("\n" + "=" * 70)
    print("📊 ANALYSIS COMPLETE")
    print("=" * 70)
    
    if success:
        print("✅ Database analysis completed successfully")
    else:
        print("❌ Database analysis failed")
    
    print("=" * 70)
