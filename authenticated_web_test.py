#!/usr/bin/env python3
"""
Test web interface with proper authentication
"""

import requests
import time
import re

def test_authenticated_order_creation():
    """Test order creation with proper authentication"""
    print("🔐 Testing Authenticated Web Interface Order Creation")
    print("=" * 70)
    
    base_url = "http://localhost:5001"
    session = requests.Session()
    
    try:
        # Step 1: Get login page
        print("1. Getting login page...")
        response = session.get(f"{base_url}/auth/login", timeout=10)
        print(f"   Login page status: {response.status_code}")
        
        if response.status_code != 200:
            print("❌ Cannot access login page")
            return False
        
        # Step 2: Extract CSRF token if present
        csrf_token = None
        csrf_match = re.search(r'name="csrf_token".*?value="([^"]+)"', response.text)
        if csrf_match:
            csrf_token = csrf_match.group(1)
            print(f"   Found CSRF token: {csrf_token[:20]}...")
        
        # Step 3: Login with test credentials
        print("2. Attempting login...")
        login_data = {
            'username': 'admin',  # Try admin first
            'password': 'admin123'
        }
        
        if csrf_token:
            login_data['csrf_token'] = csrf_token
        
        response = session.post(f"{base_url}/auth/login", 
                               data=login_data, 
                               timeout=10,
                               allow_redirects=False)
        
        print(f"   Login response: {response.status_code}")
        
        if response.status_code == 302:
            redirect_url = response.headers.get('Location', '')
            print(f"   Redirected to: {redirect_url}")
            
            if 'dashboard' in redirect_url:
                print("✅ Login successful!")
            else:
                print("⚠️  Login redirect unexpected")
                return False
        else:
            print("❌ Login failed, trying different credentials...")
            
            # Try different credentials
            for username, password in [('test', 'test'), ('user', 'password'), ('admin', 'password')]:
                login_data = {'username': username, 'password': password}
                if csrf_token:
                    login_data['csrf_token'] = csrf_token
                
                response = session.post(f"{base_url}/auth/login", 
                                       data=login_data, 
                                       timeout=10,
                                       allow_redirects=False)
                
                if response.status_code == 302:
                    print(f"✅ Login successful with {username}/{password}")
                    break
            else:
                print("❌ All login attempts failed")
                return False
        
        # Step 4: Access order creation page
        print("3. Accessing order creation page...")
        response = session.get(f"{base_url}/orders/new", timeout=10)
        print(f"   Order page status: {response.status_code}")
        
        if response.status_code != 200:
            print("❌ Cannot access order creation page after login")
            return False
        
        # Step 5: Extract CSRF token from order form
        csrf_token = None
        csrf_match = re.search(r'name="csrf_token".*?value="([^"]+)"', response.text)
        if csrf_match:
            csrf_token = csrf_match.group(1)
            print(f"   Found order form CSRF token: {csrf_token[:20]}...")
        
        # Step 6: Submit order
        print("4. Submitting authenticated order...")
        order_data = {
            'customer_name': 'Authenticated Test Customer',
            'customer_address': 'Authenticated Test Address',
            'customer_phone': '555-AUTH-TEST',
            'payment_method': 'cash',
            'po_number': 'AUTH-TEST-001',
            'product_id[]': ['P001'],
            'quantity[]': ['1'],
            'foc_quantity[]': ['0']
        }
        
        if csrf_token:
            order_data['csrf_token'] = csrf_token
        
        response = session.post(f"{base_url}/orders/new", 
                               data=order_data, 
                               timeout=15,
                               allow_redirects=False)
        
        print(f"   Order submission status: {response.status_code}")
        print(f"   Response headers: {dict(response.headers)}")
        
        if response.status_code == 302:
            redirect_url = response.headers.get('Location', '')
            print(f"   Redirected to: {redirect_url}")
            
            if '/orders/' in redirect_url and redirect_url != '/orders/':
                print("✅ Order created successfully!")
                order_id = redirect_url.split('/orders/')[-1].split('/')[0]
                print(f"   Order ID: {order_id}")
                return True
            elif 'dashboard' in redirect_url:
                print("❌ Still redirecting to dashboard - authentication issue persists")
                return False
            else:
                print("⚠️  Unexpected redirect")
                return False
                
        elif response.status_code == 200:
            # Check for error messages
            if "UNIQUE constraint failed" in response.text:
                print("❌ UNIQUE constraint error still occurring!")
                print("   This confirms the web interface has the old code")
                return False
            elif "Error placing order" in response.text:
                error_match = re.search(r'Error placing order: ([^<]+)', response.text)
                if error_match:
                    error_msg = error_match.group(1).strip()
                    print(f"❌ Order error: {error_msg}")
                return False
            else:
                print("⚠️  Unexpected 200 response")
                return False
        else:
            print(f"❌ Unexpected status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error during authenticated test: {e}")
        return False

def test_bypass_authentication():
    """Test order creation by bypassing authentication (direct database)"""
    print("\n🔧 Testing Direct Order Creation (Bypass Authentication)")
    print("=" * 70)
    
    try:
        import sqlite3
        from datetime import datetime
        
        # Use the same order ID generation as the web interface
        from app import app
        with app.app_context():
            from routes.orders import generate_order_id
            
            order_id = generate_order_id()
            print(f"Generated order ID: {order_id}")
            
            # Insert directly into database
            conn = sqlite3.connect('instance/medivent.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO orders (
                    order_id, customer_name, customer_address, customer_phone,
                    payment_method, status, sales_agent, updated_by, 
                    order_date, last_updated, order_amount
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                order_id,
                'Direct Database Test Customer',
                'Direct Database Test Address',
                '555-DIRECT-TEST',
                'cash',
                'Placed',
                'direct_test',
                'direct_test',
                datetime.now(),
                datetime.now(),
                100.0
            ))
            
            conn.commit()
            conn.close()
            
            print(f"✅ Order {order_id} created directly in database")
            return True
            
    except Exception as e:
        print(f"❌ Direct creation error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run authentication tests"""
    print("🔍 TESTING WEB INTERFACE WITH AUTHENTICATION")
    print("=" * 80)
    
    # Test 1: Authenticated web interface
    auth_test = test_authenticated_order_creation()
    
    # Test 2: Direct database creation
    direct_test = test_bypass_authentication()
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 AUTHENTICATION TEST SUMMARY")
    print("=" * 80)
    print(f"Authenticated Web Interface: {'✅ WORKING' if auth_test else '❌ FAILED'}")
    print(f"Direct Database Creation: {'✅ WORKING' if direct_test else '❌ FAILED'}")
    
    if not auth_test and direct_test:
        print("\n🔍 DIAGNOSIS:")
        print("- Direct order creation works (database and order ID generation OK)")
        print("- Authenticated web interface fails")
        print("- This suggests either:")
        print("  1. Authentication is not working properly")
        print("  2. The web interface is still using old/cached code")
        print("  3. There's a different route being used")
        
        print("\n💡 RECOMMENDED ACTIONS:")
        print("1. Check if Flask server is using the correct routes/orders.py")
        print("2. Restart Flask server completely")
        print("3. Clear all browser cache and cookies")
        print("4. Check if there are multiple Flask processes")
        print("5. Verify the order creation route is properly registered")
    elif auth_test:
        print("\n✅ DIAGNOSIS: Authentication and order creation working!")
        print("The issue may have been resolved.")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
