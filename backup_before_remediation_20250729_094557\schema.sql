-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    full_name TEXT,
    email TEXT,
    role TEXT DEFAULT 'user',
    status TEXT DEFAULT 'active',
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Products table
CREATE TABLE IF NOT EXISTS products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    product_id TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    strength TEXT,
    manufacturer TEXT,
    category TEXT,
    unit_of_measure TEXT,
    unit_price REAL DEFAULT 0.0,
    min_stock_level INTEGER DEFAULT 10,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by TEXT
);

-- Warehouses table
CREATE TABLE IF NOT EXISTS warehouses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    warehouse_id TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    address TEXT,
    city TEXT,
    country TEXT,
    capacity INTEGER,
    manager TEXT,
    phone TEXT,
    email TEXT,
    status TEXT DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by TEXT
);

-- Inventory table
CREATE TABLE IF NOT EXISTS inventory (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    inventory_id TEXT UNIQUE NOT NULL,
    product_id TEXT NOT NULL,
    batch_number TEXT NOT NULL,
    country_of_origin TEXT,
    manufacturing_date DATE,
    expiry_date DATE,
    stock_quantity INTEGER DEFAULT 0,
    allocated_quantity INTEGER DEFAULT 0,
    warehouse_id TEXT,
    location_code TEXT,
    status TEXT DEFAULT 'active',
    date_received DATE,
    received_by TEXT,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by TEXT,
    FOREIGN KEY (product_id) REFERENCES products (product_id),
    FOREIGN KEY (warehouse_id) REFERENCES warehouses (warehouse_id)
);

-- Stock Movements table
CREATE TABLE IF NOT EXISTS stock_movements (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    movement_id TEXT UNIQUE NOT NULL,
    inventory_id TEXT,
    product_id TEXT NOT NULL,
    batch_number TEXT NOT NULL,
    quantity INTEGER NOT NULL,
    from_warehouse_id TEXT,
    to_warehouse_id TEXT,
    movement_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    movement_type TEXT NOT NULL,
    moved_by TEXT,
    notes TEXT,
    FOREIGN KEY (product_id) REFERENCES products (product_id),
    FOREIGN KEY (from_warehouse_id) REFERENCES warehouses (warehouse_id),
    FOREIGN KEY (to_warehouse_id) REFERENCES warehouses (warehouse_id)
);

-- Orders table
CREATE TABLE IF NOT EXISTS orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_id TEXT UNIQUE NOT NULL,
    invoice_number TEXT,
    customer_name TEXT NOT NULL,
    customer_address TEXT,
    customer_phone TEXT,
    customer_email TEXT,
    order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status TEXT DEFAULT 'Placed',
    sales_agent TEXT,
    order_amount REAL DEFAULT 0.0,
    payment_method TEXT,
    payment_status TEXT DEFAULT 'pending',
    rider_id TEXT,
    approval_date TIMESTAMP,
    approved_by TEXT,
    dispatch_date TIMESTAMP,
    delivery_date TIMESTAMP,
    notes TEXT,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by TEXT
);

-- Order Items table
CREATE TABLE IF NOT EXISTS order_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_item_id TEXT UNIQUE NOT NULL,
    order_id TEXT NOT NULL,
    product_id TEXT NOT NULL,
    product_name TEXT,
    strength TEXT,
    batch_number TEXT,
    quantity INTEGER DEFAULT 0,
    foc_quantity INTEGER DEFAULT 0,
    unit_price REAL DEFAULT 0.0,
    line_total REAL DEFAULT 0.0,
    warehouse_id TEXT,
    location_code TEXT,
    status TEXT DEFAULT 'Placed',
    FOREIGN KEY (order_id) REFERENCES orders (order_id),
    FOREIGN KEY (product_id) REFERENCES products (product_id)
);

-- Invoices table
CREATE TABLE IF NOT EXISTS invoices (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    invoice_number TEXT UNIQUE NOT NULL,
    order_id TEXT NOT NULL,
    date_generated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    generated_by TEXT,
    subtotal REAL DEFAULT 0,
    tax_amount REAL DEFAULT 0,
    total_amount REAL DEFAULT 0,
    status TEXT DEFAULT 'Generated',
    pdf_path TEXT,
    FOREIGN KEY (order_id) REFERENCES orders (order_id)
);

-- Invoice Items table for detailed product tracking
CREATE TABLE IF NOT EXISTS invoice_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    invoice_number TEXT NOT NULL,
    product_name TEXT NOT NULL,
    quantity INTEGER NOT NULL,
    unit_price REAL NOT NULL,
    total_price REAL NOT NULL,
    batch_number TEXT,
    expiry_date TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (invoice_number) REFERENCES invoices (invoice_number)
);

-- Delivery Challans table
CREATE TABLE IF NOT EXISTS challans (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    dc_number TEXT UNIQUE NOT NULL,
    invoice_number TEXT NOT NULL,
    order_id TEXT NOT NULL,
    date_generated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    generated_by TEXT,
    pdf_path TEXT,
    FOREIGN KEY (order_id) REFERENCES orders (order_id),
    FOREIGN KEY (invoice_number) REFERENCES invoices (invoice_number)
);

-- Activity Logs table
CREATE TABLE IF NOT EXISTS activity_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    username TEXT NOT NULL,
    action TEXT NOT NULL,
    entity_id TEXT,
    details TEXT,
    ip_address TEXT,
    user_agent TEXT,
    module TEXT
);

-- Insert default warehouses
INSERT OR IGNORE INTO warehouses (warehouse_id, name, address, city, country, capacity, manager, phone, email, status, created_by, updated_by)
VALUES
('WH001', 'Karachi Warehouse', 'Plot #25, Sector 14-B, North Industrial Zone', 'Karachi', 'Pakistan', 10000, 'Admin', '+92-21-3512-4489', '<EMAIL>', 'active', 'system', 'system'),
('WH002', 'Lahore Warehouse', '123 Industrial Estate, Ferozpur Road', 'Lahore', 'Pakistan', 8000, 'Admin', '+92-42-3512-4489', '<EMAIL>', 'active', 'system', 'system');

-- Sample products removed - Use real product data from CSV files

-- Sample inventory removed - Use real inventory data from CSV files

-- Insert default admin user (password: admin123)
INSERT OR IGNORE INTO users (username, password_hash, full_name, email, role, status, created_at, updated_at)
VALUES ('admin', 'pbkdf2:sha256:150000$KDoLi7xP$b7a0a8c1c6b6b6a7e4e0e4e0e4e0e4e0e4e0e4e0e4e0e4e0e4e0e4e0e4e0e4e0', 'System Administrator', '<EMAIL>', 'admin', 'active', datetime('now'), datetime('now'));

-- Sample stock movements removed - Use real stock movement data

-- Permissions table
CREATE TABLE IF NOT EXISTS permissions (
    permission_id INTEGER PRIMARY KEY AUTOINCREMENT,
    permission_code TEXT UNIQUE NOT NULL,
    permission_name TEXT NOT NULL,
    description TEXT,
    category TEXT NOT NULL
);

-- Roles-Permissions mapping table
CREATE TABLE IF NOT EXISTS role_permissions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    role TEXT NOT NULL,
    permission_id INTEGER NOT NULL,
    FOREIGN KEY (permission_id) REFERENCES permissions (permission_id),
    UNIQUE(role, permission_id)
);

-- Insert default permissions
INSERT OR IGNORE INTO permissions (permission_code, permission_name, description, category) VALUES
-- User Management Permissions
('user_view', 'View Users', 'Can view user list and details', 'User Management'),
('user_create', 'Create Users', 'Can create new users', 'User Management'),
('user_edit', 'Edit Users', 'Can edit user details', 'User Management'),
('user_delete', 'Delete Users', 'Can delete users', 'User Management'),
('user_activate', 'Activate/Deactivate Users', 'Can activate or deactivate users', 'User Management'),
('user_reset_password', 'Reset User Passwords', 'Can reset user passwords', 'User Management'),
('role_manage', 'Manage Roles', 'Can manage role permissions', 'User Management'),

-- Order Management Permissions
('order_view', 'View Orders', 'Can view orders', 'Order Management'),
('order_create', 'Create Orders', 'Can create new orders', 'Order Management'),
('order_edit', 'Edit Orders', 'Can edit orders', 'Order Management'),
('order_delete', 'Delete Orders', 'Can delete orders', 'Order Management'),
('order_approve', 'Approve Orders', 'Can approve orders', 'Order Management'),
('order_process', 'Process Orders', 'Can process orders', 'Order Management'),
('order_dispatch', 'Dispatch Orders', 'Can dispatch orders', 'Order Management'),
('order_deliver', 'Deliver Orders', 'Can mark orders as delivered', 'Order Management'),

-- Product Management Permissions
('product_view', 'View Products', 'Can view products', 'Product Management'),
('product_create', 'Create Products', 'Can create new products', 'Product Management'),
('product_edit', 'Edit Products', 'Can edit products', 'Product Management'),
('product_delete', 'Delete Products', 'Can delete products', 'Product Management'),
('product_activate', 'Activate/Deactivate Products', 'Can activate or deactivate products', 'Product Management'),

-- Inventory Management Permissions
('inventory_view', 'View Inventory', 'Can view inventory', 'Inventory Management'),
('inventory_add', 'Add Inventory', 'Can add inventory', 'Inventory Management'),
('inventory_adjust', 'Adjust Inventory', 'Can adjust inventory levels', 'Inventory Management'),
('inventory_transfer', 'Transfer Inventory', 'Can transfer inventory between warehouses', 'Inventory Management'),
('batch_manage', 'Manage Batches', 'Can manage product batches', 'Inventory Management'),

-- Report Permissions
('report_view', 'View Reports', 'Can view reports', 'Reports'),
('report_export', 'Export Reports', 'Can export reports', 'Reports'),
('report_sales', 'View Sales Reports', 'Can view sales reports', 'Reports'),
('report_inventory', 'View Inventory Reports', 'Can view inventory reports', 'Reports'),
('report_orders', 'View Order Reports', 'Can view order reports', 'Reports'),

-- Document Permissions
('invoice_generate', 'Generate Invoices', 'Can generate invoices', 'Documents'),
('invoice_view', 'View Invoices', 'Can view invoices', 'Documents'),
('challan_generate', 'Generate Delivery Challans', 'Can generate delivery challans', 'Documents'),
('challan_view', 'View Delivery Challans', 'Can view delivery challans', 'Documents'),

-- System Permissions
('settings_view', 'View Settings', 'Can view system settings', 'System'),
('settings_edit', 'Edit Settings', 'Can edit system settings', 'System'),
('backup_create', 'Create Backups', 'Can create database backups', 'System'),
('backup_restore', 'Restore Backups', 'Can restore database backups', 'System'),
('logs_view', 'View Logs', 'Can view system logs', 'System'),

-- Dashboard Permissions
('dashboard_view', 'View Dashboard', 'Can view the main dashboard', 'Dashboard'),
('dashboard_orders_widget', 'Orders Widget', 'Can view the orders statistics widget on dashboard', 'Dashboard'),
('dashboard_inventory_widget', 'Inventory Widget', 'Can view the inventory statistics widget on dashboard', 'Dashboard'),
('dashboard_workflow_widget', 'Workflow Widget', 'Can view the order workflow widget on dashboard', 'Dashboard'),

-- Menu Visibility Permissions
('menu_dashboard', 'Dashboard Menu', 'Can see the Dashboard menu', 'Menu Visibility'),
('menu_orders', 'Orders Menu', 'Can see the Orders menu', 'Menu Visibility'),
('menu_products', 'Products Menu', 'Can see the Products menu', 'Menu Visibility'),
('menu_inventory', 'Inventory Menu', 'Can see the Inventory menu', 'Menu Visibility'),
('menu_sales', 'Sales Menu', 'Can see the Sales menu', 'Menu Visibility'),
('menu_warehouse', 'Warehouse Menu', 'Can see the Warehouse menu', 'Menu Visibility'),
('menu_reports', 'Reports Menu', 'Can see the Reports menu', 'Menu Visibility'),
('menu_users', 'User Management Menu', 'Can see the User Management menu', 'Menu Visibility'),
('menu_settings', 'Settings Menu', 'Can see the Settings menu', 'Menu Visibility');

-- Permission Audit Log table
CREATE TABLE IF NOT EXISTS permission_audit_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    username TEXT NOT NULL,
    action TEXT NOT NULL,
    role TEXT NOT NULL,
    permission_code TEXT NOT NULL,
    permission_name TEXT NOT NULL,
    previous_state TEXT,
    new_state TEXT,
    ip_address TEXT
);

-- Insert default role permissions for admin
INSERT OR IGNORE INTO role_permissions (role, permission_id)
SELECT 'admin', permission_id FROM permissions;

-- Settings table
CREATE TABLE IF NOT EXISTS settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    setting_name TEXT UNIQUE NOT NULL,
    value TEXT,
    description TEXT,
    category TEXT DEFAULT 'general',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default settings
INSERT OR IGNORE INTO settings (setting_name, value, description, category) VALUES
('company_name', 'Medivent Pharmaceuticals Pvt. Ltd.', 'Company name for invoices and documents', 'company'),
('company_address', 'Plot #25, Sector 14-B, North Industrial Zone, Karachi, Pakistan', 'Company address', 'company'),
('company_phone', '+92-21-3512-4489', 'Company phone number', 'company'),
('company_email', '<EMAIL>', 'Company email address', 'company'),
('company_ntn', 'NTN-1234567-8', 'Company NTN number', 'company'),
('invoice_prefix', 'INV', 'Invoice number prefix', 'documents'),
('challan_prefix', 'DC', 'Delivery challan number prefix', 'documents'),
('order_prefix', 'ORD', 'Order number prefix', 'documents'),
('tax_rate', '17.0', 'Default tax rate percentage', 'financial'),
('backup_retention_days', '30', 'Number of days to keep backups', 'system'),
('last_backup_date', '', 'Last database backup date', 'system'),
('app_version', '1.0.0', 'Application version', 'system'),
('db_version', '1.0.0', 'Database version', 'system');

-- Employees table
CREATE TABLE IF NOT EXISTS employees (
    employee_id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    email TEXT,
    phone TEXT,
    division_id TEXT,
    role_id TEXT,
    manager_id TEXT,
    hire_date DATE,
    status TEXT DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by TEXT,
    FOREIGN KEY (division_id) REFERENCES divisions (division_id),
    FOREIGN KEY (role_id) REFERENCES roles (role_id),
    FOREIGN KEY (manager_id) REFERENCES employees (employee_id)
);

-- Roles table
CREATE TABLE IF NOT EXISTS roles (
    role_id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    level INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by TEXT
);

-- Batch Selections table for DC generation
CREATE TABLE IF NOT EXISTS batch_selections (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_id TEXT NOT NULL,
    product_id TEXT NOT NULL,
    batch_number TEXT NOT NULL,
    warehouse_id TEXT NOT NULL,
    allocated_quantity REAL NOT NULL,
    selection_method TEXT DEFAULT 'manual', -- 'fifo', 'lifo', 'manual'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    status TEXT DEFAULT 'pending', -- 'pending', 'confirmed', 'cancelled'
    FOREIGN KEY (order_id) REFERENCES orders (order_id),
    FOREIGN KEY (product_id) REFERENCES products (product_id),
    FOREIGN KEY (warehouse_id) REFERENCES warehouses (warehouse_id)
);

-- DC Generation Sessions table
CREATE TABLE IF NOT EXISTS dc_generation_sessions (
    session_id TEXT PRIMARY KEY,
    order_id TEXT NOT NULL,
    selection_method TEXT NOT NULL, -- 'fifo', 'manual'
    warehouse_filter TEXT, -- NULL for all warehouses
    total_products INTEGER,
    allocated_products INTEGER,
    status TEXT DEFAULT 'in_progress', -- 'in_progress', 'completed', 'cancelled'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    FOREIGN KEY (order_id) REFERENCES orders (order_id)
);

-- Enhanced Delivery Challans table
CREATE TABLE IF NOT EXISTS delivery_challans (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    dc_number TEXT UNIQUE NOT NULL,
    order_id TEXT NOT NULL,
    warehouse_id TEXT NOT NULL,
    customer_id TEXT,
    customer_name TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    dispatch_date TIMESTAMP,
    delivery_date TIMESTAMP,
    status TEXT DEFAULT 'created', -- 'created', 'dispatched', 'delivered'
    notes TEXT,
    created_by TEXT,
    total_items INTEGER DEFAULT 0,
    total_amount REAL DEFAULT 0,
    batch_details TEXT, -- JSON string with batch allocation details
    pdf_path TEXT,
    FOREIGN KEY (order_id) REFERENCES orders (order_id),
    FOREIGN KEY (warehouse_id) REFERENCES warehouses (warehouse_id)
);
