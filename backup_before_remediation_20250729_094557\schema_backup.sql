-- Database Schema Backup
-- Created: 2025-07-29T09:45:58.005823

CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    full_name TEXT,
    email TEXT,
    role TEXT DEFAULT 'user',
    status TEXT DEFAULT 'active',
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
, department TEXT, employee_id TEXT, phone TEXT, last_password_change TIMESTAMP, failed_login_attempts INTEGER DEFAULT 0, account_locked_until TIMESTAMP);

CREATE TABLE sqlite_sequence(name,seq);

CREATE TABLE products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    product_id TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    strength TEXT,
    manufacturer TEXT,
    category TEXT,
    unit_of_measure TEXT,
    unit_price REAL DEFAULT 0.0,
    min_stock_level INTEGER DEFAULT 10,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by TEXT
, division_id TEXT, division TEXT, dosage_form TEXT, route_of_administration TEXT, country TEXT, brand_name TEXT, generic_name TEXT, active INTEGER DEFAULT 1, tax_exempt INTEGER DEFAULT 0, is_active INTEGER DEFAULT 1, reorder_level INTEGER DEFAULT 10, max_stock_level INTEGER DEFAULT 100, stock_status TEXT DEFAULT "active", tax_rate REAL DEFAULT 0, supplier_id TEXT, selling_price REAL DEFAULT 0, cost_price REAL DEFAULT 0, mrp REAL DEFAULT 0.0, tp_rate REAL DEFAULT 0.0, asp REAL DEFAULT 0.0, stock_quantity INTEGER DEFAULT 0, status TEXT DEFAULT "active", image_path TEXT, generic_category TEXT, batch_number TEXT, expiry_date DATE, warehouse_location TEXT, image_url TEXT, primary_image_id INTEGER, pack_size TEXT, reorder_point INTEGER DEFAULT 20, category_id TEXT, unit_id TEXT DEFAULT "PCS");

CREATE TABLE warehouses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    warehouse_id TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    address TEXT,
    city TEXT,
    country TEXT,
    capacity INTEGER,
    manager TEXT,
    phone TEXT,
    email TEXT,
    status TEXT DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by TEXT
, current_utilization INTEGER DEFAULT 0, manager_id INTEGER, description TEXT, image_path TEXT);

CREATE TABLE inventory (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    inventory_id TEXT UNIQUE NOT NULL,
    product_id TEXT NOT NULL,
    batch_number TEXT NOT NULL,
    country_of_origin TEXT,
    manufacturing_date DATE,
    expiry_date DATE,
    stock_quantity INTEGER DEFAULT 0,
    allocated_quantity INTEGER DEFAULT 0,
    warehouse_id TEXT,
    location_code TEXT,
    status TEXT DEFAULT 'active',
    date_received DATE,
    received_by TEXT,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by TEXT, payment_status TEXT DEFAULT "Pending", cost_price REAL DEFAULT 0, minimum_stock REAL DEFAULT 0, maximum_stock REAL DEFAULT 0, reorder_point REAL DEFAULT 0, location_id TEXT, bin_id TEXT, lot_number TEXT, serial_number TEXT,
    FOREIGN KEY (product_id) REFERENCES products (product_id),
    FOREIGN KEY (warehouse_id) REFERENCES warehouses (warehouse_id)
);

CREATE TABLE orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_id TEXT UNIQUE NOT NULL,
    invoice_number TEXT,
    customer_name TEXT NOT NULL,
    customer_address TEXT,
    customer_phone TEXT,
    customer_email TEXT,
    order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status TEXT DEFAULT 'Placed',
    sales_agent TEXT,
    order_amount REAL DEFAULT 0.0,
    payment_method TEXT,
    payment_status TEXT DEFAULT 'pending',
    rider_id TEXT,
    approval_date TIMESTAMP,
    approved_by TEXT,
    dispatch_date TIMESTAMP,
    delivery_date TIMESTAMP,
    notes TEXT,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by TEXT
, po_number TEXT, po_type TEXT, foc_quantity INTEGER DEFAULT 0, gross_amount REAL DEFAULT 0, sales_tax REAL DEFAULT 0, income_tax REAL DEFAULT 0, f_tax REAL DEFAULT 0, net_amount REAL DEFAULT 0, rider_assigned TEXT, delivery_method TEXT DEFAULT "RIDER", division TEXT, rider_name TEXT, vehicle_type TEXT, expected_pickup_time TEXT, pickup_notes TEXT, assigned_rider TEXT, cancel_reason TEXT, assigned_rider_id TEXT, pickup_scheduled_at TIMESTAMP, picked_up_at TIMESTAMP, out_for_delivery_at TIMESTAMP, delivery_attempted_at TIMESTAMP, delivered_at TIMESTAMP, delivery_proof_type TEXT, delivery_proof_data TEXT, delivery_rating INTEGER, delivery_feedback TEXT, return_reason TEXT, returned_at TIMESTAMP, estimated_delivery_time TIMESTAMP, actual_delivery_time TIMESTAMP, delivery_zone TEXT, priority_level INTEGER DEFAULT 1, cod_amount REAL DEFAULT 0, cod_collected BOOLEAN DEFAULT FALSE, delivery_latitude REAL, delivery_longitude REAL, pickup_latitude REAL, pickup_longitude REAL, google_maps_route TEXT, estimated_distance_km REAL, actual_distance_km REAL, dc_status TEXT DEFAULT "Pending DC", customer_id TEXT, priority TEXT DEFAULT "normal", special_instructions TEXT, discount_amount REAL DEFAULT 0, tax_amount REAL DEFAULT 0, shipping_cost REAL DEFAULT 0, order_source TEXT DEFAULT "manual", shipping_address TEXT, billing_address TEXT, order_type TEXT DEFAULT "SALE", estimated_delivery_date DATE, actual_delivery_date DATE, delivery_notes TEXT);

CREATE TABLE order_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_item_id TEXT UNIQUE NOT NULL,
    order_id TEXT NOT NULL,
    product_id TEXT NOT NULL,
    product_name TEXT,
    strength TEXT,
    batch_number TEXT,
    quantity INTEGER DEFAULT 0,
    foc_quantity INTEGER DEFAULT 0,
    unit_price REAL DEFAULT 0.0,
    line_total REAL DEFAULT 0.0,
    warehouse_id TEXT,
    location_code TEXT,
    status TEXT DEFAULT 'Placed', total_price REAL,
    FOREIGN KEY (order_id) REFERENCES orders (order_id),
    FOREIGN KEY (product_id) REFERENCES products (product_id)
);

CREATE TABLE invoices (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    invoice_number TEXT UNIQUE NOT NULL,
    order_id TEXT NOT NULL,
    date_generated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    generated_by TEXT,
    pdf_path TEXT, total_amount REAL DEFAULT 0, payment_terms INTEGER DEFAULT 30, due_date DATE, shipping_charges DECIMAL(15,2) DEFAULT 0.00, handling_charges DECIMAL(15,2) DEFAULT 0.00,
    FOREIGN KEY (order_id) REFERENCES orders (order_id)
);

CREATE TABLE activity_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    username TEXT NOT NULL,
    action TEXT NOT NULL,
    entity_id TEXT,
    details TEXT,
    ip_address TEXT,
    user_agent TEXT,
    module TEXT
, session_id TEXT, activity_duration INTEGER DEFAULT 0, page_url TEXT);

CREATE TABLE permissions (
    permission_id INTEGER PRIMARY KEY AUTOINCREMENT,
    permission_code TEXT UNIQUE NOT NULL,
    permission_name TEXT NOT NULL,
    description TEXT,
    category TEXT NOT NULL
);

CREATE TABLE role_permissions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    role TEXT NOT NULL,
    permission_id INTEGER NOT NULL,
    FOREIGN KEY (permission_id) REFERENCES permissions (permission_id),
    UNIQUE(role, permission_id)
);

CREATE TABLE permission_audit_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    username TEXT NOT NULL,
    action TEXT NOT NULL,
    role TEXT NOT NULL,
    permission_code TEXT NOT NULL,
    permission_name TEXT NOT NULL,
    previous_state TEXT,
    new_state TEXT,
    ip_address TEXT
);

CREATE TABLE employees (
    employee_id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    email TEXT,
    phone TEXT,
    division_id TEXT,
    role_id TEXT,
    manager_id TEXT,
    hire_date DATE,
    status TEXT DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by TEXT,
    FOREIGN KEY (division_id) REFERENCES "divisions_old" (division_id),
    FOREIGN KEY (role_id) REFERENCES roles (role_id),
    FOREIGN KEY (manager_id) REFERENCES employees (employee_id)
);

CREATE TABLE roles (
    role_id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    level INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by TEXT
);

CREATE TABLE customer_ledger (
        ledger_id TEXT PRIMARY KEY,
        customer_id TEXT NOT NULL,
        transaction_date DATE NOT NULL,
        transaction_type TEXT NOT NULL,
        reference_id TEXT,
        reference_type TEXT,
        debit_amount REAL DEFAULT 0,
        credit_amount REAL DEFAULT 0,
        balance REAL,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_by TEXT,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_by TEXT,
        FOREIGN KEY (customer_id) REFERENCES customers (customer_id)
    );

CREATE TABLE invoice_payments (
        payment_id TEXT PRIMARY KEY,
        invoice_id TEXT NOT NULL,
        payment_date DATE NOT NULL,
        payment_method TEXT NOT NULL,
        amount REAL NOT NULL,
        reference_number TEXT,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_by TEXT,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_by TEXT
    );

CREATE TABLE customers (
        customer_id TEXT PRIMARY KEY,
        code TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        phone TEXT,
        address TEXT,
        email TEXT,
        type TEXT DEFAULT 'PATIENT',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_by TEXT,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_by TEXT
    , city TEXT, customer_type TEXT DEFAULT "PATIENT", opening_balance REAL DEFAULT 0, current_balance REAL DEFAULT 0, credit_limit REAL DEFAULT 2000000, payment_terms TEXT DEFAULT "CASH", payment_days INTEGER DEFAULT 0, risk_category TEXT DEFAULT "low", tax_number TEXT, contact_person TEXT, territory TEXT, ntn_number TEXT, category TEXT DEFAULT "Direct Customer", status TEXT DEFAULT "active", customer_code TEXT, customer_group_id TEXT, price_list_id TEXT, outstanding_balance REAL DEFAULT 0, last_order_date DATE);

CREATE TABLE batches (
                batch_id TEXT PRIMARY KEY,
                product_id TEXT NOT NULL,
                batch_number TEXT NOT NULL,
                manufacturing_date DATE NOT NULL,
                expiry_date DATE NOT NULL,
                quantity INTEGER NOT NULL DEFAULT 0,
                warehouse_id TEXT,
                status TEXT DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products (product_id),
                FOREIGN KEY (warehouse_id) REFERENCES warehouses (warehouse_id)
            );

CREATE TABLE settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                setting_name TEXT UNIQUE NOT NULL,
                value TEXT,
                description TEXT,
                category TEXT DEFAULT 'general',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );

CREATE TABLE challans (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id TEXT NOT NULL,
                dc_number TEXT UNIQUE NOT NULL,
                customer_name TEXT NOT NULL,
                date_generated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status TEXT DEFAULT 'Generated',
                total_amount REAL DEFAULT 0,
                warehouse_id TEXT,
                generated_by TEXT, invoice_number TEXT, customer_address TEXT, customer_phone TEXT, pdf_path TEXT,
                FOREIGN KEY (order_id) REFERENCES orders (order_id)
            );

CREATE TABLE riders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                rider_id TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                phone TEXT,
                vehicle_type TEXT,
                status TEXT DEFAULT 'ACTIVE',
                current_location TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            , current_latitude REAL, current_longitude REAL, email TEXT, vehicle_number TEXT, license_number TEXT, max_orders_per_day INTEGER DEFAULT 10, rating REAL DEFAULT 5.0, total_deliveries INTEGER DEFAULT 0, successful_deliveries INTEGER DEFAULT 0, last_location_update TIMESTAMP, performance_stats TEXT, is_available INTEGER DEFAULT 1, city TEXT DEFAULT "Karachi", postal_code TEXT, address TEXT, emergency_contact TEXT, emergency_phone TEXT, hire_date DATE, salary DECIMAL(10,2), commission_rate DECIMAL(5,2) DEFAULT 0.05);

CREATE TABLE document_tracking (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                document_type TEXT NOT NULL, -- 'DC', 'INVOICE'
                document_number TEXT NOT NULL,
                order_id TEXT NOT NULL,
                customer_name TEXT NOT NULL,
                generated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                generated_by TEXT,
                file_path TEXT,
                status TEXT DEFAULT 'GENERATED'
            );

CREATE TABLE approval_workflow (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id TEXT NOT NULL,
                stage TEXT NOT NULL, -- 'PLACED', 'APPROVED', 'REJECTED', 'DC_GENERATED', 'INVOICED', 'DISPATCHED', 'DELIVERED'
                stage_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_by TEXT,
                comments TEXT,
                previous_stage TEXT
            );

CREATE TABLE po_types (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                po_type TEXT UNIQUE NOT NULL,
                description TEXT,
                payment_method TEXT DEFAULT 'CASH',
                credit_days INTEGER DEFAULT 0,
                active INTEGER DEFAULT 1
            );

CREATE TABLE batch_management (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                batch_number TEXT UNIQUE NOT NULL,
                product_id TEXT NOT NULL,
                product_name TEXT NOT NULL,
                manufacturing_date DATE,
                expiry_date DATE,
                warehouse_location TEXT DEFAULT 'Karachi',
                quantity_available INTEGER DEFAULT 0,
                status TEXT DEFAULT 'ACTIVE',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );

CREATE TABLE financial_tracking (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                transaction_id TEXT UNIQUE NOT NULL,
                order_id TEXT,
                invoice_number TEXT,
                customer_name TEXT,
                gross_amount REAL DEFAULT 0,
                sales_tax REAL DEFAULT 0,
                f_tax REAL DEFAULT 0,
                income_tax REAL DEFAULT 0,
                net_amount REAL DEFAULT 0,
                payment_method TEXT,
                payment_status TEXT DEFAULT 'PENDING',
                transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );

CREATE TABLE invoice_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    invoice_number TEXT NOT NULL,
    product_name TEXT NOT NULL,
    quantity INTEGER NOT NULL,
    unit_price REAL NOT NULL,
    total_price REAL NOT NULL,
    batch_number TEXT,
    expiry_date TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (invoice_number) REFERENCES invoices (invoice_number)
);

CREATE TABLE order_status_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id TEXT NOT NULL,
                status TEXT NOT NULL,
                previous_status TEXT,
                changed_by TEXT,
                change_reason TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                notes TEXT,
                FOREIGN KEY (order_id) REFERENCES orders (order_id)
            );

CREATE TABLE rider_assignments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id TEXT NOT NULL,
                rider_id TEXT NOT NULL,
                assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                assigned_by TEXT,
                pickup_time TIMESTAMP,
                delivery_time TIMESTAMP,
                assignment_status TEXT DEFAULT 'assigned',
                delivery_proof TEXT,
                customer_signature TEXT,
                delivery_photo TEXT,
                otp_verified BOOLEAN DEFAULT FALSE,
                delivery_notes TEXT,
                FOREIGN KEY (order_id) REFERENCES orders (order_id),
                FOREIGN KEY (rider_id) REFERENCES riders (rider_id)
            );

CREATE TABLE delivery_attempts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id TEXT NOT NULL,
                rider_id TEXT NOT NULL,
                attempt_number INTEGER DEFAULT 1,
                attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                attempt_status TEXT NOT NULL,
                failure_reason TEXT,
                customer_feedback TEXT,
                next_attempt_scheduled TIMESTAMP,
                notes TEXT,
                FOREIGN KEY (order_id) REFERENCES orders (order_id),
                FOREIGN KEY (rider_id) REFERENCES riders (rider_id)
            );

CREATE TABLE rider_locations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                rider_id TEXT NOT NULL,
                latitude REAL,
                longitude REAL,
                address TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (rider_id) REFERENCES riders (rider_id)
            );

CREATE TABLE delivery_routes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                rider_id TEXT NOT NULL,
                route_date DATE DEFAULT CURRENT_DATE,
                order_ids TEXT,
                route_sequence TEXT,
                estimated_time INTEGER,
                actual_time INTEGER,
                route_status TEXT DEFAULT 'planned',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (rider_id) REFERENCES riders (rider_id)
            );

CREATE TABLE api_keys (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                service_name TEXT NOT NULL UNIQUE,
                api_key TEXT NOT NULL,
                description TEXT,
                status TEXT DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                last_used TIMESTAMP,
                usage_count INTEGER DEFAULT 0
            , is_active BOOLEAN DEFAULT 1);

CREATE TABLE api_key_usage_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                service_name TEXT NOT NULL,
                endpoint TEXT,
                usage_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                ip_address TEXT,
                user_agent TEXT,
                status TEXT DEFAULT 'success'
            );

CREATE TABLE system_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                setting_key TEXT NOT NULL UNIQUE,
                setting_value TEXT,
                setting_type TEXT DEFAULT 'string',
                description TEXT,
                category TEXT DEFAULT 'general',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_by TEXT
            );

CREATE TABLE customer_ledger_advanced (
                ledger_id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id TEXT NOT NULL,
                transaction_date DATE NOT NULL,
                transaction_type TEXT NOT NULL, -- 'invoice', 'payment', 'credit_note', 'debit_note', 'adjustment'
                reference_number TEXT,
                invoice_id TEXT,
                payment_id TEXT,
                description TEXT,
                debit_amount DECIMAL(15,2) DEFAULT 0.00,
                credit_amount DECIMAL(15,2) DEFAULT 0.00,
                balance_amount DECIMAL(15,2) DEFAULT 0.00,
                due_date DATE,
                payment_terms TEXT,
                aging_days INTEGER DEFAULT 0,
                aging_bucket TEXT, -- '0-30', '31-60', '61-90', '90+'
                currency_code TEXT DEFAULT 'PKR',
                exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
                tax_amount DECIMAL(15,2) DEFAULT 0.00,
                discount_amount DECIMAL(15,2) DEFAULT 0.00,
                status TEXT DEFAULT 'active', -- 'active', 'disputed', 'written_off'
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_by TEXT,
                notes TEXT,
                FOREIGN KEY (customer_id) REFERENCES customers(customer_id)
            );

CREATE TABLE payments_advanced (
                payment_id TEXT PRIMARY KEY,
                customer_id TEXT NOT NULL,
                payment_date DATE NOT NULL,
                payment_method TEXT NOT NULL, -- 'cash', 'cheque', 'bank_transfer', 'credit_card', 'online'
                payment_type TEXT NOT NULL, -- 'full_payment', 'partial_payment', 'advance_payment', 'refund'
                total_amount DECIMAL(15,2) NOT NULL,
                allocated_amount DECIMAL(15,2) DEFAULT 0.00,
                unallocated_amount DECIMAL(15,2) DEFAULT 0.00,
                currency_code TEXT DEFAULT 'PKR',
                exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
                reference_number TEXT,
                bank_name TEXT,
                cheque_number TEXT,
                cheque_date DATE,
                bank_account TEXT,
                transaction_id TEXT,
                payment_status TEXT DEFAULT 'received', -- 'received', 'cleared', 'bounced', 'cancelled'
                knock_off_status TEXT DEFAULT 'pending', -- 'pending', 'partial', 'complete'
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_by TEXT,
                notes TEXT,
                FOREIGN KEY (customer_id) REFERENCES customers(customer_id)
            );

CREATE TABLE payment_allocations (
                allocation_id INTEGER PRIMARY KEY AUTOINCREMENT,
                payment_id TEXT NOT NULL,
                invoice_id TEXT NOT NULL,
                customer_id TEXT NOT NULL,
                allocation_date DATE NOT NULL,
                allocated_amount DECIMAL(15,2) NOT NULL,
                discount_amount DECIMAL(15,2) DEFAULT 0.00,
                adjustment_amount DECIMAL(15,2) DEFAULT 0.00,
                allocation_type TEXT DEFAULT 'manual', -- 'manual', 'auto', 'partial'
                status TEXT DEFAULT 'active', -- 'active', 'reversed'
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                reversed_at TIMESTAMP,
                reversed_by TEXT,
                reversal_reason TEXT,
                FOREIGN KEY (payment_id) REFERENCES payments_advanced(payment_id),
                FOREIGN KEY (invoice_id) REFERENCES invoices(invoice_id),
                FOREIGN KEY (customer_id) REFERENCES customers(customer_id)
            );

CREATE TABLE aging_analysis (
                aging_id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id TEXT NOT NULL,
                invoice_id TEXT NOT NULL,
                invoice_date DATE NOT NULL,
                due_date DATE NOT NULL,
                original_amount DECIMAL(15,2) NOT NULL,
                outstanding_amount DECIMAL(15,2) NOT NULL,
                paid_amount DECIMAL(15,2) DEFAULT 0.00,
                aging_days INTEGER NOT NULL,
                aging_bucket TEXT NOT NULL, -- 'current', '1-30', '31-60', '61-90', '91-120', '120+'
                risk_category TEXT DEFAULT 'low', -- 'low', 'medium', 'high', 'critical'
                last_payment_date DATE,
                last_contact_date DATE,
                collection_status TEXT DEFAULT 'normal', -- 'normal', 'follow_up', 'legal', 'written_off'
                analysis_date DATE NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers(customer_id),
                FOREIGN KEY (invoice_id) REFERENCES invoices(invoice_id)
            );

CREATE TABLE financial_reports_config (
                report_id INTEGER PRIMARY KEY AUTOINCREMENT,
                report_name TEXT NOT NULL,
                report_type TEXT NOT NULL, -- 'aging', 'ledger', 'payment', 'outstanding', 'collection'
                report_category TEXT NOT NULL, -- 'receivables', 'payables', 'cash_flow', 'profitability'
                parameters TEXT, -- JSON string of report parameters
                schedule_type TEXT, -- 'manual', 'daily', 'weekly', 'monthly'
                last_generated TIMESTAMP,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT
            );

CREATE TABLE collection_activities (
                activity_id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id TEXT NOT NULL,
                invoice_id TEXT,
                activity_date DATE NOT NULL,
                activity_type TEXT NOT NULL, -- 'call', 'email', 'letter', 'visit', 'legal_notice'
                activity_status TEXT NOT NULL, -- 'planned', 'completed', 'failed', 'rescheduled'
                contact_person TEXT,
                outcome TEXT, -- 'payment_promised', 'dispute_raised', 'no_response', 'payment_received'
                promised_date DATE,
                promised_amount DECIMAL(15,2),
                next_action TEXT,
                next_action_date DATE,
                priority_level TEXT DEFAULT 'medium', -- 'low', 'medium', 'high', 'urgent'
                assigned_to TEXT,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                FOREIGN KEY (customer_id) REFERENCES customers(customer_id),
                FOREIGN KEY (invoice_id) REFERENCES invoices(invoice_id)
            );

CREATE TABLE credit_management (
                credit_id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id TEXT NOT NULL,
                credit_limit DECIMAL(15,2) DEFAULT 0.00,
                credit_used DECIMAL(15,2) DEFAULT 0.00,
                credit_available DECIMAL(15,2) DEFAULT 0.00,
                payment_terms TEXT DEFAULT '30 days',
                credit_rating TEXT DEFAULT 'B', -- 'A+', 'A', 'B+', 'B', 'C', 'D'
                risk_score INTEGER DEFAULT 50, -- 0-100 scale
                last_review_date DATE,
                next_review_date DATE,
                credit_status TEXT DEFAULT 'active', -- 'active', 'suspended', 'blocked'
                overdue_amount DECIMAL(15,2) DEFAULT 0.00,
                days_overdue INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers(customer_id)
            );

CREATE TABLE financial_metrics (
                metric_id INTEGER PRIMARY KEY AUTOINCREMENT,
                metric_date DATE NOT NULL,
                total_receivables DECIMAL(15,2) DEFAULT 0.00,
                current_receivables DECIMAL(15,2) DEFAULT 0.00,
                overdue_receivables DECIMAL(15,2) DEFAULT 0.00,
                collection_efficiency DECIMAL(5,2) DEFAULT 0.00, -- percentage
                average_collection_period INTEGER DEFAULT 0, -- days
                bad_debt_provision DECIMAL(15,2) DEFAULT 0.00,
                cash_received DECIMAL(15,2) DEFAULT 0.00,
                invoices_generated DECIMAL(15,2) DEFAULT 0.00,
                payment_allocation_rate DECIMAL(5,2) DEFAULT 0.00, -- percentage
                dispute_amount DECIMAL(15,2) DEFAULT 0.00,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );

CREATE TABLE chart_of_accounts (
                account_id TEXT PRIMARY KEY,
                account_code TEXT UNIQUE NOT NULL,
                account_name TEXT NOT NULL,
                account_type TEXT NOT NULL, -- 'asset', 'liability', 'equity', 'income', 'expense'
                parent_account TEXT,
                account_level INTEGER DEFAULT 1,
                is_active BOOLEAN DEFAULT 1,
                branch_code TEXT, -- 'KHI', 'LHE', 'ALL'
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                FOREIGN KEY (parent_account) REFERENCES chart_of_accounts(account_id)
            );

CREATE TABLE cost_centers (
                cost_center_id TEXT PRIMARY KEY,
                cost_center_code TEXT UNIQUE NOT NULL,
                cost_center_name TEXT NOT NULL,
                branch_code TEXT NOT NULL, -- 'KHI', 'LHE'
                branch_name TEXT NOT NULL, -- 'Karachi', 'Lahore'
                manager_name TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT
            );

CREATE TABLE bank_details (
                bank_id TEXT PRIMARY KEY,
                bank_name TEXT NOT NULL,
                branch_name TEXT,
                account_title TEXT NOT NULL,
                account_number TEXT NOT NULL,
                iban_number TEXT,
                swift_code TEXT,
                branch_code TEXT, -- 'KHI', 'LHE', 'ALL'
                account_type TEXT DEFAULT 'current', -- 'current', 'savings'
                is_active BOOLEAN DEFAULT 1,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT
            );

CREATE TABLE pending_invoices (
                pending_id TEXT PRIMARY KEY,
                order_id TEXT NOT NULL,
                dc_number TEXT NOT NULL,
                customer_id TEXT NOT NULL,
                customer_name TEXT NOT NULL,
                customer_address TEXT,
                customer_phone TEXT,
                customer_email TEXT,
                branch_code TEXT NOT NULL, -- 'KHI', 'LHE'
                warehouse_id TEXT,
                dc_generated_date DATE NOT NULL,
                dc_generated_by TEXT,
                total_amount DECIMAL(15,2) NOT NULL,
                batch_details TEXT, -- JSON string of batch information
                pending_status TEXT DEFAULT 'pending', -- 'pending', 'approved', 'hold', 'rejected'
                pending_reason TEXT,
                assigned_to TEXT, -- Finance user assigned
                priority_level TEXT DEFAULT 'normal', -- 'low', 'normal', 'high', 'urgent'
                customer_credit_limit DECIMAL(15,2) DEFAULT 0.00,
                customer_outstanding DECIMAL(15,2) DEFAULT 0.00,
                customer_credit_days INTEGER DEFAULT 30,
                last_payment_date DATE,
                payment_history_summary TEXT,
                aging_summary TEXT,
                finance_notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_by TEXT,
                FOREIGN KEY (order_id) REFERENCES orders(order_id)
            );

CREATE TABLE customer_financial_profile (
                profile_id TEXT PRIMARY KEY,
                customer_id TEXT UNIQUE NOT NULL,
                customer_name TEXT NOT NULL,
                credit_limit DECIMAL(15,2) DEFAULT 0.00,
                credit_days INTEGER DEFAULT 30,
                payment_terms TEXT DEFAULT '30 days',
                credit_rating TEXT DEFAULT 'B', -- 'A+', 'A', 'B+', 'B', 'C', 'D'
                risk_category TEXT DEFAULT 'low', -- 'low', 'medium', 'high', 'critical'
                total_outstanding DECIMAL(15,2) DEFAULT 0.00,
                overdue_amount DECIMAL(15,2) DEFAULT 0.00,
                last_payment_date DATE,
                last_payment_amount DECIMAL(15,2) DEFAULT 0.00,
                total_invoices_count INTEGER DEFAULT 0,
                total_invoices_amount DECIMAL(15,2) DEFAULT 0.00,
                average_payment_days INTEGER DEFAULT 0,
                payment_behavior TEXT DEFAULT 'good', -- 'excellent', 'good', 'average', 'poor'
                is_credit_hold BOOLEAN DEFAULT 0,
                credit_hold_reason TEXT,
                branch_preference TEXT, -- 'KHI', 'LHE', 'BOTH'
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers(customer_id)
            );

CREATE TABLE invoices_enhanced (
                invoice_id TEXT PRIMARY KEY,
                invoice_number TEXT UNIQUE NOT NULL,
                pending_invoice_id TEXT,
                order_id TEXT NOT NULL,
                dc_number TEXT NOT NULL,
                customer_id TEXT NOT NULL,
                customer_name TEXT NOT NULL,
                branch_code TEXT NOT NULL,
                warehouse_id TEXT,
                invoice_date DATE NOT NULL,
                due_date DATE,
                payment_terms TEXT,
                subtotal_amount DECIMAL(15,2) NOT NULL,
                discount_amount DECIMAL(15,2) DEFAULT 0.00,
                total_amount DECIMAL(15,2) NOT NULL,
                paid_amount DECIMAL(15,2) DEFAULT 0.00,
                outstanding_amount DECIMAL(15,2) NOT NULL,
                payment_status TEXT DEFAULT 'unpaid', -- 'unpaid', 'partial', 'paid', 'overdue'
                invoice_status TEXT DEFAULT 'active', -- 'active', 'cancelled', 'refunded'
                batch_costing_details TEXT, -- JSON string of batch-wise costs
                generated_by TEXT,
                approved_by TEXT,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (pending_invoice_id) REFERENCES pending_invoices(pending_id),
                FOREIGN KEY (order_id) REFERENCES orders(order_id)
            );

CREATE TABLE batch_financial_data (
                batch_finance_id TEXT PRIMARY KEY,
                batch_id TEXT NOT NULL,
                batch_number TEXT NOT NULL,
                product_id TEXT NOT NULL,
                product_name TEXT NOT NULL,
                warehouse_id TEXT NOT NULL,
                branch_code TEXT NOT NULL,
                purchase_cost DECIMAL(15,2) NOT NULL,
                selling_price DECIMAL(15,2) NOT NULL,
                margin_amount DECIMAL(15,2) NOT NULL,
                margin_percentage DECIMAL(5,2) NOT NULL,
                expiry_date DATE,
                days_to_expiry INTEGER,
                expiry_risk_factor DECIMAL(3,2) DEFAULT 1.00, -- 1.00 = no risk, 0.50 = high risk
                adjusted_cost DECIMAL(15,2), -- Cost adjusted for expiry risk
                quantity_available INTEGER DEFAULT 0,
                quantity_allocated INTEGER DEFAULT 0,
                total_value DECIMAL(15,2) NOT NULL,
                last_movement_date DATE,
                movement_type TEXT, -- 'in', 'out', 'transfer', 'adjustment'
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );

CREATE TABLE payments_enhanced (
                payment_id TEXT PRIMARY KEY,
                customer_id TEXT NOT NULL,
                invoice_id TEXT,
                payment_date DATE NOT NULL,
                payment_method TEXT NOT NULL, -- 'cash', 'cheque', 'bank_transfer', 'online'
                payment_type TEXT NOT NULL, -- 'full', 'partial', 'advance', 'adjustment'
                amount DECIMAL(15,2) NOT NULL,
                reference_number TEXT,
                bank_name TEXT,
                cheque_number TEXT,
                cheque_date DATE,
                branch_code TEXT,
                received_by TEXT,
                payment_status TEXT DEFAULT 'received', -- 'received', 'cleared', 'bounced', 'cancelled'
                allocation_status TEXT DEFAULT 'unallocated', -- 'unallocated', 'partial', 'allocated'
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                FOREIGN KEY (customer_id) REFERENCES customers(customer_id),
                FOREIGN KEY (invoice_id) REFERENCES invoices_enhanced(invoice_id)
            );

CREATE TABLE payment_allocations_enhanced (
                allocation_id TEXT PRIMARY KEY,
                payment_id TEXT NOT NULL,
                invoice_id TEXT NOT NULL,
                allocated_amount DECIMAL(15,2) NOT NULL,
                allocation_date DATE NOT NULL,
                allocation_type TEXT DEFAULT 'manual', -- 'manual', 'auto'
                created_by TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (payment_id) REFERENCES payments_enhanced(payment_id),
                FOREIGN KEY (invoice_id) REFERENCES invoices_enhanced(invoice_id)
            );

CREATE TABLE financial_metrics_daily (
                metric_id TEXT PRIMARY KEY,
                metric_date DATE NOT NULL,
                branch_code TEXT NOT NULL,
                total_sales DECIMAL(15,2) DEFAULT 0.00,
                total_collections DECIMAL(15,2) DEFAULT 0.00,
                total_outstanding DECIMAL(15,2) DEFAULT 0.00,
                new_invoices_count INTEGER DEFAULT 0,
                new_invoices_amount DECIMAL(15,2) DEFAULT 0.00,
                payments_count INTEGER DEFAULT 0,
                payments_amount DECIMAL(15,2) DEFAULT 0.00,
                overdue_invoices_count INTEGER DEFAULT 0,
                overdue_amount DECIMAL(15,2) DEFAULT 0.00,
                average_collection_days INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );

CREATE TABLE payments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                payment_id TEXT UNIQUE NOT NULL,
                order_id TEXT NOT NULL,
                customer_id TEXT NOT NULL,
                amount REAL NOT NULL,
                payment_method TEXT DEFAULT 'cash',
                payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status TEXT DEFAULT 'completed',
                reference_number TEXT,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT DEFAULT 'system', payment_method_id TEXT, bank_id TEXT, cheque_number TEXT,
                FOREIGN KEY (order_id) REFERENCES orders (order_id)
            );

CREATE TABLE inventory_allocations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    allocation_id TEXT UNIQUE NOT NULL,
                    order_id TEXT NOT NULL,
                    product_id TEXT NOT NULL,
                    inventory_id INTEGER,
                    warehouse_id TEXT,
                    allocated_quantity REAL NOT NULL,
                    allocation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    status TEXT DEFAULT 'allocated',
                    released_date TIMESTAMP,
                    created_by TEXT,
                    FOREIGN KEY (order_id) REFERENCES orders (order_id),
                    FOREIGN KEY (product_id) REFERENCES products (product_id),
                    FOREIGN KEY (warehouse_id) REFERENCES warehouses (warehouse_id)
                );

CREATE TABLE audit_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    log_id TEXT UNIQUE NOT NULL,
                    user_id TEXT,
                    action TEXT NOT NULL,
                    table_name TEXT,
                    record_id TEXT,
                    old_values TEXT,
                    new_values TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    ip_address TEXT
                );

CREATE TABLE notifications (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    notification_id TEXT UNIQUE NOT NULL,
                    user_id TEXT,
                    title TEXT NOT NULL,
                    message TEXT NOT NULL,
                    notification_type TEXT DEFAULT 'info',
                    is_read INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    read_at TIMESTAMP
                , entity_type TEXT, entity_id TEXT, action_url TEXT, icon TEXT DEFAULT "fas fa-bell");

CREATE TABLE search_suggestions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    suggestion_id TEXT UNIQUE NOT NULL,
                    suggestion_text TEXT NOT NULL,
                    suggestion_type TEXT NOT NULL,
                    entity_id TEXT NOT NULL,
                    search_count INTEGER DEFAULT 0,
                    last_searched TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active INTEGER DEFAULT 1
                );

CREATE TABLE user_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT UNIQUE NOT NULL,
                    user_id TEXT NOT NULL,
                    ip_address TEXT,
                    user_agent TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active INTEGER DEFAULT 1,
                    expires_at TIMESTAMP
                );

CREATE TABLE order_attachments (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        order_id TEXT NOT NULL,
                        file_name TEXT NOT NULL,
                        file_path TEXT NOT NULL,
                        file_type TEXT DEFAULT 'PO_FILE',
                        uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (order_id) REFERENCES orders (order_id)
                    );

CREATE TABLE file_uploads (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                file_id TEXT UNIQUE NOT NULL,
                original_filename TEXT NOT NULL,
                stored_filename TEXT NOT NULL,
                file_path TEXT NOT NULL,
                file_size INTEGER,
                file_type TEXT,
                mime_type TEXT,
                upload_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                uploaded_by TEXT,
                entity_type TEXT,
                entity_id TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );

CREATE TABLE product_images (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                file_upload_id INTEGER NOT NULL,
                image_type TEXT DEFAULT 'main',
                display_order INTEGER DEFAULT 1,
                alt_text TEXT,
                is_primary BOOLEAN DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products(id),
                FOREIGN KEY (file_upload_id) REFERENCES file_uploads(id)
            );

CREATE TABLE customer_pricing (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    customer_id TEXT NOT NULL,
                    discount_percentage REAL DEFAULT 0,
                    special_pricing TEXT DEFAULT 'Standard',
                    effective_date DATE DEFAULT CURRENT_DATE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (customer_id) REFERENCES customers (customer_id)
                );

CREATE TABLE user_permissions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                permission TEXT NOT NULL,
                granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );

CREATE TABLE customer_files (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    file_id TEXT UNIQUE NOT NULL,
                    customer_id TEXT NOT NULL,
                    filename TEXT NOT NULL,
                    original_filename TEXT NOT NULL,
                    file_path TEXT NOT NULL,
                    file_size INTEGER,
                    file_type TEXT,
                    mime_type TEXT,
                    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    uploaded_by TEXT,
                    description TEXT,
                    FOREIGN KEY (customer_id) REFERENCES customers (customer_id)
                );

CREATE TABLE order_files (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    file_id TEXT UNIQUE NOT NULL,
                    order_id TEXT NOT NULL,
                    filename TEXT NOT NULL,
                    original_filename TEXT NOT NULL,
                    file_path TEXT NOT NULL,
                    file_size INTEGER,
                    file_type TEXT,
                    mime_type TEXT,
                    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    uploaded_by TEXT,
                    description TEXT,
                    file_category TEXT DEFAULT 'prescription',
                    FOREIGN KEY (order_id) REFERENCES orders (order_id)
                );

CREATE TABLE rider_bikes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                bike_id TEXT UNIQUE NOT NULL,
                rider_id TEXT NOT NULL,
                make TEXT NOT NULL,
                model TEXT NOT NULL,
                year INTEGER NOT NULL,
                color TEXT NOT NULL,
                license_plate TEXT UNIQUE NOT NULL,
                engine_number TEXT,
                chassis_number TEXT,
                registration_expiry DATE NOT NULL,
                insurance_expiry DATE NOT NULL,
                is_primary BOOLEAN DEFAULT 0,
                status TEXT DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (rider_id) REFERENCES riders (rider_id)
            );

CREATE TABLE rider_documents (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                document_id TEXT UNIQUE NOT NULL,
                rider_id TEXT NOT NULL,
                document_type TEXT NOT NULL,
                document_name TEXT NOT NULL,
                file_path TEXT NOT NULL,
                file_size INTEGER,
                mime_type TEXT,
                is_verified BOOLEAN DEFAULT 0,
                verified_by TEXT,
                verified_at TIMESTAMP,
                expiry_date DATE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (rider_id) REFERENCES riders (rider_id)
            );

CREATE TABLE bike_documents (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                document_id TEXT UNIQUE NOT NULL,
                bike_id TEXT NOT NULL,
                document_type TEXT NOT NULL,
                document_name TEXT NOT NULL,
                file_path TEXT NOT NULL,
                file_size INTEGER,
                mime_type TEXT,
                is_verified BOOLEAN DEFAULT 0,
                verified_by TEXT,
                verified_at TIMESTAMP,
                expiry_date DATE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (bike_id) REFERENCES rider_bikes (bike_id)
            );

CREATE TABLE order_approvals (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    order_id TEXT NOT NULL,
                    approved_by TEXT NOT NULL,
                    approved_at TIMESTAMP NOT NULL,
                    status TEXT DEFAULT 'approved',
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (order_id) REFERENCES orders (order_id)
                );

CREATE TABLE warehouse_processing (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    order_id TEXT NOT NULL,
                    warehouse_id TEXT NOT NULL,
                    started_at TIMESTAMP NOT NULL,
                    completed_at TIMESTAMP,
                    status TEXT DEFAULT 'processing',
                    processed_by TEXT,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (order_id) REFERENCES orders (order_id),
                    FOREIGN KEY (warehouse_id) REFERENCES warehouses (warehouse_id)
                );

CREATE TABLE payment_attachments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                payment_id TEXT NOT NULL,
                file_upload_id INTEGER NOT NULL,
                attachment_type TEXT DEFAULT 'cheque_image',
                description TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (payment_id) REFERENCES payments_enhanced (payment_id),
                FOREIGN KEY (file_upload_id) REFERENCES file_uploads (id)
            );

CREATE TABLE customer_ledger_enhanced (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id TEXT NOT NULL,
                customer_name TEXT NOT NULL,
                customer_code TEXT,
                transaction_type TEXT NOT NULL,
                transaction_id TEXT,
                transaction_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                debit_amount REAL DEFAULT 0,
                credit_amount REAL DEFAULT 0,
                balance REAL DEFAULT 0,
                description TEXT,
                reference_number TEXT,
                salesperson TEXT,
                division TEXT,
                aging_days INTEGER DEFAULT 0,
                risk_level TEXT DEFAULT 'low',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );

CREATE TABLE multi_dimensional_ledger (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                dimension_type TEXT NOT NULL,
                dimension_id TEXT NOT NULL,
                dimension_name TEXT NOT NULL,
                transaction_date DATETIME,
                transaction_type TEXT,
                amount REAL,
                customer_id TEXT,
                salesperson TEXT,
                division TEXT,
                description TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );

CREATE TABLE system_notifications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                notification_id TEXT UNIQUE NOT NULL,
                notification_type TEXT NOT NULL,
                title TEXT NOT NULL,
                message TEXT NOT NULL,
                recipient_user TEXT,
                recipient_role TEXT,
                entity_type TEXT,
                entity_id TEXT,
                priority TEXT DEFAULT 'normal',
                status TEXT DEFAULT 'unread',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                read_at DATETIME,
                action_url TEXT
            );

CREATE TABLE payment_knockoffs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    payment_id INTEGER NOT NULL,
                    order_id INTEGER NOT NULL,
                    amount DECIMAL(10,2) NOT NULL,
                    knockoff_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                    notes TEXT,
                    created_by INTEGER,
                    FOREIGN KEY (payment_id) REFERENCES payments(id),
                    FOREIGN KEY (order_id) REFERENCES orders(order_id),
                    FOREIGN KEY (created_by) REFERENCES users(id)
                );

CREATE TABLE division_analytics (
                    analytics_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    division_id TEXT NOT NULL,
                    metric_name TEXT NOT NULL,
                    metric_value DECIMAL(15,2),
                    metric_date DATE,
                    period_type TEXT DEFAULT 'monthly',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (division_id) REFERENCES "divisions_old"(division_id)
                );

CREATE TABLE division_permissions (
                    permission_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    division_id TEXT NOT NULL,
                    user_id TEXT NOT NULL,
                    permission_type TEXT NOT NULL,
                    granted_by TEXT,
                    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1,
                    FOREIGN KEY (division_id) REFERENCES "divisions_old"(division_id),
                    FOREIGN KEY (user_id) REFERENCES users(user_id),
                    UNIQUE(division_id, user_id, permission_type)
                );

CREATE TABLE division_audit_log (
                    audit_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    division_id TEXT NOT NULL,
                    action_type TEXT NOT NULL,
                    old_values TEXT,
                    new_values TEXT,
                    changed_by TEXT,
                    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    ip_address TEXT,
                    user_agent TEXT,
                    FOREIGN KEY (division_id) REFERENCES "divisions_old"(division_id)
                );

CREATE TABLE sqlite_stat1(tbl,idx,stat);

CREATE TABLE rider_performance_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    rider_id TEXT NOT NULL,
    date DATE NOT NULL,
    deliveries_completed INTEGER DEFAULT 0,
    deliveries_failed INTEGER DEFAULT 0,
    total_distance_km DECIMAL(8,2) DEFAULT 0,
    total_time_hours DECIMAL(6,2) DEFAULT 0,
    average_delivery_time_minutes DECIMAL(6,2) DEFAULT 0,
    customer_ratings_avg DECIMAL(3,2) DEFAULT 0,
    fuel_cost DECIMAL(8,2) DEFAULT 0,
    earnings DECIMAL(10,2) DEFAULT 0,
    bonus DECIMAL(8,2) DEFAULT 0,
    penalties DECIMAL(8,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (rider_id) REFERENCES riders(rider_id)
);

CREATE TABLE api_usage_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    api_key_id INTEGER NOT NULL,
    endpoint TEXT NOT NULL,
    method TEXT NOT NULL,
    status_code INTEGER,
    response_time_ms INTEGER,
    request_size_bytes INTEGER,
    response_size_bytes INTEGER,
    ip_address TEXT,
    user_agent TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (api_key_id) REFERENCES api_keys(id)
);

CREATE TABLE notification_types (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    type_name TEXT UNIQUE NOT NULL,
    display_name TEXT NOT NULL,
    icon_class TEXT DEFAULT 'fas fa-bell',
    color_class TEXT DEFAULT 'primary',
    is_active INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE user_notifications (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    notification_type_id INTEGER NOT NULL,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    data JSON, -- Additional data for the notification (order_id, product_id, etc.)
    is_read INTEGER DEFAULT 0,
    is_archived INTEGER DEFAULT 0,
    priority INTEGER DEFAULT 1, -- 1=low, 2=medium, 3=high, 4=urgent
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (notification_type_id) REFERENCES notification_types(id)
);

CREATE TABLE notification_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    notification_type_id INTEGER NOT NULL,
    is_enabled INTEGER DEFAULT 1,
    email_enabled INTEGER DEFAULT 0,
    push_enabled INTEGER DEFAULT 1,
    sound_enabled INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (notification_type_id) REFERENCES notification_types(id),
    UNIQUE(user_id, notification_type_id)
);

CREATE TABLE notification_templates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    notification_type_id INTEGER NOT NULL,
    template_name TEXT NOT NULL,
    title_template TEXT NOT NULL,
    message_template TEXT NOT NULL,
    is_active INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (notification_type_id) REFERENCES notification_types(id)
);

CREATE TABLE notification_delivery_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    notification_id INTEGER NOT NULL,
    delivery_method TEXT NOT NULL, -- 'web', 'email', 'push'
    delivery_status TEXT DEFAULT 'pending', -- 'pending', 'sent', 'failed', 'delivered'
    delivery_response TEXT,
    attempted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    delivered_at TIMESTAMP NULL,
    FOREIGN KEY (notification_id) REFERENCES user_notifications(id) ON DELETE CASCADE
);

CREATE TABLE notification_stats (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    date DATE NOT NULL,
    notification_type_id INTEGER NOT NULL,
    total_sent INTEGER DEFAULT 0,
    total_read INTEGER DEFAULT 0,
    total_clicked INTEGER DEFAULT 0,
    avg_read_time_seconds INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (notification_type_id) REFERENCES notification_types(id),
    UNIQUE(date, notification_type_id)
);

CREATE TABLE categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    category_id TEXT UNIQUE NOT NULL,
                    name TEXT NOT NULL,
                    description TEXT,
                    parent_category_id TEXT,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );

CREATE TABLE suppliers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    supplier_id TEXT UNIQUE NOT NULL,
                    name TEXT NOT NULL,
                    contact_person TEXT,
                    email TEXT,
                    phone TEXT,
                    address TEXT,
                    city TEXT,
                    country TEXT DEFAULT 'Pakistan',
                    tax_number TEXT,
                    payment_terms INTEGER DEFAULT 30,
                    credit_limit DECIMAL(15,2) DEFAULT 0.00,
                    current_balance DECIMAL(15,2) DEFAULT 0.00,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );

CREATE TABLE ai_bug_reports (
                id TEXT PRIMARY KEY,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                severity TEXT,
                category TEXT,
                description TEXT,
                file_path TEXT,
                line_number INTEGER,
                code_snippet TEXT,
                ai_analysis TEXT,
                suggested_fix TEXT,
                ai_provider TEXT,
                status TEXT DEFAULT 'new',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );

CREATE TABLE ai_error_patterns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                pattern_hash TEXT UNIQUE,
                pattern_type TEXT,
                error_signature TEXT,
                occurrence_count INTEGER DEFAULT 1,
                first_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
                ai_classification TEXT
            );

CREATE TABLE ai_performance_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                metric_type TEXT,
                metric_value REAL,
                context TEXT,
                threshold_exceeded BOOLEAN DEFAULT FALSE
            );

CREATE TABLE duplicate_resolutions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order1_id INTEGER NOT NULL,
                order2_id INTEGER NOT NULL,
                action TEXT NOT NULL,
                resolved_by TEXT NOT NULL,
                resolution_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );

CREATE TABLE user_activity_tracking (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT NOT NULL,
                user_id INTEGER NOT NULL,
                username TEXT NOT NULL,
                activity_type TEXT NOT NULL,
                page_url TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                duration INTEGER DEFAULT 0,
                metadata TEXT,
                FOREIGN KEY (session_id) REFERENCES user_sessions (session_id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            );

CREATE TABLE divisions (
                division_id TEXT PRIMARY KEY,
                code TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                description TEXT,
                parent_division_id TEXT,
                manager_id TEXT,
                status TEXT DEFAULT 'active',
                category TEXT,
                budget DECIMAL(15,2) DEFAULT 0.00,
                contact_email TEXT,
                contact_phone TEXT,
                location TEXT,
                address TEXT,
                city TEXT,
                country TEXT DEFAULT 'Pakistan',
                established_date DATE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_by TEXT,
                is_active BOOLEAN DEFAULT 1,
                sort_order INTEGER DEFAULT 0,
                metadata TEXT,
                target_revenue DECIMAL(15,2) DEFAULT 0.00,
                achieved_revenue DECIMAL(15,2) DEFAULT 0.00,
                FOREIGN KEY (manager_id) REFERENCES users(id),
                FOREIGN KEY (parent_division_id) REFERENCES divisions(division_id)
            );

CREATE TABLE batch_selections (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id TEXT NOT NULL,
                product_id TEXT NOT NULL,
                batch_number TEXT NOT NULL,
                warehouse_id TEXT NOT NULL,
                allocated_quantity REAL NOT NULL,
                selection_method TEXT DEFAULT 'manual',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                status TEXT DEFAULT 'pending'
            );

CREATE TABLE dc_generation_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT UNIQUE NOT NULL,
                order_id TEXT NOT NULL,
                status TEXT DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );

CREATE TABLE delivery_challans_backup(
  id INT,
  dc_number TEXT,
  order_id TEXT,
  warehouse_id TEXT,
  customer_id TEXT,
  customer_name TEXT,
  created_date NUM,
  dispatch_date NUM,
  delivery_date NUM,
  status TEXT,
  notes TEXT,
  created_by TEXT,
  total_items INT,
  total_amount REAL,
  batch_details TEXT,
  pdf_path TEXT
);

CREATE TABLE "delivery_challans_old" (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                dc_number TEXT UNIQUE NOT NULL,
                order_id TEXT NOT NULL,
                warehouse_id TEXT,
                customer_name TEXT,
                status TEXT DEFAULT 'created' CHECK (status IN ('created', 'dispatched', 'delivered')),
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                dispatch_date TIMESTAMP,
                delivery_date TIMESTAMP,
                created_by TEXT,
                total_items INTEGER DEFAULT 0,
                total_amount REAL DEFAULT 0,
                batch_details TEXT, -- JSON string with allocation details
                pdf_path TEXT,
                notes TEXT,
                FOREIGN KEY (order_id) REFERENCES orders (order_id),
                FOREIGN KEY (warehouse_id) REFERENCES warehouses (warehouse_id)
            );

CREATE TABLE batch_allocations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                dc_id INTEGER NOT NULL,
                inventory_id TEXT NOT NULL,
                product_id TEXT NOT NULL,
                quantity_allocated INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (dc_id) REFERENCES "delivery_challans_old" (id),
                FOREIGN KEY (inventory_id) REFERENCES inventory (inventory_id),
                FOREIGN KEY (product_id) REFERENCES products (product_id)
            );

CREATE TABLE delivery_challans (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                dc_number TEXT UNIQUE NOT NULL,
                order_id TEXT NOT NULL,
                warehouse_id TEXT,
                customer_name TEXT,
                status TEXT DEFAULT 'created',
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                dispatch_date TIMESTAMP,
                delivery_date TIMESTAMP,
                created_by TEXT,
                total_items INTEGER DEFAULT 0,
                total_amount REAL DEFAULT 0,
                batch_details TEXT,
                pdf_path TEXT,
                notes TEXT
            );

CREATE TABLE stock_movements_backup(
  id INT,
  movement_id TEXT,
  inventory_id TEXT,
  product_id TEXT,
  batch_number TEXT,
  quantity INT,
  from_warehouse_id TEXT,
  to_warehouse_id TEXT,
  movement_date NUM,
  movement_type TEXT,
  moved_by TEXT,
  notes TEXT
);

CREATE TABLE stock_movements (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        inventory_id TEXT NOT NULL,
                        movement_type TEXT NOT NULL CHECK (movement_type IN ('allocation', 'deallocation', 'adjustment')),
                        quantity INTEGER NOT NULL,
                        reference_type TEXT,
                        reference_id TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        created_by TEXT,
                        notes TEXT,
                        FOREIGN KEY (inventory_id) REFERENCES inventory (inventory_id)
                    );

