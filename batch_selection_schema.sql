-- Batch Selection System Database Schema
-- Execute this to create all required tables and columns

-- 1. Create batch_selections table
CREATE TABLE IF NOT EXISTS batch_selections (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_id TEXT NOT NULL,
    product_id TEXT NOT NULL,
    batch_number TEXT NOT NULL,
    warehouse_id TEXT NOT NULL,
    allocated_quantity REAL NOT NULL,
    selection_method TEXT NOT NULL DEFAULT 'manual',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    status TEXT DEFAULT 'pending',
    notes TEXT,
    FOREIGN KEY (order_id) REFERENCES orders(order_id),
    FOREIGN KEY (product_id) REFERENCES products(product_id),
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(warehouse_id)
);

-- 2. Create dc_generation_sessions table
CREATE TABLE IF NOT EXISTS dc_generation_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id TEXT UNIQUE NOT NULL,
    order_id TEXT NOT NULL,
    user_id TEXT,
    status TEXT DEFAULT 'active',
    allocation_method TEXT,
    warehouse_filter TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    dc_number TEXT,
    total_products INTEGER DEFAULT 0,
    allocated_products INTEGER DEFAULT 0,
    FOREIGN KEY (order_id) REFERENCES orders(order_id)
);

-- 3. Create delivery_challans table (if not exists)
CREATE TABLE IF NOT EXISTS delivery_challans (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    dc_number TEXT UNIQUE NOT NULL,
    order_id TEXT NOT NULL,
    warehouse_id TEXT,
    customer_name TEXT,
    customer_address TEXT,
    status TEXT DEFAULT 'created',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    dispatched_at TIMESTAMP,
    delivered_at TIMESTAMP,
    total_items INTEGER DEFAULT 0,
    total_amount REAL DEFAULT 0,
    batch_details TEXT,
    pdf_path TEXT,
    notes TEXT,
    FOREIGN KEY (order_id) REFERENCES orders(order_id),
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(warehouse_id)
);

-- 4. Add batch-related columns to inventory table (if they don't exist)
-- Note: SQLite doesn't support IF NOT EXISTS for ALTER TABLE, so we'll handle this in Python

-- 5. Create performance indexes
CREATE INDEX IF NOT EXISTS idx_batch_selections_order_id ON batch_selections(order_id);
CREATE INDEX IF NOT EXISTS idx_batch_selections_product_id ON batch_selections(product_id);
CREATE INDEX IF NOT EXISTS idx_batch_selections_batch_number ON batch_selections(batch_number);
CREATE INDEX IF NOT EXISTS idx_dc_sessions_order_id ON dc_generation_sessions(order_id);
CREATE INDEX IF NOT EXISTS idx_delivery_challans_order_id ON delivery_challans(order_id);
CREATE INDEX IF NOT EXISTS idx_delivery_challans_dc_number ON delivery_challans(dc_number);
CREATE INDEX IF NOT EXISTS idx_inventory_batch_number ON inventory(batch_number);
CREATE INDEX IF NOT EXISTS idx_inventory_product_warehouse ON inventory(product_id, warehouse_id);
