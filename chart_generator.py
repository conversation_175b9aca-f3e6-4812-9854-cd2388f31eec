#!/usr/bin/env python3
"""
Chart Generator Module
Provides chart generation functionality for the Medivent ERP system
"""

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import io
import base64
from typing import Dict, List, Any, Optional

# Set style for better looking charts
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class ChartGenerator:
    """
    Comprehensive chart generation class for ERP analytics
    """
    
    def __init__(self):
        self.default_figsize = (12, 8)
        self.default_dpi = 100
        
    def generate_sales_chart(self, data: List[Dict], chart_type: str = 'line') -> str:
        """
        Generate sales performance chart
        
        Args:
            data: List of sales data dictionaries
            chart_type: Type of chart ('line', 'bar', 'area')
            
        Returns:
            Base64 encoded chart image
        """
        try:
            if not data:
                return self._generate_no_data_chart("No sales data available")
            
            df = pd.DataFrame(data)
            
            fig, ax = plt.subplots(figsize=self.default_figsize, dpi=self.default_dpi)
            
            if chart_type == 'line':
                ax.plot(df['date'], df['amount'], marker='o', linewidth=2, markersize=6)
            elif chart_type == 'bar':
                ax.bar(df['date'], df['amount'], alpha=0.7)
            elif chart_type == 'area':
                ax.fill_between(df['date'], df['amount'], alpha=0.6)
            
            ax.set_title('Sales Performance Over Time', fontsize=16, fontweight='bold')
            ax.set_xlabel('Date', fontsize=12)
            ax.set_ylabel('Sales Amount (PKR)', fontsize=12)
            ax.grid(True, alpha=0.3)
            
            # Format y-axis for currency
            ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'₹{x:,.0f}'))
            
            # Rotate x-axis labels for better readability
            plt.xticks(rotation=45)
            plt.tight_layout()
            
            return self._chart_to_base64(fig)
            
        except Exception as e:
            return self._generate_error_chart(f"Error generating sales chart: {str(e)}")
    
    def generate_inventory_chart(self, data: List[Dict]) -> str:
        """
        Generate inventory levels chart
        
        Args:
            data: List of inventory data dictionaries
            
        Returns:
            Base64 encoded chart image
        """
        try:
            if not data:
                return self._generate_no_data_chart("No inventory data available")
            
            df = pd.DataFrame(data)
            
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8), dpi=self.default_dpi)
            
            # Stock levels bar chart
            ax1.bar(df['product_name'], df['current_stock'], alpha=0.7, color='skyblue')
            ax1.set_title('Current Stock Levels', fontsize=14, fontweight='bold')
            ax1.set_xlabel('Products', fontsize=12)
            ax1.set_ylabel('Stock Quantity', fontsize=12)
            ax1.tick_params(axis='x', rotation=45)
            
            # Low stock alert (pie chart)
            low_stock = df[df['current_stock'] < df['reorder_level']]
            normal_stock = df[df['current_stock'] >= df['reorder_level']]
            
            sizes = [len(low_stock), len(normal_stock)]
            labels = ['Low Stock', 'Normal Stock']
            colors = ['#ff6b6b', '#51cf66']
            
            ax2.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
            ax2.set_title('Stock Status Distribution', fontsize=14, fontweight='bold')
            
            plt.tight_layout()
            return self._chart_to_base64(fig)
            
        except Exception as e:
            return self._generate_error_chart(f"Error generating inventory chart: {str(e)}")
    
    def generate_order_status_chart(self, data: List[Dict]) -> str:
        """
        Generate order status distribution chart
        
        Args:
            data: List of order data dictionaries
            
        Returns:
            Base64 encoded chart image
        """
        try:
            if not data:
                return self._generate_no_data_chart("No order data available")
            
            df = pd.DataFrame(data)
            status_counts = df['status'].value_counts()
            
            fig, ax = plt.subplots(figsize=self.default_figsize, dpi=self.default_dpi)
            
            colors = ['#ff9999', '#66b3ff', '#99ff99', '#ffcc99', '#ff99cc']
            wedges, texts, autotexts = ax.pie(status_counts.values, labels=status_counts.index, 
                                            autopct='%1.1f%%', colors=colors, startangle=90)
            
            ax.set_title('Order Status Distribution', fontsize=16, fontweight='bold')
            
            # Enhance text appearance
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')
            
            plt.tight_layout()
            return self._chart_to_base64(fig)
            
        except Exception as e:
            return self._generate_error_chart(f"Error generating order status chart: {str(e)}")
    
    def generate_revenue_trend_chart(self, data: List[Dict]) -> str:
        """
        Generate revenue trend chart with moving average
        
        Args:
            data: List of revenue data dictionaries
            
        Returns:
            Base64 encoded chart image
        """
        try:
            if not data:
                return self._generate_no_data_chart("No revenue data available")
            
            df = pd.DataFrame(data)
            df['date'] = pd.to_datetime(df['date'])
            df = df.sort_values('date')
            
            # Calculate moving average
            df['moving_avg'] = df['revenue'].rolling(window=7, min_periods=1).mean()
            
            fig, ax = plt.subplots(figsize=self.default_figsize, dpi=self.default_dpi)
            
            # Plot revenue and moving average
            ax.plot(df['date'], df['revenue'], label='Daily Revenue', alpha=0.7, linewidth=1)
            ax.plot(df['date'], df['moving_avg'], label='7-Day Moving Average', 
                   linewidth=3, color='red')
            
            ax.set_title('Revenue Trend Analysis', fontsize=16, fontweight='bold')
            ax.set_xlabel('Date', fontsize=12)
            ax.set_ylabel('Revenue (PKR)', fontsize=12)
            ax.legend()
            ax.grid(True, alpha=0.3)
            
            # Format y-axis for currency
            ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'₹{x:,.0f}'))
            
            plt.xticks(rotation=45)
            plt.tight_layout()
            
            return self._chart_to_base64(fig)
            
        except Exception as e:
            return self._generate_error_chart(f"Error generating revenue trend chart: {str(e)}")
    
    def generate_top_products_chart(self, data: List[Dict], top_n: int = 10) -> str:
        """
        Generate top products chart
        
        Args:
            data: List of product sales data
            top_n: Number of top products to show
            
        Returns:
            Base64 encoded chart image
        """
        try:
            if not data:
                return self._generate_no_data_chart("No product sales data available")
            
            df = pd.DataFrame(data)
            top_products = df.nlargest(top_n, 'total_sales')
            
            fig, ax = plt.subplots(figsize=self.default_figsize, dpi=self.default_dpi)
            
            bars = ax.barh(top_products['product_name'], top_products['total_sales'])
            
            # Color bars with gradient
            colors = plt.cm.viridis(np.linspace(0, 1, len(bars)))
            for bar, color in zip(bars, colors):
                bar.set_color(color)
            
            ax.set_title(f'Top {top_n} Products by Sales', fontsize=16, fontweight='bold')
            ax.set_xlabel('Total Sales (PKR)', fontsize=12)
            ax.set_ylabel('Products', fontsize=12)
            
            # Format x-axis for currency
            ax.xaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'₹{x:,.0f}'))
            
            plt.tight_layout()
            return self._chart_to_base64(fig)
            
        except Exception as e:
            return self._generate_error_chart(f"Error generating top products chart: {str(e)}")
    
    def _chart_to_base64(self, fig) -> str:
        """Convert matplotlib figure to base64 string"""
        img_buffer = io.BytesIO()
        fig.savefig(img_buffer, format='png', bbox_inches='tight', 
                   facecolor='white', edgecolor='none')
        img_buffer.seek(0)
        img_str = base64.b64encode(img_buffer.getvalue()).decode()
        plt.close(fig)  # Close figure to free memory
        return f"data:image/png;base64,{img_str}"
    
    def _generate_no_data_chart(self, message: str) -> str:
        """Generate a chart showing no data message"""
        fig, ax = plt.subplots(figsize=(8, 6), dpi=self.default_dpi)
        ax.text(0.5, 0.5, message, ha='center', va='center', 
               fontsize=16, transform=ax.transAxes)
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        return self._chart_to_base64(fig)
    
    def _generate_error_chart(self, error_message: str) -> str:
        """Generate a chart showing error message"""
        fig, ax = plt.subplots(figsize=(8, 6), dpi=self.default_dpi)
        ax.text(0.5, 0.5, f"Chart Generation Error:\n{error_message}", 
               ha='center', va='center', fontsize=12, transform=ax.transAxes,
               bbox=dict(boxstyle="round,pad=0.3", facecolor="lightcoral", alpha=0.7))
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        return self._chart_to_base64(fig)

# Convenience functions for backward compatibility
def generate_sales_chart(data, chart_type='line'):
    """Generate sales chart - backward compatibility function"""
    generator = ChartGenerator()
    return generator.generate_sales_chart(data, chart_type)

def generate_inventory_chart(data):
    """Generate inventory chart - backward compatibility function"""
    generator = ChartGenerator()
    return generator.generate_inventory_chart(data)

def generate_order_chart(data):
    """Generate order status chart - backward compatibility function"""
    generator = ChartGenerator()
    return generator.generate_order_status_chart(data)
