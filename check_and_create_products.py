#!/usr/bin/env python3
"""
Check products table and create sample data if needed
"""

import sqlite3
import os

def check_and_create_products():
    """Check products table and create sample data if needed"""
    
    print("🔍 CHECKING PRODUCTS TABLE")
    print("=" * 50)
    
    try:
        # Connect to database
        db_path = 'instance/medivent.db'
        if not os.path.exists(db_path):
            print(f"❌ Database not found at {db_path}")
            return
        
        db = sqlite3.connect(db_path)
        db.row_factory = sqlite3.Row
        cursor = db.cursor()
        
        # Check if products table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='products'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("❌ Products table does not exist. Creating it...")
            create_products_table(cursor, db)
        else:
            print("✅ Products table exists")
        
        # Check table schema
        print("\n📋 PRODUCTS TABLE SCHEMA:")
        cursor.execute("PRAGMA table_info(products)")
        columns = cursor.fetchall()
        for col in columns:
            print(f"   • {col['name']} ({col['type']}) - Primary Key: {bool(col['pk'])}")
        
        # Check existing products
        cursor.execute("SELECT COUNT(*) as count FROM products")
        count = cursor.fetchone()['count']
        print(f"\n📊 EXISTING PRODUCTS: {count}")
        
        if count == 0:
            print("❌ No products found. Creating sample products...")
            create_sample_products(cursor, db)
        else:
            print("✅ Products exist. Showing first 5:")
            cursor.execute("SELECT * FROM products LIMIT 5")
            products = cursor.fetchall()
            for product in products:
                # Fix sqlite3.Row object access - use dict() or direct key access
            product_dict = dict(product)
            product_id = product_dict.get('id', product_dict.get('product_id', 'N/A'))
            product_name = product_dict.get('name', 'N/A')
            product_price = product_dict.get('unit_price', 'N/A')
            print(f"   • ID: {product_id}, Name: {product_name}, Price: {product_price}")
        
        db.close()
        print("\n✅ PRODUCTS CHECK COMPLETE")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()

def create_products_table(cursor, db):
    """Create products table with proper schema"""
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS products (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            description TEXT,
            unit_price REAL DEFAULT 0,
            category TEXT,
            manufacturer TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)
    db.commit()
    print("✅ Products table created")

def create_sample_products(cursor, db):
    """Create sample products for testing"""
    sample_products = [
        ('P001', 'Paracetamol 500mg', 'Pain relief tablets', 25.50, 'Tablets', 'Medivent Pharma'),
        ('P002', 'Amoxicillin 250mg', 'Antibiotic capsules', 45.00, 'Capsules', 'Medivent Pharma'),
        ('P003', 'Cough Syrup', 'Cough relief syrup 100ml', 85.00, 'Syrup', 'Medivent Pharma'),
        ('P004', 'Vitamin C 1000mg', 'Vitamin C tablets', 120.00, 'Tablets', 'Medivent Pharma'),
        ('P005', 'Insulin Injection', 'Diabetes medication', 450.00, 'Injections', 'Medivent Pharma'),
        ('P006', 'Aspirin 75mg', 'Blood thinner tablets', 35.00, 'Tablets', 'Medivent Pharma'),
        ('P007', 'Omeprazole 20mg', 'Acid reflux capsules', 65.00, 'Capsules', 'Medivent Pharma'),
        ('P008', 'Multivitamin Syrup', 'Children multivitamin 200ml', 95.00, 'Syrup', 'Medivent Pharma'),
    ]
    
    for product in sample_products:
        cursor.execute("""
            INSERT OR REPLACE INTO products (id, name, description, unit_price, category, manufacturer)
            VALUES (?, ?, ?, ?, ?, ?)
        """, product)
    
    db.commit()
    print(f"✅ Created {len(sample_products)} sample products")

if __name__ == "__main__":
    check_and_create_products()
