import sqlite3

conn = sqlite3.connect('instance/medivent.db')
cursor = conn.cursor()

print('=== ACCOUNTS_RECEIVABLE TABLE SCHEMA ===')
try:
    cursor.execute('PRAGMA table_info(accounts_receivable)')
    schema = cursor.fetchall()
    for col in schema:
        print(f'{col[1]:<20} {col[2]:<15} NULL:{not col[3]} DEFAULT:{col[4]}')
        
    print('\n=== SAMPLE ACCOUNTS_RECEIVABLE DATA ===')
    cursor.execute('SELECT * FROM accounts_receivable LIMIT 3')
    sample = cursor.fetchall()
    if sample:
        print('Sample rows:')
        for row in sample:
            print(f'  {row}')
    else:
        print('No data in accounts_receivable table')
        
except Exception as e:
    print(f'Error: {e}')

print('\n=== ORDERS TABLE CUSTOMER_ID COLUMN ===')
cursor.execute('SELECT order_id, customer_id, customer_name FROM orders WHERE customer_id IS NULL LIMIT 5')
null_customer_ids = cursor.fetchall()
print(f'Orders with NULL customer_id: {len(null_customer_ids)}')
for row in null_customer_ids:
    print(f'  {row[0]} | {row[1]} | {row[2]}')

conn.close()
