#!/usr/bin/env python3
"""
Check what content is actually in the assignment dashboard
"""

import requests

def check_assignment_dashboard_content():
    """Check the actual content of the assignment dashboard"""
    print("🔍 CHECKING ASSIGNMENT DASHBOARD CONTENT")
    print("=" * 60)
    
    try:
        response = requests.get('http://localhost:5000/riders/assignment-dashboard', timeout=15)
        
        if response.status_code == 200:
            print("✅ Assignment Dashboard loads successfully")
            
            # Check for strftime errors first
            strftime_errors = [
                "'str' object has no attribute 'strftime'",
                "Error loading assignment form",
                "AttributeError",
                "strftime"
            ]
            
            has_strftime_error = False
            for error in strftime_errors:
                if error in response.text:
                    print(f"❌ STRFTIME ERROR FOUND: {error}")
                    has_strftime_error = True
            
            if not has_strftime_error:
                print("✅ NO STRFTIME ERRORS - MAIN ISSUE IS RESOLVED!")
            
            # Show a sample of the content
            print("\n📄 CONTENT SAMPLE (first 1000 characters):")
            print("-" * 60)
            print(response.text[:1000])
            print("-" * 60)
            
            # Check for key elements
            key_elements = [
                "Rider Assignment Dashboard",
                "Orders Ready",
                "Available Riders",
                "Assignment",
                "Ready for Pickup",
                "packed",
                "rider"
            ]
            
            print("\n🔍 SEARCHING FOR KEY ELEMENTS:")
            for element in key_elements:
                if element.lower() in response.text.lower():
                    print(f"✅ Found: '{element}'")
                else:
                    print(f"❌ Missing: '{element}'")
            
            return not has_strftime_error
            
        else:
            print(f"❌ Assignment Dashboard returned HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error checking assignment dashboard: {e}")
        return False

if __name__ == "__main__":
    success = check_assignment_dashboard_content()
    if success:
        print("\n🎉 STRFTIME ERROR IS COMPLETELY RESOLVED!")
    else:
        print("\n⚠️ Issues still remain")
