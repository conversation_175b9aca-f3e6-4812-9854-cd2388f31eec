#!/usr/bin/env python3
"""
Check what error is actually showing in the browser by simulating the exact request
"""

import requests
import time
import sys

def simulate_browser_request():
    """Simulate the exact request a browser would make"""
    print("🌐 SIMULATING BROWSER REQUEST TO ASSIGNMENT DASHBOARD")
    print("=" * 70)
    
    # Create session with browser-like headers
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    })
    
    try:
        # First, get the assignment dashboard page
        print("📄 Loading assignment dashboard page...")
        response = session.get('http://localhost:5000/riders/assignment-dashboard', timeout=15)
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            # Check for the error message that the user reported
            error_message = "Error loading assignment form: 'str object' has no attribute 'strftime'"
            
            if error_message in response.text:
                print("🎯 FOUND THE EXACT ERROR MESSAGE!")
                
                # Find the context around the error
                error_index = response.text.find(error_message)
                start = max(0, error_index - 200)
                end = min(len(response.text), error_index + len(error_message) + 200)
                context = response.text[start:end]
                
                print("\n📍 ERROR CONTEXT:")
                print("-" * 50)
                print(context)
                print("-" * 50)
                
                return False
            else:
                print("✅ No error message found in main page")
                
                # Now simulate the AJAX call that the sidebar makes
                print("\n🔄 Simulating AJAX call to live tracking API...")
                
                # Add AJAX headers
                ajax_headers = {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json, text/javascript, */*; q=0.01',
                    'Content-Type': 'application/json',
                }
                
                api_response = session.get(
                    'http://localhost:5000/riders/api/live-tracking-data',
                    headers=ajax_headers,
                    timeout=10
                )
                
                print(f"API Status: {api_response.status_code}")
                
                if api_response.status_code == 200:
                    # Check if it's JSON or HTML
                    content_type = api_response.headers.get('content-type', '')
                    print(f"Content-Type: {content_type}")
                    
                    if 'application/json' in content_type:
                        try:
                            data = api_response.json()
                            print("✅ Valid JSON response received")
                            return True
                        except:
                            print("❌ Invalid JSON in response")
                            print(f"Response: {api_response.text[:300]}")
                            return False
                    else:
                        print("❌ API returned HTML instead of JSON")
                        
                        # Check for strftime error in API response
                        if "'str' object has no attribute 'strftime'" in api_response.text:
                            print("🎯 FOUND STRFTIME ERROR IN API RESPONSE!")
                            
                            # Find the context
                            error_index = api_response.text.find("'str' object has no attribute 'strftime'")
                            start = max(0, error_index - 300)
                            end = min(len(api_response.text), error_index + 100)
                            context = api_response.text[start:end]
                            
                            print("\n📍 API ERROR CONTEXT:")
                            print("-" * 50)
                            print(context)
                            print("-" * 50)
                        
                        return False
                else:
                    print(f"❌ API returned status {api_response.status_code}")
                    print(f"Response: {api_response.text[:300]}")
                    return False
        else:
            print(f"❌ Main page returned status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def check_server_logs():
    """Check if there are any server-side errors"""
    print("\n📋 CHECKING FOR SERVER-SIDE ERRORS")
    print("=" * 70)
    
    try:
        # Make a request that might trigger the error
        response = requests.get('http://localhost:5000/riders/api/live-tracking-data', timeout=5)
        print(f"Direct API call status: {response.status_code}")
        
        if response.status_code == 500:
            print("🎯 Server error detected!")
            if "'str' object has no attribute 'strftime'" in response.text:
                print("✅ Confirmed: strftime error in API endpoint")
                return True
        
        return False
        
    except Exception as e:
        print(f"Error checking server logs: {e}")
        return False

if __name__ == "__main__":
    print("🔍 COMPREHENSIVE ERROR DETECTION")
    print("=" * 80)
    
    # Check server logs first
    server_error = check_server_logs()
    
    # Simulate browser request
    browser_success = simulate_browser_request()
    
    if server_error:
        print("\n🎯 CONFIRMED: Server-side strftime error detected")
        sys.exit(1)
    elif browser_success:
        print("\n🎉 SUCCESS: No errors detected")
        sys.exit(0)
    else:
        print("\n❌ FAILURE: Issues detected")
        sys.exit(1)
