import sqlite3

conn = sqlite3.connect('instance/medivent.db')
cursor = conn.cursor()

print("=== CHALLANS TABLE DETAILED SCHEMA ===")
cursor.execute("PRAGMA table_info(challans)")
columns = cursor.fetchall()

for col in columns:
    null_constraint = "NOT NULL" if col[3] else "NULL"
    default_value = f"DEFAULT {col[4]}" if col[4] else ""
    print(f"{col[1]} ({col[2]}) - {null_constraint} {default_value}")

print("\n=== SAMPLE CHALLANS DATA ===")
cursor.execute("SELECT * FROM challans LIMIT 3")
samples = cursor.fetchall()

if samples:
    for sample in samples:
        print(f"ID: {sample[0]}")
        print(f"DC Number: {sample[2] if len(sample) > 2 else 'N/A'}")
        print(f"Order ID: {sample[1] if len(sample) > 1 else 'N/A'}")
        print("---")
else:
    print("No challans found")

conn.close()
