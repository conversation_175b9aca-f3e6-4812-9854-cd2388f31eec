import sqlite3

# Check the correct database that <PERSON>las<PERSON> is using
conn = sqlite3.connect('instance/medivent.db')
conn.row_factory = sqlite3.Row
cursor = conn.cursor()

order_id = 'ORD175398287142AFD661'

print(f"=== Checking correct database: instance/medivent.db ===")

# Check tables
cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
tables = cursor.fetchall()
print(f"Found {len(tables)} tables:")
for table in tables:
    print(f"  - {table[0]}")

# Check if order_items table exists
if any('order_items' in table[0] for table in tables):
    print("\n✅ order_items table found!")
    
    # Check order_items for this specific order
    items = cursor.execute('SELECT * FROM order_items WHERE order_id = ?', (order_id,)).fetchall()
    print(f"Found {len(items)} order items for {order_id}")
    
    if items:
        print("Order items:")
        for item in items:
            print(f"  - Product: {item['product_name'] if 'product_name' in item.keys() else 'N/A'} ({item['product_id']})")
            print(f"    Quantity: {item['quantity']}")
            print(f"    Status: {item['status']}")
            print(f"    Strength: {item['strength'] if 'strength' in item.keys() else 'N/A'}")
            print(f"    Unit Price: {item['unit_price']}")
            print("    ---")
    else:
        print("No order items found for this order")
        
        # Check if order exists
        order = cursor.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
        if order:
            print(f"Order exists: {order['customer_name']}, Status: {order['status']}")
        else:
            print("Order does not exist")
            
        # Show sample order_items
        sample_items = cursor.execute('SELECT order_id, product_id, quantity FROM order_items LIMIT 5').fetchall()
        print(f"\nSample order_items ({len(sample_items)} total):")
        for item in sample_items:
            print(f"  {item['order_id']} - {item['product_id']} - Qty: {item['quantity']}")
else:
    print("\n❌ order_items table not found!")

conn.close()
