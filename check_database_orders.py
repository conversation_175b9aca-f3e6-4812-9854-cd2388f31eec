#!/usr/bin/env python3
"""
Check database orders and create sample data if needed
"""

import sqlite3
import os
from datetime import datetime

def check_database():
    """Check database structure and data"""
    db_path = 'instance/medivent.db'
    
    # Ensure instance directory exists
    os.makedirs('instance', exist_ok=True)
    
    try:
        db = sqlite3.connect(db_path)
        db.row_factory = sqlite3.Row
        
        print("🔍 Checking database structure...")
        
        # Check if orders table exists
        cursor = db.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='orders'")
        if not cursor.fetchone():
            print("❌ Orders table does not exist")
            create_orders_table(db)
        else:
            print("✅ Orders table exists")
        
        # Check if order_items table exists
        cursor = db.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='order_items'")
        if not cursor.fetchone():
            print("❌ Order_items table does not exist")
            create_order_items_table(db)
        else:
            print("✅ Order_items table exists")
        
        # Check if products table exists
        cursor = db.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='products'")
        if not cursor.fetchone():
            print("❌ Products table does not exist")
            create_products_table(db)
        else:
            print("✅ Products table exists")
        
        # Check for specific order
        cursor = db.execute("SELECT * FROM orders WHERE order_id = 'ORD00000155'")
        order = cursor.fetchone()
        
        if not order:
            print("❌ Order ORD00000155 does not exist")
            create_sample_order(db)
        else:
            print("✅ Order ORD00000155 exists")
            print(f"   Customer: {order['customer_name']}")
            print(f"   Amount: {order['order_amount']}")
            print(f"   Status: {order['status']}")
        
        # Check order items
        cursor = db.execute("SELECT COUNT(*) as count FROM order_items WHERE order_id = 'ORD00000155'")
        count = cursor.fetchone()['count']
        print(f"✅ Order has {count} items")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def create_orders_table(db):
    """Create orders table"""
    print("📝 Creating orders table...")
    db.execute('''
        CREATE TABLE IF NOT EXISTS orders (
            order_id TEXT PRIMARY KEY,
            customer_name TEXT NOT NULL,
            customer_phone TEXT,
            customer_address TEXT,
            order_date TEXT NOT NULL,
            order_amount REAL DEFAULT 0,
            status TEXT DEFAULT 'Pending',
            special_instructions TEXT,
            priority_level INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    db.commit()

def create_order_items_table(db):
    """Create order_items table"""
    print("📝 Creating order_items table...")
    db.execute('''
        CREATE TABLE IF NOT EXISTS order_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_id TEXT NOT NULL,
            product_id TEXT,
            product_name TEXT NOT NULL,
            quantity INTEGER NOT NULL,
            unit_price REAL DEFAULT 0,
            total_price REAL DEFAULT 0,
            FOREIGN KEY (order_id) REFERENCES orders (order_id)
        )
    ''')
    db.commit()

def create_products_table(db):
    """Create products table"""
    print("📝 Creating products table...")
    db.execute('''
        CREATE TABLE IF NOT EXISTS products (
            product_id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            strength TEXT,
            manufacturer TEXT,
            price REAL DEFAULT 0,
            stock_quantity INTEGER DEFAULT 0
        )
    ''')
    db.commit()

def create_sample_order(db):
    """Create sample order for testing"""
    print("📝 Creating sample order ORD00000155...")
    
    # Insert sample products first
    db.execute('''
        INSERT OR REPLACE INTO products (product_id, name, strength, manufacturer, price, stock_quantity)
        VALUES ('P001', 'Paracetamol', '500mg', 'Medivent Pharma', 10.50, 100)
    ''')
    
    db.execute('''
        INSERT OR REPLACE INTO products (product_id, name, strength, manufacturer, price, stock_quantity)
        VALUES ('P002', 'Amoxicillin', '250mg', 'Medivent Pharma', 25.75, 50)
    ''')
    
    # Insert sample order
    db.execute('''
        INSERT OR REPLACE INTO orders (
            order_id, customer_name, customer_phone, customer_address,
            order_date, order_amount, status, special_instructions, priority_level
        ) VALUES (
            'ORD00000155', '3Minur', '00211111', '4',
            '2025-07-29 00:01', 444.0, 'Pending', 'Test order for QR code', 1
        )
    ''')
    
    # Insert sample order items
    db.execute('''
        INSERT OR REPLACE INTO order_items (
            order_id, product_id, product_name, quantity, unit_price, total_price
        ) VALUES (
            'ORD00000155', 'P001', 'Paracetamol', 10, 10.50, 105.0
        )
    ''')
    
    db.execute('''
        INSERT OR REPLACE INTO order_items (
            order_id, product_id, product_name, quantity, unit_price, total_price
        ) VALUES (
            'ORD00000155', 'P002', 'Amoxicillin', 5, 25.75, 128.75
        )
    ''')
    
    db.commit()
    print("✅ Sample order created successfully")

if __name__ == "__main__":
    print("🔧 DATABASE SETUP AND VERIFICATION")
    print("=" * 50)
    
    success = check_database()
    
    if success:
        print("\n✅ Database setup completed successfully")
    else:
        print("\n❌ Database setup failed")
