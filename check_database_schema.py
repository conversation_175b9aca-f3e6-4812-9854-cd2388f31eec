#!/usr/bin/env python3
"""
Check database schema for missing columns causing errors
"""

import sqlite3

def check_database_schema():
    """Check database schema for column issues"""
    print("🔍 CHECKING DATABASE SCHEMA FOR COLUMN ERRORS")
    print("=" * 60)
    
    try:
        # Connect to database
        db = sqlite3.connect('instance/medivent.db')
        db.row_factory = sqlite3.Row
        
        # Check divisions table schema
        print("\n📋 DIVISIONS TABLE SCHEMA:")
        cursor = db.execute('PRAGMA table_info(divisions)')
        divisions_columns = cursor.fetchall()
        for col in divisions_columns:
            print(f"   {col[1]} ({col[2]})")
        
        # Check if manager column exists
        manager_exists = any(col[1] == 'manager' for col in divisions_columns)
        print(f"\n❓ Does divisions table have 'manager' column? {manager_exists}")
        
        # Check stock_movements table schema  
        print("\n📋 STOCK_MOVEMENTS TABLE SCHEMA:")
        try:
            cursor = db.execute('PRAGMA table_info(stock_movements)')
            movements_columns = cursor.fetchall()
            for col in movements_columns:
                print(f"   {col[1]} ({col[2]})")
            
            # Check if product_id column exists
            product_id_exists = any(col[1] == 'product_id' for col in movements_columns)
            print(f"\n❓ Does stock_movements table have 'product_id' column? {product_id_exists}")
            
        except Exception as e:
            print(f"❌ Error checking stock_movements table: {e}")
        
        # Check products table for ASP column
        print("\n📋 PRODUCTS TABLE SCHEMA:")
        cursor = db.execute('PRAGMA table_info(products)')
        products_columns = cursor.fetchall()
        for col in products_columns:
            print(f"   {col[1]} ({col[2]})")
        
        # Check if ASP column exists
        asp_exists = any(col[1] == 'asp' for col in products_columns)
        print(f"\n❓ Does products table have 'asp' column? {asp_exists}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

if __name__ == "__main__":
    check_database_schema()
