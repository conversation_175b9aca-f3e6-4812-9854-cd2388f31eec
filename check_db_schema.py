#!/usr/bin/env python3
"""
Check database schema and identify missing tables
"""

import sqlite3
import os

def check_database_schema():
    """Check the current database schema"""
    db_path = 'medivent_erp.db'
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found: {db_path}")
        return False
    
    try:
        db = sqlite3.connect(db_path)
        cursor = db.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        tables = cursor.fetchall()
        
        print(f"📊 Database: {db_path}")
        print(f"📋 Total tables: {len(tables)}")
        print("\n🗂️  Tables in database:")
        
        table_names = []
        for table in tables:
            table_name = table[0]
            table_names.append(table_name)
            print(f"   ✅ {table_name}")
        
        # Check for customers table specifically
        print(f"\n🔍 Checking for 'customers' table:")
        if 'customers' in table_names:
            print("   ✅ customers table EXISTS")
            
            # Get customers table schema
            cursor.execute("PRAGMA table_info(customers)")
            columns = cursor.fetchall()
            print(f"   📋 Columns ({len(columns)}):")
            for col in columns:
                print(f"      - {col[1]} ({col[2]})")
        else:
            print("   ❌ customers table MISSING")
        
        # Check for orders table and its schema
        print(f"\n🔍 Checking 'orders' table schema:")
        if 'orders' in table_names:
            cursor.execute("PRAGMA table_info(orders)")
            columns = cursor.fetchall()
            print(f"   📋 Orders table columns ({len(columns)}):")
            for col in columns:
                print(f"      - {col[1]} ({col[2]})")
                
            # Check if orders table has customer_id column
            column_names = [col[1] for col in columns]
            if 'customer_id' in column_names:
                print("   ✅ orders.customer_id column EXISTS")
            else:
                print("   ❌ orders.customer_id column MISSING")
        else:
            print("   ❌ orders table MISSING")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Error checking database: {e}")
        return False

if __name__ == '__main__':
    check_database_schema()
