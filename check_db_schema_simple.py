#!/usr/bin/env python3
"""
Simple database schema checker
"""

import sqlite3
import os

def check_database_schema():
    """Check database schema and foreign key constraints"""
    
    db_path = 'instance/medivent.db'
    if not os.path.exists(db_path):
        print(f"❌ Database not found at: {db_path}")
        return
    
    print(f"✅ Database found at: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Get accounts_receivable table schema
        print("\n🔍 ACCOUNTS_RECEIVABLE TABLE SCHEMA:")
        print("=" * 60)
        cursor.execute("PRAGMA table_info(accounts_receivable)")
        columns = cursor.fetchall()
        
        if not columns:
            print("❌ accounts_receivable table does not exist!")
            return
            
        for col in columns:
            print(f"{col[1]:<20} {col[2]:<15} {'NOT NULL' if col[3] else 'NULL':<10} {'PK' if col[5] else '':<5}")

        # Get foreign key constraints
        print("\n🔗 FOREIGN KEY CONSTRAINTS:")
        print("=" * 60)
        cursor.execute("PRAGMA foreign_key_list(accounts_receivable)")
        fks = cursor.fetchall()
        
        if not fks:
            print("ℹ️  No foreign key constraints found")
        else:
            for fk in fks:
                print(f"Column: {fk[3]} -> References: {fk[2]}.{fk[4]}")

        # Check customers table
        print("\n👥 CUSTOMERS TABLE SAMPLE:")
        print("=" * 60)
        cursor.execute("SELECT customer_id, name FROM customers LIMIT 5")
        customers = cursor.fetchall()
        
        if not customers:
            print("❌ No customers found!")
        else:
            for customer in customers:
                print(f"{customer[0]:<15} {customer[1]}")

        # Check orders with NULL customer_id
        print("\n📋 ORDERS WITH NULL CUSTOMER_ID:")
        print("=" * 60)
        cursor.execute("SELECT order_id, customer_name, customer_id FROM orders WHERE customer_id IS NULL LIMIT 5")
        orders = cursor.fetchall()
        
        if not orders:
            print("✅ No orders with NULL customer_id found")
        else:
            for order in orders:
                print(f"{order[0]:<15} {order[1]:<20} {order[2] or 'NULL'}")

        # Check specific order that's causing the error
        print("\n🎯 CHECKING SPECIFIC ORDER (ORD00000147):")
        print("=" * 60)
        cursor.execute("SELECT order_id, customer_name, customer_id FROM orders WHERE order_id = 'ORD00000147'")
        specific_order = cursor.fetchone()
        
        if specific_order:
            print(f"Order ID: {specific_order[0]}")
            print(f"Customer Name: {specific_order[1]}")
            print(f"Customer ID: {specific_order[2] or 'NULL'}")
        else:
            print("❌ Order ORD00000147 not found")

        conn.close()
        
    except Exception as e:
        print(f"❌ Error checking database: {e}")

if __name__ == "__main__":
    check_database_schema()
