#!/usr/bin/env python3
"""
Check database status for order and DC/invoice tracking
"""

import sqlite3
import json

def check_database():
    try:
        # Connect to database
        conn = sqlite3.connect('instance/medivent.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        print('=== CHECKING DATABASE SCHEMA ===')

        # Check if delivery_challans table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='delivery_challans'")
        dc_table = cursor.fetchone()
        print(f'delivery_challans table exists: {bool(dc_table)}')

        # Check orders table structure
        cursor.execute('PRAGMA table_info(orders)')
        orders_columns = cursor.fetchall()
        print('\nOrders table columns:')
        for col in orders_columns:
            print(f'  {col[1]} ({col[2]})')

        # Check if there's a specific order
        cursor.execute('SELECT order_id, status FROM orders WHERE order_id = ?', ('ORD1753983391CA9E99E1',))
        order = cursor.fetchone()
        if order:
            print(f'\nOrder ORD1753983391CA9E99E1 status: {order[1]}')
        else:
            print('\nOrder ORD1753983391CA9E99E1 not found')

        # Check if DC exists for this order
        if dc_table:
            cursor.execute('SELECT dc_number FROM delivery_challans WHERE order_id = ?', ('ORD1753983391CA9E99E1',))
            dc = cursor.fetchone()
            if dc:
                print(f'DC exists for order: {dc[0]}')
            else:
                print('No DC found for this order')

        # Check invoices table
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='invoices'")
        invoice_table = cursor.fetchone()
        print(f'\ninvoices table exists: {bool(invoice_table)}')

        if invoice_table:
            cursor.execute('SELECT invoice_number FROM invoices WHERE order_id = ?', ('ORD1753983391CA9E99E1',))
            invoice = cursor.fetchone()
            if invoice:
                print(f'Invoice exists for order: {invoice[0]}')
            else:
                print('No invoice found for this order')

        # Check all orders and their statuses
        cursor.execute('SELECT order_id, status FROM orders LIMIT 10')
        orders = cursor.fetchall()
        print('\nSample orders:')
        for order in orders:
            print(f'  {order[0]}: {order[1]}')

        conn.close()
        print('\n=== DATABASE CHECK COMPLETE ===')

    except Exception as e:
        print(f'Error checking database: {e}')

if __name__ == '__main__':
    check_database()
