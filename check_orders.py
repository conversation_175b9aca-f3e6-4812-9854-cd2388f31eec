#!/usr/bin/env python3
"""
Quick script to check order statuses in the database
"""
import sqlite3
import sys
import os

def check_orders():
    db_path = 'medivent.db'

    print(f"Checking database: {db_path}")
    print(f"File exists: {os.path.exists(db_path)}")
    if os.path.exists(db_path):
        print(f"File size: {os.path.getsize(db_path)} bytes")

    if not os.path.exists(db_path):
        print(f"Database file {db_path} not found!")
        return

    try:
        db = sqlite3.connect(db_path)
        db.row_factory = sqlite3.Row
        cursor = db.cursor()

        print('\n=== CHECKING TABLES ===')
        tables = cursor.execute("SELECT name FROM sqlite_master WHERE type='table'").fetchall()
        if tables:
            print(f"Found {len(tables)} tables:")
            for table in tables:
                try:
                    count = cursor.execute(f'SELECT COUNT(*) FROM {table["name"]}').fetchone()[0]
                    print(f'  {table["name"]}: {count} records')
                except Exception as e:
                    print(f'  {table["name"]}: Error counting - {e}')
        else:
            print('No tables found in database')
            return

        print('\n=== CHECKING ORDER STATUSES ===')
        try:
            statuses = cursor.execute('SELECT status, COUNT(*) as count FROM orders GROUP BY status').fetchall()
            for status in statuses:
                print(f'{status["status"]}: {status["count"]} orders')
        except Exception as e:
            print(f'Error checking orders: {e}')
        
        print('\n=== CHECKING FINANCE PENDING ORDERS ===')
        finance_pending = cursor.execute('''
            SELECT order_id, customer_name, order_amount, status, payment_status 
            FROM orders 
            WHERE status = "Finance Pending" 
            LIMIT 5
        ''').fetchall()
        
        if finance_pending:
            for order in finance_pending:
                print(f'Order {order["order_id"]}: {order["customer_name"]} - Rs.{order["order_amount"]} - Status: {order["status"]} - Payment: {order["payment_status"]}')
        else:
            print('No Finance Pending orders found')
        
        print('\n=== CHECKING RECENT ORDERS ===')
        recent = cursor.execute('''
            SELECT order_id, customer_name, status, order_date 
            FROM orders 
            ORDER BY order_date DESC 
            LIMIT 5
        ''').fetchall()
        
        for order in recent:
            print(f'Order {order["order_id"]}: {order["customer_name"]} - Status: {order["status"]} - Date: {order["order_date"]}')
        
        print('\n=== CHECKING DELIVERY CHALLANS ===')
        dcs = cursor.execute('''
            SELECT dc_number, order_id, status, created_date 
            FROM delivery_challans 
            ORDER BY created_date DESC 
            LIMIT 5
        ''').fetchall()
        
        if dcs:
            print('Recent DCs:')
            for dc in dcs:
                print(f'DC {dc["dc_number"]}: Order {dc["order_id"]} - Status: {dc["status"]} - Date: {dc["created_date"]}')
        else:
            print('No delivery challans found')
        
        db.close()
        
    except Exception as e:
        print(f"Error checking database: {e}")

if __name__ == '__main__':
    check_orders()
