import sqlite3

conn = sqlite3.connect('instance/medivent.db')
cursor = conn.cursor()

print('=== ORDERS TABLE SCHEMA ===')
cursor.execute('PRAGMA table_info(orders)')
schema = cursor.fetchall()
for col in schema:
    print(f'{col[1]:<20} {col[2]:<15} NULL:{not col[3]} DEFAULT:{col[4]}')

print('\n=== SAMPLE ORDER DATA ===')
cursor.execute('SELECT * FROM orders LIMIT 1')
sample = cursor.fetchone()
if sample:
    print('Sample row:')
    for i, col in enumerate(schema):
        print(f'  {col[1]}: {sample[i]}')

conn.close()
