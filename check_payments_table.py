#!/usr/bin/env python3
"""
Check if payments table exists and what tables are available
"""

def check_payments_table():
    """Check payments table status"""
    print("🔍 CHECKING PAYMENTS TABLE STATUS")
    print("=" * 60)
    
    try:
        import sqlite3
        db = sqlite3.connect('instance/medivent.db')
        db.row_factory = sqlite3.Row
        
        # Get all tables
        tables = db.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name").fetchall()
        
        print(f"📊 Total tables in database: {len(tables)}")
        
        # Look for payment-related tables
        payment_tables = []
        all_tables = []
        
        for table in tables:
            table_name = table[0]
            all_tables.append(table_name)
            if 'payment' in table_name.lower():
                payment_tables.append(table_name)
        
        print(f"\n💰 PAYMENT-RELATED TABLES ({len(payment_tables)}):")
        if payment_tables:
            for table in payment_tables:
                print(f"   ✅ {table}")
                
                # Get table schema
                schema = db.execute(f"PRAGMA table_info({table})").fetchall()
                print(f"      Columns: {', '.join([col[1] for col in schema])}")
        else:
            print("   ❌ No payment-related tables found")
        
        # Check specifically for 'payments' table
        print(f"\n🎯 CHECKING FOR 'payments' TABLE:")
        if 'payments' in all_tables:
            print("   ✅ 'payments' table exists")
            
            # Get schema
            schema = db.execute("PRAGMA table_info(payments)").fetchall()
            print("   📋 Schema:")
            for col in schema:
                print(f"      • {col[1]} ({col[2]})")
                
            # Get row count
            count = db.execute("SELECT COUNT(*) as count FROM payments").fetchone()[0]
            print(f"   📊 Row count: {count}")
            
        else:
            print("   ❌ 'payments' table does NOT exist")
        
        # Show all tables for reference
        print(f"\n📋 ALL TABLES IN DATABASE:")
        for i, table in enumerate(all_tables, 1):
            print(f"   {i:2d}. {table}")
        
        db.close()
        return 'payments' in all_tables
        
    except Exception as e:
        print(f"❌ Error checking payments table: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    check_payments_table()
