#!/usr/bin/env python3
import sqlite3

db = sqlite3.connect('instance/medivent.db')
db.row_factory = sqlite3.Row

# Check product P003 and its division
product = db.execute('''
    SELECT p.*, d.name as division_name, d.status as division_status 
    FROM products p 
    LEFT JOIN divisions d ON p.division_id = d.division_id 
    WHERE p.product_id = "P003"
''').fetchone()

if product:
    print(f'Product P003: {product["name"]}')
    print(f'Division ID: {product["division_id"]}')
    print(f'Division Name: {product["division_name"]}')
    print(f'Division Status: {product["division_status"]}')
else:
    print('Product P003 not found')

db.close()
