#!/usr/bin/env python3
"""
Check product database schema and status distribution
"""

import sqlite3

def check_product_schema():
    """Check products table schema and status distribution"""
    try:
        db = sqlite3.connect('instance/medivent.db')
        db.row_factory = sqlite3.Row
        cursor = db.cursor()

        print("🔍 PRODUCTS TABLE SCHEMA ANALYSIS")
        print("=" * 60)
        
        # Check products table schema
        cursor.execute('PRAGMA table_info(products)')
        columns = cursor.fetchall()
        print('\n📋 PRODUCTS TABLE COLUMNS:')
        for col in columns:
            print(f'  • {col[1]} ({col[2]}) - Default: {col[4]} - NotNull: {col[3]}')

        # Check if status column exists and sample data
        try:
            cursor.execute('SELECT status, COUNT(*) FROM products GROUP BY status')
            status_counts = cursor.fetchall()
            print('\n📊 PRODUCT STATUS DISTRIBUTION:')
            for row in status_counts:
                print(f'  • {row[0]}: {row[1]} products')
        except Exception as e:
            print(f'\n❌ Status column check failed: {e}')
            
            # Check for alternative status columns
            print('\n🔍 Checking for alternative status columns...')
            cursor.execute('SELECT * FROM products LIMIT 1')
            sample_row = cursor.fetchone()
            if sample_row:
                print('Available columns:')
                for key in sample_row.keys():
                    if 'status' in key.lower() or 'active' in key.lower():
                        print(f'  • {key}')

        # Check sample products
        print('\n📝 SAMPLE PRODUCTS:')
        cursor.execute('SELECT product_id, name, status FROM products LIMIT 5')
        samples = cursor.fetchall()
        for row in samples:
            print(f'  • {row[0]}: {row[1]} - Status: {row[2] if len(row) > 2 else "N/A"}')

        db.close()
        print('\n✅ Schema analysis complete!')
        
    except Exception as e:
        print(f'❌ Error: {e}')

if __name__ == '__main__':
    check_product_schema()
