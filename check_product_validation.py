#!/usr/bin/env python3
"""
Check product validation issues
"""

import sqlite3

def check_products_and_divisions():
    """Check products and their divisions"""
    print("🔍 CHECKING PRODUCTS AND DIVISIONS")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        # Check if P001 exists
        print("1. Checking product P001...")
        cursor.execute('SELECT * FROM products WHERE product_id = ?', ('P001',))
        product = cursor.fetchone()
        
        if product:
            print("✅ Product P001 exists")
            print(f"   Product details: {dict(zip([col[0] for col in cursor.description], product))}")
        else:
            print("❌ Product P001 does not exist")
            
            # Get any available products
            cursor.execute('SELECT product_id, name, division FROM products LIMIT 5')
            products = cursor.fetchall()
            print(f"Available products: {products}")
        
        # Check divisions table
        print("\n2. Checking divisions...")
        cursor.execute('SELECT * FROM divisions')
        divisions = cursor.fetchall()
        print(f"Available divisions: {divisions}")
        
        # Check if divisions are active
        cursor.execute('SELECT * FROM divisions WHERE status = ?', ('active',))
        active_divisions = cursor.fetchall()
        print(f"Active divisions: {active_divisions}")
        
        conn.close()
        return product is not None
        
    except Exception as e:
        print(f"❌ Error checking products: {e}")
        return False

def test_product_validator():
    """Test the product validator directly"""
    print("\n🧪 TESTING PRODUCT VALIDATOR")
    print("=" * 60)
    
    try:
        from app import app
        with app.app_context():
            from database import get_db
            from utils.product_validator import get_product_validator
            
            db = get_db()
            product_validator = get_product_validator(db)
            
            # Test P001 validation
            print("Testing P001 validation...")
            is_valid, product_info = product_validator.validate_product_exists('P001')
            print(f"Is valid: {is_valid}")
            print(f"Product info: {product_info}")
            
            if not is_valid:
                print("❌ P001 validation failed")
                
                # Get valid products for order placement
                print("Getting valid products for order placement...")
                valid_products = product_validator.get_products_for_order_placement()
                print(f"Valid products: {valid_products[:3] if valid_products else 'None'}")
                
                return False
            else:
                print("✅ P001 validation passed")
                return True
                
    except Exception as e:
        print(f"❌ Product validator error: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_test_product():
    """Create a test product if needed"""
    print("\n🛠️  CREATING TEST PRODUCT")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        # Check if we need to create a division first
        cursor.execute('SELECT * FROM divisions WHERE status = ?', ('active',))
        active_division = cursor.fetchone()
        
        if not active_division:
            print("Creating test division...")
            cursor.execute('''
                INSERT OR IGNORE INTO divisions (name, status, description)
                VALUES (?, ?, ?)
            ''', ('Test Division', 'active', 'Test division for order creation'))
            conn.commit()
            
            cursor.execute('SELECT * FROM divisions WHERE name = ?', ('Test Division',))
            active_division = cursor.fetchone()
        
        division_name = active_division[1] if active_division else 'Test Division'
        print(f"Using division: {division_name}")
        
        # Create test product
        cursor.execute('SELECT * FROM products WHERE product_id = ?', ('TEST001',))
        if not cursor.fetchone():
            print("Creating test product TEST001...")
            cursor.execute('''
                INSERT INTO products (
                    product_id, name, division, unit_price, status, 
                    description, category, manufacturer
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                'TEST001', 
                'Test Product for Order Creation',
                division_name,
                100.0,
                'active',
                'Test product for debugging order creation',
                'Test Category',
                'Test Manufacturer'
            ))
            conn.commit()
            print("✅ Test product TEST001 created")
        else:
            print("✅ Test product TEST001 already exists")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error creating test product: {e}")
        return False

def test_order_with_valid_product():
    """Test order creation with a valid product"""
    print("\n🧪 TESTING ORDER WITH VALID PRODUCT")
    print("=" * 60)
    
    try:
        import requests
        import time
        
        base_url = "http://localhost:5001"
        session = requests.Session()
        
        # Login
        login_data = {'username': 'admin', 'password': 'admin123'}
        response = session.post(f"{base_url}/login", data=login_data, allow_redirects=False)
        
        if response.status_code != 302:
            print("❌ Login failed")
            return False
        
        print("✅ Login successful")
        
        # Get current order count
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM orders')
        initial_count = cursor.fetchone()[0]
        conn.close()
        
        # Submit order with valid product
        order_data = {
            'customer_name': 'Valid Product Test Customer',
            'customer_address': 'Valid Product Test Address',
            'customer_phone': '555-VALID-TEST',
            'payment_method': 'cash',
            'po_number': 'VALID-TEST-001',
            'product_id[]': ['TEST001'],
            'quantity[]': ['1'],
            'foc_quantity[]': ['0']
        }
        
        response = session.post(f"{base_url}/orders/new", 
                               data=order_data, 
                               timeout=15,
                               allow_redirects=False)
        
        print(f"Order submission status: {response.status_code}")
        
        if response.status_code == 302:
            redirect_url = response.headers.get('Location', '')
            print(f"Redirected to: {redirect_url}")
            
            # Check if order was created
            time.sleep(1)
            conn = sqlite3.connect('instance/medivent.db')
            cursor = conn.cursor()
            cursor.execute('SELECT COUNT(*) FROM orders')
            final_count = cursor.fetchone()[0]
            conn.close()
            
            if final_count > initial_count:
                print("✅ Order created successfully!")
                return True
            else:
                print("❌ Order not created despite redirect")
                return False
        else:
            print("❌ Order submission failed")
            return False
            
    except Exception as e:
        print(f"❌ Error testing order: {e}")
        return False

def main():
    """Run all validation checks"""
    print("🔍 PRODUCT VALIDATION INVESTIGATION")
    print("=" * 80)
    
    # Check 1: Products and divisions
    products_ok = check_products_and_divisions()
    
    # Check 2: Product validator
    validator_ok = test_product_validator()
    
    # Check 3: Create test product if needed
    if not products_ok or not validator_ok:
        print("\n⚠️  Issues detected, creating test product...")
        create_test_product()
        
        # Retest validator
        validator_ok = test_product_validator()
    
    # Check 4: Test order with valid product
    if validator_ok:
        order_ok = test_order_with_valid_product()
    else:
        order_ok = False
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 VALIDATION RESULTS")
    print("=" * 80)
    print(f"Products/Divisions Check: {'✅ PASS' if products_ok else '❌ FAIL'}")
    print(f"Product Validator Test: {'✅ PASS' if validator_ok else '❌ FAIL'}")
    print(f"Order Creation Test: {'✅ PASS' if order_ok else '❌ FAIL'}")
    
    if order_ok:
        print("\n🎉 SUCCESS!")
        print("✅ Product validation is working")
        print("✅ Order creation is functional")
        print("✅ Web interface discrepancy resolved!")
    else:
        print("\n❌ ISSUES REMAIN")
        print("💡 Product validation is preventing order creation")
        print("   Possible solutions:")
        print("   1. Use a valid product ID that exists in database")
        print("   2. Ensure product has an active division")
        print("   3. Check product validator logic")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
