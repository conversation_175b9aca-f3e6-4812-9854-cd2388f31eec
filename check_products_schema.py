#!/usr/bin/env python3
"""
Check Products Table Schema and ASP Fields
"""

import sqlite3
import os

def check_products_schema():
    """Check the products table schema and ASP-related fields"""
    
    db_path = 'instance/medivent.db'
    
    if not os.path.exists(db_path):
        print(f"❌ Database not found at: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check products table structure
        print("🔍 PRODUCTS TABLE SCHEMA:")
        print("=" * 50)
        cursor.execute("PRAGMA table_info(products)")
        columns = cursor.fetchall()
        
        for col in columns:
            print(f"  {col[1]} ({col[2]}) - {col[3]} - Default: {col[4]}")
        
        # Check if ASP-related columns exist
        column_names = [col[1] for col in columns]
        asp_fields = ['asp', 'mrp', 'tp_rate', 'unit_price']
        
        print("\n🔍 ASP-RELATED FIELDS CHECK:")
        print("=" * 50)
        for field in asp_fields:
            if field in column_names:
                print(f"  ✅ {field} - EXISTS")
            else:
                print(f"  ❌ {field} - MISSING")
        
        # Check sample data
        print("\n🔍 SAMPLE PRODUCTS DATA:")
        print("=" * 50)
        cursor.execute("SELECT * FROM products LIMIT 3")
        products = cursor.fetchall()
        
        if products:
            for product in products:
                print(f"  Product ID: {product[1] if len(product) > 1 else 'N/A'}")
                print(f"  Name: {product[2] if len(product) > 2 else 'N/A'}")
                print(f"  Unit Price: {product[6] if len(product) > 6 else 'N/A'}")
                print("  ---")
        else:
            print("  No products found in database")
        
        # Check order_items table for pricing
        print("\n🔍 ORDER_ITEMS PRICING FIELDS:")
        print("=" * 50)
        cursor.execute("PRAGMA table_info(order_items)")
        order_items_columns = cursor.fetchall()
        
        for col in order_items_columns:
            if 'price' in col[1].lower() or 'total' in col[1].lower():
                print(f"  {col[1]} ({col[2]}) - Default: {col[4]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error checking database: {e}")
        return False

if __name__ == "__main__":
    check_products_schema()
