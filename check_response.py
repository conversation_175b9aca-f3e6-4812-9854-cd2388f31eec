import requests

try:
    response = requests.get('http://127.0.0.1:5001/orders/ORD175397491316416F32/history', timeout=10)
    
    # Save the response to a file for inspection
    with open('response.html', 'w', encoding='utf-8') as f:
        f.write(response.text)
    
    print(f'Status: {response.status_code}')
    print(f'Content saved to response.html ({len(response.text)} chars)')
    
    # Check for specific patterns
    patterns = [
        'Order Details',
        'Axinix',
        'Rejected',
        'timeline',
        'Order Items',
        'Activity Log'
    ]
    
    for pattern in patterns:
        if pattern in response.text:
            print(f'✅ Found: {pattern}')
        else:
            print(f'❌ Missing: {pattern}')
    
    # Check if there are any error messages
    if 'error' in response.text.lower():
        print('⚠️ Error messages found in response')
    
except Exception as e:
    print(f'Error: {e}')
