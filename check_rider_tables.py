#!/usr/bin/env python3
"""
Check rider-related tables in the database
"""

import sqlite3
import sys
import os

def check_rider_tables():
    """Check all rider-related tables and their structure"""
    try:
        # Connect to database
        db_path = 'instance/medivent.db'
        if not os.path.exists(db_path):
            print(f"❌ Database not found at {db_path}")
            return False
            
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 CHECKING RIDER-RELATED TABLES")
        print("=" * 50)
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        all_tables = [table[0] for table in cursor.fetchall()]
        
        # Find rider-related tables
        rider_tables = [table for table in all_tables if 'rider' in table.lower()]
        
        print(f"📊 Found {len(rider_tables)} rider-related tables:")
        for table in rider_tables:
            print(f"  ✅ {table}")
        
        print("\n🔍 DETAILED TABLE STRUCTURES:")
        print("=" * 50)
        
        # Check each rider table structure
        for table in rider_tables:
            print(f"\n📋 TABLE: {table}")
            print("-" * 30)
            
            cursor.execute(f"PRAGMA table_info({table})")
            columns = cursor.fetchall()
            
            for col in columns:
                col_id, name, data_type, not_null, default_val, pk = col
                pk_marker = " (PK)" if pk else ""
                null_marker = " NOT NULL" if not_null else ""
                default_marker = f" DEFAULT {default_val}" if default_val else ""
                print(f"  {name}: {data_type}{pk_marker}{null_marker}{default_marker}")
            
            # Get row count
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"  📊 Rows: {count}")
        
        # Check orders table for rider integration
        print(f"\n📋 TABLE: orders (rider integration)")
        print("-" * 30)
        cursor.execute("PRAGMA table_info(orders)")
        columns = cursor.fetchall()
        
        rider_columns = [col for col in columns if 'rider' in col[1].lower() or col[1] in ['warehouse_status', 'packed_at', 'packed_by']]
        for col in rider_columns:
            col_id, name, data_type, not_null, default_val, pk = col
            pk_marker = " (PK)" if pk else ""
            null_marker = " NOT NULL" if not_null else ""
            default_marker = f" DEFAULT {default_val}" if default_val else ""
            print(f"  {name}: {data_type}{pk_marker}{null_marker}{default_marker}")
        
        conn.close()
        print(f"\n✅ Database check completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error checking database: {e}")
        return False

if __name__ == "__main__":
    check_rider_tables()
