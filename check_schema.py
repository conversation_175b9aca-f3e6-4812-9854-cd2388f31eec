#!/usr/bin/env python3
"""
Check database schema for order_items table
"""

import sqlite3

def check_order_items_schema():
    """Check the order_items table schema"""
    print("📋 ORDER ITEMS TABLE SCHEMA")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        # Get table schema
        cursor.execute('PRAGMA table_info(order_items)')
        columns = cursor.fetchall()
        
        print("Columns:")
        for col in columns:
            col_name = col[1]
            col_type = col[2]
            not_null = "NOT NULL" if col[3] else ""
            primary_key = "PRIMARY KEY" if col[5] else ""
            print(f"  {col_name:20} {col_type:15} {not_null:10} {primary_key}")
        
        # Check if order_item_id is auto-increment
        cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='order_items'")
        create_sql = cursor.fetchone()
        if create_sql:
            print(f"\nTable creation SQL:")
            print(create_sql[0])
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error checking schema: {e}")
        return False

def fix_order_creation():
    """Create a fixed order creation function"""
    print("\n🔧 TESTING FIXED ORDER CREATION")
    print("=" * 50)
    
    try:
        from app import app
        with app.app_context():
            from database import get_db
            from routes.orders import generate_order_id
            from datetime import datetime
            import sqlite3
            
            db = get_db()
            
            # Generate order ID
            order_id = generate_order_id()
            print(f"Generated order ID: {order_id}")
            
            # Begin transaction
            db.execute('BEGIN IMMEDIATE TRANSACTION')
            
            # Insert order
            db.execute('''
                INSERT INTO orders (
                    order_id, customer_name, customer_address, customer_phone,
                    payment_method, status, sales_agent, updated_by, order_date, last_updated
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                order_id, 'Fixed Test Customer', 'Fixed Test Address', '555-FIXED-TEST',
                'cash', 'Placed', 'admin', 'admin', datetime.now(), datetime.now()
            ))
            
            # Insert order item with proper fields
            # Check if order_item_id is auto-increment or needs to be provided
            try:
                # Try without order_item_id (auto-increment)
                db.execute('''
                    INSERT INTO order_items (
                        order_id, product_id, quantity, unit_price, total_price, foc_quantity
                    ) VALUES (?, ?, ?, ?, ?, ?)
                ''', (order_id, 'P001', 1, 25.5, 25.5, 0))
                print("✅ Order item inserted without order_item_id")
            except sqlite3.IntegrityError as e:
                if 'order_item_id' in str(e):
                    print("⚠️  order_item_id required, generating one...")
                    # Generate order_item_id
                    import uuid
                    order_item_id = str(uuid.uuid4())[:8].upper()
                    
                    db.execute('''
                        INSERT INTO order_items (
                            order_item_id, order_id, product_id, quantity, unit_price, total_price, foc_quantity
                        ) VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (order_item_id, order_id, 'P001', 1, 25.5, 25.5, 0))
                    print(f"✅ Order item inserted with order_item_id: {order_item_id}")
                else:
                    raise e
            
            # Update order total
            db.execute('UPDATE orders SET order_amount = ? WHERE order_id = ?', (25.5, order_id))
            
            # Commit
            db.execute('COMMIT')
            
            print(f"✅ Order {order_id} created successfully!")
            return True
            
    except Exception as e:
        print(f"❌ Fixed order creation failed: {e}")
        import traceback
        traceback.print_exc()
        try:
            db.execute('ROLLBACK')
        except:
            pass
        return False

def main():
    """Check schema and test fixes"""
    print("🔍 DATABASE SCHEMA INVESTIGATION")
    print("=" * 80)
    
    # Check schema
    schema_ok = check_order_items_schema()
    
    # Test fixed order creation
    if schema_ok:
        creation_ok = fix_order_creation()
    else:
        creation_ok = False
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 SCHEMA INVESTIGATION RESULTS")
    print("=" * 80)
    print(f"Schema Check: {'✅ SUCCESS' if schema_ok else '❌ FAILED'}")
    print(f"Fixed Order Creation: {'✅ SUCCESS' if creation_ok else '❌ FAILED'}")
    
    if creation_ok:
        print("\n🎉 DATABASE ISSUE RESOLVED!")
        print("✅ Order creation now works with proper schema")
        print("💡 The issue was missing order_item_id in INSERT statement")
    else:
        print("\n❌ DATABASE ISSUES REMAIN")
        print("💡 Need to investigate schema further")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
