#!/usr/bin/env python3
"""
Check if the order sequence table is in sync with existing orders
"""

import sqlite3

def check_sequence_sync():
    """Check if sequence table is synchronized with orders"""
    print("🔍 CHECKING ORDER SEQUENCE SYNCHRONIZATION")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        # Get sequence table status
        print("1. Order sequence table status:")
        cursor.execute('SELECT COUNT(*) FROM order_sequence')
        seq_count = cursor.fetchone()[0]
        print(f"   Sequence entries: {seq_count}")
        
        cursor.execute('SELECT MAX(id) FROM order_sequence')
        max_seq_id = cursor.fetchone()[0]
        print(f"   Max sequence ID: {max_seq_id}")
        
        # Get highest order number from orders table
        print("\n2. Orders table analysis:")
        cursor.execute('SELECT COUNT(*) FROM orders')
        order_count = cursor.fetchone()[0]
        print(f"   Total orders: {order_count}")
        
        cursor.execute("SELECT order_id FROM orders WHERE order_id LIKE 'ORD%' ORDER BY order_id DESC LIMIT 1")
        latest_order = cursor.fetchone()
        if latest_order:
            latest_order_id = latest_order[0]
            try:
                latest_order_num = int(latest_order_id[3:])
                print(f"   Latest order: {latest_order_id} (number: {latest_order_num})")
            except:
                print(f"   Latest order: {latest_order_id} (invalid format)")
                latest_order_num = 0
        else:
            print("   No ORD orders found")
            latest_order_num = 0
        
        # Check for synchronization issues
        print("\n3. Synchronization check:")
        if max_seq_id and latest_order_num:
            if max_seq_id >= latest_order_num:
                print(f"   ✅ Sequence is ahead: {max_seq_id} >= {latest_order_num}")
                sync_ok = True
            else:
                print(f"   ❌ Sequence is behind: {max_seq_id} < {latest_order_num}")
                sync_ok = False
        else:
            print("   ⚠️  Cannot determine sync status")
            sync_ok = False
        
        # Test order ID generation
        print("\n4. Testing order ID generation:")
        from app import app
        with app.app_context():
            from routes.orders import generate_order_id
            
            # Generate 3 test IDs
            for i in range(3):
                order_id = generate_order_id()
                print(f"   Generated: {order_id}")
        
        conn.close()
        return sync_ok
        
    except Exception as e:
        print(f"❌ Error checking sync: {e}")
        import traceback
        traceback.print_exc()
        return False

def fix_sequence_sync():
    """Fix sequence synchronization if needed"""
    print("\n🔧 FIXING SEQUENCE SYNCHRONIZATION")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        # Get the highest order number
        cursor.execute("SELECT order_id FROM orders WHERE order_id LIKE 'ORD%' ORDER BY order_id DESC LIMIT 1")
        latest_order = cursor.fetchone()
        
        if latest_order:
            latest_order_id = latest_order[0]
            latest_order_num = int(latest_order_id[3:])
            print(f"Latest order number: {latest_order_num}")
            
            # Clear sequence table and rebuild
            print("Rebuilding sequence table...")
            cursor.execute('DELETE FROM order_sequence')
            
            # Insert entries up to the latest order number + buffer
            target_count = latest_order_num + 10  # Add buffer
            for i in range(1, target_count + 1):
                cursor.execute('INSERT INTO order_sequence (id) VALUES (?)', (i,))
            
            conn.commit()
            print(f"✅ Rebuilt sequence table with {target_count} entries")
            
            # Verify
            cursor.execute('SELECT MAX(id) FROM order_sequence')
            new_max = cursor.fetchone()[0]
            print(f"✅ New max sequence ID: {new_max}")
            
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error fixing sync: {e}")
        return False

def main():
    """Check and fix sequence synchronization"""
    print("🔄 ORDER SEQUENCE SYNCHRONIZATION CHECK")
    print("=" * 80)
    
    # Check current sync status
    sync_ok = check_sequence_sync()
    
    # Fix if needed
    if not sync_ok:
        print("\n⚠️  Synchronization issues detected, attempting fix...")
        fix_success = fix_sequence_sync()
        
        if fix_success:
            print("\n✅ Re-checking after fix...")
            sync_ok = check_sequence_sync()
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 SYNCHRONIZATION RESULTS")
    print("=" * 80)
    print(f"Sequence Synchronization: {'✅ OK' if sync_ok else '❌ ISSUES'}")
    
    if sync_ok:
        print("\n🎉 SEQUENCE SYNCHRONIZED!")
        print("✅ Order ID generation should work correctly")
        print("✅ UNIQUE constraint errors should be resolved")
    else:
        print("\n❌ SYNCHRONIZATION ISSUES REMAIN")
        print("💡 Manual intervention may be required")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
