import sqlite3

conn = sqlite3.connect('database.db')
cursor = conn.cursor()

print("=== DATABASE TABLES ===")

# Get all tables
cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
tables = cursor.fetchall()

print(f"Found {len(tables)} tables:")
for table in tables:
    print(f"  - {table[0]}")

print("\n=== CHECKING FOR ORDER-RELATED TABLES ===")

# Check for any table that might contain order items
for table in tables:
    table_name = table[0]
    if 'order' in table_name.lower() or 'item' in table_name.lower():
        print(f"\nTable: {table_name}")
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
        
        # Check row count
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]
        print(f"  Rows: {count}")

conn.close()
