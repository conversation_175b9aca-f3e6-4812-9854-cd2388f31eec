#!/usr/bin/env python3
"""
Template Syntax Checker
Checks for common Jinja2 template syntax errors
"""

import os
import re

def check_template_syntax(file_path):
    """Check a single template file for syntax errors"""
    errors = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
        
        # Track open/close tags
        if_stack = []
        for_stack = []
        
        for line_num, line in enumerate(lines, 1):
            # Check for if statements
            if_matches = re.findall(r'{%\s*if\s+', line)
            elif_matches = re.findall(r'{%\s*elif\s+', line)
            else_matches = re.findall(r'{%\s*else\s*%}', line)
            endif_matches = re.findall(r'{%\s*endif\s*%}', line)
            
            # Check for for loops
            for_matches = re.findall(r'{%\s*for\s+', line)
            endfor_matches = re.findall(r'{%\s*endfor\s*%}', line)
            
            # Track if statements
            for match in if_matches:
                if_stack.append(line_num)
            
            for match in endif_matches:
                if if_stack:
                    if_stack.pop()
                else:
                    errors.append(f"Line {line_num}: Unexpected {{% endif %}} without matching {{% if %}}")
            
            # Track for loops
            for match in for_matches:
                for_stack.append(line_num)
            
            for match in endfor_matches:
                if for_stack:
                    for_stack.pop()
                else:
                    errors.append(f"Line {line_num}: Unexpected {{% endfor %}} without matching {{% for %}}")
            
            # Check for unclosed HTML tags in table structures
            if '<td>' in line and '</td>' not in line:
                # Check if the td is closed on subsequent lines
                td_closed = False
                for check_line in lines[line_num:line_num+5]:  # Check next 5 lines
                    if '</td>' in check_line:
                        td_closed = True
                        break
                if not td_closed:
                    errors.append(f"Line {line_num}: Unclosed <td> tag")
            
            # Check for unclosed tr tags
            if '<tr>' in line and '</tr>' not in line:
                tr_closed = False
                for check_line in lines[line_num:line_num+20]:  # Check next 20 lines
                    if '</tr>' in check_line:
                        tr_closed = True
                        break
                if not tr_closed:
                    errors.append(f"Line {line_num}: Unclosed <tr> tag")
        
        # Check for unclosed if statements
        for line_num in if_stack:
            errors.append(f"Line {line_num}: Unclosed {{% if %}} statement")
        
        # Check for unclosed for loops
        for line_num in for_stack:
            errors.append(f"Line {line_num}: Unclosed {{% for %}} loop")
        
        return errors
        
    except Exception as e:
        return [f"Error reading file: {str(e)}"]

def main():
    """Check all product templates for syntax errors"""
    print("🔍 Checking Template Syntax Errors...")
    print("=" * 50)
    
    # Check product templates
    product_templates = [
        'templates/products/view_all_products.html',
        'templates/products/product_management.html',
        'templates/products/add_product_enhanced.html',
        'templates/products/new.html',
        'templates/products/update.html',
        'templates/products/index.html',
        'templates/products/view.html'
    ]
    
    total_errors = 0
    
    for template in product_templates:
        if os.path.exists(template):
            errors = check_template_syntax(template)
            if errors:
                print(f"\n❌ {template}:")
                for error in errors:
                    print(f"   {error}")
                total_errors += len(errors)
            else:
                print(f"✅ {template}: No syntax errors found")
        else:
            print(f"⚠️ {template}: File not found")
    
    print(f"\n📊 Summary: {total_errors} total syntax errors found")
    
    if total_errors == 0:
        print("🎉 All templates have correct syntax!")
    else:
        print("🔧 Please fix the syntax errors above")
    
    return total_errors == 0

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
