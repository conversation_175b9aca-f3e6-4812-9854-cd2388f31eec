import sqlite3

order_id = 'ORD1753983391CA9E99E1'

conn = sqlite3.connect('instance/medivent.db')
conn.row_factory = sqlite3.Row
cursor = conn.cursor()

print("🔍 COMPARING QUERIES FOR ORDER ITEMS")
print("=" * 50)

# Query 1: From view_history (current)
print("1️⃣ Query from view_history function:")
items1 = cursor.execute('''
    SELECT * FROM order_items
    WHERE order_id = ?
    ORDER BY product_name
''', (order_id,)).fetchall()

print(f"   Found {len(items1)} items")
if items1:
    item = items1[0]
    print(f"   Product: {item['product_name']}")
    print(f"   Strength: {item['strength']}")
    print(f"   Quantity: {item['quantity']}")
    print(f"   Keys: {list(item.keys())}")

# Query 2: From view_order (working)
print("\n2️⃣ Query from view_order function:")
items2 = cursor.execute('''
    SELECT oi.*, p.name as product_name
    FROM order_items oi
    LEFT JOIN products p ON oi.product_id = p.product_id
    WHERE oi.order_id = ?
''', (order_id,)).fetchall()

print(f"   Found {len(items2)} items")
if items2:
    item = items2[0]
    print(f"   Product: {item['product_name']}")
    print(f"   Strength: {item['strength']}")
    print(f"   Quantity: {item['quantity']}")
    print(f"   Keys: {list(item.keys())}")

# Query 3: Simple test
print("\n3️⃣ Simple test query:")
items3 = cursor.execute('SELECT product_name, strength, quantity FROM order_items WHERE order_id = ?', (order_id,)).fetchall()

print(f"   Found {len(items3)} items")
if items3:
    item = items3[0]
    print(f"   Product: {item['product_name']}")
    print(f"   Strength: {item['strength']}")
    print(f"   Quantity: {item['quantity']}")

conn.close()
