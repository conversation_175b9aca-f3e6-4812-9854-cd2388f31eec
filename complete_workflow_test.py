#!/usr/bin/env python3
"""
Complete Order Workflow Test
Tests the entire process from order creation to dispatch rider assignment
"""

import requests
import time
import json
import sqlite3
from datetime import datetime

class OrderWorkflowTester:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.created_orders = []
        
    def wait_for_server(self):
        """Wait for Flask server to be ready"""
        print("🔄 Waiting for Flask server to start...")
        for i in range(10):
            try:
                response = self.session.get(self.base_url, timeout=5)
                if response.status_code == 200:
                    print("✅ Flask server is ready!")
                    return True
            except:
                time.sleep(2)
        print("❌ Flask server not responding")
        return False
    
    def create_test_orders(self, count=5):
        """Create test orders"""
        print(f"\n📝 Creating {count} test orders...")
        
        for i in range(1, count + 1):
            order_data = {
                'customer_name': f'Test Customer {i}',
                'customer_address': f'Test Address {i}, City',
                'customer_phone': f'123456789{i}',
                'payment_method': 'cash',
                'po_number': f'PO-TEST-{i:03d}',
                'product_id[]': ['P001', 'P002'],  # Test with multiple products
                'quantity[]': ['2', '1'],
                'foc_quantity[]': ['0', '0']
            }
            
            try:
                response = self.session.post(f"{self.base_url}/orders/new", 
                                           data=order_data, 
                                           timeout=10, 
                                           allow_redirects=False)
                
                if response.status_code in [200, 302]:
                    print(f"  ✅ Order {i} created successfully")
                    # Extract order ID from redirect or response
                    if response.status_code == 302:
                        redirect_url = response.headers.get('Location', '')
                        if '/orders/' in redirect_url:
                            order_id = redirect_url.split('/orders/')[-1].split('/')[0]
                            self.created_orders.append(order_id)
                else:
                    print(f"  ❌ Order {i} failed: {response.status_code}")
                    
            except Exception as e:
                print(f"  ❌ Order {i} error: {e}")
        
        return len(self.created_orders)
    
    def get_recent_orders_from_db(self):
        """Get recent orders from database"""
        try:
            conn = sqlite3.connect('instance/medivent.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT order_id, customer_name, status, order_date 
                FROM orders 
                ORDER BY order_date DESC 
                LIMIT 10
            ''')
            
            orders = cursor.fetchall()
            conn.close()
            
            print(f"\n📋 Recent orders from database:")
            for order_id, customer_name, status, order_date in orders:
                print(f"  {order_id}: {customer_name} - {status} ({order_date})")
                if order_id not in self.created_orders:
                    self.created_orders.append(order_id)
            
            return orders
            
        except Exception as e:
            print(f"❌ Database error: {e}")
            return []
    
    def approve_orders(self):
        """Approve the created orders"""
        print(f"\n✅ Approving orders...")
        
        for order_id in self.created_orders[:5]:  # Approve first 5
            try:
                # Try to approve via API
                approve_data = {
                    'action': 'approve',
                    'notes': f'Auto-approved for testing - {datetime.now()}'
                }
                
                response = self.session.post(f"{self.base_url}/orders/{order_id}/approve", 
                                           data=approve_data, 
                                           timeout=10)
                
                if response.status_code in [200, 302]:
                    print(f"  ✅ Order {order_id} approved")
                else:
                    print(f"  ⚠️  Order {order_id} approval status: {response.status_code}")
                    
            except Exception as e:
                print(f"  ❌ Error approving {order_id}: {e}")
    
    def generate_invoices(self):
        """Generate invoices for approved orders"""
        print(f"\n🧾 Generating invoices...")
        
        for order_id in self.created_orders[:5]:
            try:
                response = self.session.get(f"{self.base_url}/finance/generate-invoice/{order_id}", 
                                          timeout=10)
                
                if response.status_code in [200, 302]:
                    print(f"  ✅ Invoice generated for {order_id}")
                else:
                    print(f"  ⚠️  Invoice generation for {order_id}: {response.status_code}")
                    
            except Exception as e:
                print(f"  ❌ Error generating invoice for {order_id}: {e}")
    
    def generate_delivery_challans(self):
        """Generate delivery challans"""
        print(f"\n📦 Generating delivery challans...")
        
        for order_id in self.created_orders[:5]:
            try:
                dc_data = {
                    'order_id': order_id,
                    'notes': 'Auto-generated DC for testing'
                }
                
                response = self.session.post(f"{self.base_url}/orders/{order_id}/generate-dc", 
                                           data=dc_data, 
                                           timeout=10)
                
                if response.status_code in [200, 302]:
                    print(f"  ✅ DC generated for {order_id}")
                else:
                    print(f"  ⚠️  DC generation for {order_id}: {response.status_code}")
                    
            except Exception as e:
                print(f"  ❌ Error generating DC for {order_id}: {e}")
    
    def assign_riders(self):
        """Assign riders to orders"""
        print(f"\n🚴 Assigning dispatch riders...")
        
        # Get available riders first
        try:
            response = self.session.get(f"{self.base_url}/api/riders/available", timeout=10)
            if response.status_code == 200:
                riders = response.json()
                print(f"  Found {len(riders)} available riders")
            else:
                # Use default test riders
                riders = [
                    {'id': 'R001', 'name': 'Test Rider 1'},
                    {'id': 'R002', 'name': 'Test Rider 2'},
                    {'id': 'R003', 'name': 'Test Rider 3'}
                ]
                print(f"  Using default test riders")
        except:
            riders = [{'id': 'R001', 'name': 'Test Rider 1'}]
        
        for i, order_id in enumerate(self.created_orders[:5]):
            try:
                rider = riders[i % len(riders)]  # Cycle through riders
                
                assign_data = {
                    'rider_id': rider['id'],
                    'rider_name': rider['name'],
                    'vehicle_type': 'motorcycle',
                    'expected_pickup_time': '14:00',
                    'pickup_notes': f'Test pickup for {order_id}'
                }
                
                response = self.session.post(f"{self.base_url}/orders/{order_id}/assign-rider", 
                                           data=assign_data, 
                                           timeout=10)
                
                if response.status_code in [200, 302]:
                    print(f"  ✅ Rider {rider['name']} assigned to {order_id}")
                else:
                    print(f"  ⚠️  Rider assignment for {order_id}: {response.status_code}")
                    
            except Exception as e:
                print(f"  ❌ Error assigning rider to {order_id}: {e}")
    
    def check_final_status(self):
        """Check final status of all orders"""
        print(f"\n📊 Final Order Status Check:")
        
        try:
            conn = sqlite3.connect('instance/medivent.db')
            cursor = conn.cursor()
            
            for order_id in self.created_orders[:5]:
                cursor.execute('''
                    SELECT order_id, customer_name, status, assigned_rider, 
                           invoice_number, dc_status
                    FROM orders 
                    WHERE order_id = ?
                ''', (order_id,))
                
                result = cursor.fetchone()
                if result:
                    order_id, customer_name, status, rider, invoice, dc_status = result
                    print(f"  {order_id}:")
                    print(f"    Customer: {customer_name}")
                    print(f"    Status: {status}")
                    print(f"    Rider: {rider or 'Not assigned'}")
                    print(f"    Invoice: {invoice or 'Not generated'}")
                    print(f"    DC Status: {dc_status or 'Not generated'}")
                    print()
            
            conn.close()
            
        except Exception as e:
            print(f"❌ Error checking final status: {e}")
    
    def run_complete_test(self):
        """Run the complete workflow test"""
        print("=" * 60)
        print("🧪 COMPLETE ORDER WORKFLOW TEST")
        print("=" * 60)
        
        # Step 1: Wait for server
        if not self.wait_for_server():
            return False
        
        # Step 2: Create orders
        created_count = self.create_test_orders(5)
        if created_count == 0:
            # Try to get recent orders from database
            self.get_recent_orders_from_db()
        
        if not self.created_orders:
            print("❌ No orders available for testing")
            return False
        
        print(f"📋 Working with orders: {self.created_orders[:5]}")
        
        # Step 3: Approve orders
        self.approve_orders()
        
        # Step 4: Generate invoices
        self.generate_invoices()
        
        # Step 5: Generate delivery challans
        self.generate_delivery_challans()
        
        # Step 6: Assign riders
        self.assign_riders()
        
        # Step 7: Check final status
        self.check_final_status()
        
        print("=" * 60)
        print("🎉 WORKFLOW TEST COMPLETED!")
        print("=" * 60)
        
        return True

if __name__ == "__main__":
    tester = OrderWorkflowTester()
    tester.run_complete_test()
