#!/usr/bin/env python3
"""
Comprehensive database schema analysis to identify all column issues
"""

import sqlite3
import os

def analyze_database_schema():
    """Comprehensive analysis of database schema"""
    
    print("🔍 COMPREHENSIVE DATABASE SCHEMA ANALYSIS")
    print("=" * 80)
    
    db_path = 'instance/medivent.db'
    if not os.path.exists(db_path):
        print("❌ Database file not found!")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        tables = [row[0] for row in cursor.fetchall()]
        
        print(f"📋 Found {len(tables)} tables in database")
        print("-" * 80)
        
        # Analyze each table
        for table_name in tables:
            print(f"\n📊 TABLE: {table_name}")
            print("-" * 40)
            
            # Get table schema
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            print("Columns:")
            for col in columns:
                print(f"  • {col[1]} ({col[2]}) {'PRIMARY KEY' if col[5] else ''} {'NOT NULL' if col[3] else ''}")
            
            # Get row count
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            row_count = cursor.fetchone()[0]
            print(f"Row count: {row_count}")
        
        # Specific analysis for problematic tables
        print("\n" + "=" * 80)
        print("🎯 SPECIFIC PROBLEM ANALYSIS")
        print("=" * 80)
        
        # 1. Divisions table analysis
        print("\n1️⃣ DIVISIONS TABLE ANALYSIS:")
        cursor.execute("PRAGMA table_info(divisions)")
        div_columns = [col[1] for col in cursor.fetchall()]
        print(f"   Columns: {div_columns}")
        
        if 'manager' in div_columns:
            print("   ✅ 'manager' column EXISTS")
        else:
            print("   ❌ 'manager' column MISSING")
            
        if 'manager_id' in div_columns:
            print("   ✅ 'manager_id' column EXISTS")
        else:
            print("   ❌ 'manager_id' column MISSING")
        
        # 2. Stock movements table analysis
        print("\n2️⃣ STOCK_MOVEMENTS TABLE ANALYSIS:")
        try:
            cursor.execute("PRAGMA table_info(stock_movements)")
            stock_columns = [col[1] for col in cursor.fetchall()]
            print(f"   Columns: {stock_columns}")
            
            if 'product_id' in stock_columns:
                print("   ✅ 'product_id' column EXISTS")
            else:
                print("   ❌ 'product_id' column MISSING")
                
        except Exception as e:
            print(f"   ❌ Error accessing stock_movements table: {e}")
        
        # 3. Products table ASP analysis
        print("\n3️⃣ PRODUCTS TABLE ASP ANALYSIS:")
        cursor.execute("PRAGMA table_info(products)")
        prod_columns = [col[1] for col in cursor.fetchall()]
        print(f"   Columns: {prod_columns}")
        
        asp_fields = ['asp', 'mrp', 'tp_rate']
        for field in asp_fields:
            if field in prod_columns:
                print(f"   ✅ '{field}' column EXISTS")
            else:
                print(f"   ❌ '{field}' column MISSING")
        
        # Check ASP data
        cursor.execute("SELECT COUNT(*) FROM products WHERE asp > 0")
        asp_count = cursor.fetchone()[0]
        print(f"   📊 Products with ASP values: {asp_count}")
        
        # Sample ASP data
        cursor.execute("SELECT name, unit_price, asp, mrp, tp_rate FROM products WHERE asp > 0 LIMIT 3")
        asp_products = cursor.fetchall()
        if asp_products:
            print("   📦 Sample products with ASP:")
            for p in asp_products:
                print(f"      {p[0]}: unit_price={p[1]}, asp={p[2]}, mrp={p[3]}, tp_rate={p[4]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database analysis error: {e}")
        return False

def check_specific_routes():
    """Check specific problematic routes"""
    
    print("\n" + "=" * 80)
    print("🔍 CHECKING SPECIFIC PROBLEMATIC ROUTES")
    print("=" * 80)
    
    # Check if PROD002 exists
    try:
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        print("\n1️⃣ CHECKING PRODUCT PROD002:")
        cursor.execute("SELECT product_id, name FROM products WHERE product_id = 'PROD002'")
        product = cursor.fetchone()
        if product:
            print(f"   ✅ Product PROD002 exists: {product[1]}")
        else:
            print("   ❌ Product PROD002 not found")
            
        # Check inventory INV20250728221554
        print("\n2️⃣ CHECKING INVENTORY INV20250728221554:")
        cursor.execute("SELECT inventory_id, product_name FROM inventory WHERE inventory_id = 'INV20250728221554'")
        inventory = cursor.fetchone()
        if inventory:
            print(f"   ✅ Inventory INV20250728221554 exists: {inventory[1]}")
        else:
            print("   ❌ Inventory INV20250728221554 not found")
            
        conn.close()
        
    except Exception as e:
        print(f"❌ Route checking error: {e}")

if __name__ == "__main__":
    print("🚀 Starting comprehensive database analysis...")
    
    success = analyze_database_schema()
    check_specific_routes()
    
    if success:
        print("\n✅ Database analysis completed successfully!")
    else:
        print("\n❌ Database analysis failed!")
