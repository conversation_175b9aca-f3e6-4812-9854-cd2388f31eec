#!/usr/bin/env python3
"""
Comprehensive Finance Workflow Diagnosis
"""

import sqlite3
import os
from datetime import datetime

def diagnose_finance_issues():
    """Diagnose all finance workflow issues"""
    
    print('🔍 COMPREHENSIVE FINANCE DIAGNOSIS')
    print('=' * 80)
    
    db_path = 'instance/medivent.db'
    if not os.path.exists(db_path):
        print('❌ Database not found')
        return
    
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    # 1. HELD INVOICES DIAGNOSIS
    print('\n📋 HELD INVOICES DIAGNOSIS')
    print('-' * 50)
    
    # Check invoice_holds table
    try:
        cursor.execute('SELECT COUNT(*) as total FROM invoice_holds')
        total_holds = cursor.fetchone()['total']
        print(f'Total records in invoice_holds table: {total_holds}')
        
        if total_holds == 0:
            print('❌ ISSUE: No records in invoice_holds table')
            print('   This is why held invoices page shows "No Invoices on Hold"')
            
            # Check if there are orders that should be on hold
            cursor.execute('SELECT COUNT(*) as held_orders FROM orders WHERE status LIKE "%hold%" OR status LIKE "%Hold%"')
            held_orders = cursor.fetchone()['held_orders']
            print(f'   Orders with hold status in orders table: {held_orders}')
            
            if held_orders > 0:
                print('   ⚠️ MISMATCH: Orders marked as held but not in invoice_holds table')
                cursor.execute('SELECT order_id, customer_name, status FROM orders WHERE status LIKE "%hold%" OR status LIKE "%Hold%" LIMIT 5')
                held_samples = cursor.fetchall()
                for order in held_samples:
                    print(f'     {order["order_id"]}: {order["customer_name"]} - {order["status"]}')
        else:
            cursor.execute('SELECT * FROM invoice_holds WHERE status = "active" LIMIT 5')
            active_holds = cursor.fetchall()
            print(f'Active holds: {len(active_holds)}')
            for hold in active_holds:
                print(f'  {hold["hold_id"]}: Order {hold["order_id"]} - {hold["hold_reason"]}')
    
    except Exception as e:
        print(f'❌ Error checking invoice_holds: {e}')
    
    # 2. CUSTOMER LEDGER DIAGNOSIS
    print('\n📋 CUSTOMER LEDGER DIAGNOSIS')
    print('-' * 50)
    
    try:
        # Check total customers
        cursor.execute('SELECT COUNT(*) as total FROM customers')
        total_customers = cursor.fetchone()['total']
        print(f'Total customers in database: {total_customers}')
        
        # Check customers with orders
        cursor.execute('''
            SELECT COUNT(DISTINCT customer_name) as customers_with_orders 
            FROM orders 
            WHERE status != "Cancelled"
        ''')
        customers_with_orders = cursor.fetchone()['customers_with_orders']
        print(f'Customers with orders: {customers_with_orders}')
        
        # Check customers with outstanding amounts (current ledger query)
        cursor.execute('''
            SELECT COUNT(*) as customers_with_outstanding
            FROM (
                SELECT customer_name
                FROM orders 
                WHERE status != "Cancelled"
                GROUP BY customer_name
                HAVING SUM(CASE WHEN payment_status = "pending" THEN order_amount ELSE 0 END) > 0
            )
        ''')
        customers_with_outstanding = cursor.fetchone()['customers_with_outstanding']
        print(f'Customers with outstanding amounts: {customers_with_outstanding}')
        
        if customers_with_outstanding == 0:
            print('❌ ISSUE: No customers with outstanding amounts')
            print('   This is why customer ledger shows "No customers found"')
            
            # Check payment statuses
            cursor.execute('SELECT payment_status, COUNT(*) as count FROM orders WHERE status != "Cancelled" GROUP BY payment_status')
            payment_statuses = cursor.fetchall()
            print('   Payment status breakdown:')
            for status in payment_statuses:
                print(f'     {status["payment_status"]}: {status["count"]} orders')
        else:
            # Show sample customers with outstanding amounts
            cursor.execute('''
                SELECT customer_name, 
                       SUM(CASE WHEN payment_status = "pending" THEN order_amount ELSE 0 END) as outstanding
                FROM orders 
                WHERE status != "Cancelled"
                GROUP BY customer_name
                HAVING outstanding > 0
                ORDER BY outstanding DESC
                LIMIT 5
            ''')
            outstanding_customers = cursor.fetchall()
            print('Sample customers with outstanding amounts:')
            for customer in outstanding_customers:
                print(f'  {customer["customer_name"]}: Rs.{customer["outstanding"]}')
    
    except Exception as e:
        print(f'❌ Error checking customer ledger: {e}')
    
    # 3. PAYMENT COLLECTION DIAGNOSIS
    print('\n📋 PAYMENT COLLECTION DIAGNOSIS')
    print('-' * 50)
    
    try:
        # Check orders for payment collection
        cursor.execute('''
            SELECT COUNT(*) as pending_payments
            FROM orders 
            WHERE payment_status = "pending" AND status != "Cancelled"
        ''')
        pending_payments = cursor.fetchone()['pending_payments']
        print(f'Orders with pending payments: {pending_payments}')
        
        if pending_payments > 0:
            # Check specific order mentioned in URL
            cursor.execute('SELECT * FROM orders WHERE order_id = "ORD00000147"')
            specific_order = cursor.fetchone()
            if specific_order:
                print(f'Order ORD00000147 details:')
                print(f'  Customer: {specific_order["customer_name"]}')
                print(f'  Amount: Rs.{specific_order["order_amount"]}')
                print(f'  Payment Status: {specific_order["payment_status"]}')
                print(f'  Status: {specific_order["status"]}')
            else:
                print('❌ Order ORD00000147 not found')
            
            # Sample pending orders
            cursor.execute('''
                SELECT order_id, customer_name, order_amount, payment_status
                FROM orders 
                WHERE payment_status = "pending" AND status != "Cancelled"
                LIMIT 5
            ''')
            pending_orders = cursor.fetchall()
            print('Sample pending payment orders:')
            for order in pending_orders:
                print(f'  {order["order_id"]}: {order["customer_name"]} - Rs.{order["order_amount"]}')
    
    except Exception as e:
        print(f'❌ Error checking payment collection: {e}')
    
    # 4. ACCOUNTS RECEIVABLE DIAGNOSIS
    print('\n📋 ACCOUNTS RECEIVABLE DIAGNOSIS')
    print('-' * 50)
    
    try:
        # Check if accounts_receivable table exists
        cursor.execute('SELECT name FROM sqlite_master WHERE type="table" AND name="accounts_receivable"')
        ar_table = cursor.fetchone()
        
        if ar_table:
            cursor.execute('SELECT COUNT(*) as total FROM accounts_receivable')
            ar_count = cursor.fetchone()['total']
            print(f'Records in accounts_receivable table: {ar_count}')
        else:
            print('❌ accounts_receivable table does not exist')
            print('   This may cause issues with payment processing integration')
    
    except Exception as e:
        print(f'❌ Error checking accounts receivable: {e}')
    
    # 5. RECOMMENDATIONS
    print('\n🔧 RECOMMENDATIONS')
    print('-' * 50)
    
    print('1. HELD INVOICES FIX:')
    print('   - Create sample held invoice records in invoice_holds table')
    print('   - OR modify route to show orders with hold status from orders table')
    
    print('\n2. CUSTOMER LEDGER FIX:')
    print('   - Modify query to show all customers with orders (not just outstanding)')
    print('   - OR create sample orders with pending payment status')
    
    print('\n3. PAYMENT COLLECTION FIX:')
    print('   - Verify customer name field mapping in template')
    print('   - Check if customer_name is properly passed to template')
    
    print('\n4. ACCOUNTS RECEIVABLE INTEGRATION:')
    print('   - Create accounts_receivable table if missing')
    print('   - Implement payment processing to update receivables')
    
    conn.close()
    print('\n✅ Diagnosis complete')

if __name__ == "__main__":
    diagnose_finance_issues()
