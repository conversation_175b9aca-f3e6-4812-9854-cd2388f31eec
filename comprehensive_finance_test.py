#!/usr/bin/env python3
"""
Comprehensive Finance Routes Testing Script
Tests all finance routes systematically and verifies HTTP 200 responses
"""

import requests
import time
import sys
from datetime import datetime

def test_finance_routes():
    """Test all finance routes systematically"""
    
    base_url = "http://127.0.0.1:5001"
    
    # Comprehensive list of finance routes to test
    finance_routes = [
        # Core Finance Routes
        "/finance",
        "/finance/dashboard", 
        "/finance/held-invoices",
        "/finance/customer-ledger",
        "/finance/payment-collection",
        "/finance/pending-invoices",
        "/finance/accounts-receivable",
        "/finance/invoice-generation",
        "/finance/payment-history",
        "/finance/analytics",
        
        # Enhanced Finance Routes
        "/finance/aging-analysis",
        "/finance/salesperson-ledger", 
        "/finance/division-ledger",
        "/finance/financial-reports",
        "/finance/settings",
        
        # API Routes
        "/finance/api/stats",
        "/finance/api/dashboard-data",
        
        # Specific Test Routes
        "/finance/customer-ledger?view=aging",
        "/finance/payment-collection?invoice_id=ORD00000147",
        "/finance/held-invoices",
    ]
    
    print("🔍 COMPREHENSIVE FINANCE ROUTES TESTING")
    print("=" * 80)
    print(f"🌐 Base URL: {base_url}")
    print(f"📋 Testing {len(finance_routes)} routes")
    print(f"🕒 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    results = []
    passed = 0
    failed = 0
    
    for i, route in enumerate(finance_routes, 1):
        print(f"\n[{i:2d}/{len(finance_routes)}] Testing: {route}")
        
        try:
            # Make request with timeout
            response = requests.get(f"{base_url}{route}", timeout=10)
            status_code = response.status_code
            
            if status_code == 200:
                print(f"    ✅ SUCCESS - HTTP {status_code}")
                results.append((route, status_code, "SUCCESS", ""))
                passed += 1
            elif status_code == 302:
                print(f"    🔄 REDIRECT - HTTP {status_code} (likely login required)")
                results.append((route, status_code, "REDIRECT", "Login required"))
                passed += 1  # Redirects are expected for protected routes
            elif status_code == 404:
                print(f"    ❌ NOT FOUND - HTTP {status_code}")
                results.append((route, status_code, "NOT_FOUND", "Route not found"))
                failed += 1
            elif status_code == 500:
                print(f"    💥 SERVER ERROR - HTTP {status_code}")
                results.append((route, status_code, "SERVER_ERROR", "Internal server error"))
                failed += 1
            else:
                print(f"    ⚠️  UNEXPECTED - HTTP {status_code}")
                results.append((route, status_code, "UNEXPECTED", f"Unexpected status code"))
                failed += 1
                
        except requests.exceptions.ConnectionError:
            print(f"    🔌 CONNECTION ERROR - Server not running")
            results.append((route, 0, "CONNECTION_ERROR", "Server not running"))
            failed += 1
            
        except requests.exceptions.Timeout:
            print(f"    ⏰ TIMEOUT - Request timed out")
            results.append((route, 0, "TIMEOUT", "Request timeout"))
            failed += 1
            
        except Exception as e:
            print(f"    💥 ERROR - {str(e)}")
            results.append((route, 0, "ERROR", str(e)))
            failed += 1
        
        # Small delay between requests
        time.sleep(0.1)
    
    # Print comprehensive summary
    print("\n" + "=" * 80)
    print("📊 COMPREHENSIVE TEST RESULTS SUMMARY")
    print("=" * 80)
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success Rate: {(passed/(passed+failed)*100):.1f}%")
    print(f"🕒 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Detailed results
    print("\n📋 DETAILED RESULTS:")
    print("-" * 80)
    for route, status, result, error in results:
        status_icon = "✅" if result in ["SUCCESS", "REDIRECT"] else "❌"
        print(f"{status_icon} {route:<50} HTTP {status:<3} {result}")
        if error:
            print(f"   └─ {error}")
    
    # Recommendations
    print("\n🔧 RECOMMENDATIONS:")
    print("-" * 80)
    
    if failed == 0:
        print("🎉 All routes are working perfectly!")
        print("✅ Finance module is fully functional")
    else:
        print("⚠️  Some routes need attention:")
        
        failed_routes = [r for r in results if r[2] not in ["SUCCESS", "REDIRECT"]]
        for route, status, result, error in failed_routes:
            print(f"   • Fix {route} - {result}")
    
    return passed, failed, results

if __name__ == "__main__":
    try:
        passed, failed, results = test_finance_routes()
        
        # Exit with appropriate code
        if failed == 0:
            print("\n🎉 ALL TESTS PASSED!")
            sys.exit(0)
        else:
            print(f"\n⚠️  {failed} TESTS FAILED!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n⏹️  Testing interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Testing script error: {e}")
        sys.exit(1)
