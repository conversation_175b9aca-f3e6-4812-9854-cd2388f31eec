#!/usr/bin/env python3
"""
Comprehensive Fix Validation for Critical Errors
Phase 4: Complete end-to-end testing and validation
"""

import sqlite3
import os
import json
from datetime import datetime

def setup_test_environment():
    """Set up test environment with required order and data"""
    print("🔧 SETTING UP TEST ENVIRONMENT")
    print("=" * 60)
    
    try:
        # Ensure database exists
        if not os.path.exists('instance/medivent.db'):
            print("❌ Database file not found")
            return False
            
        conn = sqlite3.connect('instance/medivent.db')
        conn.row_factory = sqlite3.Row
        
        # Check if order ORD00000155 exists
        order = conn.execute('SELECT * FROM orders WHERE order_id = ?', ('ORD00000155',)).fetchone()
        
        if not order:
            print("Creating test order ORD00000155...")
            
            # Create the order
            conn.execute('''
                INSERT OR REPLACE INTO orders 
                (order_id, customer_name, customer_phone, customer_address, 
                 order_amount, order_date, status, created_at, priority_level)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                'ORD00000155',
                '3Minur',
                '00211111',
                '4',
                444.0,
                '2025-07-20 00:01',
                'Normal',
                datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                1
            ))
            
            # Create order items
            conn.execute('''
                INSERT OR REPLACE INTO order_items 
                (order_id, product_id, product_name, quantity, unit_price, total_price)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', ('ORD00000155', 'P001', 'Sample Medicine', 1, 444.0, 444.0))
            
            conn.commit()
            print("✅ Test order created")
        else:
            print("✅ Test order already exists")
            
        # Verify order data
        order = conn.execute('SELECT * FROM orders WHERE order_id = ?', ('ORD00000155',)).fetchone()
        items = conn.execute('SELECT * FROM order_items WHERE order_id = ?', ('ORD00000155',)).fetchall()
        
        print(f"   Order ID: {order['order_id']}")
        print(f"   Customer: {order['customer_name']}")
        print(f"   Amount: Rs.{order['order_amount']}")
        print(f"   Items: {len(items)} items")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Setup error: {e}")
        return False

def validate_file_structure():
    """Validate all required files exist"""
    print("\n🔍 VALIDATING FILE STRUCTURE")
    print("=" * 60)
    
    required_files = {
        'API Endpoints': 'api_endpoints.py',
        'Orders Routes': 'routes/orders.py',
        'QR Generator': 'utils/qr_code_generator.py',
        'Enhanced Modal JS': 'static/js/enhanced_modal.js',
        'Enhanced Modal CSS': 'static/css/enhanced_modal.css',
        'Enhanced Modal Template': 'templates/components/enhanced_order_modal.html',
        'Address Label Template': 'templates/warehouse/address_label.html',
        'Packing Dashboard': 'templates/warehouse/packing_dashboard.html'
    }
    
    all_files_exist = True
    
    for description, file_path in required_files.items():
        if os.path.exists(file_path):
            print(f"✅ {description}: {file_path}")
        else:
            print(f"❌ {description}: {file_path} - MISSING")
            all_files_exist = False
    
    return all_files_exist

def validate_routes_added():
    """Validate that new routes were added correctly"""
    print("\n🔍 VALIDATING ROUTES ADDED")
    print("=" * 60)
    
    try:
        with open('routes/orders.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for print-address route
        if '@orders_bp.route(\'/<order_id>/print-address\')' in content:
            print("✅ Print address route added")
        else:
            print("❌ Print address route missing")
            return False
            
        # Check for details JSON route
        if '@orders_bp.route(\'/<order_id>/details\')' in content:
            print("✅ Order details JSON route added")
        else:
            print("❌ Order details JSON route missing")
            return False
            
        # Check for proper function names
        if 'def print_address_label(order_id):' in content:
            print("✅ Print address function defined")
        else:
            print("❌ Print address function missing")
            return False
            
        if 'def get_order_details_json(order_id):' in content:
            print("✅ Order details JSON function defined")
        else:
            print("❌ Order details JSON function missing")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Route validation error: {e}")
        return False

def validate_api_endpoints():
    """Validate API endpoints exist"""
    print("\n🔍 VALIDATING API ENDPOINTS")
    print("=" * 60)
    
    try:
        with open('api_endpoints.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for order details API
        if '@api_bp.route(\'/order-details/<order_id>\', methods=[\'GET\'])' in content:
            print("✅ Order details API endpoint exists")
        else:
            print("❌ Order details API endpoint missing")
            return False
            
        # Check for QR code API
        if '@api_bp.route(\'/order-qr-code/<order_id>\', methods=[\'GET\'])' in content:
            print("✅ QR code API endpoint exists")
        else:
            print("❌ QR code API endpoint missing")
            return False
            
        # Check for QR generator import
        if 'from utils.qr_code_generator import generate_order_qr_code' in content:
            print("✅ QR generator import exists")
        else:
            print("❌ QR generator import missing")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ API validation error: {e}")
        return False

def validate_qr_dependencies():
    """Validate QR code dependencies"""
    print("\n🔍 VALIDATING QR DEPENDENCIES")
    print("=" * 60)
    
    try:
        import qrcode
        print("✅ qrcode library available")
        
        from PIL import Image
        print("✅ PIL (Pillow) library available")
        
        from utils.qr_code_generator import generate_order_qr_code
        print("✅ QR generator module importable")
        
        # Test QR generation
        test_data = {
            'success': True,
            'order': {'order_id': 'TEST', 'customer_name': 'Test', 'order_amount': 100},
            'order_items': [{'product_name': 'Test Product', 'quantity': 1}],
            'summary': {'total_items': 1, 'order_amount': 100}
        }
        
        result = generate_order_qr_code(test_data, include_branding=False)
        if result.get('success'):
            print("✅ QR code generation test successful")
            return True
        else:
            print(f"❌ QR code generation failed: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ QR dependency error: {e}")
        return False

def validate_static_directories():
    """Ensure static directories exist"""
    print("\n🔍 VALIDATING STATIC DIRECTORIES")
    print("=" * 60)
    
    directories = [
        'static',
        'static/qr_codes',
        'static/uploads',
        'static/js',
        'static/css'
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)
            print(f"✅ Created directory: {directory}")
        else:
            print(f"✅ Directory exists: {directory}")
    
    return True

def main():
    """Run comprehensive validation"""
    print("🎯 COMPREHENSIVE FIX VALIDATION")
    print("=" * 80)
    print("Validating fixes for 'Unable to Load Order Details' and 'QR Code unavailable'")
    print("=" * 80)
    
    # Run all validations
    setup_ok = setup_test_environment()
    files_ok = validate_file_structure()
    routes_ok = validate_routes_added()
    api_ok = validate_api_endpoints()
    qr_ok = validate_qr_dependencies()
    dirs_ok = validate_static_directories()
    
    # Summary
    print("\n📊 VALIDATION RESULTS")
    print("=" * 60)
    print(f"Test Environment: {'✅ READY' if setup_ok else '❌ FAILED'}")
    print(f"File Structure: {'✅ COMPLETE' if files_ok else '❌ INCOMPLETE'}")
    print(f"Routes Added: {'✅ SUCCESS' if routes_ok else '❌ FAILED'}")
    print(f"API Endpoints: {'✅ VALID' if api_ok else '❌ INVALID'}")
    print(f"QR Dependencies: {'✅ WORKING' if qr_ok else '❌ BROKEN'}")
    print(f"Static Directories: {'✅ READY' if dirs_ok else '❌ FAILED'}")
    
    all_ok = setup_ok and files_ok and routes_ok and api_ok and qr_ok and dirs_ok
    
    if all_ok:
        print("\n🎉 ALL VALIDATIONS PASSED!")
        print("=" * 60)
        print("✅ Both critical errors should now be resolved")
        print("✅ Enhanced modal should load order details successfully")
        print("✅ QR codes should generate and display properly")
        print("✅ Print address functionality should work")
        print("\n🚀 NEXT STEPS:")
        print("1. Restart Flask server to pick up new routes")
        print("2. Test in browser: warehouse packing page")
        print("3. Click 'View Details' for order ORD00000155")
        print("4. Click 'Print Address' for order ORD00000155")
        print("5. Verify both functions work without errors")
    else:
        print("\n⚠️ SOME VALIDATIONS FAILED")
        print("Please check the failed items above and fix before testing")

if __name__ == "__main__":
    main()
