#!/usr/bin/env python3
"""
Comprehensive Hold Workflow Fix
This script will diagnose and fix all issues with the hold workflow
"""

import sqlite3
import os
from datetime import datetime
import time

def comprehensive_hold_fix():
    print("🔧 COMPREHENSIVE HOLD WORKFLOW FIX")
    print("=" * 60)
    
    db_path = 'instance/medivent.db'
    
    # Step 1: Verify database exists
    if not os.path.exists(db_path):
        print("❌ Database file not found!")
        return False
    
    print("✅ Database file found")
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Step 2: Ensure invoice_holds table exists with correct structure
        print("\n🔧 Step 2: Ensuring invoice_holds table exists...")
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS invoice_holds (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                hold_id TEXT UNIQUE NOT NULL,
                order_id TEXT NOT NULL,
                invoice_id TEXT,
                hold_reason TEXT NOT NULL,
                hold_comments TEXT,
                hold_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                hold_by TEXT NOT NULL,
                release_date TIMESTAMP,
                release_by TEXT,
                release_comments TEXT,
                status TEXT DEFAULT 'active' CHECK (status IN ('active', 'released')),
                priority_level TEXT DEFAULT 'normal' CHECK (priority_level IN ('low', 'normal', 'high', 'urgent')),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        print("✅ invoice_holds table ensured")
        
        # Step 3: Check current state
        print("\n📊 Step 3: Checking current state...")
        
        cursor.execute('SELECT COUNT(*) as count FROM invoice_holds')
        total_holds = cursor.fetchone()['count']
        print(f"Total holds in database: {total_holds}")
        
        cursor.execute('SELECT COUNT(*) as count FROM invoice_holds WHERE status = "active"')
        active_holds = cursor.fetchone()['count']
        print(f"Active holds: {active_holds}")
        
        # Step 4: Check specific order ORD00000243
        print("\n🔍 Step 4: Checking ORD00000243...")
        
        cursor.execute('SELECT order_id, customer_name, status FROM orders WHERE order_id = ?', ('ORD00000243',))
        order = cursor.fetchone()
        
        if order:
            print(f"Order found: {order['order_id']} | {order['customer_name']} | {order['status']}")
            
            # Check if hold record exists
            cursor.execute('SELECT * FROM invoice_holds WHERE order_id = ?', ('ORD00000243',))
            hold = cursor.fetchone()
            
            if hold:
                print("✅ Hold record exists:")
                print(f"   Hold ID: {hold['hold_id']}")
                print(f"   Status: {hold['status']}")
                print(f"   Reason: {hold['hold_reason']}")
                print(f"   Date: {hold['hold_date']}")
            else:
                print("❌ No hold record found - Creating one now...")
                
                # Create a hold record for testing
                hold_id = f"HOLD{int(time.time())}"
                cursor.execute('''
                    INSERT INTO invoice_holds (
                        hold_id, order_id, hold_reason, hold_comments,
                        hold_date, hold_by, priority_level, status
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    hold_id, 'ORD00000243', 'Test hold for verification',
                    'Created by comprehensive fix script',
                    datetime.now().isoformat(), 'system', 'normal', 'active'
                ))
                
                # Update order status
                cursor.execute('''
                    UPDATE orders 
                    SET status = 'On Hold' 
                    WHERE order_id = ?
                ''', ('ORD00000243',))
                
                print(f"✅ Created hold record: {hold_id}")
        else:
            print("❌ Order ORD00000243 not found!")
        
        # Step 5: Test the held invoices query
        print("\n🧪 Step 5: Testing held invoices query...")
        
        try:
            held_invoices = cursor.execute('''
                SELECT
                    ih.hold_id, ih.order_id, ih.hold_reason, ih.hold_comments,
                    ih.hold_date, ih.hold_by, ih.priority_level,
                    o.customer_name, o.order_amount, o.order_date, o.status,
                    CAST(julianday('now') - julianday(ih.hold_date) AS INTEGER) as days_on_hold
                FROM invoice_holds ih
                JOIN orders o ON ih.order_id = o.order_id
                WHERE ih.status = 'active'
                ORDER BY ih.hold_date DESC
            ''').fetchall()
            
            print(f"✅ Query successful - Found {len(held_invoices)} held invoices")
            
            if held_invoices:
                print("\nHeld invoices:")
                for invoice in held_invoices:
                    print(f"  {invoice['order_id']} | {invoice['customer_name']} | {invoice['hold_reason']}")
                    
                # Check if ORD00000243 is in results
                ord243_found = any(inv['order_id'] == 'ORD00000243' for inv in held_invoices)
                if ord243_found:
                    print("✅ ORD00000243 found in held invoices query!")
                else:
                    print("❌ ORD00000243 NOT found in held invoices query")
            else:
                print("⚠️ No held invoices found")
                
        except Exception as e:
            print(f"❌ Query failed: {e}")
            return False
        
        # Step 6: Commit changes
        conn.commit()
        print("\n✅ All changes committed to database")
        
        # Step 7: Final verification
        print("\n🎯 Step 7: Final verification...")
        
        cursor.execute('SELECT COUNT(*) as count FROM invoice_holds WHERE status = "active"')
        final_active = cursor.fetchone()['count']
        print(f"Final active holds count: {final_active}")
        
        cursor.execute('''
            SELECT COUNT(*) as count 
            FROM invoice_holds ih 
            JOIN orders o ON ih.order_id = o.order_id 
            WHERE ih.status = 'active'
        ''')
        joinable_holds = cursor.fetchone()['count']
        print(f"Holds that can join with orders: {joinable_holds}")
        
        if final_active > 0 and joinable_holds == final_active:
            print("✅ Hold workflow is now working correctly!")
            return True
        else:
            print("❌ There are still issues with the hold workflow")
            return False
        
    except Exception as e:
        print(f"❌ Error during fix: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    success = comprehensive_hold_fix()
    if success:
        print("\n🎉 HOLD WORKFLOW FIX COMPLETED SUCCESSFULLY!")
        print("✅ The held invoices page should now show held orders correctly")
    else:
        print("\n❌ HOLD WORKFLOW FIX FAILED!")
        print("Please check the errors above and try again")
