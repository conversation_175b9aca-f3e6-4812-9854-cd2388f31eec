#!/usr/bin/env python3
"""
Comprehensive test to understand inventory route behavior
"""

import requests
import time
from urllib.parse import urlparse

def test_with_session():
    """Test inventory route with proper session handling"""
    try:
        print("🔍 COMPREHENSIVE INVENTORY TEST")
        print("=" * 60)
        
        # Create session
        session = requests.Session()
        
        # Step 1: Get login page
        print("1️⃣ Getting login page...")
        login_page = session.get('http://127.0.0.1:5001/login', timeout=10)
        print(f"Login page status: {login_page.status_code}")
        
        # Step 2: Login
        print("\n2️⃣ Logging in...")
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        login_response = session.post(
            'http://127.0.0.1:5001/login', 
            data=login_data, 
            timeout=10,
            allow_redirects=True
        )
        
        print(f"Login response status: {login_response.status_code}")
        print(f"Login response URL: {login_response.url}")
        
        # Check if login was successful
        if 'dashboard' in login_response.url or login_response.status_code == 200:
            print("✅ Login appears successful")
        else:
            print("❌ Login may have failed")
            return False
        
        # Step 3: Test dashboard first
        print("\n3️⃣ Testing dashboard access...")
        dashboard_response = session.get('http://127.0.0.1:5001/dashboard', timeout=10)
        print(f"Dashboard status: {dashboard_response.status_code}")
        print(f"Dashboard URL: {dashboard_response.url}")
        
        # Step 4: Test inventory route
        print("\n4️⃣ Testing inventory route...")
        inventory_response = session.get(
            'http://127.0.0.1:5001/inventory/', 
            timeout=10,
            allow_redirects=False  # Don't follow redirects to see what happens
        )
        
        print(f"Inventory status: {inventory_response.status_code}")
        print(f"Inventory headers: {dict(inventory_response.headers)}")
        
        if inventory_response.status_code == 302:
            redirect_location = inventory_response.headers.get('Location', '')
            print(f"❌ REDIRECT DETECTED: {redirect_location}")
            
            # Follow the redirect to see where it goes
            if redirect_location:
                print("\n5️⃣ Following redirect...")
                redirect_response = session.get(
                    redirect_location if redirect_location.startswith('http') else f'http://127.0.0.1:5001{redirect_location}',
                    timeout=10
                )
                print(f"Redirect destination status: {redirect_response.status_code}")
                print(f"Redirect destination URL: {redirect_response.url}")
                
                # Check content for error messages
                content = redirect_response.text
                if 'Access denied' in content:
                    print("🔍 Found 'Access denied' message")
                elif 'Permission' in content:
                    print("🔍 Found permission-related message")
                elif 'error' in content.lower():
                    print("🔍 Found error message")
                else:
                    print("🔍 No obvious error message found")
            
            return False
            
        elif inventory_response.status_code == 200:
            print("✅ SUCCESS! Inventory route returned 200")
            
            # Check content
            content = inventory_response.text
            print(f"Content length: {len(content)}")
            
            # Look for key elements
            checks = [
                ('Products Overview', 'Products Overview section'),
                ('Inventory Records', 'Inventory Records section'),
                ('Paracetamol', 'Paracetamol product'),
                ('1435', 'Stock quantity 1435'),
                ('1319', 'Available stock 1319'),
                ('batch', 'Batch information')
            ]
            
            for search_term, description in checks:
                if search_term in content:
                    print(f"✅ Found {description}")
                else:
                    print(f"❌ Missing {description}")
            
            # Save successful response
            with open('inventory_success_response.html', 'w', encoding='utf-8') as f:
                f.write(content)
            print("💾 Response saved to: inventory_success_response.html")
            
            return True
        
        else:
            print(f"❌ Unexpected status code: {inventory_response.status_code}")
            return False
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 COMPREHENSIVE INVENTORY ROUTE TEST")
    print("=" * 70)
    
    success = test_with_session()
    
    print("\n" + "=" * 70)
    print("📊 FINAL RESULT:")
    print("=" * 70)
    
    if success:
        print("🎉 INVENTORY ROUTE IS WORKING!")
        print("✅ The batch display fix has been successfully applied")
        print("✅ Users can now see batch counts and available stock")
    else:
        print("❌ INVENTORY ROUTE STILL HAS ISSUES")
        print("🔍 Check the output above for specific error details")
    
    print("=" * 70)
