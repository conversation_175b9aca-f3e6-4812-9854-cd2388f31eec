#!/usr/bin/env python3
"""
Comprehensive Investigation Analysis for Inventory Conflicts and Expected Delivery Date Issues
"""

import sqlite3
import os
from datetime import datetime

def analyze_database_structure():
    """Analyze database structure and identify issues"""
    print("=" * 80)
    print("COMPREHENSIVE INVESTIGATION ANALYSIS")
    print("=" * 80)
    print(f"Analysis Date: {datetime.now()}")
    print()
    
    db_path = 'instance/medivent.db'
    if not os.path.exists(db_path):
        print("❌ Database not found at instance/medivent.db")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        print("1. DATABASE STRUCTURE ANALYSIS")
        print("-" * 50)
        
        # Check orders table structure
        cursor.execute('PRAGMA table_info(orders)')
        orders_columns = cursor.fetchall()
        
        print("Orders Table Columns:")
        expected_delivery_exists = False
        for col in orders_columns:
            if 'expected' in col[1].lower() or 'delivery' in col[1].lower():
                print(f"  ✅ {col[1]} - {col[2]} - Default: {col[4]}")
                if 'estimated_delivery_date' in col[1]:
                    expected_delivery_exists = True
            elif col[1] in ['order_id', 'customer_name', 'order_date', 'status']:
                print(f"  ✅ {col[1]} - {col[2]} - Default: {col[4]}")
        
        print(f"\nExpected Delivery Date field exists: {'✅ YES' if expected_delivery_exists else '❌ NO'}")
        
        # Check inventory vs products stock conflicts
        print("\n2. INVENTORY CONFLICTS ANALYSIS")
        print("-" * 50)
        
        # Get products with stock_quantity
        cursor.execute('''
            SELECT product_id, name, stock_quantity 
            FROM products 
            WHERE stock_quantity > 0 
            LIMIT 10
        ''')
        products_with_stock = cursor.fetchall()
        
        print(f"Products table has {len(products_with_stock)} products with stock_quantity > 0")
        
        # Get inventory records
        cursor.execute('''
            SELECT product_id, SUM(stock_quantity) as total_stock, 
                   SUM(allocated_quantity) as total_allocated,
                   SUM(stock_quantity - allocated_quantity) as available_stock
            FROM inventory 
            WHERE status = 'active'
            GROUP BY product_id
            LIMIT 10
        ''')
        inventory_stock = cursor.fetchall()
        
        print(f"Inventory table has {len(inventory_stock)} products with active inventory")
        
        # Check for conflicts
        conflicts = []
        for product in products_with_stock:
            product_id = product['product_id']
            product_stock = product['stock_quantity']
            
            # Find corresponding inventory
            inv_record = None
            for inv in inventory_stock:
                if inv['product_id'] == product_id:
                    inv_record = inv
                    break
            
            if inv_record:
                inv_available = inv_record['available_stock']
                if product_stock != inv_available:
                    conflicts.append({
                        'product_id': product_id,
                        'product_name': product['name'],
                        'products_stock': product_stock,
                        'inventory_available': inv_available,
                        'difference': product_stock - inv_available
                    })
        
        print(f"\n📊 STOCK CONFLICTS FOUND: {len(conflicts)}")
        for conflict in conflicts[:5]:  # Show first 5 conflicts
            print(f"  ❌ {conflict['product_id']} ({conflict['product_name']})")
            print(f"     Products table: {conflict['products_stock']}")
            print(f"     Inventory available: {conflict['inventory_available']}")
            print(f"     Difference: {conflict['difference']}")
            print()
        
        # Check order creation issues
        print("3. ORDER CREATION ANALYSIS")
        print("-" * 50)
        
        # Check recent orders
        cursor.execute('''
            SELECT order_id, customer_name, order_date, estimated_delivery_date, status
            FROM orders 
            ORDER BY order_date DESC 
            LIMIT 5
        ''')
        recent_orders = cursor.fetchall()
        
        print(f"Recent orders: {len(recent_orders)}")
        delivery_date_missing = 0
        for order in recent_orders:
            has_delivery_date = order['estimated_delivery_date'] is not None
            print(f"  Order {order['order_id']}: Delivery Date = {'✅' if has_delivery_date else '❌ Missing'}")
            if not has_delivery_date:
                delivery_date_missing += 1
        
        print(f"\nOrders missing delivery date: {delivery_date_missing}/{len(recent_orders)}")
        
        # Check order items allocation
        print("\n4. ORDER ITEMS ALLOCATION ANALYSIS")
        print("-" * 50)
        
        cursor.execute('''
            SELECT oi.order_id, oi.product_id, oi.quantity, 
                   p.name as product_name,
                   COALESCE(SUM(i.stock_quantity - i.allocated_quantity), 0) as available_stock
            FROM order_items oi
            LEFT JOIN products p ON oi.product_id = p.product_id
            LEFT JOIN inventory i ON oi.product_id = i.product_id AND i.status = 'active'
            WHERE oi.status = 'Placed'
            GROUP BY oi.order_id, oi.product_id, oi.quantity, p.name
            LIMIT 10
        ''')
        order_items = cursor.fetchall()
        
        allocation_issues = 0
        for item in order_items:
            if item['available_stock'] < item['quantity']:
                allocation_issues += 1
                print(f"  ❌ Order {item['order_id']}: {item['product_name']}")
                print(f"     Requested: {item['quantity']}, Available: {item['available_stock']}")
        
        print(f"\nOrder items with insufficient stock: {allocation_issues}/{len(order_items)}")
        
        conn.close()
        
        # Summary
        print("\n5. SUMMARY OF ISSUES")
        print("-" * 50)
        issues = []
        
        if not expected_delivery_exists:
            issues.append("❌ Expected Delivery Date field missing from orders table")
        
        if conflicts:
            issues.append(f"❌ {len(conflicts)} stock conflicts between products and inventory tables")
        
        if delivery_date_missing > 0:
            issues.append(f"❌ {delivery_date_missing} recent orders missing delivery dates")
        
        if allocation_issues > 0:
            issues.append(f"❌ {allocation_issues} order items with insufficient stock")
        
        if not issues:
            print("✅ No major issues found!")
        else:
            for issue in issues:
                print(f"  {issue}")
        
        return len(issues) == 0
        
    except Exception as e:
        print(f"❌ Analysis failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    analyze_database_structure()
