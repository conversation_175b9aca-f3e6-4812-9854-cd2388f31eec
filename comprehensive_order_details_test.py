#!/usr/bin/env python3
"""
Comprehensive Order Details Test
Test all fixes applied to resolve the "Unable to Load Order Details" error
"""

import requests
import json
import time
import os

def test_flask_server():
    """Test if Flask server is running"""
    print("🔍 TESTING FLASK SERVER")
    print("=" * 50)
    
    try:
        response = requests.get('http://127.0.0.1:5001/', timeout=5)
        print(f"✅ Flask server running - Status: {response.status_code}")
        return True
    except requests.exceptions.ConnectionError:
        print("❌ Flask server not running")
        print("   Please start the server with: python app.py")
        return False
    except Exception as e:
        print(f"❌ Server test error: {e}")
        return False

def test_api_endpoints():
    """Test all API endpoints"""
    print("\n🔍 TESTING API ENDPOINTS")
    print("=" * 50)
    
    endpoints = [
        ('/api/order-details/ORD00000155', 'Order Details API'),
        ('/api/order-qr-code/ORD00000155', 'QR Code API'),
        ('/orders/ORD00000155/details', 'Order Details JSON (Fallback)'),
        ('/orders/ORD00000155/print-address', 'Print Address')
    ]
    
    results = {}
    
    for endpoint, description in endpoints:
        try:
            url = f'http://127.0.0.1:5001{endpoint}'
            print(f"\n📡 Testing: {description}")
            print(f"   URL: {url}")
            
            response = requests.get(url, timeout=10)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '')
                
                if 'application/json' in content_type:
                    try:
                        data = response.json()
                        success = data.get('success', False)
                        print(f"   JSON Success: {success}")
                        
                        if success:
                            print(f"   ✅ {description} - Working")
                            results[endpoint] = 'PASS'
                        else:
                            print(f"   ❌ {description} - API Error: {data.get('message', 'Unknown')}")
                            results[endpoint] = 'FAIL'
                    except json.JSONDecodeError:
                        print(f"   ❌ {description} - Invalid JSON")
                        results[endpoint] = 'FAIL'
                        
                elif 'text/html' in content_type:
                    print(f"   ✅ {description} - HTML Response ({len(response.text)} chars)")
                    results[endpoint] = 'PASS'
                else:
                    print(f"   ✅ {description} - Response ({content_type})")
                    results[endpoint] = 'PASS'
            else:
                print(f"   ❌ {description} - HTTP {response.status_code}")
                results[endpoint] = 'FAIL'
                
        except Exception as e:
            print(f"   ❌ {description} - Error: {e}")
            results[endpoint] = 'ERROR'
    
    return results

def test_warehouse_packing_page():
    """Test the warehouse packing page"""
    print("\n🔍 TESTING WAREHOUSE PACKING PAGE")
    print("=" * 50)
    
    try:
        url = 'http://127.0.0.1:5001/warehouse/packing'
        print(f"📡 Testing: {url}")
        
        response = requests.get(url, timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            html_content = response.text
            print(f"   ✅ Page loaded: {len(html_content)} characters")
            
            # Check for critical elements
            checks = [
                ('enhanced_modal.js', 'Enhanced Modal JS'),
                ('enhanced_modal.css', 'Enhanced Modal CSS'),
                ('viewOrderDetails(', 'View Details Function'),
                ('ORD00000155', 'Test Order ID'),
                ('enhancedOrderModal', 'Modal Element ID'),
                ('showEnhancedOrderDetails', 'Enhanced Modal Function')
            ]
            
            all_checks_pass = True
            for check, description in checks:
                if check in html_content:
                    print(f"   ✅ {description}: Found")
                else:
                    print(f"   ❌ {description}: Missing")
                    all_checks_pass = False
            
            return all_checks_pass
        else:
            print(f"   ❌ Page load error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Page test error: {e}")
        return False

def test_javascript_files():
    """Test JavaScript files exist and are accessible"""
    print("\n🔍 TESTING JAVASCRIPT FILES")
    print("=" * 50)
    
    js_files = [
        '/static/js/enhanced_modal.js',
        '/static/css/enhanced_modal.css'
    ]
    
    results = {}
    
    for js_file in js_files:
        try:
            url = f'http://127.0.0.1:5001{js_file}'
            print(f"📡 Testing: {js_file}")
            
            response = requests.get(url, timeout=5)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ File accessible: {len(response.text)} characters")
                results[js_file] = 'PASS'
            else:
                print(f"   ❌ File not accessible: HTTP {response.status_code}")
                results[js_file] = 'FAIL'
                
        except Exception as e:
            print(f"   ❌ File test error: {e}")
            results[js_file] = 'ERROR'
    
    return results

def create_browser_test_instructions():
    """Create instructions for browser testing"""
    print("\n🔍 BROWSER TESTING INSTRUCTIONS")
    print("=" * 50)
    
    instructions = """
🌐 BROWSER TESTING STEPS:

1. Open your browser and navigate to:
   http://127.0.0.1:5001/warehouse/packing

2. Open Browser Developer Tools (F12)
   - Go to Console tab
   - Look for any JavaScript errors (red text)

3. Test Enhanced Modal:
   - Find order ORD00000155 in the table
   - Click "View Details" button
   - Check console for debug messages

4. Expected Console Messages:
   ✅ "Warehouse packing dashboard scripts loading..."
   ✅ "Enhanced order modal initialized successfully"
   ✅ "showEnhancedOrderDetails available: true"
   ✅ "Enhanced modal element found: true"

5. If Enhanced Modal Fails:
   - Should automatically fall back to basic modal
   - Check console for fallback messages

6. Test Basic Modal Fallback:
   - If enhanced modal fails, basic modal should work
   - Should show order details in a simpler format

7. Network Tab Testing:
   - Go to Network tab in developer tools
   - Click "View Details" button
   - Check for API calls to:
     * /api/order-details/ORD00000155
     * /api/order-qr-code/ORD00000155
   - All should return HTTP 200 status

8. Common Issues to Check:
   ❌ "showEnhancedOrderDetails is not defined"
   ❌ "Enhanced order modal not found"
   ❌ "Bootstrap is not defined"
   ❌ "$ is not defined"
   ❌ Network errors (404, 500)

9. Success Criteria:
   ✅ Modal opens when clicking "View Details"
   ✅ Order information displays correctly
   ✅ QR code generates and displays
   ✅ No JavaScript errors in console
   ✅ All API calls return HTTP 200
"""
    
    print(instructions)
    
    # Save instructions to file
    try:
        with open('BROWSER_TESTING_INSTRUCTIONS.md', 'w', encoding='utf-8') as f:
            f.write(instructions)
        print("\n📄 Instructions saved to: BROWSER_TESTING_INSTRUCTIONS.md")
    except Exception as e:
        print(f"\n❌ Could not save instructions: {e}")

def main():
    """Run comprehensive test suite"""
    print("🚀 COMPREHENSIVE ORDER DETAILS TEST SUITE")
    print("=" * 80)
    print("Testing all fixes for 'Unable to Load Order Details' error")
    print("=" * 80)
    
    # Test 1: Flask Server
    server_ok = test_flask_server()
    
    if not server_ok:
        print("\n⚠️ Cannot proceed without Flask server running")
        return
    
    # Test 2: API Endpoints
    api_results = test_api_endpoints()
    
    # Test 3: Warehouse Packing Page
    page_ok = test_warehouse_packing_page()
    
    # Test 4: JavaScript Files
    js_results = test_javascript_files()
    
    # Test 5: Browser Instructions
    create_browser_test_instructions()
    
    # Summary
    print(f"\n📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    print(f"Flask Server: {'✅ RUNNING' if server_ok else '❌ NOT RUNNING'}")
    print(f"Warehouse Page: {'✅ LOADED' if page_ok else '❌ ISSUES'}")
    
    print(f"\nAPI Endpoints:")
    for endpoint, result in api_results.items():
        status = '✅ PASS' if result == 'PASS' else '❌ FAIL'
        print(f"  {endpoint}: {status}")
    
    print(f"\nJavaScript Files:")
    for js_file, result in js_results.items():
        status = '✅ PASS' if result == 'PASS' else '❌ FAIL'
        print(f"  {js_file}: {status}")
    
    # Overall assessment
    all_api_pass = all(result == 'PASS' for result in api_results.values())
    all_js_pass = all(result == 'PASS' for result in js_results.values())
    
    if server_ok and page_ok and all_api_pass and all_js_pass:
        print(f"\n🎉 ALL TESTS PASSED!")
        print("✅ Backend infrastructure is working correctly")
        print("✅ All API endpoints are functional")
        print("✅ JavaScript files are accessible")
        print("✅ Warehouse packing page loads correctly")
        print("\n🌐 Next step: Test in browser using the instructions above")
    else:
        print(f"\n⚠️ SOME TESTS FAILED")
        print("Please check the failed items above and fix before browser testing")

if __name__ == "__main__":
    main()
