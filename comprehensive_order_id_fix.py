#!/usr/bin/env python3
"""
Comprehensive Order ID Fix - Multiple Strategies Implementation and Testing
"""

import sqlite3
import os
import time
import uuid
import secrets
import threading
import json
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
import hashlib

# Windows compatibility - fcntl not available on Windows
try:
    import fcntl
    HAS_FCNTL = True
except ImportError:
    HAS_FCNTL = False

class OrderIDGenerator:
    """Multiple strategies for generating unique order IDs"""
    
    def __init__(self, db_path='instance/medivent.db'):
        self.db_path = db_path
        self.lock = threading.Lock()
        self.counter_file = 'order_counter.json'
        
    def strategy_1_auto_increment(self):
        """Strategy 1: Database auto-increment with sequence table"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Create sequence table if not exists
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS order_sequence (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Insert and get auto-increment ID
            cursor.execute('INSERT INTO order_sequence DEFAULT VALUES')
            sequence_id = cursor.lastrowid
            
            # Generate order ID with sequence
            order_id = f"ORD{sequence_id:08d}"
            
            conn.commit()
            conn.close()
            
            return order_id
            
        except Exception as e:
            print(f"Strategy 1 error: {e}")
            return None
    
    def strategy_2_uuid_based(self):
        """Strategy 2: Pure UUID4 approach"""
        try:
            # Generate UUID4 and take first 12 characters
            uuid_str = str(uuid.uuid4()).replace('-', '').upper()[:12]
            order_id = f"ORD{uuid_str}"
            return order_id
            
        except Exception as e:
            print(f"Strategy 2 error: {e}")
            return None
    
    def strategy_3_atomic_counter(self):
        """Strategy 3: Atomic counter with file locking"""
        try:
            with self.lock:
                # Read current counter
                if os.path.exists(self.counter_file):
                    with open(self.counter_file, 'r') as f:
                        data = json.load(f)
                        counter = data.get('counter', 1)
                else:
                    counter = 1
                
                # Increment counter
                counter += 1
                
                # Write back to file
                with open(self.counter_file, 'w') as f:
                    json.dump({'counter': counter, 'updated': datetime.now().isoformat()}, f)
                
                # Generate order ID
                timestamp = int(time.time())
                order_id = f"ORD{timestamp}{counter:06d}"
                
                return order_id
                
        except Exception as e:
            print(f"Strategy 3 error: {e}")
            return None
    
    def strategy_4_hybrid(self):
        """Strategy 4: Hybrid approach combining multiple techniques"""
        try:
            # Component 1: Timestamp with microseconds
            timestamp = int(time.time() * 1000000)
            
            # Component 2: Process and thread info
            pid = os.getpid()
            tid = threading.get_ident()
            
            # Component 3: Secure random
            random_part = secrets.token_hex(3)
            
            # Component 4: Hash of combined components for uniqueness
            combined = f"{timestamp}{pid}{tid}{random_part}"
            hash_part = hashlib.md5(combined.encode()).hexdigest()[:6]
            
            order_id = f"ORD{timestamp}{hash_part.upper()}"
            
            return order_id
            
        except Exception as e:
            print(f"Strategy 4 error: {e}")
            return None
    
    def strategy_5_database_lock(self):
        """Strategy 5: Database-level locking with retry"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Use database-level locking
            cursor.execute('BEGIN EXCLUSIVE TRANSACTION')
            
            # Get last order ID from database
            cursor.execute('SELECT order_id FROM orders ORDER BY id DESC LIMIT 1')
            last_order = cursor.fetchone()
            
            if last_order:
                # Extract number from last order ID
                last_id = last_order[0]
                if last_id.startswith('ORD'):
                    try:
                        # Extract numeric part
                        numeric_part = ''.join(filter(str.isdigit, last_id))
                        if numeric_part:
                            next_num = int(numeric_part) + 1
                        else:
                            next_num = int(time.time())
                    except:
                        next_num = int(time.time())
                else:
                    next_num = int(time.time())
            else:
                next_num = int(time.time())
            
            # Generate new order ID
            order_id = f"ORD{next_num}"
            
            # Verify uniqueness
            cursor.execute('SELECT COUNT(*) FROM orders WHERE order_id = ?', (order_id,))
            if cursor.fetchone()[0] > 0:
                # If exists, add random suffix
                suffix = secrets.token_hex(3).upper()
                order_id = f"ORD{next_num}{suffix}"
            
            cursor.execute('COMMIT')
            conn.close()
            
            return order_id
            
        except Exception as e:
            print(f"Strategy 5 error: {e}")
            return None

def test_strategy(strategy_name, generator_func, num_tests=50):
    """Test a specific strategy for uniqueness and performance"""
    print(f"\n🧪 Testing {strategy_name}...")
    
    start_time = time.time()
    generated_ids = []
    errors = 0
    
    # Generate IDs
    for i in range(num_tests):
        try:
            order_id = generator_func()
            if order_id:
                generated_ids.append(order_id)
            else:
                errors += 1
        except Exception as e:
            print(f"  Error in test {i}: {e}")
            errors += 1
    
    end_time = time.time()
    
    # Check uniqueness
    unique_ids = set(generated_ids)
    duplicates = len(generated_ids) - len(unique_ids)
    
    # Results
    print(f"  ✅ Generated: {len(generated_ids)}/{num_tests}")
    print(f"  ✅ Unique: {len(unique_ids)}")
    print(f"  ❌ Duplicates: {duplicates}")
    print(f"  ❌ Errors: {errors}")
    print(f"  ⏱️  Time: {end_time - start_time:.3f}s")
    
    if len(generated_ids) > 0:
        print(f"  📝 Sample IDs: {generated_ids[:3]}")
    
    return {
        'strategy': strategy_name,
        'generated': len(generated_ids),
        'unique': len(unique_ids),
        'duplicates': duplicates,
        'errors': errors,
        'time': end_time - start_time,
        'success_rate': len(unique_ids) / num_tests * 100
    }

def test_concurrent_generation(strategy_name, generator_func, num_threads=10, orders_per_thread=5):
    """Test concurrent order ID generation"""
    print(f"\n⚡ Testing {strategy_name} - Concurrent Generation...")
    
    def generate_orders(thread_id):
        results = []
        for i in range(orders_per_thread):
            try:
                order_id = generator_func()
                if order_id:
                    results.append(order_id)
            except Exception as e:
                print(f"  Thread {thread_id} error: {e}")
        return results
    
    start_time = time.time()
    all_ids = []
    
    with ThreadPoolExecutor(max_workers=num_threads) as executor:
        futures = [executor.submit(generate_orders, i) for i in range(num_threads)]
        for future in futures:
            all_ids.extend(future.result())
    
    end_time = time.time()
    
    # Check results
    unique_ids = set(all_ids)
    duplicates = len(all_ids) - len(unique_ids)
    
    print(f"  ✅ Total Generated: {len(all_ids)}")
    print(f"  ✅ Unique: {len(unique_ids)}")
    print(f"  ❌ Duplicates: {duplicates}")
    print(f"  ⏱️  Time: {end_time - start_time:.3f}s")
    
    if duplicates > 0:
        print(f"  ⚠️  DUPLICATE IDs FOUND!")
        duplicate_ids = [id for id in all_ids if all_ids.count(id) > 1]
        print(f"  📝 Duplicates: {set(duplicate_ids)}")
    
    return duplicates == 0

def main():
    """Main testing function"""
    print("🔍 COMPREHENSIVE ORDER ID GENERATION TESTING")
    print("=" * 60)
    
    # Initialize generator
    generator = OrderIDGenerator()
    
    # Test all strategies
    strategies = [
        ("Strategy 1: Auto-Increment", generator.strategy_1_auto_increment),
        ("Strategy 2: UUID-Based", generator.strategy_2_uuid_based),
        ("Strategy 3: Atomic Counter", generator.strategy_3_atomic_counter),
        ("Strategy 4: Hybrid", generator.strategy_4_hybrid),
        ("Strategy 5: Database Lock", generator.strategy_5_database_lock),
    ]
    
    results = []
    
    # Test each strategy
    for strategy_name, strategy_func in strategies:
        result = test_strategy(strategy_name, strategy_func, 20)
        results.append(result)
        
        # Test concurrent generation
        concurrent_success = test_concurrent_generation(strategy_name, strategy_func, 5, 3)
        result['concurrent_success'] = concurrent_success
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 STRATEGY COMPARISON SUMMARY")
    print("=" * 60)
    
    for result in results:
        status = "✅ PASS" if result['duplicates'] == 0 and result['errors'] == 0 and result.get('concurrent_success', False) else "❌ FAIL"
        print(f"{result['strategy']:<30} | {status} | Success: {result['success_rate']:.1f}% | Time: {result['time']:.3f}s")
    
    # Find best strategy
    best_strategy = max(results, key=lambda x: (x['success_rate'], -x['time'], x.get('concurrent_success', False)))
    print(f"\n🏆 BEST STRATEGY: {best_strategy['strategy']}")
    
    return results

if __name__ == "__main__":
    main()
