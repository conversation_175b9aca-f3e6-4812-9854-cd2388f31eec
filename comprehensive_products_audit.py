#!/usr/bin/env python3
"""
Comprehensive Products Module Audit and Fix
"""

import sqlite3
import os
from datetime import datetime

def comprehensive_products_audit():
    """Perform comprehensive audit and fix of products module"""
    
    print("🔍 COMPREHENSIVE PRODUCTS MODULE AUDIT")
    print("=" * 60)
    
    try:
        # Connect to database
        db_path = 'instance/medivent.db'
        if not os.path.exists(db_path):
            print(f"❌ Database not found at {db_path}")
            return False
        
        db = sqlite3.connect(db_path)
        db.row_factory = sqlite3.Row
        cursor = db.cursor()
        
        # 1. Test database schema
        print("\n1️⃣ TESTING DATABASE SCHEMA:")
        print("-" * 40)
        
        # Check divisions table structure
        cursor.execute("PRAGMA table_info(divisions)")
        divisions_columns = [col[1] for col in cursor.fetchall()]
        print(f"   📋 Divisions columns: {', '.join(divisions_columns)}")
        
        has_manager_id = 'manager_id' in divisions_columns
        has_manager = 'manager' in divisions_columns
        print(f"   ✅ Has manager_id: {has_manager_id}")
        print(f"   ❌ Has manager: {has_manager}")
        
        # Check products table structure
        cursor.execute("PRAGMA table_info(products)")
        products_columns = [col[1] for col in cursor.fetchall()]
        print(f"   📋 Products columns: {', '.join(products_columns)}")
        
        # 2. Test the problematic query
        print("\n2️⃣ TESTING PROBLEMATIC QUERY:")
        print("-" * 40)
        
        try:
            # Test the fixed query
            cursor.execute('''
                SELECT p.*, d.name as division_name, d.category as division_category,
                       d.manager_id as division_manager_id
                FROM products p
                LEFT JOIN divisions d ON p.division_id = d.division_id
                WHERE p.product_id = ?
            ''', ('P001',))
            
            result = cursor.fetchone()
            if result:
                print("   ✅ Query executed successfully")
                print(f"   📋 Product found: {result['name'] if 'name' in result.keys() else 'N/A'}")
                print(f"   📋 Division: {result['division_name'] if 'division_name' in result.keys() else 'N/A'}")
            else:
                print("   ⚠️ No product P001 found")
        except Exception as e:
            print(f"   ❌ Query failed: {str(e)}")
        
        # 3. Test sqlite3.Row object handling
        print("\n3️⃣ TESTING SQLITE3.ROW OBJECT HANDLING:")
        print("-" * 40)
        
        cursor.execute("SELECT * FROM products LIMIT 1")
        product = cursor.fetchone()
        
        if product:
            try:
                # Test dict conversion
                product_dict = dict(product)
                print("   ✅ Dict conversion successful")
                
                # Test key access
                product_id = product_dict.get('product_id', 'N/A')
                product_name = product_dict.get('name', 'N/A')
                print(f"   ✅ Key access successful: {product_id} - {product_name}")
                
            except Exception as e:
                print(f"   ❌ Row object handling failed: {str(e)}")
        else:
            print("   ⚠️ No products found for testing")
        
        # 4. Test datetime handling
        print("\n4️⃣ TESTING DATETIME HANDLING:")
        print("-" * 40)
        
        if product:
            try:
                created_at = product_dict.get('created_at')
                if created_at:
                    # Test datetime conversion
                    dt_str = str(created_at)
                    if 'T' in dt_str:
                        dt_obj = datetime.fromisoformat(dt_str.replace('T', ' ').split('.')[0])
                    else:
                        dt_obj = datetime.strptime(dt_str.split('.')[0], '%Y-%m-%d %H:%M:%S')
                    
                    formatted_date = dt_obj.strftime('%Y-%m-%d %H:%M')
                    print(f"   ✅ Datetime conversion successful: {formatted_date}")
                else:
                    print("   ⚠️ No created_at field found")
            except Exception as e:
                print(f"   ❌ Datetime handling failed: {str(e)}")
        
        # 5. Create sample data if needed
        print("\n5️⃣ CREATING SAMPLE DATA:")
        print("-" * 40)
        
        cursor.execute("SELECT COUNT(*) as count FROM products")
        count = cursor.fetchone()['count']
        print(f"   📊 Current products: {count}")
        
        if count < 5:
            print("   🔄 Creating sample products...")
            create_sample_data(cursor, db)
        else:
            print("   ✅ Sufficient products exist")
        
        # 6. Final verification
        print("\n6️⃣ FINAL VERIFICATION:")
        print("-" * 40)
        
        # Test product listing
        cursor.execute("""
            SELECT p.product_id, p.name, d.name as division_name
            FROM products p
            LEFT JOIN divisions d ON p.division_id = d.division_id
            LIMIT 5
        """)
        
        products = cursor.fetchall()
        print(f"   📋 Sample products ({len(products)}):")
        for product in products:
            product_dict = dict(product)
            pid = product_dict.get('product_id', 'N/A')
            name = product_dict.get('name', 'N/A')
            division = product_dict.get('division_name', 'N/A')
            print(f"      • {pid}: {name} (Division: {division})")
        
        db.close()
        print("\n✅ COMPREHENSIVE AUDIT COMPLETE")
        return True
        
    except Exception as e:
        print(f"❌ Audit failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def create_sample_data(cursor, db):
    """Create minimal sample data for testing"""
    
    # Ensure we have a division
    cursor.execute("SELECT division_id FROM divisions WHERE is_active = 1 LIMIT 1")
    division = cursor.fetchone()
    
    if not division:
        cursor.execute("""
            INSERT OR REPLACE INTO divisions 
            (division_id, code, name, status, is_active)
            VALUES ('DIV001', 'TEST', 'Test Division', 'active', 1)
        """)
        division_id = 'DIV001'
    else:
        division_id = division['division_id']
    
    # Create sample products
    sample_products = [
        ('P001', 'Paracetamol 500mg', 'Test pain relief', 25.50),
        ('P002', 'Amoxicillin 250mg', 'Test antibiotic', 45.00),
        ('P003', 'Cough Syrup', 'Test cough medicine', 85.00),
    ]
    
    for product_id, name, description, price in sample_products:
        cursor.execute("""
            INSERT OR REPLACE INTO products 
            (product_id, name, description, unit_price, division_id, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, datetime('now'), datetime('now'))
        """, (product_id, name, description, price, division_id))
        print(f"      ✅ Created: {product_id} - {name}")
    
    db.commit()

if __name__ == "__main__":
    comprehensive_products_audit()
