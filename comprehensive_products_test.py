#!/usr/bin/env python3
"""
Comprehensive Products Module Test Suite
"""

import requests
import sqlite3
import os
import time
from datetime import datetime

class ProductsTestSuite:
    def __init__(self):
        self.base_url = "http://127.0.0.1:5001"
        self.db_path = 'instance/medivent.db'
        self.test_results = []
        
    def run_all_tests(self):
        """Run comprehensive test suite"""
        print("🧪 COMPREHENSIVE PRODUCTS MODULE TEST SUITE")
        print("=" * 70)
        
        # Test 1: Database Schema and Data
        self.test_database_schema()
        
        # Test 2: SQLite Row Object Handling
        self.test_sqlite_row_handling()
        
        # Test 3: DateTime Formatting
        self.test_datetime_formatting()
        
        # Test 4: Product Routes
        self.test_product_routes()
        
        # Test 5: CRUD Operations
        self.test_crud_operations()
        
        # Generate final report
        self.generate_report()
        
    def test_database_schema(self):
        """Test database schema and column issues"""
        print("\n1️⃣ TESTING DATABASE SCHEMA:")
        print("-" * 50)
        
        try:
            db = sqlite3.connect(self.db_path)
            db.row_factory = sqlite3.Row
            cursor = db.cursor()
            
            # Test divisions table columns
            cursor.execute("PRAGMA table_info(divisions)")
            divisions_columns = [col[1] for col in cursor.fetchall()]
            
            has_manager_id = 'manager_id' in divisions_columns
            has_manager = 'manager' in divisions_columns
            
            print(f"   📋 Divisions table columns: {len(divisions_columns)} found")
            print(f"   ✅ Has manager_id column: {has_manager_id}")
            print(f"   ❌ Has manager column: {has_manager}")
            
            # Test the problematic query
            try:
                cursor.execute('''
                    SELECT p.*, d.name as division_name, d.category as division_category,
                           d.manager_id as division_manager_id
                    FROM products p
                    LEFT JOIN divisions d ON p.division_id = d.division_id
                    LIMIT 1
                ''')
                result = cursor.fetchone()
                print("   ✅ Fixed query executes successfully")
                self.test_results.append(("Database Schema", "PASS", "Fixed d.manager -> d.manager_id"))
            except Exception as e:
                print(f"   ❌ Query still fails: {str(e)}")
                self.test_results.append(("Database Schema", "FAIL", str(e)))
            
            db.close()
            
        except Exception as e:
            print(f"   ❌ Database test failed: {str(e)}")
            self.test_results.append(("Database Schema", "FAIL", str(e)))
    
    def test_sqlite_row_handling(self):
        """Test SQLite Row object handling"""
        print("\n2️⃣ TESTING SQLITE ROW OBJECT HANDLING:")
        print("-" * 50)
        
        try:
            db = sqlite3.connect(self.db_path)
            db.row_factory = sqlite3.Row
            cursor = db.cursor()
            
            cursor.execute("SELECT * FROM products LIMIT 1")
            product = cursor.fetchone()
            
            if product:
                # Test dict conversion
                product_dict = dict(product)
                product_id = product_dict.get('product_id', 'N/A')
                product_name = product_dict.get('name', 'N/A')
                
                print(f"   ✅ Dict conversion successful")
                print(f"   ✅ Key access works: {product_id} - {product_name}")
                self.test_results.append(("SQLite Row Handling", "PASS", "Dict conversion works"))
            else:
                print("   ⚠️ No products found for testing")
                self.test_results.append(("SQLite Row Handling", "SKIP", "No products found"))
            
            db.close()
            
        except Exception as e:
            print(f"   ❌ SQLite Row test failed: {str(e)}")
            self.test_results.append(("SQLite Row Handling", "FAIL", str(e)))
    
    def test_datetime_formatting(self):
        """Test datetime formatting fixes"""
        print("\n3️⃣ TESTING DATETIME FORMATTING:")
        print("-" * 50)
        
        try:
            db = sqlite3.connect(self.db_path)
            db.row_factory = sqlite3.Row
            cursor = db.cursor()
            
            cursor.execute("SELECT created_at, updated_at FROM products WHERE created_at IS NOT NULL LIMIT 1")
            product = cursor.fetchone()
            
            if product:
                product_dict = dict(product)
                created_at = product_dict.get('created_at')
                
                if created_at:
                    # Test datetime conversion
                    dt_str = str(created_at)
                    if 'T' in dt_str:
                        dt_obj = datetime.fromisoformat(dt_str.replace('T', ' ').split('.')[0])
                    else:
                        dt_obj = datetime.strptime(dt_str.split('.')[0], '%Y-%m-%d %H:%M:%S')
                    
                    formatted_date = dt_obj.strftime('%Y-%m-%d %H:%M')
                    print(f"   ✅ Datetime conversion successful: {formatted_date}")
                    self.test_results.append(("DateTime Formatting", "PASS", "Conversion works"))
                else:
                    print("   ⚠️ No datetime fields found")
                    self.test_results.append(("DateTime Formatting", "SKIP", "No datetime data"))
            else:
                print("   ⚠️ No products with datetime found")
                self.test_results.append(("DateTime Formatting", "SKIP", "No products found"))
            
            db.close()
            
        except Exception as e:
            print(f"   ❌ DateTime test failed: {str(e)}")
            self.test_results.append(("DateTime Formatting", "FAIL", str(e)))
    
    def test_product_routes(self):
        """Test product routes accessibility"""
        print("\n4️⃣ TESTING PRODUCT ROUTES:")
        print("-" * 50)
        
        routes_to_test = [
            ("/products/", "Products Index"),
            ("/products/product_management/", "Product Management"),
            ("/products/new", "New Product"),
            ("/products/P001", "View Product P001"),
            ("/products/P003", "View Product P003"),
        ]
        
        for route, description in routes_to_test:
            try:
                response = requests.get(f"{self.base_url}{route}", timeout=5)
                if response.status_code == 200:
                    print(f"   ✅ {description}: HTTP 200")
                    self.test_results.append((f"Route {route}", "PASS", "HTTP 200"))
                elif response.status_code == 302:
                    print(f"   🔄 {description}: HTTP 302 (Redirect)")
                    self.test_results.append((f"Route {route}", "PASS", "HTTP 302 Redirect"))
                else:
                    print(f"   ❌ {description}: HTTP {response.status_code}")
                    self.test_results.append((f"Route {route}", "FAIL", f"HTTP {response.status_code}"))
            except Exception as e:
                print(f"   ❌ {description}: {str(e)}")
                self.test_results.append((f"Route {route}", "FAIL", str(e)))
            
            time.sleep(0.5)  # Rate limiting
    
    def test_crud_operations(self):
        """Test CRUD operations via database"""
        print("\n5️⃣ TESTING CRUD OPERATIONS:")
        print("-" * 50)
        
        try:
            db = sqlite3.connect(self.db_path)
            db.row_factory = sqlite3.Row
            cursor = db.cursor()
            
            # Test READ
            cursor.execute("SELECT COUNT(*) as count FROM products")
            count = cursor.fetchone()['count']
            print(f"   ✅ READ: Found {count} products")
            
            # Test CREATE (if needed)
            if count < 3:
                cursor.execute("""
                    INSERT OR REPLACE INTO products 
                    (product_id, name, description, unit_price, created_at)
                    VALUES ('TEST001', 'Test Product', 'Test Description', 99.99, datetime('now'))
                """)
                db.commit()
                print("   ✅ CREATE: Test product created")
            
            # Test UPDATE
            cursor.execute("""
                UPDATE products 
                SET description = 'Updated Test Description', updated_at = datetime('now')
                WHERE product_id = 'TEST001'
            """)
            db.commit()
            print("   ✅ UPDATE: Test product updated")
            
            # Test DELETE (cleanup)
            cursor.execute("DELETE FROM products WHERE product_id = 'TEST001'")
            db.commit()
            print("   ✅ DELETE: Test product deleted")
            
            self.test_results.append(("CRUD Operations", "PASS", "All operations work"))
            db.close()
            
        except Exception as e:
            print(f"   ❌ CRUD test failed: {str(e)}")
            self.test_results.append(("CRUD Operations", "FAIL", str(e)))
    
    def generate_report(self):
        """Generate final test report"""
        print("\n" + "=" * 70)
        print("📊 COMPREHENSIVE TEST RESULTS SUMMARY")
        print("=" * 70)
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r[1] == "PASS"])
        failed_tests = len([r for r in self.test_results if r[1] == "FAIL"])
        skipped_tests = len([r for r in self.test_results if r[1] == "SKIP"])
        
        print(f"📋 Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"⚠️ Skipped: {skipped_tests}")
        print(f"📈 Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        print("\n📋 DETAILED RESULTS:")
        print("-" * 50)
        for test_name, status, details in self.test_results:
            status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
            print(f"{status_icon} {test_name}: {status}")
            if details:
                print(f"   └─ {details}")
        
        if failed_tests == 0:
            print("\n🎉 ALL CRITICAL TESTS PASSED!")
            print("✅ Products module is working correctly")
        else:
            print(f"\n⚠️ {failed_tests} TESTS FAILED")
            print("❌ Some issues still need attention")

if __name__ == "__main__":
    test_suite = ProductsTestSuite()
    test_suite.run_all_tests()
