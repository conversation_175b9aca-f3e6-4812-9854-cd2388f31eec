#!/usr/bin/env python3
"""
Comprehensive test for all rider-related functionality
"""

import requests
import time

def test_all_rider_routes():
    """Test all rider routes comprehensively"""
    base_url = "http://127.0.0.1:5000"
    
    print("🧪 COMPREHENSIVE RIDER FUNCTIONALITY TEST")
    print("=" * 60)
    
    # All rider routes to test
    rider_routes = [
        ("/riders/", "Riders Main Page"),
        ("/riders/dashboard", "Riders Dashboard"),
        ("/riders/tracking", "Live Tracking"),
        ("/riders/performance", "Performance Analytics"),
        ("/riders/analytics", "Advanced Analytics"),
        ("/riders/reports", "Reports"),
        ("/riders/delivery-routes", "Delivery Routes"),
        ("/riders/assignment-dashboard", "Assignment Dashboard"),
        ("/riders/self-pickup", "Self Pickup"),
        ("/riders/register", "Register New Rider"),
        ("/riders/comprehensive-reports", "Comprehensive Reports"),
        ("/riders/modern-dashboard", "Modern Dashboard"),
        ("/riders/orders", "Rider Orders"),
        ("/riders/export", "Export Riders"),
        ("/riders/bulk-actions", "Bulk Actions"),
        ("/riders/notifications", "Notifications"),
        ("/riders/settings", "Settings"),
        ("/riders/profile", "Profile Management"),
        ("/riders/earnings", "Earnings Tracking")
    ]
    
    print(f"🎯 Testing {len(rider_routes)} rider routes...")
    print("-" * 60)
    
    passed = 0
    failed = 0
    
    for route, description in rider_routes:
        try:
            response = requests.get(f"{base_url}{route}", timeout=10)
            status = response.status_code
            
            if status == 200:
                print(f"✅ {route:<30} | {description:<25} | HTTP {status}")
                passed += 1
            elif status == 302:
                print(f"🔄 {route:<30} | {description:<25} | HTTP {status} (Redirect)")
                passed += 1  # Redirects are often expected (login required)
            elif status == 404:
                print(f"❌ {route:<30} | {description:<25} | HTTP {status} (Not Found)")
                failed += 1
            else:
                print(f"⚠️ {route:<30} | {description:<25} | HTTP {status}")
                failed += 1
                
        except requests.exceptions.ConnectionError:
            print(f"❌ {route:<30} | {description:<25} | Connection Error")
            failed += 1
        except requests.exceptions.Timeout:
            print(f"❌ {route:<30} | {description:<25} | Timeout")
            failed += 1
        except Exception as e:
            print(f"❌ {route:<30} | {description:<25} | Error: {e}")
            failed += 1
    
    print("-" * 60)
    print(f"📊 RESULTS: {passed} passed, {failed} failed")
    
    return passed, failed

def test_navigation_links():
    """Test navigation links work correctly"""
    base_url = "http://127.0.0.1:5000"
    
    print("\n🧭 TESTING NAVIGATION LINKS")
    print("=" * 60)
    
    try:
        # Test main page for rider navigation
        response = requests.get(base_url, timeout=10)
        
        if response.status_code == 200:
            content = response.text.lower()
            
            # Check for common navigation patterns
            nav_checks = [
                ("riders", "Riders navigation link"),
                ("dashboard", "Dashboard navigation"),
                ("tracking", "Tracking navigation"),
                ("performance", "Performance navigation"),
                ("analytics", "Analytics navigation"),
                ("reports", "Reports navigation")
            ]
            
            for pattern, description in nav_checks:
                if pattern in content:
                    print(f"✅ {description:<30} | Found in navigation")
                else:
                    print(f"⚠️ {description:<30} | Not found in navigation")
            
            # Check for BuildError
            if "builderror" in content:
                print("❌ BuildError found in main page!")
                return False
            else:
                print("✅ No BuildError found in main page")
                
        return True
        
    except Exception as e:
        print(f"❌ Navigation test error: {e}")
        return False

def test_template_rendering():
    """Test template rendering for errors"""
    base_url = "http://127.0.0.1:5000"
    
    print("\n🎨 TESTING TEMPLATE RENDERING")
    print("=" * 60)
    
    # Key pages to test for template errors
    test_pages = [
        ("/", "Main Page"),
        ("/riders/", "Riders Index"),
        ("/riders/dashboard", "Riders Dashboard"),
        ("/riders/tracking", "Live Tracking"),
        ("/riders/performance", "Performance Page")
    ]
    
    all_good = True
    
    for route, description in test_pages:
        try:
            response = requests.get(f"{base_url}{route}", timeout=10)
            
            if response.status_code == 200:
                content = response.text.lower()
                
                # Check for template errors
                error_patterns = [
                    "builderror",
                    "could not build url",
                    "templatenotfound",
                    "jinja2.exceptions",
                    "werkzeug.routing.builderror"
                ]
                
                errors_found = []
                for pattern in error_patterns:
                    if pattern in content:
                        errors_found.append(pattern)
                
                if errors_found:
                    print(f"❌ {description:<20} | Errors: {', '.join(errors_found)}")
                    all_good = False
                else:
                    print(f"✅ {description:<20} | No template errors")
            else:
                print(f"⚠️ {description:<20} | HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ {description:<20} | Exception: {e}")
            all_good = False
    
    return all_good

def main():
    """Run all tests"""
    print("🚀 STARTING COMPREHENSIVE RIDER SYSTEM TEST")
    print("=" * 60)
    
    # Wait for server
    print("⏳ Waiting for server to be ready...")
    time.sleep(2)
    
    # Run all tests
    passed_routes, failed_routes = test_all_rider_routes()
    nav_ok = test_navigation_links()
    template_ok = test_template_rendering()
    
    # Final summary
    print("\n🏁 FINAL TEST SUMMARY")
    print("=" * 60)
    print(f"📊 Route Tests: {passed_routes} passed, {failed_routes} failed")
    print(f"🧭 Navigation: {'✅ PASS' if nav_ok else '❌ FAIL'}")
    print(f"🎨 Templates: {'✅ PASS' if template_ok else '❌ FAIL'}")
    
    overall_success = (failed_routes == 0) and nav_ok and template_ok
    
    if overall_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Rider routing system is working perfectly!")
        print("✅ No BuildError exceptions found!")
        print("✅ All navigation links are functional!")
        print("✅ All templates render without errors!")
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("Please check the issues above.")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
