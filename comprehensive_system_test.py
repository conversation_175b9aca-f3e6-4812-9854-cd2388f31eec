#!/usr/bin/env python3
"""
Comprehensive System Testing Script
Tests all routes, database connections, and functionality
"""

import requests
import sqlite3
import os
import sys
from datetime import datetime

class SystemTester:
    def __init__(self, base_url="http://127.0.0.1:5001"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        self.db_path = 'instance/medivent.db'
        
    def log_result(self, test_name, status, message=""):
        """Log test result"""
        result = {
            'test': test_name,
            'status': status,
            'message': message,
            'timestamp': datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_icon} {test_name}: {message}")
    
    def test_database_connection(self):
        """Test database connectivity and basic queries"""
        print("\n🗄️ DATABASE CONNECTION TESTS")
        print("=" * 50)
        
        try:
            if not os.path.exists(self.db_path):
                self.log_result("Database File", "FAIL", f"Database file not found: {self.db_path}")
                return False
            
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # Test basic tables
            tables = ['products', 'inventory', 'orders', 'users', 'stock_movements']
            for table in tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    self.log_result(f"Table {table}", "PASS", f"{count} records")
                except Exception as e:
                    self.log_result(f"Table {table}", "FAIL", str(e))
            
            # Test stock_movements schema
            try:
                cursor.execute("PRAGMA table_info(stock_movements)")
                columns = [col[1] for col in cursor.fetchall()]
                required_cols = ['from_warehouse_id', 'to_warehouse_id', 'movement_date']
                missing_cols = [col for col in required_cols if col not in columns]
                
                if missing_cols:
                    self.log_result("Stock Movements Schema", "FAIL", f"Missing columns: {missing_cols}")
                else:
                    self.log_result("Stock Movements Schema", "PASS", "All required columns present")
            except Exception as e:
                self.log_result("Stock Movements Schema", "FAIL", str(e))
            
            conn.close()
            return True
            
        except Exception as e:
            self.log_result("Database Connection", "FAIL", str(e))
            return False
    
    def test_critical_routes(self):
        """Test critical application routes"""
        print("\n🌐 ROUTE TESTING")
        print("=" * 50)
        
        # Critical routes to test
        routes = [
            ('/', 'Dashboard'),
            ('/products/product_management/', 'Product Management'),
            ('/products/update_selection', 'Product Update Selection'),
            ('/inventory/', 'Inventory Dashboard'),
            ('/orders/', 'Orders Dashboard'),
            ('/orders/new', 'New Order Form'),
            ('/products/new', 'New Product Form'),
        ]
        
        for route, name in routes:
            try:
                url = f"{self.base_url}{route}"
                response = self.session.get(url, timeout=10)
                
                if response.status_code == 200:
                    self.log_result(f"Route {name}", "PASS", f"HTTP {response.status_code}")
                elif response.status_code == 302:
                    self.log_result(f"Route {name}", "WARN", f"HTTP {response.status_code} (Redirect)")
                else:
                    self.log_result(f"Route {name}", "FAIL", f"HTTP {response.status_code}")
                    
            except requests.exceptions.ConnectionError:
                self.log_result(f"Route {name}", "FAIL", "Connection refused - Flask app not running")
            except Exception as e:
                self.log_result(f"Route {name}", "FAIL", str(e))
    
    def test_product_functionality(self):
        """Test product-related functionality"""
        print("\n📦 PRODUCT FUNCTIONALITY TESTS")
        print("=" * 50)
        
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # Test product status functionality
            cursor.execute("SELECT product_id, name, status FROM products LIMIT 1")
            product = cursor.fetchone()
            
            if product:
                product_id = product['product_id']
                current_status = product['status'] if 'status' in product.keys() else 'active'
                
                self.log_result("Product Status Check", "PASS", f"Product {product_id} status: {current_status}")
                
                # Test product activation/deactivation routes exist
                activation_routes = [
                    f'/products/activate/{product_id}',
                    f'/products/deactivate/{product_id}'
                ]
                
                for route in activation_routes:
                    # We won't actually call these (POST methods) but check they exist
                    self.log_result(f"Route {route}", "INFO", "Route defined (not tested - POST method)")
            else:
                self.log_result("Product Status Check", "FAIL", "No products found in database")
            
            conn.close()
            
        except Exception as e:
            self.log_result("Product Functionality", "FAIL", str(e))
    
    def test_inventory_functionality(self):
        """Test inventory-related functionality"""
        print("\n📊 INVENTORY FUNCTIONALITY TESTS")
        print("=" * 50)
        
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # Test inventory aggregation query
            cursor.execute("""
                SELECT p.product_id, p.name,
                       COUNT(i.inventory_id) as batch_count,
                       COALESCE(SUM(i.stock_quantity), 0) as total_stock,
                       COALESCE(SUM(i.stock_quantity - i.allocated_quantity), 0) as available_stock
                FROM products p
                LEFT JOIN inventory i ON p.product_id = i.product_id AND i.status = 'active'
                GROUP BY p.product_id, p.name
                LIMIT 1
            """)
            
            result = cursor.fetchone()
            if result:
                self.log_result("Inventory Aggregation", "PASS", 
                              f"Product: {result['name']}, Batches: {result['batch_count']}, Available: {result['available_stock']}")
            else:
                self.log_result("Inventory Aggregation", "FAIL", "No inventory data found")
            
            # Test stock movements query
            cursor.execute("""
                SELECT COUNT(*) as movement_count
                FROM stock_movements m
                WHERE m.from_warehouse_id IS NOT NULL
            """)
            
            movement_count = cursor.fetchone()[0]
            self.log_result("Stock Movements Query", "PASS", f"{movement_count} movements with warehouse data")
            
            conn.close()
            
        except Exception as e:
            self.log_result("Inventory Functionality", "FAIL", str(e))
    
    def generate_report(self):
        """Generate comprehensive test report"""
        print("\n📋 TEST SUMMARY REPORT")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed_tests = len([r for r in self.test_results if r['status'] == 'FAIL'])
        warning_tests = len([r for r in self.test_results if r['status'] == 'WARN'])
        
        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"⚠️ Warnings: {warning_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ FAILED TESTS:")
            for result in self.test_results:
                if result['status'] == 'FAIL':
                    print(f"   - {result['test']}: {result['message']}")
        
        if warning_tests > 0:
            print("\n⚠️ WARNINGS:")
            for result in self.test_results:
                if result['status'] == 'WARN':
                    print(f"   - {result['test']}: {result['message']}")
        
        return failed_tests == 0

def main():
    """Run comprehensive system tests"""
    print("🧪 COMPREHENSIVE SYSTEM TESTING")
    print("=" * 60)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tester = SystemTester()
    
    # Run all test suites
    tester.test_database_connection()
    tester.test_critical_routes()
    tester.test_product_functionality()
    tester.test_inventory_functionality()
    
    # Generate final report
    success = tester.generate_report()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 ALL TESTS PASSED! System is functioning correctly.")
    else:
        print("⚠️ Some tests failed. Please review the issues above.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
