#!/usr/bin/env python3
"""
Comprehensive Testing and Verification Script
Tests all routes, database operations, and form submissions
"""

import requests
import sqlite3
import os
import json
from datetime import datetime, timedelta

def test_database_fixes():
    """Test database fixes"""
    print("=" * 80)
    print("TESTING DATABASE FIXES")
    print("=" * 80)
    
    db_path = 'instance/medivent.db'
    if not os.path.exists(db_path):
        print("❌ Database not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        print("1. Testing Expected Delivery Date Field")
        print("-" * 50)
        
        # Check if field exists
        cursor.execute('PRAGMA table_info(orders)')
        columns = cursor.fetchall()
        delivery_field_exists = any('estimated_delivery_date' in col[1] for col in columns)
        
        print(f"Expected delivery date field exists: {'✅' if delivery_field_exists else '❌'}")
        
        # Check recent orders have delivery dates
        cursor.execute('''
            SELECT order_id, estimated_delivery_date 
            FROM orders 
            ORDER BY order_date DESC 
            LIMIT 5
        ''')
        recent_orders = cursor.fetchall()
        
        orders_with_dates = sum(1 for order in recent_orders if order['estimated_delivery_date'])
        print(f"Recent orders with delivery dates: {orders_with_dates}/{len(recent_orders)}")
        
        print("\n2. Testing Inventory Consistency")
        print("-" * 50)
        
        # Check inventory consistency
        cursor.execute('''
            SELECT p.product_id, p.stock_quantity,
                   COALESCE(SUM(i.stock_quantity - i.allocated_quantity), 0) as inventory_available
            FROM products p
            LEFT JOIN inventory i ON p.product_id = i.product_id AND i.status = 'active'
            WHERE p.stock_quantity > 0
            GROUP BY p.product_id, p.stock_quantity
            LIMIT 10
        ''')
        stock_comparison = cursor.fetchall()
        
        conflicts = 0
        for item in stock_comparison:
            if item['stock_quantity'] != item['inventory_available']:
                conflicts += 1
        
        print(f"Stock conflicts: {conflicts}/{len(stock_comparison)}")
        
        conn.close()
        return delivery_field_exists and conflicts == 0
        
    except Exception as e:
        print(f"❌ Database test failed: {str(e)}")
        return False

def test_flask_routes():
    """Test Flask application routes"""
    print("\n3. Testing Flask Routes")
    print("-" * 50)
    
    base_url = "http://127.0.0.1:5001"
    
    # Test routes
    routes_to_test = [
        ("/", "Dashboard"),
        ("/orders", "Orders List"),
        ("/orders/new", "New Order Form"),
        ("/inventory", "Inventory"),
        ("/products", "Products")
    ]
    
    session = requests.Session()
    
    # First try to login
    try:
        login_response = session.post(f"{base_url}/auth/login", data={
            'username': 'admin',
            'password': 'admin123'
        }, timeout=10)
        
        if login_response.status_code == 200:
            print("✅ Login successful")
        else:
            print("❌ Login failed, testing without authentication")
    except:
        print("❌ Could not connect to Flask app")
        return False
    
    # Test routes
    route_results = []
    for route, name in routes_to_test:
        try:
            response = session.get(f"{base_url}{route}", timeout=10)
            status = "✅" if response.status_code == 200 else "❌"
            print(f"{status} {name}: HTTP {response.status_code}")
            route_results.append(response.status_code == 200)
        except Exception as e:
            print(f"❌ {name}: Connection failed - {str(e)}")
            route_results.append(False)
    
    return all(route_results)

def test_order_creation():
    """Test order creation with expected delivery date"""
    print("\n4. Testing Order Creation")
    print("-" * 50)
    
    base_url = "http://127.0.0.1:5001"
    session = requests.Session()
    
    try:
        # Login first
        login_response = session.post(f"{base_url}/auth/login", data={
            'username': 'admin',
            'password': 'admin123'
        }, timeout=10)
        
        if login_response.status_code != 200:
            print("❌ Could not login for order creation test")
            return False
        
        # Get new order form to extract CSRF token
        form_response = session.get(f"{base_url}/orders/new", timeout=10)
        if form_response.status_code != 200:
            print("❌ Could not access new order form")
            return False
        
        print("✅ Accessed new order form")
        
        # Test order creation with delivery date
        tomorrow = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
        
        order_data = {
            'customer_name': 'Test Customer Delivery Date',
            'customer_address': 'Test Address',
            'customer_phone': '555-TEST-DELIVERY',
            'payment_mode': 'cash',
            'delivery_date': tomorrow,  # This is the key field we're testing
            'product_id[]': ['P001'],
            'quantity[]': ['1'],
            'foc_quantity[]': ['0']
        }
        
        # Submit order
        create_response = session.post(f"{base_url}/orders/new", data=order_data, timeout=10)
        
        if create_response.status_code in [200, 302]:  # 302 for redirect after success
            print("✅ Order creation request successful")
            
            # Verify order was created with delivery date
            db_path = 'instance/medivent.db'
            if os.path.exists(db_path):
                conn = sqlite3.connect(db_path)
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT order_id, customer_name, estimated_delivery_date
                    FROM orders 
                    WHERE customer_name = 'Test Customer Delivery Date'
                    ORDER BY order_date DESC
                    LIMIT 1
                ''')
                test_order = cursor.fetchone()
                
                if test_order and test_order['estimated_delivery_date']:
                    print(f"✅ Order created with delivery date: {test_order['estimated_delivery_date']}")
                    conn.close()
                    return True
                else:
                    print("❌ Order created but delivery date not saved")
                    conn.close()
                    return False
            else:
                print("❌ Could not verify order in database")
                return False
        else:
            print(f"❌ Order creation failed: HTTP {create_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Order creation test failed: {str(e)}")
        return False

def run_comprehensive_tests():
    """Run all tests"""
    print("COMPREHENSIVE TESTING AND VERIFICATION")
    print("=" * 80)
    print(f"Test Date: {datetime.now()}")
    print()
    
    # Run tests
    test_results = []
    
    # Test 1: Database fixes
    test_results.append(test_database_fixes())
    
    # Test 2: Flask routes
    test_results.append(test_flask_routes())
    
    # Test 3: Order creation
    test_results.append(test_order_creation())
    
    # Summary
    print("\n" + "=" * 80)
    print("TEST SUMMARY")
    print("=" * 80)
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        return True
    else:
        print("❌ Some tests failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    run_comprehensive_tests()
