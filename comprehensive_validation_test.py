#!/usr/bin/env python3
"""
Comprehensive validation test for all fixes
"""

import requests
import json
import time
import sys

def test_api_endpoints():
    """Test API endpoints"""
    print("🔍 TESTING API ENDPOINTS")
    print("=" * 40)
    
    base_url = "http://127.0.0.1:5001"
    
    try:
        # Test order details API
        print("1. Testing Order Details API...")
        response = requests.get(f"{base_url}/api/order-details/ORD00000155", timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                if data.get('success'):
                    print("   ✅ Order Details API working")
                    order = data.get('order', {})
                    print(f"   Customer: {order.get('customer_name')}")
                    print(f"   Amount: {order.get('order_amount')}")
                    return True
                else:
                    print(f"   ❌ API returned error: {data.get('message')}")
            except json.JSONDecodeError:
                print(f"   ❌ Invalid JSON response")
                print(f"   Raw response: {response.text[:100]}")
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            print(f"   Response: {response.text[:100]}")
        
        return False
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_qr_code_api():
    """Test QR code API"""
    print("\n2. Testing QR Code API...")
    
    base_url = "http://127.0.0.1:5001"
    
    try:
        response = requests.get(f"{base_url}/api/order-qr-code/ORD00000155", timeout=15)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                if data.get('success'):
                    qr_info = data.get('qr_code', {})
                    base64_length = len(qr_info.get('base64', ''))
                    print(f"   ✅ QR Code API working - Base64 length: {base64_length}")
                    return True
                else:
                    print(f"   ❌ QR API returned error: {data.get('message')}")
            except json.JSONDecodeError:
                print(f"   ❌ Invalid JSON response")
                print(f"   Raw response: {response.text[:100]}")
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            print(f"   Response: {response.text[:100]}")
        
        return False
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_warehouse_page():
    """Test warehouse packing page"""
    print("\n3. Testing Warehouse Packing Page...")
    
    base_url = "http://127.0.0.1:5001"
    
    try:
        response = requests.get(f"{base_url}/warehouse/packing", timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Warehouse packing page accessible")
            
            # Check if enhanced modal JavaScript is present
            if 'enhanced_modal.js' in response.text:
                print("   ✅ Enhanced modal JavaScript included")
            else:
                print("   ⚠️ Enhanced modal JavaScript not found")
            
            return True
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
        
        return False
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_address_label():
    """Test address label page"""
    print("\n4. Testing Address Label Page...")
    
    base_url = "http://127.0.0.1:5001"
    
    try:
        response = requests.get(f"{base_url}/orders/ORD00000155/print-address", timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Address label page accessible")
            
            # Check if QR code loading JavaScript is present
            if 'loadQRCode' in response.text:
                print("   ✅ QR code loading JavaScript included")
            else:
                print("   ⚠️ QR code loading JavaScript not found")
            
            return True
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
        
        return False
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_server_status():
    """Test if server is running"""
    print("🔍 TESTING SERVER STATUS")
    print("=" * 40)
    
    base_url = "http://127.0.0.1:5001"
    
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"Server Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Server is running")
            return True
        else:
            print("❌ Server returned error")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Server not running")
        return False
    except Exception as e:
        print(f"❌ Server error: {e}")
        return False

def main():
    """Main validation function"""
    print("🧪 COMPREHENSIVE VALIDATION TEST")
    print("=" * 60)
    print("Testing both critical fixes:")
    print("1. 'Unable to Load Order Details' error in enhanced modal")
    print("2. 'QR Code unavailable' error in address print labels")
    print("=" * 60)
    
    # Test server status
    server_ok = test_server_status()
    if not server_ok:
        print("\n❌ Server is not running. Please start the Flask application first.")
        return False
    
    # Test API endpoints
    api_ok = test_api_endpoints()
    qr_ok = test_qr_code_api()
    
    # Test web pages
    warehouse_ok = test_warehouse_page()
    address_ok = test_address_label()
    
    print("\n📊 COMPREHENSIVE VALIDATION SUMMARY:")
    print("=" * 50)
    print(f"Server Status:           {'✅ OK' if server_ok else '❌ Failed'}")
    print(f"Order Details API:       {'✅ OK' if api_ok else '❌ Failed'}")
    print(f"QR Code API:             {'✅ OK' if qr_ok else '❌ Failed'}")
    print(f"Warehouse Packing Page:  {'✅ OK' if warehouse_ok else '❌ Failed'}")
    print(f"Address Label Page:      {'✅ OK' if address_ok else '❌ Failed'}")
    
    # Overall status
    all_ok = server_ok and api_ok and qr_ok and warehouse_ok and address_ok
    
    print("\n🎯 CRITICAL ISSUES STATUS:")
    print("=" * 50)
    if api_ok:
        print("✅ FIXED: 'Unable to Load Order Details' error")
        print("   Enhanced modal can now load order information")
    else:
        print("❌ NOT FIXED: 'Unable to Load Order Details' error")
    
    if qr_ok:
        print("✅ FIXED: 'QR Code unavailable' error")
        print("   Address labels can now display QR codes")
    else:
        print("❌ NOT FIXED: 'QR Code unavailable' error")
    
    if all_ok:
        print("\n🎉 ALL TESTS PASSED!")
        print("Both critical issues have been resolved successfully.")
        print("\nNext steps:")
        print("1. Test the enhanced modal in the browser")
        print("2. Test QR code generation on address labels")
        print("3. Verify end-to-end workflow")
    else:
        print("\n⚠️ SOME TESTS FAILED")
        print("Check the output above for specific issues.")
    
    return all_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
