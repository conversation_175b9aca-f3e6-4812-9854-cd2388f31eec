@echo off
REM ============================================================================
REM Flask ERP Firewall Configuration Script
REM Automatically configures Windows Firewall for network access
REM ============================================================================

echo.
echo ============================================================================
echo 🔥 Flask ERP Firewall Configuration Script
echo ============================================================================
echo.
echo This script will configure Windows Firewall to allow Flask ERP network access
echo Ports to configure: 5000 (primary) and 8080 (fallback)
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Administrator privileges confirmed
    echo.
) else (
    echo ❌ ERROR: This script must be run as Administrator
    echo.
    echo Please:
    echo 1. Right-click on this file
    echo 2. Select "Run as administrator"
    echo 3. Click "Yes" when prompted
    echo.
    pause
    exit /b 1
)

echo 🔍 Checking current firewall status...
netsh advfirewall show allprofiles state | findstr "State"
echo.

echo 📋 Current Flask ERP firewall rules (if any):
netsh advfirewall firewall show rule name="Flask ERP Port 5000" >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Flask ERP Port 5000 rule exists
    netsh advfirewall firewall show rule name="Flask ERP Port 5000"
) else (
    echo ⚠️ Flask ERP Port 5000 rule does not exist
)

netsh advfirewall firewall show rule name="Flask ERP Port 8080" >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Flask ERP Port 8080 rule exists
    netsh advfirewall firewall show rule name="Flask ERP Port 8080"
) else (
    echo ⚠️ Flask ERP Port 8080 rule does not exist
)
echo.

echo 🔧 Configuring firewall rules...
echo.

REM Remove existing rules if they exist
echo 🗑️ Removing any existing Flask ERP firewall rules...
netsh advfirewall firewall delete rule name="Flask ERP Port 5000" >nul 2>&1
netsh advfirewall firewall delete rule name="Flask ERP Port 8080" >nul 2>&1
echo ✅ Existing rules removed (if any)
echo.

REM Add inbound rule for port 5000
echo 📥 Adding inbound rule for port 5000...
netsh advfirewall firewall add rule name="Flask ERP Port 5000" dir=in action=allow protocol=TCP localport=5000 profile=any
if %errorLevel% == 0 (
    echo ✅ Port 5000 inbound rule added successfully
) else (
    echo ❌ Failed to add port 5000 inbound rule
)

REM Add outbound rule for port 5000
echo 📤 Adding outbound rule for port 5000...
netsh advfirewall firewall add rule name="Flask ERP Port 5000 Out" dir=out action=allow protocol=TCP localport=5000 profile=any
if %errorLevel% == 0 (
    echo ✅ Port 5000 outbound rule added successfully
) else (
    echo ❌ Failed to add port 5000 outbound rule
)

REM Add inbound rule for port 8080
echo 📥 Adding inbound rule for port 8080...
netsh advfirewall firewall add rule name="Flask ERP Port 8080" dir=in action=allow protocol=TCP localport=8080 profile=any
if %errorLevel% == 0 (
    echo ✅ Port 8080 inbound rule added successfully
) else (
    echo ❌ Failed to add port 8080 inbound rule
)

REM Add outbound rule for port 8080
echo 📤 Adding outbound rule for port 8080...
netsh advfirewall firewall add rule name="Flask ERP Port 8080 Out" dir=out action=allow protocol=TCP localport=8080 profile=any
if %errorLevel% == 0 (
    echo ✅ Port 8080 outbound rule added successfully
) else (
    echo ❌ Failed to add port 8080 outbound rule
)

echo.
echo 🔍 Verifying firewall configuration...
echo.

REM Verify rules were created
echo 📋 Verifying Flask ERP firewall rules:
netsh advfirewall firewall show rule name="Flask ERP Port 5000" | findstr "Rule Name\|Enabled\|Direction\|Action\|Protocol\|Local Port"
echo.
netsh advfirewall firewall show rule name="Flask ERP Port 8080" | findstr "Rule Name\|Enabled\|Direction\|Action\|Protocol\|Local Port"
echo.

REM Get local IP address for reference
echo 🌐 Network Information:
echo.
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4 Address"') do (
    set "ip=%%a"
    setlocal enabledelayedexpansion
    set "ip=!ip: =!"
    echo 📍 Your Local IP Address: !ip!
    echo 🌍 Network Access URLs:
    echo    Primary:  http://!ip!:5000
    echo    Fallback: http://!ip!:8080
    endlocal
    goto :found_ip
)
:found_ip

echo.
echo ============================================================================
echo ✅ Firewall Configuration Complete!
echo ============================================================================
echo.
echo 🎯 What was configured:
echo    ✅ Inbound rule for port 5000 (Flask ERP primary port)
echo    ✅ Outbound rule for port 5000
echo    ✅ Inbound rule for port 8080 (Flask ERP fallback port)
echo    ✅ Outbound rule for port 8080
echo    ✅ Rules apply to all network profiles (Domain, Private, Public)
echo.
echo 📱 Next Steps:
echo    1. Start your Flask ERP application: python app.py
echo    2. Test local access: http://localhost:5000
echo    3. Test network access from other devices using the URLs above
echo    4. Login with: username=admin, password=admin123
echo.
echo 🔒 Security Notes:
echo    - These rules allow Flask ERP access from your local network
echo    - Only use on trusted networks (home/office WiFi)
echo    - Consider disabling rules when not needed for extra security
echo.
echo 🆘 Troubleshooting:
echo    - If connection still fails, check that both devices are on same WiFi
echo    - Verify Flask app is running with: python app.py
echo    - Test with ping: ping [YOUR_IP_ADDRESS]
echo.

REM Optional: Test port connectivity
echo 🧪 Would you like to test port connectivity? (y/n)
set /p test_ports="Enter choice: "
if /i "%test_ports%"=="y" (
    echo.
    echo 🔍 Testing port 5000...
    netstat -an | findstr :5000
    if %errorLevel% == 0 (
        echo ✅ Port 5000 is active
    ) else (
        echo ⚠️ Port 5000 is not active (Flask app may not be running)
    )
    
    echo 🔍 Testing port 8080...
    netstat -an | findstr :8080
    if %errorLevel% == 0 (
        echo ✅ Port 8080 is active
    ) else (
        echo ⚠️ Port 8080 is not active (normal if Flask app is running on 5000)
    )
)

echo.
echo 🎉 Configuration complete! Your Flask ERP is now ready for network access.
echo.
pause
