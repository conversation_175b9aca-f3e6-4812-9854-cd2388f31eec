#!/usr/bin/env python3
"""
Create DC Pending Quantities Table
This script creates the table to track pending quantities for partial DC generation
"""

import sqlite3
import os

def create_dc_pending_table():
    """Create the dc_pending_quantities table"""
    try:
        # Connect to database
        db_path = 'instance/medivent.db'
        if not os.path.exists(db_path):
            print(f"❌ Database not found at {db_path}")
            return False
            
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔧 Creating dc_pending_quantities table...")
        
        # Create the table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS dc_pending_quantities (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id TEXT NOT NULL,
                product_id TEXT NOT NULL,
                pending_quantity REAL NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status TEXT DEFAULT 'pending',
                notes TEXT,
                FOREIGN KEY (order_id) REFERENCES orders(order_id),
                FOREIGN KEY (product_id) REFERENCES products(product_id),
                UNIQUE(order_id, product_id)
            )
        ''')
        
        # Create indexes for better performance
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_dc_pending_order_id 
            ON dc_pending_quantities(order_id)
        ''')
        
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_dc_pending_product_id 
            ON dc_pending_quantities(product_id)
        ''')
        
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_dc_pending_status 
            ON dc_pending_quantities(status)
        ''')
        
        conn.commit()
        print("✅ dc_pending_quantities table created successfully!")
        
        # Verify table creation
        cursor.execute('''
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='dc_pending_quantities'
        ''')
        
        if cursor.fetchone():
            print("✅ Table verification successful!")
            
            # Show table schema
            cursor.execute('PRAGMA table_info(dc_pending_quantities)')
            columns = cursor.fetchall()
            print("\n📋 Table Schema:")
            for col in columns:
                print(f"   {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'}")
                
        else:
            print("❌ Table verification failed!")
            return False
            
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error creating table: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Creating DC Pending Quantities Table...")
    success = create_dc_pending_table()
    
    if success:
        print("\n🎉 DC Pending Quantities table setup completed successfully!")
        print("\n📝 Next Steps:")
        print("   1. Test partial DC generation functionality")
        print("   2. Verify pending quantities are tracked correctly")
        print("   3. Create UI for viewing pending quantities")
    else:
        print("\n❌ Setup failed. Please check the errors above.")
