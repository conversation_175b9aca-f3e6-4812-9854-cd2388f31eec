-- Create delivery_challans table if it doesn't exist
CREATE TABLE IF NOT EXISTS delivery_challans (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    dc_number TEXT UNIQUE NOT NULL,
    order_id TEXT NOT NULL,
    warehouse_id TEXT,
    customer_name TEXT,
    customer_address TEXT,
    status TEXT DEFAULT 'created',
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    dispatch_date TIMESTAMP,
    delivery_date TIMESTAMP,
    created_by TEXT,
    total_items INTEGER DEFAULT 0,
    total_amount REAL DEFAULT 0,
    batch_details TEXT,
    pdf_path TEXT,
    notes TEXT,
    FOREIGN KEY (order_id) REFERENCES orders(order_id),
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(warehouse_id)
);

-- Check if table was created
SELECT 'delivery_challans table created successfully' as result;
