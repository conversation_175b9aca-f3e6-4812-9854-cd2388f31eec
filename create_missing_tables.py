#!/usr/bin/env python3
"""
Create missing database tables for rider assignment functionality
"""

import sqlite3
import os

def create_missing_tables():
    """Create missing tables in the database"""
    
    # Use the same database path as the app
    db_path = 'instance/medivent.db'
    
    if not os.path.exists(db_path):
        print(f"❌ Database not found at {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔧 Creating missing tables...")
        
        # Create rider_assignments table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS rider_assignments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id TEXT NOT NULL,
                rider_id TEXT NOT NULL,
                assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                assigned_by TEXT,
                pickup_time TIMESTAMP,
                delivery_time TIMESTAMP,
                assignment_status TEXT DEFAULT 'assigned',
                delivery_proof TEXT,
                customer_signature TEXT,
                delivery_photo TEXT,
                otp_verified BOOLEAN DEFAULT FALSE,
                delivery_notes TEXT,
                FOREIGN KEY (order_id) REFERENCES orders (order_id),
                FOREIGN KEY (rider_id) REFERENCES riders (rider_id)
            )
        ''')
        print("✅ Created rider_assignments table")
        
        # Create order_status_history table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS order_status_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id TEXT NOT NULL,
                status TEXT NOT NULL,
                previous_status TEXT,
                changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                changed_by TEXT,
                notes TEXT,
                FOREIGN KEY (order_id) REFERENCES orders (order_id)
            )
        ''')
        print("✅ Created order_status_history table")
        
        # Create delivery_routes table if missing
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS delivery_routes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                rider_id TEXT NOT NULL,
                route_date DATE DEFAULT CURRENT_DATE,
                order_ids TEXT,
                route_sequence TEXT,
                estimated_time INTEGER,
                actual_time INTEGER,
                route_status TEXT DEFAULT 'planned',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (rider_id) REFERENCES riders (rider_id)
            )
        ''')
        print("✅ Created delivery_routes table")
        
        # Verify riders table has required columns
        cursor.execute("PRAGMA table_info(riders)")
        columns = [row[1] for row in cursor.fetchall()]
        
        required_columns = {
            'is_available': 'INTEGER DEFAULT 1',
            'city': 'TEXT DEFAULT "Karachi"',
            'rating': 'REAL DEFAULT 5.0',
            'total_deliveries': 'INTEGER DEFAULT 0',
            'successful_deliveries': 'INTEGER DEFAULT 0'
        }
        
        for col_name, col_def in required_columns.items():
            if col_name not in columns:
                cursor.execute(f'ALTER TABLE riders ADD COLUMN {col_name} {col_def}')
                print(f"✅ Added {col_name} column to riders table")
        
        conn.commit()
        conn.close()
        
        print("🎉 All missing tables and columns created successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error creating tables: {e}")
        return False

if __name__ == "__main__":
    create_missing_tables()
