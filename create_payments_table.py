#!/usr/bin/env python3
"""
Create the missing payments table in the database
"""

def create_payments_table():
    """Create payments table with proper schema"""
    print("🏗️ CREATING PAYMENTS TABLE")
    print("=" * 60)
    
    try:
        import sqlite3
        db = sqlite3.connect('instance/medivent.db')
        db.row_factory = sqlite3.Row
        
        # Create payments table based on the schema documentation
        create_table_sql = '''
        CREATE TABLE payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            payment_id TEXT UNIQUE NOT NULL,
            invoice_id TEXT,
            order_id TEXT,
            customer_id TEXT NOT NULL,
            amount DECIMAL(15,2) NOT NULL DEFAULT 0.00,
            payment_date DATE DEFAULT CURRENT_DATE,
            payment_method TEXT DEFAULT 'Cash',
            reference_number TEXT,
            status TEXT DEFAULT 'Completed',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by TEXT,
            division_id TEXT,
            bank_details TEXT,
            FOREIGN KEY (invoice_id) REFERENCES invoices(invoice_id),
            FOREIGN KEY (order_id) REFERENCES orders(order_id),
            FOREIGN KEY (customer_id) REFERENCES customers(customer_id),
            FOREIGN KEY (division_id) REFERENCES divisions(division_id)
        );
        '''
        
        print("📋 Creating payments table...")
        db.execute(create_table_sql)
        
        # Create indexes for better performance
        print("📊 Creating indexes...")
        
        indexes = [
            "CREATE INDEX idx_payments_payment_id ON payments(payment_id);",
            "CREATE INDEX idx_payments_order_id ON payments(order_id);",
            "CREATE INDEX idx_payments_customer_id ON payments(customer_id);",
            "CREATE INDEX idx_payments_payment_date ON payments(payment_date);",
            "CREATE INDEX idx_payments_status ON payments(status);"
        ]
        
        for index_sql in indexes:
            db.execute(index_sql)
        
        # Insert some sample data based on existing orders
        print("💰 Adding sample payment data...")
        
        # Get some orders to create sample payments
        orders = db.execute('''
            SELECT order_id, customer_name, order_amount, order_date
            FROM orders 
            WHERE status IN ('Delivered', 'Completed')
            ORDER BY order_date DESC
            LIMIT 10
        ''').fetchall()
        
        sample_payments = []
        for i, order in enumerate(orders, 1):
            payment_id = f"PAY{order['order_id'][-6:]}"
            sample_payments.append((
                payment_id,
                None,  # invoice_id
                order['order_id'],
                order['customer_name'],  # Using customer_name as customer_id for now
                order['order_amount'],
                order['order_date'],
                'Cash' if i % 3 == 0 else 'Bank Transfer' if i % 2 == 0 else 'Online',
                f"REF{payment_id}",
                'Completed',
                f'Payment for order {order["order_id"]}',
                order['order_date'],
                'system',
                None,  # division_id
                None   # bank_details
            ))
        
        if sample_payments:
            insert_sql = '''
                INSERT INTO payments (
                    payment_id, invoice_id, order_id, customer_id, amount,
                    payment_date, payment_method, reference_number, status, notes,
                    created_at, created_by, division_id, bank_details
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            '''
            
            db.executemany(insert_sql, sample_payments)
            print(f"   ✅ Added {len(sample_payments)} sample payment records")
        
        db.commit()
        
        # Verify table creation
        print("\n🔍 Verifying table creation...")
        
        # Check table exists
        tables = db.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='payments'").fetchall()
        if tables:
            print("   ✅ Payments table created successfully")
            
            # Check schema
            schema = db.execute("PRAGMA table_info(payments)").fetchall()
            print(f"   📋 Table has {len(schema)} columns:")
            for col in schema:
                print(f"      • {col[1]} ({col[2]})")
            
            # Check row count
            count = db.execute("SELECT COUNT(*) as count FROM payments").fetchone()[0]
            print(f"   📊 Table has {count} rows")
            
            # Test a simple query
            test_query = db.execute("SELECT COUNT(*) as total, SUM(amount) as total_amount FROM payments").fetchone()
            print(f"   💰 Total payments: {test_query[0]}, Total amount: {test_query[1]}")
            
        else:
            print("   ❌ Failed to create payments table")
            return False
        
        db.close()
        print("\n🎉 PAYMENTS TABLE CREATED SUCCESSFULLY!")
        return True
        
    except Exception as e:
        print(f"❌ Error creating payments table: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    create_payments_table()
