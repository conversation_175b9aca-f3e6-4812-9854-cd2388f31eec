#!/usr/bin/env python3
"""
Create Sample Data for Partial Pending System
This will populate the database with sample data to demonstrate the system
"""

import sqlite3
from datetime import datetime, timedelta
import random

def create_sample_data():
    """Create sample data for partial pending system"""
    print("🔧 Creating sample data for Partial Pending System...")
    
    try:
        # Connect to database
        conn = sqlite3.connect('ledger.db')
        cursor = conn.cursor()
        
        # 1. Create partial_dc_tracking table if it doesn't exist
        print("\n1️⃣ Creating partial_dc_tracking table...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS partial_dc_tracking (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id TEXT NOT NULL,
                dc_number TEXT,
                product_id TEXT NOT NULL,
                product_name TEXT,
                strength TEXT,
                original_quantity INTEGER NOT NULL,
                delivered_quantity INTEGER DEFAULT 0,
                pending_quantity INTEGER NOT NULL,
                status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'partially_fulfilled', 'completed', 'cancelled')),
                priority_level INTEGER DEFAULT 1 CHECK (priority_level BETWEEN 1 AND 5),
                expected_fulfillment_date DATE,
                last_stock_check TIMESTAMP,
                stock_available INTEGER DEFAULT 0,
                reorder_suggested BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                notes TEXT
            )
        ''')
        
        # 2. Create realtime_inventory_status table
        print("2️⃣ Creating realtime_inventory_status table...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS realtime_inventory_status (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id TEXT NOT NULL,
                warehouse_id TEXT DEFAULT 'MAIN',
                current_stock INTEGER DEFAULT 0,
                available_stock INTEGER DEFAULT 0,
                reserved_stock INTEGER DEFAULT 0,
                incoming_stock INTEGER DEFAULT 0,
                reorder_point INTEGER DEFAULT 10,
                max_stock_level INTEGER DEFAULT 1000,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_by TEXT,
                stock_status TEXT DEFAULT 'normal',
                alert_threshold INTEGER DEFAULT 20,
                expected_arrival_date DATE,
                supplier_info TEXT,
                notes TEXT,
                UNIQUE(product_id, warehouse_id)
            )
        ''')
        
        # 3. Create inventory_notifications table
        print("3️⃣ Creating inventory_notifications table...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS inventory_notifications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id TEXT NOT NULL,
                notification_type TEXT NOT NULL,
                title TEXT NOT NULL,
                message TEXT NOT NULL,
                priority TEXT DEFAULT 'medium',
                is_read BOOLEAN DEFAULT FALSE,
                is_dismissed BOOLEAN DEFAULT FALSE,
                target_user TEXT,
                related_order_id TEXT,
                related_dc_number TEXT,
                action_required BOOLEAN DEFAULT FALSE,
                action_url TEXT,
                expires_at TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                read_at TIMESTAMP,
                dismissed_at TIMESTAMP
            )
        ''')
        
        # 4. Insert sample partial pending data
        print("\n4️⃣ Inserting sample partial pending data...")
        
        sample_partial_data = [
            ('ORD-2024-001', 'DC-001', 'PROD-001', 'Paracetamol Tablets', '500mg', 100, 60, 40, 'pending', 4),
            ('ORD-2024-001', 'DC-001', 'PROD-002', 'Ibuprofen Tablets', '400mg', 50, 30, 20, 'pending', 3),
            ('ORD-2024-002', 'DC-002', 'PROD-003', 'Amoxicillin Capsules', '250mg', 75, 25, 50, 'pending', 5),
            ('ORD-2024-003', 'DC-003', 'PROD-004', 'Vitamin C Tablets', '1000mg', 200, 150, 50, 'pending', 2),
            ('ORD-2024-004', 'DC-004', 'PROD-005', 'Aspirin Tablets', '75mg', 120, 80, 40, 'pending', 3),
            ('ORD-2024-005', 'DC-005', 'PROD-006', 'Metformin Tablets', '500mg', 90, 45, 45, 'pending', 4),
        ]
        
        for data in sample_partial_data:
            cursor.execute('''
                INSERT OR REPLACE INTO partial_dc_tracking 
                (order_id, dc_number, product_id, product_name, strength, original_quantity, 
                 delivered_quantity, pending_quantity, status, priority_level, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'system')
            ''', data)
        
        # 5. Insert sample inventory status
        print("5️⃣ Inserting sample inventory status...")
        
        sample_inventory = [
            ('PROD-001', 'MAIN', 150, 120, 30, 100, 'normal'),
            ('PROD-002', 'MAIN', 80, 60, 20, 50, 'normal'),
            ('PROD-003', 'MAIN', 25, 15, 10, 75, 'low'),
            ('PROD-004', 'MAIN', 300, 280, 20, 0, 'high'),
            ('PROD-005', 'MAIN', 45, 35, 10, 60, 'medium'),
            ('PROD-006', 'MAIN', 200, 180, 20, 80, 'high'),
        ]
        
        for inv_data in sample_inventory:
            cursor.execute('''
                INSERT OR REPLACE INTO realtime_inventory_status 
                (product_id, warehouse_id, current_stock, available_stock, reserved_stock, 
                 incoming_stock, stock_status, updated_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, 'system')
            ''', inv_data)
        
        # 6. Insert sample notifications
        print("6️⃣ Inserting sample notifications...")
        
        sample_notifications = [
            ('PROD-001', 'stock_available', 'Stock Available', 'Paracetamol 500mg now available for Order ORD-2024-001', 'high', 'ORD-2024-001'),
            ('PROD-003', 'low_stock', 'Low Stock Alert', 'Amoxicillin 250mg running low - only 25 units remaining', 'urgent', 'ORD-2024-002'),
            ('PROD-004', 'stock_available', 'Stock Available', 'Vitamin C 1000mg fully stocked for Order ORD-2024-003', 'medium', 'ORD-2024-003'),
            ('PROD-005', 'reorder_suggestion', 'Reorder Suggested', 'Aspirin 75mg should be reordered soon', 'medium', None),
        ]
        
        for notif_data in sample_notifications:
            cursor.execute('''
                INSERT INTO inventory_notifications 
                (product_id, notification_type, title, message, priority, related_order_id, action_required)
                VALUES (?, ?, ?, ?, ?, ?, TRUE)
            ''', notif_data)
        
        # 7. Create orders table and sample data
        print("7️⃣ Creating orders table and sample data...")

        # Create orders table first
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS orders (
                order_id TEXT PRIMARY KEY,
                customer_name TEXT NOT NULL,
                customer_address TEXT,
                customer_phone TEXT,
                order_date DATE,
                order_amount DECIMAL(10,2),
                status TEXT DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        sample_orders = [
            ('ORD-2024-001', 'John Doe', '123 Main St, City', '9876543210', '2024-01-15', 2500.00, 'partial'),
            ('ORD-2024-002', 'Jane Smith', '456 Oak Ave, Town', '9876543211', '2024-01-16', 1875.00, 'partial'),
            ('ORD-2024-003', 'Bob Johnson', '789 Pine Rd, Village', '9876543212', '2024-01-17', 5000.00, 'partial'),
            ('ORD-2024-004', 'Alice Brown', '321 Elm St, City', '9876543213', '2024-01-18', 3000.00, 'partial'),
            ('ORD-2024-005', 'Charlie Wilson', '654 Maple Dr, Town', '9876543214', '2024-01-19', 2250.00, 'partial'),
        ]

        for order_data in sample_orders:
            cursor.execute('''
                INSERT OR REPLACE INTO orders
                (order_id, customer_name, customer_address, customer_phone, order_date, order_amount, status)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', order_data)
        
        # Commit all changes
        conn.commit()
        
        print("\n✅ Sample data created successfully!")
        print("\n📊 Summary:")
        print(f"   • {len(sample_partial_data)} partial pending items")
        print(f"   • {len(sample_inventory)} inventory records")
        print(f"   • {len(sample_notifications)} notifications")
        print(f"   • {len(sample_orders)} sample orders")
        
        print("\n🌐 You can now access:")
        print("   • Main Dashboard: http://127.0.0.1:5001/partial-pending/")
        print("   • Order Details: http://127.0.0.1:5001/partial-pending/order/ORD-2024-001")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating sample data: {e}")
        return False
        
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    success = create_sample_data()
    if success:
        print("\n🎉 Sample data creation completed!")
        print("🚀 Start the Flask app and visit the Partial Pending dashboard to see the system in action!")
    else:
        print("\n💥 Sample data creation failed!")
