#!/usr/bin/env python3
"""
Create sample products for testing
"""

import sqlite3
import os

def create_sample_products():
    """Create sample products for testing"""
    
    print("🔍 CREATING SAMPLE PRODUCTS")
    print("=" * 50)
    
    try:
        # Connect to database
        db_path = 'instance/medivent.db'
        if not os.path.exists(db_path):
            print(f"❌ Database not found at {db_path}")
            return
        
        db = sqlite3.connect(db_path)
        db.row_factory = sqlite3.Row
        cursor = db.cursor()
        
        # Check existing products
        cursor.execute("SELECT COUNT(*) as count FROM products")
        count = cursor.fetchone()['count']
        print(f"📊 EXISTING PRODUCTS: {count}")
        
        # First, clear existing problematic products
        print("🗑️ CLEARING EXISTING PRODUCTS...")
        cursor.execute("DELETE FROM products")
        db.commit()

        # Get valid division IDs
        cursor.execute("SELECT division_id FROM divisions WHERE is_active = 1 AND status = 'active' LIMIT 3")
        divisions = cursor.fetchall()

        if not divisions:
            print("❌ No active divisions found. Creating default division...")
            cursor.execute("""
                INSERT OR REPLACE INTO divisions
                (division_id, code, name, description, status, is_active, manager_id)
                VALUES ('DIV001', 'PHARMA', 'Pharmaceutical Division', 'Main pharmaceutical products', 'active', 1, 'USR001')
            """)
            db.commit()
            divisions = [{'division_id': 'DIV001'}]

        division_id = divisions[0]['division_id']
        print(f"📋 Using division: {division_id}")

        # Comprehensive pharmaceutical products data
        sample_products = [
            ('P001', 'Paracetamol 500mg', '500mg', 'Medivent Pharma', 'Paracetamol', 'Tablets', 25.50, 100, 'Pain relief and fever reducer', 30.00, 28.00, 26.50),
            ('P002', 'Amoxicillin 250mg', '250mg', 'Medivent Pharma', 'Amoxicillin', 'Capsules', 45.00, 50, 'Antibiotic for bacterial infections', 52.00, 48.00, 46.50),
            ('P003', 'Cough Syrup', '100ml', 'Medivent Pharma', 'Dextromethorphan', 'Syrup', 85.00, 75, 'Cough suppressant syrup', 95.00, 88.00, 86.50),
            ('P004', 'Vitamin C 1000mg', '1000mg', 'Medivent Pharma', 'Ascorbic Acid', 'Tablets', 120.00, 200, 'Vitamin C supplement', 135.00, 125.00, 122.50),
            ('P005', 'Insulin Injection', '10ml', 'Medivent Pharma', 'Human Insulin', 'Injections', 450.00, 25, 'Diabetes medication', 520.00, 480.00, 465.00),
            ('P006', 'Aspirin 75mg', '75mg', 'Medivent Pharma', 'Acetylsalicylic Acid', 'Tablets', 35.00, 150, 'Blood thinner and pain relief', 42.00, 38.00, 36.50),
            ('P007', 'Omeprazole 20mg', '20mg', 'Medivent Pharma', 'Omeprazole', 'Capsules', 65.00, 80, 'Acid reflux and heartburn relief', 75.00, 68.00, 66.50),
            ('P008', 'Multivitamin Syrup', '200ml', 'Medivent Pharma', 'Mixed Vitamins', 'Syrup', 95.00, 60, 'Children multivitamin supplement', 110.00, 98.00, 96.50),
            ('P009', 'Ibuprofen 400mg', '400mg', 'Medivent Pharma', 'Ibuprofen', 'Tablets', 55.00, 120, 'Anti-inflammatory pain reliever', 65.00, 58.00, 56.50),
            ('P010', 'Cetirizine 10mg', '10mg', 'Medivent Pharma', 'Cetirizine HCl', 'Tablets', 40.00, 90, 'Antihistamine for allergies', 48.00, 42.00, 41.00),
            ('P011', 'Metformin 500mg', '500mg', 'Medivent Pharma', 'Metformin HCl', 'Tablets', 75.00, 110, 'Type 2 diabetes medication', 85.00, 78.00, 76.50),
            ('P012', 'Loratadine 10mg', '10mg', 'Medivent Pharma', 'Loratadine', 'Tablets', 38.00, 85, 'Non-drowsy antihistamine', 45.00, 40.00, 39.00),
            ('P013', 'Calcium Carbonate 500mg', '500mg', 'Medivent Pharma', 'Calcium Carbonate', 'Tablets', 60.00, 140, 'Calcium supplement', 70.00, 63.00, 61.50),
            ('P014', 'Azithromycin 250mg', '250mg', 'Medivent Pharma', 'Azithromycin', 'Tablets', 180.00, 40, 'Antibiotic for respiratory infections', 210.00, 190.00, 185.00),
            ('P015', 'Simvastatin 20mg', '20mg', 'Medivent Pharma', 'Simvastatin', 'Tablets', 95.00, 70, 'Cholesterol lowering medication', 110.00, 98.00, 96.50),
        ]
        
        # Insert sample products with comprehensive data
        for product_data in sample_products:
            product_id, name, strength, manufacturer, generic_name, category, unit_price, min_stock, description, mrp, tp_rate, asp = product_data
            try:
                cursor.execute("""
                    INSERT OR REPLACE INTO products
                    (product_id, name, strength, manufacturer, generic_name, category, unit_price,
                     min_stock_level, description, division_id, mrp, tp_rate, asp,
                     created_at, created_by, updated_at, updated_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
                            datetime('now'), 'system', datetime('now'), 'system')
                """, (product_id, name, strength, manufacturer, generic_name, category, unit_price,
                      min_stock, description, division_id, mrp, tp_rate, asp))
                print(f"✅ Created/Updated: {product_id} - {name} ({strength})")
            except Exception as e:
                print(f"❌ Error creating {product_id}: {str(e)}")
        
        db.commit()
        
        # Verify products were created
        cursor.execute("SELECT COUNT(*) as count FROM products")
        new_count = cursor.fetchone()['count']
        print(f"\n📊 TOTAL PRODUCTS AFTER CREATION: {new_count}")
        
        # Show first 5 products
        print("\n📋 SAMPLE PRODUCTS:")
        cursor.execute("SELECT product_id, name, unit_price FROM products LIMIT 5")
        products = cursor.fetchall()
        for product in products:
            print(f"   • {product['product_id']}: {product['name']} - ${product['unit_price']}")
        
        db.close()
        print("\n✅ SAMPLE PRODUCTS CREATION COMPLETE")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    create_sample_products()
