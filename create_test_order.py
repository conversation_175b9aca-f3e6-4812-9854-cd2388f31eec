#!/usr/bin/env python3
"""
Create Test Order ORD00000155 for debugging
"""

import sqlite3
from datetime import datetime

def create_test_order():
    """Create the test order ORD00000155"""
    print("🔧 CREATING TEST ORDER ORD00000155")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        conn.row_factory = sqlite3.Row
        
        # Check if order already exists
        existing_order = conn.execute('SELECT * FROM orders WHERE order_id = ?', ('ORD00000155',)).fetchone()
        
        if existing_order:
            print('✅ Order ORD00000155 already exists')
            print(f'   Customer: {existing_order["customer_name"]}')
            print(f'   Amount: Rs.{existing_order["order_amount"]}')
            return True
        
        # Create the order
        print('Creating order ORD00000155...')
        
        order_data = {
            'order_id': 'ORD00000155',
            'customer_name': '3Minur',
            'customer_phone': '00211111',
            'customer_address': '4',
            'order_amount': 444.0,
            'order_date': '2025-07-20 00:01',
            'status': 'Normal',
            'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'priority_level': 1
        }
        
        # Insert order
        conn.execute('''
            INSERT OR REPLACE INTO orders 
            (order_id, customer_name, customer_phone, customer_address, 
             order_amount, order_date, status, created_at, priority_level)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            order_data['order_id'],
            order_data['customer_name'],
            order_data['customer_phone'],
            order_data['customer_address'],
            order_data['order_amount'],
            order_data['order_date'],
            order_data['status'],
            order_data['created_at'],
            order_data['priority_level']
        ))
        
        # Create order items
        order_items = [
            {
                'order_id': 'ORD00000155',
                'product_id': 'P001',
                'product_name': 'Sample Medicine',
                'quantity': 1,
                'unit_price': 444.0,
                'total_price': 444.0
            }
        ]
        
        for item in order_items:
            conn.execute('''
                INSERT OR REPLACE INTO order_items 
                (order_id, product_id, product_name, quantity, unit_price, total_price)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                item['order_id'],
                item['product_id'],
                item['product_name'],
                item['quantity'],
                item['unit_price'],
                item['total_price']
            ))
        
        conn.commit()
        print('✅ Order ORD00000155 created successfully')
        
        # Verify creation
        created_order = conn.execute('SELECT * FROM orders WHERE order_id = ?', ('ORD00000155',)).fetchone()
        if created_order:
            print(f'   Customer: {created_order["customer_name"]}')
            print(f'   Amount: Rs.{created_order["order_amount"]}')
            print(f'   Status: {created_order["status"]}')
            
        items = conn.execute('SELECT * FROM order_items WHERE order_id = ?', ('ORD00000155',)).fetchall()
        print(f'   Order items: {len(items)} items created')
        
        return True
        
    except Exception as e:
        print(f'❌ Error creating order: {e}')
        return False
    finally:
        if conn:
            conn.close()

def test_qr_generation():
    """Test QR code generation with the created order"""
    print("\n🔍 TESTING QR CODE GENERATION")
    print("=" * 50)
    
    try:
        # Test QR code dependencies
        import qrcode
        from PIL import Image
        print('✅ QR code dependencies available')
        
        # Test QR code generator
        from utils.qr_code_generator import generate_order_qr_code
        print('✅ QR code generator imported successfully')
        
        # Create sample order data for QR generation
        sample_order_data = {
            'success': True,
            'order': {
                'order_id': 'ORD00000155',
                'customer_name': '3Minur',
                'customer_phone': '00211111',
                'customer_address': '4',
                'order_amount': 444.0,
                'order_date': '2025-07-20 00:01',
                'status': 'Normal'
            },
            'order_items': [
                {
                    'product_name': 'Sample Medicine',
                    'quantity': 1,
                    'unit_price': 444.0,
                    'total_price': 444.0
                }
            ],
            'summary': {
                'total_items': 1,
                'order_amount': 444.0
            }
        }
        
        # Generate QR code
        result = generate_order_qr_code(sample_order_data, include_branding=True)
        
        if result.get('success'):
            print('✅ QR code generation successful')
            qr_data = result.get('qr_code', {})
            print(f'   Base64 length: {len(qr_data.get("base64", ""))}')
            print(f'   File path: {qr_data.get("file_path", "N/A")}')
            return True
        else:
            print(f'❌ QR code generation failed: {result.get("error")}')
            return False
            
    except Exception as e:
        print(f'❌ QR code test error: {e}')
        return False

def main():
    """Run all setup and tests"""
    print("🚀 SETTING UP TEST ORDER AND QR CODE")
    print("=" * 60)
    
    order_created = create_test_order()
    qr_working = test_qr_generation()
    
    print("\n📊 SETUP RESULTS")
    print("=" * 50)
    print(f"Test Order Created: {'✅ SUCCESS' if order_created else '❌ FAILED'}")
    print(f"QR Code Generation: {'✅ WORKING' if qr_working else '❌ FAILED'}")
    
    if order_created and qr_working:
        print("\n🎉 SETUP COMPLETE!")
        print("Order ORD00000155 is ready for testing both issues:")
        print("1. Order details API should now work")
        print("2. QR code generation should now work")
        print("3. Print address route has been added")
    else:
        print("\n⚠️ SETUP INCOMPLETE - CHECK ERRORS ABOVE")

if __name__ == "__main__":
    main()
