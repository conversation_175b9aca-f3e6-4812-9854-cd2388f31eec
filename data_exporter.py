#!/usr/bin/env python3
"""
Data Exporter Module
Provides data export functionality for the Medivent ERP system
"""

import pandas as pd
import sqlite3
import json
import csv
import io
from datetime import datetime
from typing import Dict, List, Any, Optional
from flask import Response

class DataExporter:
    """
    Comprehensive data export class for ERP data
    """
    
    def __init__(self, db_path: str = 'instance/medivent.db'):
        self.db_path = db_path
        
    def get_db_connection(self):
        """Get database connection"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def export_orders_csv(self, start_date: str = None, end_date: str = None) -> Response:
        """
        Export orders data to CSV
        
        Args:
            start_date: Start date filter (YYYY-MM-DD)
            end_date: End date filter (YYYY-MM-DD)
            
        Returns:
            Flask Response with CSV data
        """
        try:
            conn = self.get_db_connection()
            
            query = """
                SELECT 
                    o.order_id,
                    o.customer_id,
                    c.customer_name,
                    o.order_date,
                    o.order_amount,
                    o.status,
                    o.payment_status,
                    o.delivery_address,
                    o.created_at
                FROM orders o
                LEFT JOIN customers c ON o.customer_id = c.customer_id
                WHERE 1=1
            """
            params = []
            
            if start_date:
                query += " AND DATE(o.order_date) >= ?"
                params.append(start_date)
            if end_date:
                query += " AND DATE(o.order_date) <= ?"
                params.append(end_date)
                
            query += " ORDER BY o.order_date DESC"
            
            df = pd.read_sql_query(query, conn, params=params)
            conn.close()
            
            # Create CSV output
            output = io.StringIO()
            df.to_csv(output, index=False)
            output.seek(0)
            
            # Create filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"orders_export_{timestamp}.csv"
            
            return Response(
                output.getvalue(),
                mimetype='text/csv',
                headers={'Content-Disposition': f'attachment; filename={filename}'}
            )
            
        except Exception as e:
            return Response(f"Error exporting orders: {str(e)}", status=500)
    
    def export_products_csv(self) -> Response:
        """
        Export products data to CSV
        
        Returns:
            Flask Response with CSV data
        """
        try:
            conn = self.get_db_connection()
            
            query = """
                SELECT 
                    p.product_id,
                    p.name as product_name,
                    p.description,
                    p.price,
                    p.category,
                    p.manufacturer,
                    i.current_stock,
                    i.reorder_level,
                    i.max_stock_level,
                    p.created_at
                FROM products p
                LEFT JOIN inventory i ON p.product_id = i.product_id
                ORDER BY p.name
            """
            
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            # Create CSV output
            output = io.StringIO()
            df.to_csv(output, index=False)
            output.seek(0)
            
            # Create filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"products_export_{timestamp}.csv"
            
            return Response(
                output.getvalue(),
                mimetype='text/csv',
                headers={'Content-Disposition': f'attachment; filename={filename}'}
            )
            
        except Exception as e:
            return Response(f"Error exporting products: {str(e)}", status=500)
    
    def export_customers_csv(self) -> Response:
        """
        Export customers data to CSV
        
        Returns:
            Flask Response with CSV data
        """
        try:
            conn = self.get_db_connection()
            
            query = """
                SELECT 
                    c.customer_id,
                    c.customer_name,
                    c.email,
                    c.phone,
                    c.address,
                    c.city,
                    c.status,
                    COUNT(o.order_id) as total_orders,
                    COALESCE(SUM(o.order_amount), 0) as total_spent,
                    MAX(o.order_date) as last_order_date,
                    c.created_at
                FROM customers c
                LEFT JOIN orders o ON c.customer_id = o.customer_id
                GROUP BY c.customer_id
                ORDER BY c.customer_name
            """
            
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            # Create CSV output
            output = io.StringIO()
            df.to_csv(output, index=False)
            output.seek(0)
            
            # Create filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"customers_export_{timestamp}.csv"
            
            return Response(
                output.getvalue(),
                mimetype='text/csv',
                headers={'Content-Disposition': f'attachment; filename={filename}'}
            )
            
        except Exception as e:
            return Response(f"Error exporting customers: {str(e)}", status=500)
    
    def export_inventory_csv(self) -> Response:
        """
        Export inventory data to CSV
        
        Returns:
            Flask Response with CSV data
        """
        try:
            conn = self.get_db_connection()
            
            query = """
                SELECT 
                    p.product_id,
                    p.name as product_name,
                    p.category,
                    i.current_stock,
                    i.reorder_level,
                    i.max_stock_level,
                    CASE 
                        WHEN i.current_stock <= i.reorder_level THEN 'Low Stock'
                        WHEN i.current_stock >= i.max_stock_level * 0.8 THEN 'High Stock'
                        ELSE 'Normal Stock'
                    END as stock_status,
                    i.last_updated
                FROM products p
                LEFT JOIN inventory i ON p.product_id = i.product_id
                WHERE i.current_stock IS NOT NULL
                ORDER BY i.current_stock ASC
            """
            
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            # Create CSV output
            output = io.StringIO()
            df.to_csv(output, index=False)
            output.seek(0)
            
            # Create filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"inventory_export_{timestamp}.csv"
            
            return Response(
                output.getvalue(),
                mimetype='text/csv',
                headers={'Content-Disposition': f'attachment; filename={filename}'}
            )
            
        except Exception as e:
            return Response(f"Error exporting inventory: {str(e)}", status=500)
    
    def export_sales_report_csv(self, start_date: str = None, end_date: str = None) -> Response:
        """
        Export sales report to CSV
        
        Args:
            start_date: Start date filter (YYYY-MM-DD)
            end_date: End date filter (YYYY-MM-DD)
            
        Returns:
            Flask Response with CSV data
        """
        try:
            conn = self.get_db_connection()
            
            query = """
                SELECT 
                    DATE(o.order_date) as sale_date,
                    COUNT(o.order_id) as total_orders,
                    SUM(o.order_amount) as total_revenue,
                    AVG(o.order_amount) as avg_order_value,
                    COUNT(DISTINCT o.customer_id) as unique_customers
                FROM orders o
                WHERE o.status != 'cancelled'
            """
            params = []
            
            if start_date:
                query += " AND DATE(o.order_date) >= ?"
                params.append(start_date)
            if end_date:
                query += " AND DATE(o.order_date) <= ?"
                params.append(end_date)
                
            query += " GROUP BY DATE(o.order_date) ORDER BY sale_date DESC"
            
            df = pd.read_sql_query(query, conn, params=params)
            conn.close()
            
            # Create CSV output
            output = io.StringIO()
            df.to_csv(output, index=False)
            output.seek(0)
            
            # Create filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"sales_report_{timestamp}.csv"
            
            return Response(
                output.getvalue(),
                mimetype='text/csv',
                headers={'Content-Disposition': f'attachment; filename={filename}'}
            )
            
        except Exception as e:
            return Response(f"Error exporting sales report: {str(e)}", status=500)
    
    def export_data_json(self, table_name: str, limit: int = 1000) -> Response:
        """
        Export table data to JSON
        
        Args:
            table_name: Name of the table to export
            limit: Maximum number of records to export
            
        Returns:
            Flask Response with JSON data
        """
        try:
            conn = self.get_db_connection()
            
            # Validate table name to prevent SQL injection
            valid_tables = ['orders', 'products', 'customers', 'inventory', 'users', 'notifications']
            if table_name not in valid_tables:
                return Response(f"Invalid table name: {table_name}", status=400)
            
            query = f"SELECT * FROM {table_name} LIMIT ?"
            df = pd.read_sql_query(query, conn, params=[limit])
            conn.close()
            
            # Convert to JSON
            json_data = df.to_json(orient='records', date_format='iso', indent=2)
            
            # Create filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{table_name}_export_{timestamp}.json"
            
            return Response(
                json_data,
                mimetype='application/json',
                headers={'Content-Disposition': f'attachment; filename={filename}'}
            )
            
        except Exception as e:
            return Response(f"Error exporting {table_name}: {str(e)}", status=500)
    
    def get_export_summary(self) -> Dict[str, Any]:
        """
        Get summary of available data for export
        
        Returns:
            Dictionary with export summary information
        """
        try:
            conn = self.get_db_connection()
            
            # Get record counts for each table
            tables = ['orders', 'products', 'customers', 'inventory', 'users', 'notifications']
            summary = {}
            
            for table in tables:
                try:
                    count_query = f"SELECT COUNT(*) as count FROM {table}"
                    result = conn.execute(count_query).fetchone()
                    summary[table] = result['count']
                except sqlite3.OperationalError:
                    summary[table] = 0  # Table doesn't exist
            
            conn.close()
            
            return {
                'status': 'success',
                'table_counts': summary,
                'available_formats': ['CSV', 'JSON'],
                'last_updated': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f"Error getting export summary: {str(e)}"
            }

# Convenience functions for backward compatibility
def export_orders_csv(start_date=None, end_date=None):
    """Export orders to CSV - backward compatibility function"""
    exporter = DataExporter()
    return exporter.export_orders_csv(start_date, end_date)

def export_products_csv():
    """Export products to CSV - backward compatibility function"""
    exporter = DataExporter()
    return exporter.export_products_csv()

def export_inventory_csv():
    """Export inventory to CSV - backward compatibility function"""
    exporter = DataExporter()
    return exporter.export_inventory_csv()
