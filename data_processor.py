#!/usr/bin/env python3
"""
Data Processor Module
Provides data processing and analysis functionality for the Medivent ERP system
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import sqlite3
import json

class DataProcessor:
    """
    Comprehensive data processing class for ERP analytics
    """
    
    def __init__(self, db_path: str = 'instance/medivent.db'):
        self.db_path = db_path
        
    def get_db_connection(self):
        """Get database connection"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def process_sales_data(self, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """
        Process sales data for analytics
        
        Args:
            start_date: Start date for analysis (YYYY-MM-DD)
            end_date: End date for analysis (YYYY-MM-DD)
            
        Returns:
            Processed sales data dictionary
        """
        try:
            conn = self.get_db_connection()
            
            # Build query with date filters
            query = """
                SELECT 
                    DATE(order_date) as date,
                    SUM(order_amount) as daily_total,
                    COUNT(*) as order_count,
                    AVG(order_amount) as avg_order_value
                FROM orders 
                WHERE 1=1
            """
            params = []
            
            if start_date:
                query += " AND DATE(order_date) >= ?"
                params.append(start_date)
            if end_date:
                query += " AND DATE(order_date) <= ?"
                params.append(end_date)
                
            query += " GROUP BY DATE(order_date) ORDER BY date"
            
            df = pd.read_sql_query(query, conn, params=params)
            conn.close()
            
            if df.empty:
                return self._empty_sales_data()
            
            # Calculate additional metrics
            total_revenue = df['daily_total'].sum()
            total_orders = df['order_count'].sum()
            avg_daily_revenue = df['daily_total'].mean()
            
            # Growth rate calculation
            if len(df) > 1:
                recent_avg = df.tail(7)['daily_total'].mean()
                previous_avg = df.head(7)['daily_total'].mean()
                growth_rate = ((recent_avg - previous_avg) / previous_avg * 100) if previous_avg > 0 else 0
            else:
                growth_rate = 0
            
            return {
                'daily_data': df.to_dict('records'),
                'summary': {
                    'total_revenue': float(total_revenue),
                    'total_orders': int(total_orders),
                    'avg_daily_revenue': float(avg_daily_revenue),
                    'avg_order_value': float(df['avg_order_value'].mean()),
                    'growth_rate': float(growth_rate),
                    'date_range': {
                        'start': df['date'].min(),
                        'end': df['date'].max()
                    }
                }
            }
            
        except Exception as e:
            return {'error': f"Error processing sales data: {str(e)}"}
    
    def process_inventory_data(self) -> Dict[str, Any]:
        """
        Process inventory data for analytics
        
        Returns:
            Processed inventory data dictionary
        """
        try:
            conn = self.get_db_connection()
            
            query = """
                SELECT 
                    p.name as product_name,
                    p.product_id,
                    i.current_stock,
                    i.reorder_level,
                    i.max_stock_level,
                    CASE 
                        WHEN i.current_stock <= i.reorder_level THEN 'Low Stock'
                        WHEN i.current_stock >= i.max_stock_level * 0.8 THEN 'High Stock'
                        ELSE 'Normal Stock'
                    END as stock_status
                FROM products p
                LEFT JOIN inventory i ON p.product_id = i.product_id
                WHERE i.current_stock IS NOT NULL
                ORDER BY i.current_stock ASC
            """
            
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            if df.empty:
                return self._empty_inventory_data()
            
            # Calculate inventory metrics
            total_products = len(df)
            low_stock_count = len(df[df['stock_status'] == 'Low Stock'])
            high_stock_count = len(df[df['stock_status'] == 'High Stock'])
            normal_stock_count = len(df[df['stock_status'] == 'Normal Stock'])
            
            total_stock_value = df['current_stock'].sum()
            avg_stock_level = df['current_stock'].mean()
            
            return {
                'inventory_data': df.to_dict('records'),
                'summary': {
                    'total_products': total_products,
                    'low_stock_count': low_stock_count,
                    'high_stock_count': high_stock_count,
                    'normal_stock_count': normal_stock_count,
                    'total_stock_value': float(total_stock_value),
                    'avg_stock_level': float(avg_stock_level),
                    'stock_distribution': {
                        'low_stock_percentage': (low_stock_count / total_products * 100) if total_products > 0 else 0,
                        'normal_stock_percentage': (normal_stock_count / total_products * 100) if total_products > 0 else 0,
                        'high_stock_percentage': (high_stock_count / total_products * 100) if total_products > 0 else 0
                    }
                }
            }
            
        except Exception as e:
            return {'error': f"Error processing inventory data: {str(e)}"}
    
    def process_order_data(self, days: int = 30) -> Dict[str, Any]:
        """
        Process order data for analytics
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Processed order data dictionary
        """
        try:
            conn = self.get_db_connection()
            
            # Get orders from last N days
            query = """
                SELECT 
                    status,
                    COUNT(*) as count,
                    SUM(order_amount) as total_amount,
                    AVG(order_amount) as avg_amount
                FROM orders 
                WHERE order_date >= date('now', '-{} days')
                GROUP BY status
                ORDER BY count DESC
            """.format(days)
            
            df = pd.read_sql_query(query, conn)
            
            # Get daily order trends
            trend_query = """
                SELECT 
                    DATE(order_date) as date,
                    COUNT(*) as order_count,
                    SUM(order_amount) as daily_revenue
                FROM orders 
                WHERE order_date >= date('now', '-{} days')
                GROUP BY DATE(order_date)
                ORDER BY date
            """.format(days)
            
            trend_df = pd.read_sql_query(trend_query, conn)
            conn.close()
            
            if df.empty:
                return self._empty_order_data()
            
            # Calculate metrics
            total_orders = df['count'].sum()
            total_revenue = df['total_amount'].sum()
            avg_order_value = total_revenue / total_orders if total_orders > 0 else 0
            
            return {
                'status_data': df.to_dict('records'),
                'trend_data': trend_df.to_dict('records'),
                'summary': {
                    'total_orders': int(total_orders),
                    'total_revenue': float(total_revenue),
                    'avg_order_value': float(avg_order_value),
                    'analysis_period_days': days,
                    'most_common_status': df.iloc[0]['status'] if not df.empty else 'N/A'
                }
            }
            
        except Exception as e:
            return {'error': f"Error processing order data: {str(e)}"}
    
    def process_customer_data(self) -> Dict[str, Any]:
        """
        Process customer data for analytics
        
        Returns:
            Processed customer data dictionary
        """
        try:
            conn = self.get_db_connection()
            
            query = """
                SELECT 
                    c.customer_name,
                    c.customer_id,
                    COUNT(o.order_id) as total_orders,
                    SUM(o.order_amount) as total_spent,
                    AVG(o.order_amount) as avg_order_value,
                    MAX(o.order_date) as last_order_date
                FROM customers c
                LEFT JOIN orders o ON c.customer_id = o.customer_id
                GROUP BY c.customer_id, c.customer_name
                HAVING COUNT(o.order_id) > 0
                ORDER BY total_spent DESC
                LIMIT 50
            """
            
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            if df.empty:
                return self._empty_customer_data()
            
            # Calculate customer segments
            df['customer_segment'] = pd.cut(df['total_spent'], 
                                          bins=[0, 10000, 50000, float('inf')], 
                                          labels=['Low Value', 'Medium Value', 'High Value'])
            
            segment_counts = df['customer_segment'].value_counts()
            
            return {
                'customer_data': df.to_dict('records'),
                'summary': {
                    'total_customers': len(df),
                    'avg_customer_value': float(df['total_spent'].mean()),
                    'top_customer': df.iloc[0]['customer_name'] if not df.empty else 'N/A',
                    'segments': {
                        'high_value': int(segment_counts.get('High Value', 0)),
                        'medium_value': int(segment_counts.get('Medium Value', 0)),
                        'low_value': int(segment_counts.get('Low Value', 0))
                    }
                }
            }
            
        except Exception as e:
            return {'error': f"Error processing customer data: {str(e)}"}
    
    def process_product_performance(self, days: int = 30) -> Dict[str, Any]:
        """
        Process product performance data
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Processed product performance data
        """
        try:
            conn = self.get_db_connection()
            
            query = """
                SELECT 
                    p.name as product_name,
                    p.product_id,
                    COUNT(oi.id) as times_ordered,
                    SUM(oi.quantity) as total_quantity_sold,
                    SUM(oi.total_price) as total_revenue,
                    AVG(oi.unit_price) as avg_unit_price
                FROM products p
                JOIN order_items oi ON p.product_id = oi.product_id
                JOIN orders o ON oi.order_id = o.order_id
                WHERE o.order_date >= date('now', '-{} days')
                GROUP BY p.product_id, p.name
                ORDER BY total_revenue DESC
                LIMIT 20
            """.format(days)
            
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            if df.empty:
                return self._empty_product_data()
            
            return {
                'product_data': df.to_dict('records'),
                'summary': {
                    'total_products_sold': len(df),
                    'top_product': df.iloc[0]['product_name'] if not df.empty else 'N/A',
                    'total_revenue': float(df['total_revenue'].sum()),
                    'analysis_period_days': days
                }
            }
            
        except Exception as e:
            return {'error': f"Error processing product performance data: {str(e)}"}
    
    def _empty_sales_data(self) -> Dict[str, Any]:
        """Return empty sales data structure"""
        return {
            'daily_data': [],
            'summary': {
                'total_revenue': 0,
                'total_orders': 0,
                'avg_daily_revenue': 0,
                'avg_order_value': 0,
                'growth_rate': 0,
                'date_range': {'start': None, 'end': None}
            }
        }
    
    def _empty_inventory_data(self) -> Dict[str, Any]:
        """Return empty inventory data structure"""
        return {
            'inventory_data': [],
            'summary': {
                'total_products': 0,
                'low_stock_count': 0,
                'high_stock_count': 0,
                'normal_stock_count': 0,
                'total_stock_value': 0,
                'avg_stock_level': 0,
                'stock_distribution': {
                    'low_stock_percentage': 0,
                    'normal_stock_percentage': 0,
                    'high_stock_percentage': 0
                }
            }
        }
    
    def _empty_order_data(self) -> Dict[str, Any]:
        """Return empty order data structure"""
        return {
            'status_data': [],
            'trend_data': [],
            'summary': {
                'total_orders': 0,
                'total_revenue': 0,
                'avg_order_value': 0,
                'analysis_period_days': 30,
                'most_common_status': 'N/A'
            }
        }
    
    def _empty_customer_data(self) -> Dict[str, Any]:
        """Return empty customer data structure"""
        return {
            'customer_data': [],
            'summary': {
                'total_customers': 0,
                'avg_customer_value': 0,
                'top_customer': 'N/A',
                'segments': {
                    'high_value': 0,
                    'medium_value': 0,
                    'low_value': 0
                }
            }
        }
    
    def _empty_product_data(self) -> Dict[str, Any]:
        """Return empty product data structure"""
        return {
            'product_data': [],
            'summary': {
                'total_products_sold': 0,
                'top_product': 'N/A',
                'total_revenue': 0,
                'analysis_period_days': 30
            }
        }

# Convenience functions for backward compatibility
def process_sales_data(start_date=None, end_date=None):
    """Process sales data - backward compatibility function"""
    processor = DataProcessor()
    return processor.process_sales_data(start_date, end_date)

def process_inventory_data():
    """Process inventory data - backward compatibility function"""
    processor = DataProcessor()
    return processor.process_inventory_data()

def process_order_data(days=30):
    """Process order data - backward compatibility function"""
    processor = DataProcessor()
    return processor.process_order_data(days)
