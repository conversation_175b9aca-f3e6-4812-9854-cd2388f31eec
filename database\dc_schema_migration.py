#!/usr/bin/env python3
"""
DC System Database Schema Migration
Creates clean, normalized tables for the new DC generation system
"""

import sqlite3
import os
from datetime import datetime


def migrate_dc_schema():
    """Migrate database schema for new DC system"""
    
    db_path = 'instance/medivent.db'
    
    if not os.path.exists(db_path):
        print("❌ Database not found!")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔄 Starting DC system database migration...")
        
        # 1. Backup existing delivery_challans table
        print("\n1️⃣ Backing up existing delivery_challans table...")
        try:
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS delivery_challans_backup AS 
                SELECT * FROM delivery_challans
            ''')
            print("✅ Backup created")
        except Exception as e:
            print(f"⚠️ Backup warning: {e}")
        
        # 2. Drop and recreate delivery_challans table with clean schema
        print("\n2️⃣ Recreating delivery_challans table...")
        cursor.execute('DROP TABLE IF EXISTS delivery_challans')
        
        cursor.execute('''
            CREATE TABLE delivery_challans (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                dc_number TEXT UNIQUE NOT NULL,
                order_id TEXT NOT NULL,
                warehouse_id TEXT,
                customer_name TEXT,
                status TEXT DEFAULT 'created' CHECK (status IN ('created', 'dispatched', 'delivered')),
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                dispatch_date TIMESTAMP,
                delivery_date TIMESTAMP,
                created_by TEXT,
                total_items INTEGER DEFAULT 0,
                total_amount REAL DEFAULT 0,
                batch_details TEXT, -- JSON string with allocation details
                pdf_path TEXT,
                notes TEXT,
                FOREIGN KEY (order_id) REFERENCES orders (order_id),
                FOREIGN KEY (warehouse_id) REFERENCES warehouses (warehouse_id)
            )
        ''')
        print("✅ delivery_challans table recreated")
        
        # 3. Create batch_allocations table for normalized batch tracking
        print("\n3️⃣ Creating batch_allocations table...")
        cursor.execute('DROP TABLE IF EXISTS batch_allocations')
        
        cursor.execute('''
            CREATE TABLE batch_allocations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                dc_id INTEGER NOT NULL,
                inventory_id TEXT NOT NULL,
                product_id TEXT NOT NULL,
                quantity_allocated INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (dc_id) REFERENCES delivery_challans (id),
                FOREIGN KEY (inventory_id) REFERENCES inventory (inventory_id),
                FOREIGN KEY (product_id) REFERENCES products (product_id)
            )
        ''')
        print("✅ batch_allocations table created")
        
        # 4. Ensure stock_movements table exists
        print("\n4️⃣ Ensuring stock_movements table...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS stock_movements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                inventory_id TEXT NOT NULL,
                movement_type TEXT NOT NULL CHECK (movement_type IN ('allocation', 'deallocation', 'adjustment')),
                quantity INTEGER NOT NULL,
                reference_type TEXT, -- 'dc_generation', 'order_cancellation', etc.
                reference_id TEXT,   -- DC number, order ID, etc.
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                notes TEXT,
                FOREIGN KEY (inventory_id) REFERENCES inventory (inventory_id)
            )
        ''')
        print("✅ stock_movements table ready")
        
        # 5. Ensure inventory table has allocated_quantity column
        print("\n5️⃣ Updating inventory table...")
        try:
            cursor.execute('ALTER TABLE inventory ADD COLUMN allocated_quantity INTEGER DEFAULT 0')
            print("✅ allocated_quantity column added to inventory")
        except sqlite3.OperationalError:
            print("✅ allocated_quantity column already exists")
        
        # 6. Create indexes for performance
        print("\n6️⃣ Creating indexes...")
        indexes = [
            'CREATE INDEX IF NOT EXISTS idx_delivery_challans_order_id ON delivery_challans(order_id)',
            'CREATE INDEX IF NOT EXISTS idx_delivery_challans_dc_number ON delivery_challans(dc_number)',
            'CREATE INDEX IF NOT EXISTS idx_delivery_challans_status ON delivery_challans(status)',
            'CREATE INDEX IF NOT EXISTS idx_batch_allocations_dc_id ON batch_allocations(dc_id)',
            'CREATE INDEX IF NOT EXISTS idx_batch_allocations_inventory_id ON batch_allocations(inventory_id)',
            'CREATE INDEX IF NOT EXISTS idx_stock_movements_inventory_id ON stock_movements(inventory_id)',
            'CREATE INDEX IF NOT EXISTS idx_stock_movements_reference ON stock_movements(reference_type, reference_id)',
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        print("✅ Indexes created")
        
        # 7. Clean up old problematic tables
        print("\n7️⃣ Cleaning up old tables...")
        cleanup_tables = ['batch_selections', 'dc_generation_sessions']
        for table in cleanup_tables:
            try:
                cursor.execute(f'DROP TABLE IF EXISTS {table}')
                print(f"✅ Removed old table: {table}")
            except Exception as e:
                print(f"⚠️ Warning cleaning {table}: {e}")
        
        # 8. Verify schema
        print("\n8️⃣ Verifying schema...")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        tables = [row[0] for row in cursor.fetchall()]
        
        required_tables = ['delivery_challans', 'batch_allocations', 'stock_movements', 'inventory']
        for table in required_tables:
            if table in tables:
                print(f"✅ {table} table verified")
            else:
                print(f"❌ {table} table missing")
        
        # 9. Add sample data if needed
        print("\n9️⃣ Checking for sample data...")
        cursor.execute("SELECT COUNT(*) FROM inventory WHERE product_id IN ('P001', 'P002')")
        inventory_count = cursor.fetchone()[0]
        
        if inventory_count == 0:
            print("Adding sample inventory...")
            sample_inventory = [
                ('INV001', 'P001', 'BATCH001', '2024-06-01', '2025-06-01', 100, 0, 'WH001', 'A1-01', 'active'),
                ('INV002', 'P001', 'BATCH002', '2024-06-15', '2025-06-15', 75, 0, 'WH001', 'A1-02', 'active'),
                ('INV003', 'P001', 'BATCH003', '2024-07-01', '2025-07-01', 50, 0, 'WH002', 'B1-01', 'active'),
                ('INV004', 'P002', 'BATCH004', '2024-06-01', '2025-06-01', 80, 0, 'WH001', 'A2-01', 'active'),
                ('INV005', 'P002', 'BATCH005', '2024-06-15', '2025-06-15', 60, 0, 'WH002', 'B2-01', 'active'),
            ]
            
            for inv in sample_inventory:
                cursor.execute('''
                    INSERT OR REPLACE INTO inventory 
                    (inventory_id, product_id, batch_number, manufacturing_date, expiry_date, 
                     stock_quantity, allocated_quantity, warehouse_id, location_code, status)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', inv)
            
            print(f"✅ Added {len(sample_inventory)} sample inventory records")
        else:
            print(f"✅ Found {inventory_count} existing inventory records")
        
        # Commit all changes
        conn.commit()
        conn.close()
        
        print("\n🎉 DC system database migration completed successfully!")
        print("\n📋 Summary:")
        print("   ✅ delivery_challans table recreated with clean schema")
        print("   ✅ batch_allocations table created for normalized tracking")
        print("   ✅ stock_movements table ensured for audit trail")
        print("   ✅ inventory table updated with allocated_quantity")
        print("   ✅ Performance indexes created")
        print("   ✅ Old problematic tables cleaned up")
        print("   ✅ Sample inventory data available")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False


if __name__ == "__main__":
    migrate_dc_schema()
