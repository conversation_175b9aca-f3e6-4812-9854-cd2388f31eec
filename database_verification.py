#!/usr/bin/env python3
"""
Database Verification Script
Verifies database schema, data integrity, and functionality
"""

import sqlite3
import os
from datetime import datetime

def test_database():
    """Test database connectivity and schema"""
    print("🗄️ DATABASE VERIFICATION")
    print("=" * 50)
    
    db_path = 'instance/medivent.db'
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Test basic tables
        tables = ['products', 'inventory', 'orders', 'users', 'stock_movements']
        for table in tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"✅ Table {table}: {count} records")
            except Exception as e:
                print(f"❌ Table {table}: {str(e)}")
        
        # Test products table schema
        print("\n📦 PRODUCTS TABLE VERIFICATION")
        cursor.execute("PRAGMA table_info(products)")
        columns = [col[1] for col in cursor.fetchall()]
        required_cols = ['product_id', 'name', 'status', 'unit_price', 'category']
        
        for col in required_cols:
            if col in columns:
                print(f"✅ Column {col}: Present")
            else:
                print(f"❌ Column {col}: Missing")
        
        # Test product status functionality
        print("\n🔄 PRODUCT STATUS VERIFICATION")
        cursor.execute("SELECT status, COUNT(*) FROM products GROUP BY status")
        status_counts = cursor.fetchall()
        
        for row in status_counts:
            status = row[0] if row[0] else 'NULL'
            count = row[1]
            print(f"✅ Status '{status}': {count} products")
        
        # Test stock_movements schema
        print("\n📊 STOCK MOVEMENTS VERIFICATION")
        cursor.execute("PRAGMA table_info(stock_movements)")
        columns = [col[1] for col in cursor.fetchall()]
        required_cols = ['from_warehouse_id', 'to_warehouse_id', 'movement_date', 'moved_by']
        
        for col in required_cols:
            if col in columns:
                print(f"✅ Column {col}: Present")
            else:
                print(f"❌ Column {col}: Missing")
        
        # Test inventory aggregation
        print("\n📋 INVENTORY AGGREGATION TEST")
        cursor.execute("""
            SELECT p.product_id, p.name,
                   COUNT(i.inventory_id) as batch_count,
                   COALESCE(SUM(i.stock_quantity), 0) as total_stock,
                   COALESCE(SUM(i.stock_quantity - i.allocated_quantity), 0) as available_stock
            FROM products p
            LEFT JOIN inventory i ON p.product_id = i.product_id AND i.status = 'active'
            GROUP BY p.product_id, p.name
            LIMIT 3
        """)
        
        results = cursor.fetchall()
        for row in results:
            print(f"✅ {row['name']}: {row['batch_count']} batches, {row['available_stock']} available")
        
        # Test pagination utility
        print("\n📄 PAGINATION VERIFICATION")
        try:
            from utils.pagination import Pagination, paginate_query_results
            
            # Test pagination with products
            cursor.execute("SELECT COUNT(*) FROM products")
            total_count = cursor.fetchone()[0]
            
            pagination = Pagination(page=1, per_page=10, total_count=total_count)
            print(f"✅ Pagination utility: {pagination.pages} pages, {total_count} total items")
            
        except ImportError as e:
            print(f"❌ Pagination utility: {str(e)}")
        
        conn.close()
        print("\n🎉 Database verification completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Database error: {str(e)}")
        return False

def test_template_files():
    """Test critical template files exist"""
    print("\n📄 TEMPLATE VERIFICATION")
    print("=" * 50)
    
    templates = [
        'templates/products/index.html',
        'templates/inventory/index.html',
        'templates/inventory/product_inventory.html',
        'templates/orders/index.html',
        'templates/base.html'
    ]
    
    for template in templates:
        if os.path.exists(template):
            print(f"✅ Template {template}: Found")
        else:
            print(f"❌ Template {template}: Missing")

def main():
    """Run database verification"""
    print(f"🧪 SYSTEM VERIFICATION - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    db_success = test_database()
    test_template_files()
    
    print("\n" + "=" * 60)
    if db_success:
        print("✅ Database verification completed successfully!")
        print("🚀 System is ready for testing!")
    else:
        print("❌ Database verification failed!")
    
    return db_success

if __name__ == "__main__":
    main()
