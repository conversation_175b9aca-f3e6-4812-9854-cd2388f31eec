#!/usr/bin/env python3
import sqlite3

# Simple database test
conn = sqlite3.connect('instance/medivent.db')
conn.row_factory = sqlite3.Row
cursor = conn.cursor()

print("=== SIMPLE DATABASE TEST ===")

# Check if invoice_holds table exists
try:
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='invoice_holds'")
    table_exists = cursor.fetchone()
    if table_exists:
        print("✅ invoice_holds table exists")
    else:
        print("❌ invoice_holds table does NOT exist")
        conn.close()
        exit(1)
except Exception as e:
    print(f"❌ Error checking table: {e}")
    conn.close()
    exit(1)

# Check table structure
print("\n📋 Table structure:")
cursor.execute("PRAGMA table_info(invoice_holds)")
columns = cursor.fetchall()
for col in columns:
    print(f"  {col['name']} ({col['type']})")

# Check current data
print("\n📊 Current data:")
cursor.execute('SELECT COUNT(*) as count FROM invoice_holds')
total = cursor.fetchone()['count']
print(f"Total records: {total}")

cursor.execute('SELECT COUNT(*) as count FROM invoice_holds WHERE status = "active"')
active = cursor.fetchone()['count']
print(f"Active holds: {active}")

# Show recent records
print("\n📝 Recent records:")
cursor.execute('SELECT order_id, hold_reason, status, hold_date FROM invoice_holds ORDER BY hold_date DESC LIMIT 3')
recent = cursor.fetchall()
for record in recent:
    print(f"  {record['order_id']} | {record['status']} | {record['hold_date']}")

# Check specific order
print("\n🔍 Checking ORD00000243:")
cursor.execute('SELECT * FROM invoice_holds WHERE order_id = ?', ('ORD00000243',))
ord243_hold = cursor.fetchone()
if ord243_hold:
    print("✅ Hold record found:")
    for key in ord243_hold.keys():
        print(f"  {key}: {ord243_hold[key]}")
else:
    print("❌ No hold record found")

# Check order status
cursor.execute('SELECT order_id, status FROM orders WHERE order_id = ?', ('ORD00000243',))
ord243_order = cursor.fetchone()
if ord243_order:
    print(f"Order status: {ord243_order['status']}")
else:
    print("Order not found")

conn.close()
print("\n✅ Test complete")
