#!/usr/bin/env python3
"""
Debug API Calls - Check why modal isn't loading real data
"""

import requests
import json

def test_order_api_directly():
    """Test the order API directly"""
    print("🔍 TESTING ORDER API DIRECTLY")
    print("=" * 50)
    
    # Test with ORD00000155 (visible in screenshot)
    order_id = 'ORD00000155'
    
    try:
        url = f'http://127.0.0.1:5001/api/order-details/{order_id}'
        print(f"Testing: {url}")
        
        response = requests.get(url, timeout=10)
        print(f"Status: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"Success: {data.get('success')}")
                
                if data.get('success'):
                    print("\n📊 ORDER DATA:")
                    order = data.get('order', {})
                    print(f"   Order ID: {order.get('order_id')}")
                    print(f"   Customer: {order.get('customer_name')}")
                    print(f"   Status: {order.get('status')}")
                    print(f"   Amount: {order.get('order_amount')}")
                    
                    print("\n📦 ORDER ITEMS:")
                    items = data.get('order_items', [])
                    print(f"   Total items: {len(items)}")
                    
                    for i, item in enumerate(items):
                        print(f"   Item {i+1}:")
                        print(f"     Product: {item.get('product_name')}")
                        print(f"     Quantity: {item.get('quantity')}")
                        print(f"     Price: {item.get('unit_price')}")
                        print(f"     Total: {float(item.get('quantity', 0)) * float(item.get('unit_price', 0))}")
                    
                    return True, data
                else:
                    print(f"❌ API Error: {data.get('message')}")
                    return False, data
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON Error: {e}")
                print(f"Raw response: {response.text[:500]}")
                return False, None
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text[:500]}")
            return False, None
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False, None

def test_qr_api_directly():
    """Test the QR API directly"""
    print("\n🔍 TESTING QR API DIRECTLY")
    print("=" * 50)
    
    order_id = 'ORD00000155'
    
    try:
        url = f'http://127.0.0.1:5001/api/order-qr-code/{order_id}'
        print(f"Testing: {url}")
        
        response = requests.get(url, timeout=10)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"Success: {data.get('success')}")
                
                if data.get('success'):
                    qr_data = data.get('qr_code', {})
                    base64_len = len(qr_data.get('base64', ''))
                    print(f"✅ QR Code generated: {base64_len} characters")
                    return True, data
                else:
                    print(f"❌ QR Error: {data.get('message')}")
                    return False, data
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON Error: {e}")
                print(f"Raw response: {response.text[:200]}")
                return False, None
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False, None

def check_database_directly():
    """Check database directly"""
    print("\n🔍 CHECKING DATABASE DIRECTLY")
    print("=" * 50)
    
    try:
        import sqlite3
        import os
        
        db_path = 'instance/medivent.db'
        if not os.path.exists(db_path):
            print(f"❌ Database not found: {db_path}")
            return False
            
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Check order ORD00000155
        order_id = 'ORD00000155'
        print(f"Checking order: {order_id}")
        
        cursor.execute("SELECT * FROM orders WHERE order_id = ?", (order_id,))
        order = cursor.fetchone()
        
        if order:
            print("✅ Order found in database:")
            print(f"   Customer: {order['customer_name']}")
            print(f"   Status: {order['status']}")
            print(f"   Amount: {order['order_amount']}")
            
            # Check order items
            cursor.execute("""
                SELECT oi.*, p.name as product_name, p.strength, p.manufacturer
                FROM order_items oi
                LEFT JOIN products p ON oi.product_id = p.product_id
                WHERE oi.order_id = ?
            """, (order_id,))
            
            items = cursor.fetchall()
            print(f"\n📦 Order Items: {len(items)} found")
            
            for i, item in enumerate(items):
                print(f"   Item {i+1}:")
                print(f"     Product: {item['product_name'] or 'Unknown'}")
                print(f"     Quantity: {item['quantity']}")
                print(f"     Price: {item['unit_price']}")
            
            conn.close()
            return True
        else:
            print(f"❌ Order {order_id} not found in database")
            conn.close()
            return False
            
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 DEBUGGING API CALLS")
    print("=" * 70)
    
    # Check database first
    db_ok = check_database_directly()
    
    # Test APIs
    api_ok, api_data = test_order_api_directly()
    qr_ok, qr_data = test_qr_api_directly()
    
    print("\n" + "=" * 70)
    print("📊 DEBUG RESULTS")
    print("=" * 70)
    
    print(f"Database: {'✅ OK' if db_ok else '❌ ISSUES'}")
    print(f"Order API: {'✅ OK' if api_ok else '❌ ISSUES'}")
    print(f"QR API: {'✅ OK' if qr_ok else '❌ ISSUES'}")
    
    if api_ok and api_data:
        print("\n🎯 API IS WORKING!")
        print("   The issue is likely in the JavaScript")
        print("   Need to check browser console for errors")
        
        # Show what the API returns
        items = api_data.get('order_items', [])
        if items:
            print(f"\n📦 API RETURNS {len(items)} ITEMS:")
            for item in items:
                print(f"   - {item.get('product_name')} (Qty: {item.get('quantity')})")
        
        return True
    else:
        print("\n❌ API ISSUES FOUND")
        print("   Need to fix API endpoints first")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🔧 NEXT STEPS:")
        print("   1. Open browser Developer Tools (F12)")
        print("   2. Go to Console tab")
        print("   3. Click 'View Details' on order ORD00000155")
        print("   4. Look for JavaScript errors or API call failures")
        print("   5. Check Network tab for failed requests")
    else:
        print("\n❌ Fix API issues first")
