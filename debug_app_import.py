#!/usr/bin/env python3
"""
Debug app.py import issues
"""

import sys
import traceback

def test_app_import():
    """Test importing the main app"""
    try:
        print("🔍 Testing app.py import...")
        
        # Test importing app module
        import app
        print("✅ app.py imported successfully")
        
        # Check if create_app function exists
        if hasattr(app, 'create_app'):
            print("✅ create_app function found")
            
            # Try to create app
            flask_app = app.create_app()
            print("✅ Flask app created successfully")
            
            # Check registered blueprints
            with flask_app.app_context():
                blueprints = list(flask_app.blueprints.keys())
                print(f"✅ Registered blueprints ({len(blueprints)}): {blueprints}")
                
                # Check for API blueprint specifically
                if 'api' in blueprints:
                    print("✅ API blueprint is registered")
                    
                    # List API routes
                    rules = list(flask_app.url_map.iter_rules())
                    api_routes = [rule for rule in rules if rule.rule.startswith('/api/')]
                    print(f"✅ Found {len(api_routes)} API routes:")
                    for rule in api_routes:
                        print(f"   {rule.rule} -> {rule.endpoint}")
                else:
                    print("❌ API blueprint is NOT registered")
        else:
            print("❌ create_app function not found")
            
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("Traceback:")
        traceback.print_exc()
        return False

def test_direct_import():
    """Test importing API endpoints directly"""
    try:
        print("\n🔍 Testing direct API import...")
        
        from api_endpoints import api_bp
        print("✅ API blueprint imported directly")
        print(f"   Blueprint name: {api_bp.name}")
        print(f"   Blueprint url_prefix: {api_bp.url_prefix}")
        
        return True
        
    except Exception as e:
        print(f"❌ Direct import error: {e}")
        print("Traceback:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 APP IMPORT DEBUG TEST")
    print("=" * 50)
    
    direct_ok = test_direct_import()
    app_ok = test_app_import()
    
    print("\n📊 SUMMARY:")
    print(f"Direct API Import: {'✅ OK' if direct_ok else '❌ Failed'}")
    print(f"App Import: {'✅ OK' if app_ok else '❌ Failed'}")
    
    if direct_ok and app_ok:
        print("\n🎉 All imports successful!")
    else:
        print("\n⚠️ Import issues detected.")
    
    sys.exit(0 if (direct_ok and app_ok) else 1)
