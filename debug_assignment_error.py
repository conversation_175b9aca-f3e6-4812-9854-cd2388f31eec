#!/usr/bin/env python3
"""
Debug the assignment dashboard error in detail
"""

import sys
import traceback
from datetime import datetime

def test_assignment_dashboard_route():
    """Test the assignment dashboard route directly"""
    print("🔍 DEBUGGING ASSIGNMENT DASHBOARD ROUTE")
    print("=" * 60)
    
    try:
        # Import Flask app components
        sys.path.append('.')
        from app import app
        from utils.db import get_db
        
        with app.app_context():
            db = get_db()
            
            print("✅ Database connection successful")
            
            # Execute the same query as the route
            ready_orders = db.execute('''
                SELECT o.*, c.name as customer_name, c.address as customer_address,
                       c.phone as customer_phone, c.city as customer_city
                FROM orders o
                LEFT JOIN customers c ON o.customer_id = c.customer_id
                WHERE o.status = 'Ready for Pickup' AND o.warehouse_status = 'packed'
                AND (o.rider_id IS NULL OR o.rider_id = '')
                ORDER BY o.priority_level DESC, o.packed_at ASC
            ''').fetchall()
            
            print(f"✅ Found {len(ready_orders)} ready orders")
            
            # Process orders exactly like the route does
            processed_orders = []
            for i, order in enumerate(ready_orders):
                print(f"\n📦 Processing Order {i+1}:")
                order_dict = dict(order)
                
                # Check packed_at specifically
                packed_at = order_dict.get('packed_at')
                print(f"  order_id: {order_dict.get('order_id')}")
                print(f"  packed_at: {packed_at} (type: {type(packed_at).__name__})")
                
                # Test the datetime fields processing
                datetime_fields = ['packed_at', 'order_date', 'last_updated', 'dispatch_date',
                                 'delivery_date', 'approval_date', 'pickup_scheduled_at',
                                 'picked_up_at', 'out_for_delivery_at', 'delivered_at']

                for field in datetime_fields:
                    if field in order_dict and order_dict[field] is not None:
                        original_value = order_dict[field]
                        # Ensure the field is a string for consistent template processing
                        if not isinstance(order_dict[field], str):
                            order_dict[field] = str(order_dict[field])
                            print(f"  Converted {field}: {original_value} -> {order_dict[field]}")

                processed_orders.append(order_dict)
            
            # Test the template filter directly
            print(f"\n🧪 TESTING TEMPLATE FILTER:")
            format_datetime = app.jinja_env.filters.get('format_datetime')
            if format_datetime:
                print("✅ format_datetime filter found")
                
                # Test with actual data
                for order in processed_orders[:2]:  # Test first 2 orders
                    packed_at = order.get('packed_at')
                    if packed_at:
                        try:
                            result = format_datetime(packed_at, '%Y-%m-%d %H:%M')
                            print(f"  ✅ Filter test: '{packed_at}' -> '{result}'")
                        except Exception as e:
                            print(f"  ❌ Filter error: '{packed_at}' -> {e}")
                            traceback.print_exc()
            else:
                print("❌ format_datetime filter NOT found")
            
            # Test template rendering
            print(f"\n🎨 TESTING TEMPLATE RENDERING:")
            try:
                from flask import render_template_string
                
                # Simple template test
                test_template = """
                {% for order in orders %}
                <p>Order: {{ order.order_id }} - Packed: {{ order.packed_at | format_datetime('%Y-%m-%d %H:%M') if order.packed_at else 'N/A' }}</p>
                {% endfor %}
                """
                
                result = render_template_string(test_template, orders=processed_orders[:1])
                print("✅ Template rendering successful")
                print(f"Result: {result.strip()}")
                
            except Exception as e:
                print(f"❌ Template rendering error: {e}")
                traceback.print_exc()
                
    except Exception as e:
        print(f"❌ Route test error: {e}")
        traceback.print_exc()

def test_filter_registration():
    """Test if the filter is properly registered"""
    print("\n🔧 TESTING FILTER REGISTRATION")
    print("=" * 60)
    
    try:
        sys.path.append('.')
        from app import app
        
        with app.app_context():
            filters = app.jinja_env.filters
            print(f"Total filters registered: {len(filters)}")
            
            # Check for our filter
            if 'format_datetime' in filters:
                print("✅ format_datetime filter is registered")
                filter_func = filters['format_datetime']
                print(f"Filter function: {filter_func}")
                
                # Test the filter directly
                test_values = [
                    "2025-07-31 07:38:21",
                    "2025-07-26 11:17:39.885061",
                    "2025-07-28",
                    None,
                    ""
                ]
                
                for value in test_values:
                    try:
                        result = filter_func(value, '%Y-%m-%d %H:%M')
                        print(f"  ✅ '{value}' -> '{result}'")
                    except Exception as e:
                        print(f"  ❌ '{value}' -> ERROR: {e}")
                        
            else:
                print("❌ format_datetime filter is NOT registered")
                print("Available filters:", list(filters.keys())[:10])
                
    except Exception as e:
        print(f"❌ Filter registration test error: {e}")
        traceback.print_exc()

def main():
    """Main debug function"""
    print("🐛 ASSIGNMENT DASHBOARD ERROR DEBUGGING")
    print("=" * 80)
    
    # Test filter registration
    test_filter_registration()
    
    # Test the route
    test_assignment_dashboard_route()

if __name__ == "__main__":
    main()
