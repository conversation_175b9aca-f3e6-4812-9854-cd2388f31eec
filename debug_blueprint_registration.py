#!/usr/bin/env python3
"""
Debug Blueprint Registration
Test and fix blueprint import/registration issues
"""

import sys
import traceback

def test_blueprint_import():
    """Test if the blueprint can be imported"""
    print("🔍 Testing Blueprint Import")
    print("-" * 40)
    
    try:
        from routes.partial_pending import partial_pending_bp
        print("✅ Blueprint imported successfully")
        print(f"   Blueprint name: {partial_pending_bp.name}")
        print(f"   URL prefix: {partial_pending_bp.url_prefix}")
        
        # Check routes
        routes = []
        for rule in partial_pending_bp.url_map.iter_rules():
            routes.append(f"{rule.rule} -> {rule.endpoint}")
        
        print(f"   Routes registered: {len(routes)}")
        for route in routes[:5]:  # Show first 5 routes
            print(f"     {route}")
        
        return True, partial_pending_bp
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        traceback.print_exc()
        return False, None
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")
        traceback.print_exc()
        return False, None

def test_flask_app_registration():
    """Test blueprint registration in Flask app"""
    print("\n🔍 Testing Flask App Registration")
    print("-" * 40)
    
    try:
        # Import Flask app components
        from flask import Flask
        
        # Create test app
        test_app = Flask(__name__)
        test_app.config['SECRET_KEY'] = 'test'
        
        # Try to import and register blueprint
        from routes.partial_pending import partial_pending_bp
        test_app.register_blueprint(partial_pending_bp)
        
        print("✅ Blueprint registered in test app successfully")
        
        # Check registered endpoints
        endpoints = []
        for rule in test_app.url_map.iter_rules():
            if 'partial_pending' in rule.endpoint:
                endpoints.append(f"{rule.rule} -> {rule.endpoint}")
        
        print(f"   Partial pending endpoints: {len(endpoints)}")
        for endpoint in endpoints:
            print(f"     {endpoint}")
        
        return True
        
    except Exception as e:
        print(f"❌ Registration Error: {e}")
        traceback.print_exc()
        return False

def check_dependencies():
    """Check if all required dependencies are available"""
    print("\n🔍 Checking Dependencies")
    print("-" * 40)
    
    required_modules = [
        'flask',
        'flask_login', 
        'database',
        'datetime',
        'json',
        'sqlite3'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} - MISSING")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n⚠️  Missing modules: {missing_modules}")
        return False
    else:
        print("\n✅ All dependencies available")
        return True

def fix_blueprint_registration():
    """Fix blueprint registration issues"""
    print("\n🔧 Fixing Blueprint Registration")
    print("-" * 40)
    
    try:
        # Check if the issue is in app.py registration
        with open('app.py', 'r', encoding='utf-8') as f:
            app_content = f.read()
        
        # Check if blueprint is properly registered
        if 'partial_pending_bp' in app_content and 'register_blueprint' in app_content:
            print("✅ Blueprint registration found in app.py")
            
            # Check if there are any syntax issues around the registration
            lines = app_content.split('\n')
            for i, line in enumerate(lines):
                if 'partial_pending_bp' in line:
                    print(f"   Line {i+1}: {line.strip()}")
            
            return True
        else:
            print("❌ Blueprint registration not found in app.py")
            return False
            
    except Exception as e:
        print(f"❌ Error checking app.py: {e}")
        return False

def create_minimal_blueprint():
    """Create a minimal working blueprint for testing"""
    print("\n🔧 Creating Minimal Test Blueprint")
    print("-" * 40)
    
    minimal_blueprint_content = '''#!/usr/bin/env python3
"""
Minimal Partial Pending Blueprint for Testing
"""

from flask import Blueprint, render_template
from flask_login import login_required

# Create blueprint
partial_pending_bp = Blueprint('partial_pending', __name__, url_prefix='/partial-pending')

@partial_pending_bp.route('/')
@login_required
def index():
    """Minimal partial pending dashboard"""
    return "<h1>Partial Pending Dashboard</h1><p>System is working!</p>"

@partial_pending_bp.route('/test')
def test():
    """Test route"""
    return "Partial Pending Blueprint is working!"
'''
    
    try:
        # Backup original file
        import shutil
        shutil.copy('routes/partial_pending.py', 'routes/partial_pending_backup.py')
        print("✅ Original blueprint backed up")
        
        # Create minimal version
        with open('routes/partial_pending_minimal.py', 'w', encoding='utf-8') as f:
            f.write(minimal_blueprint_content)
        print("✅ Minimal blueprint created")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating minimal blueprint: {e}")
        return False

def main():
    """Run all debugging steps"""
    print("🔧 BLUEPRINT REGISTRATION DEBUGGING")
    print("=" * 50)
    
    # Step 1: Check dependencies
    deps_ok = check_dependencies()
    
    # Step 2: Test blueprint import
    import_ok, blueprint = test_blueprint_import()
    
    # Step 3: Test Flask app registration
    if import_ok:
        registration_ok = test_flask_app_registration()
    else:
        registration_ok = False
    
    # Step 4: Check app.py registration
    app_registration_ok = fix_blueprint_registration()
    
    # Summary
    print("\n" + "=" * 50)
    print("🎯 DEBUGGING SUMMARY")
    print("=" * 50)
    
    print(f"Dependencies: {'✅' if deps_ok else '❌'}")
    print(f"Blueprint Import: {'✅' if import_ok else '❌'}")
    print(f"Flask Registration: {'✅' if registration_ok else '❌'}")
    print(f"App.py Registration: {'✅' if app_registration_ok else '❌'}")
    
    if all([deps_ok, import_ok, registration_ok, app_registration_ok]):
        print("\n🎉 All checks passed! Blueprint should work.")
        print("💡 The issue might be with Flask app startup order.")
        print("🔧 Try restarting the Flask application.")
    else:
        print("\n⚠️  Issues found. Creating minimal blueprint for testing...")
        create_minimal_blueprint()
        print("\n💡 Use the minimal blueprint to test basic functionality.")

if __name__ == "__main__":
    main()
