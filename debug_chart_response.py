import requests
import time

print('🔍 DEBUGGING CHART API RESPONSES')
print('=' * 50)

time.sleep(2)

base_url = 'http://127.0.0.1:5001'
endpoint = '/finance/api/chart-data/division-breakdown'

try:
    response = requests.get(base_url + endpoint, timeout=10)
    print(f'Status: {response.status_code}')
    print(f'Headers: {dict(response.headers)}')
    print(f'Content-Type: {response.headers.get("content-type", "Not set")}')
    print(f'Content-Length: {len(response.content)}')
    print(f'Raw content: {response.content}')
    print(f'Text content: {response.text}')
    
    if response.text.strip():
        try:
            data = response.json()
            print(f'JSON data: {data}')
        except Exception as e:
            print(f'JSON parse error: {e}')
    else:
        print('❌ Empty response!')
        
except Exception as e:
    print(f'❌ Request error: {e}')
