#!/usr/bin/env python3
"""
Debug Critical Errors - Phase 1 Investigation
Test both order details API and QR code generation issues
"""

import requests
import sqlite3
import json
import os

def test_database_connection():
    """Test database connection and check for order ORD00000155"""
    print("🔍 TESTING DATABASE CONNECTION")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        conn.row_factory = sqlite3.Row
        
        # Check if order exists
        order = conn.execute('SELECT * FROM orders WHERE order_id = ?', ('ORD00000155',)).fetchone()
        if order:
            print('✅ Order ORD00000155 found in database')
            print(f'   Customer: {order["customer_name"]}')
            print(f'   Amount: Rs.{order["order_amount"]}')
            print(f'   Status: {order["status"]}')
            print(f'   Date: {order["order_date"]}')
        else:
            print('❌ Order ORD00000155 NOT found in database')
            
        # List some recent orders
        orders = conn.execute('SELECT order_id, customer_name FROM orders ORDER BY order_id DESC LIMIT 5').fetchall()
        print(f'\n📋 Recent orders in database:')
        for o in orders:
            print(f'   {o["order_id"]} - {o["customer_name"]}')
            
        # Check order items
        items = conn.execute('SELECT * FROM order_items WHERE order_id = ?', ('ORD00000155',)).fetchall()
        print(f'\n📦 Order items for ORD00000155: {len(items)} items found')
        
        conn.close()
        return True
        
    except Exception as e:
        print(f'❌ Database error: {e}')
        return False

def test_qr_dependencies():
    """Test QR code dependencies"""
    print("\n🔍 TESTING QR CODE DEPENDENCIES")
    print("=" * 50)
    
    try:
        import qrcode
        print('✅ qrcode module available')
        print(f'   Version: {qrcode.__version__}')
    except ImportError as e:
        print(f'❌ qrcode module not available: {e}')
        return False

    try:
        from PIL import Image
        print('✅ PIL (Pillow) available')
    except ImportError as e:
        print(f'❌ PIL (Pillow) not available: {e}')
        return False

    try:
        from utils.qr_code_generator import generate_order_qr_code
        print('✅ QR code generator module available')
        return True
    except ImportError as e:
        print(f'❌ QR code generator not available: {e}')
        return False

def test_server_connection():
    """Test if Flask server is running"""
    print("\n🔍 TESTING SERVER CONNECTION")
    print("=" * 50)
    
    try:
        response = requests.get('http://127.0.0.1:5001/', timeout=5)
        print(f'✅ Server is running - Status: {response.status_code}')
        return True
    except Exception as e:
        print(f'❌ Server not running: {e}')
        return False

def test_api_endpoints():
    """Test the specific API endpoints causing issues"""
    print("\n🔍 TESTING API ENDPOINTS")
    print("=" * 50)
    
    # Test order details API
    try:
        print('Testing order details API...')
        response = requests.get('http://127.0.0.1:5001/api/order-details/ORD00000155', timeout=10)
        print(f'   Status: {response.status_code}')
        
        if response.status_code == 200:
            data = response.json()
            print(f'   Success: {data.get("success")}')
            if data.get('success'):
                print('   ✅ Order details API working')
            else:
                print(f'   ❌ API error: {data.get("message")}')
        else:
            print(f'   ❌ HTTP Error: {response.status_code}')
            print(f'   Response: {response.text[:200]}')
            
    except Exception as e:
        print(f'   ❌ Error testing order details: {e}')

    # Test QR code API
    try:
        print('\nTesting QR code API...')
        response = requests.get('http://127.0.0.1:5001/api/order-qr-code/ORD00000155', timeout=10)
        print(f'   Status: {response.status_code}')
        
        if response.status_code == 200:
            data = response.json()
            print(f'   Success: {data.get("success")}')
            if data.get('success'):
                print('   ✅ QR code API working')
            else:
                print(f'   ❌ QR API error: {data.get("message")}')
        else:
            print(f'   ❌ HTTP Error: {response.status_code}')
            print(f'   Response: {response.text[:200]}')
            
    except Exception as e:
        print(f'   ❌ Error testing QR code: {e}')

def test_print_address_route():
    """Test the print address route"""
    print("\n🔍 TESTING PRINT ADDRESS ROUTE")
    print("=" * 50)
    
    try:
        response = requests.get('http://127.0.0.1:5001/orders/ORD00000155/print-address', timeout=10)
        print(f'   Status: {response.status_code}')
        
        if response.status_code == 200:
            print('   ✅ Print address route accessible')
            
            # Check if QR code loading JavaScript is present
            if 'loadQRCode' in response.text:
                print('   ✅ QR code loading JavaScript included')
            else:
                print('   ⚠️ QR code loading JavaScript not found')
                
        else:
            print(f'   ❌ HTTP Error: {response.status_code}')
            
    except Exception as e:
        print(f'   ❌ Error testing print address: {e}')

def main():
    """Run all tests"""
    print("🚨 CRITICAL ERRORS INVESTIGATION - PHASE 1")
    print("=" * 60)
    print("Testing both 'Unable to Load Order Details' and 'QR Code unavailable' errors")
    print("=" * 60)
    
    # Run all tests
    db_ok = test_database_connection()
    qr_ok = test_qr_dependencies()
    server_ok = test_server_connection()
    
    if server_ok:
        test_api_endpoints()
        test_print_address_route()
    
    # Summary
    print("\n📊 INVESTIGATION SUMMARY")
    print("=" * 50)
    print(f"Database Connection: {'✅ OK' if db_ok else '❌ FAILED'}")
    print(f"QR Dependencies: {'✅ OK' if qr_ok else '❌ FAILED'}")
    print(f"Server Running: {'✅ OK' if server_ok else '❌ FAILED'}")
    
    if not db_ok:
        print("\n🔧 RECOMMENDED ACTIONS:")
        print("1. Check database file exists at instance/medivent.db")
        print("2. Verify order ORD00000155 exists in database")
        
    if not qr_ok:
        print("\n🔧 RECOMMENDED ACTIONS:")
        print("1. Install QR code dependencies: pip install qrcode[pil] pillow")
        print("2. Check utils/qr_code_generator.py file exists")
        
    if not server_ok:
        print("\n🔧 RECOMMENDED ACTIONS:")
        print("1. Start Flask server: python app.py")
        print("2. Check server is running on port 5001")

if __name__ == "__main__":
    main()
