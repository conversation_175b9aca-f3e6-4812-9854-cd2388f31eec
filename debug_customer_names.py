#!/usr/bin/env python3
"""
Debug script to check customer names in the database
"""

import sqlite3
import os

def check_customer_names():
    """Check customer names in the database"""
    
    # Check if database exists
    db_path = 'instance/medivent.db'
    if not os.path.exists(db_path):
        print("❌ Database not found at instance/medivent.db")
        return
    
    print("🔍 CUSTOMER NAMES DEBUG ANALYSIS")
    print("=" * 50)
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Check total orders
        cursor.execute("SELECT COUNT(*) as total FROM orders")
        total_orders = cursor.fetchone()['total']
        print(f"📊 Total orders in database: {total_orders}")
        
        # Check orders with customer names
        cursor.execute("SELECT COUNT(*) as total FROM orders WHERE customer_name IS NOT NULL AND customer_name != ''")
        orders_with_names = cursor.fetchone()['total']
        print(f"👤 Orders with customer names: {orders_with_names}")
        
        # Check orders without customer names
        cursor.execute("SELECT COUNT(*) as total FROM orders WHERE customer_name IS NULL OR customer_name = ''")
        orders_without_names = cursor.fetchone()['total']
        print(f"❌ Orders without customer names: {orders_without_names}")
        
        # Sample orders with customer names
        print("\n📋 SAMPLE ORDERS WITH CUSTOMER NAMES:")
        cursor.execute("""
            SELECT order_id, customer_name, order_amount, payment_status, status 
            FROM orders 
            WHERE customer_name IS NOT NULL AND customer_name != '' 
            LIMIT 5
        """)
        orders_with_names = cursor.fetchall()
        
        for order in orders_with_names:
            print(f"  Order {order['order_id']}: '{order['customer_name']}' - Rs.{order['order_amount']} - {order['payment_status']}")
        
        # Sample orders without customer names
        print("\n❌ SAMPLE ORDERS WITHOUT CUSTOMER NAMES:")
        cursor.execute("""
            SELECT order_id, customer_name, order_amount, payment_status, status 
            FROM orders 
            WHERE customer_name IS NULL OR customer_name = '' 
            LIMIT 5
        """)
        orders_without_names = cursor.fetchall()
        
        for order in orders_without_names:
            customer_display = order['customer_name'] if order['customer_name'] else 'NULL/EMPTY'
            print(f"  Order {order['order_id']}: '{customer_display}' - Rs.{order['order_amount']} - {order['payment_status']}")
        
        # Check payment collection query specifically
        print("\n🔍 PAYMENT COLLECTION QUERY TEST:")
        cursor.execute("""
            SELECT
                o.order_id, o.customer_name,
                COALESCE(o.order_amount, 0) as order_amount,
                o.order_date,
                o.payment_status, o.customer_phone, o.sales_agent, o.division,
                CASE
                    WHEN julianday('now') - julianday(o.order_date) > 90 THEN 'high'
                    WHEN julianday('now') - julianday(o.order_date) > 60 THEN 'medium'
                    ELSE 'low'
                END as priority_level,
                CAST(julianday('now') - julianday(o.order_date) AS INTEGER) as days_pending
            FROM orders o
            WHERE o.status != 'Cancelled'
            AND o.payment_status IN ('pending', 'paid', 'partial')
            LIMIT 5
        """)
        payment_data = cursor.fetchall()
        
        print(f"Payment collection query returned {len(payment_data)} records:")
        for payment in payment_data:
            customer_display = payment['customer_name'] if payment['customer_name'] else 'NULL/EMPTY'
            print(f"  Order {payment['order_id']}: Customer='{customer_display}' Amount={payment['order_amount']} Status={payment['payment_status']}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    check_customer_names()
