#!/usr/bin/env python3
"""
Debug script to investigate datetime fields in database and strftime error
"""

import sqlite3
import sys
import os
from datetime import datetime

def check_database_files():
    """Check which database files exist"""
    print("🔍 CHECKING DATABASE FILES")
    print("=" * 50)
    
    db_files = [
        'database.db',
        'medivent.db', 
        'medivent_erp.db',
        'instance/medivent.db'
    ]
    
    for db_file in db_files:
        if os.path.exists(db_file):
            size = os.path.getsize(db_file)
            print(f"✅ {db_file} exists ({size} bytes)")
        else:
            print(f"❌ {db_file} not found")

def analyze_database_structure(db_path):
    """Analyze database structure and datetime fields"""
    print(f"\n🔍 ANALYZING DATABASE: {db_path}")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if orders table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='orders'")
        if not cursor.fetchone():
            print("❌ Orders table not found")
            return False
            
        # Get table structure
        cursor.execute('PRAGMA table_info(orders)')
        columns = cursor.fetchall()
        
        print("📋 ORDERS TABLE STRUCTURE:")
        datetime_columns = []
        for col in columns:
            col_name, col_type = col[1], col[2]
            if 'date' in col_name.lower() or 'at' in col_name.lower() or 'timestamp' in col_type.lower():
                datetime_columns.append(col_name)
                print(f"  📅 {col_name}: {col_type}")
            else:
                print(f"     {col_name}: {col_type}")
        
        # Check sample data
        print(f"\n📊 SAMPLE DATETIME DATA:")
        cursor.execute(f'SELECT order_id, {", ".join(datetime_columns)} FROM orders WHERE packed_at IS NOT NULL LIMIT 3')
        rows = cursor.fetchall()
        
        if not rows:
            print("❌ No orders with packed_at found")
            return False
            
        for i, row in enumerate(rows):
            print(f"\nOrder {i+1}: {row[0]}")
            for j, col_name in enumerate(datetime_columns):
                value = row[j+1] if j+1 < len(row) else None
                print(f"  {col_name}: {value} (type: {type(value).__name__})")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def test_assignment_dashboard_route():
    """Test the assignment dashboard route directly"""
    print(f"\n🧪 TESTING ASSIGNMENT DASHBOARD ROUTE")
    print("=" * 60)
    
    try:
        # Import Flask app components
        sys.path.append('.')
        from app import app
        from utils.db import get_db
        
        with app.app_context():
            db = get_db()
            
            # Execute the same query as the route
            ready_orders = db.execute('''
                SELECT o.*, c.name as customer_name, c.address as customer_address,
                       c.phone as customer_phone, c.city as customer_city
                FROM orders o
                LEFT JOIN customers c ON o.customer_id = c.customer_id
                WHERE o.status = 'Ready for Pickup' AND o.warehouse_status = 'packed'
                AND (o.rider_id IS NULL OR o.rider_id = '')
                ORDER BY o.priority_level DESC, o.packed_at ASC
                LIMIT 3
            ''').fetchall()
            
            print(f"Found {len(ready_orders)} ready orders")
            
            for i, order in enumerate(ready_orders):
                print(f"\nOrder {i+1}:")
                order_dict = dict(order)
                
                # Check packed_at specifically
                packed_at = order_dict.get('packed_at')
                print(f"  order_id: {order_dict.get('order_id')}")
                print(f"  packed_at: {packed_at} (type: {type(packed_at).__name__})")
                
                # Test datetime conversion
                if packed_at:
                    try:
                        if isinstance(packed_at, str):
                            converted = datetime.fromisoformat(packed_at.replace('Z', '+00:00'))
                            print(f"  ✅ Conversion successful: {converted}")
                        else:
                            print(f"  ⚠️ Not a string, type: {type(packed_at)}")
                    except Exception as e:
                        print(f"  ❌ Conversion failed: {e}")
                        
    except Exception as e:
        print(f"❌ Route test error: {e}")

def main():
    """Main debug function"""
    print("🐛 DATETIME STRFTIME ERROR INVESTIGATION")
    print("=" * 80)
    
    # Check database files
    check_database_files()
    
    # Analyze each database
    db_files = ['instance/medivent.db', 'database.db', 'medivent.db']
    
    for db_file in db_files:
        if os.path.exists(db_file):
            if analyze_database_structure(db_file):
                break
    
    # Test the actual route
    test_assignment_dashboard_route()

if __name__ == "__main__":
    main()
