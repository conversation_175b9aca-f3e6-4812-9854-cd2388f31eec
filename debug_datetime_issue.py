#!/usr/bin/env python3
"""
Debug the datetime strftime issue in assignment dashboard
"""

import sqlite3
from datetime import datetime

def analyze_datetime_issue():
    """Analyze the datetime issue in the database"""
    
    print("🔍 ANALYZING DATETIME ISSUE")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        # Check orders table schema
        print("1. Orders table datetime columns:")
        cursor.execute("PRAGMA table_info(orders)")
        columns = cursor.fetchall()
        
        datetime_columns = []
        for col in columns:
            col_name, col_type = col[1], col[2]
            if 'date' in col_name.lower() or 'at' in col_name.lower():
                datetime_columns.append((col_name, col_type))
                print(f"   📅 {col_name:<20} → {col_type}")
        
        # Check sample data from orders table
        print("\n2. Sample order data with datetime fields:")
        cursor.execute("""
            SELECT order_id, packed_at, order_date, last_updated, dispatch_date
            FROM orders
            WHERE packed_at IS NOT NULL
            LIMIT 3
        """)
        
        sample_orders = cursor.fetchall()
        
        if sample_orders:
            for order in sample_orders:
                order_id, packed_at, order_date, last_updated, dispatch_date = order
                print(f"   Order {order_id}:")
                print(f"     packed_at: {packed_at} (type: {type(packed_at).__name__})")
                print(f"     order_date: {order_date} (type: {type(order_date).__name__})")
                print(f"     last_updated: {last_updated} (type: {type(last_updated).__name__})")
                print(f"     dispatch_date: {dispatch_date} (type: {type(dispatch_date).__name__})")
                print()
        else:
            print("   ⚠️  No orders with packed_at found")
        
        # Test the exact query from assignment dashboard
        print("3. Testing assignment dashboard query:")
        cursor.execute('''
            SELECT o.order_id, o.packed_at, c.name as customer_name
            FROM orders o
            LEFT JOIN customers c ON o.customer_id = c.customer_id
            WHERE o.status = 'Ready for Pickup' AND o.warehouse_status = 'packed'
            AND (o.rider_id IS NULL OR o.rider_id = '')
            ORDER BY o.priority_level DESC, o.packed_at ASC
            LIMIT 2
        ''')
        
        test_orders = cursor.fetchall()
        
        if test_orders:
            for order in test_orders:
                order_id, packed_at, customer_name = order
                print(f"   Order {order_id}: packed_at = {packed_at} (type: {type(packed_at).__name__})")
                
                # Test if we can parse it as datetime
                if packed_at:
                    try:
                        if isinstance(packed_at, str):
                            # Try to parse string to datetime
                            dt = datetime.fromisoformat(packed_at.replace('Z', '+00:00'))
                            print(f"     ✅ Can parse as datetime: {dt}")
                            print(f"     ✅ strftime result: {dt.strftime('%Y-%m-%d %H:%M')}")
                        else:
                            print(f"     ✅ Already datetime object: {packed_at}")
                    except Exception as e:
                        print(f"     ❌ Cannot parse as datetime: {e}")
        else:
            print("   ⚠️  No orders found matching assignment criteria")
        
        conn.close()
        
    except Exception as e:
        print(f"💥 Database error: {e}")

def test_datetime_conversion():
    """Test datetime conversion solutions"""
    
    print("\n4. Testing datetime conversion solutions:")
    print("-" * 40)
    
    # Test different datetime string formats
    test_strings = [
        "2024-01-15 10:30:00",
        "2024-01-15T10:30:00",
        "2024-01-15 10:30:00.123456",
        "2024-01-15T10:30:00.123456Z",
    ]
    
    for test_str in test_strings:
        try:
            # Method 1: fromisoformat
            dt1 = datetime.fromisoformat(test_str.replace('Z', '+00:00'))
            print(f"   ✅ '{test_str}' → {dt1.strftime('%Y-%m-%d %H:%M')}")
        except Exception as e:
            print(f"   ❌ '{test_str}' → Error: {e}")

if __name__ == "__main__":
    analyze_datetime_issue()
    test_datetime_conversion()
