#!/usr/bin/env python3
"""
Debug script to test finance dashboard stats
"""

import sqlite3
import os
import sys
from datetime import datetime, <PERSON><PERSON><PERSON>

def debug_finance_dashboard():
    """Debug the finance dashboard stats issue"""
    
    # Check if database exists
    db_path = 'instance/medivent.db'
    if not os.path.exists(db_path):
        print("❌ Database not found at instance/medivent.db")
        return
    
    print("🔍 FINANCE DASHBOARD DEBUG ANALYSIS")
    print("=" * 60)
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Import the ModernFinanceManager from app.py
        sys.path.append('.')
        from app import ModernFinanceManager
        
        # Create finance manager
        finance_manager = ModernFinanceManager(conn)
        print("✅ ModernFinanceManager created successfully")
        
        # Test get_dashboard_stats method
        print("\n📊 Testing get_dashboard_stats()...")
        try:
            stats = finance_manager.get_dashboard_stats()
            print(f"✅ Stats retrieved successfully")
            print(f"📋 Stats type: {type(stats)}")
            print(f"📋 Stats keys: {list(stats.keys()) if isinstance(stats, dict) else 'Not a dict'}")
            
            # Check for hold_count specifically
            if isinstance(stats, dict):
                if 'hold_count' in stats:
                    print(f"✅ hold_count found: {stats['hold_count']}")
                else:
                    print("❌ hold_count NOT found in stats")
                    print(f"Available keys: {list(stats.keys())}")
            else:
                print(f"❌ Stats is not a dictionary: {type(stats)}")
                
        except Exception as e:
            print(f"❌ Error in get_dashboard_stats(): {e}")
            import traceback
            traceback.print_exc()
        
        # Check if invoice_holds table exists
        print("\n🗄️ Checking invoice_holds table...")
        try:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='invoice_holds'")
            table_exists = cursor.fetchone()
            if table_exists:
                print("✅ invoice_holds table exists")
                
                # Check table structure
                cursor.execute("PRAGMA table_info(invoice_holds)")
                columns = cursor.fetchall()
                print("📋 Table columns:")
                for col in columns:
                    print(f"   - {col['name']} ({col['type']})")
                
                # Check data
                cursor.execute("SELECT COUNT(*) as count FROM invoice_holds")
                count = cursor.fetchone()['count']
                print(f"📊 Total records in invoice_holds: {count}")
                
                # Check active holds
                cursor.execute("SELECT COUNT(*) as count FROM invoice_holds WHERE status = 'active'")
                active_count = cursor.fetchone()['count']
                print(f"📊 Active holds: {active_count}")
                
            else:
                print("❌ invoice_holds table does NOT exist")
                
        except Exception as e:
            print(f"❌ Error checking invoice_holds table: {e}")
        
        # Test _table_exists method
        print("\n🔍 Testing _table_exists method...")
        try:
            exists = finance_manager._table_exists('invoice_holds')
            print(f"✅ _table_exists('invoice_holds'): {exists}")
        except Exception as e:
            print(f"❌ Error in _table_exists: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_finance_dashboard()
