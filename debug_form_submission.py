#!/usr/bin/env python3
"""
Debug form submission to see exactly what's happening
"""

import requests
import time

def debug_form_submission():
    """Debug the exact form submission process"""
    print("🔍 DEBUGGING FORM SUBMISSION")
    print("=" * 60)
    
    base_url = "http://localhost:5001"
    session = requests.Session()
    
    try:
        # Login
        print("1. Logging in...")
        login_data = {'username': 'admin', 'password': 'admin123'}
        response = session.post(f"{base_url}/login", data=login_data, allow_redirects=False)
        
        if response.status_code != 302:
            print("❌ Login failed")
            return False
        
        print("✅ Login successful")
        
        # Get the order form page
        print("2. Getting order form page...")
        response = session.get(f"{base_url}/orders/new", timeout=10)
        print(f"   Order form status: {response.status_code}")
        
        if response.status_code != 200:
            print("❌ Cannot access order form")
            return False
        
        # Check what form fields are expected
        print("3. Analyzing form fields...")
        form_html = response.text
        
        # Look for required fields
        import re
        required_fields = re.findall(r'name="([^"]+)"[^>]*required', form_html)
        print(f"   Required fields found: {required_fields}")
        
        # Look for all input fields
        all_fields = re.findall(r'name="([^"]+)"', form_html)
        unique_fields = list(set(all_fields))
        print(f"   All form fields: {unique_fields}")
        
        # Check if there are any hidden fields or CSRF tokens
        csrf_fields = re.findall(r'name="csrf_token".*?value="([^"]+)"', form_html)
        if csrf_fields:
            print(f"   CSRF token found: {csrf_fields[0][:20]}...")
        
        # Submit form with comprehensive data
        print("4. Submitting form with comprehensive data...")
        
        # Prepare comprehensive form data
        form_data = {
            'customer_name': 'Debug Form Customer',
            'customer_address': 'Debug Form Address',
            'customer_phone': '555-DEBUG-FORM',
            'payment_method': 'cash',
            'po_number': 'DEBUG-FORM-001',
            'product_id[]': ['P001'],
            'quantity[]': ['1'],
            'foc_quantity[]': ['0']
        }
        
        # Add CSRF token if found
        if csrf_fields:
            form_data['csrf_token'] = csrf_fields[0]
        
        # Add any other fields that might be required
        additional_fields = {
            'sales_agent': 'admin',
            'delivery_date': '2025-08-05',
            'notes': 'Debug test order',
            'status': 'Placed'
        }
        
        for field, value in additional_fields.items():
            if field in unique_fields:
                form_data[field] = value
                print(f"   Added field: {field} = {value}")
        
        print(f"   Final form data: {form_data}")
        
        # Submit the form
        response = session.post(f"{base_url}/orders/new", 
                               data=form_data, 
                               timeout=15,
                               allow_redirects=False)
        
        print(f"   Response status: {response.status_code}")
        print(f"   Response headers: {dict(response.headers)}")
        
        if response.status_code == 302:
            redirect_url = response.headers.get('Location', '')
            print(f"   Redirect URL: {redirect_url}")
            
            if '/orders/' in redirect_url and redirect_url != '/orders/new':
                print("✅ Order creation successful!")
                return True
            else:
                print("⚠️  Redirected but not to order page")
                
                # Follow the redirect to see what page we end up on
                response = session.get(f"{base_url}{redirect_url}", timeout=10)
                print(f"   Final page status: {response.status_code}")
                
                # Check for error messages
                if 'alert-danger' in response.text:
                    error_match = re.search(r'alert-danger[^>]*>([^<]+)', response.text)
                    if error_match:
                        print(f"   Error message: {error_match.group(1).strip()}")
                
                return False
                
        elif response.status_code == 200:
            print("   Form returned 200 - checking response...")
            
            # Check for error messages in the response
            if 'alert-danger' in response.text:
                error_matches = re.findall(r'alert-danger[^>]*>([^<]+)', response.text)
                for error in error_matches:
                    print(f"   Error: {error.strip()}")
            
            if 'alert-warning' in response.text:
                warning_matches = re.findall(r'alert-warning[^>]*>([^<]+)', response.text)
                for warning in warning_matches:
                    print(f"   Warning: {warning.strip()}")
            
            return False
        else:
            print(f"   Unexpected status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_flask_logs():
    """Check Flask server logs for any errors"""
    print("\n📋 CHECKING FLASK SERVER LOGS")
    print("=" * 60)
    
    try:
        # Try to read from the Flask process
        import subprocess
        result = subprocess.run(['powershell', '-Command', 
                               'Get-Process python | Where-Object {$_.MainWindowTitle -like "*Flask*"}'], 
                              capture_output=True, text=True, timeout=5)
        
        if result.stdout:
            print("Flask processes found:")
            print(result.stdout)
        else:
            print("No Flask processes found in window titles")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking logs: {e}")
        return False

def main():
    """Run form submission debugging"""
    print("🐛 FORM SUBMISSION DEBUG SESSION")
    print("=" * 80)
    
    # Test form submission
    form_success = debug_form_submission()
    
    # Check Flask logs
    log_check = check_flask_logs()
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 FORM DEBUG RESULTS")
    print("=" * 80)
    print(f"Form Submission Test: {'✅ SUCCESS' if form_success else '❌ FAILED'}")
    print(f"Flask Log Check: {'✅ SUCCESS' if log_check else '❌ FAILED'}")
    
    if form_success:
        print("\n🎉 FORM SUBMISSION WORKING!")
        print("✅ Order creation via web interface successful")
        print("✅ All validation passed")
    else:
        print("\n❌ FORM SUBMISSION ISSUES")
        print("💡 Possible causes:")
        print("   1. Missing required form fields")
        print("   2. CSRF token validation failing")
        print("   3. Server-side validation errors")
        print("   4. Database constraint violations")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
