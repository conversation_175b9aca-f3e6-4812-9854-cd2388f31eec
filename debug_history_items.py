import sqlite3

# Debug the specific order that's not showing items in history
order_id = 'ORD1753983391CA9E99E1'

conn = sqlite3.connect('instance/medivent.db')
conn.row_factory = sqlite3.Row
cursor = conn.cursor()

print(f"🔍 DEBUGGING ORDER ITEMS FOR: {order_id}")
print("=" * 50)

# 1. Check if order exists
print("1️⃣ Checking order existence...")
order = cursor.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
if order:
    print(f"   ✅ Order found: {order['customer_name']}")
    print(f"   📊 Status: {order['status']}")
    print(f"   💰 Total: {order['order_amount']}")
else:
    print("   ❌ Order not found")
    exit()

# 2. Check order_items table structure
print("\n2️⃣ Checking order_items table structure...")
cursor.execute("PRAGMA table_info(order_items)")
columns = cursor.fetchall()
print("   Columns:")
for col in columns:
    print(f"      {col[1]} ({col[2]})")

# 3. Check order items with the exact query from view_history
print("\n3️⃣ Testing exact query from view_history function...")
order_items = cursor.execute('''
    SELECT * FROM order_items
    WHERE order_id = ?
    ORDER BY product_name
''', (order_id,)).fetchall()

print(f"   📦 Found {len(order_items)} order items")

if order_items:
    for i, item in enumerate(order_items, 1):
        print(f"\n   Item {i}:")
        print(f"      Product Name: {item['product_name'] if 'product_name' in item.keys() else 'N/A'}")
        print(f"      Strength: {item['strength'] if 'strength' in item.keys() else 'N/A'}")
        print(f"      Quantity: {item['quantity'] if 'quantity' in item.keys() else 'N/A'}")
        print(f"      Unit Price: {item['unit_price'] if 'unit_price' in item.keys() else 'N/A'}")
        print(f"      Status: {item['status'] if 'status' in item.keys() else 'N/A'}")
        print(f"      All keys: {list(item.keys())}")
else:
    print("   ❌ No order items found!")

# 4. Check if there are any order items for this order with different query
print("\n4️⃣ Alternative query check...")
alt_items = cursor.execute('SELECT COUNT(*) as count FROM order_items WHERE order_id = ?', (order_id,)).fetchone()
print(f"   📊 Total order_items count: {alt_items['count']}")

# 5. Show all order_items for debugging
print("\n5️⃣ All order_items in database...")
all_items = cursor.execute('SELECT order_id, product_name, quantity FROM order_items LIMIT 10').fetchall()
for item in all_items:
    print(f"   Order: {item['order_id']}, Product: {item['product_name']}, Qty: {item['quantity']}")

# 6. Check if the specific order has items with a broader search
print(f"\n6️⃣ Searching for any items related to {order_id}...")
related_items = cursor.execute('''
    SELECT * FROM order_items 
    WHERE order_id LIKE ? OR order_id = ?
''', (f'%{order_id[-8:]}%', order_id)).fetchall()

print(f"   📦 Found {len(related_items)} related items")
for item in related_items:
    print(f"      Order ID: {item['order_id']}")
    print(f"      Product: {item['product_name']}")

conn.close()
