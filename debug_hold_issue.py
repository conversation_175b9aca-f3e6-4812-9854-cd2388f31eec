#!/usr/bin/env python3
import sqlite3
import json

def debug_hold_issue():
    print("🔍 DEBUGGING HOLD WORKFLOW ISSUE")
    print("=" * 60)
    
    conn = sqlite3.connect('instance/medivent.db')
    conn.row_factory = sqlite3.Row  # Enable column access by name
    cursor = conn.cursor()
    
    # 1. Check if invoice_holds table exists and its structure
    print("\n1️⃣ CHECKING INVOICE_HOLDS TABLE STRUCTURE")
    print("-" * 50)
    
    try:
        cursor.execute("PRAGMA table_info(invoice_holds)")
        columns = cursor.fetchall()
        if columns:
            print("✅ invoice_holds table exists with columns:")
            for col in columns:
                print(f"   {col['name']} ({col['type']}) - {'NOT NULL' if col['notnull'] else 'NULL'}")
        else:
            print("❌ invoice_holds table does not exist!")
            return False
    except Exception as e:
        print(f"❌ Error checking table structure: {e}")
        return False
    
    # 2. Check current data in invoice_holds
    print("\n2️⃣ CHECKING CURRENT INVOICE_HOLDS DATA")
    print("-" * 50)
    
    cursor.execute('SELECT COUNT(*) as count FROM invoice_holds')
    total_holds = cursor.fetchone()['count']
    print(f"Total records in invoice_holds: {total_holds}")
    
    cursor.execute('SELECT COUNT(*) as count FROM invoice_holds WHERE status = "active"')
    active_holds = cursor.fetchone()['count']
    print(f"Active holds: {active_holds}")
    
    # Show recent holds
    cursor.execute('''
        SELECT hold_id, order_id, hold_reason, status, hold_date 
        FROM invoice_holds 
        ORDER BY hold_date DESC 
        LIMIT 5
    ''')
    recent_holds = cursor.fetchall()
    print("\nRecent holds:")
    for hold in recent_holds:
        print(f"  {hold['hold_id']} | {hold['order_id']} | {hold['status']} | {hold['hold_date']}")
    
    # 3. Check specific order ORD00000243
    print("\n3️⃣ CHECKING ORDER ORD00000243")
    print("-" * 50)
    
    # Check order status
    cursor.execute('SELECT order_id, customer_name, status, notes FROM orders WHERE order_id = ?', ('ORD00000243',))
    order = cursor.fetchone()
    if order:
        print(f"Order found: {order['order_id']} | {order['customer_name']} | {order['status']}")
        print(f"Notes: {order['notes']}")
    else:
        print("❌ Order ORD00000243 not found!")
        return False
    
    # Check hold record for this order
    cursor.execute('SELECT * FROM invoice_holds WHERE order_id = ?', ('ORD00000243',))
    hold_record = cursor.fetchone()
    if hold_record:
        print("✅ Hold record found:")
        for key in hold_record.keys():
            print(f"   {key}: {hold_record[key]}")
    else:
        print("❌ No hold record found for ORD00000243")
    
    # 4. Test the exact query used by held_invoices page
    print("\n4️⃣ TESTING HELD INVOICES QUERY")
    print("-" * 50)
    
    try:
        held_invoices = cursor.execute('''
            SELECT
                ih.hold_id, ih.order_id, ih.hold_reason, ih.hold_comments,
                ih.hold_date, ih.hold_by, ih.priority_level,
                o.customer_name, o.order_amount, o.order_date, o.status,
                CAST(julianday('now') - julianday(ih.hold_date) AS INTEGER) as days_on_hold
            FROM invoice_holds ih
            JOIN orders o ON ih.order_id = o.order_id
            WHERE ih.status = 'active'
            ORDER BY ih.hold_date DESC
        ''').fetchall()
        
        print(f"Query returned {len(held_invoices)} held invoices")
        
        if held_invoices:
            print("\nHeld invoices from query:")
            for invoice in held_invoices:
                print(f"  {invoice['order_id']} | {invoice['customer_name']} | {invoice['hold_reason']}")
                
            # Check if ORD00000243 is in the results
            ord243_in_results = any(inv['order_id'] == 'ORD00000243' for inv in held_invoices)
            if ord243_in_results:
                print("✅ ORD00000243 IS in the held invoices query results")
            else:
                print("❌ ORD00000243 is NOT in the held invoices query results")
        else:
            print("❌ No held invoices returned by query")
            
    except Exception as e:
        print(f"❌ Error running held invoices query: {e}")
        return False
    
    # 5. Check for any issues with the join
    print("\n5️⃣ CHECKING JOIN INTEGRITY")
    print("-" * 50)
    
    # Check if there are holds without matching orders
    cursor.execute('''
        SELECT ih.order_id, ih.hold_id 
        FROM invoice_holds ih 
        LEFT JOIN orders o ON ih.order_id = o.order_id 
        WHERE o.order_id IS NULL AND ih.status = 'active'
    ''')
    orphaned_holds = cursor.fetchall()
    
    if orphaned_holds:
        print("❌ Found orphaned holds (holds without matching orders):")
        for hold in orphaned_holds:
            print(f"   Hold {hold['hold_id']} references non-existent order {hold['order_id']}")
    else:
        print("✅ No orphaned holds found")
    
    # 6. Final diagnosis
    print("\n6️⃣ DIAGNOSIS")
    print("-" * 50)
    
    if order and order['status'] == 'On Hold' and not hold_record:
        print("🔍 ISSUE FOUND: Order is marked 'On Hold' but no record in invoice_holds table")
        print("   This suggests the hold API is updating orders but not creating hold records")
    elif hold_record and hold_record['status'] == 'active' and not ord243_in_results:
        print("🔍 ISSUE FOUND: Hold record exists but not appearing in query results")
        print("   This suggests a JOIN or query issue")
    elif not order or order['status'] != 'On Hold':
        print("🔍 ISSUE FOUND: Order status was not updated properly")
    else:
        print("✅ Everything looks correct - the hold should be visible")
    
    conn.close()
    return True

if __name__ == "__main__":
    debug_hold_issue()
