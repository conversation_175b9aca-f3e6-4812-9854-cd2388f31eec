#!/usr/bin/env python3
"""
Debug inventory route error by testing each component
"""

import sqlite3
import sys
import traceback

def test_inventory_components():
    """Test each component of the inventory route"""
    try:
        print("🔍 DEBUGGING INVENTORY ROUTE COMPONENTS")
        print("=" * 50)
        
        # Test database connection
        print("1️⃣ Testing database connection...")
        try:
            db = sqlite3.connect('instance/medivent.db')
            db.row_factory = sqlite3.Row
            print("✅ Database connection successful")
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
            return False
        
        # Test inventory validator import
        print("\n2️⃣ Testing inventory validator import...")
        try:
            from utils.inventory_validator import get_inventory_validator
            inventory_validator = get_inventory_validator(db)
            print("✅ Inventory validator import successful")
        except Exception as e:
            print(f"❌ Inventory validator import failed: {e}")
            traceback.print_exc()
            return False
        
        # Test inventory summary
        print("\n3️⃣ Testing inventory summary...")
        try:
            summary = inventory_validator.get_inventory_summary()
            print(f"✅ Inventory summary successful: {summary}")
        except Exception as e:
            print(f"❌ Inventory summary failed: {e}")
            traceback.print_exc()
            return False
        
        # Test low stock products
        print("\n4️⃣ Testing low stock products...")
        try:
            low_stock_products = inventory_validator.get_low_stock_products()
            print(f"✅ Low stock products successful: {len(low_stock_products)} products")
        except Exception as e:
            print(f"❌ Low stock products failed: {e}")
            traceback.print_exc()
            return False
        
        # Test recent inventory query
        print("\n5️⃣ Testing recent inventory query...")
        try:
            cursor = db.execute('''
                SELECT i.inventory_id, i.batch_number, i.stock_quantity,
                       i.allocated_quantity, i.date_received, i.status,
                       p.name as product_name, p.strength,
                       d.name as division_name,
                       w.name as warehouse_name
                FROM inventory i
                JOIN products p ON i.product_id = p.product_id
                JOIN divisions d ON p.division_id = d.division_id
                LEFT JOIN warehouses w ON i.warehouse_id = w.warehouse_id
                WHERE i.status = 'active' AND d.is_active = 1
                ORDER BY i.date_received DESC
                LIMIT 20
            ''')
            recent_inventory = cursor.fetchall()
            print(f"✅ Recent inventory query successful: {len(recent_inventory)} records")
        except Exception as e:
            print(f"❌ Recent inventory query failed: {e}")
            traceback.print_exc()
            return False
        
        # Test products summary query (our new query)
        print("\n6️⃣ Testing products summary query...")
        try:
            cursor = db.execute('''
                SELECT p.product_id, p.name as product_name, p.strength,
                       d.name as division_name,
                       COALESCE(SUM(i.stock_quantity), 0) as total_stock,
                       COALESCE(SUM(i.stock_quantity - COALESCE(i.allocated_quantity, 0)), 0) as available_stock,
                       COUNT(CASE WHEN i.status = 'active' THEN i.inventory_id END) as batch_count
                FROM products p
                JOIN divisions d ON p.division_id = d.division_id
                LEFT JOIN inventory i ON p.product_id = i.product_id AND i.status = 'active'
                WHERE d.is_active = 1
                GROUP BY p.product_id, p.name, p.strength, d.name
                ORDER BY p.name
            ''')
            products_summary = cursor.fetchall()
            print(f"✅ Products summary query successful: {len(products_summary)} products")
            
            # Show some sample data
            for i, product in enumerate(products_summary[:3]):
                print(f"    {i+1}. {product['product_name']}: {product['total_stock']} total, {product['available_stock']} available, {product['batch_count']} batches")
                
        except Exception as e:
            print(f"❌ Products summary query failed: {e}")
            traceback.print_exc()
            return False
        
        # Test template rendering (simulate)
        print("\n7️⃣ Testing template data structure...")
        try:
            template_data = {
                'summary': summary,
                'low_stock_products': low_stock_products,
                'recent_inventory': recent_inventory,
                'products_summary': products_summary
            }
            print("✅ Template data structure successful")
            print(f"    Summary keys: {list(summary.keys()) if summary else 'None'}")
            print(f"    Low stock products: {len(low_stock_products)}")
            print(f"    Recent inventory: {len(recent_inventory)}")
            print(f"    Products summary: {len(products_summary)}")
        except Exception as e:
            print(f"❌ Template data structure failed: {e}")
            traceback.print_exc()
            return False
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Component test failed: {str(e)}")
        traceback.print_exc()
        return False

def test_template_exists():
    """Test if template file exists"""
    try:
        print("\n8️⃣ Testing template file...")
        import os
        template_path = 'templates/inventory/index.html'
        if os.path.exists(template_path):
            print(f"✅ Template exists: {template_path}")
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            print(f"    Template size: {len(content)} characters")
            print(f"    Contains 'Products Overview': {'Products Overview' in content}")
            print(f"    Contains 'Inventory Records': {'Inventory Records' in content}")
            return True
        else:
            print(f"❌ Template missing: {template_path}")
            return False
    except Exception as e:
        print(f"❌ Template test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 INVENTORY ROUTE COMPONENT DEBUG")
    print("=" * 70)
    
    success1 = test_inventory_components()
    success2 = test_template_exists()
    
    print("\n" + "=" * 70)
    print("📊 DEBUG RESULTS:")
    print("=" * 70)
    
    if success1:
        print("✅ All inventory components working")
    else:
        print("❌ Inventory components have issues")
    
    if success2:
        print("✅ Template file exists and looks correct")
    else:
        print("❌ Template file has issues")
    
    if success1 and success2:
        print("\n🎉 ALL COMPONENTS WORKING - ISSUE MIGHT BE ELSEWHERE!")
    else:
        print("\n❌ FOUND ISSUES - NEED TO FIX COMPONENTS")
    
    print("=" * 70)
