#!/usr/bin/env python3
"""
Debug the inventory block not found issue - ENHANCED VERSION
"""

import sqlite3
import json
import os
import sys

def debug_inventory_issue():
    """Debug the specific inventory issue for order ORD175411154600DAC554"""

    print("🔍 DEBUGGING INVENTORY BLOCK NOT FOUND ISSUE")
    print("=" * 60)

    # Check if database exists
    db_path = 'instance/medivent.db'
    if not os.path.exists(db_path):
        print(f"❌ Database not found at: {db_path}")
        return

    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        order_id = 'ORD175411154600DAC554'

        print(f"\n1. 🔍 CHECKING ORDER: {order_id}")
        order = cursor.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
        if order:
            print(f"   ✅ Order found: {order['customer_name']} - Status: {order['status']}")
        else:
            print("   ❌ Order NOT found")
            # Check if any orders exist
            all_orders = cursor.execute('SELECT order_id FROM orders LIMIT 5').fetchall()
            print(f"   📋 Sample orders in database: {[o['order_id'] for o in all_orders]}")
            return

        print(f"\n2. 🔍 CHECKING ORDER ITEMS:")
        items = cursor.execute('SELECT * FROM order_items WHERE order_id = ?', (order_id,)).fetchall()
        print(f"   Found {len(items)} order items:")

        if not items:
            print("   ❌ NO ORDER ITEMS FOUND - This is the root cause!")
            return

        for item in items:
            product_id = item['product_id']
            quantity = item['quantity']
            print(f"     • Product: {product_id} - Required Qty: {quantity}")

            # Check if product exists
            product = cursor.execute('SELECT name FROM products WHERE product_id = ?', (product_id,)).fetchone()
            if product:
                print(f"       ✅ Product exists: {product['name']}")
            else:
                print(f"       ❌ Product {product_id} NOT found in products table")

            # Check inventory for this product
            inventory = cursor.execute('''
                SELECT inventory_id, batch_number, warehouse_id, stock_quantity,
                       COALESCE(allocated_quantity, 0) as allocated_quantity,
                       (stock_quantity - COALESCE(allocated_quantity, 0)) as available_qty,
                       status, manufacturing_date, expiry_date
                FROM inventory
                WHERE product_id = ?
                ORDER BY manufacturing_date ASC
            ''', (product_id,)).fetchall()

            if inventory:
                active_inventory = [inv for inv in inventory if inv['status'] == 'active']
                available_inventory = [inv for inv in active_inventory if inv['available_qty'] > 0]
                total_available = sum(inv['available_qty'] for inv in available_inventory)

                print(f"       📦 Total inventory: {len(inventory)} records")
                print(f"       📦 Active inventory: {len(active_inventory)} records")
                print(f"       📦 Available inventory: {len(available_inventory)} records")
                print(f"       📦 Total available quantity: {total_available}")

                for inv in available_inventory[:3]:  # Show first 3 available
                    print(f"         - ID: {inv['inventory_id']} | Batch: {inv['batch_number']} | Available: {inv['available_qty']} | Warehouse: {inv['warehouse_id']}")
            else:
                print(f"       ❌ NO inventory found for product {product_id}")

        print(f"\n3. 🔍 CHECKING BATCH SELECTIONS:")
        selections = cursor.execute('SELECT * FROM batch_selections WHERE order_id = ?', (order_id,)).fetchall()
        print(f"   Found {len(selections)} batch selections:")

        if not selections:
            print("   ❌ NO BATCH SELECTIONS FOUND - User needs to allocate batches first!")
        else:
            for sel in selections:
                print(f"     • Product: {sel['product_id']} | Batch: {sel['batch_number']} | Qty: {sel['allocated_quantity']} | Status: {sel['status']}")

        print(f"\n4. 🔍 CHECKING DELIVERY CHALLANS:")
        dcs = cursor.execute('SELECT * FROM delivery_challans WHERE order_id = ?', (order_id,)).fetchall()
        print(f"   Found {len(dcs)} delivery challans")

        for dc in dcs:
            print(f"     • DC: {dc['dc_number']} | Status: {dc['status']} | Created: {dc['created_date']}")

        print(f"\n5. 🔍 CHECKING DATABASE STATISTICS:")
        stats = {
            'total_orders': cursor.execute('SELECT COUNT(*) FROM orders').fetchone()[0],
            'total_products': cursor.execute('SELECT COUNT(*) FROM products').fetchone()[0],
            'total_inventory': cursor.execute('SELECT COUNT(*) FROM inventory').fetchone()[0],
            'active_inventory': cursor.execute("SELECT COUNT(*) FROM inventory WHERE status = 'active'").fetchone()[0],
            'available_inventory': cursor.execute("SELECT COUNT(*) FROM inventory WHERE status = 'active' AND (stock_quantity - COALESCE(allocated_quantity, 0)) > 0").fetchone()[0]
        }

        for key, value in stats.items():
            print(f"   {key}: {value}")

        conn.close()

        print(f"\n🎯 DIAGNOSIS COMPLETE")
        print("=" * 60)

    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_inventory_issue()
