#!/usr/bin/env python3
"""
Debug inventory permissions and route mapping
"""

import sqlite3
import sys

def check_admin_permissions():
    """Check what permissions the admin user has"""
    try:
        print("🔍 CHECKING ADMIN PERMISSIONS")
        print("=" * 50)
        
        db = sqlite3.connect('instance/medivent.db')
        db.row_factory = sqlite3.Row
        
        # Check if admin user exists
        admin = db.execute("SELECT * FROM users WHERE username = 'admin'").fetchone()
        if admin:
            print(f"✅ Admin user found: {admin['username']} (role: {admin['role']})")
        else:
            print("❌ Admin user not found")
            return False
        
        # Check admin permissions
        permissions = db.execute('''
            SELECT p.permission_code, p.permission_name
            FROM role_permissions rp
            JOIN permissions p ON rp.permission_id = p.permission_id
            WHERE rp.role = 'admin'
            ORDER BY p.permission_code
        ''').fetchall()
        
        print(f"\n📋 Admin permissions ({len(permissions)}):")
        inventory_permissions = []
        for perm in permissions:
            if 'inventory' in perm['permission_code']:
                inventory_permissions.append(perm['permission_code'])
                print(f"  ✅ {perm['permission_code']}: {perm['permission_name']}")
            else:
                print(f"  • {perm['permission_code']}: {perm['permission_name']}")
        
        print(f"\n🎯 Inventory-related permissions: {inventory_permissions}")
        
        # Check specifically for inventory_view
        has_inventory_view = any(p['permission_code'] == 'inventory_view' for p in permissions)
        print(f"Has 'inventory_view' permission: {has_inventory_view}")
        
        db.close()
        return has_inventory_view
        
    except Exception as e:
        print(f"❌ Permission check failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def check_route_mapping():
    """Check Flask route mapping"""
    try:
        print("\n🛣️ CHECKING ROUTE MAPPING")
        print("=" * 50)
        
        # Import Flask app
        from app import app
        
        with app.app_context():
            rules = list(app.url_map.iter_rules())
            
            # Find inventory-related routes
            inventory_routes = []
            for rule in rules:
                if 'inventory' in rule.rule or 'inventory' in rule.endpoint:
                    inventory_routes.append((rule.rule, rule.endpoint, list(rule.methods)))
            
            print(f"Found {len(inventory_routes)} inventory-related routes:")
            for rule, endpoint, methods in inventory_routes:
                print(f"  • {rule} → {endpoint} [{', '.join(methods)}]")
            
            # Check specifically for /inventory/ route
            inventory_index_routes = [r for r in inventory_routes if r[0] == '/inventory/']
            if inventory_index_routes:
                print(f"\n🎯 /inventory/ route found:")
                for rule, endpoint, methods in inventory_index_routes:
                    print(f"  ✅ {rule} → {endpoint} [{', '.join(methods)}]")
                    return endpoint
            else:
                print("\n❌ /inventory/ route not found")
                return None
        
    except Exception as e:
        print(f"❌ Route mapping check failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def check_permission_middleware_logic():
    """Check permission middleware logic"""
    try:
        print("\n🔒 CHECKING PERMISSION MIDDLEWARE LOGIC")
        print("=" * 50)
        
        from utils.permissions import permission_middleware
        
        # Check the route_permissions mapping
        from utils.permissions import has_permission
        
        # Simulate checking inventory permission
        print("Testing permission check for 'inventory_view'...")
        
        # This won't work without proper Flask context, but we can check the mapping
        from utils.permissions import permission_middleware
        
        print("✅ Permission middleware imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Permission middleware check failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 INVENTORY PERMISSIONS DEBUG")
    print("=" * 70)
    
    success1 = check_admin_permissions()
    endpoint = check_route_mapping()
    success3 = check_permission_middleware_logic()
    
    print("\n" + "=" * 70)
    print("📊 DEBUG RESULTS:")
    print("=" * 70)
    
    if success1:
        print("✅ Admin has inventory permissions")
    else:
        print("❌ Admin missing inventory permissions")
    
    if endpoint:
        print(f"✅ Inventory route found: {endpoint}")
    else:
        print("❌ Inventory route not found")
    
    if success3:
        print("✅ Permission middleware accessible")
    else:
        print("❌ Permission middleware issues")
    
    print("\n🔍 ANALYSIS:")
    if not success1:
        print("❌ ISSUE: Admin user doesn't have 'inventory_view' permission")
        print("💡 SOLUTION: Grant 'inventory_view' permission to admin role")
    elif not endpoint:
        print("❌ ISSUE: Inventory route not properly registered")
        print("💡 SOLUTION: Check blueprint registration")
    else:
        print("✅ Permissions and routes look correct")
        print("🤔 Issue might be elsewhere - check Flask app restart")
    
    print("=" * 70)
