#!/usr/bin/env python3
"""
Debug inventory response to see what's actually being returned
"""

import requests
import sys

def debug_inventory_response():
    """Debug what's actually being returned from inventory route"""
    try:
        print("🔍 DEBUGGING INVENTORY RESPONSE")
        print("=" * 50)
        
        response = requests.get('http://127.0.0.1:5001/inventory/', timeout=10)
        
        print(f"Status Code: {response.status_code}")
        print(f"Content Length: {len(response.text)}")
        print(f"Content Type: {response.headers.get('Content-Type', 'Unknown')}")
        
        if response.status_code == 200:
            content = response.text
            
            # Check for key sections
            print("\n🔍 CONTENT ANALYSIS:")
            print(f"  • Contains 'Products Overview': {'Products Overview' in content}")
            print(f"  • Contains 'Inventory Records': {'Inventory Records' in content}")
            print(f"  • Contains 'Paracetamol': {'Paracetamol' in content}")
            print(f"  • Contains 'batch': {'batch' in content.lower()}")
            print(f"  • Contains 'available': {'available' in content.lower()}")
            
            # Look for template structure
            print(f"  • Contains HTML structure: {'<html>' in content or '<!DOCTYPE' in content}")
            print(f"  • Contains base template: {'{% extends' in content or 'base.html' in content}")
            
            # Check for error indicators
            print(f"  • Contains error: {'error' in content.lower()}")
            print(f"  • Contains exception: {'exception' in content.lower()}")
            print(f"  • Contains traceback: {'traceback' in content.lower()}")
            
            # Look for specific template elements
            print(f"  • Contains card structure: {'card' in content.lower()}")
            print(f"  • Contains table structure: {'<table' in content}")
            print(f"  • Contains Bootstrap: {'bootstrap' in content.lower()}")
            
            # Check for redirect
            if response.history:
                print(f"\n⚠️ REDIRECTS DETECTED:")
                for i, resp in enumerate(response.history):
                    print(f"  {i+1}. {resp.status_code} -> {resp.url}")
                print(f"  Final: {response.url}")
            
            # Show first 500 characters
            print(f"\n📄 FIRST 500 CHARACTERS:")
            print("-" * 50)
            print(content[:500])
            print("-" * 50)
            
            # Show last 500 characters
            print(f"\n📄 LAST 500 CHARACTERS:")
            print("-" * 50)
            print(content[-500:])
            print("-" * 50)
            
            # Look for specific template sections
            if 'Products Overview' in content:
                # Find the Products Overview section
                start = content.find('Products Overview')
                section = content[start:start+1000]
                print(f"\n📋 PRODUCTS OVERVIEW SECTION:")
                print("-" * 50)
                print(section)
                print("-" * 50)
            
            # Save full response for analysis
            with open('inventory_response_debug.html', 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"\n💾 Full response saved to: inventory_response_debug.html")
            
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"Response: {response.text[:500]}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ Debug failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_inventory_response()
