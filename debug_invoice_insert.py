import sqlite3

conn = sqlite3.connect('instance/medivent.db')
cursor = conn.cursor()

print("=== TESTING INVOICE INSERT ===")

# Test the exact query from the code
try:
    cursor.execute('''
        INSERT INTO invoices (invoice_number, order_id, generated_by, pdf_path, date_generated)
        VALUES (?, ?, ?, ?, ?)
    ''', ('TEST123', 'ORD123', 'admin', '/test/path.pdf', '2024-07-31T10:00:00'))
    
    print("✅ Invoice insert successful")
    
    # Check if it was inserted
    cursor.execute("SELECT * FROM invoices WHERE invoice_number = 'TEST123'")
    result = cursor.fetchone()
    if result:
        print("✅ Invoice found in database")
        print(f"   Invoice Number: {result[1]}")
        print(f"   Order ID: {result[2]}")
        print(f"   Date Generated: {result[3]}")
    
    # Clean up test data
    cursor.execute("DELETE FROM invoices WHERE invoice_number = 'TEST123'")
    conn.commit()
    
except Exception as e:
    print(f"❌ Error: {e}")
    print(f"Error type: {type(e)}")

# Check the actual table structure
print("\n=== INVOICES TABLE STRUCTURE ===")
cursor.execute("PRAGMA table_info(invoices)")
columns = cursor.fetchall()
for col in columns:
    print(f"{col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'}")

conn.close()
