#!/usr/bin/env python3
"""
Debug main app blueprint registration issues
"""

import sys
import traceback
import os

def debug_app_registration():
    """Debug the main app blueprint registration"""
    print("🔍 DEBUGGING MAIN APP BLUEPRINT REGISTRATION")
    print("=" * 60)
    
    try:
        # Step 1: Test individual imports
        print("1. Testing individual imports...")
        
        from flask import Flask
        print("   ✅ Flask imported")
        
        from api_endpoints import api_bp
        print("   ✅ API blueprint imported")
        print(f"   Blueprint name: {api_bp.name}")
        print(f"   Blueprint url_prefix: {api_bp.url_prefix}")
        
        # Step 2: Check if app.py has create_app function
        print("\n2. Checking app.py structure...")
        
        # Read app.py to understand its structure
        with open('app.py', 'r', encoding='utf-8') as f:
            app_content = f.read()
        
        if 'def create_app(' in app_content:
            print("   ✅ create_app function found")
        elif 'app = Flask(' in app_content:
            print("   ✅ Direct Flask app creation found")
        else:
            print("   ❌ No clear app creation pattern found")
        
        # Check for API blueprint registration
        if 'from api_endpoints import api_bp' in app_content:
            print("   ✅ API blueprint import found in app.py")
        else:
            print("   ❌ API blueprint import NOT found in app.py")
        
        if 'app.register_blueprint(api_bp)' in app_content:
            print("   ✅ API blueprint registration found in app.py")
        else:
            print("   ❌ API blueprint registration NOT found in app.py")
        
        # Step 3: Try to create a minimal version of the main app
        print("\n3. Testing main app creation...")
        
        # Create a minimal Flask app similar to main app
        app = Flask(__name__)
        app.config['SECRET_KEY'] = 'test-key'
        
        # Try to register the API blueprint
        app.register_blueprint(api_bp)
        print("   ✅ API blueprint registered successfully")
        
        # Check registered routes
        with app.app_context():
            rules = list(app.url_map.iter_rules())
            api_routes = [rule for rule in rules if rule.rule.startswith('/api/')]
            
            print(f"   ✅ Found {len(api_routes)} API routes:")
            for rule in api_routes:
                print(f"      {rule.rule} -> {rule.endpoint}")
        
        # Step 4: Test the routes with test client
        print("\n4. Testing routes with test client...")
        
        with app.test_client() as client:
            # Test order details
            response = client.get('/api/order-details/ORD00000155')
            print(f"   Order Details API: Status {response.status_code}")
            if response.status_code == 200:
                try:
                    data = response.get_json()
                    print(f"   Success: {data.get('success') if data else 'No JSON'}")
                except:
                    print(f"   Raw response length: {len(response.data)}")
            
            # Test QR code
            response = client.get('/api/order-qr-code/ORD00000155')
            print(f"   QR Code API: Status {response.status_code}")
            if response.status_code == 200:
                try:
                    data = response.get_json()
                    print(f"   Success: {data.get('success') if data else 'No JSON'}")
                except:
                    print(f"   Raw response length: {len(response.data)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("Traceback:")
        traceback.print_exc()
        return False

def check_main_app_startup():
    """Check if main app starts without errors"""
    print("\n🔍 CHECKING MAIN APP STARTUP")
    print("=" * 40)
    
    try:
        # Try to import the main app module
        print("Attempting to import main app...")
        
        # This will execute app.py and show any import errors
        import app
        print("✅ Main app imported successfully")
        
        # Check if app has the expected attributes
        if hasattr(app, 'app'):
            print("✅ Flask app instance found")
            
            # Check blueprints
            blueprints = list(app.app.blueprints.keys())
            print(f"✅ Registered blueprints: {blueprints}")
            
            if 'api' in blueprints:
                print("✅ API blueprint is registered in main app")
            else:
                print("❌ API blueprint is NOT registered in main app")
        else:
            print("❌ No Flask app instance found")
        
        return True
        
    except Exception as e:
        print(f"❌ Main app startup error: {e}")
        print("Traceback:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_ok = debug_app_registration()
    startup_ok = check_main_app_startup()
    
    print("\n📊 SUMMARY:")
    print(f"Blueprint Debug: {'✅ OK' if debug_ok else '❌ Failed'}")
    print(f"Main App Startup: {'✅ OK' if startup_ok else '❌ Failed'}")
    
    if debug_ok and startup_ok:
        print("\n🎉 All tests passed!")
    else:
        print("\n⚠️ Issues detected - check output above")
    
    sys.exit(0 if (debug_ok and startup_ok) else 1)
