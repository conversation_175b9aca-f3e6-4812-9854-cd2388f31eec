#!/usr/bin/env python3
"""
Debug order creation to see what's happening
"""

import requests
import time
import sqlite3

def debug_order_creation():
    """Debug the order creation process"""
    print("🔍 DEBUGGING ORDER CREATION PROCESS")
    print("=" * 60)
    
    base_url = "http://localhost:5001"
    session = requests.Session()
    
    try:
        # Login first
        print("1. Logging in...")
        login_data = {'username': 'admin', 'password': 'admin123'}
        response = session.post(f"{base_url}/login", data=login_data, allow_redirects=False)
        
        if response.status_code != 302:
            print("❌ Login failed")
            return False
        
        print("✅ Login successful")
        
        # Get current order count
        print("2. Checking current order count...")
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM orders')
        initial_count = cursor.fetchone()[0]
        print(f"   Initial order count: {initial_count}")
        
        # Get latest order ID
        cursor.execute('SELECT order_id FROM orders ORDER BY order_date DESC LIMIT 1')
        latest_order = cursor.fetchone()
        latest_order_id = latest_order[0] if latest_order else "None"
        print(f"   Latest order ID: {latest_order_id}")
        conn.close()
        
        # Submit order with detailed tracking
        print("3. Submitting order...")
        order_data = {
            'customer_name': 'Debug Test Customer',
            'customer_address': 'Debug Test Address',
            'customer_phone': '555-DEBUG-TEST',
            'payment_method': 'cash',
            'po_number': 'DEBUG-001'
        }
        
        response = session.post(f"{base_url}/orders/new", 
                               data=order_data, 
                               timeout=15,
                               allow_redirects=False)
        
        print(f"   Response status: {response.status_code}")
        print(f"   Response headers: {dict(response.headers)}")
        
        if response.status_code == 302:
            redirect_url = response.headers.get('Location', '')
            print(f"   Redirect URL: {redirect_url}")
        elif response.status_code == 200:
            print("   Response returned 200 - checking for errors...")
            if "Error" in response.text:
                # Extract error message
                import re
                error_match = re.search(r'Error[^:]*: ([^<\n]+)', response.text)
                if error_match:
                    print(f"   Error found: {error_match.group(1)}")
                else:
                    print("   Generic error found in response")
            else:
                print("   No obvious error in response")
        
        # Check order count after submission
        print("4. Checking order count after submission...")
        time.sleep(1)  # Give database time to update
        
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM orders')
        final_count = cursor.fetchone()[0]
        print(f"   Final order count: {final_count}")
        
        if final_count > initial_count:
            print(f"✅ Order count increased by {final_count - initial_count}")
            
            # Get the new order
            cursor.execute('SELECT order_id, customer_name FROM orders ORDER BY order_date DESC LIMIT 1')
            new_order = cursor.fetchone()
            if new_order:
                new_order_id, customer_name = new_order
                print(f"   New order: {new_order_id} - {customer_name}")
                
                if 'Debug Test Customer' in customer_name:
                    print("✅ Order created successfully via web interface!")
                    conn.close()
                    return True
        else:
            print("❌ Order count did not increase")
        
        conn.close()
        return False
        
    except Exception as e:
        print(f"❌ Error during debug: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_order_sequence_table():
    """Check the order sequence table status"""
    print("\n🔧 CHECKING ORDER SEQUENCE TABLE")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        # Check if table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='order_sequence'")
        table_exists = cursor.fetchone()
        print(f"Order sequence table exists: {table_exists is not None}")
        
        if table_exists:
            cursor.execute('SELECT COUNT(*) FROM order_sequence')
            count = cursor.fetchone()[0]
            print(f"Order sequence entries: {count}")
            
            cursor.execute('SELECT MAX(id) FROM order_sequence')
            max_id = cursor.fetchone()[0]
            print(f"Max sequence ID: {max_id}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error checking sequence table: {e}")
        return False

def test_order_id_generation():
    """Test order ID generation directly"""
    print("\n🆔 TESTING ORDER ID GENERATION")
    print("=" * 50)
    
    try:
        from app import app
        with app.app_context():
            from routes.orders import generate_order_id
            
            print("Generating 3 test order IDs:")
            for i in range(3):
                order_id = generate_order_id()
                print(f"  {i+1}. {order_id}")
            
            print("✅ Order ID generation working")
            return True
            
    except Exception as e:
        print(f"❌ Order ID generation error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run debug tests"""
    print("🐛 ORDER CREATION DEBUG SESSION")
    print("=" * 80)
    
    # Test 1: Order ID generation
    id_test = test_order_id_generation()
    
    # Test 2: Order sequence table
    seq_test = check_order_sequence_table()
    
    # Test 3: Full order creation debug
    creation_test = debug_order_creation()
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 DEBUG RESULTS")
    print("=" * 80)
    print(f"Order ID Generation: {'✅ WORKING' if id_test else '❌ FAILED'}")
    print(f"Order Sequence Table: {'✅ WORKING' if seq_test else '❌ FAILED'}")
    print(f"Web Order Creation: {'✅ WORKING' if creation_test else '❌ FAILED'}")
    
    if id_test and seq_test and creation_test:
        print("\n🎉 ALL SYSTEMS WORKING!")
        print("✅ Order ID generation is functional")
        print("✅ Database sequence table is working")
        print("✅ Web interface order creation is successful")
        print("\n💡 The discrepancy has been completely resolved!")
    elif id_test and seq_test and not creation_test:
        print("\n⚠️  PARTIAL SUCCESS")
        print("✅ Backend systems working")
        print("❌ Web interface has issues")
        print("💡 The order creation route may have validation issues")
    else:
        print("\n❌ ISSUES DETECTED")
        print("💡 Some components are not working correctly")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
