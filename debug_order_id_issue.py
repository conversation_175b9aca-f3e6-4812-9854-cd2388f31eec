#!/usr/bin/env python3
"""
Debug script to investigate the UNIQUE constraint failed: orders.order_id issue
"""

import sqlite3
import os
import time
import uuid
from datetime import datetime

def check_database_structure():
    """Check the database structure and constraints"""
    db_path = 'instance/medivent.db'
    
    if not os.path.exists(db_path):
        print(f"❌ Database not found: {db_path}")
        return
    
    print(f"🔍 Analyzing database: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check orders table schema
        print("\n📋 Orders table schema:")
        cursor.execute("PRAGMA table_info(orders)")
        columns = cursor.fetchall()
        for col in columns:
            print(f"  {col[1]} {col[2]} {'PRIMARY KEY' if col[5] else ''} {'NOT NULL' if col[3] else ''}")
        
        # Check for UNIQUE constraints
        print("\n🔒 UNIQUE constraints on orders table:")
        cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='orders'")
        schema = cursor.fetchone()
        if schema:
            print(f"  {schema[0]}")
        
        # Check for indexes
        print("\n📊 Indexes on orders table:")
        cursor.execute("PRAGMA index_list(orders)")
        indexes = cursor.fetchall()
        for idx in indexes:
            print(f"  Index: {idx[1]} (unique: {idx[2]})")
            cursor.execute(f"PRAGMA index_info({idx[1]})")
            cols = cursor.fetchall()
            for col in cols:
                print(f"    Column: {col[2]}")
        
        # Check current order_id values
        print("\n📝 Current order_id values (last 10):")
        cursor.execute("SELECT order_id, customer_name, status, order_date FROM orders ORDER BY order_date DESC LIMIT 10")
        orders = cursor.fetchall()
        for order in orders:
            print(f"  {order[0]} - {order[1]} - {order[2]} - {order[3]}")
        
        # Check for any duplicate order_ids
        print("\n🔍 Checking for duplicate order_ids:")
        cursor.execute("SELECT order_id, COUNT(*) as count FROM orders GROUP BY order_id HAVING COUNT(*) > 1")
        duplicates = cursor.fetchall()
        if duplicates:
            print("❌ DUPLICATE ORDER IDs FOUND:")
            for order_id, count in duplicates:
                print(f"  {order_id}: {count} occurrences")
        else:
            print("✅ No duplicate order_ids found")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error analyzing database: {e}")

def test_order_id_generation():
    """Test the order ID generation logic"""
    print("\n🧪 Testing order ID generation logic:")
    
    def generate_order_id():
        """Generate unique order ID with timestamp and random component"""
        timestamp = str(int(time.time()))
        random_part = uuid.uuid4().hex[:8].upper()
        return f"ORD{timestamp}{random_part}"
    
    # Generate multiple IDs quickly to test for collisions
    ids = []
    for i in range(100):
        order_id = generate_order_id()
        ids.append(order_id)
        if i < 5:
            print(f"  Generated: {order_id}")
        time.sleep(0.001)  # Small delay
    
    # Check for duplicates in generated IDs
    unique_ids = set(ids)
    if len(unique_ids) == len(ids):
        print(f"✅ Generated {len(ids)} unique IDs successfully")
    else:
        print(f"❌ Generated {len(ids)} IDs but only {len(unique_ids)} were unique!")
        duplicates = [id for id in ids if ids.count(id) > 1]
        print(f"  Duplicate IDs: {set(duplicates)}")

def simulate_concurrent_order_creation():
    """Simulate concurrent order creation to test for race conditions"""
    print("\n⚡ Simulating concurrent order creation:")
    
    db_path = 'instance/medivent.db'
    
    def create_test_order(order_suffix):
        """Create a test order"""
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Generate order ID
            timestamp = str(int(time.time()))
            random_part = uuid.uuid4().hex[:8].upper()
            order_id = f"TEST{timestamp}{random_part}{order_suffix}"
            
            # Try to insert
            cursor.execute('''
                INSERT INTO orders (
                    order_id, customer_name, customer_address, customer_phone,
                    payment_method, status, sales_agent, updated_by, order_date, last_updated
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                order_id, f"Test Customer {order_suffix}", "Test Address", "123456789",
                "cash", "Placed", "test_user", "test_user", datetime.now(), datetime.now()
            ))
            
            conn.commit()
            conn.close()
            print(f"  ✅ Created order: {order_id}")
            return True
            
        except sqlite3.IntegrityError as e:
            print(f"  ❌ Failed to create order {order_suffix}: {e}")
            return False
        except Exception as e:
            print(f"  ❌ Error creating order {order_suffix}: {e}")
            return False
    
    # Create multiple test orders quickly
    success_count = 0
    for i in range(5):
        if create_test_order(f"_{i}"):
            success_count += 1
    
    print(f"  Created {success_count}/5 test orders successfully")
    
    # Clean up test orders
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("DELETE FROM orders WHERE order_id LIKE 'TEST%'")
        deleted = cursor.rowcount
        conn.commit()
        conn.close()
        print(f"  🧹 Cleaned up {deleted} test orders")
    except Exception as e:
        print(f"  ⚠️  Error cleaning up test orders: {e}")

def main():
    """Main function"""
    print("🔍 DEBUGGING ORDER ID UNIQUE CONSTRAINT ISSUE")
    print("=" * 50)
    
    check_database_structure()
    test_order_id_generation()
    simulate_concurrent_order_creation()
    
    print("\n" + "=" * 50)
    print("🎯 ANALYSIS COMPLETE")

if __name__ == "__main__":
    main()
