import sqlite3

# Connect to database
conn = sqlite3.connect('database.db')
conn.row_factory = sqlite3.Row
cursor = conn.cursor()

order_id = 'ORD175398287142AFD661'

print(f"=== Debugging Order Items for {order_id} ===\n")

# 1. Check if order exists
print("1. Checking if order exists:")
order = cursor.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
if order:
    print(f"✅ Order found: {order['order_id']}")
    print(f"   Status: {order['status']}")
    print(f"   Customer: {order['customer_name']}")
    print(f"   Sales Agent: {order['sales_agent']}")
else:
    print(f"❌ Order {order_id} not found!")
    exit()

print()

# 2. Check order_items table structure
print("2. Order Items table structure:")
cursor.execute("PRAGMA table_info(order_items)")
columns = cursor.fetchall()
for col in columns:
    print(f"   {col['name']} ({col['type']})")

print()

# 3. Check if order_items exist for this order
print("3. Checking order_items for this order:")
order_items = cursor.execute('SELECT * FROM order_items WHERE order_id = ?', (order_id,)).fetchall()
print(f"   Found {len(order_items)} order items")

if order_items:
    for i, item in enumerate(order_items):
        print(f"   Item {i+1}:")
        print(f"     Product ID: {item['product_id']}")
        print(f"     Quantity: {item['quantity']}")
        print(f"     Status: {item['status']}")
        print(f"     Unit Price: {item['unit_price']}")
else:
    print("   ❌ No order items found!")

print()

# 4. Check products table
print("4. Checking products table:")
cursor.execute("PRAGMA table_info(products)")
columns = cursor.fetchall()
for col in columns:
    print(f"   {col['name']} ({col['type']})")

print()

# 5. If order_items exist, check the JOIN query
if order_items:
    print("5. Testing the JOIN query:")
    join_query = '''
        SELECT oi.*, 
               COALESCE(p.name, 'Unknown Product') as product_name, 
               COALESCE(p.strength, 'N/A') as strength
        FROM order_items oi
        LEFT JOIN products p ON oi.product_id = p.product_id
        WHERE oi.order_id = ?
        ORDER BY COALESCE(p.name, 'Unknown Product')
    '''
    
    result = cursor.execute(join_query, (order_id,)).fetchall()
    print(f"   JOIN query returned {len(result)} items")
    
    for i, item in enumerate(result):
        print(f"   Item {i+1}:")
        print(f"     Product Name: {item['product_name']}")
        print(f"     Strength: {item['strength']}")
        print(f"     Quantity: {item['quantity']}")
        print(f"     Status: {item['status']}")

# 6. Check if there are any order_items at all in the database
print()
print("6. Checking total order_items in database:")
total_items = cursor.execute('SELECT COUNT(*) as count FROM order_items').fetchone()
print(f"   Total order items in database: {total_items['count']}")

# 7. Show some sample order_items
print()
print("7. Sample order_items (first 5):")
sample_items = cursor.execute('SELECT * FROM order_items LIMIT 5').fetchall()
for item in sample_items:
    print(f"   Order ID: {item['order_id']}, Product ID: {item['product_id']}, Qty: {item['quantity']}")

conn.close()
