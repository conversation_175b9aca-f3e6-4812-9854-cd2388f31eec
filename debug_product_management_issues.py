#!/usr/bin/env python3
"""
Debug Product Management Issues
Comprehensive analysis of routing, templates, and data flow
"""

import sqlite3
import requests
import json

def debug_database_stats():
    """Debug database statistics calculation"""
    print("📊 DEBUGGING DATABASE STATISTICS:")
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Check products table structure
        cursor.execute("PRAGMA table_info(products)")
        columns = cursor.fetchall()
        print(f"   📋 Products table columns:")
        for col in columns:
            print(f"      • {col['name']} ({col['type']})")
        
        # Get sample data
        cursor.execute("SELECT product_id, name, status, is_active FROM products LIMIT 5")
        samples = cursor.fetchall()
        print(f"\n   📝 Sample product data:")
        for sample in samples:
            print(f"      • {sample['product_id']}: {sample['name']} | status={sample['status']} | is_active={sample['is_active']}")
        
        # Test the exact queries used in the route
        print(f"\n   🧮 Testing route queries:")
        
        # Total products
        cursor.execute("SELECT COUNT(*) as total FROM products")
        total = cursor.fetchone()['total']
        print(f"      • Total products: {total}")
        
        # Active products (exact route logic)
        cursor.execute("SELECT COUNT(*) as active FROM products WHERE (LOWER(status) = 'active' AND is_active = 1)")
        active = cursor.fetchone()['active']
        print(f"      • Active products: {active}")
        
        # Inactive products (exact route logic)
        cursor.execute("SELECT COUNT(*) as inactive FROM products WHERE (LOWER(status) != 'active' OR is_active = 0)")
        inactive = cursor.fetchone()['inactive']
        print(f"      • Inactive products: {inactive}")
        
        # Categories
        cursor.execute("SELECT COUNT(DISTINCT category) as categories FROM products WHERE category IS NOT NULL AND category != 'Uncategorized'")
        categories = cursor.fetchone()['categories']
        print(f"      • Categories: {categories}")
        
        # Test individual product is_active calculation
        print(f"\n   🔍 Testing individual product is_active logic:")
        cursor.execute("SELECT product_id, name, status, is_active FROM products LIMIT 3")
        test_products = cursor.fetchall()
        for product in test_products:
            # This is the exact logic from the route
            is_active_calc = product['is_active'] if product['is_active'] is not None else (product['status'].lower() == 'active' if product['status'] else False)
            print(f"      • {product['product_id']}: status='{product['status']}', is_active={product['is_active']} → calculated={is_active_calc}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Database debug failed: {e}")
        return False

def debug_route_registration():
    """Debug Flask route registration"""
    print("\n🛣️ DEBUGGING ROUTE REGISTRATION:")
    
    try:
        from app import app
        
        with app.app_context():
            rules = list(app.url_map.iter_rules())
            
            # Find all product-related routes
            product_routes = []
            for rule in rules:
                if 'product' in rule.endpoint.lower() or 'product' in rule.rule.lower():
                    product_routes.append({
                        'rule': rule.rule,
                        'endpoint': rule.endpoint,
                        'methods': list(rule.methods)
                    })
            
            print(f"   📋 Found {len(product_routes)} product-related routes:")
            for route in sorted(product_routes, key=lambda x: x['rule']):
                print(f"      • {route['rule']} → {route['endpoint']} {route['methods']}")
            
            # Check for conflicts
            management_routes = [r for r in product_routes if 'management' in r['rule'] or 'management' in r['endpoint']]
            print(f"\n   🎯 Product management routes:")
            for route in management_routes:
                print(f"      • {route['rule']} → {route['endpoint']}")
            
            # Check for view_all routes
            view_all_routes = [r for r in product_routes if 'view_all' in r['endpoint']]
            print(f"\n   👁️ View all routes:")
            for route in view_all_routes:
                print(f"      • {route['rule']} → {route['endpoint']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Route debug failed: {e}")
        return False

def debug_ajax_routes():
    """Debug AJAX activation routes"""
    print("\n🔧 DEBUGGING AJAX ROUTES:")
    
    try:
        from app import app
        
        with app.app_context():
            rules = list(app.url_map.iter_rules())
            
            # Find activation routes
            activation_routes = []
            for rule in rules:
                if 'activate' in rule.endpoint or 'deactivate' in rule.endpoint:
                    activation_routes.append({
                        'rule': rule.rule,
                        'endpoint': rule.endpoint,
                        'methods': list(rule.methods)
                    })
            
            print(f"   📋 Found {len(activation_routes)} activation routes:")
            for route in activation_routes:
                print(f"      • {route['rule']} → {route['endpoint']} {route['methods']}")
            
            # Test route construction
            from flask import url_for
            try:
                activate_url = url_for('products.activate_product', product_id='P001')
                print(f"   ✅ Activate URL: {activate_url}")
            except Exception as e:
                print(f"   ❌ Activate URL failed: {e}")
            
            try:
                deactivate_url = url_for('products.deactivate_product', product_id='P001')
                print(f"   ✅ Deactivate URL: {deactivate_url}")
            except Exception as e:
                print(f"   ❌ Deactivate URL failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ AJAX debug failed: {e}")
        return False

def debug_template_rendering():
    """Debug template rendering issues"""
    print("\n🎨 DEBUGGING TEMPLATE RENDERING:")
    
    try:
        # Check if templates exist
        import os
        
        templates_to_check = [
            'templates/products/product_management.html',
            'templates/products/view_all_products.html',
            'templates/products/index.html'
        ]
        
        for template in templates_to_check:
            if os.path.exists(template):
                print(f"   ✅ {template} exists")
                
                # Check template content
                with open(template, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # Check for key elements
                checks = [
                    ('Products Management' in content, 'Title'),
                    ('stats.total_products' in content, 'Total products KPI'),
                    ('stats.active_products' in content, 'Active products KPI'),
                    ('stats.inactive_products' in content, 'Inactive products KPI'),
                    ('toggleProductStatus' in content, 'Toggle function'),
                    ('url_for(\'products.product_management\')' in content, 'Correct filter URLs')
                ]
                
                for check, desc in checks:
                    status = "✅" if check else "❌"
                    print(f"      {status} {desc}")
            else:
                print(f"   ❌ {template} missing")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Template debug failed: {e}")
        return False

def test_live_routes():
    """Test live route responses"""
    print("\n🌐 TESTING LIVE ROUTES:")
    
    base_url = "http://127.0.0.1:5001"
    
    routes_to_test = [
        ("/products/product_management/", "Product Management"),
        ("/products/product_management/?status=active", "Active Filter"),
        ("/products/view_all/", "View All Products"),
        ("/product_management", "Legacy Redirect")
    ]
    
    for route, name in routes_to_test:
        try:
            response = requests.get(f"{base_url}{route}", timeout=5)
            print(f"   • {name}: Status {response.status_code}")
            
            if response.status_code == 200:
                content = response.text
                if 'Products Management' in content:
                    print(f"      ✅ Contains 'Products Management'")
                elif 'Products Gallery' in content:
                    print(f"      ⚠️ Contains 'Products Gallery' (wrong template)")
                else:
                    print(f"      ❓ Unknown template")
                    
        except requests.exceptions.RequestException as e:
            print(f"   ❌ {name}: Connection error - {e}")

def main():
    """Run all debugging tests"""
    print("🔍 COMPREHENSIVE PRODUCT MANAGEMENT DEBUG")
    print("=" * 60)
    
    tests = [
        debug_database_stats,
        debug_route_registration,
        debug_ajax_routes,
        debug_template_rendering,
        test_live_routes
    ]
    
    for test in tests:
        try:
            test()
        except Exception as e:
            print(f"❌ Test {test.__name__} failed: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 DEBUG COMPLETE")

if __name__ == "__main__":
    main()
