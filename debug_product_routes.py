#!/usr/bin/env python3
"""
Debug the product routes to find the issue
"""

def debug_product_routes():
    """Check what product routes are actually registered"""
    try:
        from app import app
        
        print("🔍 DEBUGGING PRODUCT ROUTES")
        print("=" * 60)
        
        with app.app_context():
            # Get all routes
            rules = list(app.url_map.iter_rules())
            
            # Find all product-related routes
            product_routes = []
            for rule in rules:
                if 'products' in rule.endpoint:
                    product_routes.append({
                        'rule': rule.rule,
                        'endpoint': rule.endpoint,
                        'methods': list(rule.methods)
                    })
            
            print(f"📋 Found {len(product_routes)} product routes:")
            for route in sorted(product_routes, key=lambda x: x['endpoint']):
                print(f"   • {route['endpoint']} → {route['rule']} {route['methods']}")
            
            # Check specifically for view_all_products
            view_all_routes = [r for r in product_routes if 'view_all' in r['endpoint']]
            
            print(f"\n🎯 VIEW ALL ROUTES:")
            if view_all_routes:
                for route in view_all_routes:
                    print(f"   ✅ {route['endpoint']} → {route['rule']}")
            else:
                print("   ❌ NO VIEW ALL ROUTES FOUND!")
                
                # Check for similar routes
                similar_routes = [r for r in product_routes if 'view' in r['endpoint']]
                if similar_routes:
                    print(f"\n🔍 SIMILAR VIEW ROUTES:")
                    for route in similar_routes:
                        print(f"   • {route['endpoint']} → {route['rule']}")
            
            # Test url_for for the problematic endpoint
            print(f"\n🧪 TESTING URL_FOR:")
            try:
                from flask import url_for
                url = url_for('products.view_all_products')
                print(f"   ✅ url_for('products.view_all_products') = {url}")
            except Exception as e:
                print(f"   ❌ url_for('products.view_all_products') failed: {str(e)}")
                
                # Try alternative endpoints
                alternatives = ['products.view_product', 'products.view_all', 'view_all_products']
                for alt in alternatives:
                    try:
                        url = url_for(alt)
                        print(f"   ✅ url_for('{alt}') = {url}")
                    except Exception as alt_e:
                        print(f"   ❌ url_for('{alt}') failed: {str(alt_e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Debug failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_product_routes()
