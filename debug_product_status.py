#!/usr/bin/env python3
"""
Debug Product Status Issues
Check database schema and data for product status functionality
"""

import sqlite3

def debug_products_table():
    """Debug products table structure and data"""
    try:
        conn = sqlite3.connect('instance/medivent.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        print("🔍 DEBUGGING PRODUCTS TABLE")
        print("=" * 50)
        
        # Check table schema
        print("\n📋 Table Schema:")
        cursor.execute("PRAGMA table_info(products)")
        columns = cursor.fetchall()
        
        for col in columns:
            print(f"   {col['name']} ({col['type']}) - Default: {col['dflt_value']}")
        
        # Check if status and is_active columns exist
        column_names = [col['name'] for col in columns]
        has_status = 'status' in column_names
        has_is_active = 'is_active' in column_names
        
        print(f"\n✅ Has 'status' column: {has_status}")
        print(f"✅ Has 'is_active' column: {has_is_active}")
        
        # Check current data
        print("\n📊 Current Product Data:")
        cursor.execute("SELECT COUNT(*) as total FROM products")
        total = cursor.fetchone()['total']
        print(f"   Total products: {total}")
        
        if has_status:
            cursor.execute("SELECT status, COUNT(*) as count FROM products GROUP BY status")
            status_counts = cursor.fetchall()
            print("   Status distribution:")
            for row in status_counts:
                print(f"     {row['status']}: {row['count']}")
        
        if has_is_active:
            cursor.execute("SELECT is_active, COUNT(*) as count FROM products GROUP BY is_active")
            active_counts = cursor.fetchall()
            print("   is_active distribution:")
            for row in active_counts:
                print(f"     {row['is_active']}: {row['count']}")
        
        # Sample products
        print("\n📝 Sample Products (first 5):")
        if has_status and has_is_active:
            cursor.execute("SELECT product_id, name, status, is_active FROM products LIMIT 5")
        elif has_status:
            cursor.execute("SELECT product_id, name, status FROM products LIMIT 5")
        else:
            cursor.execute("SELECT product_id, name FROM products LIMIT 5")
        
        samples = cursor.fetchall()
        for product in samples:
            print(f"   {dict(product)}")
        
        # Test filtering queries
        print("\n🔍 Testing Filter Queries:")
        
        if has_status:
            # Test active filter
            cursor.execute("SELECT COUNT(*) as count FROM products WHERE LOWER(status) = 'active'")
            active_count = cursor.fetchone()['count']
            print(f"   Active products (status='active'): {active_count}")
            
            # Test inactive filter
            cursor.execute("SELECT COUNT(*) as count FROM products WHERE LOWER(status) != 'active'")
            inactive_count = cursor.fetchone()['count']
            print(f"   Inactive products (status!='active'): {inactive_count}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error debugging products table: {e}")
        return False

def test_activation_routes():
    """Test if activation routes work with current data"""
    try:
        conn = sqlite3.connect('instance/medivent.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        print("\n🧪 TESTING ACTIVATION FUNCTIONALITY")
        print("=" * 50)
        
        # Get a sample product
        cursor.execute("SELECT product_id, name, status FROM products LIMIT 1")
        sample_product = cursor.fetchone()
        
        if sample_product:
            product_id = sample_product['product_id']
            current_status = sample_product['status']
            
            print(f"📝 Sample Product: {sample_product['name']} (ID: {product_id})")
            print(f"   Current status: {current_status}")
            
            # Test update query (without actually updating)
            print("\n🔧 Testing Update Queries:")
            
            # Test activate query
            print("   Testing activate query structure...")
            try:
                cursor.execute("SELECT COUNT(*) FROM products WHERE product_id = ?", (product_id,))
                print("   ✅ Product lookup query works")
            except Exception as e:
                print(f"   ❌ Product lookup failed: {e}")
            
            # Test status update structure
            print("   Testing status update query structure...")
            try:
                # Just test the query structure, don't actually update
                cursor.execute("SELECT 1 WHERE EXISTS (SELECT 1 FROM products WHERE product_id = ?)", (product_id,))
                print("   ✅ Update query structure is valid")
            except Exception as e:
                print(f"   ❌ Update query structure invalid: {e}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error testing activation routes: {e}")
        return False

def check_missing_columns():
    """Check if we need to add missing columns"""
    try:
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        print("\n🔧 CHECKING FOR MISSING COLUMNS")
        print("=" * 50)
        
        # Check current schema
        cursor.execute("PRAGMA table_info(products)")
        columns = [col[1] for col in cursor.fetchall()]
        
        missing_columns = []
        
        if 'status' not in columns:
            missing_columns.append('status')
        
        if 'is_active' not in columns:
            missing_columns.append('is_active')
        
        if missing_columns:
            print(f"❌ Missing columns: {missing_columns}")
            print("\n🔧 Adding missing columns...")
            
            if 'status' not in columns:
                cursor.execute("ALTER TABLE products ADD COLUMN status TEXT DEFAULT 'active'")
                print("   ✅ Added 'status' column")
            
            if 'is_active' not in columns:
                cursor.execute("ALTER TABLE products ADD COLUMN is_active INTEGER DEFAULT 1")
                print("   ✅ Added 'is_active' column")
            
            conn.commit()
            print("   ✅ Database schema updated")
        else:
            print("✅ All required columns exist")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error checking/adding columns: {e}")
        return False

def main():
    """Run all debugging tests"""
    print("🚀 PRODUCT STATUS DEBUGGING")
    print("=" * 60)
    
    tests = [
        check_missing_columns,
        debug_products_table,
        test_activation_routes
    ]
    
    for test in tests:
        try:
            test()
        except Exception as e:
            print(f"❌ Test failed: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 DEBUGGING COMPLETE")
    print("Check the output above for any issues that need fixing.")

if __name__ == "__main__":
    main()
