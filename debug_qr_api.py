#!/usr/bin/env python3
"""
Debug QR Code API by simulating the exact call
"""

import os
import sys
import json

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_qr_api():
    """Debug the QR API by calling it directly"""
    try:
        print("🔍 Debugging QR Code API...")
        
        # Import Flask app
        from app import app
        
        with app.test_client() as client:
            print("✅ Flask test client created")
            
            # Test the order details API first
            print("\n1. Testing order details API...")
            order_response = client.get('/api/order-details/ORD00000165')
            print(f"   Status: {order_response.status_code}")
            
            if order_response.status_code == 200:
                order_data = order_response.get_json()
                print(f"   Success: {order_data.get('success')}")
                if order_data.get('success'):
                    print("   ✅ Order details retrieved successfully")
                else:
                    print(f"   ❌ Order details failed: {order_data.get('message')}")
            else:
                print(f"   ❌ HTTP Error: {order_response.status_code}")
                print(f"   Response: {order_response.get_data(as_text=True)[:200]}")
            
            # Test the QR code API
            print("\n2. Testing QR code API...")
            qr_response = client.get('/api/order-qr-code/ORD00000165?branding=true')
            print(f"   Status: {qr_response.status_code}")
            
            if qr_response.status_code == 200:
                qr_data = qr_response.get_json()
                print(f"   Success: {qr_data.get('success')}")
                
                if qr_data.get('success'):
                    print("   ✅ QR code generated successfully")
                    qr_info = qr_data.get('qr_code', {})
                    print(f"   File path: {qr_info.get('file_path')}")
                    print(f"   Base64 length: {len(qr_info.get('base64', ''))}")
                    
                    # Check if file exists
                    file_path = qr_info.get('file_path')
                    if file_path and os.path.exists(file_path):
                        print(f"   ✅ QR file exists: {file_path}")
                        print(f"   File size: {os.path.getsize(file_path)} bytes")
                    else:
                        print(f"   ❌ QR file not found: {file_path}")
                        
                else:
                    print(f"   ❌ QR generation failed: {qr_data.get('error')}")
                    
            else:
                print(f"   ❌ HTTP Error: {qr_response.status_code}")
                response_text = qr_response.get_data(as_text=True)
                print(f"   Response: {response_text[:500]}")
                
                # Try to parse as JSON to see error details
                try:
                    error_data = json.loads(response_text)
                    print(f"   Error details: {error_data}")
                except:
                    pass
            
    except Exception as e:
        print(f"❌ Debug error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🐛 QR Code API Debug Tool")
    print("=" * 50)
    debug_qr_api()
    print("=" * 50)
    print("Debug completed!")
