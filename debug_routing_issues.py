#!/usr/bin/env python3
"""
Comprehensive routing issues analysis and fix script
"""

import os
import re
import sqlite3
from pathlib import Path

def analyze_routing_issues():
    """Analyze all routing issues in the application"""
    
    print("🔍 COMPREHENSIVE ROUTING ANALYSIS")
    print("=" * 60)
    
    issues = []
    
    # 1. Find broken url_for references
    broken_endpoints = find_broken_endpoints()
    issues.extend(broken_endpoints)
    
    # 2. Check template references
    template_issues = check_template_references()
    issues.extend(template_issues)
    
    # 3. Check Python file references
    python_issues = check_python_references()
    issues.extend(python_issues)
    
    # 4. Verify blueprint registration
    blueprint_issues = check_blueprint_registration()
    issues.extend(blueprint_issues)
    
    return issues

def find_broken_endpoints():
    """Find broken endpoint references"""
    
    print("\n1️⃣ FINDING BROKEN ENDPOINTS")
    print("-" * 40)
    
    broken_patterns = [
        r"url_for\(['\"]products['\"]\)",  # Should be products.index or products.product_management
        r"url_for\(['\"]view_all_products['\"]\)",  # Should be products.view_all_products
        r"url_for\(['\"]product_management['\"]\)",  # Should be products.product_management
        r"url_for\(['\"]new_product['\"]\)",  # Should be products.new_product
        r"url_for\(['\"]update_product['\"]\)",  # Should be products.update_product
        r"url_for\(['\"]delete_product['\"]\)",  # Should be products.delete_product
    ]
    
    issues = []
    
    # Search in all relevant files
    search_dirs = ['templates', 'routes', '.']
    file_extensions = ['.html', '.py', '.htm', '.jinja2']
    
    for search_dir in search_dirs:
        if os.path.exists(search_dir):
            for root, dirs, files in os.walk(search_dir):
                # Skip cache directories
                dirs[:] = [d for d in dirs if d not in ['__pycache__', '.git', 'node_modules']]
                
                for file in files:
                    if any(file.endswith(ext) for ext in file_extensions):
                        file_path = os.path.join(root, file)
                        
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                                
                                for i, line in enumerate(content.split('\n'), 1):
                                    for pattern in broken_patterns:
                                        if re.search(pattern, line):
                                            issues.append({
                                                'type': 'broken_endpoint',
                                                'file': file_path,
                                                'line': i,
                                                'content': line.strip(),
                                                'pattern': pattern,
                                                'severity': 'HIGH'
                                            })
                                            print(f"   ❌ {file_path}:{i} - {line.strip()}")
                        
                        except Exception as e:
                            print(f"   ⚠️ Error reading {file_path}: {e}")
    
    print(f"   📊 Found {len(issues)} broken endpoint references")
    return issues

def check_template_references():
    """Check template references for issues"""
    
    print("\n2️⃣ CHECKING TEMPLATE REFERENCES")
    print("-" * 40)
    
    issues = []
    
    # Check specific problematic templates
    problematic_templates = [
        'templates/products/index.html',
        'templates/products/products_list.html', 
        'templates/products/view_all_products.html',
        'templates/products/add_product_enhanced.html',
        'templates/products/update.html',
        'templates/products/new.html'
    ]
    
    for template_path in problematic_templates:
        if os.path.exists(template_path):
            try:
                with open(template_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                    # Check for specific issues
                    lines = content.split('\n')
                    for i, line in enumerate(lines, 1):
                        # Check for broken url_for patterns
                        if "url_for('products')" in line and "products.product_management" not in line:
                            issues.append({
                                'type': 'template_reference',
                                'file': template_path,
                                'line': i,
                                'content': line.strip(),
                                'issue': "Should use 'products.index' or 'products.product_management'",
                                'severity': 'HIGH'
                            })
                            print(f"   ❌ {template_path}:{i} - {line.strip()}")
                        
                        if "url_for('view_all_products')" in line:
                            issues.append({
                                'type': 'template_reference',
                                'file': template_path,
                                'line': i,
                                'content': line.strip(),
                                'issue': "Should use 'products.view_all_products'",
                                'severity': 'HIGH'
                            })
                            print(f"   ❌ {template_path}:{i} - {line.strip()}")
            
            except Exception as e:
                print(f"   ⚠️ Error reading {template_path}: {e}")
        else:
            print(f"   ⚠️ Template not found: {template_path}")
    
    print(f"   📊 Found {len(issues)} template reference issues")
    return issues

def check_python_references():
    """Check Python file references"""
    
    print("\n3️⃣ CHECKING PYTHON REFERENCES")
    print("-" * 40)
    
    issues = []
    
    # Check app.py for remaining broken references
    if os.path.exists('app.py'):
        try:
            with open('app.py', 'r', encoding='utf-8') as f:
                content = f.read()
                
                lines = content.split('\n')
                for i, line in enumerate(lines, 1):
                    # Check for remaining broken url_for patterns
                    if "url_for('product_management')" in line:
                        issues.append({
                            'type': 'python_reference',
                            'file': 'app.py',
                            'line': i,
                            'content': line.strip(),
                            'issue': "Should use 'products.product_management'",
                            'severity': 'HIGH'
                        })
                        print(f"   ❌ app.py:{i} - {line.strip()}")
        
        except Exception as e:
            print(f"   ⚠️ Error reading app.py: {e}")
    
    print(f"   📊 Found {len(issues)} Python reference issues")
    return issues

def check_blueprint_registration():
    """Check blueprint registration"""
    
    print("\n4️⃣ CHECKING BLUEPRINT REGISTRATION")
    print("-" * 40)
    
    issues = []
    
    try:
        # Import and check Flask app
        import sys
        sys.path.append('.')
        
        from app import app
        
        with app.app_context():
            # Get all registered routes
            rules = list(app.url_map.iter_rules())
            
            # Check for products blueprint routes
            products_routes = [rule for rule in rules if 'products.' in rule.endpoint]
            
            print(f"   📋 Found {len(products_routes)} products blueprint routes:")
            
            expected_routes = [
                'products.index',
                'products.product_management', 
                'products.new_product',
                'products.view_product',
                'products.update_product',
                'products.update_product_selection',
                'products.view_all_products',
                'products.delete_product',
                'products.activate_product',
                'products.deactivate_product'
            ]
            
            found_endpoints = [rule.endpoint for rule in products_routes]
            
            for expected in expected_routes:
                if expected in found_endpoints:
                    print(f"      ✅ {expected}")
                else:
                    print(f"      ❌ {expected} - MISSING")
                    issues.append({
                        'type': 'missing_route',
                        'endpoint': expected,
                        'severity': 'HIGH'
                    })
            
            # Check for unexpected routes
            for endpoint in found_endpoints:
                if endpoint not in expected_routes:
                    print(f"      ⚠️ {endpoint} - UNEXPECTED")
    
    except Exception as e:
        print(f"   ❌ Error checking blueprint registration: {e}")
        issues.append({
            'type': 'blueprint_check_failed',
            'error': str(e),
            'severity': 'HIGH'
        })
    
    print(f"   📊 Found {len(issues)} blueprint issues")
    return issues

if __name__ == "__main__":
    issues = analyze_routing_issues()
    
    print(f"\n📊 SUMMARY")
    print("=" * 60)
    print(f"Total issues found: {len(issues)}")
    
    # Group by severity
    high_issues = [i for i in issues if i.get('severity') == 'HIGH']
    print(f"High priority issues: {len(high_issues)}")
    
    if high_issues:
        print("\n🚨 HIGH PRIORITY ISSUES TO FIX:")
        for issue in high_issues[:10]:  # Show first 10
            print(f"   • {issue.get('type', 'unknown')}: {issue.get('file', 'N/A')}:{issue.get('line', 'N/A')}")
