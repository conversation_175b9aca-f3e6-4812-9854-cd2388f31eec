#!/usr/bin/env python3
"""
Debug Specific Order: ORD1754153041416935BB
"""

import sqlite3
import json
import os
from datetime import datetime

def debug_specific_order():
    """Debug the specific order from the error message"""
    
    print("🔍 DEBUGGING SPECIFIC ORDER: ORD1754153041416935BB")
    print("=" * 70)
    
    # Check if database exists
    db_path = 'instance/medivent.db'
    if not os.path.exists(db_path):
        print(f"❌ Database not found at: {db_path}")
        return False
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        order_id = 'ORD1754153041416935BB'
        
        print(f"\n🔍 STEP 1: CHECK ORDER EXISTS")
        print("-" * 50)
        
        # Check order
        order = cursor.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
        if not order:
            print(f"❌ Order {order_id} NOT FOUND")
            
            # Check if any orders exist
            all_orders = cursor.execute('SELECT order_id, customer_name, status FROM orders ORDER BY order_date DESC LIMIT 10').fetchall()
            if all_orders:
                print(f"\n📋 Found {len(all_orders)} other orders:")
                for ord in all_orders:
                    print(f"   - {ord['order_id']}: {ord['customer_name']} ({ord['status']})")
            else:
                print("❌ No orders found in database")
            
            return False
        else:
            print(f"✅ Order found:")
            print(f"   Customer: {order['customer_name']}")
            print(f"   Status: {order['status']}")
            print(f"   Amount: {order['order_amount']}")
            print(f"   Date: {order['order_date']}")
        
        print(f"\n🔍 STEP 2: CHECK ORDER ITEMS")
        print("-" * 50)
        
        # Check order items
        items = cursor.execute('''
            SELECT oi.*, p.name as product_name, p.strength
            FROM order_items oi
            LEFT JOIN products p ON oi.product_id = p.product_id
            WHERE oi.order_id = ?
            ORDER BY oi.order_item_id
        ''', (order_id,)).fetchall()
        
        if not items:
            print(f"❌ No order items found for {order_id}")
            return False
        else:
            print(f"✅ Found {len(items)} order items:")
            for item in items:
                print(f"   - {item['product_id']}: {item['product_name']} x{item['quantity']}")
        
        print(f"\n🔍 STEP 3: CHECK INVENTORY FOR EACH PRODUCT")
        print("-" * 50)
        
        for item in items:
            product_id = item['product_id']
            required_qty = item['quantity']
            
            print(f"\n📦 Product: {product_id} ({item['product_name']})")
            print(f"   Required: {required_qty}")
            
            # Check inventory
            inventory = cursor.execute('''
                SELECT i.inventory_id, i.batch_number, i.stock_quantity, 
                       COALESCE(i.allocated_quantity, 0) as allocated_quantity,
                       (i.stock_quantity - COALESCE(i.allocated_quantity, 0)) as available_qty,
                       i.warehouse_id, w.name as warehouse_name,
                       i.manufacturing_date, i.expiry_date, i.status
                FROM inventory i
                LEFT JOIN warehouses w ON i.warehouse_id = w.warehouse_id
                WHERE i.product_id = ?
                ORDER BY i.manufacturing_date ASC
            ''', (product_id,)).fetchall()
            
            if inventory:
                total_available = sum(inv['available_qty'] for inv in inventory if inv['status'] == 'active')
                print(f"   Available: {total_available}")
                
                for inv in inventory:
                    status_icon = "✅" if inv['status'] == 'active' else "❌"
                    print(f"   {status_icon} {inv['inventory_id']}: Batch {inv['batch_number']} - {inv['available_qty']} available ({inv['warehouse_name']})")
            else:
                print(f"   ❌ No inventory found")
        
        print(f"\n🔍 STEP 4: CHECK WAREHOUSES")
        print("-" * 50)
        
        warehouses = cursor.execute('SELECT * FROM warehouses ORDER BY name').fetchall()
        if warehouses:
            print(f"✅ Found {len(warehouses)} warehouses:")
            for wh in warehouses:
                status_icon = "✅" if wh['status'] == 'active' else "❌"
                print(f"   {status_icon} {wh['warehouse_id']}: {wh['name']} ({wh['status']})")
        else:
            print("❌ No warehouses found")
        
        print(f"\n🔍 STEP 5: CHECK BATCH SELECTIONS TABLE")
        print("-" * 50)
        
        # Check if batch_selections table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='batch_selections'")
        if cursor.fetchone():
            selections = cursor.execute('SELECT * FROM batch_selections WHERE order_id = ?', (order_id,)).fetchall()
            if selections:
                print(f"✅ Found {len(selections)} batch selections:")
                for sel in selections:
                    print(f"   - {sel['product_id']}: {sel['quantity']} from batch {sel['batch_number']}")
            else:
                print("ℹ️  No batch selections found (normal for new orders)")
        else:
            print("❌ batch_selections table does not exist")
        
        print(f"\n🔍 STEP 6: CHECK DELIVERY CHALLANS TABLE")
        print("-" * 50)
        
        # Check if delivery_challans table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='delivery_challans'")
        if cursor.fetchone():
            dcs = cursor.execute('SELECT * FROM delivery_challans WHERE order_id = ?', (order_id,)).fetchall()
            if dcs:
                print(f"✅ Found {len(dcs)} delivery challans:")
                for dc in dcs:
                    print(f"   - {dc['dc_number']}: {dc['status']} ({dc['created_date']})")
            else:
                print("ℹ️  No delivery challans found (normal for new orders)")
        else:
            print("❌ delivery_challans table does not exist")
        
        print(f"\n🔍 STEP 7: SIMULATE BATCH SELECTION DATA")
        print("-" * 50)
        
        # Simulate what the JavaScript would send
        batch_selections = {}
        
        for item in items:
            product_id = item['product_id']
            required_qty = item['quantity']
            
            # Get available inventory for this product
            inventory = cursor.execute('''
                SELECT inventory_id, 
                       (stock_quantity - COALESCE(allocated_quantity, 0)) as available_qty
                FROM inventory 
                WHERE product_id = ? AND status = 'active'
                AND (stock_quantity - COALESCE(allocated_quantity, 0)) > 0
                ORDER BY manufacturing_date ASC
            ''', (product_id,)).fetchall()
            
            if inventory:
                batch_selections[product_id] = []
                remaining = required_qty
                
                for inv in inventory:
                    if remaining <= 0:
                        break
                    
                    allocate_qty = min(remaining, inv['available_qty'])
                    if allocate_qty > 0:
                        batch_selections[product_id].append({
                            'inventory_id': inv['inventory_id'],
                            'quantity': allocate_qty
                        })
                        remaining -= allocate_qty
                        print(f"   ✅ Allocated {allocate_qty} from {inv['inventory_id']}")
        
        if batch_selections:
            print(f"\n📋 Simulated batch selections:")
            print(json.dumps(batch_selections, indent=2))
            
            # Test if this would work with DC generation
            print(f"\n🧪 Testing DC generation logic...")
            
            for product_id, product_selections in batch_selections.items():
                for batch_selection in product_selections:
                    inventory_id = batch_selection['inventory_id']
                    quantity = batch_selection['quantity']
                    
                    if inventory_id is None:
                        print(f"❌ Found None inventory_id for product {product_id}")
                    else:
                        print(f"✅ Valid selection: {inventory_id} -> {quantity}")
        else:
            print("❌ No valid batch selections could be created")
        
        conn.close()
        
        print(f"\n🎯 SUMMARY")
        print("=" * 70)
        print("✅ Order verification completed")
        print("✅ Order items verification completed") 
        print("✅ Inventory verification completed")
        print("✅ Batch selection simulation completed")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during debug: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_specific_order()
    if success:
        print("\n🎉 DEBUG COMPLETED SUCCESSFULLY!")
    else:
        print("\n💥 DEBUG FOUND ISSUES")
