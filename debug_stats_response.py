#!/usr/bin/env python3
"""
Debug the stats response to see what's actually being returned
"""

import requests

def debug_stats_response():
    """Debug the actual response content"""
    try:
        url = "http://192.168.99.34:5001/products/update_selection"
        print(f"🔍 DEBUGGING RESPONSE FROM: {url}")
        print("=" * 60)
        
        response = requests.get(url, timeout=10)
        
        print(f"Status Code: {response.status_code}")
        print(f"Content Length: {len(response.text)}")
        
        # Get first 2000 characters to see what's actually returned
        content = response.text[:2000]
        print("\n📄 RESPONSE CONTENT (first 2000 chars):")
        print("-" * 40)
        print(content)
        print("-" * 40)
        
        # Check for error messages
        if "Error" in response.text:
            print("\n❌ ERRORS FOUND IN RESPONSE:")
            lines = response.text.split('\n')
            for i, line in enumerate(lines):
                if 'Error' in line:
                    print(f"Line {i+1}: {line.strip()}")
        
        # Check for stats references
        if "stats" in response.text:
            print("\n📊 STATS REFERENCES FOUND:")
            lines = response.text.split('\n')
            for i, line in enumerate(lines):
                if 'stats' in line.lower():
                    print(f"Line {i+1}: {line.strip()}")
        else:
            print("\n❌ NO STATS REFERENCES FOUND IN RESPONSE")
        
        return response.text
        
    except Exception as e:
        print(f"❌ Debug failed: {str(e)}")
        return None

if __name__ == "__main__":
    debug_stats_response()
