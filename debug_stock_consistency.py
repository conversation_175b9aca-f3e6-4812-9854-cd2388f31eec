#!/usr/bin/env python3
"""
Debug stock consistency across all modules
"""

import sqlite3
import sys
import os

def debug_stock_consistency():
    """Check stock calculations across all modules"""
    try:
        print("🔍 DEBUGGING STOCK CONSISTENCY")
        print("=" * 60)
        
        # Connect to database
        db = sqlite3.connect('instance/medivent.db')
        db.row_factory = sqlite3.Row
        
        # Test product P001 (Paracetamol 500mg) as shown in screenshots
        product_id = 'P001'
        
        print(f"📋 ANALYZING PRODUCT: {product_id}")
        print("-" * 40)
        
        # 1. Check product details
        product = db.execute('SELECT * FROM products WHERE product_id = ?', (product_id,)).fetchone()
        if product:
            strength = product['strength'] if 'strength' in product.keys() else ''
            print(f"✅ Product found: {product['name']} {strength}")
        else:
            print(f"❌ Product {product_id} not found")
            return False
        
        # 2. Check inventory records
        print(f"\n📦 INVENTORY RECORDS:")
        inventory_records = db.execute('''
            SELECT inventory_id, batch_number, stock_quantity, allocated_quantity,
                   (stock_quantity - allocated_quantity) as available,
                   warehouse_id, status, expiry_date
            FROM inventory 
            WHERE product_id = ?
            ORDER BY expiry_date
        ''', (product_id,)).fetchall()
        
        total_stock = 0
        total_allocated = 0
        total_available = 0
        
        for inv in inventory_records:
            available = inv['stock_quantity'] - (inv['allocated_quantity'] or 0)
            total_stock += inv['stock_quantity']
            total_allocated += (inv['allocated_quantity'] or 0)
            total_available += available
            
            print(f"   • Batch {inv['batch_number']}: Stock={inv['stock_quantity']}, "
                  f"Allocated={inv['allocated_quantity'] or 0}, Available={available}, "
                  f"Warehouse={inv['warehouse_id']}, Status={inv['status']}")
        
        print(f"\n📊 TOTALS:")
        print(f"   • Total Stock: {total_stock}")
        print(f"   • Total Allocated: {total_allocated}")
        print(f"   • Total Available: {total_available}")
        
        # 3. Test different stock calculation methods
        print(f"\n🧪 TESTING STOCK CALCULATION METHODS:")
        
        # Method 1: Basic sum (used in some places)
        basic_stock = db.execute('''
            SELECT COALESCE(SUM(stock_quantity), 0) as total_stock
            FROM inventory 
            WHERE product_id = ? AND status = 'active'
        ''', (product_id,)).fetchone()['total_stock']
        
        # Method 2: Available stock (stock - allocated)
        available_stock = db.execute('''
            SELECT COALESCE(SUM(stock_quantity - allocated_quantity), 0) as available_stock
            FROM inventory 
            WHERE product_id = ? AND status = 'active'
        ''', (product_id,)).fetchone()['available_stock']
        
        # Method 3: Product realtime service method
        try:
            from utils.product_realtime_service import get_products_with_inventory_realtime
            realtime_products = get_products_with_inventory_realtime(db)
            realtime_stock = None
            for p in realtime_products:
                if p.get('product_id') == product_id:
                    realtime_stock = p.get('available_stock', 0)
                    break
        except Exception as e:
            realtime_stock = f"Error: {e}"
        
        # Method 4: Inventory validator method
        try:
            from utils.inventory_validator import get_inventory_validator
            validator = get_inventory_validator(db)
            validator_stock = validator.get_available_stock(product_id)
        except Exception as e:
            validator_stock = f"Error: {e}"
        
        print(f"   • Basic Stock Sum: {basic_stock}")
        print(f"   • Available Stock (stock-allocated): {available_stock}")
        print(f"   • Realtime Service Stock: {realtime_stock}")
        print(f"   • Inventory Validator Stock: {validator_stock}")
        
        # 4. Check consistency
        print(f"\n✅ CONSISTENCY CHECK:")
        methods = [
            ("Manual Calculation", total_available),
            ("Available Stock Query", available_stock),
            ("Realtime Service", realtime_stock if isinstance(realtime_stock, int) else 0),
            ("Inventory Validator", validator_stock if isinstance(validator_stock, int) else 0)
        ]
        
        consistent = True
        base_value = methods[0][1]
        
        for method_name, value in methods:
            if isinstance(value, int) and value != base_value:
                consistent = False
                print(f"   ❌ {method_name}: {value} (MISMATCH)")
            else:
                print(f"   ✅ {method_name}: {value}")
        
        if consistent:
            print(f"\n🎉 ALL METHODS CONSISTENT: {base_value} units available")
        else:
            print(f"\n❌ INCONSISTENCY DETECTED - Different methods return different values!")
        
        db.close()
        return consistent
        
    except Exception as e:
        print(f"❌ Debug failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_order_approval_fix():
    """Test the order approval AttributeError fix"""
    try:
        print(f"\n🧪 TESTING ORDER APPROVAL FIX")
        print("=" * 40)
        
        # Test sqlite3.Row behavior
        db = sqlite3.connect('instance/medivent.db')
        db.row_factory = sqlite3.Row
        
        # Get a sample order
        order = db.execute('SELECT * FROM orders LIMIT 1').fetchone()
        if order:
            print(f"✅ Sample order found: {order['order_id']}")
            
            # Test the fixed method
            customer_data = {
                'name': order['customer_name'],
                'phone': order['customer_phone'] if 'customer_phone' in order.keys() else '',
                'address': order['customer_address'] if 'customer_address' in order.keys() else ''
            }
            
            print(f"✅ Customer data extraction works:")
            print(f"   • Name: {customer_data['name']}")
            print(f"   • Phone: {customer_data['phone']}")
            print(f"   • Address: {customer_data['address']}")
            
            return True
        else:
            print("❌ No orders found to test")
            return False
            
    except Exception as e:
        print(f"❌ Order approval test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 COMPREHENSIVE STOCK CONSISTENCY DEBUG")
    print("=" * 70)
    
    success1 = debug_stock_consistency()
    success2 = test_order_approval_fix()
    
    print("\n" + "=" * 70)
    if success1 and success2:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Stock calculations are consistent")
        print("✅ Order approval fix works")
    else:
        print("❌ ISSUES DETECTED - Need to fix inconsistencies")
    print("=" * 70)
