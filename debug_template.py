import requests

try:
    response = requests.get('http://127.0.0.1:5001/orders/ORD175397491316416F32/history', timeout=10)
    print(f'Status: {response.status_code}')
    print('Content length:', len(response.text))
    print('\nFirst 1000 characters:')
    print('=' * 50)
    print(response.text[:1000])
    print('=' * 50)
    
    # Look for error messages
    if 'error' in response.text.lower() or 'exception' in response.text.lower():
        print('\n❌ ERRORS FOUND:')
        lines = response.text.split('\n')
        for i, line in enumerate(lines):
            if 'error' in line.lower() or 'exception' in line.lower():
                print(f'Line {i}: {line.strip()}')
    
    # Check if it's a redirect or error page
    if '<title>' in response.text:
        title_start = response.text.find('<title>') + 7
        title_end = response.text.find('</title>')
        title = response.text[title_start:title_end]
        print(f'\nPage title: {title}')
        
except Exception as e:
    print(f'Error: {e}')
