#!/usr/bin/env python3
"""
DEEP INVESTIGATION: Database Column Mismatch Error Analysis
Comprehensive analysis of sqlite3.OperationalError: table invoices has no column named created_at
"""

import sqlite3
import os

def investigate_database_schema():
    """Investigate the actual database schema"""
    print("🔍 DEEP INVESTIGATION: Database Schema Analysis")
    print("=" * 60)
    
    db_path = 'instance/medivent.db'
    if not os.path.exists(db_path):
        print(f"❌ Database not found: {db_path}")
        return
    
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    # 1. Check invoices table schema
    print("\n📋 INVOICES TABLE SCHEMA:")
    print("-" * 30)
    cursor.execute("PRAGMA table_info(invoices)")
    invoice_columns = cursor.fetchall()
    
    if invoice_columns:
        for col in invoice_columns:
            print(f"  {col['name']} ({col['type']}) - {'NOT NULL' if col['notnull'] else 'NULL'}")
    else:
        print("  ❌ invoices table not found")
    
    # 2. Check challans table schema  
    print("\n📋 CHALLANS TABLE SCHEMA:")
    print("-" * 30)
    cursor.execute("PRAGMA table_info(challans)")
    challan_columns = cursor.fetchall()
    
    if challan_columns:
        for col in challan_columns:
            print(f"  {col['name']} ({col['type']}) - {'NOT NULL' if col['notnull'] else 'NULL'}")
    else:
        print("  ❌ challans table not found")
    
    # 3. Check for created_at vs date_generated patterns
    print("\n🔍 COLUMN NAME ANALYSIS:")
    print("-" * 30)
    
    invoice_col_names = [col['name'] for col in invoice_columns]
    challan_col_names = [col['name'] for col in challan_columns]
    
    print(f"Invoices has 'created_at': {'created_at' in invoice_col_names}")
    print(f"Invoices has 'date_generated': {'date_generated' in invoice_col_names}")
    print(f"Challans has 'created_at': {'created_at' in challan_col_names}")
    print(f"Challans has 'date_generated': {'date_generated' in challan_col_names}")
    
    # 4. Show sample data if tables exist
    if invoice_columns:
        print(f"\n📊 INVOICES TABLE SAMPLE DATA:")
        print("-" * 30)
        cursor.execute("SELECT * FROM invoices LIMIT 3")
        sample_invoices = cursor.fetchall()
        for invoice in sample_invoices:
            print(f"  Invoice ID: {invoice.get('invoice_id', 'N/A')}")
            print(f"  Order ID: {invoice.get('order_id', 'N/A')}")
            if 'date_generated' in invoice.keys():
                print(f"  Date Generated: {invoice['date_generated']}")
            if 'created_at' in invoice.keys():
                print(f"  Created At: {invoice['created_at']}")
            print("  ---")
    
    conn.close()
    
    return {
        'invoice_columns': invoice_col_names,
        'challan_columns': challan_col_names
    }

def investigate_code_references():
    """Investigate code references to created_at vs date_generated"""
    print("\n🔍 CODE ANALYSIS: Column References")
    print("=" * 60)
    
    # This will be done manually by examining the routes/orders.py file
    print("Manual code analysis required - checking routes/orders.py")

if __name__ == "__main__":
    schema_info = investigate_database_schema()
    investigate_code_references()
