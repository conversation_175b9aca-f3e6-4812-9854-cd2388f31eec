#!/usr/bin/env python3
"""
Detailed debugging of order creation process
"""

import requests
import sqlite3
import time

def simulate_order_creation_directly():
    """Simulate the exact order creation process directly"""
    print("🔬 SIMULATING ORDER CREATION DIRECTLY")
    print("=" * 60)
    
    try:
        from app import app
        with app.app_context():
            from database import get_db
            from routes.orders import generate_order_id
            from utils.product_validator import get_product_validator
            from utils.inventory_validator import get_inventory_validator
            from datetime import datetime
            import sqlite3
            
            # Simulate the exact process from routes/orders.py
            db = get_db()
            
            # Step 1: Get form data (simulated)
            customer_name = 'Direct Simulation Customer'
            customer_address = 'Direct Simulation Address'
            customer_phone = '555-DIRECT-SIM'
            payment_method = 'cash'
            
            print(f"1. Form data: {customer_name}, {customer_address}, {customer_phone}, {payment_method}")
            
            # Step 2: Generate order ID
            order_id = generate_order_id()
            print(f"2. Generated order ID: {order_id}")
            
            if not order_id:
                print("❌ Order ID generation failed")
                return False
            
            # Step 3: Begin transaction
            print("3. Beginning transaction...")
            db.execute('BEGIN IMMEDIATE TRANSACTION')
            
            # Step 4: Insert order
            print("4. Inserting order...")
            try:
                # Simulate current_user
                class MockUser:
                    username = 'admin'
                
                current_user = MockUser()
                ORDER_STATUSES = ['Placed', 'Confirmed', 'Processing', 'Approved']
                
                db.execute('''
                    INSERT INTO orders (
                        order_id, customer_name, customer_address, customer_phone,
                        payment_method, status, sales_agent, updated_by, order_date, last_updated
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    order_id, customer_name, customer_address, customer_phone,
                    payment_method, ORDER_STATUSES[0], current_user.username,
                    current_user.username, datetime.now(), datetime.now()
                ))
                print("✅ Order inserted successfully")
            except sqlite3.IntegrityError as e:
                print(f"❌ Order insertion failed: {e}")
                db.execute('ROLLBACK')
                return False
            
            # Step 5: Process order items
            print("5. Processing order items...")
            product_ids = ['P001']
            quantities = ['1']
            foc_quantities = ['0']
            
            total_amount = 0
            inventory_validator = get_inventory_validator(db)
            product_validator = get_product_validator(db)
            
            for i in range(len(product_ids)):
                product_id = product_ids[i]
                quantity = int(quantities[i])
                foc_quantity = int(foc_quantities[i]) if i < len(foc_quantities) else 0
                
                print(f"   Processing product {product_id}, qty: {quantity}, foc: {foc_quantity}")
                
                # Validate product
                is_valid, product_info = product_validator.validate_product_exists(product_id)
                print(f"   Product validation: {is_valid}, info: {product_info}")
                
                if not is_valid or not product_info.get('division_valid', False):
                    print(f"❌ Product {product_id} validation failed")
                    raise Exception(f'Invalid product: {product_id}')
                
                # Get product details
                product = db.execute('SELECT * FROM products WHERE product_id = ?', (product_id,)).fetchone()
                if not product:
                    print(f"❌ Product {product_id} not found in database")
                    continue
                
                print(f"   Product found: {product['name']}, price: {product['unit_price']}")
                
                # Calculate amounts
                unit_price = float(product['unit_price'])
                line_total = quantity * unit_price
                total_amount += line_total
                
                print(f"   Line total: {line_total}, running total: {total_amount}")
                
                # Insert order item
                try:
                    db.execute('''
                        INSERT INTO order_items (
                            order_id, product_id, quantity, unit_price, total_price, foc_quantity
                        ) VALUES (?, ?, ?, ?, ?, ?)
                    ''', (order_id, product_id, quantity, unit_price, line_total, foc_quantity))
                    print(f"   ✅ Order item inserted")
                except Exception as e:
                    print(f"   ❌ Order item insertion failed: {e}")
                    raise e
            
            # Step 6: Update order total
            print(f"6. Updating order total: {total_amount}")
            db.execute('UPDATE orders SET order_amount = ? WHERE order_id = ?', (total_amount, order_id))
            
            # Step 7: Commit transaction
            print("7. Committing transaction...")
            db.execute('COMMIT')
            
            print(f"✅ Order {order_id} created successfully!")
            return True
            
    except Exception as e:
        print(f"❌ Direct simulation failed: {e}")
        import traceback
        traceback.print_exc()
        try:
            db.execute('ROLLBACK')
        except:
            pass
        return False

def test_web_interface_with_debugging():
    """Test web interface with detailed response analysis"""
    print("\n🌐 TESTING WEB INTERFACE WITH DEBUGGING")
    print("=" * 60)
    
    try:
        base_url = "http://localhost:5001"
        session = requests.Session()
        
        # Login
        login_data = {'username': 'admin', 'password': 'admin123'}
        response = session.post(f"{base_url}/login", data=login_data, allow_redirects=False)
        
        if response.status_code != 302:
            print("❌ Login failed")
            return False
        
        print("✅ Login successful")
        
        # Submit order and capture full response
        order_data = {
            'customer_name': 'Web Debug Customer',
            'customer_address': 'Web Debug Address',
            'customer_phone': '555-WEB-DEBUG',
            'payment_method': 'cash',
            'po_number': 'WEB-DEBUG-001',
            'product_id[]': ['P001'],
            'quantity[]': ['1'],
            'foc_quantity[]': ['0']
        }
        
        print("Submitting order...")
        response = session.post(f"{base_url}/orders/new", 
                               data=order_data, 
                               timeout=15,
                               allow_redirects=True)  # Follow redirects to see final page
        
        print(f"Final response status: {response.status_code}")
        print(f"Final URL: {response.url}")
        
        # Check if we're back on the order form
        if '/orders/new' in response.url:
            print("⚠️  Redirected back to order form")
            
            # Look for flash messages or errors in the response
            response_text = response.text
            
            # Check for common error indicators
            if 'alert-danger' in response_text:
                print("❌ Danger alert found in response")
                import re
                danger_match = re.search(r'alert-danger[^>]*>([^<]+)', response_text)
                if danger_match:
                    print(f"   Error message: {danger_match.group(1).strip()}")
            
            if 'alert-warning' in response_text:
                print("⚠️  Warning alert found in response")
                import re
                warning_match = re.search(r'alert-warning[^>]*>([^<]+)', response_text)
                if warning_match:
                    print(f"   Warning message: {warning_match.group(1).strip()}")
            
            if 'Error' in response_text:
                print("❌ Generic error found in response")
                import re
                error_matches = re.findall(r'Error[^<\n]*', response_text)
                for error in error_matches[:3]:  # Show first 3 errors
                    print(f"   Error: {error}")
            
            return False
        else:
            print("✅ Redirected to different page (likely success)")
            return True
            
    except Exception as e:
        print(f"❌ Web interface test failed: {e}")
        return False

def main():
    """Run detailed debugging"""
    print("🔬 DETAILED ORDER CREATION DEBUGGING")
    print("=" * 80)
    
    # Test 1: Direct simulation
    direct_success = simulate_order_creation_directly()
    
    # Test 2: Web interface with debugging
    web_success = test_web_interface_with_debugging()
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 DETAILED DEBUG RESULTS")
    print("=" * 80)
    print(f"Direct Simulation: {'✅ SUCCESS' if direct_success else '❌ FAILED'}")
    print(f"Web Interface: {'✅ SUCCESS' if web_success else '❌ FAILED'}")
    
    if direct_success and not web_success:
        print("\n🔍 DIAGNOSIS:")
        print("✅ Direct order creation works perfectly")
        print("❌ Web interface has specific issues")
        print("💡 The problem is likely in:")
        print("   1. Form validation on the web interface")
        print("   2. Missing required fields in the form")
        print("   3. CSRF token issues")
        print("   4. Session/authentication issues")
        print("   5. Route registration problems")
    elif direct_success and web_success:
        print("\n🎉 COMPLETE SUCCESS!")
        print("✅ Both direct and web interface working")
        print("✅ Order creation is fully functional")
    else:
        print("\n❌ DEEPER ISSUES")
        print("Both direct and web interface have problems")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
