#!/usr/bin/env python3
"""
Direct database inventory synchronization fix
"""

import sqlite3
import os

def fix_inventory_sync():
    """Fix inventory synchronization directly"""
    print("🚨 DIRECT INVENTORY SYNCHRONIZATION FIX")
    print("=" * 60)
    
    db_path = 'instance/medivent.db'
    if not os.path.exists(db_path):
        print("❌ Database not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        print("1. ANALYZING CONFLICTS")
        print("-" * 40)
        
        # Get conflicts
        cursor.execute('''
            SELECT p.product_id, p.name, p.stock_quantity,
                   COALESCE(SUM(i.stock_quantity - COALESCE(i.allocated_quantity, 0)), 0) as actual_available
            FROM products p
            LEFT JOIN inventory i ON p.product_id = i.product_id AND i.status = 'active'
            GROUP BY p.product_id, p.name, p.stock_quantity
            HAVING p.stock_quantity != COALESCE(SUM(i.stock_quantity - COALESCE(i.allocated_quantity, 0)), 0)
        ''')
        conflicts = cursor.fetchall()
        
        print(f"Found {len(conflicts)} conflicts")
        
        # Show priority conflicts (Cough Syrup and Paracetamol)
        priority_conflicts = []
        for conflict in conflicts:
            if 'Cough' in conflict['name'] or 'Paracetamol' in conflict['name']:
                priority_conflicts.append(conflict)
                print(f"🚨 PRIORITY: {conflict['name']}")
                print(f"   Products Table: {conflict['stock_quantity']} units")
                print(f"   Actual Available: {conflict['actual_available']} units")
        
        print("\n2. FIXING CONFLICTS")
        print("-" * 40)
        
        # Fix each conflict
        fixed = 0
        for conflict in conflicts:
            product_id = conflict['product_id']
            correct_stock = max(0, conflict['actual_available'])
            
            cursor.execute('''
                UPDATE products 
                SET stock_quantity = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE product_id = ?
            ''', (correct_stock, product_id))
            
            print(f"✅ {conflict['name']}: {conflict['stock_quantity']} → {correct_stock}")
            fixed += 1
        
        # Commit changes
        conn.commit()
        
        print(f"\n3. VERIFICATION")
        print("-" * 40)
        
        # Check if conflicts are resolved
        cursor.execute('''
            SELECT COUNT(*) as remaining_conflicts
            FROM products p
            LEFT JOIN inventory i ON p.product_id = i.product_id AND i.status = 'active'
            GROUP BY p.product_id
            HAVING p.stock_quantity != COALESCE(SUM(i.stock_quantity - COALESCE(i.allocated_quantity, 0)), 0)
        ''')
        
        remaining = cursor.fetchone()
        remaining_count = remaining['remaining_conflicts'] if remaining else 0
        
        # Verify priority products specifically
        cursor.execute('''
            SELECT p.name, p.stock_quantity,
                   COALESCE(SUM(i.stock_quantity - COALESCE(i.allocated_quantity, 0)), 0) as actual_available
            FROM products p
            LEFT JOIN inventory i ON p.product_id = i.product_id AND i.status = 'active'
            WHERE p.name LIKE '%Cough%' OR p.name LIKE '%Paracetamol%'
            GROUP BY p.product_id, p.name, p.stock_quantity
        ''')
        
        priority_after = cursor.fetchall()
        
        print("Priority products after fix:")
        for product in priority_after:
            status = "✅ FIXED" if product['stock_quantity'] == product['actual_available'] else "❌ STILL CONFLICT"
            print(f"  {product['name']}: {product['stock_quantity']} units - {status}")
        
        conn.close()
        
        print(f"\n4. SUMMARY")
        print("-" * 40)
        print(f"✅ Fixed {fixed} inventory conflicts")
        print(f"✅ Remaining conflicts: {remaining_count}")
        
        if remaining_count == 0:
            print("🎉 ALL CONFLICTS RESOLVED!")
        else:
            print("⚠️ Some conflicts may remain")
        
        print("🔄 REFRESH YOUR BROWSER TO SEE THE CHANGES!")
        
        return remaining_count == 0
        
    except Exception as e:
        print(f"❌ Fix failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = fix_inventory_sync()
    if success:
        print("\n🎉 INVENTORY SYNC COMPLETE!")
    else:
        print("\n❌ INVENTORY SYNC HAD ISSUES")
