# Flask ERP Network Access Configuration Guide

## Overview
This guide explains how to configure and access your Flask ERP system from multiple devices on the same local network.

## Current Configuration Status ✅

Your Flask ERP system is **already configured** for network access with the following settings:

### Application Configuration
- **Host Binding**: `0.0.0.0` (binds to all network interfaces)
- **Primary Port**: `5000`
- **Fallback Port**: `8080` (if port 5000 is busy)
- **Debug Mode**: Enabled for development

### Database & File Handling
- **Database**: SQLite with relative paths (`app.instance_path/medivent.db`)
- **Upload Folder**: Relative paths (`app.instance_path/uploads`)
- **File Access**: All file operations use relative paths that work from any network access point

## Accessing the Application

### 1. From the Host Machine (Server)
```
http://localhost:5000
```

### 2. From Other Devices on the Same Network
```
http://[HOST_IP_ADDRESS]:5000
```

## Finding Your Host Machine's IP Address

### Windows
1. Open Command Prompt (cmd)
2. Run: `ipconfig`
3. Look for "IPv4 Address" under your active network adapter
4. Example: `*************`

### Mac/Linux
1. Open Terminal
2. Run: `ifconfig` or `ip addr show`
3. Look for inet address under your active network interface
4. Example: `*************`

### Alternative Method (All Systems)
1. Open your web browser on the host machine
2. Go to: `https://whatismyipaddress.com/`
3. Note the "Private IP" address (not the public one)

## Network Access Examples

If your host machine's IP is `*************`, other devices can access:
- **Main URL**: `http://*************:5000`
- **Fallback URL**: `http://*************:8080` (if port 5000 is busy)

## Firewall Configuration

### Windows Firewall
1. Open "Windows Defender Firewall with Advanced Security"
2. Click "Inbound Rules" → "New Rule"
3. Select "Port" → "TCP" → Specific local ports: `5000,8080`
4. Allow the connection
5. Apply to all profiles (Domain, Private, Public)
6. Name the rule: "Flask ERP Access"

### Alternative Windows Method
1. Open Command Prompt as Administrator
2. Run these commands:
```cmd
netsh advfirewall firewall add rule name="Flask ERP Port 5000" dir=in action=allow protocol=TCP localport=5000
netsh advfirewall firewall add rule name="Flask ERP Port 8080" dir=in action=allow protocol=TCP localport=8080
```

### Mac Firewall
1. System Preferences → Security & Privacy → Firewall
2. Click "Firewall Options"
3. Add Python/Flask application to allowed apps
4. Or disable firewall temporarily for testing

### Linux (Ubuntu/Debian)
```bash
sudo ufw allow 5000
sudo ufw allow 8080
sudo ufw reload
```

## Testing Network Access

### Step 1: Start the Application
1. Navigate to your project directory
2. Run: `python app.py`
3. Verify you see the network URL message

### Step 2: Test Local Access
1. Open browser on host machine
2. Go to: `http://localhost:5000`
3. Verify the application loads correctly

### Step 3: Test Network Access
1. Find your host machine's IP address (see above)
2. On another device (phone, tablet, another computer):
   - Connect to the same WiFi network
   - Open web browser
   - Go to: `http://[HOST_IP]:5000`
3. Verify the application loads and functions correctly

## Troubleshooting

### Common Issues

#### 1. "This site can't be reached" Error
**Causes & Solutions:**
- **Firewall blocking**: Configure firewall (see above)
- **Wrong IP address**: Double-check host IP using `ipconfig`/`ifconfig`
- **Different networks**: Ensure both devices are on same WiFi/network
- **Port already in use**: Application will automatically try port 8080

#### 2. Application Loads but Features Don't Work
**Causes & Solutions:**
- **Database permissions**: Check that the SQLite file is writable
- **Upload folder permissions**: Ensure uploads directory exists and is writable
- **Static files**: Clear browser cache and refresh

#### 3. Slow Performance from Remote Devices
**Causes & Solutions:**
- **WiFi signal**: Move closer to router
- **Network congestion**: Limit other network usage
- **Debug mode**: Consider disabling debug mode for production use

### Network Diagnostics

#### Test Network Connectivity
From another device, test if the host is reachable:

**Windows/Mac/Linux:**
```bash
ping [HOST_IP_ADDRESS]
telnet [HOST_IP_ADDRESS] 5000
```

#### Check if Port is Open
**Windows:**
```cmd
netstat -an | findstr :5000
```

**Mac/Linux:**
```bash
netstat -an | grep :5000
```

## Security Considerations

### Development vs Production

**Current Setup (Development):**
- Debug mode enabled
- No authentication on network access
- Suitable for local network testing

**For Production Use:**
- Disable debug mode: `debug=False`
- Implement proper authentication
- Use HTTPS with SSL certificates
- Consider using a proper WSGI server (Gunicorn, uWSGI)

### Network Security
- Only use on trusted local networks
- Consider VPN for remote access
- Monitor access logs for unusual activity
- Keep the application updated

## Advanced Configuration

### Custom Port Configuration
To use a different port, modify `app.py`:
```python
app.run(host='0.0.0.0', port=YOUR_PORT, debug=True, use_reloader=False)
```

### Multiple Network Interfaces
The current configuration (`host='0.0.0.0'`) binds to all available network interfaces, including:
- Ethernet connections
- WiFi connections
- Virtual network adapters

## Support

If you encounter issues:
1. Check the console output for error messages
2. Verify firewall settings
3. Test with a simple ping to the host machine
4. Try accessing from the host machine first
5. Check that both devices are on the same network

## Quick Reference

| Component | Configuration | Status |
|-----------|---------------|---------|
| Host Binding | `0.0.0.0` | ✅ Configured |
| Primary Port | `5000` | ✅ Configured |
| Fallback Port | `8080` | ✅ Configured |
| Database Paths | Relative | ✅ Compatible |
| Upload Paths | Relative | ✅ Compatible |
| Firewall | User Setup Required | ⚠️ Manual |

**Next Steps**: Configure firewall and test network access from other devices.
