#!/usr/bin/env python3
"""
Emergency fix for order ID UNIQUE constraint issue
"""

import sqlite3
import os
import uuid
from datetime import datetime

def emergency_fix():
    db_path = 'instance/medivent.db'
    
    if not os.path.exists(db_path):
        print("❌ Database file not found")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 Checking for duplicate order IDs...")
        
        # Check for duplicate order IDs
        cursor.execute('SELECT order_id, COUNT(*) as count FROM orders GROUP BY order_id HAVING COUNT(*) > 1')
        duplicates = cursor.fetchall()
        
        if duplicates:
            print(f"❌ FOUND {len(duplicates)} DUPLICATE ORDER IDs:")
            for order_id, count in duplicates:
                print(f"  {order_id}: {count} occurrences")
                
                # Fix duplicates by updating all but the first one
                cursor.execute('SELECT id FROM orders WHERE order_id = ? ORDER BY id', (order_id,))
                ids = cursor.fetchall()
                
                # Keep the first one, update the rest
                for i, (row_id,) in enumerate(ids[1:], 1):
                    new_order_id = f"{order_id}_DUP_{i}"
                    cursor.execute('UPDATE orders SET order_id = ? WHERE id = ?', (new_order_id, row_id))
                    print(f"    Updated duplicate {row_id} to {new_order_id}")
            
            conn.commit()
            print("✅ Fixed duplicate order IDs")
        else:
            print("✅ No duplicate order IDs found")
        
        # Create order_sequence table if it doesn't exist
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='order_sequence'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("🔧 Creating order_sequence table...")
            cursor.execute('''
                CREATE TABLE order_sequence (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Initialize with current max order count + 1000 for safety
            cursor.execute('SELECT COUNT(*) FROM orders')
            order_count = cursor.fetchone()[0]
            start_id = order_count + 1000
            
            # Insert initial sequence entries
            for i in range(start_id):
                cursor.execute('INSERT INTO order_sequence DEFAULT VALUES')
            
            conn.commit()
            print(f"✅ Created order_sequence table starting at {start_id}")
        else:
            print("✅ Order sequence table already exists")
        
        # Test the order ID generation
        print("🧪 Testing order ID generation...")
        cursor.execute('INSERT INTO order_sequence DEFAULT VALUES')
        sequence_id = cursor.lastrowid
        test_order_id = f"ORD{sequence_id:08d}"
        print(f"✅ Test order ID: {test_order_id}")
        
        conn.commit()
        conn.close()
        
        print("🎉 Emergency fix completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during fix: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    emergency_fix()
