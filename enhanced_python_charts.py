#!/usr/bin/env python3
"""
Enhanced Python Charts Module
Advanced charting capabilities with machine learning insights for Medivent ERP
"""

import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
import io
import base64
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple

class EnhancedPythonCharts:
    """
    Enhanced charting with ML insights and advanced visualizations
    """
    
    def __init__(self):
        # Set modern color palette
        self.colors = {
            'primary': '#2E86AB',
            'secondary': '#A23B72', 
            'accent': '#F18F01',
            'success': '#C73E1D',
            'info': '#5D737E',
            'light': '#F5F5F5',
            'dark': '#2C3E50'
        }
        
        # Configure matplotlib and seaborn
        plt.style.use('seaborn-v0_8-whitegrid')
        sns.set_palette([self.colors['primary'], self.colors['secondary'], 
                        self.colors['accent'], self.colors['success'], self.colors['info']])
    
    def generate_predictive_sales_chart(self, data: List[Dict], forecast_days: int = 30) -> str:
        """
        Generate sales chart with ML-based predictions
        
        Args:
            data: Historical sales data
            forecast_days: Number of days to forecast
            
        Returns:
            HTML string with interactive predictive chart
        """
        try:
            if not data or len(data) < 10:
                return self._generate_error_html("Insufficient data for prediction (minimum 10 records required)")
            
            df = pd.DataFrame(data)
            df['date'] = pd.to_datetime(df['date'])
            df = df.sort_values('date')
            
            # Prepare data for ML model
            df['days_since_start'] = (df['date'] - df['date'].min()).dt.days
            X = df[['days_since_start']].values
            y = df['amount'].values
            
            # Train linear regression model
            model = LinearRegression()
            model.fit(X, y)
            
            # Generate predictions
            last_day = df['days_since_start'].max()
            future_days = np.arange(last_day + 1, last_day + forecast_days + 1).reshape(-1, 1)
            predictions = model.predict(future_days)
            
            # Create future dates
            future_dates = [df['date'].max() + timedelta(days=i) for i in range(1, forecast_days + 1)]
            
            # Create interactive plot
            fig = go.Figure()
            
            # Historical data
            fig.add_trace(go.Scatter(
                x=df['date'],
                y=df['amount'],
                mode='lines+markers',
                name='Historical Sales',
                line=dict(color=self.colors['primary'], width=3),
                marker=dict(size=6)
            ))
            
            # Predictions
            fig.add_trace(go.Scatter(
                x=future_dates,
                y=predictions,
                mode='lines+markers',
                name='Predicted Sales',
                line=dict(color=self.colors['accent'], width=3, dash='dash'),
                marker=dict(size=6, symbol='diamond')
            ))
            
            # Add trend line for historical data
            historical_trend = model.predict(X)
            fig.add_trace(go.Scatter(
                x=df['date'],
                y=historical_trend,
                mode='lines',
                name='Trend Line',
                line=dict(color=self.colors['secondary'], width=2, dash='dot'),
                opacity=0.7
            ))
            
            # Calculate R-squared
            r_squared = model.score(X, y)
            
            fig.update_layout(
                title=f'Sales Prediction with ML (R² = {r_squared:.3f})',
                xaxis_title='Date',
                yaxis_title='Sales Amount (₹)',
                template='plotly_white',
                height=600,
                hovermode='x unified',
                annotations=[
                    dict(
                        x=0.02, y=0.98,
                        xref='paper', yref='paper',
                        text=f'Forecast Period: {forecast_days} days<br>Model Accuracy: {r_squared:.1%}',
                        showarrow=False,
                        bgcolor='rgba(255,255,255,0.8)',
                        bordercolor='gray',
                        borderwidth=1
                    )
                ]
            )
            
            return fig.to_html(include_plotlyjs='cdn')
            
        except Exception as e:
            return self._generate_error_html(f"Error generating predictive chart: {str(e)}")
    
    def generate_customer_segmentation_chart(self, data: List[Dict]) -> str:
        """
        Generate customer segmentation using K-means clustering
        
        Args:
            data: Customer data with purchase history
            
        Returns:
            HTML string with segmentation visualization
        """
        try:
            if not data or len(data) < 5:
                return self._generate_error_html("Insufficient customer data for segmentation")
            
            df = pd.DataFrame(data)
            
            # Prepare features for clustering
            features = ['total_spent', 'order_count', 'avg_order_value']
            if not all(col in df.columns for col in features):
                return self._generate_error_html("Missing required columns for segmentation")
            
            # Standardize features
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(df[features])
            
            # Perform K-means clustering
            n_clusters = min(4, len(df) // 2)  # Ensure reasonable number of clusters
            kmeans = KMeans(n_clusters=n_clusters, random_state=42)
            df['segment'] = kmeans.fit_predict(X_scaled)
            
            # Create segment labels
            segment_labels = {0: 'High Value', 1: 'Medium Value', 2: 'Low Value', 3: 'New Customers'}
            df['segment_label'] = df['segment'].map(lambda x: segment_labels.get(x, f'Segment {x}'))
            
            # Create 3D scatter plot
            fig = go.Figure(data=go.Scatter3d(
                x=df['total_spent'],
                y=df['order_count'],
                z=df['avg_order_value'],
                mode='markers',
                marker=dict(
                    size=8,
                    color=df['segment'],
                    colorscale='Viridis',
                    showscale=True,
                    colorbar=dict(title="Customer Segment")
                ),
                text=df.apply(lambda row: f"Customer: {row.get('customer_name', 'N/A')}<br>"
                                        f"Segment: {row['segment_label']}<br>"
                                        f"Total Spent: ₹{row['total_spent']:,.2f}<br>"
                                        f"Orders: {row['order_count']}<br>"
                                        f"Avg Order: ₹{row['avg_order_value']:,.2f}", axis=1),
                hovertemplate='%{text}<extra></extra>'
            ))
            
            fig.update_layout(
                title='Customer Segmentation Analysis',
                scene=dict(
                    xaxis_title='Total Spent (₹)',
                    yaxis_title='Order Count',
                    zaxis_title='Average Order Value (₹)'
                ),
                height=700,
                template='plotly_white'
            )
            
            return fig.to_html(include_plotlyjs='cdn')
            
        except Exception as e:
            return self._generate_error_html(f"Error generating segmentation chart: {str(e)}")
    
    def generate_advanced_inventory_analytics(self, data: List[Dict]) -> str:
        """
        Generate advanced inventory analytics with multiple insights
        
        Args:
            data: Inventory data with stock levels, turnover rates
            
        Returns:
            HTML string with comprehensive inventory dashboard
        """
        try:
            if not data:
                return self._generate_error_html("No inventory data available")
            
            df = pd.DataFrame(data)
            
            # Create subplot dashboard
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=('Stock Level Distribution', 'ABC Analysis', 
                              'Turnover Rate vs Stock Level', 'Reorder Recommendations'),
                specs=[[{"type": "histogram"}, {"type": "scatter"}],
                       [{"type": "scatter"}, {"type": "bar"}]]
            )
            
            # Stock level distribution
            fig.add_trace(
                go.Histogram(x=df['current_stock'], name='Stock Distribution',
                           marker_color=self.colors['primary'], opacity=0.7),
                row=1, col=1
            )
            
            # ABC Analysis (if revenue data available)
            if 'annual_revenue' in df.columns:
                df_sorted = df.sort_values('annual_revenue', ascending=False)
                df_sorted['cumulative_revenue'] = df_sorted['annual_revenue'].cumsum()
                df_sorted['revenue_percentage'] = df_sorted['cumulative_revenue'] / df_sorted['annual_revenue'].sum() * 100
                
                # Classify into ABC categories
                df_sorted['abc_category'] = 'C'
                df_sorted.loc[df_sorted['revenue_percentage'] <= 80, 'abc_category'] = 'A'
                df_sorted.loc[(df_sorted['revenue_percentage'] > 80) & (df_sorted['revenue_percentage'] <= 95), 'abc_category'] = 'B'
                
                colors_abc = {'A': self.colors['success'], 'B': self.colors['accent'], 'C': self.colors['info']}
                
                for category in ['A', 'B', 'C']:
                    category_data = df_sorted[df_sorted['abc_category'] == category]
                    fig.add_trace(
                        go.Scatter(x=category_data.index, y=category_data['annual_revenue'],
                                 mode='markers', name=f'Category {category}',
                                 marker=dict(color=colors_abc[category], size=8)),
                        row=1, col=2
                    )
            
            # Turnover rate vs stock level (if turnover data available)
            if 'turnover_rate' in df.columns:
                fig.add_trace(
                    go.Scatter(x=df['current_stock'], y=df['turnover_rate'],
                             mode='markers', name='Turnover Analysis',
                             marker=dict(color=self.colors['secondary'], size=8)),
                    row=2, col=1
                )
            
            # Reorder recommendations
            if 'reorder_level' in df.columns:
                reorder_needed = df[df['current_stock'] <= df['reorder_level']].head(10)
                if not reorder_needed.empty:
                    fig.add_trace(
                        go.Bar(x=reorder_needed['product_name'], y=reorder_needed['current_stock'],
                             name='Reorder Needed', marker_color=self.colors['accent']),
                        row=2, col=2
                    )
            
            fig.update_layout(
                title_text="Advanced Inventory Analytics Dashboard",
                showlegend=True,
                height=800,
                template="plotly_white"
            )
            
            return fig.to_html(include_plotlyjs='cdn')
            
        except Exception as e:
            return self._generate_error_html(f"Error generating inventory analytics: {str(e)}")
    
    def generate_performance_heatmap(self, data: List[Dict], metric: str = 'sales') -> str:
        """
        Generate performance heatmap with time series analysis
        
        Args:
            data: Performance data with dates and metrics
            metric: Metric to analyze ('sales', 'orders', 'revenue')
            
        Returns:
            Base64 encoded heatmap image
        """
        try:
            if not data:
                return self._generate_no_data_chart("No performance data available")
            
            df = pd.DataFrame(data)
            df['date'] = pd.to_datetime(df['date'])
            
            # Extract time components
            df['hour'] = df['date'].dt.hour
            df['day_of_week'] = df['date'].dt.day_name()
            df['week'] = df['date'].dt.isocalendar().week
            
            # Create pivot table for heatmap
            if metric in df.columns:
                pivot_data = df.pivot_table(
                    values=metric, 
                    index='day_of_week', 
                    columns='hour', 
                    aggfunc='mean',
                    fill_value=0
                )
                
                # Create matplotlib heatmap
                fig, ax = plt.subplots(figsize=(15, 8))
                
                # Generate heatmap
                sns.heatmap(pivot_data, annot=True, fmt='.1f', cmap='YlOrRd',
                           cbar_kws={'label': f'Average {metric.title()}'}, ax=ax)
                
                ax.set_title(f'{metric.title()} Performance Heatmap by Day and Hour', 
                           fontsize=16, fontweight='bold', pad=20)
                ax.set_xlabel('Hour of Day', fontsize=12)
                ax.set_ylabel('Day of Week', fontsize=12)
                
                # Rotate labels for better readability
                plt.xticks(rotation=0)
                plt.yticks(rotation=0)
                plt.tight_layout()
                
                return self._chart_to_base64(fig)
            else:
                return self._generate_error_chart(f"Metric '{metric}' not found in data")
                
        except Exception as e:
            return self._generate_error_chart(f"Error generating performance heatmap: {str(e)}")
    
    def generate_correlation_network(self, data: List[Dict]) -> str:
        """
        Generate network graph showing correlations between metrics
        
        Args:
            data: Data with multiple numeric metrics
            
        Returns:
            HTML string with network visualization
        """
        try:
            if not data:
                return self._generate_error_html("No data available for correlation analysis")
            
            df = pd.DataFrame(data)
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            
            if len(numeric_cols) < 2:
                return self._generate_error_html("Insufficient numeric columns for correlation analysis")
            
            # Calculate correlation matrix
            corr_matrix = df[numeric_cols].corr()
            
            # Create network graph
            fig = go.Figure()
            
            # Add nodes
            for i, col in enumerate(numeric_cols):
                fig.add_trace(go.Scatter(
                    x=[i], y=[0],
                    mode='markers+text',
                    marker=dict(size=30, color=self.colors['primary']),
                    text=col,
                    textposition='middle center',
                    name=col,
                    showlegend=False
                ))
            
            # Add edges for strong correlations
            for i, col1 in enumerate(numeric_cols):
                for j, col2 in enumerate(numeric_cols):
                    if i < j and abs(corr_matrix.loc[col1, col2]) > 0.5:
                        correlation = corr_matrix.loc[col1, col2]
                        color = self.colors['success'] if correlation > 0 else self.colors['accent']
                        
                        fig.add_trace(go.Scatter(
                            x=[i, j], y=[0, 0],
                            mode='lines',
                            line=dict(color=color, width=abs(correlation) * 5),
                            name=f'{col1}-{col2}: {correlation:.2f}',
                            showlegend=False
                        ))
            
            fig.update_layout(
                title='Metric Correlation Network',
                xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
                yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
                height=500,
                template='plotly_white'
            )
            
            return fig.to_html(include_plotlyjs='cdn')
            
        except Exception as e:
            return self._generate_error_html(f"Error generating correlation network: {str(e)}")
    
    def _chart_to_base64(self, fig) -> str:
        """Convert matplotlib figure to base64 string"""
        img_buffer = io.BytesIO()
        fig.savefig(img_buffer, format='png', bbox_inches='tight', 
                   facecolor='white', edgecolor='none', dpi=150)
        img_buffer.seek(0)
        img_str = base64.b64encode(img_buffer.getvalue()).decode()
        plt.close(fig)
        return f"data:image/png;base64,{img_str}"
    
    def _generate_no_data_chart(self, message: str) -> str:
        """Generate a chart showing no data message"""
        fig, ax = plt.subplots(figsize=(10, 6))
        ax.text(0.5, 0.5, message, ha='center', va='center', 
               fontsize=16, transform=ax.transAxes,
               bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.7))
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        return self._chart_to_base64(fig)
    
    def _generate_error_chart(self, error_message: str) -> str:
        """Generate a chart showing error message"""
        fig, ax = plt.subplots(figsize=(10, 6))
        ax.text(0.5, 0.5, f"Chart Generation Error:\n{error_message}", 
               ha='center', va='center', fontsize=12, transform=ax.transAxes,
               bbox=dict(boxstyle="round,pad=0.3", facecolor="lightcoral", alpha=0.7))
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        return self._chart_to_base64(fig)
    
    def _generate_error_html(self, error_message: str) -> str:
        """Generate HTML for errors"""
        return f"""
        <div style="text-align: center; padding: 50px; background-color: #f8d7da; 
                    border-radius: 8px; color: #721c24; border: 1px solid #f5c6cb;">
            <h3><i class="fas fa-exclamation-triangle"></i> Chart Generation Error</h3>
            <p>{error_message}</p>
            <small>Please check your data and try again.</small>
        </div>
        """

# Convenience functions for backward compatibility
def generate_predictive_chart(data, forecast_days=30):
    """Generate predictive chart - backward compatibility function"""
    generator = EnhancedPythonCharts()
    return generator.generate_predictive_sales_chart(data, forecast_days)

def generate_customer_segments(data):
    """Generate customer segmentation - backward compatibility function"""
    generator = EnhancedPythonCharts()
    return generator.generate_customer_segmentation_chart(data)

def generate_inventory_analytics(data):
    """Generate inventory analytics - backward compatibility function"""
    generator = EnhancedPythonCharts()
    return generator.generate_advanced_inventory_analytics(data)
