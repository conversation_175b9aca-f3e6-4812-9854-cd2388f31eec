#!/usr/bin/env python3
"""
Final Complete Verification Test
Verify that both UNIQUE constraint error and BuildError are completely resolved
"""

import requests
import sqlite3
import time
from datetime import datetime

def test_builderror_resolution():
    """Test that BuildError is completely resolved"""
    print("🔧 TESTING BUILDERROR RESOLUTION")
    print("=" * 50)
    
    base_url = "http://localhost:5001"
    session = requests.Session()
    
    try:
        # Login
        login_data = {'username': 'admin', 'password': 'admin123'}
        login_response = session.post(f"{base_url}/login", data=login_data)
        print(f"   🔐 Login status: {login_response.status_code}")
        
        # Test dashboard (where BuildError occurred)
        dashboard_response = session.get(f"{base_url}/")
        print(f"   🏠 Dashboard status: {dashboard_response.status_code}")
        
        # Test orders page
        orders_response = session.get(f"{base_url}/orders")
        print(f"   📋 Orders page status: {orders_response.status_code}")
        
        # Test new order form
        new_order_response = session.get(f"{base_url}/orders/new")
        print(f"   📝 New order form status: {new_order_response.status_code}")
        
        # Check if all pages load without BuildError
        all_success = all(resp.status_code == 200 for resp in [
            login_response, dashboard_response, orders_response, new_order_response
        ])
        
        if all_success:
            print("   ✅ BuildError completely resolved!")
            return True
        else:
            print("   ❌ Some pages still have issues")
            return False
            
    except Exception as e:
        print(f"   ❌ BuildError test failed: {e}")
        return False

def test_unique_constraint_resolution():
    """Test that UNIQUE constraint error is completely resolved"""
    print("\n🆔 TESTING UNIQUE CONSTRAINT RESOLUTION")
    print("=" * 50)
    
    base_url = "http://localhost:5001"
    session = requests.Session()
    
    try:
        # Login
        login_data = {'username': 'admin', 'password': 'admin123'}
        session.post(f"{base_url}/login", data=login_data)
        
        # Get initial order count
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM orders')
        initial_count = cursor.fetchone()[0]
        print(f"   📊 Initial order count: {initial_count}")
        conn.close()
        
        # Test 1: Terminal order creation
        print("   🖥️  Testing terminal order creation...")
        from app import app
        with app.app_context():
            from database import get_db
            from routes.orders import generate_order_id, generate_order_item_id
            
            db = get_db()
            order_id = generate_order_id()
            order_item_id = generate_order_item_id()
            
            print(f"      Generated order ID: {order_id}")
            
            # Create order
            db.execute('BEGIN IMMEDIATE TRANSACTION')
            
            db.execute('''
                INSERT INTO orders (
                    order_id, customer_name, customer_address, customer_phone,
                    payment_method, status, sales_agent, updated_by, order_date, last_updated
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                order_id, 'Final Verification Customer', 'Final Verification Address', '555-FINAL-VER',
                'cash', 'Placed', 'admin', 'admin', datetime.now(), datetime.now()
            ))
            
            db.execute('''
                INSERT INTO order_items (
                    order_item_id, order_id, product_id, product_name, strength,
                    quantity, foc_quantity, unit_price, line_total, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                order_item_id, order_id, 'P001', 'Paracetamol 500mg', '500mg',
                1, 0, 25.5, 25.5, 'Placed'
            ))
            
            db.execute('UPDATE orders SET order_amount = ? WHERE order_id = ?', (25.5, order_id))
            db.execute('COMMIT')
            
            print(f"      ✅ Terminal order created: {order_id}")
        
        # Test 2: Web interface order creation
        print("   🌐 Testing web interface order creation...")
        order_data = {
            'customer_name': 'Final Web Verification Customer',
            'customer_address': 'Final Web Verification Address',
            'customer_phone': '555-FINAL-WEB-VER',
            'payment_mode': 'cash',  # Using correct field name
            'po_number': 'FINAL-WEB-VER-001',
            'sales_agent': 'admin',
            'product_id[]': ['P001'],
            'quantity[]': ['1'],
            'rate[]': ['25.50'],
            'foc_quantity[]': ['0']
        }
        
        response = session.post(f"{base_url}/orders/new", data=order_data, allow_redirects=False)
        print(f"      Response status: {response.status_code}")
        
        if response.status_code == 302:
            redirect_url = response.headers.get('Location', '')
            if '/orders/' in redirect_url and redirect_url != '/orders/new':
                print(f"      ✅ Web order created successfully")
                web_success = True
            else:
                print(f"      ❌ Web order creation failed")
                web_success = False
        else:
            print(f"      ❌ Unexpected response: {response.status_code}")
            web_success = False
        
        # Verify final count
        time.sleep(1)
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM orders')
        final_count = cursor.fetchone()[0]
        print(f"   📊 Final order count: {final_count}")
        
        orders_created = final_count - initial_count
        print(f"   📈 Orders created: {orders_created}")
        
        if orders_created >= 2 and web_success:
            print("   ✅ UNIQUE constraint error completely resolved!")
            return True
        else:
            print("   ❌ UNIQUE constraint issues may remain")
            return False
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ UNIQUE constraint test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complete_system_functionality():
    """Test complete system functionality"""
    print("\n🔄 TESTING COMPLETE SYSTEM FUNCTIONALITY")
    print("=" * 50)
    
    base_url = "http://localhost:5001"
    session = requests.Session()
    
    try:
        # Login
        login_data = {'username': 'admin', 'password': 'admin123'}
        session.post(f"{base_url}/login", data=login_data)
        
        # Test key endpoints
        endpoints = [
            ('/', 'Dashboard'),
            ('/orders', 'Orders List'),
            ('/orders/new', 'New Order Form'),
            ('/orders/search', 'Order Search'),
        ]
        
        results = {}
        for endpoint, name in endpoints:
            try:
                response = session.get(f"{base_url}{endpoint}", timeout=10)
                success = response.status_code == 200
                results[name] = success
                status = "✅" if success else "❌"
                print(f"   {status} {name}: {response.status_code}")
            except Exception as e:
                results[name] = False
                print(f"   ❌ {name}: {e}")
        
        # Test order creation workflow
        print("   🔄 Testing order creation workflow...")
        
        # Get order form
        form_response = session.get(f"{base_url}/orders/new")
        if form_response.status_code == 200:
            print("      ✅ Order form accessible")
            
            # Submit order
            order_data = {
                'customer_name': 'System Test Customer',
                'customer_address': 'System Test Address',
                'customer_phone': '555-SYSTEM-TEST',
                'payment_mode': 'cash',
                'po_number': 'SYSTEM-TEST-001',
                'sales_agent': 'admin',
                'product_id[]': ['P001'],
                'quantity[]': ['1'],
                'rate[]': ['25.50'],
                'foc_quantity[]': ['0']
            }
            
            submit_response = session.post(f"{base_url}/orders/new", data=order_data, allow_redirects=False)
            
            if submit_response.status_code == 302:
                redirect_url = submit_response.headers.get('Location', '')
                if '/orders/' in redirect_url:
                    print("      ✅ Order submission successful")
                    results['Order Creation'] = True
                else:
                    print("      ❌ Order submission failed")
                    results['Order Creation'] = False
            else:
                print(f"      ❌ Order submission error: {submit_response.status_code}")
                results['Order Creation'] = False
        else:
            print("      ❌ Order form not accessible")
            results['Order Creation'] = False
        
        # Calculate success rate
        success_count = sum(results.values())
        total_tests = len(results)
        success_rate = success_count / total_tests * 100
        
        print(f"   📊 System functionality: {success_count}/{total_tests} ({success_rate:.1f}%)")
        
        return success_rate >= 80
        
    except Exception as e:
        print(f"   ❌ System functionality test failed: {e}")
        return False

def main():
    """Run final complete verification"""
    print("🏁 FINAL COMPLETE VERIFICATION")
    print("=" * 80)
    
    # Wait for server to be ready
    print("⏳ Waiting for server to be ready...")
    time.sleep(2)
    
    results = {}
    
    # Test 1: BuildError resolution
    results['BuildError Resolution'] = test_builderror_resolution()
    
    # Test 2: UNIQUE constraint resolution
    results['UNIQUE Constraint Resolution'] = test_unique_constraint_resolution()
    
    # Test 3: Complete system functionality
    results['System Functionality'] = test_complete_system_functionality()
    
    # Final summary
    print("\n" + "=" * 80)
    print("🎯 FINAL VERIFICATION RESULTS")
    print("=" * 80)
    
    for test, success in results.items():
        status = "✅ SUCCESS" if success else "❌ FAILED"
        print(f"{test}: {status}")
    
    total_success = sum(results.values())
    total_tests = len(results)
    overall_success_rate = total_success / total_tests * 100
    
    print(f"\n🏆 Overall Success Rate: {total_success}/{total_tests} ({overall_success_rate:.1f}%)")
    
    if overall_success_rate == 100:
        print("\n🎉 COMPLETE SUCCESS!")
        print("✅ BuildError completely resolved")
        print("✅ UNIQUE constraint error completely resolved")
        print("✅ Both terminal and web interface working perfectly")
        print("✅ All system functionality operational")
        print("✅ Order creation workflow fully functional")
        print("\n🏆 MISSION ACCOMPLISHED!")
        print("🎊 The system is now operating at 100% capacity!")
    elif overall_success_rate >= 80:
        print("\n🎉 SUBSTANTIAL SUCCESS!")
        print(f"✅ {overall_success_rate:.1f}% of functionality working")
        print("✅ Major issues resolved")
        print("⚠️  Minor issues may remain")
    else:
        print("\n⚠️  PARTIAL SUCCESS")
        print(f"💡 {overall_success_rate:.1f}% success rate")
        print("💡 Additional work may be needed")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
