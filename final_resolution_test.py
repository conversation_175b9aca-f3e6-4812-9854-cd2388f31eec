#!/usr/bin/env python3
"""
Final test to confirm the discrepancy is resolved
"""

import requests
import sqlite3
import time

def test_final_resolution():
    """Test that both terminal and web interface work correctly"""
    print("🎯 FINAL RESOLUTION TEST")
    print("=" * 80)
    
    base_url = "http://localhost:5001"
    session = requests.Session()
    
    try:
        # Wait for server to start
        print("1. Waiting for server to start...")
        time.sleep(5)
        
        # Login
        print("2. Logging in...")
        login_data = {'username': 'admin', 'password': 'admin123'}
        response = session.post(f"{base_url}/login", data=login_data, allow_redirects=False)
        
        if response.status_code != 302:
            print("❌ Login failed")
            return False
        
        print("✅ Login successful")
        
        # Get initial order count
        print("3. Getting initial order count...")
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM orders')
        initial_count = cursor.fetchone()[0]
        print(f"   Initial order count: {initial_count}")
        
        cursor.execute('SELECT order_id FROM orders ORDER BY order_date DESC LIMIT 1')
        latest_order = cursor.fetchone()
        latest_order_id = latest_order[0] if latest_order else "None"
        print(f"   Latest order ID: {latest_order_id}")
        conn.close()
        
        # Submit order via web interface
        print("4. Submitting order via web interface...")
        order_data = {
            'customer_name': 'Final Resolution Test Customer',
            'customer_address': 'Final Resolution Test Address',
            'customer_phone': '555-FINAL-RESOLUTION',
            'payment_method': 'cash',
            'po_number': 'FINAL-RESOLUTION-001',
            'product_id[]': ['P001'],
            'quantity[]': ['1'],
            'foc_quantity[]': ['0']
        }
        
        response = session.post(f"{base_url}/orders/new", 
                               data=order_data, 
                               timeout=15,
                               allow_redirects=True)
        
        print(f"   Response status: {response.status_code}")
        print(f"   Final URL: {response.url}")
        
        # Check if order was created
        print("5. Checking if order was created...")
        time.sleep(2)  # Give database time to update
        
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM orders')
        final_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT order_id, customer_name FROM orders ORDER BY order_date DESC LIMIT 1')
        new_order = cursor.fetchone()
        conn.close()
        
        print(f"   Final order count: {final_count}")
        
        if final_count > initial_count:
            new_order_id, customer_name = new_order
            print(f"✅ Order created: {new_order_id} - {customer_name}")
            
            if 'Final Resolution Test Customer' in customer_name:
                print("🎉 WEB INTERFACE ORDER CREATION SUCCESSFUL!")
                return True
            else:
                print("⚠️  Order created but not from our test")
                return False
        else:
            print("❌ No new order created")
            
            # Check for error messages
            if '/orders/new' in response.url:
                print("   Redirected back to order form - checking for errors...")
                if 'alert-danger' in response.text:
                    import re
                    error_match = re.search(r'alert-danger[^>]*>([^<]+)', response.text)
                    if error_match:
                        print(f"   Error: {error_match.group(1).strip()}")
            
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_terminal_order_creation():
    """Test terminal order creation to ensure it still works"""
    print("\n🖥️  TESTING TERMINAL ORDER CREATION")
    print("=" * 60)
    
    try:
        from app import app
        with app.app_context():
            from database import get_db
            from routes.orders import generate_order_id, generate_order_item_id
            from datetime import datetime
            
            db = get_db()
            
            # Generate IDs
            order_id = generate_order_id()
            order_item_id = generate_order_item_id()
            
            print(f"Generated order ID: {order_id}")
            print(f"Generated order item ID: {order_item_id}")
            
            # Create order
            db.execute('BEGIN IMMEDIATE TRANSACTION')
            
            # Insert order
            db.execute('''
                INSERT INTO orders (
                    order_id, customer_name, customer_address, customer_phone,
                    payment_method, status, sales_agent, updated_by, order_date, last_updated
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                order_id, 'Terminal Test Customer', 'Terminal Test Address', '555-TERMINAL-TEST',
                'cash', 'Placed', 'admin', 'admin', datetime.now(), datetime.now()
            ))
            
            # Insert order item
            db.execute('''
                INSERT INTO order_items (
                    order_item_id, order_id, product_id, product_name, strength,
                    quantity, foc_quantity, unit_price, line_total, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                order_item_id, order_id, 'P001', 'Paracetamol 500mg', '500mg',
                1, 0, 25.5, 25.5, 'Placed'
            ))
            
            # Update order total
            db.execute('UPDATE orders SET order_amount = ? WHERE order_id = ?', (25.5, order_id))
            
            # Commit
            db.execute('COMMIT')
            
            print(f"✅ Terminal order created: {order_id}")
            return True
            
    except Exception as e:
        print(f"❌ Terminal order creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run final resolution test"""
    print("🏁 FINAL DISCREPANCY RESOLUTION TEST")
    print("=" * 80)
    
    # Test 1: Terminal order creation
    terminal_success = test_terminal_order_creation()
    
    # Test 2: Web interface order creation
    web_success = test_final_resolution()
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 FINAL RESOLUTION RESULTS")
    print("=" * 80)
    print(f"Terminal Order Creation: {'✅ SUCCESS' if terminal_success else '❌ FAILED'}")
    print(f"Web Interface Order Creation: {'✅ SUCCESS' if web_success else '❌ FAILED'}")
    
    if terminal_success and web_success:
        print("\n🎉 COMPLETE SUCCESS!")
        print("✅ Terminal tests working")
        print("✅ Web interface working")
        print("✅ Both systems are now consistent")
        print("✅ Discrepancy completely resolved!")
        print("\n💡 ROOT CAUSE IDENTIFIED AND FIXED:")
        print("   - Conflicting route in app.py was overriding the blueprint route")
        print("   - Old route had incorrect order_item_id handling")
        print("   - Old route used different form validation")
        print("   - Removed conflicting route, now using correct blueprint")
    elif terminal_success and not web_success:
        print("\n⚠️  PARTIAL SUCCESS")
        print("✅ Terminal tests working")
        print("❌ Web interface still has issues")
        print("💡 May need additional debugging")
    elif not terminal_success and web_success:
        print("\n⚠️  UNEXPECTED RESULT")
        print("❌ Terminal tests failing")
        print("✅ Web interface working")
        print("💡 Terminal system may have been affected by changes")
    else:
        print("\n❌ BOTH SYSTEMS FAILING")
        print("💡 Need to investigate further")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
