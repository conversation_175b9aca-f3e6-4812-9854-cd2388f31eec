@echo off
cls
echo ============================================================
echo 🔄 FLASK SERVER RESTART SCRIPT
echo ============================================================
echo.

echo 🔥 Step 1: Killing all Python processes...
taskkill /F /IM python.exe >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Python processes killed
) else (
    echo ℹ️ No Python processes were running
)

echo.
echo ⏳ Step 2: Waiting 3 seconds for cleanup...
timeout /t 3 /nobreak >nul

echo.
echo 🧪 Step 3: Testing syntax...
python -c "print('✅ Python is working')" 2>nul
if %errorlevel% == 0 (
    echo ✅ Python syntax check passed
) else (
    echo ❌ Python syntax check failed
    pause
    exit /b 1
)

echo.
echo 🚀 Step 4: Starting Flask server...
echo 📝 Starting server in new window...
start "Flask Server - Ledger Application" cmd /k "python app.py"

echo.
echo ⏳ Step 5: Waiting for server to start...
timeout /t 5 /nobreak >nul

echo.
echo ✅ Server restart completed!
echo.
echo 🌐 Your application should be available at:
echo    http://127.0.0.1:5000
echo    http://localhost:5000
echo.
echo 📋 Next steps:
echo    1. Check the Flask server window for any error messages
echo    2. Open your browser and navigate to the URL above
echo    3. Try the Delivery Challan generation workflow
echo.
echo ============================================================
echo Press any key to exit this script...
pause >nul
