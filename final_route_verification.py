#!/usr/bin/env python3
"""
Final Route Verification
Verify that the partial_pending routes are working on port 5001
"""

import requests
import time

def test_routes():
    """Test all partial pending routes"""
    print("🔍 TESTING PARTIAL PENDING ROUTES ON PORT 5001")
    print("=" * 60)
    
    routes = [
        ('http://127.0.0.1:5001/partial-pending/', 'Main Dashboard'),
        ('http://127.0.0.1:5001/partial-pending/status', 'Status API'),
    ]
    
    results = {}
    
    for url, description in routes:
        try:
            print(f"\n📍 Testing: {description}")
            print(f"   URL: {url}")
            
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                print(f"   ✅ SUCCESS - Status: {response.status_code}")
                
                # Show response preview
                if 'status' in url:
                    try:
                        data = response.json()
                        print(f"   📊 Response: {data}")
                    except:
                        print(f"   📄 Response: {response.text[:100]}...")
                else:
                    print(f"   📄 Response: {response.text[:100]}...")
                
                results[url] = True
                
            elif response.status_code == 302:
                print(f"   🔄 REDIRECT - Status: {response.status_code}")
                print(f"   📍 Location: {response.headers.get('Location', 'Unknown')}")
                results[url] = True  # Redirect is acceptable for login-required routes
                
            else:
                print(f"   ❌ FAILED - Status: {response.status_code}")
                print(f"   📄 Response: {response.text[:200]}...")
                results[url] = False
                
        except requests.exceptions.ConnectionError:
            print(f"   ❌ CONNECTION ERROR - Flask app not running")
            results[url] = False
            
        except Exception as e:
            print(f"   ❌ ERROR - {e}")
            results[url] = False
    
    # Summary
    print("\n" + "=" * 60)
    print("🎯 VERIFICATION SUMMARY")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = sum(1 for success in results.values() if success)
    success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
    
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Success Rate: {success_rate:.1f}%")
    
    if success_rate == 100:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Partial Pending Blueprint is working correctly on port 5001")
        print("🌐 You can now access: http://127.0.0.1:5001/partial-pending/")
    elif success_rate >= 50:
        print("\n⚠️  PARTIAL SUCCESS")
        print("💡 Some routes are working, check individual results above")
    else:
        print("\n❌ TESTS FAILED")
        print("💡 Check if Flask app is running: python app.py")
    
    return success_rate == 100

if __name__ == "__main__":
    success = test_routes()
    
    if success:
        print("\n🚀 NEXT STEPS:")
        print("1. ✅ Routes are working - you can use the partial pending system")
        print("2. 🔧 To add more features, edit routes/partial_pending.py")
        print("3. 🎨 To customize the UI, create templates/partial_pending/")
        print("4. 📊 To add database features, import from database")
    else:
        print("\n🔧 TROUBLESHOOTING:")
        print("1. Make sure Flask app is running: python app.py")
        print("2. Check for any error messages in the Flask console")
        print("3. Verify port 5001 is not blocked by firewall")
        print("4. Try restarting the Flask application")
