#!/usr/bin/env python3
"""
Final comprehensive test to verify strftime error is completely resolved
"""

import requests
import json
import time
import sys

def test_assignment_dashboard_complete():
    """Complete test of assignment dashboard functionality"""
    print("🎯 FINAL COMPREHENSIVE STRFTIME ERROR TEST")
    print("=" * 70)
    
    try:
        # Test the main assignment dashboard page
        print("1️⃣ Testing assignment dashboard page...")
        response = requests.get('http://localhost:5000/riders/assignment-dashboard', timeout=15)
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ Dashboard failed with status {response.status_code}")
            return False
        
        # Check for the specific error message the user reported
        error_patterns = [
            "Error loading assignment form: 'str object' has no attribute 'strftime'",
            "'str' object has no attribute 'strftime'",
            "Error loading assignment form",
            "strftime"
        ]
        
        found_errors = []
        for pattern in error_patterns:
            if pattern in response.text:
                found_errors.append(pattern)
        
        if found_errors:
            print("❌ ERRORS FOUND IN DASHBOARD:")
            for error in found_errors:
                print(f"   - {error}")
            return False
        else:
            print("✅ No strftime errors found in dashboard")
        
        # Check for expected content
        expected_content = [
            "Rider Assignment Dashboard",
            "Orders Ready for Rider Assignment",
            "Available Riders"
        ]
        
        for content in expected_content:
            if content in response.text:
                print(f"✅ Found expected content: {content}")
            else:
                print(f"⚠️ Missing expected content: {content}")
        
        print("\n2️⃣ Testing live tracking API (sidebar functionality)...")
        
        # Create session to maintain cookies
        session = requests.Session()
        
        # Get session from main page
        session.get('http://localhost:5000/riders/assignment-dashboard', timeout=10)
        
        # Test the API endpoint
        api_response = session.get('http://localhost:5000/riders/api/live-tracking-data', timeout=10)
        
        print(f"   API Status: {api_response.status_code}")
        print(f"   Content-Type: {api_response.headers.get('content-type', 'Unknown')}")
        
        if api_response.status_code == 200:
            content_type = api_response.headers.get('content-type', '')
            
            if 'application/json' in content_type:
                try:
                    data = api_response.json()
                    print("✅ API returned valid JSON")
                    
                    # Check data structure
                    required_keys = ['active_orders', 'recent_updates', 'today_stats', 'last_updated']
                    for key in required_keys:
                        if key in data:
                            print(f"✅ API contains {key}")
                        else:
                            print(f"❌ API missing {key}")
                            return False
                    
                    # Check datetime formatting in active orders
                    if 'active_orders' in data and data['active_orders']:
                        print(f"✅ Found {len(data['active_orders'])} active orders")
                        for i, order in enumerate(data['active_orders'][:3]):  # Check first 3
                            dispatch_time = order.get('dispatch_time', 'N/A')
                            print(f"   Order {i+1}: {order.get('order_id')} - Dispatch: {dispatch_time}")
                    
                    return True
                    
                except json.JSONDecodeError as e:
                    print(f"❌ API JSON decode error: {e}")
                    return False
            else:
                print("❌ API returned HTML instead of JSON")
                
                # Check for strftime error in API
                if "'str' object has no attribute 'strftime'" in api_response.text:
                    print("🎯 FOUND STRFTIME ERROR IN API!")
                    return False
                
                return False
        else:
            print(f"❌ API failed with status {api_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def test_datetime_functionality():
    """Test datetime functionality specifically"""
    print("\n3️⃣ Testing datetime functionality...")
    
    try:
        # Import and test the datetime functions directly
        sys.path.append('.')
        from routes.modern_riders import safe_datetime_format
        
        # Test various datetime formats
        test_cases = [
            ("2025-07-31 07:38:21", "2025-07-31 07:38"),
            ("2025-07-26 11:17:39.885061", "2025-07-26 11:17"),
            ("2025-07-28", "2025-07-28 00:00"),
            (None, "N/A"),
            ("", "N/A"),
            ("invalid", "invalid")
        ]
        
        all_passed = True
        for input_val, expected in test_cases:
            try:
                result = safe_datetime_format(input_val, '%Y-%m-%d %H:%M')
                if result == expected:
                    print(f"✅ '{input_val}' -> '{result}'")
                else:
                    print(f"❌ '{input_val}' -> '{result}' (expected '{expected}')")
                    all_passed = False
            except Exception as e:
                print(f"❌ '{input_val}' -> ERROR: {e}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Datetime test error: {e}")
        return False

def main():
    """Main test function"""
    print("🔍 COMPREHENSIVE STRFTIME ERROR RESOLUTION TEST")
    print("=" * 80)
    
    # Wait for server
    time.sleep(2)
    
    # Run all tests
    dashboard_test = test_assignment_dashboard_complete()
    datetime_test = test_datetime_functionality()
    
    print("\n" + "=" * 80)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 80)
    
    if dashboard_test:
        print("✅ Assignment Dashboard Test: PASSED")
    else:
        print("❌ Assignment Dashboard Test: FAILED")
    
    if datetime_test:
        print("✅ Datetime Functionality Test: PASSED")
    else:
        print("❌ Datetime Functionality Test: FAILED")
    
    if dashboard_test and datetime_test:
        print("\n🎉 SUCCESS: ALL TESTS PASSED!")
        print("🎯 STRFTIME ERROR HAS BEEN COMPLETELY RESOLVED!")
        return True
    else:
        print("\n❌ FAILURE: SOME TESTS FAILED")
        print("🔧 STRFTIME ERROR STILL EXISTS")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
