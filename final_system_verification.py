#!/usr/bin/env python3
"""
Final System Verification for Product Management and Partial DC Generation
Tests all implemented features and ensures system integrity
"""

import sqlite3
import requests
import json
from datetime import datetime

def test_product_status_filtering():
    """Test that only active products appear in order forms and DC generation"""
    print("🔍 Testing Product Status Filtering:")
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        # Test 1: Check products table has status columns
        cursor.execute("PRAGMA table_info(products)")
        columns = [col[1] for col in cursor.fetchall()]
        
        has_status = 'status' in columns
        has_is_active = 'is_active' in columns
        
        print(f"   ✅ Products table has 'status' column: {has_status}")
        print(f"   ✅ Products table has 'is_active' column: {has_is_active}")
        
        # Test 2: Count active vs total products
        cursor.execute("SELECT COUNT(*) FROM products")
        total_products = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM products WHERE status = 'active' AND is_active = 1")
        active_products = cursor.fetchone()[0]
        
        print(f"   📊 Total products: {total_products}")
        print(f"   📊 Active products: {active_products}")
        
        # Test 3: Verify filtering in order placement query
        cursor.execute('''
            SELECT COUNT(DISTINCT p.product_id) FROM products p
            JOIN divisions d ON p.division_id = d.division_id
            LEFT JOIN inventory i ON p.product_id = i.product_id AND i.status = 'active'
            WHERE d.status = 'Active' AND p.status = 'active' AND p.is_active = 1
        ''')
        filtered_for_orders = cursor.fetchone()[0]
        
        print(f"   ✅ Products available for orders: {filtered_for_orders}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Product filtering test failed: {e}")
        return False

def test_partial_dc_functionality():
    """Test partial DC generation setup"""
    print("\n📋 Testing Partial DC Generation:")
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        # Test 1: Check dc_pending_quantities table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='dc_pending_quantities'")
        table_exists = cursor.fetchone() is not None
        print(f"   ✅ dc_pending_quantities table exists: {table_exists}")
        
        if table_exists:
            # Test 2: Check table schema
            cursor.execute("PRAGMA table_info(dc_pending_quantities)")
            columns = [col[1] for col in cursor.fetchall()]
            
            required_columns = ['order_id', 'product_id', 'pending_quantity', 'status']
            for col in required_columns:
                has_col = col in columns
                print(f"   ✅ Column '{col}' exists: {has_col}")
        
        # Test 3: Check if we have orders that could use partial DC
        cursor.execute("SELECT COUNT(*) FROM orders WHERE status = 'Approved'")
        approved_orders = cursor.fetchone()[0]
        print(f"   📊 Approved orders available: {approved_orders}")
        
        conn.close()
        return table_exists
        
    except Exception as e:
        print(f"   ❌ Partial DC test failed: {e}")
        return False

def test_route_accessibility():
    """Test critical routes are accessible"""
    print("\n🌐 Testing Route Accessibility:")
    
    base_url = "http://127.0.0.1:5001"
    
    critical_routes = [
        ("/", "Dashboard"),
        ("/product_management", "Product Management"),
        ("/products/product_management/", "Products Management"),
        ("/inventory/", "Inventory"),
        ("/orders/new", "New Order"),
        ("/dc/pending-quantities", "DC Pending Quantities")
    ]
    
    results = []
    
    for route, name in critical_routes:
        try:
            response = requests.get(f"{base_url}{route}", timeout=10)
            status_ok = response.status_code == 200
            status_symbol = "✅" if status_ok else "❌"
            print(f"   {status_symbol} {name} ({route}) - Status: {response.status_code}")
            results.append(status_ok)
            
        except requests.exceptions.RequestException as e:
            print(f"   ❌ {name} ({route}) - Error: Connection failed")
            results.append(False)
    
    return all(results)

def test_database_integrity():
    """Test database integrity and relationships"""
    print("\n🗄️ Testing Database Integrity:")
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        # Test critical tables
        critical_tables = [
            'products', 'divisions', 'orders', 'order_items', 
            'inventory', 'delivery_challans', 'dc_pending_quantities'
        ]
        
        all_tables_exist = True
        for table in critical_tables:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            exists = cursor.fetchone() is not None
            status = "✅" if exists else "❌"
            print(f"   {status} Table '{table}' exists")
            if not exists:
                all_tables_exist = False
        
        # Test data integrity
        cursor.execute("SELECT COUNT(*) FROM products")
        product_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM inventory WHERE status = 'active'")
        inventory_count = cursor.fetchone()[0]
        
        print(f"   📊 Products in database: {product_count}")
        print(f"   📦 Active inventory records: {inventory_count}")
        
        conn.close()
        return all_tables_exist
        
    except Exception as e:
        print(f"   ❌ Database integrity test failed: {e}")
        return False

def test_template_rendering():
    """Test that templates render without errors"""
    print("\n🎨 Testing Template Rendering:")
    
    base_url = "http://127.0.0.1:5001"
    
    template_routes = [
        ("/products/product_management/", "Product Management Template"),
        ("/inventory/", "Inventory Template"),
        ("/dc/pending-quantities", "DC Pending Template")
    ]
    
    results = []
    
    for route, name in template_routes:
        try:
            response = requests.get(f"{base_url}{route}", timeout=10)
            
            # Check for template errors
            content = response.text.lower()
            has_errors = any(error in content for error in [
                'templatesyntaxerror', 'undefinederror', 'error 500',
                'internal server error', 'template not found'
            ])
            
            status_ok = response.status_code == 200 and not has_errors
            status_symbol = "✅" if status_ok else "❌"
            
            if has_errors:
                print(f"   {status_symbol} {name} - Template errors detected")
            else:
                print(f"   {status_symbol} {name} - Renders correctly")
                
            results.append(status_ok)
            
        except Exception as e:
            print(f"   ❌ {name} - Template test failed")
            results.append(False)
    
    return all(results)

def main():
    """Run final system verification"""
    print("🚀 FINAL SYSTEM VERIFICATION")
    print("Testing Product Management & Partial DC Generation")
    print("=" * 60)
    
    tests = [
        ("Product Status Filtering", test_product_status_filtering),
        ("Partial DC Functionality", test_partial_dc_functionality),
        ("Route Accessibility", test_route_accessibility),
        ("Database Integrity", test_database_integrity),
        ("Template Rendering", test_template_rendering)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Final Summary
    print("\n" + "=" * 60)
    print("🎯 FINAL VERIFICATION RESULTS:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} {test_name}")
    
    print(f"\n📊 Overall Score: {passed}/{total} ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 SYSTEM VERIFICATION COMPLETE!")
        print("✅ All features implemented successfully")
        print("✅ Product status filtering active")
        print("✅ Partial DC generation ready")
        print("✅ Database integrity verified")
        print("✅ All routes accessible")
        print("✅ Templates rendering correctly")
        print("\n🚀 System is ready for production use!")
    else:
        print(f"\n⚠️ {total - passed} test(s) failed")
        print("Please review and fix the failed components")

if __name__ == "__main__":
    main()
