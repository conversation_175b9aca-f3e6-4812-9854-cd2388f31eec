#!/usr/bin/env python3
"""
Final comprehensive test for all product routes
"""

import requests
import time

def test_route(url, route_name):
    """Test a single route"""
    try:
        print(f"🧪 Testing {route_name}: {url}")
        
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            print(f"✅ {route_name} - Status: {response.status_code}")
            return True
        else:
            print(f"❌ {route_name} - Status: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"❌ {route_name} - Connection failed")
        return False
    except Exception as e:
        print(f"❌ {route_name} - Error: {str(e)}")
        return False

def main():
    """Run comprehensive tests"""
    print("🚀 FINAL COMPREHENSIVE ROUTE TESTING")
    print("=" * 60)
    
    base_url = "http://192.168.99.34:5001"
    
    # Test routes
    routes_to_test = [
        ("/products/product_management", "Product Management"),
        ("/products/update_selection", "Update Product Selection"),
        ("/products/new", "New Product"),
        ("/products/view_all", "View All Products"),
        ("/", "Dashboard"),
    ]
    
    passed = 0
    total = len(routes_to_test)
    
    for route, name in routes_to_test:
        url = f"{base_url}{route}"
        if test_route(url, name):
            passed += 1
        time.sleep(0.5)  # Small delay between requests
    
    print("\n" + "=" * 60)
    print(f"📊 RESULTS: {passed}/{total} routes passed")
    
    if passed == total:
        print("🎉 ALL ROUTES ARE WORKING!")
        print("✅ The duplicate route issue has been resolved")
        print("✅ Navigation links should work correctly")
        print("✅ Stats variable is being passed properly")
        print("✅ Error 'stats is undefined' has been fixed")
    else:
        print("❌ Some routes failed - check server logs")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
