#!/usr/bin/env python3
"""
Final test of web interface with correct authentication route
"""

import requests
import time
import re

def test_final_web_interface():
    """Test web interface with correct authentication"""
    print("🔐 FINAL WEB INTERFACE TEST WITH CORRECT AUTHENTICATION")
    print("=" * 80)
    
    base_url = "http://localhost:5001"
    session = requests.Session()
    
    try:
        # Wait for server to start
        print("1. Waiting for server to start...")
        time.sleep(5)
        
        # Step 1: Test server connectivity
        print("2. Testing server connectivity...")
        response = session.get(f"{base_url}/", timeout=10)
        print(f"   Root page status: {response.status_code}")
        
        if response.status_code != 200:
            print("❌ Server not accessible")
            return False
        
        # Step 2: Get login page (correct route)
        print("3. Getting login page...")
        response = session.get(f"{base_url}/login", timeout=10)
        print(f"   Login page status: {response.status_code}")
        
        if response.status_code != 200:
            print("❌ Cannot access login page")
            return False
        
        # Step 3: Try to login with default credentials
        print("4. Attempting login...")
        
        # Try common default credentials
        credentials = [
            ('admin', 'admin'),
            ('admin', 'admin123'),
            ('admin', 'password'),
            ('test', 'test'),
            ('user', 'user')
        ]
        
        login_successful = False
        for username, password in credentials:
            login_data = {
                'username': username,
                'password': password
            }
            
            response = session.post(f"{base_url}/login", 
                                   data=login_data, 
                                   timeout=10,
                                   allow_redirects=False)
            
            print(f"   Trying {username}/{password}: {response.status_code}")
            
            if response.status_code == 302:
                redirect_url = response.headers.get('Location', '')
                if 'dashboard' in redirect_url:
                    print(f"✅ Login successful with {username}/{password}")
                    login_successful = True
                    break
        
        if not login_successful:
            print("❌ All login attempts failed")
            print("   Creating a test user in database...")
            
            # Create test user directly in database
            try:
                import sqlite3
                from werkzeug.security import generate_password_hash
                
                conn = sqlite3.connect('instance/medivent.db')
                cursor = conn.cursor()
                
                # Check if test user exists
                cursor.execute('SELECT id FROM users WHERE username = ?', ('testuser',))
                if not cursor.fetchone():
                    # Create test user
                    password_hash = generate_password_hash('testpass')
                    cursor.execute('''
                        INSERT INTO users (username, password_hash, full_name, email, role, status)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', ('testuser', password_hash, 'Test User', '<EMAIL>', 'admin', 'active'))
                    conn.commit()
                    print("   Test user created: testuser/testpass")
                
                conn.close()
                
                # Try login with test user
                login_data = {'username': 'testuser', 'password': 'testpass'}
                response = session.post(f"{base_url}/login", 
                                       data=login_data, 
                                       timeout=10,
                                       allow_redirects=False)
                
                if response.status_code == 302:
                    print("✅ Login successful with test user")
                    login_successful = True
                else:
                    print("❌ Test user login failed")
                    return False
                    
            except Exception as e:
                print(f"❌ Error creating test user: {e}")
                return False
        
        # Step 4: Access order creation page
        print("5. Accessing order creation page...")
        response = session.get(f"{base_url}/orders/new", timeout=10)
        print(f"   Order creation page: {response.status_code}")
        
        if response.status_code != 200:
            print("❌ Cannot access order creation page after login")
            return False
        
        # Step 5: Submit order
        print("6. Submitting order...")
        order_data = {
            'customer_name': 'Final Test Customer',
            'customer_address': 'Final Test Address',
            'customer_phone': '555-FINAL-TEST',
            'payment_method': 'cash',
            'po_number': 'FINAL-TEST-001',
            'product_id[]': ['P001'],
            'quantity[]': ['1'],
            'foc_quantity[]': ['0']
        }
        
        response = session.post(f"{base_url}/orders/new", 
                               data=order_data, 
                               timeout=15,
                               allow_redirects=False)
        
        print(f"   Order submission: {response.status_code}")
        
        if response.status_code == 302:
            redirect_url = response.headers.get('Location', '')
            print(f"   Redirected to: {redirect_url}")
            
            if '/orders/' in redirect_url and redirect_url != '/orders/':
                print("✅ ORDER CREATED SUCCESSFULLY!")
                order_id = redirect_url.split('/orders/')[-1].split('/')[0]
                print(f"   Order ID: {order_id}")
                return True
            else:
                print("⚠️  Unexpected redirect")
                return False
                
        elif response.status_code == 200:
            # Check for errors
            if "UNIQUE constraint failed" in response.text:
                print("❌ UNIQUE constraint error still occurring!")
                print("   The web interface is still using old code")
                return False
            elif "Error placing order" in response.text:
                error_match = re.search(r'Error placing order: ([^<]+)', response.text)
                if error_match:
                    error_msg = error_match.group(1).strip()
                    print(f"❌ Order error: {error_msg}")
                return False
            else:
                print("⚠️  Order form returned without clear success/error")
                return False
        else:
            print(f"❌ Unexpected status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error during test: {e}")
        return False

def check_database_for_new_order():
    """Check if the order was actually created in database"""
    print("\n📊 Checking Database for New Order")
    print("=" * 50)
    
    try:
        import sqlite3
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        # Get most recent order
        cursor.execute('''
            SELECT order_id, customer_name, status, order_date 
            FROM orders 
            ORDER BY order_date DESC 
            LIMIT 1
        ''')
        
        latest_order = cursor.fetchone()
        if latest_order:
            order_id, customer, status, date = latest_order
            print(f"Latest order: {order_id} - {customer} - {status} ({date})")
            
            if 'Final Test Customer' in customer:
                print("✅ Web interface order found in database!")
                return True
            else:
                print("⚠️  Latest order is not from web interface test")
                return False
        else:
            print("❌ No orders found in database")
            return False
            
        conn.close()
        
    except Exception as e:
        print(f"❌ Database check error: {e}")
        return False

def main():
    """Run final comprehensive test"""
    print("🎯 FINAL COMPREHENSIVE WEB INTERFACE TEST")
    print("=" * 80)
    
    # Test web interface
    web_success = test_final_web_interface()
    
    # Check database
    db_success = check_database_for_new_order()
    
    # Summary
    print("\n" + "=" * 80)
    print("🏁 FINAL TEST RESULTS")
    print("=" * 80)
    print(f"Web Interface Test: {'✅ SUCCESS' if web_success else '❌ FAILED'}")
    print(f"Database Verification: {'✅ SUCCESS' if db_success else '❌ FAILED'}")
    
    if web_success and db_success:
        print("\n🎉 COMPLETE SUCCESS!")
        print("✅ Web interface is working correctly")
        print("✅ Order creation is functional")
        print("✅ Database integration is working")
        print("✅ Authentication is working")
        print("\n💡 The discrepancy has been resolved!")
        print("   - Terminal tests: ✅ Working")
        print("   - Web interface: ✅ Working")
        print("   - Both systems are now consistent")
    elif web_success and not db_success:
        print("\n⚠️  PARTIAL SUCCESS")
        print("✅ Web interface responds correctly")
        print("❌ Database verification failed")
        print("💡 Order may have been created but not found in expected format")
    else:
        print("\n❌ TEST FAILED")
        print("💡 The web interface still has issues")
        print("   Possible causes:")
        print("   1. Flask server not using updated code")
        print("   2. Authentication issues")
        print("   3. Route registration problems")
        print("   4. Database connection issues")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
