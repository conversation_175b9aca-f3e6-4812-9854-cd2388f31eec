import sqlite3

# Quick database check
conn = sqlite3.connect('instance/medivent.db')
conn.row_factory = sqlite3.Row
cursor = conn.cursor()

print("=== FINANCE DATABASE CHECK ===")

# 1. Check invoice_holds
try:
    cursor.execute('SELECT COUNT(*) as count FROM invoice_holds')
    holds_count = cursor.fetchone()['count']
    print(f"Invoice holds: {holds_count}")
    
    if holds_count == 0:
        print("  ❌ No held invoices - this is why page shows empty")
except Exception as e:
    print(f"Invoice holds table: ERROR - {e}")

# 2. Check customers with outstanding
try:
    cursor.execute('''
        SELECT COUNT(*) as count FROM (
            SELECT customer_name FROM orders 
            WHERE status != "Cancelled" 
            GROUP BY customer_name 
            HAVING SUM(CASE WHEN payment_status = "pending" THEN order_amount ELSE 0 END) > 0
        )
    ''')
    outstanding_count = cursor.fetchone()['count']
    print(f"Customers with outstanding: {outstanding_count}")
    
    if outstanding_count == 0:
        print("  ❌ No customers with outstanding amounts - this is why ledger shows empty")
except Exception as e:
    print(f"Customer check error: {e}")

# 3. Check specific order
try:
    cursor.execute('SELECT customer_name, order_amount, payment_status FROM orders WHERE order_id = "ORD00000147"')
    order = cursor.fetchone()
    if order:
        print(f"ORD00000147: {order['customer_name']} - Rs.{order['order_amount']} - {order['payment_status']}")
    else:
        print("ORD00000147: NOT FOUND")
except Exception as e:
    print(f"Order check error: {e}")

# 4. Check payment statuses
try:
    cursor.execute('SELECT payment_status, COUNT(*) as count FROM orders WHERE status != "Cancelled" GROUP BY payment_status')
    statuses = cursor.fetchall()
    print("Payment statuses:")
    for status in statuses:
        print(f"  {status['payment_status']}: {status['count']}")
except Exception as e:
    print(f"Payment status error: {e}")

# 5. Check total orders and customers
try:
    cursor.execute('SELECT COUNT(*) as count FROM orders WHERE status != "Cancelled"')
    total_orders = cursor.fetchone()['count']
    print(f"Total active orders: {total_orders}")
    
    cursor.execute('SELECT COUNT(DISTINCT customer_name) as count FROM orders WHERE status != "Cancelled"')
    total_customers = cursor.fetchone()['count']
    print(f"Total customers with orders: {total_customers}")
except Exception as e:
    print(f"Totals error: {e}")

conn.close()
print("=== CHECK COMPLETE ===")
