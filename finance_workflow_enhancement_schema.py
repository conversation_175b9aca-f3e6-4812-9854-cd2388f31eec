#!/usr/bin/env python3
"""
Finance Workflow Enhancement Database Schema
Creates comprehensive database schema for enhanced finance workflow features
"""

import sqlite3
import os
from datetime import datetime

def create_enhanced_finance_schema():
    """Create enhanced finance workflow database schema"""
    
    # Connect to database
    db_path = 'instance/medivent.db'
    if not os.path.exists('instance'):
        os.makedirs('instance')
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    print("🚀 Creating Enhanced Finance Workflow Schema...")
    print("=" * 60)
    
    try:
        # 1. Invoice Hold System
        print("\n1️⃣ Creating invoice_holds table...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS invoice_holds (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                hold_id TEXT UNIQUE NOT NULL,
                order_id TEXT NOT NULL,
                invoice_id TEXT,
                hold_reason TEXT NOT NULL,
                hold_comments TEXT,
                hold_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                hold_by TEXT NOT NULL,
                release_date TIMESTAMP,
                release_by TEXT,
                release_comments TEXT,
                status TEXT DEFAULT 'active' CHECK (status IN ('active', 'released')),
                priority_level TEXT DEFAULT 'normal' CHECK (priority_level IN ('low', 'normal', 'high', 'urgent')),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (order_id) REFERENCES orders(order_id),
                FOREIGN KEY (invoice_id) REFERENCES invoices(invoice_number)
            )
        ''')
        print("   ✅ invoice_holds table created")
        
        # 2. Enhanced Comments System
        print("\n2️⃣ Creating finance_comments table...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS finance_comments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                comment_id TEXT UNIQUE NOT NULL,
                entity_type TEXT NOT NULL CHECK (entity_type IN ('order', 'invoice', 'payment', 'hold', 'release')),
                entity_id TEXT NOT NULL,
                comment_text TEXT NOT NULL,
                comment_type TEXT DEFAULT 'general' CHECK (comment_type IN ('general', 'approval', 'hold', 'release', 'payment', 'system')),
                created_by TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_internal BOOLEAN DEFAULT 0,
                parent_comment_id TEXT,
                FOREIGN KEY (parent_comment_id) REFERENCES finance_comments(comment_id)
            )
        ''')
        print("   ✅ finance_comments table created")
        
        # 3. Payment Attachments System
        print("\n3️⃣ Creating payment_attachments table...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS payment_attachments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                attachment_id TEXT UNIQUE NOT NULL,
                payment_id TEXT,
                order_id TEXT,
                invoice_id TEXT,
                file_name TEXT NOT NULL,
                file_path TEXT NOT NULL,
                file_type TEXT NOT NULL,
                file_size INTEGER,
                upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                uploaded_by TEXT NOT NULL,
                attachment_type TEXT DEFAULT 'payment_proof' CHECK (attachment_type IN ('payment_proof', 'cheque', 'bank_slip', 'receipt', 'other')),
                verification_status TEXT DEFAULT 'pending' CHECK (verification_status IN ('pending', 'verified', 'rejected')),
                verified_by TEXT,
                verified_at TIMESTAMP,
                verification_notes TEXT,
                FOREIGN KEY (payment_id) REFERENCES payments(payment_id),
                FOREIGN KEY (order_id) REFERENCES orders(order_id),
                FOREIGN KEY (invoice_id) REFERENCES invoices(invoice_number)
            )
        ''')
        print("   ✅ payment_attachments table created")
        
        # 4. Enhanced Payments System
        print("\n4️⃣ Creating payments_enhanced table...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS payments_enhanced (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                payment_id TEXT UNIQUE NOT NULL,
                invoice_id TEXT,
                order_id TEXT NOT NULL,
                customer_id TEXT NOT NULL,
                payment_amount DECIMAL(15,2) NOT NULL,
                payment_method TEXT DEFAULT 'cash' CHECK (payment_method IN ('cash', 'cheque', 'bank_transfer', 'online', 'card', 'other')),
                payment_date DATE DEFAULT CURRENT_DATE,
                payment_time TIME DEFAULT CURRENT_TIME,
                reference_number TEXT,
                bank_details TEXT,
                cheque_number TEXT,
                cheque_date DATE,
                payment_status TEXT DEFAULT 'pending' CHECK (payment_status IN ('pending', 'verified', 'cleared', 'bounced', 'cancelled')),
                is_partial BOOLEAN DEFAULT 0,
                remaining_amount DECIMAL(15,2) DEFAULT 0,
                payment_notes TEXT,
                collected_by TEXT NOT NULL,
                verified_by TEXT,
                verification_date TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (invoice_id) REFERENCES invoices(invoice_number),
                FOREIGN KEY (order_id) REFERENCES orders(order_id),
                FOREIGN KEY (customer_id) REFERENCES customers(customer_id)
            )
        ''')
        print("   ✅ payments_enhanced table created")
        
        # 5. Accounts Receivable System
        print("\n5️⃣ Creating accounts_receivable table...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS accounts_receivable (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                receivable_id TEXT UNIQUE NOT NULL,
                invoice_id TEXT NOT NULL,
                order_id TEXT NOT NULL,
                customer_id TEXT NOT NULL,
                customer_name TEXT NOT NULL,
                invoice_amount DECIMAL(15,2) NOT NULL,
                paid_amount DECIMAL(15,2) DEFAULT 0,
                outstanding_amount DECIMAL(15,2) NOT NULL,
                invoice_date DATE NOT NULL,
                due_date DATE NOT NULL,
                days_outstanding INTEGER DEFAULT 0,
                aging_bucket TEXT DEFAULT '0-30' CHECK (aging_bucket IN ('0-30', '31-60', '61-90', '91-120', '120+')),
                status TEXT DEFAULT 'outstanding' CHECK (status IN ('outstanding', 'partial', 'paid', 'overdue', 'written_off')),
                payment_terms INTEGER DEFAULT 30,
                last_payment_date DATE,
                last_payment_amount DECIMAL(15,2) DEFAULT 0,
                collection_notes TEXT,
                assigned_collector TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (invoice_id) REFERENCES invoices(invoice_number),
                FOREIGN KEY (order_id) REFERENCES orders(order_id),
                FOREIGN KEY (customer_id) REFERENCES customers(customer_id)
            )
        ''')
        print("   ✅ accounts_receivable table created")
        
        # 6. Salesperson and Division Ledgers
        print("\n6️⃣ Creating salesperson_ledger table...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS salesperson_ledger (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ledger_id TEXT UNIQUE NOT NULL,
                salesperson_name TEXT NOT NULL,
                transaction_date DATE NOT NULL,
                transaction_type TEXT NOT NULL CHECK (transaction_type IN ('sale', 'return', 'commission', 'adjustment')),
                order_id TEXT,
                invoice_id TEXT,
                customer_name TEXT,
                sale_amount DECIMAL(15,2) DEFAULT 0,
                commission_rate DECIMAL(5,2) DEFAULT 0,
                commission_amount DECIMAL(15,2) DEFAULT 0,
                payment_status TEXT DEFAULT 'pending',
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (order_id) REFERENCES orders(order_id),
                FOREIGN KEY (invoice_id) REFERENCES invoices(invoice_number)
            )
        ''')
        print("   ✅ salesperson_ledger table created")
        
        print("\n7️⃣ Creating division_ledger table...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS division_ledger (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ledger_id TEXT UNIQUE NOT NULL,
                division_id TEXT NOT NULL,
                division_name TEXT NOT NULL,
                transaction_date DATE NOT NULL,
                transaction_type TEXT NOT NULL CHECK (transaction_type IN ('sale', 'return', 'expense', 'transfer', 'adjustment')),
                order_id TEXT,
                invoice_id TEXT,
                customer_name TEXT,
                amount DECIMAL(15,2) NOT NULL,
                description TEXT,
                reference_number TEXT,
                created_by TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (order_id) REFERENCES orders(order_id),
                FOREIGN KEY (invoice_id) REFERENCES invoices(invoice_number)
            )
        ''')
        print("   ✅ division_ledger table created")
        
        # 7. Create indexes for performance
        print("\n8️⃣ Creating performance indexes...")
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_invoice_holds_order_id ON invoice_holds(order_id)",
            "CREATE INDEX IF NOT EXISTS idx_invoice_holds_status ON invoice_holds(status)",
            "CREATE INDEX IF NOT EXISTS idx_finance_comments_entity ON finance_comments(entity_type, entity_id)",
            "CREATE INDEX IF NOT EXISTS idx_payment_attachments_payment_id ON payment_attachments(payment_id)",
            "CREATE INDEX IF NOT EXISTS idx_payments_enhanced_order_id ON payments_enhanced(order_id)",
            "CREATE INDEX IF NOT EXISTS idx_accounts_receivable_customer_id ON accounts_receivable(customer_id)",
            "CREATE INDEX IF NOT EXISTS idx_accounts_receivable_status ON accounts_receivable(status)",
            "CREATE INDEX IF NOT EXISTS idx_salesperson_ledger_salesperson ON salesperson_ledger(salesperson_name)",
            "CREATE INDEX IF NOT EXISTS idx_division_ledger_division ON division_ledger(division_id)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        print("   ✅ Performance indexes created")
        
        # Commit all changes
        conn.commit()
        print(f"\n✅ Enhanced Finance Workflow Schema Created Successfully!")
        print(f"📅 Created at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating schema: {str(e)}")
        conn.rollback()
        return False
        
    finally:
        conn.close()

if __name__ == "__main__":
    success = create_enhanced_finance_schema()
    if success:
        print("\n🎉 Database schema enhancement completed successfully!")
    else:
        print("\n💥 Database schema enhancement failed!")
