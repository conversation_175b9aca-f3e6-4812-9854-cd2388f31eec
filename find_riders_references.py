#!/usr/bin/env python3
"""
Find all remaining url_for('riders') references
"""

import os
import re

def find_riders_references():
    """Find all files with url_for('riders') references"""
    
    print("🔍 SEARCHING FOR REMAINING url_for('riders') REFERENCES")
    print("=" * 60)
    
    problematic_files = []
    
    # Search patterns
    patterns = [
        r"url_for\(['\"]riders['\"]\)",
        r"url_for\(['\"]riders['\"]",
        r"url_for\('riders'\)",
        r'url_for\("riders"\)',
    ]
    
    # File extensions to search
    extensions = ['.py', '.html', '.htm', '.jinja2', '.j2']
    
    # Directories to skip
    skip_dirs = ['__pycache__', '.git', 'node_modules', 'venv', '.venv']
    
    for root, dirs, files in os.walk('.'):
        # Skip certain directories
        dirs[:] = [d for d in dirs if d not in skip_dirs]
        
        for file in files:
            if any(file.endswith(ext) for ext in extensions):
                file_path = os.path.join(root, file)
                
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        lines = content.split('\n')
                    
                    for pattern in patterns:
                        matches = re.finditer(pattern, content, re.IGNORECASE)
                        for match in matches:
                            # Find line number
                            line_num = content[:match.start()].count('\n') + 1
                            line_content = lines[line_num - 1].strip()
                            
                            problematic_files.append({
                                'file': file_path,
                                'line': line_num,
                                'content': line_content,
                                'pattern': pattern
                            })
                            
                except Exception as e:
                    print(f"❌ Error reading {file_path}: {e}")
                    continue
    
    if problematic_files:
        print(f"❌ Found {len(problematic_files)} problematic references:")
        print("-" * 60)
        
        for ref in problematic_files:
            print(f"📁 File: {ref['file']}")
            print(f"📍 Line {ref['line']}: {ref['content']}")
            print(f"🔍 Pattern: {ref['pattern']}")
            print("-" * 40)
            
        return problematic_files
    else:
        print("✅ No problematic url_for('riders') references found!")
        return []

def find_endpoint_checks():
    """Find endpoint checks that might be problematic"""
    
    print("\n🔍 SEARCHING FOR ENDPOINT CHECKS")
    print("=" * 60)
    
    problematic_checks = []
    
    # Search for endpoint checks
    patterns = [
        r"request\.endpoint\s*==\s*['\"]riders['\"]",
        r"endpoint\s*==\s*['\"]riders['\"]",
    ]
    
    extensions = ['.py', '.html', '.htm', '.jinja2', '.j2']
    skip_dirs = ['__pycache__', '.git', 'node_modules', 'venv', '.venv']
    
    for root, dirs, files in os.walk('.'):
        dirs[:] = [d for d in dirs if d not in skip_dirs]
        
        for file in files:
            if any(file.endswith(ext) for ext in extensions):
                file_path = os.path.join(root, file)
                
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        lines = content.split('\n')
                    
                    for pattern in patterns:
                        matches = re.finditer(pattern, content, re.IGNORECASE)
                        for match in matches:
                            line_num = content[:match.start()].count('\n') + 1
                            line_content = lines[line_num - 1].strip()
                            
                            problematic_checks.append({
                                'file': file_path,
                                'line': line_num,
                                'content': line_content,
                                'pattern': pattern
                            })
                            
                except Exception as e:
                    continue
    
    if problematic_checks:
        print(f"❌ Found {len(problematic_checks)} problematic endpoint checks:")
        print("-" * 60)
        
        for ref in problematic_checks:
            print(f"📁 File: {ref['file']}")
            print(f"📍 Line {ref['line']}: {ref['content']}")
            print(f"🔍 Pattern: {ref['pattern']}")
            print("-" * 40)
            
        return problematic_checks
    else:
        print("✅ No problematic endpoint checks found!")
        return []

def main():
    """Main function"""
    
    # Find url_for references
    url_for_refs = find_riders_references()
    
    # Find endpoint checks
    endpoint_checks = find_endpoint_checks()
    
    # Summary
    print("\n📊 SUMMARY")
    print("=" * 60)
    print(f"🔗 url_for('riders') references: {len(url_for_refs)}")
    print(f"🎯 Endpoint checks: {len(endpoint_checks)}")
    
    total_issues = len(url_for_refs) + len(endpoint_checks)
    
    if total_issues == 0:
        print("\n🎉 NO ISSUES FOUND!")
        print("✅ All references appear to be correctly formatted")
    else:
        print(f"\n❌ TOTAL ISSUES: {total_issues}")
        print("These need to be fixed to resolve the BuildError")
    
    return total_issues == 0

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
