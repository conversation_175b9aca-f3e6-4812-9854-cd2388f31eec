#!/usr/bin/env python3
"""
Find all instances where sqlite3.Row objects are using .get() method
"""

import os
import re

def find_row_get_usage():
    """Find all .get() usage that might be on sqlite3.Row objects"""
    
    print("🔍 FINDING SQLITE3.ROW .get() USAGE")
    print("=" * 60)
    
    problematic_files = []
    
    # Patterns to look for
    patterns = [
        r'(\w+)\.get\([\'"](\w+)[\'"]',  # variable.get('key')
        r'(\w+)\.get\([\'"](\w+)[\'"],\s*[^)]+\)',  # variable.get('key', default)
    ]
    
    # File extensions to check
    extensions = ['.py']
    
    # Directories to skip
    skip_dirs = ['__pycache__', '.git', 'node_modules', 'venv', 'env']
    
    for root, dirs, files in os.walk('.'):
        # Skip certain directories
        dirs[:] = [d for d in dirs if d not in skip_dirs]
        
        for file in files:
            if any(file.endswith(ext) for ext in extensions):
                file_path = os.path.join(root, file)
                
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        lines = content.split('\n')
                    
                    for pattern in patterns:
                        matches = re.finditer(pattern, content)
                        for match in matches:
                            # Find line number
                            line_num = content[:match.start()].count('\n') + 1
                            line_content = lines[line_num - 1].strip()
                            
                            # Check if this might be a Row object
                            variable_name = match.group(1)
                            key_name = match.group(2)
                            
                            # Look for context clues that this is a Row object
                            context_lines = lines[max(0, line_num-10):line_num+5]
                            context = '\n'.join(context_lines)
                            
                            is_likely_row = False
                            
                            # Check for database-related context
                            if any(keyword in context.lower() for keyword in [
                                'fetchone', 'fetchall', 'cursor', 'execute', 'db.execute',
                                'sqlite3.row', 'row_factory', 'get_db'
                            ]):
                                is_likely_row = True
                            
                            # Check if variable name suggests it's a database row
                            if any(name in variable_name.lower() for name in [
                                'order', 'product', 'customer', 'user', 'inventory', 
                                'item', 'row', 'record', 'entry'
                            ]):
                                is_likely_row = True
                            
                            if is_likely_row:
                                problematic_files.append({
                                    'file': file_path,
                                    'line': line_num,
                                    'content': line_content,
                                    'variable': variable_name,
                                    'key': key_name,
                                    'pattern': pattern
                                })
                            
                except Exception as e:
                    print(f"❌ Error reading {file_path}: {e}")
                    continue
    
    # Report findings
    if problematic_files:
        print(f"🚨 Found {len(problematic_files)} potential sqlite3.Row .get() usage:")
        print("-" * 60)
        
        for issue in problematic_files:
            print(f"📁 {issue['file']}:{issue['line']}")
            print(f"   Variable: {issue['variable']}")
            print(f"   Key: {issue['key']}")
            print(f"   Code: {issue['content']}")
            print()
    else:
        print("✅ No problematic .get() usage found on sqlite3.Row objects")
    
    return problematic_files

if __name__ == "__main__":
    issues = find_row_get_usage()
    
    if issues:
        print("=" * 60)
        print("🔧 RECOMMENDED FIXES:")
        print("=" * 60)
        
        for issue in issues:
            print(f"File: {issue['file']}:{issue['line']}")
            print(f"Change: {issue['variable']}.get('{issue['key']}') → {issue['variable']}['{issue['key']}'] if '{issue['key']}' in {issue['variable']}.keys() else default_value")
            print()
    
    print("=" * 60)
