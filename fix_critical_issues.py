#!/usr/bin/env python3
"""
Fix Critical Issues: Missing Routes and Database Tables
"""

import sqlite3
import os
from datetime import datetime

def create_delivery_challans_table():
    """Create the missing delivery_challans table"""
    db_path = 'instance/medivent.db'
    
    if not os.path.exists(db_path):
        print(f"❌ Database not found at {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if table exists
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='delivery_challans'
        """)
        
        if cursor.fetchone():
            print("✅ delivery_challans table already exists")
            conn.close()
            return True
        
        # Create delivery_challans table
        cursor.execute('''
            CREATE TABLE delivery_challans (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                dc_number TEXT UNIQUE NOT NULL,
                order_id TEXT NOT NULL,
                warehouse_id TEXT,
                customer_name TEXT,
                customer_address TEXT,
                status TEXT DEFAULT 'created',
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                dispatch_date TIMESTAMP,
                delivery_date TIMESTAMP,
                created_by TEXT,
                total_items INTEGER DEFAULT 0,
                total_amount REAL DEFAULT 0,
                batch_details TEXT,
                pdf_path TEXT,
                notes TEXT,
                FOREIGN KEY (order_id) REFERENCES orders(order_id),
                FOREIGN KEY (warehouse_id) REFERENCES warehouses(warehouse_id)
            )
        ''')
        
        print("✅ delivery_challans table created successfully")
        
        # Create some sample data if needed
        cursor.execute("SELECT COUNT(*) FROM orders WHERE status = 'Approved'")
        approved_orders = cursor.fetchone()[0]
        
        if approved_orders > 0:
            print(f"📋 Found {approved_orders} approved orders ready for DC generation")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error creating delivery_challans table: {e}")
        return False

def add_display_challan_route():
    """Add the missing display_challan route to app.py"""
    
    route_code = '''
@app.route('/display_challan/<order_id>')
@login_required
def display_challan(order_id):
    """Display delivery challan PDF for an order"""
    try:
        db = get_db()
        
        # Get delivery challan for the order
        challan = db.execute('''
            SELECT dc.*, o.customer_name, o.customer_address, o.customer_phone
            FROM delivery_challans dc
            JOIN orders o ON dc.order_id = o.order_id
            WHERE dc.order_id = ?
        ''', (order_id,)).fetchone()
        
        if not challan:
            flash(f'No delivery challan found for order {order_id}', 'warning')
            return redirect(url_for('orders.workflow'))
        
        # If PDF exists, serve it
        if challan['pdf_path'] and os.path.exists(challan['pdf_path']):
            return send_file(challan['pdf_path'], mimetype='application/pdf')
        
        # Otherwise redirect to DC generation
        flash('PDF not found. Please regenerate the delivery challan.', 'warning')
        return redirect(url_for('dc_generation.view_dc', dc_number=challan['dc_number']))
        
    except Exception as e:
        flash(f'Error displaying challan: {str(e)}', 'error')
        return redirect(url_for('orders.workflow'))
'''
    
    return route_code

def main():
    """Main function to fix critical issues"""
    print("🔧 Fixing Critical Issues...")
    print("=" * 50)
    
    # Fix 1: Create missing delivery_challans table
    print("1. Creating missing delivery_challans table...")
    if create_delivery_challans_table():
        print("   ✅ Database table fixed")
    else:
        print("   ❌ Database table fix failed")
    
    print()
    
    # Fix 2: Show route code to add
    print("2. Missing display_challan route code:")
    print("   📝 Add this route to app.py:")
    print()
    print(add_display_challan_route())
    
    print()
    print("🎯 Next Steps:")
    print("   1. Add the display_challan route to app.py")
    print("   2. Restart the server")
    print("   3. Test the workflow page")
    
    print()
    print("✅ Critical issues analysis complete!")

if __name__ == "__main__":
    main()
