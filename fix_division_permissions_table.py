#!/usr/bin/env python3
"""
Fix Division Permissions Table
Create the missing division_permissions table and verify all division-related functionality
"""

import sqlite3
import os
from datetime import datetime

def fix_division_permissions_table():
    """Create missing division_permissions table and fix related issues"""
    
    print("🔧 FIXING DIVISION PERMISSIONS TABLE")
    print("=" * 60)
    
    try:
        # Connect to database
        db_path = 'instance/medivent.db'
        if not os.path.exists(db_path):
            print(f"❌ Database not found at {db_path}")
            return False
        
        db = sqlite3.connect(db_path)
        db.row_factory = sqlite3.Row
        cursor = db.cursor()
        
        # 1. Check if division_permissions table exists
        print("\n1️⃣ CHECKING DIVISION_PERMISSIONS TABLE:")
        print("-" * 50)
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='division_permissions'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("   ✅ division_permissions table already exists")
            
            # Check table structure
            cursor.execute("PRAGMA table_info(division_permissions)")
            columns = [col[1] for col in cursor.fetchall()]
            print(f"   📋 Columns: {', '.join(columns)}")
            
        else:
            print("   ❌ division_permissions table missing - creating now...")
            
            # Create division_permissions table
            cursor.execute('''
                CREATE TABLE division_permissions (
                    permission_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    division_id TEXT NOT NULL,
                    user_id TEXT NOT NULL,
                    permission_type TEXT NOT NULL,
                    granted_by TEXT,
                    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1,
                    FOREIGN KEY (division_id) REFERENCES divisions(division_id),
                    FOREIGN KEY (user_id) REFERENCES users(id),
                    UNIQUE(division_id, user_id, permission_type)
                )
            ''')
            
            db.commit()
            print("   ✅ division_permissions table created successfully")
        
        # 2. Verify divisions table structure
        print("\n2️⃣ VERIFYING DIVISIONS TABLE STRUCTURE:")
        print("-" * 50)
        
        cursor.execute("PRAGMA table_info(divisions)")
        divisions_columns = [col[1] for col in cursor.fetchall()]
        
        has_manager_id = 'manager_id' in divisions_columns
        has_manager = 'manager' in divisions_columns
        
        print(f"   📋 Divisions table has {len(divisions_columns)} columns")
        print(f"   ✅ Has manager_id column: {has_manager_id}")
        print(f"   ❌ Has manager column: {has_manager}")
        
        if not has_manager_id:
            print("   ⚠️ WARNING: manager_id column missing from divisions table")
            return False
        
        # 3. Test division queries
        print("\n3️⃣ TESTING DIVISION QUERIES:")
        print("-" * 50)
        
        try:
            # Test the query that was failing
            cursor.execute('''
                SELECT d.division_id, d.name, d.manager_id,
                       COUNT(p.product_id) as product_count
                FROM divisions d
                LEFT JOIN products p ON d.division_id = p.division_id
                WHERE d.is_active = 1 AND d.status = 'active'
                GROUP BY d.division_id, d.name, d.manager_id
                LIMIT 5
            ''')
            
            divisions = cursor.fetchall()
            print(f"   ✅ Division query successful - found {len(divisions)} active divisions")
            
            for division in divisions:
                div_dict = dict(division)
                div_id = div_dict.get('division_id', 'N/A')
                div_name = div_dict.get('name', 'N/A')
                manager_id = div_dict.get('manager_id', 'N/A')
                product_count = div_dict.get('product_count', 0)
                print(f"      • {div_id}: {div_name} (Manager: {manager_id}, Products: {product_count})")
                
        except Exception as e:
            print(f"   ❌ Division query failed: {str(e)}")
            return False
        
        # 4. Test division_permissions queries
        print("\n4️⃣ TESTING DIVISION_PERMISSIONS QUERIES:")
        print("-" * 50)
        
        try:
            # Test the query from routes/divisions_modern.py
            cursor.execute('''
                SELECT dp.*, u.username, u.email, u.role,
                       granter.username as granted_by_username
                FROM division_permissions dp
                LEFT JOIN users u ON dp.user_id = u.id
                LEFT JOIN users granter ON dp.granted_by = granter.id
                WHERE dp.is_active = 1
                ORDER BY dp.permission_type, u.username
                LIMIT 5
            ''')
            
            permissions = cursor.fetchall()
            print(f"   ✅ Division permissions query successful - found {len(permissions)} permissions")
            
            if len(permissions) == 0:
                print("   ℹ️ No permissions found - this is normal for a fresh installation")
            else:
                for perm in permissions:
                    perm_dict = dict(perm)
                    username = perm_dict.get('username', 'N/A')
                    perm_type = perm_dict.get('permission_type', 'N/A')
                    print(f"      • {username}: {perm_type}")
                
        except Exception as e:
            print(f"   ❌ Division permissions query failed: {str(e)}")
            return False
        
        # 5. Clean up and recreate division manager assignments
        print("\n5️⃣ FIXING DIVISION MANAGER ASSIGNMENTS:")
        print("-" * 50)
        
        # Get all divisions with invalid manager_id references
        cursor.execute('''
            SELECT d.division_id, d.name, d.manager_id
            FROM divisions d
            LEFT JOIN users u ON d.manager_id = u.id
            WHERE d.manager_id IS NOT NULL AND u.id IS NULL
        ''')
        
        invalid_managers = cursor.fetchall()
        
        if invalid_managers:
            print(f"   ⚠️ Found {len(invalid_managers)} divisions with invalid manager references")
            
            for division in invalid_managers:
                div_dict = dict(division)
                div_id = div_dict.get('division_id')
                div_name = div_dict.get('name')
                invalid_manager_id = div_dict.get('manager_id')
                
                print(f"      • {div_name} (ID: {div_id}) has invalid manager_id: {invalid_manager_id}")
                
                # Set manager_id to NULL for invalid references
                cursor.execute('''
                    UPDATE divisions 
                    SET manager_id = NULL, updated_at = datetime('now'), updated_by = 'system_fix'
                    WHERE division_id = ?
                ''', (div_id,))
                
                print(f"        ✅ Cleared invalid manager_id for {div_name}")
            
            db.commit()
            print("   ✅ All invalid manager references cleared")
        else:
            print("   ✅ All division manager references are valid")
        
        # 6. Final verification
        print("\n6️⃣ FINAL VERIFICATION:")
        print("-" * 50)
        
        # Test product details query
        cursor.execute('''
            SELECT p.*, d.name as division_name, d.manager_id as division_manager_id
            FROM products p
            LEFT JOIN divisions d ON p.division_id = d.division_id
            WHERE p.product_id = 'P001'
        ''')
        
        product = cursor.fetchone()
        if product:
            product_dict = dict(product)
            print("   ✅ Product details query successful")
            print(f"      • Product: {product_dict.get('name', 'N/A')}")
            print(f"      • Division: {product_dict.get('division_name', 'N/A')}")
            print(f"      • Manager ID: {product_dict.get('division_manager_id', 'N/A')}")
        else:
            print("   ⚠️ No product P001 found for testing")
        
        db.close()
        print("\n✅ DIVISION PERMISSIONS TABLE FIX COMPLETE")
        return True
        
    except Exception as e:
        print(f"❌ Fix failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = fix_division_permissions_table()
    
    if success:
        print("\n🎉 ALL DIVISION ISSUES FIXED!")
        print("✅ division_permissions table created/verified")
        print("✅ Division manager references cleaned up")
        print("✅ All SQL queries working correctly")
        print("\n🔄 Next steps:")
        print("   1. Test product details page")
        print("   2. Test division management page")
        print("   3. Verify all edit_product links work")
    else:
        print("\n❌ SOME ISSUES REMAIN - CHECK LOGS ABOVE")
