#!/usr/bin/env python3
"""
Quick fix to remove duplicate function definitions from routes/modern_riders.py
"""

def fix_duplicate_functions():
    """Remove duplicate function definitions"""
    
    with open('routes/modern_riders.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Find the line where error handlers start
    error_handler_line = None
    for i, line in enumerate(lines):
        if '@riders_bp.errorhandler(404)' in line:
            error_handler_line = i
            break
    
    if error_handler_line is None:
        print("❌ Could not find error handler line")
        return False
    
    # Find the line where duplicate functions start (after "# Error handlers for the blueprint")
    duplicate_start = None
    for i in range(len(lines)):
        if '# Error handlers for the blueprint' in lines[i]:
            # Look for orphaned content after this line
            for j in range(i+1, error_handler_line):
                if lines[j].strip() and not lines[j].startswith('@riders_bp.errorhandler'):
                    duplicate_start = j
                    break
            break
    
    if duplicate_start is not None:
        print(f"🔧 Removing duplicate content from line {duplicate_start+1} to {error_handler_line}")
        
        # Keep everything before the duplicates and after the error handlers
        clean_lines = lines[:duplicate_start] + lines[error_handler_line:]
        
        # Write the cleaned file
        with open('routes/modern_riders.py', 'w', encoding='utf-8') as f:
            f.writelines(clean_lines)
        
        print("✅ Successfully removed duplicate functions")
        return True
    else:
        print("✅ No duplicate functions found to remove")
        return True

if __name__ == "__main__":
    fix_duplicate_functions()
