#!/usr/bin/env python3
"""
Fix Expected Delivery Date Issues - Ensure all orders have proper delivery dates
"""

import sqlite3
import os
from datetime import datetime, timed<PERSON>ta

def fix_expected_delivery_dates():
    """Fix missing expected delivery dates in existing orders"""
    print("=" * 80)
    print("FIXING EXPECTED DELIVERY DATE ISSUES")
    print("=" * 80)
    print(f"Fix Date: {datetime.now()}")
    print()
    
    db_path = 'instance/medivent.db'
    if not os.path.exists(db_path):
        print("❌ Database not found at instance/medivent.db")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        print("1. ANALYZING DELIVERY DATE STATUS")
        print("-" * 50)
        
        # Check orders without delivery dates
        cursor.execute('''
            SELECT order_id, customer_name, order_date, status
            FROM orders 
            WHERE estimated_delivery_date IS NULL OR estimated_delivery_date = ''
            ORDER BY order_date DESC
        ''')
        orders_without_delivery = cursor.fetchall()
        
        print(f"Orders without delivery dates: {len(orders_without_delivery)}")
        
        # Check orders with delivery dates
        cursor.execute('''
            SELECT COUNT(*) as count
            FROM orders 
            WHERE estimated_delivery_date IS NOT NULL AND estimated_delivery_date != ''
        ''')
        orders_with_delivery = cursor.fetchone()['count']
        
        print(f"Orders with delivery dates: {orders_with_delivery}")
        
        print("\n2. SETTING DEFAULT DELIVERY DATES")
        print("-" * 50)
        
        # Set delivery dates for orders without them
        # Strategy: Set delivery date to 3 days after order date for pending orders
        #          Set delivery date to 1 day after order date for completed orders
        
        updated_count = 0
        
        for order in orders_without_delivery:
            order_id = order['order_id']
            order_date_str = order['order_date']
            status = order['status']
            
            try:
                # Parse order date
                if isinstance(order_date_str, str):
                    # Try different date formats
                    for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M:%S.%f', '%Y-%m-%d']:
                        try:
                            order_date = datetime.strptime(order_date_str, fmt)
                            break
                        except ValueError:
                            continue
                    else:
                        # If no format works, use current date
                        order_date = datetime.now()
                else:
                    order_date = datetime.now()
                
                # Calculate delivery date based on status
                if status in ['Delivered', 'Dispatched']:
                    # For completed orders, set delivery date to 1 day after order
                    delivery_date = order_date + timedelta(days=1)
                elif status in ['Cancelled', 'Rejected']:
                    # For cancelled orders, set delivery date to order date
                    delivery_date = order_date
                else:
                    # For pending orders, set delivery date to 3 days after order
                    delivery_date = order_date + timedelta(days=3)
                
                # Update the order
                cursor.execute('''
                    UPDATE orders 
                    SET estimated_delivery_date = ?,
                        last_updated = CURRENT_TIMESTAMP,
                        updated_by = 'system_delivery_fix'
                    WHERE order_id = ?
                ''', (delivery_date.strftime('%Y-%m-%d'), order_id))
                
                print(f"  ✅ Set delivery date for {order_id}: {delivery_date.strftime('%Y-%m-%d')}")
                updated_count += 1
                
            except Exception as e:
                print(f"  ❌ Failed to set delivery date for {order_id}: {str(e)}")
        
        print("\n3. VERIFICATION")
        print("-" * 50)
        
        # Verify the fix
        cursor.execute('''
            SELECT COUNT(*) as count
            FROM orders 
            WHERE estimated_delivery_date IS NULL OR estimated_delivery_date = ''
        ''')
        remaining_without_delivery = cursor.fetchone()['count']
        
        cursor.execute('''
            SELECT COUNT(*) as count
            FROM orders 
            WHERE estimated_delivery_date IS NOT NULL AND estimated_delivery_date != ''
        ''')
        total_with_delivery = cursor.fetchone()['count']
        
        print(f"Orders still without delivery dates: {remaining_without_delivery}")
        print(f"Total orders with delivery dates: {total_with_delivery}")
        
        # Show sample of recent orders with delivery dates
        cursor.execute('''
            SELECT order_id, customer_name, order_date, estimated_delivery_date, status
            FROM orders 
            WHERE estimated_delivery_date IS NOT NULL AND estimated_delivery_date != ''
            ORDER BY order_date DESC
            LIMIT 5
        ''')
        sample_orders = cursor.fetchall()
        
        print("\nSample orders with delivery dates:")
        for order in sample_orders:
            print(f"  {order['order_id']}: {order['estimated_delivery_date']} ({order['status']})")
        
        # Commit changes
        conn.commit()
        conn.close()
        
        print("\n4. SUMMARY")
        print("-" * 50)
        print(f"✅ Updated {updated_count} orders with delivery dates")
        print(f"✅ Orders without delivery dates: {remaining_without_delivery}")
        print(f"✅ Total orders with delivery dates: {total_with_delivery}")
        
        return remaining_without_delivery == 0
        
    except Exception as e:
        print(f"❌ Fix failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    fix_expected_delivery_dates()
