#!/usr/bin/env python3
"""
Fix invalid order IDs in the database
"""

import sqlite3
import re

def find_invalid_order_ids():
    """Find orders with invalid order ID formats"""
    print("🔍 FINDING INVALID ORDER IDs")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        # Get all orders
        cursor.execute('SELECT order_id, customer_name, order_date FROM orders ORDER BY order_date DESC')
        orders = cursor.fetchall()
        
        valid_orders = []
        invalid_orders = []
        
        # Check each order ID format
        for order_id, customer_name, order_date in orders:
            # Valid format: ORD followed by 8 digits
            if re.match(r'^ORD\d{8}$', order_id):
                valid_orders.append((order_id, customer_name, order_date))
            else:
                invalid_orders.append((order_id, customer_name, order_date))
        
        print(f"✅ Valid orders: {len(valid_orders)}")
        print(f"❌ Invalid orders: {len(invalid_orders)}")
        
        if invalid_orders:
            print("\nInvalid order IDs found:")
            for order_id, customer_name, order_date in invalid_orders:
                print(f"  {order_id}: {customer_name} ({order_date})")
        
        conn.close()
        return invalid_orders
        
    except Exception as e:
        print(f"❌ Error finding invalid orders: {e}")
        return []

def fix_invalid_order_ids(invalid_orders):
    """Fix invalid order IDs by regenerating them"""
    print("\n🔧 FIXING INVALID ORDER IDs")
    print("=" * 50)
    
    if not invalid_orders:
        print("✅ No invalid order IDs to fix")
        return True
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        # Get the highest valid order number
        cursor.execute("SELECT order_id FROM orders WHERE order_id REGEXP '^ORD[0-9]{8}$' ORDER BY order_id DESC LIMIT 1")
        latest_valid = cursor.fetchone()
        
        if latest_valid:
            latest_num = int(latest_valid[0][3:])
            print(f"Latest valid order number: {latest_num}")
        else:
            latest_num = 0
            print("No valid orders found, starting from 1")
        
        # Fix each invalid order
        for i, (old_order_id, customer_name, order_date) in enumerate(invalid_orders):
            new_order_num = latest_num + i + 1
            new_order_id = f"ORD{new_order_num:08d}"
            
            print(f"Fixing: {old_order_id} -> {new_order_id}")
            
            # Update orders table
            cursor.execute('UPDATE orders SET order_id = ? WHERE order_id = ?', (new_order_id, old_order_id))
            
            # Update order_items table
            cursor.execute('UPDATE order_items SET order_id = ? WHERE order_id = ?', (new_order_id, old_order_id))
            
            # Update any other tables that reference order_id
            tables_to_update = ['invoices', 'challans', 'activity_logs']
            for table in tables_to_update:
                try:
                    cursor.execute(f'UPDATE {table} SET order_id = ? WHERE order_id = ?', (new_order_id, old_order_id))
                except sqlite3.OperationalError:
                    # Table might not exist or have order_id column
                    pass
        
        conn.commit()
        print(f"✅ Fixed {len(invalid_orders)} invalid order IDs")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error fixing invalid orders: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_order_creation_after_fix():
    """Test order creation after fixing invalid IDs"""
    print("\n🧪 TESTING ORDER CREATION AFTER FIX")
    print("=" * 50)
    
    try:
        from app import app
        with app.app_context():
            from routes.orders import generate_order_id
            
            # Generate test order ID
            order_id = generate_order_id()
            print(f"Generated order ID: {order_id}")
            
            if order_id and re.match(r'^ORD\d{8}$', order_id):
                print("✅ Order ID generation working correctly")
                return True
            else:
                print("❌ Order ID generation still has issues")
                return False
                
    except Exception as e:
        print(f"❌ Error testing order creation: {e}")
        return False

def main():
    """Fix invalid order IDs and test"""
    print("🔧 INVALID ORDER ID FIX")
    print("=" * 80)
    
    # Find invalid orders
    invalid_orders = find_invalid_order_ids()
    
    # Fix them
    fix_success = fix_invalid_order_ids(invalid_orders)
    
    # Test order creation
    if fix_success:
        test_success = test_order_creation_after_fix()
    else:
        test_success = False
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 INVALID ORDER ID FIX RESULTS")
    print("=" * 80)
    print(f"Invalid Orders Found: {len(invalid_orders)}")
    print(f"Fix Applied: {'✅ SUCCESS' if fix_success else '❌ FAILED'}")
    print(f"Order Creation Test: {'✅ SUCCESS' if test_success else '❌ FAILED'}")
    
    if fix_success and test_success:
        print("\n🎉 ORDER ID ISSUES RESOLVED!")
        print("✅ All order IDs are now in correct format")
        print("✅ Order ID generation is working")
        print("✅ UNIQUE constraint errors should be resolved")
    else:
        print("\n❌ ISSUES REMAIN")
        print("💡 Manual database cleanup may be required")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
