#!/usr/bin/env python3
"""
Fix Inventory Conflicts Between Products and Inventory Tables
"""

import sqlite3
import os
from datetime import datetime

def fix_inventory_conflicts():
    """Fix conflicts between products table stock_quantity and inventory table"""
    print("=" * 80)
    print("FIXING INVENTORY CONFLICTS")
    print("=" * 80)
    print(f"Fix Date: {datetime.now()}")
    print()
    
    db_path = 'instance/medivent.db'
    if not os.path.exists(db_path):
        print("❌ Database not found at instance/medivent.db")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        print("1. ANALYZING INVENTORY CONFLICTS")
        print("-" * 50)
        
        # Get products with stock_quantity
        cursor.execute('''
            SELECT product_id, name, stock_quantity 
            FROM products 
            WHERE stock_quantity > 0
        ''')
        products_with_stock = cursor.fetchall()
        
        print(f"Found {len(products_with_stock)} products with stock in products table")
        
        # Get inventory totals by product
        cursor.execute('''
            SELECT product_id, 
                   SUM(stock_quantity) as total_stock, 
                   SUM(allocated_quantity) as total_allocated,
                   SUM(stock_quantity - allocated_quantity) as available_stock
            FROM inventory 
            WHERE status = 'active'
            GROUP BY product_id
        ''')
        inventory_totals = cursor.fetchall()
        
        print(f"Found {len(inventory_totals)} products with inventory records")
        
        # Create inventory lookup
        inventory_lookup = {}
        for inv in inventory_totals:
            inventory_lookup[inv['product_id']] = {
                'total_stock': inv['total_stock'],
                'available_stock': inv['available_stock'],
                'allocated': inv['total_allocated']
            }
        
        print("\n2. IDENTIFYING CONFLICTS")
        print("-" * 50)
        
        conflicts = []
        products_without_inventory = []
        
        for product in products_with_stock:
            product_id = product['product_id']
            product_stock = product['stock_quantity']
            
            if product_id in inventory_lookup:
                inv_available = inventory_lookup[product_id]['available_stock']
                if product_stock != inv_available:
                    conflicts.append({
                        'product_id': product_id,
                        'product_name': product['name'],
                        'products_stock': product_stock,
                        'inventory_available': inv_available,
                        'inventory_total': inventory_lookup[product_id]['total_stock'],
                        'inventory_allocated': inventory_lookup[product_id]['allocated']
                    })
            else:
                products_without_inventory.append({
                    'product_id': product_id,
                    'product_name': product['name'],
                    'products_stock': product_stock
                })
        
        print(f"Stock conflicts found: {len(conflicts)}")
        print(f"Products without inventory: {len(products_without_inventory)}")
        
        print("\n3. FIXING CONFLICTS")
        print("-" * 50)
        
        # Strategy: Use inventory table as source of truth and update products table
        fixed_count = 0
        
        # Fix conflicts by updating products table to match inventory
        for conflict in conflicts:
            product_id = conflict['product_id']
            correct_stock = conflict['inventory_available']
            
            cursor.execute('''
                UPDATE products 
                SET stock_quantity = ?, 
                    updated_at = CURRENT_TIMESTAMP,
                    updated_by = 'system_sync'
                WHERE product_id = ?
            ''', (correct_stock, product_id))
            
            print(f"  ✅ Fixed {product_id}: {conflict['products_stock']} → {correct_stock}")
            fixed_count += 1
        
        # Create inventory records for products without inventory
        created_count = 0
        for product in products_without_inventory:
            product_id = product['product_id']
            stock_qty = product['products_stock']
            
            # Generate inventory ID
            inventory_id = f"INV-{product_id}-SYNC-{datetime.now().strftime('%Y%m%d')}"
            batch_number = f"BATCH-{product_id}-{datetime.now().strftime('%Y%m')}"
            
            cursor.execute('''
                INSERT INTO inventory (
                    inventory_id, product_id, batch_number, stock_quantity, 
                    allocated_quantity, warehouse_id, status, date_received, 
                    received_by, last_updated, updated_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                inventory_id, product_id, batch_number, stock_qty,
                0, 'MAIN', 'active', datetime.now().date(),
                'system_sync', datetime.now(), 'system_sync'
            ))
            
            print(f"  ✅ Created inventory for {product_id}: {stock_qty} units")
            created_count += 1
        
        print("\n4. VERIFICATION")
        print("-" * 50)
        
        # Verify fixes
        cursor.execute('''
            SELECT p.product_id, p.name, p.stock_quantity,
                   COALESCE(SUM(i.stock_quantity - i.allocated_quantity), 0) as inventory_available
            FROM products p
            LEFT JOIN inventory i ON p.product_id = i.product_id AND i.status = 'active'
            WHERE p.stock_quantity > 0
            GROUP BY p.product_id, p.name, p.stock_quantity
            HAVING p.stock_quantity != COALESCE(SUM(i.stock_quantity - i.allocated_quantity), 0)
        ''')
        remaining_conflicts = cursor.fetchall()
        
        print(f"Remaining conflicts: {len(remaining_conflicts)}")
        
        if len(remaining_conflicts) == 0:
            print("✅ All inventory conflicts resolved!")
        else:
            print("❌ Some conflicts remain:")
            for conflict in remaining_conflicts[:5]:
                print(f"  - {conflict['product_id']}: Products={conflict['stock_quantity']}, Inventory={conflict['inventory_available']}")
        
        # Commit changes
        conn.commit()
        conn.close()
        
        print("\n5. SUMMARY")
        print("-" * 50)
        print(f"✅ Fixed {fixed_count} stock conflicts")
        print(f"✅ Created {created_count} inventory records")
        print(f"✅ Remaining conflicts: {len(remaining_conflicts)}")
        
        return len(remaining_conflicts) == 0
        
    except Exception as e:
        print(f"❌ Fix failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    fix_inventory_conflicts()
