#!/usr/bin/env python3
"""
IMMEDIATE FIX for inventory synchronization issue
Based on user's screenshot showing Products Overview vs Inventory Records conflicts
"""

import sqlite3
import os

def fix_inventory_sync():
    """Fix the inventory synchronization issue immediately"""
    print("🚨 URGENT INVENTORY SYNCHRONIZATION FIX")
    print("=" * 60)
    
    db_path = 'instance/medivent.db'
    if not os.path.exists(db_path):
        print("❌ Database not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        print("1. CHECKING CONFLICTS FOR COUGH SYRUP & PARACETAMOL")
        print("-" * 50)
        
        # Check specific products mentioned in screenshot
        cursor.execute('''
            SELECT p.product_id, p.name, p.stock_quantity as products_stock,
                   COALESCE(SUM(i.stock_quantity - COALESCE(i.allocated_quantity, 0)), 0) as actual_available
            FROM products p
            LEFT JOIN inventory i ON p.product_id = i.product_id AND i.status = 'active'
            WHERE p.name LIKE '%Cough%' OR p.name LIKE '%Paracetamol%'
            GROUP BY p.product_id, p.name, p.stock_quantity
        ''')
        
        priority_products = cursor.fetchall()
        
        print("BEFORE FIX:")
        for product in priority_products:
            print(f"  {product['name']}: Products Table = {product['products_stock']}, Actual Available = {product['actual_available']}")
        
        print("\n2. FIXING ALL INVENTORY CONFLICTS")
        print("-" * 50)
        
        # Update all products with correct stock from inventory
        cursor.execute('''
            UPDATE products 
            SET stock_quantity = (
                SELECT COALESCE(SUM(i.stock_quantity - COALESCE(i.allocated_quantity, 0)), 0)
                FROM inventory i 
                WHERE i.product_id = products.product_id AND i.status = 'active'
            ),
            updated_at = CURRENT_TIMESTAMP
            WHERE product_id IN (
                SELECT p.product_id
                FROM products p
                LEFT JOIN inventory i ON p.product_id = i.product_id AND i.status = 'active'
                GROUP BY p.product_id
                HAVING p.stock_quantity != COALESCE(SUM(i.stock_quantity - COALESCE(i.allocated_quantity, 0)), 0)
            )
        ''')
        
        rows_updated = cursor.rowcount
        print(f"✅ Updated {rows_updated} products")
        
        print("\n3. VERIFICATION")
        print("-" * 50)
        
        # Check the same products again
        cursor.execute('''
            SELECT p.product_id, p.name, p.stock_quantity as products_stock,
                   COALESCE(SUM(i.stock_quantity - COALESCE(i.allocated_quantity, 0)), 0) as actual_available
            FROM products p
            LEFT JOIN inventory i ON p.product_id = i.product_id AND i.status = 'active'
            WHERE p.name LIKE '%Cough%' OR p.name LIKE '%Paracetamol%'
            GROUP BY p.product_id, p.name, p.stock_quantity
        ''')
        
        after_products = cursor.fetchall()
        
        print("AFTER FIX:")
        for product in after_products:
            print(f"  {product['name']}: Products Table = {product['products_stock']}, Actual Available = {product['actual_available']}")
        
        # Check for any remaining conflicts
        cursor.execute('''
            SELECT COUNT(*) as conflict_count
            FROM products p
            LEFT JOIN inventory i ON p.product_id = i.product_id AND i.status = 'active'
            GROUP BY p.product_id
            HAVING p.stock_quantity != COALESCE(SUM(i.stock_quantity - COALESCE(i.allocated_quantity, 0)), 0)
        ''')
        
        remaining = cursor.fetchone()
        remaining_count = remaining['conflict_count'] if remaining else 0
        
        # Commit the changes
        conn.commit()
        conn.close()
        
        print(f"\n4. SUMMARY")
        print("-" * 50)
        print(f"✅ Fixed {rows_updated} inventory conflicts")
        print(f"✅ Remaining conflicts: {remaining_count}")
        
        if remaining_count == 0:
            print("🎉 ALL CONFLICTS RESOLVED!")
            print("🔄 REFRESH YOUR BROWSER TO SEE THE CHANGES!")
        else:
            print("⚠️ Some conflicts may remain - check the inventory page")
        
        return remaining_count == 0
        
    except Exception as e:
        print(f"❌ Fix failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = fix_inventory_sync()
    if success:
        print("\n🎉 INVENTORY SYNC COMPLETE!")
        print("🔄 REFRESH YOUR BROWSER NOW!")
    else:
        print("\n❌ INVENTORY SYNC FAILED")
