#!/usr/bin/env python3
"""
Fix Missing Database Tables
Creates the missing rider_bikes and rider_performance_logs tables
"""

import sqlite3
import os
from datetime import datetime

def create_missing_tables():
    """Create missing database tables"""
    db_path = 'instance/medivent.db'
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔧 Creating missing database tables...")
        print("=" * 50)
        
        # Create rider_bikes table
        print("📋 Creating rider_bikes table...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS rider_bikes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                bike_id TEXT UNIQUE NOT NULL,
                rider_id TEXT NOT NULL,
                make TEXT NOT NULL,
                model TEXT NOT NULL,
                year INTEGER NOT NULL,
                color TEXT NOT NULL,
                license_plate TEXT UNIQUE NOT NULL,
                engine_number TEXT,
                chassis_number TEXT,
                registration_expiry DATE NOT NULL,
                insurance_expiry DATE NOT NULL,
                is_primary BOOLEAN DEFAULT 0,
                status TEXT DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (rider_id) REFERENCES riders (rider_id)
            )
        ''')
        print("✅ rider_bikes table created successfully")
        
        # Create rider_performance_logs table
        print("📋 Creating rider_performance_logs table...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS rider_performance_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                log_id TEXT UNIQUE NOT NULL,
                rider_id TEXT NOT NULL,
                date DATE NOT NULL,
                deliveries_completed INTEGER DEFAULT 0,
                deliveries_attempted INTEGER DEFAULT 0,
                on_time_deliveries INTEGER DEFAULT 0,
                late_deliveries INTEGER DEFAULT 0,
                failed_deliveries INTEGER DEFAULT 0,
                customer_ratings_avg REAL DEFAULT 0.0,
                customer_ratings_count INTEGER DEFAULT 0,
                distance_traveled_km REAL DEFAULT 0.0,
                fuel_consumed_liters REAL DEFAULT 0.0,
                average_delivery_time_minutes REAL DEFAULT 0.0,
                earnings REAL DEFAULT 0.0,
                bonus_earned REAL DEFAULT 0.0,
                penalties REAL DEFAULT 0.0,
                working_hours REAL DEFAULT 0.0,
                break_time_minutes INTEGER DEFAULT 0,
                overtime_hours REAL DEFAULT 0.0,
                performance_score REAL DEFAULT 0.0,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (rider_id) REFERENCES riders (rider_id)
            )
        ''')
        print("✅ rider_performance_logs table created successfully")
        
        # Create bike_documents table (referenced in the schema)
        print("📋 Creating bike_documents table...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS bike_documents (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                document_id TEXT UNIQUE NOT NULL,
                bike_id TEXT NOT NULL,
                document_type TEXT NOT NULL,
                document_name TEXT NOT NULL,
                file_path TEXT NOT NULL,
                file_size INTEGER,
                mime_type TEXT,
                is_verified BOOLEAN DEFAULT 0,
                verified_by TEXT,
                verified_at TIMESTAMP,
                expiry_date DATE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (bike_id) REFERENCES rider_bikes (bike_id)
            )
        ''')
        print("✅ bike_documents table created successfully")
        
        # Verify tables were created
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name IN ('rider_bikes', 'rider_performance_logs', 'bike_documents');")
        created_tables = cursor.fetchall()
        
        print(f"\n✅ Successfully created {len(created_tables)} tables:")
        for table in created_tables:
            print(f"   - {table[0]}")
        
        # Add some sample data to prevent empty table errors
        print("\n📊 Adding sample data...")
        
        # Check if riders table has data
        cursor.execute("SELECT COUNT(*) FROM riders;")
        rider_count = cursor.fetchone()[0]
        
        if rider_count > 0:
            # Get a sample rider
            cursor.execute("SELECT rider_id FROM riders LIMIT 1;")
            sample_rider = cursor.fetchone()
            
            if sample_rider:
                rider_id = sample_rider[0]
                
                # Add sample bike
                cursor.execute('''
                    INSERT OR IGNORE INTO rider_bikes (
                        bike_id, rider_id, make, model, year, color, license_plate,
                        registration_expiry, insurance_expiry, is_primary, status
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    f'BIKE_{rider_id}_001', rider_id, 'Honda', 'CD 70', 2022, 'Red',
                    f'KHI-{rider_id[-3:]}-001', '2025-12-31', '2025-12-31', 1, 'active'
                ))
                
                # Add sample performance log
                cursor.execute('''
                    INSERT OR IGNORE INTO rider_performance_logs (
                        log_id, rider_id, date, deliveries_completed, deliveries_attempted,
                        on_time_deliveries, customer_ratings_avg, performance_score
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    f'LOG_{rider_id}_{datetime.now().strftime("%Y%m%d")}', rider_id,
                    datetime.now().date(), 5, 5, 4, 4.2, 85.0
                ))
                
                print(f"   ✅ Added sample data for rider: {rider_id}")
        
        conn.commit()
        conn.close()
        
        print(f"\n🎉 Database tables created successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error creating tables: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

if __name__ == "__main__":
    success = create_missing_tables()
    if success:
        print("\n✅ All missing tables have been created!")
        print("🚀 You can now test the riders functionality.")
    else:
        print("\n❌ Failed to create tables. Please check the error messages above.")
