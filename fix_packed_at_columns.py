#!/usr/bin/env python3
"""
Database migration script to add missing packed_at, packed_by, and packing_notes columns
to the orders table in the Medivent ERP system.
"""

import sqlite3
import os
from datetime import datetime

def backup_database():
    """Create a backup of the database before making changes"""
    try:
        db_path = os.path.join('instance', 'medivent.db')
        backup_path = os.path.join('instance', f'medivent_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db')
        
        if os.path.exists(db_path):
            # Copy database file
            import shutil
            shutil.copy2(db_path, backup_path)
            print(f"✅ Database backed up to: {backup_path}")
            return backup_path
        else:
            print("❌ Database file not found")
            return None
    except Exception as e:
        print(f"❌ Backup failed: {e}")
        return None

def check_columns_exist(cursor):
    """Check if the columns already exist"""
    cursor.execute("PRAGMA table_info(orders)")
    columns = cursor.fetchall()
    column_names = [col[1] for col in columns]
    
    packed_at_exists = 'packed_at' in column_names
    packed_by_exists = 'packed_by' in column_names
    packing_notes_exists = 'packing_notes' in column_names
    
    return packed_at_exists, packed_by_exists, packing_notes_exists

def add_missing_columns():
    """Add the missing columns to the orders table"""
    try:
        print("=== MEDIVENT ERP DATABASE MIGRATION ===")
        print("Adding missing packing columns to orders table")
        print()
        
        # Backup database first
        backup_path = backup_database()
        if not backup_path:
            print("❌ Cannot proceed without backup")
            return False
        
        # Connect to database
        db_path = os.path.join('instance', 'medivent.db')
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 Checking current table structure...")
        packed_at_exists, packed_by_exists, packing_notes_exists = check_columns_exist(cursor)
        
        print(f"   packed_at column: {'✅ EXISTS' if packed_at_exists else '❌ MISSING'}")
        print(f"   packed_by column: {'✅ EXISTS' if packed_by_exists else '❌ MISSING'}")
        print(f"   packing_notes column: {'✅ EXISTS' if packing_notes_exists else '❌ MISSING'}")
        
        # Add missing columns
        changes_made = False
        
        if not packed_at_exists:
            print("\n🔧 Adding packed_at column...")
            cursor.execute("ALTER TABLE orders ADD COLUMN packed_at TIMESTAMP")
            print("   ✅ packed_at column added")
            changes_made = True
        
        if not packed_by_exists:
            print("\n🔧 Adding packed_by column...")
            cursor.execute("ALTER TABLE orders ADD COLUMN packed_by TEXT")
            print("   ✅ packed_by column added")
            changes_made = True
        
        if not packing_notes_exists:
            print("\n🔧 Adding packing_notes column...")
            cursor.execute("ALTER TABLE orders ADD COLUMN packing_notes TEXT")
            print("   ✅ packing_notes column added")
            changes_made = True
        
        if changes_made:
            # Commit changes
            conn.commit()
            print("\n💾 Changes committed to database")
            
            # Verify the changes
            print("\n🔍 Verifying changes...")
            packed_at_exists, packed_by_exists, packing_notes_exists = check_columns_exist(cursor)
            
            if packed_at_exists and packed_by_exists and packing_notes_exists:
                print("   ✅ All columns successfully added!")
            else:
                print("   ❌ Some columns may not have been added correctly")
                return False
        else:
            print("\n✅ All columns already exist - no changes needed")
        
        # Show updated table structure
        print("\n📋 UPDATED ORDERS TABLE STRUCTURE:")
        cursor.execute("PRAGMA table_info(orders)")
        columns = cursor.fetchall()
        for col in columns:
            if col[1] in ['packed_at', 'packed_by', 'packing_notes']:
                print(f"   🆕 {col[1]} - {col[2]}")
            else:
                print(f"      {col[1]} - {col[2]}")
        
        # Test a simple query to ensure everything works
        print("\n🧪 Testing query with new columns...")
        cursor.execute("""
            SELECT order_id, customer_name, warehouse_status, packed_at, packed_by, packing_notes
            FROM orders 
            WHERE warehouse_status = 'packed'
            LIMIT 3
        """)
        test_results = cursor.fetchall()
        print(f"   ✅ Query successful - found {len(test_results)} packed orders")
        
        conn.close()
        
        print("\n" + "="*50)
        print("🎉 DATABASE MIGRATION COMPLETED SUCCESSFULLY!")
        print("="*50)
        print(f"📁 Backup saved at: {backup_path}")
        print("🚀 The application should now work without packed_at errors")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Migration failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = add_missing_columns()
    if success:
        print("\n✅ Migration completed successfully!")
        print("You can now restart the application.")
    else:
        print("\n❌ Migration failed!")
        print("Please check the error messages above.")
