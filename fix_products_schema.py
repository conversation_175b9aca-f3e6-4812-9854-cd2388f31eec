#!/usr/bin/env python3
"""
Fix products table schema and add missing columns
"""

import sqlite3
from datetime import datetime

def fix_products_schema():
    """Fix products table schema and add missing columns"""
    
    print("🔧 FIXING PRODUCTS TABLE SCHEMA")
    print("=" * 50)
    
    conn = sqlite3.connect('medivent_erp.db')
    cursor = conn.cursor()
    
    # Check current products table structure
    cursor.execute('PRAGMA table_info(products)')
    columns = cursor.fetchall()
    column_names = [col[1] for col in columns]
    
    print("Current products table columns:")
    for col in columns:
        print(f"  ✅ {col[1]} ({col[2]})")
    
    # Check for missing columns
    missing_columns = []
    required_columns = {
        'status': 'TEXT DEFAULT "active"',
        'is_active': 'INTEGER DEFAULT 1'
    }
    
    for col_name, col_def in required_columns.items():
        if col_name not in column_names:
            missing_columns.append((col_name, col_def))
    
    if missing_columns:
        print(f"\n❌ Missing columns: {[col[0] for col in missing_columns]}")
        
        # Add missing columns
        for col_name, col_def in missing_columns:
            try:
                cursor.execute(f'ALTER TABLE products ADD COLUMN {col_name} {col_def}')
                print(f"  ✅ Added column: {col_name}")
            except Exception as e:
                print(f"  ❌ Error adding {col_name}: {e}")
    else:
        print("\n✅ All required columns exist")
    
    # Check if we have any products
    cursor.execute('SELECT COUNT(*) FROM products')
    count = cursor.fetchone()[0]
    print(f"\nProducts in database: {count}")
    
    if count == 0:
        print("⚠️  No products found - adding sample data for testing")
        
        # Add sample products for testing
        sample_products = [
            ('P001', 'Paracetamol', '500mg', 'ABC Pharma', 'Tablets', 'Strip', 25.50),
            ('P002', 'Amoxicillin', '250mg', 'XYZ Pharma', 'Capsules', 'Strip', 45.00),
            ('P003', 'Cough Syrup', '100ml', 'DEF Pharma', 'Syrup', 'Bottle', 85.75),
            ('P004', 'Aspirin', '100mg', 'GHI Pharma', 'Tablets', 'Strip', 15.25),
            ('P005', 'Vitamin C', '1000mg', 'JKL Pharma', 'Tablets', 'Bottle', 120.00)
        ]
        
        for product in sample_products:
            try:
                cursor.execute("""
                    INSERT INTO products (product_id, name, strength, manufacturer, category, unit_of_measure, unit_price, status, is_active, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, 'active', 1, ?)
                """, (*product, datetime.now()))
                print(f"  ✅ Added sample product: {product[1]}")
            except Exception as e:
                print(f"  ❌ Error adding {product[1]}: {e}")
    
    conn.commit()
    
    # Verify final state
    cursor.execute('PRAGMA table_info(products)')
    final_columns = cursor.fetchall()
    
    cursor.execute('SELECT COUNT(*) FROM products')
    final_count = cursor.fetchone()[0]
    
    print(f"\n📊 FINAL STATE:")
    print(f"  Columns: {len(final_columns)}")
    print(f"  Products: {final_count}")
    
    if final_count > 0:
        cursor.execute('SELECT id, product_id, name, status, is_active FROM products LIMIT 5')
        products = cursor.fetchall()
        print("\nSample products:")
        for p in products:
            print(f"  ID: {p[0]}, Product_ID: {p[1]}, Name: {p[2]}, Status: {p[3]}, Is_Active: {p[4]}")
    
    conn.close()
    return True

if __name__ == "__main__":
    fix_products_schema()
