#!/usr/bin/env python3
"""
Fix stock_movements table schema by adding missing columns
"""

import sqlite3
import sys
from datetime import datetime

def fix_stock_movements_schema():
    """Fix the stock_movements table schema"""
    try:
        print("🔧 FIXING STOCK_MOVEMENTS TABLE SCHEMA")
        print("=" * 60)
        
        db = sqlite3.connect('instance/medivent.db')
        db.row_factory = sqlite3.Row
        cursor = db.cursor()
        
        # 1. Check current schema
        print("1️⃣ Checking current schema...")
        cursor.execute("PRAGMA table_info(stock_movements)")
        columns = [col['name'] for col in cursor.fetchall()]
        print(f"Current columns: {', '.join(columns)}")
        
        # 2. Add missing columns
        missing_columns = []
        
        if 'from_warehouse_id' not in columns:
            missing_columns.append('from_warehouse_id')
            print("2️⃣ Adding from_warehouse_id column...")
            cursor.execute("ALTER TABLE stock_movements ADD COLUMN from_warehouse_id TEXT")
            print("✅ Added from_warehouse_id column")
        
        if 'to_warehouse_id' not in columns:
            missing_columns.append('to_warehouse_id')
            print("3️⃣ Adding to_warehouse_id column...")
            cursor.execute("ALTER TABLE stock_movements ADD COLUMN to_warehouse_id TEXT")
            print("✅ Added to_warehouse_id column")
        
        if 'movement_date' not in columns:
            missing_columns.append('movement_date')
            print("4️⃣ Adding movement_date column...")
            cursor.execute("ALTER TABLE stock_movements ADD COLUMN movement_date TIMESTAMP")
            print("✅ Added movement_date column")
        
        if 'moved_by' not in columns:
            missing_columns.append('moved_by')
            print("5️⃣ Adding moved_by column...")
            cursor.execute("ALTER TABLE stock_movements ADD COLUMN moved_by TEXT")
            print("✅ Added moved_by column")
        
        # 3. Update existing records with default values
        if missing_columns:
            print("6️⃣ Updating existing records...")
            
            # Set movement_date to created_at for existing records
            if 'movement_date' in missing_columns:
                cursor.execute("""
                    UPDATE stock_movements 
                    SET movement_date = created_at 
                    WHERE movement_date IS NULL
                """)
                print("✅ Updated movement_date for existing records")
            
            # Set default warehouse for existing records
            if 'from_warehouse_id' in missing_columns or 'to_warehouse_id' in missing_columns:
                cursor.execute("""
                    UPDATE stock_movements 
                    SET from_warehouse_id = 'MAIN', to_warehouse_id = 'MAIN'
                    WHERE from_warehouse_id IS NULL OR to_warehouse_id IS NULL
                """)
                print("✅ Updated warehouse IDs for existing records")
        
        # 4. Verify the fix
        print("7️⃣ Verifying the fix...")
        cursor.execute("PRAGMA table_info(stock_movements)")
        new_columns = [col['name'] for col in cursor.fetchall()]
        print(f"Updated columns: {', '.join(new_columns)}")
        
        # 5. Test the problematic query
        print("8️⃣ Testing inventory history query...")
        try:
            cursor.execute('''
                SELECT m.*,
                       w1.name as from_warehouse_name,
                       w2.name as to_warehouse_name
                FROM stock_movements m
                LEFT JOIN warehouses w1 ON m.from_warehouse_id = w1.warehouse_id
                LEFT JOIN warehouses w2 ON m.to_warehouse_id = w2.warehouse_id
                WHERE m.inventory_id = ?
                ORDER BY m.movement_date DESC
                LIMIT 1
            ''', ('test',))
            
            print("✅ Inventory history query now works!")
            
        except Exception as e:
            print(f"❌ Query still fails: {str(e)}")
            return False
        
        # Commit changes
        db.commit()
        db.close()
        
        print("\n" + "=" * 60)
        print("✅ STOCK_MOVEMENTS SCHEMA FIXED SUCCESSFULLY")
        print("=" * 60)
        
        if missing_columns:
            print(f"Added columns: {', '.join(missing_columns)}")
        else:
            print("No missing columns found - schema was already correct")
        
        return True
        
    except Exception as e:
        print(f"❌ Schema fix failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 STOCK MOVEMENTS SCHEMA FIX")
    print("=" * 70)
    
    success = fix_stock_movements_schema()
    
    if success:
        print("✅ Schema fix completed successfully")
    else:
        print("❌ Schema fix failed")
    
    print("=" * 70)
