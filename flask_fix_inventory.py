#!/usr/bin/env python3
"""
Flask-based inventory synchronization fix
"""

from app import app, db
from models import Product, Inventory
from sqlalchemy import func

def fix_inventory_conflicts():
    """Fix inventory conflicts using Flask ORM"""
    with app.app_context():
        print("🚨 URGENT INVENTORY SYNCHRONIZATION FIX")
        print("=" * 60)
        
        # Get all products with their current stock
        products = Product.query.all()
        print(f"Found {len(products)} products")
        
        conflicts_fixed = 0
        
        for product in products:
            # Calculate actual available stock from inventory
            actual_stock = db.session.query(
                func.coalesce(
                    func.sum(
                        Inventory.stock_quantity - func.coalesce(Inventory.allocated_quantity, 0)
                    ), 0
                )
            ).filter(
                Inventory.product_id == product.product_id,
                Inventory.status == 'active'
            ).scalar()
            
            # Check if there's a conflict
            if product.stock_quantity != actual_stock:
                print(f"🔧 Fixing {product.name}: {product.stock_quantity} → {actual_stock}")
                product.stock_quantity = actual_stock
                conflicts_fixed += 1
        
        # Commit all changes
        db.session.commit()
        
        print(f"\n✅ Fixed {conflicts_fixed} inventory conflicts")
        print("🔄 REFRESH YOUR BROWSER TO SEE THE CHANGES!")
        
        return conflicts_fixed

if __name__ == "__main__":
    try:
        fixed = fix_inventory_conflicts()
        print(f"\n🎉 SUCCESS: Fixed {fixed} conflicts!")
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        import traceback
        traceback.print_exc()
