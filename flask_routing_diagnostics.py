#!/usr/bin/env python3
"""
Flask Routing Diagnostics
Comprehensive diagnosis and fix for partial_pending blueprint routing issues
"""

import sys
import os
import traceback
import importlib.util

def test_blueprint_import():
    """Test if the partial_pending blueprint can be imported"""
    print("🔍 1. TESTING BLUEPRINT IMPORT")
    print("-" * 50)
    
    try:
        # Test import
        from routes.partial_pending import partial_pending_bp
        
        print("✅ Blueprint imported successfully")
        print(f"   Name: {partial_pending_bp.name}")
        print(f"   URL Prefix: {partial_pending_bp.url_prefix}")
        
        # Check if routes are registered
        route_count = 0
        for rule in partial_pending_bp.url_map.iter_rules():
            route_count += 1
            print(f"   Route: {rule.rule} -> {rule.endpoint}")
        
        print(f"   Total routes: {route_count}")
        
        return True, partial_pending_bp
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("   Checking if routes directory exists...")
        
        if os.path.exists('routes'):
            print("   ✅ routes/ directory exists")
            if os.path.exists('routes/partial_pending.py'):
                print("   ✅ partial_pending.py file exists")
                print("   ❌ Import still failing - checking file content...")
                
                # Check file encoding
                try:
                    with open('routes/partial_pending.py', 'r', encoding='utf-8') as f:
                        content = f.read()
                    print(f"   ✅ File readable, {len(content)} characters")
                except UnicodeDecodeError as ue:
                    print(f"   ❌ File encoding error: {ue}")
                    return False, None
                except Exception as fe:
                    print(f"   ❌ File read error: {fe}")
                    return False, None
            else:
                print("   ❌ partial_pending.py file missing")
        else:
            print("   ❌ routes/ directory missing")
        
        traceback.print_exc()
        return False, None
        
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")
        traceback.print_exc()
        return False, None

def test_flask_app_creation():
    """Test Flask app creation and blueprint registration"""
    print("\n🔍 2. TESTING FLASK APP CREATION")
    print("-" * 50)
    
    try:
        from flask import Flask
        from flask_login import LoginManager
        
        # Create minimal test app
        test_app = Flask(__name__)
        test_app.config['SECRET_KEY'] = 'test-key-for-diagnostics'
        
        # Initialize login manager
        login_manager = LoginManager()
        login_manager.init_app(test_app)
        login_manager.login_view = 'login'
        
        print("✅ Test Flask app created")
        
        # Try to import and register blueprint
        from routes.partial_pending import partial_pending_bp
        test_app.register_blueprint(partial_pending_bp)
        
        print("✅ Blueprint registered successfully")
        
        # Check registered routes
        partial_routes = []
        for rule in test_app.url_map.iter_rules():
            if 'partial_pending' in rule.endpoint:
                partial_routes.append(f"{rule.rule} -> {rule.endpoint}")
        
        print(f"   Registered partial_pending routes: {len(partial_routes)}")
        for route in partial_routes:
            print(f"     {route}")
        
        return True, test_app
        
    except Exception as e:
        print(f"❌ Flask app creation failed: {e}")
        traceback.print_exc()
        return False, None

def test_database_connection():
    """Test database connection for blueprint"""
    print("\n🔍 3. TESTING DATABASE CONNECTION")
    print("-" * 50)
    
    try:
        from database import get_db
        
        db = get_db()
        print("✅ Database connection successful")
        
        # Test if required tables exist
        required_tables = [
            'partial_dc_tracking',
            'inventory_notifications',
            'orders',
            'products'
        ]
        
        for table in required_tables:
            cursor = db.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            exists = cursor.fetchone() is not None
            print(f"   {'✅' if exists else '❌'} Table {table}: {'EXISTS' if exists else 'MISSING'}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        traceback.print_exc()
        return False

def check_app_py_registration():
    """Check if blueprint is properly registered in app.py"""
    print("\n🔍 4. CHECKING APP.PY REGISTRATION")
    print("-" * 50)
    
    try:
        # Read app.py with proper encoding
        encodings_to_try = ['utf-8', 'latin-1', 'cp1252']
        app_content = None
        
        for encoding in encodings_to_try:
            try:
                with open('app.py', 'r', encoding=encoding) as f:
                    app_content = f.read()
                print(f"✅ app.py read successfully with {encoding} encoding")
                break
            except UnicodeDecodeError:
                continue
        
        if app_content is None:
            print("❌ Could not read app.py with any encoding")
            return False
        
        # Check for blueprint registration
        if 'partial_pending_bp' in app_content:
            print("✅ partial_pending_bp found in app.py")
            
            # Find the registration lines
            lines = app_content.split('\n')
            for i, line in enumerate(lines):
                if 'partial_pending' in line and ('import' in line or 'register_blueprint' in line):
                    print(f"   Line {i+1}: {line.strip()}")
            
            return True
        else:
            print("❌ partial_pending_bp not found in app.py")
            return False
            
    except Exception as e:
        print(f"❌ Error checking app.py: {e}")
        traceback.print_exc()
        return False

def create_minimal_test_blueprint():
    """Create a minimal test blueprint to verify basic functionality"""
    print("\n🔧 5. CREATING MINIMAL TEST BLUEPRINT")
    print("-" * 50)
    
    minimal_blueprint = '''#!/usr/bin/env python3
"""
Minimal Test Blueprint for Partial Pending
"""

from flask import Blueprint

# Create minimal blueprint
partial_pending_test_bp = Blueprint('partial_pending_test', __name__, url_prefix='/partial-pending-test')

@partial_pending_test_bp.route('/')
def test_index():
    """Minimal test route"""
    return '''
    <h1>🎉 Partial Pending Test Blueprint Working!</h1>
    <p>This confirms that blueprint registration is working correctly.</p>
    <p>Port: 5001</p>
    <p>URL: /partial-pending-test/</p>
    <a href="/">Back to Dashboard</a>
    '''

@partial_pending_test_bp.route('/status')
def test_status():
    """Test status route"""
    return {
        "status": "working",
        "blueprint": "partial_pending_test",
        "port": 5001,
        "message": "Blueprint registration successful"
    }
'''
    
    try:
        with open('routes/partial_pending_test.py', 'w', encoding='utf-8') as f:
            f.write(minimal_blueprint)
        
        print("✅ Minimal test blueprint created: routes/partial_pending_test.py")
        print("   You can test this by:")
        print("   1. Adding this to app.py:")
        print("      from routes.partial_pending_test import partial_pending_test_bp")
        print("      app.register_blueprint(partial_pending_test_bp)")
        print("   2. Accessing: http://127.0.0.1:5001/partial-pending-test/")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating test blueprint: {e}")
        return False

def fix_blueprint_registration():
    """Fix blueprint registration issues"""
    print("\n🔧 6. FIXING BLUEPRINT REGISTRATION")
    print("-" * 50)
    
    try:
        # Read current app.py
        encodings_to_try = ['utf-8', 'latin-1', 'cp1252']
        app_content = None
        
        for encoding in encodings_to_try:
            try:
                with open('app.py', 'r', encoding=encoding) as f:
                    app_content = f.read()
                break
            except UnicodeDecodeError:
                continue
        
        if app_content is None:
            print("❌ Could not read app.py")
            return False
        
        # Check if blueprint registration exists
        if 'partial_pending_bp' in app_content and 'register_blueprint(partial_pending_bp)' in app_content:
            print("✅ Blueprint registration already exists in app.py")
            print("   The issue might be with import or blueprint definition")
            
            # Check if there are any syntax errors around the registration
            lines = app_content.split('\n')
            for i, line in enumerate(lines):
                if 'partial_pending' in line:
                    # Show context around the line
                    start = max(0, i-2)
                    end = min(len(lines), i+3)
                    print(f"\n   Context around line {i+1}:")
                    for j in range(start, end):
                        marker = ">>>" if j == i else "   "
                        print(f"   {marker} {j+1}: {lines[j]}")
            
            return True
        else:
            print("❌ Blueprint registration missing or incomplete")
            print("   Need to add blueprint registration to app.py")
            return False
            
    except Exception as e:
        print(f"❌ Error fixing registration: {e}")
        traceback.print_exc()
        return False

def main():
    """Run comprehensive Flask routing diagnostics"""
    print("🔧 FLASK ROUTING DIAGNOSTICS - PORT 5001")
    print("=" * 60)
    print("Diagnosing partial_pending blueprint routing issues...")
    
    # Run all diagnostic tests
    results = {}
    
    # Test 1: Blueprint Import
    results['import'], blueprint = test_blueprint_import()
    
    # Test 2: Flask App Creation
    results['flask_app'], test_app = test_flask_app_creation()
    
    # Test 3: Database Connection
    results['database'] = test_database_connection()
    
    # Test 4: App.py Registration
    results['app_registration'] = check_app_py_registration()
    
    # Test 5: Create Test Blueprint
    results['test_blueprint'] = create_minimal_test_blueprint()
    
    # Test 6: Fix Registration
    results['fix_registration'] = fix_blueprint_registration()
    
    # Summary
    print("\n" + "=" * 60)
    print("🎯 DIAGNOSTIC SUMMARY")
    print("=" * 60)
    
    for test, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test.replace('_', ' ').title()}")
    
    passed_tests = sum(1 for result in results.values() if result)
    total_tests = len(results)
    success_rate = (passed_tests / total_tests) * 100
    
    print(f"\nSuccess Rate: {success_rate:.1f}% ({passed_tests}/{total_tests})")
    
    # Recommendations
    print("\n🔧 RECOMMENDATIONS:")
    
    if results['import'] and results['flask_app']:
        print("✅ Blueprint is working correctly")
        print("💡 Try restarting the Flask app on port 5001")
        print("🌐 Access: http://127.0.0.1:5001/partial-pending/")
    elif not results['import']:
        print("❌ Blueprint import failing")
        print("💡 Check routes/partial_pending.py for syntax errors")
        print("💡 Try the test blueprint: http://127.0.0.1:5001/partial-pending-test/")
    elif not results['app_registration']:
        print("❌ Blueprint not registered in app.py")
        print("💡 Add blueprint registration to app.py")
    else:
        print("⚠️  Mixed results - check individual test outputs above")
    
    print("\n🚀 Next Steps:")
    print("1. Restart Flask app: python app.py")
    print("2. Test URL: http://127.0.0.1:5001/partial-pending/")
    print("3. Check test blueprint: http://127.0.0.1:5001/partial-pending-test/")

if __name__ == "__main__":
    main()
