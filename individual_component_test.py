#!/usr/bin/env python3
"""
Individual Component Testing
Test each component separately to isolate issues
"""

import requests
import json
import sqlite3
import os
from datetime import datetime

def test_database_component():
    """Test database component individually"""
    print("🔍 TESTING DATABASE COMPONENT")
    print("=" * 50)
    
    try:
        if not os.path.exists('instance/medivent.db'):
            print("❌ Database file not found")
            return False
            
        conn = sqlite3.connect('instance/medivent.db')
        conn.row_factory = sqlite3.Row
        
        # Test order existence
        order = conn.execute('SELECT * FROM orders WHERE order_id = ?', ('ORD00000155',)).fetchone()
        
        if order:
            print(f"✅ Order ORD00000155 exists")
            print(f"   Customer: {order['customer_name']}")
            print(f"   Status: {order['status']}")
            print(f"   Amount: Rs.{order['order_amount']}")
        else:
            print("❌ Order ORD00000155 not found")
            return False
        
        # Test order items
        items = conn.execute('SELECT * FROM order_items WHERE order_id = ?', ('ORD00000155',)).fetchall()
        print(f"✅ Order items: {len(items)} items found")
        
        # Test API query simulation
        api_items = conn.execute('''
            SELECT oi.*, p.name as product_name 
            FROM order_items oi 
            LEFT JOIN products p ON oi.product_id = p.product_id 
            WHERE oi.order_id = ?
        ''', ('ORD00000155',)).fetchall()
        
        print(f"✅ API query simulation: {len(api_items)} items")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database test error: {e}")
        return False

def test_api_component():
    """Test API component individually"""
    print("\n🔍 TESTING API COMPONENT")
    print("=" * 50)
    
    # Test API endpoints without authentication
    endpoints = [
        ('/api/order-details/ORD00000155', 'Order Details API'),
        ('/api/order-qr-code/ORD00000155', 'QR Code API')
    ]
    
    results = {}
    
    for endpoint, description in endpoints:
        try:
            url = f'http://127.0.0.1:5001{endpoint}'
            print(f"\n📡 Testing: {description}")
            
            response = requests.get(url, timeout=10)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    success = data.get('success', False)
                    print(f"   Success: {success}")
                    
                    if success:
                        print(f"   ✅ {description} working correctly")
                        if 'order' in data:
                            print(f"   📊 Order ID: {data['order'].get('order_id', 'N/A')}")
                        if 'qr_code_base64' in data:
                            print(f"   📊 QR Code: {len(data['qr_code_base64'])} characters")
                        results[endpoint] = 'PASS'
                    else:
                        print(f"   ❌ {description} API error: {data.get('message', 'Unknown')}")
                        results[endpoint] = 'FAIL'
                        
                except json.JSONDecodeError:
                    print(f"   ❌ {description} invalid JSON response")
                    results[endpoint] = 'FAIL'
            else:
                print(f"   ❌ {description} HTTP error: {response.status_code}")
                results[endpoint] = 'FAIL'
                
        except Exception as e:
            print(f"   ❌ {description} error: {e}")
            results[endpoint] = 'ERROR'
    
    return results

def test_routes_component():
    """Test routes component individually"""
    print("\n🔍 TESTING ROUTES COMPONENT")
    print("=" * 50)
    
    # Test routes that don't require authentication
    routes = [
        ('/orders/ORD00000155/details', 'Order Details JSON Route'),
        ('/orders/ORD00000155/print-address', 'Print Address Route')
    ]
    
    results = {}
    
    for route, description in routes:
        try:
            url = f'http://127.0.0.1:5001{route}'
            print(f"\n📡 Testing: {description}")
            
            response = requests.get(url, timeout=10)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '')
                
                if 'application/json' in content_type:
                    try:
                        data = response.json()
                        success = data.get('success', False)
                        print(f"   JSON Success: {success}")
                        
                        if success:
                            print(f"   ✅ {description} working correctly")
                            results[route] = 'PASS'
                        else:
                            print(f"   ❌ {description} error: {data.get('error', 'Unknown')}")
                            results[route] = 'FAIL'
                    except json.JSONDecodeError:
                        print(f"   ❌ {description} invalid JSON")
                        results[route] = 'FAIL'
                        
                elif 'text/html' in content_type:
                    print(f"   ✅ {description} HTML response: {len(response.text)} chars")
                    results[route] = 'PASS'
                else:
                    print(f"   ✅ {description} response: {content_type}")
                    results[route] = 'PASS'
            else:
                print(f"   ❌ {description} HTTP error: {response.status_code}")
                results[route] = 'FAIL'
                
        except Exception as e:
            print(f"   ❌ {description} error: {e}")
            results[route] = 'ERROR'
    
    return results

def test_static_files_component():
    """Test static files component"""
    print("\n🔍 TESTING STATIC FILES COMPONENT")
    print("=" * 50)
    
    static_files = [
        ('/static/js/enhanced_modal.js', 'Enhanced Modal JavaScript'),
        ('/static/css/enhanced_modal.css', 'Enhanced Modal CSS')
    ]
    
    results = {}
    
    for file_path, description in static_files:
        try:
            url = f'http://127.0.0.1:5001{file_path}'
            print(f"\n📡 Testing: {description}")
            
            response = requests.get(url, timeout=5)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                content = response.text
                print(f"   ✅ {description}: {len(content)} characters")
                
                # Check for key content
                if 'enhanced_modal.js' in file_path:
                    if 'showEnhancedOrderDetails' in content:
                        print(f"   ✅ showEnhancedOrderDetails function found")
                    else:
                        print(f"   ❌ showEnhancedOrderDetails function missing")
                        
                    if 'EnhancedOrderModal' in content:
                        print(f"   ✅ EnhancedOrderModal class found")
                    else:
                        print(f"   ❌ EnhancedOrderModal class missing")
                
                results[file_path] = 'PASS'
            else:
                print(f"   ❌ {description} not accessible: HTTP {response.status_code}")
                results[file_path] = 'FAIL'
                
        except Exception as e:
            print(f"   ❌ {description} error: {e}")
            results[file_path] = 'ERROR'
    
    return results

def test_template_files_component():
    """Test template files exist"""
    print("\n🔍 TESTING TEMPLATE FILES COMPONENT")
    print("=" * 50)
    
    template_files = [
        'templates/warehouse/packing_dashboard.html',
        'templates/components/enhanced_order_modal.html',
        'templates/warehouse/address_label.html'
    ]
    
    results = {}
    
    for template_file in template_files:
        try:
            print(f"\n📄 Testing: {template_file}")
            
            if os.path.exists(template_file):
                with open(template_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                print(f"   ✅ File exists: {len(content)} characters")
                
                # Check for key content
                if 'packing_dashboard.html' in template_file:
                    checks = [
                        ('viewOrderDetails(', 'viewOrderDetails function calls'),
                        ('enhanced_modal.js', 'Enhanced modal JS inclusion'),
                        ('enhanced_order_modal.html', 'Enhanced modal template inclusion')
                    ]
                    
                    for check, description in checks:
                        if check in content:
                            print(f"   ✅ {description}: Found")
                        else:
                            print(f"   ❌ {description}: Missing")
                
                results[template_file] = 'PASS'
            else:
                print(f"   ❌ File does not exist")
                results[template_file] = 'FAIL'
                
        except Exception as e:
            print(f"   ❌ Template test error: {e}")
            results[template_file] = 'ERROR'
    
    return results

def create_manual_test_page():
    """Create a simple test page to bypass authentication"""
    print("\n🔍 CREATING MANUAL TEST PAGE")
    print("=" * 50)
    
    test_page_content = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Details Test Page</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/enhanced_modal.css">
</head>
<body>
    <div class="container mt-5">
        <h1>Order Details Test Page</h1>
        <p>This page tests the order details functionality without authentication.</p>
        
        <div class="row">
            <div class="col-md-6">
                <h3>Test Enhanced Modal</h3>
                <button class="btn btn-primary" onclick="testEnhancedModal()">
                    Test Enhanced Modal for ORD00000155
                </button>
                <div id="enhanced-result" class="mt-3"></div>
            </div>
            
            <div class="col-md-6">
                <h3>Test Basic Modal</h3>
                <button class="btn btn-secondary" onclick="testBasicModal()">
                    Test Basic Modal for ORD00000155
                </button>
                <div id="basic-result" class="mt-3"></div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h3>Test API Endpoints</h3>
                <button class="btn btn-info" onclick="testAPIEndpoints()">
                    Test All API Endpoints
                </button>
                <div id="api-result" class="mt-3"></div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h3>Console Output</h3>
                <div id="console-output" class="border p-3" style="height: 300px; overflow-y: auto; background-color: #f8f9fa;"></div>
            </div>
        </div>
    </div>

    <!-- Include Enhanced Order Modal -->
    <div class="modal fade" id="enhancedOrderModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Order Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="modalLoadingState">Loading...</div>
                    <div id="modalContent" style="display: none;"></div>
                    <div id="modalErrorState" style="display: none;"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/enhanced_modal.js"></script>
    
    <script>
        // Capture console output
        const originalLog = console.log;
        const originalError = console.error;
        const consoleOutput = document.getElementById('console-output');
        
        function addToConsole(message, type = 'log') {
            const div = document.createElement('div');
            div.className = type === 'error' ? 'text-danger' : 'text-dark';
            div.textContent = new Date().toLocaleTimeString() + ' - ' + message;
            consoleOutput.appendChild(div);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        function testEnhancedModal() {
            console.log('Testing enhanced modal...');
            const result = document.getElementById('enhanced-result');
            
            if (typeof showEnhancedOrderDetails === 'function') {
                result.innerHTML = '<div class="alert alert-success">Enhanced modal function available</div>';
                try {
                    showEnhancedOrderDetails('ORD00000155');
                } catch (error) {
                    result.innerHTML = '<div class="alert alert-danger">Enhanced modal error: ' + error.message + '</div>';
                }
            } else {
                result.innerHTML = '<div class="alert alert-warning">Enhanced modal function not available</div>';
            }
        }
        
        function testBasicModal() {
            console.log('Testing basic modal...');
            const result = document.getElementById('basic-result');
            
            fetch('/orders/ORD00000155/details')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        result.innerHTML = '<div class="alert alert-success">Basic modal API working</div>';
                    } else {
                        result.innerHTML = '<div class="alert alert-danger">Basic modal API error: ' + data.error + '</div>';
                    }
                })
                .catch(error => {
                    result.innerHTML = '<div class="alert alert-danger">Basic modal fetch error: ' + error.message + '</div>';
                });
        }
        
        function testAPIEndpoints() {
            console.log('Testing API endpoints...');
            const result = document.getElementById('api-result');
            result.innerHTML = '<div class="alert alert-info">Testing API endpoints...</div>';
            
            const endpoints = [
                '/api/order-details/ORD00000155',
                '/api/order-qr-code/ORD00000155'
            ];
            
            Promise.all(endpoints.map(endpoint => 
                fetch(endpoint).then(response => ({
                    endpoint,
                    status: response.status,
                    ok: response.ok
                }))
            )).then(results => {
                let html = '<div class="alert alert-success">API Test Results:</div><ul>';
                results.forEach(result => {
                    const status = result.ok ? 'success' : 'danger';
                    html += `<li class="text-${status}">${result.endpoint}: HTTP ${result.status}</li>`;
                });
                html += '</ul>';
                result.innerHTML = html;
            }).catch(error => {
                result.innerHTML = '<div class="alert alert-danger">API test error: ' + error.message + '</div>';
            });
        }
        
        // Auto-run tests on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded successfully');
            testAPIEndpoints();
        });
    </script>
</body>
</html>
"""
    
    try:
        with open('static/test_order_details.html', 'w', encoding='utf-8') as f:
            f.write(test_page_content)
        
        print("✅ Manual test page created: static/test_order_details.html")
        print("📍 Access at: http://127.0.0.1:5001/static/test_order_details.html")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create test page: {e}")
        return False

def main():
    """Run individual component tests"""
    print("🚀 INDIVIDUAL COMPONENT TESTING")
    print("=" * 80)
    print("Testing each component separately to isolate issues")
    print("=" * 80)
    
    # Test each component individually
    db_ok = test_database_component()
    api_results = test_api_component()
    routes_results = test_routes_component()
    static_results = test_static_files_component()
    template_results = test_template_files_component()
    test_page_ok = create_manual_test_page()
    
    # Summary
    print(f"\n📊 INDIVIDUAL COMPONENT TEST RESULTS")
    print("=" * 60)
    
    print(f"Database Component: {'✅ WORKING' if db_ok else '❌ ISSUES'}")
    
    print(f"\nAPI Components:")
    for endpoint, result in api_results.items():
        status = '✅ PASS' if result == 'PASS' else '❌ FAIL'
        print(f"  {endpoint}: {status}")
    
    print(f"\nRoute Components:")
    for route, result in routes_results.items():
        status = '✅ PASS' if result == 'PASS' else '❌ FAIL'
        print(f"  {route}: {status}")
    
    print(f"\nStatic File Components:")
    for file_path, result in static_results.items():
        status = '✅ PASS' if result == 'PASS' else '❌ FAIL'
        print(f"  {file_path}: {status}")
    
    print(f"\nTemplate Components:")
    for template, result in template_results.items():
        status = '✅ PASS' if result == 'PASS' else '❌ FAIL'
        print(f"  {template}: {status}")
    
    print(f"\nManual Test Page: {'✅ CREATED' if test_page_ok else '❌ FAILED'}")
    
    # Overall assessment
    all_api_pass = all(result == 'PASS' for result in api_results.values())
    all_routes_pass = all(result == 'PASS' for result in routes_results.values())
    all_static_pass = all(result == 'PASS' for result in static_results.values())
    all_templates_pass = all(result == 'PASS' for result in template_results.values())
    
    if db_ok and all_api_pass and all_routes_pass and all_static_pass and all_templates_pass:
        print(f"\n🎉 ALL COMPONENTS WORKING!")
        print("✅ Database layer functional")
        print("✅ API endpoints working")
        print("✅ Route handlers working")
        print("✅ Static files accessible")
        print("✅ Templates available")
        
        if test_page_ok:
            print(f"\n🌐 NEXT STEP: Open the manual test page")
            print(f"📍 URL: http://127.0.0.1:5001/static/test_order_details.html")
            print(f"This page will test the order details functionality without authentication")
    else:
        print(f"\n⚠️ SOME COMPONENTS HAVE ISSUES")
        print("Please check the failed components above")

if __name__ == "__main__":
    main()
