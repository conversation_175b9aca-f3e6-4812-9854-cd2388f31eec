#!/usr/bin/env python3
"""
Initialize the database with schema and sample data
"""
import sqlite3
import os

def init_database():
    """Initialize database with schema"""
    db_path = 'medivent.db'
    schema_path = 'schema.sql'
    
    print(f"Initializing database: {db_path}")
    
    # Remove existing database if it exists
    if os.path.exists(db_path):
        print(f"Removing existing database: {db_path}")
        os.remove(db_path)
    
    # Create new database
    db = sqlite3.connect(db_path)
    db.row_factory = sqlite3.Row
    
    # Read and execute schema
    if os.path.exists(schema_path):
        print(f"Reading schema from: {schema_path}")
        with open(schema_path, 'r') as f:
            schema_sql = f.read()
        
        # Execute schema
        db.executescript(schema_sql)
        print("✅ Schema executed successfully")
    else:
        print(f"❌ Schema file not found: {schema_path}")
        return False
    
    # Add some sample data for testing
    print("Adding sample data...")
    
    # Add admin user
    db.execute('''
        INSERT INTO users (username, password_hash, full_name, email, role, status)
        VALUES ('admin', 'pbkdf2:sha256:600000$salt$hash', 'Admin User', '<EMAIL>', 'admin', 'active')
    ''')
    
    # Add sample customer
    db.execute('''
        INSERT INTO customers (customer_id, name, address, phone, email, status)
        VALUES ('CUST001', 'Test Customer', '123 Test Street', '+92-300-1234567', '<EMAIL>', 'active')
    ''')
    
    # Add sample product
    db.execute('''
        INSERT INTO products (product_id, name, strength, manufacturer, category, unit_of_measure, unit_price)
        VALUES ('P001', 'Test Medicine', '500mg', 'Test Pharma', 'Tablets', 'Box', 100.0)
    ''')
    
    # Add sample warehouse
    db.execute('''
        INSERT INTO warehouses (warehouse_id, name, address, city, country, status)
        VALUES ('WH001', 'Main Warehouse', 'Industrial Area', 'Karachi', 'Pakistan', 'active')
    ''')
    
    # Add sample order
    db.execute('''
        INSERT INTO orders (order_id, customer_name, customer_address, customer_phone, order_amount, status, payment_status)
        VALUES ('ORD001', 'Test Customer', '123 Test Street', '+92-300-1234567', 1000.0, 'Approved', 'pending')
    ''')
    
    # Add sample order that will be used for DC generation
    db.execute('''
        INSERT INTO orders (order_id, customer_name, customer_address, customer_phone, order_amount, status, payment_status)
        VALUES ('ORD002', 'Test Customer 2', '456 Test Avenue', '+92-300-7654321', 1500.0, 'Finance Pending', 'pending')
    ''')
    
    db.commit()
    db.close()
    
    print("✅ Database initialized successfully!")
    print(f"Database file: {db_path}")
    print("Sample data added:")
    print("  - Admin user: admin/admin")
    print("  - Test customer: CUST001")
    print("  - Test product: P001")
    print("  - Test warehouse: WH001")
    print("  - Test orders: ORD001 (Approved), ORD002 (Finance Pending)")
    
    return True

if __name__ == '__main__':
    init_database()
