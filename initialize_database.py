#!/usr/bin/env python3
"""
Initialize database with proper schema to fix missing tables issue
"""

import sqlite3
import os
from datetime import datetime

def initialize_database():
    """Initialize database with proper schema"""
    # Use the same path as Flask app
    import os
    instance_path = os.path.join(os.getcwd(), 'instance')
    os.makedirs(instance_path, exist_ok=True)
    db_path = os.path.join(instance_path, 'medivent.db')
    
    print(f"🔧 Initializing database: {db_path}")
    
    try:
        # Connect to database
        db = sqlite3.connect(db_path)
        db.row_factory = sqlite3.Row
        
        print("📖 Reading schema file...")
        
        # Read and execute schema
        with open('schema.sql', 'r', encoding='utf-8') as f:
            schema_sql = f.read()
        
        print("🗂️  Creating tables...")
        db.executescript(schema_sql)
        
        print("✅ Database schema applied successfully!")
        
        # Verify tables were created
        cursor = db.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        tables = cursor.fetchall()
        
        print(f"\n📋 Tables created ({len(tables)}):")
        for table in tables:
            print(f"   ✅ {table[0]}")
        
        # Verify customers table specifically
        if any(table[0] == 'customers' for table in tables):
            print(f"\n🔍 Verifying customers table:")
            cursor.execute("SELECT COUNT(*) as count FROM customers")
            count = cursor.fetchone()[0]
            print(f"   📊 Customers records: {count}")
            
            if count > 0:
                cursor.execute("SELECT customer_id, name FROM customers LIMIT 3")
                customers = cursor.fetchall()
                for customer in customers:
                    print(f"      - {customer[0]}: {customer[1]}")
        
        # Verify orders table has customer_id column
        if any(table[0] == 'orders' for table in tables):
            print(f"\n🔍 Verifying orders table schema:")
            cursor.execute("PRAGMA table_info(orders)")
            columns = cursor.fetchall()
            
            column_names = [col[1] for col in columns]
            if 'customer_id' in column_names:
                print("   ✅ orders.customer_id column EXISTS")
            else:
                print("   ❌ orders.customer_id column MISSING")
                
            if 'warehouse_status' in column_names:
                print("   ✅ orders.warehouse_status column EXISTS")
            else:
                print("   ❌ orders.warehouse_status column MISSING")
        
        # Add some sample orders with customer_id for testing
        print(f"\n📝 Adding sample orders for testing...")
        
        sample_orders = [
            ('ORD001', 'CUST001', 'ABC Pharmacy', 'Shop #12, Main Market, Gulshan-e-Iqbal', '+92-21-1234567', 'Ready for Pickup', 1500.00),
            ('ORD002', 'CUST002', 'XYZ Medical Store', '456 Medical Plaza, Johar Town', '+92-42-7654321', 'Ready for Pickup', 2300.00),
            ('ORD003', 'CUST003', 'Health Plus Clinic', 'Clinic Building, F-8 Markaz', '+92-51-9876543', 'Approved', 1800.00)
        ]
        
        for order_id, customer_id, customer_name, address, phone, status, amount in sample_orders:
            db.execute('''
                INSERT OR IGNORE INTO orders (
                    order_id, customer_id, customer_name, customer_address, 
                    customer_phone, status, order_amount, order_date
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (order_id, customer_id, customer_name, address, phone, status, amount, datetime.now()))
        
        db.commit()
        
        # Verify sample orders
        cursor.execute("SELECT COUNT(*) as count FROM orders")
        orders_count = cursor.fetchone()[0]
        print(f"   📊 Orders records: {orders_count}")
        
        db.close()
        
        print(f"\n🎉 Database initialization completed successfully!")
        print(f"   📁 Database file: {db_path}")
        print(f"   🗂️  Total tables: {len(tables)}")
        print(f"   👥 Customers: Available")
        print(f"   📦 Orders: Available with customer_id")
        
        return True
        
    except Exception as e:
        print(f"❌ Error initializing database: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    initialize_database()
