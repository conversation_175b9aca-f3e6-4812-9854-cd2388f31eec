#!/usr/bin/env python3
"""
Installation script for Enhanced Order Modal
Automates the setup process and validates installation
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_header(title):
    """Print formatted header"""
    print(f"\n{'='*60}")
    print(f"🚀 {title}")
    print(f"{'='*60}")

def print_step(step, description):
    """Print formatted step"""
    print(f"\n📋 Step {step}: {description}")
    print("-" * 40)

def run_command(command, description):
    """Run command and handle errors"""
    print(f"⚡ {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} completed successfully")
            return True
        else:
            print(f"❌ {description} failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ {description} failed: {e}")
        return False

def check_dependencies():
    """Check if required dependencies are installed"""
    print_step(1, "Checking Dependencies")
    
    dependencies = ['qrcode', 'PIL']
    missing = []
    
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✅ {dep} is available")
        except ImportError:
            print(f"❌ {dep} is missing")
            missing.append(dep)
    
    return len(missing) == 0, missing

def install_dependencies(missing_deps):
    """Install missing dependencies"""
    print_step(2, "Installing Dependencies")
    
    if not missing_deps:
        print("✅ All dependencies already installed")
        return True
    
    # Install qrcode with PIL support
    return run_command("pip install qrcode[pil] pillow", "Installing QR code libraries")

def create_directories():
    """Create required directories"""
    print_step(3, "Creating Directories")
    
    directories = [
        'static/qr_codes',
        'templates/components',
        'templates/examples',
        'utils'
    ]
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"✅ Created/verified directory: {directory}")
        except Exception as e:
            print(f"❌ Failed to create directory {directory}: {e}")
            return False
    
    return True

def validate_files():
    """Validate that all required files exist"""
    print_step(4, "Validating Files")
    
    required_files = [
        'templates/components/enhanced_order_modal.html',
        'static/css/enhanced_modal.css',
        'static/js/enhanced_modal.js',
        'utils/qr_code_generator.py',
        'templates/examples/enhanced_modal_integration.html'
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing_files.append(file_path)
    
    return len(missing_files) == 0, missing_files

def test_installation():
    """Test the installation"""
    print_step(5, "Testing Installation")
    
    try:
        # Test QR code generation
        from utils.qr_code_generator import OrderQRCodeGenerator
        
        generator = OrderQRCodeGenerator()
        test_data = {
            'order': {
                'order_id': 'INSTALL_TEST',
                'order_date_formatted': '2025-08-05 12:00',
                'status': 'Test',
                'order_amount': 100.0,
                'customer_name': 'Installation Test'
            },
            'items': [],
            'foc_items': [],
            'customer': {},
            'summary': {'total_items': 0, 'item_types': 0, 'foc_items_count': 0}
        }
        
        result = generator.generate_order_qr_code(test_data)
        
        if result.get('success'):
            print("✅ QR code generation test passed")
            
            # Clean up test file
            qr_file = result['qr_code'].get('file_path')
            if qr_file and os.path.exists(qr_file):
                os.remove(qr_file)
                print("✅ Test file cleaned up")
            
            return True
        else:
            print(f"❌ QR code generation test failed: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ Installation test failed: {e}")
        return False

def update_existing_templates():
    """Provide guidance for updating existing templates"""
    print_step(6, "Template Integration Guide")
    
    print("📝 To integrate the enhanced modal into your existing templates:")
    print()
    print("1. Add CSS to your template head:")
    print('   <link rel="stylesheet" href="{{ url_for(\'static\', filename=\'css/enhanced_modal.css\') }}">')
    print()
    print("2. Add JavaScript before closing body tag:")
    print('   <script src="{{ url_for(\'static\', filename=\'js/enhanced_modal.js\') }}"></script>')
    print()
    print("3. Include modal component:")
    print("   {% include 'components/enhanced_order_modal.html' %}")
    print()
    print("4. Update button onclick handlers:")
    print("   OLD: onclick=\"viewOrderDetails('ORD123')\"")
    print("   NEW: onclick=\"showEnhancedOrderDetails('ORD123')\"")
    print()
    print("5. Example templates to update:")
    
    # Find templates that might need updating
    template_dirs = ['templates/warehouse', 'templates/orders', 'templates/riders']
    for template_dir in template_dirs:
        if os.path.exists(template_dir):
            for file in os.listdir(template_dir):
                if file.endswith('.html'):
                    file_path = os.path.join(template_dir, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            if 'viewOrderDetails' in content:
                                print(f"   📄 {file_path}")
                    except:
                        pass

def create_backup():
    """Create backup of existing files"""
    print_step(7, "Creating Backup")
    
    backup_dir = f"backup_enhanced_modal_{int(__import__('time').time())}"
    
    files_to_backup = [
        'templates/warehouse/packing_dashboard.html'  # We modified this file
    ]
    
    backed_up = []
    for file_path in files_to_backup:
        if os.path.exists(file_path):
            try:
                os.makedirs(backup_dir, exist_ok=True)
                backup_path = os.path.join(backup_dir, os.path.basename(file_path))
                shutil.copy2(file_path, backup_path)
                backed_up.append(file_path)
                print(f"✅ Backed up: {file_path}")
            except Exception as e:
                print(f"⚠️ Could not backup {file_path}: {e}")
    
    if backed_up:
        print(f"📁 Backup created in: {backup_dir}")
    else:
        print("ℹ️ No files needed backup")

def main():
    """Main installation function"""
    print_header("ENHANCED ORDER MODAL INSTALLATION")
    
    print("🎯 This script will install and configure the Enhanced Order Modal")
    print("   with QR code integration for your Medivent ERP system.")
    
    # Step 1: Check dependencies
    deps_ok, missing_deps = check_dependencies()
    
    # Step 2: Install dependencies if needed
    if not deps_ok:
        if not install_dependencies(missing_deps):
            print("\n❌ Failed to install dependencies. Please install manually:")
            print("   pip install qrcode[pil] pillow")
            return False
    
    # Step 3: Create directories
    if not create_directories():
        print("\n❌ Failed to create required directories")
        return False
    
    # Step 4: Validate files
    files_ok, missing_files = validate_files()
    if not files_ok:
        print(f"\n❌ Missing {len(missing_files)} required files:")
        for file in missing_files:
            print(f"   - {file}")
        print("\nPlease ensure all files are in the correct locations.")
        return False
    
    # Step 5: Test installation
    if not test_installation():
        print("\n❌ Installation test failed")
        return False
    
    # Step 6: Create backup
    create_backup()
    
    # Step 7: Integration guide
    update_existing_templates()
    
    # Success message
    print_header("INSTALLATION COMPLETE")
    print("🎉 Enhanced Order Modal has been successfully installed!")
    print()
    print("📋 NEXT STEPS:")
    print("1. Review the integration guide above")
    print("2. Update your templates to use the enhanced modal")
    print("3. Test with real order data")
    print("4. Check the documentation: ENHANCED_MODAL_DOCUMENTATION.md")
    print("5. Run validation: python test_enhanced_modal.py")
    print()
    print("🔗 DEMO:")
    print("   Visit /examples/enhanced-modal-integration to see the demo")
    print()
    print("📞 SUPPORT:")
    print("   If you encounter issues, run: python test_enhanced_modal.py")
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
