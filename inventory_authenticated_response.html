<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Medivent Pharmaceuticals ERP</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">

    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">

    <!-- Select2 CSS for enhanced dropdowns -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">

    <!-- Animate.css for animations -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <!-- Custom CSS -->
    <style>
        :root {
            --primary: #0572CE;
            --secondary: #3A7CA5;
            --accent: #F7941D;
            --background: #F5F5F5;
            --card: #FFFFFF;
            --text: #333333;
            --text-light: #666666;
            --success: #4CAF50;
            --warning: #FFC107;
            --error: #F44336;
            --border: #E0E0E0;
        }

        body {
            background-color: var(--background);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .sidebar {
            background-color: var(--primary);
            color: white;
            min-height: 100vh;
            padding-top: 20px;
            width: 100%;
            overflow-y: auto;
            overflow-x: hidden;
            max-height: 100vh;
            position: relative;
            transition: all 0.3s ease;
        }

        /* Minimized sidebar styles */
        .sidebar.minimized {
            width: 60px;
            min-width: 60px;
        }

        .sidebar.minimized .sidebar-header h4,
        .sidebar.minimized .nav-link span,
        .sidebar.minimized .collapse {
            display: none;
        }

        .sidebar.minimized .nav-link {
            text-align: center;
            padding: 12px 0;
            justify-content: center;
        }

        .sidebar.minimized .nav-link i {
            margin-right: 0;
            font-size: 1.2rem;
        }

        /* Sidebar toggle button */
        .sidebar-toggle {
            position: absolute;
            top: 10px;
            right: -15px;
            background: var(--primary);
            color: white;
            border: 2px solid white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 1000;
            transition: all 0.3s ease;
            font-size: 12px;
        }

        .sidebar-toggle:hover {
            background: white;
            color: var(--primary);
            transform: scale(1.1);
        }

        /* Adjust main content when sidebar is minimized */
        .main-content {
            transition: margin-left 0.3s ease;
        }

        .main-content.sidebar-minimized {
            margin-left: -140px; /* Adjust based on sidebar width difference */
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 8px 12px;
            margin-bottom: 3px;
            border-radius: 5px;
            font-size: 0.9rem;
            white-space: nowrap;
            overflow: visible; /* Changed from hidden to visible */
            text-overflow: clip; /* Changed from ellipsis to clip */
            width: 100%;
            position: relative;
        }

        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.2);
            width: 100%;
            display: block;
        }

        /* Add tooltip-style hover effect for menu items */
        .sidebar .nav-link span {
            position: relative;
            z-index: 1;
        }

        .sidebar .nav-link:hover span::after {
            content: attr(data-title);
            position: absolute;
            left: 100%;
            top: 0;
            white-space: nowrap;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 3px;
            z-index: 999;
            margin-left: 10px;
            display: none; /* Will be shown via JavaScript */
        }

        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.2);
        }

        .sidebar .nav-link i {
            margin-right: 8px;
            width: 16px;
            text-align: center;
        }

        /* Submenu styling */
        .sidebar .collapse.list-unstyled {
            padding-left: 0;
            width: 100%;
            margin-left: 0;
            transition: all 0.3s ease;
        }

        .sidebar .collapse.list-unstyled .nav-link {
            padding-left: 25px; /* Further reduced padding for better text display */
            font-size: 0.85rem;
            width: 100%;
            text-overflow: ellipsis;
            overflow: hidden;
            position: relative; /* For icon positioning */
            transition: all 0.2s ease;
        }

        .sidebar .collapse.list-unstyled .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.25);
            transform: translateX(3px);
        }

        /* Dropdown toggle icon */
        .sidebar .nav-link.dropdown-toggle::after {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.3s ease;
        }

        .sidebar .nav-link.dropdown-toggle[aria-expanded="true"]::after {
            transform: translateY(-50%) rotate(180deg);
        }

        /* Position icons consistently */
        .sidebar .nav-link i {
            width: 14px;
            text-align: center;
            margin-right: 3px;
            position: absolute;
            left: 8px;
        }

        /* Add padding for text to accommodate icons */
        .sidebar .nav-link span {
            padding-left: 16px;
        }

        /* Prevent horizontal scrolling in sidebar */
        .sidebar .nav {
            width: 100%;
        }

        .sidebar .nav-item {
            width: 100%;
        }

        .content {
            padding: 20px;
        }

        .navbar {
            background-color: white;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        .card-header {
            border-radius: 10px 10px 0 0 !important;
            font-weight: bold;
        }

        .btn-primary {
            background-color: var(--primary);
            border-color: var(--primary);
        }

        .btn-primary:hover {
            background-color: var(--secondary);
            border-color: var(--secondary);
        }

        .table th {
            font-weight: 600;
        }

        .badge {
            padding: 5px 10px;
            font-weight: 500;
        }

        /* Modern UI/UX Improvements */

        /* Loading Spinner */
        .loading-spinner {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid var(--primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Progress Indicators */
        .progress-container {
            position: relative;
            margin: 20px 0;
        }

        .progress-bar-animated {
            animation: progress-bar-stripes 1s linear infinite;
        }

        /* Enhanced Buttons */
        .btn {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .btn-loading {
            pointer-events: none;
            opacity: 0.7;
        }

        .btn-loading::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            margin: auto;
            border: 2px solid transparent;
            border-top-color: #ffffff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
        }

        /* Enhanced Cards */
        .card {
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .card-enhanced {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        }

        /* Mobile Responsive Improvements */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -100%;
                top: 0;
                width: 280px;
                height: 100vh;
                z-index: 1000;
                transition: left 0.3s ease;
            }

            .sidebar.show {
                left: 0;
            }

            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 999;
                display: none;
            }

            .sidebar-overlay.show {
                display: block;
            }

            .content {
                padding: 10px;
            }

            .table-responsive {
                font-size: 0.875rem;
            }

            .btn-group-vertical .btn {
                margin-bottom: 5px;
            }
        }

        /* Enhanced Tables */
        .table-enhanced {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .table-enhanced thead th {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            color: white;
            border: none;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.875rem;
            letter-spacing: 0.5px;
        }

        .table-enhanced tbody tr {
            transition: all 0.2s ease;
        }

        .table-enhanced tbody tr:hover {
            background-color: rgba(5, 114, 206, 0.05);
            transform: scale(1.01);
        }

        /* Search Enhancement */
        .search-container {
            position: relative;
        }

        .search-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-radius: 0 0 8px 8px;
            max-height: 300px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .search-suggestion-item {
            padding: 10px 15px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.2s ease;
        }

        .search-suggestion-item:hover,
        .search-suggestion-item.active {
            background-color: var(--primary);
            color: white;
        }

        /* Notification Styles */
        .notification-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            max-width: 400px;
        }

        .notification {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--primary);
            animation: slideInRight 0.3s ease;
        }

        .notification.success {
            border-left-color: var(--success);
        }

        .notification.warning {
            border-left-color: var(--warning);
        }

        .notification.error {
            border-left-color: var(--error);
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* Export Button Styles */
        .export-buttons {
            margin: 20px 0;
        }

        .export-btn {
            margin-right: 10px;
            margin-bottom: 10px;
        }

        /* Dark Mode Support */
        @media (prefers-color-scheme: dark) {
            :root {
                --background: #1a1a1a;
                --card: #2d2d2d;
                --text: #ffffff;
                --text-light: #cccccc;
                --border: #404040;
            }

            body {
                background-color: var(--background);
                color: var(--text);
            }

            .card {
                background-color: var(--card);
                color: var(--text);
            }

            .table {
                color: var(--text);
            }

            .table-enhanced tbody tr:hover {
                background-color: rgba(255, 255, 255, 0.05);
            }
        }
    </style>

    

<style>
    /* Order workflow progress bar styling */
    .progress-bar {
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        transition: all 0.3s ease;
        cursor: pointer;
        color: white;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        position: relative;
        overflow: hidden;
        font-size: 0.9rem;
        border-right: 1px solid rgba(255, 255, 255, 0.3);
    }

    .progress-bar:last-child {
        border-right: none;
    }

    .progress-bar:hover {
        opacity: 0.9;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        z-index: 10;
    }

    .progress-bar:active {
        transform: translateY(1px);
    }

    .progress {
        overflow: visible;
        height: 60px !important;
        border-radius: 8px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        background-color: #f8f9fa;
        padding: 0;
        border: 1px solid #dee2e6;
        display: flex;
        flex-wrap: nowrap;
    }

    /* Status count styling */
    .status-count {
        display: block;
        font-size: 0.8rem;
        margin-top: 3px;
        opacity: 0.9;
    }

    /* Status label styling */
    .status-label {
        display: block;
        font-weight: bold;
    }

    /* Fix for status display */
    .progress-bar {
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 5px 0;
        min-width: 100px; /* Ensure minimum width for readability */
        height: 60px;
        line-height: 1.2;
    }

    /* Make sure text is centered and visible */
    .progress-bar .status-label {
        font-size: 14px;
        font-weight: bold;
        text-align: center;
        margin-bottom: 2px;
        color: white;
        text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
    }

    .progress-bar .status-count {
        font-size: 12px;
        text-align: center;
        color: white;
        text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
    }

    /* Add tooltip-like effect on hover */
    .progress-bar:after {
        content: "View Orders";
        position: absolute;
        bottom: -30px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0,0,0,0.8);
        color: white;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        opacity: 0;
        transition: opacity 0.3s;
        pointer-events: none;
        white-space: nowrap;
    }

    .progress-bar:hover:after {
        opacity: 1;
    }

    /* Custom colors for better visibility */
    .bg-placed {
        background-color: #ffc107;
        border: 1px solid #e0a800;
    }
    .bg-approved {
        background-color: #17a2b8;
        border: 1px solid #138496;
    }
    .bg-processing {
        background-color: #007bff;
        border: 1px solid #0069d9;
    }
    .bg-ready {
        background-color: #6c757d;
        border: 1px solid #5a6268;
    }
    .bg-dispatched {
        background-color: #343a40;
        border: 1px solid #23272b;
    }
    .bg-delivered {
        background-color: #28a745;
        border: 1px solid #218838;
    }
    .bg-cancelled {
        background-color: #dc3545;
        border: 1px solid #c82333;
    }
</style>

</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Mobile Sidebar Overlay -->
            <div class="sidebar-overlay" id="sidebarOverlay"></div>

            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar d-none d-md-block" id="sidebarMenu">
                <!-- Sidebar Toggle Button -->
                <div class="sidebar-toggle" id="sidebarToggle" onclick="toggleSidebarMinimize()" title="Minimize/Maximize Sidebar">
                    <i class="fas fa-chevron-left" id="toggleIcon"></i>
                </div>

                <div class="text-center mb-4 sidebar-header">
                    <h4>Medivent ERP</h4>
                </div>
                <ul class="nav flex-column">
                    
                    <li class="nav-item">
                        <a class="nav-link active" href="/dashboard">
                            <i class="fas fa-tachometer-alt"></i><span>Dashboard</span>
                        </a>
                    </li>
                    

                    
                    <li class="nav-item">
                        <a class="nav-link " href="/dashboard?view=ceo">
                            <i class="fas fa-chart-line"></i><span>Executive Dashboard</span>
                        </a>
                    </li>
                    

                    <!-- Order Management -->
                    
                    <li class="nav-item">
                        <a class="nav-link dropdown-toggle " href="#orderSubmenu" data-toggle="collapse" aria-expanded="false">
                            <i class="fas fa-shopping-cart"></i><span>Order Management</span>
                        </a>
                        <ul class="collapse list-unstyled ml-3" id="orderSubmenu">
                            <li>
                                <a class="nav-link " href="/orders/new">
                                    <i class="fas fa-plus-circle"></i><span>Place New Order</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/orders/">
                                    <i class="fas fa-edit"></i><span>Update Order</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/orders/?search=true">
                                    <i class="fas fa-search"></i><span>Search Orders</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/orders/workflow">
                                    <i class="fas fa-tasks"></i><span>Order Workflow</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/orders/workflow?status=Ready+for+Pickup">
                                    <i class="fas fa-truck"></i><span>Dispatch Orders</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/orders/workflow?status=Dispatched">
                                    <i class="fas fa-check-circle"></i><span>Deliver Orders</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/orders/?history=true">
                                    <i class="fas fa-history"></i><span>Order History</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/orders/?invoice=true">
                                    <i class="fas fa-file-invoice"></i><span>Fetch Invoice/Challan</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    

                    <!-- Product -->
                    
                    <li class="nav-item">
                        <a class="nav-link dropdown-toggle " href="#productSubmenu" data-toggle="collapse" aria-expanded="false">
                            <i class="fas fa-pills"></i><span>Product</span>
                        </a>
                        <ul class="collapse list-unstyled ml-3" id="productSubmenu">
                            <li>
                                <a class="nav-link " href="/products/">
                                    <i class="fas fa-th-large"></i><span>Products Gallery</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/product_management">
                                    <i class="fas fa-cogs"></i><span>Product Management</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/products/new">
                                    <i class="fas fa-plus-circle"></i><span>Add Product</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/products/update_selection">
                                    <i class="fas fa-edit"></i><span>Update Product</span>
                                </a>
                            </li>

                            <li>
                                <a class="nav-link " href="/products/view_all/">
                                    <i class="fas fa-list"></i><span>View All Products</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    

                    <!-- Warehouse Management -->
                    
                    <li class="nav-item">
                        <a class="nav-link dropdown-toggle " href="#warehouseSubmenu" data-toggle="collapse" aria-expanded="false">
                            <i class="fas fa-warehouse"></i><span>Warehouse Management</span>
                        </a>
                        <ul class="collapse list-unstyled ml-3" id="warehouseSubmenu">
                            <li>
                                <a class="nav-link " href="/warehouse-management/add">
                                    <i class="fas fa-plus-circle"></i><span>Add Warehouse</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/warehouse-management/manage">
                                    <i class="fas fa-warehouse"></i><span>Manage Warehouses</span>
                                </a>
                            </li>

                            <li>
                                <a class="nav-link " href="/dc-pending">
                                    <i class="fas fa-clock"></i><span>DC Pending</span>
                                </a>
                            </li>

                            <li>
                                <a class="nav-link " href="/inventory/">
                                    <i class="fas fa-boxes"></i><span>Inventory Management</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/delivery_challans">
                                    <i class="fas fa-truck"></i><span>All Delivery Challans</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/warehouse/reports">
                                    <i class="fas fa-chart-line"></i><span>Warehouse Reports</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    

                    <!-- Packing Operations -->
                    
                    <li class="nav-item">
                        <a class="nav-link dropdown-toggle " href="#packingSubmenu" data-toggle="collapse" aria-expanded="false">
                            <i class="fas fa-box-open"></i><span>Packing Operations</span>
                        </a>
                        <ul class="collapse list-unstyled ml-3" id="packingSubmenu">
                            <li>
                                <a class="nav-link " href="/warehouse/packing">
                                    <i class="fas fa-box-open"></i><span>Packing Dashboard</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/warehouse/orders">
                                    <i class="fas fa-list-ul"></i><span>Orders Queue</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    

                    <!-- Rider Management - Comprehensive Menu -->
                    
                    <li class="nav-item">
                        <a class="nav-link dropdown-toggle " href="#riderSubmenu" data-toggle="collapse" aria-expanded="false">
                            <i class="fas fa-motorcycle"></i><span>Rider Management</span>
                        </a>
                        <ul class="collapse list-unstyled ml-3" id="riderSubmenu">
                            <li>
                                <a class="nav-link " href="/riders/dashboard">
                                    <i class="fas fa-users"></i><span>All Riders</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/riders/register">
                                    <i class="fas fa-user-plus"></i><span>Register Rider</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/rider/delivery_routes">
                                    <i class="fas fa-route"></i><span>Delivery Routes</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/rider/performance">
                                    <i class="fas fa-chart-line"></i><span>Rider Performance</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/riders/reports">
                                    <i class="fas fa-chart-bar"></i><span>Reports</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/riders/assignment-dashboard">
                                    <i class="fas fa-clipboard-list"></i><span>Assignment Dashboard</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/riders/dashboard">
                                    <i class="fas fa-tachometer-alt"></i><span>Professional Dashboard</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/admin/rider_tracking">
                                    <i class="fas fa-map-marker-alt"></i><span>Admin Tracking</span>
                                </a>
                            </li>

                        </ul>
                    </li>
                    

                    <!-- TCS Tracking -->
                    <li class="nav-item">
                        <a class="nav-link " href="/track">
                            <i class="fas fa-shipping-fast"></i><span>TCS Tracking</span>
                        </a>
                    </li>

                    <!-- Reports -->
                    
                    <li class="nav-item">
                        <a class="nav-link dropdown-toggle " href="#reportsSubmenu" data-toggle="collapse" aria-expanded="false">
                            <i class="fas fa-chart-bar"></i><span>Reports</span>
                        </a>
                        <ul class="collapse list-unstyled ml-3" id="reportsSubmenu">
                            <li>
                                <a class="nav-link " href="/sales_report/daily">
                                    <i class="fas fa-calendar-day"></i><span>Daily Sales Report</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/weekly_sales_report">
                                    <i class="fas fa-calendar-week"></i><span>Weekly Sales Report</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/sales_report/monthly">
                                    <i class="fas fa-calendar-alt"></i><span>Monthly Sales Report</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/reports/custom-date-range">
                                    <i class="fas fa-calendar"></i><span>Custom Date Range Report</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/reports/sales-by-agent">
                                    <i class="fas fa-user-tie"></i><span>Sales by Agent Report</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/reports/product-performance">
                                    <i class="fas fa-chart-line"></i><span>Product Performance Report</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/inventory_report">
                                    <i class="fas fa-boxes"></i><span>Inventory Reports</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/reports">
                                    <i class="fas fa-chart-pie"></i><span>All Reports</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    

                    <!-- Finance/Accounts -->
                    
                    <li class="nav-item">
                        <a class="nav-link dropdown-toggle " href="#financeSubmenu" data-toggle="collapse" aria-expanded="false">
                            <i class="fas fa-money-bill-wave"></i><span>Finance/Accounts</span>
                        </a>
                        <ul class="collapse list-unstyled ml-3" id="financeSubmenu">
                            <li>
                                <a class="nav-link " href="/finance/dashboard">
                                    <i class="fas fa-tachometer-alt"></i><span>Finance Dashboard</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/finance/pending-invoices">
                                    <i class="fas fa-file-invoice-dollar"></i><span>Pending Invoices</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/finance/customer-ledger">
                                    <i class="fas fa-users"></i><span>Customer Ledger</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/finance/payment-collection">
                                    <i class="fas fa-hand-holding-usd"></i><span>Payment Collection</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/finance/financial-reports">
                                    <i class="fas fa-chart-pie"></i><span>Financial Reports</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/finance/comprehensive-reports">
                                    <i class="fas fa-file-alt"></i><span>Comprehensive Reports</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    



                    <!-- Sales Team Management -->
                    <li class="nav-item">
                        <a class="nav-link dropdown-toggle " href="#salesSubmenu" data-toggle="collapse" aria-expanded="false">
                            <i class="fas fa-users"></i><span>Sales Team</span>
                        </a>
                        <ul class="collapse list-unstyled ml-3" id="salesSubmenu">
                            <li>
                                <a class="nav-link " href="/sales-team">
                                    <i class="fas fa-chart-line"></i><span>Sales Dashboard</span>
                                </a>
                            </li>
                            <!-- Dynamic Division Submenus -->
                            
                            <li>
                                <a class="nav-link" href="/sales-team/division/Aqvida">
                                    <i class="fas fa-building"></i><span>Aqvida Division</span>
                                </a>
                            </li>
                            
                            <li>
                                <a class="nav-link" href="/sales-team/division/Finance%20Division">
                                    <i class="fas fa-building"></i><span>Finance Division Division</span>
                                </a>
                            </li>
                            
                            <li>
                                <a class="nav-link" href="/sales-team/division/Sales%20Division">
                                    <i class="fas fa-building"></i><span>Sales Division Division</span>
                                </a>
                            </li>
                            
                            <li>
                                <a class="nav-link" href="/sales-team/division/new%20divison">
                                    <i class="fas fa-building"></i><span>new divison Division</span>
                                </a>
                            </li>
                            
                            <li>
                                <a class="nav-link" href="/sales-team/division/test05">
                                    <i class="fas fa-building"></i><span>test05 Division</span>
                                </a>
                            </li>
                            
                            <li>
                                <a class="nav-link" href="/reports/sales-by-agent">
                                    <i class="fas fa-user-tie"></i><span>Agent Performance</span>
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- User Management -->
                    
                    <li class="nav-item">
                        <a class="nav-link dropdown-toggle " href="#userSubmenu" data-toggle="collapse" aria-expanded="false">
                            <i class="fas fa-users-cog"></i><span>User Management</span>
                        </a>
                        <ul class="collapse list-unstyled ml-3" id="userSubmenu">
                            <li>
                                <a class="nav-link " href="/users">
                                    <i class="fas fa-users"></i><span>View All Users</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/users/new">
                                    <i class="fas fa-user-plus"></i><span>Add New User</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/users/permissions">
                                    <i class="fas fa-user-tag"></i><span>Manage Roles</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/settings">
                                    <i class="fas fa-key"></i><span>Change Password</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/users/permissions">
                                    <i class="fas fa-clipboard-list"></i><span>View User Activity Logs</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    

                    <!-- Division Management -->
                    
                    <li class="nav-item">
                        <a class="nav-link dropdown-toggle " href="#divisionSubmenu" data-toggle="collapse" aria-expanded="false">
                            <i class="fas fa-building"></i><span>Division Management</span>
                        </a>
                        <ul class="collapse list-unstyled ml-3" id="divisionSubmenu">
                            <li>
                                <a class="nav-link " href="/divisions/">
                                    <i class="fas fa-list"></i><span>All Divisions</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/divisions/analytics">
                                    <i class="fas fa-chart-line"></i><span>Analytics</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link" href="/divisions/export">
                                    <i class="fas fa-download"></i><span>Export Data</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    

                    <!-- Customer Management -->
                    
                    <li class="nav-item">
                        <a class="nav-link dropdown-toggle " href="#customerSubmenu" data-toggle="collapse" aria-expanded="false">
                            <i class="fas fa-user-tag"></i><span>Customer Management</span>
                        </a>
                        <ul class="collapse list-unstyled ml-3" id="customerSubmenu">
                            <li>
                                <a class="nav-link " href="/customers">
                                    <i class="fas fa-users"></i><span>View All Customers</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/customers?view=by_type">
                                    <i class="fas fa-user-tag"></i><span>View Customers by Type</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/customers?action=add">
                                    <i class="fas fa-user-plus"></i><span>Add New Customer</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/customers?view=pricing">
                                    <i class="fas fa-tags"></i><span>View Customer Pricing</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/customers?action=set_pricing">
                                    <i class="fas fa-dollar-sign"></i><span>Set Customer Pricing</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/customers?view=orders">
                                    <i class="fas fa-history"></i><span>View Customer Order History</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    



                    <!-- Import Data -->
                    
                    <li class="nav-item">
                        <a class="nav-link " href="/import_data">
                            <i class="fas fa-file-import"></i><span>Import Data</span>
                        </a>
                    </li>
                    

                    <!-- Settings -->
                    
                    <li class="nav-item">
                        <a class="nav-link " href="/settings">
                            <i class="fas fa-cog"></i><span>Settings</span>
                        </a>
                    </li>
                    
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 ml-sm-auto px-0 main-content" id="mainContent">
                <!-- Top Navbar -->
                <nav class="navbar navbar-expand-lg navbar-light">
                    <button class="navbar-toggler d-md-none" type="button" onclick="toggleSidebar()">
                        <span class="navbar-toggler-icon"></span>
                    </button>

                    <!-- Intelligent Search Bar -->
                    <div class="flex-grow-1 mx-3">
                        <!-- Intelligent Search Component -->
<div class="intelligent-search-container">
    <div class="search-input-wrapper">
        <div class="input-group">
            <span class="input-group-text search-icon">
                <i class="fas fa-search"></i>
            </span>
            <input type="text" 
                   class="form-control intelligent-search-input" 
                   id="intelligentSearchInput"
                   placeholder="Search orders, customers, products, finance..." 
                   autocomplete="off">
            <div class="input-group-append">
                <button class="btn btn-outline-secondary dropdown-toggle" 
                        type="button" 
                        data-bs-toggle="dropdown" 
                        aria-expanded="false">
                    <span id="searchTypeLabel">All</span>
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" data-type="all">All Results</a></li>
                    <li><a class="dropdown-item" href="#" data-type="customers">Customers</a></li>
                    <li><a class="dropdown-item" href="#" data-type="orders">Orders</a></li>
                    <li><a class="dropdown-item" href="#" data-type="products">Products</a></li>
                    <li><a class="dropdown-item" href="#" data-type="finance">Finance</a></li>
                </ul>
            </div>
        </div>
    </div>
    
    <!-- Search Results Dropdown -->
    <div class="search-results-dropdown" id="searchResultsDropdown" style="display: none;">
        <div class="search-results-header">
            <div class="d-flex justify-content-between align-items-center">
                <span class="search-results-title">Search Results</span>
                <span class="search-results-count" id="searchResultsCount">0 results</span>
            </div>
        </div>
        
        <!-- Suggestions Section -->
        <div class="search-suggestions-section" id="searchSuggestionsSection" style="display: none;">
            <div class="search-section-title">
                <i class="fas fa-lightbulb me-2"></i>Suggestions
            </div>
            <div class="search-suggestions" id="searchSuggestions"></div>
        </div>
        
        <!-- Results Section -->
        <div class="search-results-section" id="searchResultsSection">
            <div class="search-results" id="searchResults"></div>
        </div>
        
        <!-- No Results -->
        <div class="no-search-results" id="noSearchResults" style="display: none;">
            <div class="text-center py-4">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h6 class="text-muted">No results found</h6>
                <p class="text-muted small">Try different keywords or check spelling</p>
            </div>
        </div>
        
        <!-- Loading -->
        <div class="search-loading" id="searchLoading" style="display: none;">
            <div class="text-center py-4">
                <div class="spinner-border spinner-border-sm text-primary me-2" role="status"></div>
                <span>Searching...</span>
            </div>
        </div>
    </div>
</div>

<style>
.intelligent-search-container {
    position: relative;
    width: 100%;
    max-width: 500px;
}

.search-input-wrapper {
    position: relative;
}

.intelligent-search-input {
    border-radius: 25px;
    padding: 12px 20px;
    font-size: 14px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.intelligent-search-input:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.search-icon {
    background: transparent;
    border: none;
    color: #6c757d;
}

.search-results-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    z-index: 1050;
    max-height: 500px;
    overflow-y: auto;
    margin-top: 5px;
}

.search-results-header {
    padding: 15px 20px 10px;
    border-bottom: 1px solid #f8f9fa;
    background: #f8f9fa;
    border-radius: 15px 15px 0 0;
}

.search-results-title {
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.search-results-count {
    font-size: 12px;
    color: #6c757d;
}

.search-section-title {
    padding: 10px 20px 5px;
    font-size: 12px;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.search-suggestions {
    padding: 0 10px 10px;
}

.search-suggestion-item {
    display: flex;
    align-items: center;
    padding: 8px 15px;
    margin: 2px 0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
}

.search-suggestion-item:hover {
    background: #f8f9fa;
}

.search-suggestion-icon {
    width: 20px;
    margin-right: 10px;
    color: #6c757d;
    font-size: 12px;
}

.search-results {
    padding: 0 10px 10px;
}

.search-result-item {
    display: flex;
    align-items: flex-start;
    padding: 12px 15px;
    margin: 2px 0;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    color: inherit;
}

.search-result-item:hover {
    background: #f8f9fa;
    text-decoration: none;
    color: inherit;
    transform: translateY(-1px);
}

.search-result-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 16px;
    color: white;
    flex-shrink: 0;
}

.search-result-icon.customer { background: linear-gradient(135deg, #007bff, #0056b3); }
.search-result-icon.order { background: linear-gradient(135deg, #28a745, #1e7e34); }
.search-result-icon.product { background: linear-gradient(135deg, #ffc107, #e0a800); }
.search-result-icon.invoice { background: linear-gradient(135deg, #dc3545, #c82333); }
.search-result-icon.payment { background: linear-gradient(135deg, #6f42c1, #5a32a3); }

.search-result-content {
    flex: 1;
    min-width: 0;
}

.search-result-title {
    font-weight: 600;
    font-size: 14px;
    color: #212529;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.search-result-subtitle {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.search-result-description {
    font-size: 11px;
    color: #868e96;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.search-result-type {
    position: absolute;
    top: 8px;
    right: 8px;
    background: var(--primary);
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    text-transform: uppercase;
    font-weight: 500;
}

.no-search-results {
    padding: 20px;
}

.search-loading {
    padding: 20px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .intelligent-search-container {
        max-width: 100%;
    }
    
    .search-results-dropdown {
        max-height: 400px;
    }
}

/* Highlight matching text */
.search-highlight {
    background: rgba(255, 193, 7, 0.3);
    font-weight: 600;
    padding: 1px 2px;
    border-radius: 2px;
}

/* Animation for dropdown */
.search-results-dropdown {
    animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

<script>
class IntelligentSearch {
    constructor() {
        this.searchInput = document.getElementById('intelligentSearchInput');
        this.searchResults = document.getElementById('searchResults');
        this.searchSuggestions = document.getElementById('searchSuggestions');
        this.searchResultsDropdown = document.getElementById('searchResultsDropdown');
        this.searchResultsCount = document.getElementById('searchResultsCount');
        this.searchSuggestionsSection = document.getElementById('searchSuggestionsSection');
        this.searchResultsSection = document.getElementById('searchResultsSection');
        this.noSearchResults = document.getElementById('noSearchResults');
        this.searchLoading = document.getElementById('searchLoading');
        this.searchTypeLabel = document.getElementById('searchTypeLabel');
        
        this.currentSearchType = 'all';
        this.searchTimeout = null;
        this.currentQuery = '';
        
        this.init();
    }
    
    init() {
        // Search input events
        this.searchInput.addEventListener('input', (e) => {
            this.handleSearchInput(e.target.value);
        });
        
        this.searchInput.addEventListener('focus', () => {
            if (this.currentQuery.length >= 2) {
                this.showDropdown();
            }
        });
        
        this.searchInput.addEventListener('keydown', (e) => {
            this.handleKeydown(e);
        });
        
        // Search type dropdown
        document.querySelectorAll('.dropdown-item[data-type]').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                this.setSearchType(e.target.dataset.type, e.target.textContent);
            });
        });
        
        // Click outside to close
        document.addEventListener('click', (e) => {
            if (!this.searchInput.contains(e.target) && !this.searchResultsDropdown.contains(e.target)) {
                this.hideDropdown();
            }
        });
    }
    
    handleSearchInput(query) {
        this.currentQuery = query.trim();
        
        // Clear previous timeout
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }
        
        if (this.currentQuery.length < 2) {
            this.hideDropdown();
            return;
        }
        
        // Debounce search
        this.searchTimeout = setTimeout(() => {
            this.performSearch(this.currentQuery);
        }, 300);
    }
    
    async performSearch(query) {
        this.showLoading();
        
        try {
            const response = await fetch(`/api/intelligent-search?q=${encodeURIComponent(query)}&type=${this.currentSearchType}&limit=10`);
            const data = await response.json();
            
            if (data.error) {
                throw new Error(data.error);
            }
            
            this.displayResults(data);
            
        } catch (error) {
            console.error('Search error:', error);
            this.showNoResults();
        }
    }
    
    displayResults(data) {
        this.hideLoading();
        
        // Update results count
        this.searchResultsCount.textContent = `${data.total_found} result${data.total_found !== 1 ? 's' : ''}`;
        
        // Display suggestions
        if (data.suggestions && data.suggestions.length > 0) {
            this.displaySuggestions(data.suggestions);
            this.searchSuggestionsSection.style.display = 'block';
        } else {
            this.searchSuggestionsSection.style.display = 'none';
        }
        
        // Display results
        if (data.results && data.results.length > 0) {
            this.displaySearchResults(data.results);
            this.searchResultsSection.style.display = 'block';
            this.noSearchResults.style.display = 'none';
        } else {
            this.searchResultsSection.style.display = 'none';
            this.noSearchResults.style.display = 'block';
        }
        
        this.showDropdown();
    }
    
    displaySuggestions(suggestions) {
        this.searchSuggestions.innerHTML = suggestions.map(suggestion => `
            <div class="search-suggestion-item" onclick="intelligentSearch.selectSuggestion('${suggestion.text}')">
                <div class="search-suggestion-icon">
                    <i class="${suggestion.icon}"></i>
                </div>
                <span>${this.highlightMatch(suggestion.text, this.currentQuery)}</span>
            </div>
        `).join('');
    }
    
    displaySearchResults(results) {
        this.searchResults.innerHTML = results.map(result => `
            <a href="${result.url}" class="search-result-item" onclick="intelligentSearch.hideDropdown()">
                <div class="search-result-icon ${result.type}">
                    <i class="${result.icon}"></i>
                </div>
                <div class="search-result-content">
                    <div class="search-result-title">${this.highlightMatch(result.title, this.currentQuery)}</div>
                    <div class="search-result-subtitle">${this.highlightMatch(result.subtitle, this.currentQuery)}</div>
                    <div class="search-result-description">${result.description}</div>
                </div>
            </a>
        `).join('');
    }
    
    highlightMatch(text, query) {
        if (!query || !text) return text;
        
        const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
        return text.replace(regex, '<span class="search-highlight">$1</span>');
    }
    
    selectSuggestion(suggestion) {
        this.searchInput.value = suggestion;
        this.currentQuery = suggestion;
        this.performSearch(suggestion);
    }
    
    setSearchType(type, label) {
        this.currentSearchType = type;
        this.searchTypeLabel.textContent = label;
        
        if (this.currentQuery.length >= 2) {
            this.performSearch(this.currentQuery);
        }
    }
    
    showDropdown() {
        this.searchResultsDropdown.style.display = 'block';
    }
    
    hideDropdown() {
        this.searchResultsDropdown.style.display = 'none';
    }
    
    showLoading() {
        this.searchLoading.style.display = 'block';
        this.searchResultsSection.style.display = 'none';
        this.searchSuggestionsSection.style.display = 'none';
        this.noSearchResults.style.display = 'none';
        this.showDropdown();
    }
    
    hideLoading() {
        this.searchLoading.style.display = 'none';
    }
    
    showNoResults() {
        this.hideLoading();
        this.searchResultsSection.style.display = 'none';
        this.searchSuggestionsSection.style.display = 'none';
        this.noSearchResults.style.display = 'block';
        this.searchResultsCount.textContent = '0 results';
        this.showDropdown();
    }
    
    handleKeydown(e) {
        if (e.key === 'Escape') {
            this.hideDropdown();
        }
        // Add arrow key navigation here if needed
    }
}

// Initialize search when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.intelligentSearch = new IntelligentSearch();
});
</script>
                    </div>

                    <div class="collapse navbar-collapse" id="navbarSupportedContent">
                        <ul class="navbar-nav mr-auto">
                            <li class="nav-item">
                                <span class="navbar-text">
                                    <i class="far fa-calendar-alt"></i> Today: 2025-08-01
                                </span>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/track-order" title="Track Order">
                                    <i class="fas fa-search"></i> <small>Track Order</small>
                                </a>
                            </li>
                        </ul>
                        <ul class="navbar-nav">
                            <!-- Notifications Dropdown -->
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle position-relative" href="#" id="notificationsDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <i class="fas fa-bell"></i>
                                    <span class="badge badge-danger badge-pill position-absolute notification-badge" style="top: 0; right: 5px; font-size: 0.6rem; display: none;">0</span>
                                </a>
                                <div class="dropdown-menu dropdown-menu-right notification-dropdown" aria-labelledby="notificationsDropdown" style="width: 350px; max-height: 400px; overflow-y: auto;">
                                    <div class="dropdown-header d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-bell"></i> Notifications</span>
                                        <div>
                                            <button class="btn btn-sm btn-outline-secondary me-1" onclick="markAllNotificationsRead()" title="Mark all as read">
                                                <i class="fas fa-check-double"></i>
                                            </button>
                                            <a href="/notifications" class="btn btn-sm btn-outline-primary">View All</a>
                                        </div>
                                    </div>
                                    <div class="dropdown-divider"></div>
                                    <div id="notification-dropdown-list">
                                        <div class="dropdown-item text-center text-muted">
                                            <i class="fas fa-spinner fa-spin"></i> Loading...
                                        </div>
                                    </div>
                                    <div class="dropdown-divider"></div>
                                    <div class="dropdown-footer text-center">
                                        <a href="/notifications" class="btn btn-sm btn-primary btn-block">
                                            <i class="fas fa-bell"></i> View All Notifications
                                        </a>
                                    </div>
                                </div>
                            </li>

                            <!-- User Dropdown -->
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <i class="fas fa-user-circle"></i> admin
                                </a>
                                <div class="dropdown-menu dropdown-menu-right" aria-labelledby="navbarDropdown">
                                    <a class="dropdown-item" href="#">
                                        <i class="fas fa-user"></i> Profile
                                    </a>
                                    <div class="dropdown-divider"></div>
                                    <a class="dropdown-item" href="/logout">
                                        <i class="fas fa-sign-out-alt"></i> Logout
                                    </a>
                                </div>
                            </li>
                        </ul>
                    </div>
                </nav>

                <!-- Mobile Sidebar -->
                <div class="collapse d-md-none" id="sidebarMenu">
                    <ul class="nav flex-column bg-light p-2">
                        
                        <li class="nav-item">
                            <a class="nav-link text-dark active" href="/dashboard">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        

                        
                        <li class="nav-item">
                            <a class="nav-link text-dark " href="/orders/">
                                <i class="fas fa-shopping-cart"></i> Orders
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-dark " href="/orders/workflow">
                                <i class="fas fa-tasks"></i> Order Workflow
                            </a>
                        </li>
                        

                        
                        <li class="nav-item">
                            <a class="nav-link text-dark " href="/products/">
                                <i class="fas fa-pills"></i> Products
                            </a>
                        </li>
                        

                        
                        <li class="nav-item">
                            <a class="nav-link text-dark " href="/inventory/">
                                <i class="fas fa-warehouse"></i> Inventory
                            </a>
                        </li>
                        

                        
                        <li class="nav-item">
                            <a class="nav-link text-dark " href="/reports">
                                <i class="fas fa-chart-bar"></i> Reports
                            </a>
                        </li>
                        

                        <!-- Sales Team Mobile Menu -->
                        <li class="nav-item">
                            <a class="nav-link text-dark " href="/sales-team">
                                <i class="fas fa-users"></i> Sales Team
                            </a>
                        </li>
                        <!-- Dynamic Division Links for Mobile -->
                        
                        <li class="nav-item ml-3">
                            <a class="nav-link text-dark" href="/sales-team/division/Aqvida">
                                <i class="fas fa-building"></i> Aqvida
                            </a>
                        </li>
                        
                        <li class="nav-item ml-3">
                            <a class="nav-link text-dark" href="/sales-team/division/Finance%20Division">
                                <i class="fas fa-building"></i> Finance Division
                            </a>
                        </li>
                        
                        <li class="nav-item ml-3">
                            <a class="nav-link text-dark" href="/sales-team/division/Sales%20Division">
                                <i class="fas fa-building"></i> Sales Division
                            </a>
                        </li>
                        
                        <li class="nav-item ml-3">
                            <a class="nav-link text-dark" href="/sales-team/division/new%20divison">
                                <i class="fas fa-building"></i> new divison
                            </a>
                        </li>
                        
                        <li class="nav-item ml-3">
                            <a class="nav-link text-dark" href="/sales-team/division/test05">
                                <i class="fas fa-building"></i> test05
                            </a>
                        </li>
                        

                        
                        <li class="nav-item">
                            <a class="nav-link text-dark " href="/dashboard?view=finance_overview">
                                <i class="fas fa-money-bill-wave"></i> Finance/Accounts
                            </a>
                        </li>
                        

                        
                        <li class="nav-item">
                            <a class="nav-link text-dark " href="/settings">
                                <i class="fas fa-cog"></i> Settings
                            </a>
                        </li>
                        
                    </ul>
                </div>

                <!-- Content Area -->
                <div class="content">
                    <!-- Flash Messages -->
                    
                        
                            
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    Error loading inventory dashboard: &#39;sqlite3.Row object&#39; has no attribute &#39;available_stock&#39;
                                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                            
                        
                    

                    <!-- Main Content -->
                    
<div class="container-fluid">
    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <h2 class="card-title">Welcome, System Administrator</h2>
                    <p class="card-text">Welcome to Medivent Pharmaceuticals ERP System. Here's an overview of your business.</p>
                    
                </div>
            </div>
        </div>
    </div>

    
    <!-- Stats Cards -->
    <div class="row mb-4">
        
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">Total Orders</h6>
                            <h2 class="mb-0" id="orders-count">23</h2>
                        </div>
                        <i class="fas fa-shopping-cart fa-3x opacity-50"></i>
                    </div>
                </div>
                <div class="card-footer bg-transparent border-0">
                    <a href="/orders/" class="text-white">View Details <i class="fas fa-arrow-right ml-1"></i></a>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">Pending Approvals</h6>
                            <h2 class="mb-0" id="pending-approvals">0</h2>
                        </div>
                        <i class="fas fa-clock fa-3x opacity-50"></i>
                    </div>
                </div>
                <div class="card-footer bg-transparent border-0">
                    <a href="/orders/workflow?status=Placed" class="text-white">View Details <i class="fas fa-arrow-right ml-1"></i></a>
                </div>
            </div>
        </div>
        

        
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">Products</h6>
                            <h2 class="mb-0" id="products-count">15</h2>
                        </div>
                        <i class="fas fa-pills fa-3x opacity-50"></i>
                    </div>
                </div>
                <div class="card-footer bg-transparent border-0">
                    <a href="#" class="text-white">View Details <i class="fas fa-arrow-right ml-1"></i></a>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">Low Stock Items</h6>
                            <h2 class="mb-0" id="low-stock-count">1</h2>
                        </div>
                        <i class="fas fa-exclamation-triangle fa-3x opacity-50"></i>
                    </div>
                </div>
                <div class="card-footer bg-transparent border-0">
                    <a href="#" class="text-white">View Details <i class="fas fa-arrow-right ml-1"></i></a>
                </div>
            </div>
        </div>
        
    </div>

    
    <!-- Order Workflow Section -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Order Workflow</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="progress">
                                <a href="/orders/workflow?status=Placed" class="progress-bar bg-placed" role="progressbar"
                                   style="width: 14.3%; text-decoration: none;"
                                   aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                    <span class="status-label">Placed</span>
                                    <span class="status-count">(0)</span>
                                </a>
                                <a href="/orders/workflow?status=Approved" class="progress-bar bg-approved" role="progressbar"
                                   style="width: 14.3%; text-decoration: none;"
                                   aria-valuenow="4" aria-valuemin="0" aria-valuemax="100">
                                    <span class="status-label">Approved</span>
                                    <span class="status-count">(4)</span>
                                </a>
                                <a href="/orders/workflow?status=Processing" class="progress-bar bg-processing" role="progressbar"
                                   style="width: 14.3%; text-decoration: none;"
                                   aria-valuenow="1" aria-valuemin="0" aria-valuemax="100">
                                    <span class="status-label">Processing</span>
                                    <span class="status-count">(1)</span>
                                </a>
                                <a href="/orders/workflow?status=Ready+for+Pickup" class="progress-bar bg-ready" role="progressbar"
                                   style="width: 14.3%; text-decoration: none;"
                                   aria-valuenow="6" aria-valuemin="0" aria-valuemax="100">
                                    <span class="status-label">Ready</span>
                                    <span class="status-count">(6)</span>
                                </a>
                                <a href="/orders/workflow?status=Dispatched" class="progress-bar bg-dispatched" role="progressbar"
                                   style="width: 14.3%; text-decoration: none;"
                                   aria-valuenow="3" aria-valuemin="0" aria-valuemax="100">
                                    <span class="status-label">Dispatched</span>
                                    <span class="status-count">(3)</span>
                                </a>
                                <a href="/orders/workflow?status=Delivered" class="progress-bar bg-delivered" role="progressbar"
                                   style="width: 14.3%; text-decoration: none;"
                                   aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                    <span class="status-label">Delivered</span>
                                    <span class="status-count">(0)</span>
                                </a>
                                <a href="/orders/workflow?status=Cancelled" class="progress-bar bg-cancelled" role="progressbar"
                                   style="width: 14.3%; text-decoration: none;"
                                   aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                    <span class="status-label">Cancelled</span>
                                    <span class="status-count">(0)</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12 text-center">
                            <a href="/orders/workflow" class="btn btn-primary">Manage Order Workflow</a>
                            <a href="/orders/" class="btn btn-outline-primary ml-2">View All Orders</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="/orders/new" class="btn btn-outline-primary btn-block py-3">
                                <i class="fas fa-plus-circle mb-2 fa-2x"></i><br>
                                Place New Order
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="/orders/workflow?status=Placed" class="btn btn-outline-warning btn-block py-3">
                                <i class="fas fa-check-circle mb-2 fa-2x"></i><br>
                                Approve Orders
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="/orders/workflow?status=Approved" class="btn btn-outline-info btn-block py-3">
                                <i class="fas fa-truck mb-2 fa-2x"></i><br>
                                Dispatch Orders
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="/orders/workflow?status=Dispatched" class="btn btn-outline-success btn-block py-3">
                                <i class="fas fa-box-open mb-2 fa-2x"></i><br>
                                Deliver Orders
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Executive Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-gradient-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0" id="totalRevenueCard">Rs.0</h4>
                            <p class="mb-0">Total Revenue</p>
                            <small><i class="fas fa-info-circle"></i> No revenue data available</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-gradient-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0" id="totalOrdersCard">0</h4>
                            <p class="mb-0">Total Orders</p>
                            <small><i class="fas fa-info-circle"></i> No orders data available</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-shopping-cart fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-gradient-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0" id="avgOrderValueCard">Rs.0</h4>
                            <p class="mb-0">Avg Order Value</p>
                            <small><i class="fas fa-info-circle"></i> No order value data available</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calculator fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-gradient-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0" id="activeCustomersCard">0</h4>
                            <p class="mb-0">Active Customers</p>
                            <small><i class="fas fa-info-circle"></i> No customer data available</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Advanced Analytics Section -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5 class="mb-0">🏥 Medivent Pharmaceuticals - Advanced Business Intelligence</h5>
                    <small>Real-time analytics - No data available (empty database)</small>
                </div>
                <div class="card-body">
                    <!-- Analytics Navigation Tabs -->
                    <ul class="nav nav-tabs nav-tabs-custom" id="analyticsTab" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="executive-tab" data-toggle="tab" href="#executive-dashboard" role="tab">
                                <i class="fas fa-crown"></i> Executive Overview
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="sales-tab" data-toggle="tab" href="#sales-analytics" role="tab">
                                <i class="fas fa-chart-line"></i> Sales Analytics
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="division-tab" data-toggle="tab" href="#division-analytics" role="tab">
                                <i class="fas fa-building"></i> Division Performance
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="inventory-tab" data-toggle="tab" href="#inventory-analytics" role="tab">
                                <i class="fas fa-boxes"></i> Inventory Intelligence
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="financial-tab" data-toggle="tab" href="#financial-analytics" role="tab">
                                <i class="fas fa-dollar-sign"></i> Financial Intelligence
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="operational-tab" data-toggle="tab" href="#operational-analytics" role="tab">
                                <i class="fas fa-cogs"></i> Operations
                            </a>
                        </li>
                    </ul>

                    <div class="tab-content mt-3" id="analyticsTabContent">
                        <!-- Executive Overview Tab -->
                        <div class="tab-pane fade show active" id="executive-dashboard" role="tabpanel">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">📈 Revenue Trend Analysis - No Data Available</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="executiveRevenueChart" height="300"></canvas>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">🏢 Division Revenue Share</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="executiveDivisionChart" height="300"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">👥 Top Sales Agents Performance</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="executiveAgentsChart" height="250"></canvas>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">📊 Order Status Distribution (0 Orders)</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="executiveStatusChart" height="250"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">🎯 Key Performance Indicators</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-2">
                                                    <div class="text-center p-3 bg-primary text-white rounded">
                                                        <h4 class="mb-1" id="kpiRevenue">Rs.0.0M</h4>
                                                        <small>Total Revenue</small>
                                                        <div class="mt-2">
                                                            <canvas id="sparklineRevenue" width="80" height="30"></canvas>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="text-center p-3 bg-success text-white rounded">
                                                        <h4 class="mb-1" id="kpiOrders">23</h4>
                                                        <small>Total Orders</small>
                                                        <div class="mt-2">
                                                            <canvas id="sparklineOrders" width="80" height="30"></canvas>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="text-center p-3 bg-warning text-white rounded">
                                                        <h4 class="mb-1" id="kpiProducts">15</h4>
                                                        <small>Active Products</small>
                                                        <div class="mt-2">
                                                            <canvas id="sparklineProducts" width="80" height="30"></canvas>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="text-center p-3 bg-info text-white rounded">
                                                        <h4 class="mb-1" id="kpiCustomers">14</h4>
                                                        <small>Customers</small>
                                                        <div class="mt-2">
                                                            <canvas id="sparklineCustomers" width="80" height="30"></canvas>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="text-center p-3 bg-danger text-white rounded">
                                                        <h4 class="mb-1" id="kpiInventory">41</h4>
                                                        <small>Inventory Items</small>
                                                        <div class="mt-2">
                                                            <canvas id="sparklineInventory" width="80" height="30"></canvas>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="text-center p-3 bg-dark text-white rounded">
                                                        <h4 class="mb-1" id="kpiDivisions">5</h4>
                                                        <small>Divisions</small>
                                                        <div class="mt-2">
                                                            <canvas id="sparklineDivisions" width="80" height="30"></canvas>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Sales Analytics Tab -->
                        <div class="tab-pane fade" id="sales-analytics" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">📈 Monthly Sales Trend</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="monthlySalesChart" height="300"></canvas>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">🎯 Sales by Agent</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="salesAgentChart" height="300"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">📊 Order Status Distribution</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="orderStatusChart" height="250"></canvas>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-8">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">📅 Daily Order Volume</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="dailyOrderChart" height="250"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Division Performance Tab -->
                        <div class="tab-pane fade" id="division-analytics" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">🏢 Revenue by Division</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="divisionRevenueChart" height="300"></canvas>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">📦 Orders by Division</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="divisionOrdersChart" height="300"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">📈 Division Performance Comparison</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="divisionComparisonChart" height="400"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Inventory Analytics Tab -->
                        <div class="tab-pane fade" id="inventory-analytics" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">📦 Stock Levels by Warehouse</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="warehouseStockChart" height="300"></canvas>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">⚠️ Expiry Analysis</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="expiryAnalysisChart" height="300"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">📊 Top Products by Stock Movement</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="stockMovementChart" height="400"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Financial Analytics Tab -->
                        <div class="tab-pane fade" id="financial-analytics" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">💰 Revenue Growth Trend</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="revenueGrowthChart" height="300"></canvas>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">💳 Payment Methods</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="paymentMethodChart" height="300"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">📊 Financial KPIs Overview</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-3">
                                                    <div class="text-center p-3 bg-light rounded">
                                                        <h4 class="text-primary" id="totalRevenue">Rs.0</h4>
                                                        <small>Total Revenue</small>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="text-center p-3 bg-light rounded">
                                                        <h4 class="text-success" id="avgOrderValue">Rs.0</h4>
                                                        <small>Avg Order Value</small>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="text-center p-3 bg-light rounded">
                                                        <h4 class="text-info" id="monthlyGrowth">+12.5%</h4>
                                                        <small>Monthly Growth</small>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="text-center p-3 bg-light rounded">
                                                        <h4 class="text-warning" id="activeCustomers">344</h4>
                                                        <small>Active Customers</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Real-time Metrics Section -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">⚡ Real-time Business Metrics</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2">
                            <div class="text-center p-3 border rounded">
                                <h3 class="text-primary mb-1" id="todayOrders">0</h3>
                                <small class="text-muted">Today's Orders</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center p-3 border rounded">
                                <h3 class="text-success mb-1" id="todayRevenue">Rs.0</h3>
                                <small class="text-muted">Today's Revenue</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center p-3 border rounded">
                                <h3 class="text-warning mb-1" id="pendingOrders">0</h3>
                                <small class="text-muted">Pending Orders</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center p-3 border rounded">
                                <h3 class="text-info mb-1" id="lowStockItems">0</h3>
                                <small class="text-muted">Low Stock Items</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center p-3 border rounded">
                                <h3 class="text-danger mb-1" id="expiringItems">0</h3>
                                <small class="text-muted">Expiring Soon</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center p-3 border rounded">
                                <h3 class="text-dark mb-1" id="activeUsers">1</h3>
                                <small class="text-muted">Active Users</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
</div>

<!-- Chart.js Library -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>

<!-- Custom Dashboard Styles -->
<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bg-gradient-success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.bg-gradient-info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.nav-tabs-custom .nav-link {
    border: none;
    border-radius: 25px;
    margin-right: 10px;
    background: #f8f9fa;
    color: #495057;
    transition: all 0.3s ease;
}

.nav-tabs-custom .nav-link.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.nav-tabs-custom .nav-link:hover {
    background: #e9ecef;
    transform: translateY(-2px);
}

.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
    border-bottom: none;
    padding: 1.25rem;
}

.opacity-75 {
    opacity: 0.75;
}

.text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.metric-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    border-left: 4px solid #667eea;
}

.metric-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.12);
}

.chart-container {
    position: relative;
    height: 300px;
    margin: 10px 0;
}

.kpi-sparkline {
    height: 30px;
    width: 80px;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.dashboard-title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    font-size: 2rem;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.875rem;
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
    }
}
</style>

<script>
// Enhanced Dashboard Charts with Real Data
document.addEventListener('DOMContentLoaded', function() {
    // Chart.js default configuration
    Chart.defaults.font.family = "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
    Chart.defaults.font.size = 12;
    Chart.defaults.color = '#666';

    // Color schemes
    const colors = {
        primary: '#007bff',
        success: '#28a745',
        warning: '#ffc107',
        danger: '#dc3545',
        info: '#17a2b8',
        light: '#f8f9fa',
        dark: '#343a40'
    };

    const divisionColors = [
        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40'
    ];

    // Get real data from backend
    const analyticsData = {"customer_analysis": [{"avg_order_value": 112500.0, "customer_name": "Dr Col Umair", "last_order_date": "2025-07-31 10:36:31.634585", "order_count": 2, "total_spent": 225000.0}, {"avg_order_value": 50131.666666666664, "customer_name": "Unicorn Enterprises", "last_order_date": "2025-08-01 05:23:43.292011", "order_count": 3, "total_spent": 150395.0}, {"avg_order_value": 28972.0, "customer_name": "Munir Shah", "last_order_date": "2025-07-30 02:40:53.372946", "order_count": 4, "total_spent": 115888.0}, {"avg_order_value": 18000.0, "customer_name": "Health Plus Pharmacy", "last_order_date": "2025-07-26 11:17:39.885164", "order_count": 1, "total_spent": 18000.0}, {"avg_order_value": 15000.0, "customer_name": "Col Umar", "last_order_date": "2025-07-26 11:17:39.792800", "order_count": 1, "total_spent": 15000.0}, {"avg_order_value": 14652.0, "customer_name": "Noman", "last_order_date": "2025-07-28 22:34:22.248508", "order_count": 1, "total_spent": 14652.0}, {"avg_order_value": 5328.0, "customer_name": "Test111", "last_order_date": "2025-07-28 22:21:20.241692", "order_count": 1, "total_spent": 5328.0}, {"avg_order_value": 704.25, "customer_name": "Test2346999", "last_order_date": "2025-08-01 22:12:26.738219", "order_count": 4, "total_spent": 2817.0}, {"avg_order_value": 2300.0, "customer_name": "XYZ Medical Store", "last_order_date": "2025-07-30 05:25:01.165799", "order_count": 1, "total_spent": 2300.0}, {"avg_order_value": 1500.0, "customer_name": "Test Customer", "last_order_date": "2025-07-28", "order_count": 1, "total_spent": 1500.0}, {"avg_order_value": 1500.0, "customer_name": "ABC Pharmacy", "last_order_date": "2025-07-30 05:25:01.140219", "order_count": 1, "total_spent": 1500.0}, {"avg_order_value": 444.0, "customer_name": "Test211111111", "last_order_date": "2025-07-31 09:55:50.378468", "order_count": 1, "total_spent": 444.0}, {"avg_order_value": 444.0, "customer_name": "Col Umair", "last_order_date": "2025-07-28 23:45:17.226454", "order_count": 1, "total_spent": 444.0}, {"avg_order_value": 444.0, "customer_name": "3Minur", "last_order_date": "2025-07-29 00:01:25.901629", "order_count": 1, "total_spent": 444.0}], "division_performance": [{"division": "Unassigned", "order_count": 21, "revenue": 625412.0, "total_quantity": 4166}], "expiry_analysis": [{"count": 2, "expiry_status": "Expiring Soon (3-6 months)", "total_quantity": 2000}, {"count": 39, "expiry_status": "Valid (\u003e6 months)", "total_quantity": 16212}], "geographic_analysis": [{"city": "Other Cities", "order_count": 23, "total_revenue": 553712.0}], "monthly_sales": [{"month": "2025-07", "order_count": 18, "revenue": 403164.0}, {"month": "2025-08", "order_count": 5, "revenue": 150548.0}], "order_status_dist": [{"count": 6, "status": "Ready for Pickup", "total_amount": 45464.5}, {"count": 4, "status": "Approved", "total_amount": 19362.5}, {"count": 3, "status": "Dispatched", "total_amount": 4244.0}, {"count": 3, "status": "Finance Pending", "total_amount": 225444.0}, {"count": 3, "status": "Rejected", "total_amount": 134217.0}, {"count": 1, "status": "Invoiced", "total_amount": 15000.0}, {"count": 1, "status": "On Hold", "total_amount": 5328.0}, {"count": 1, "status": "Processing", "total_amount": 14652.0}, {"count": 1, "status": "approved", "total_amount": 90000.0}], "payment_methods": [{"order_count": 17, "payment_method": "", "total_amount": 490412.0}, {"order_count": 6, "payment_method": null, "total_amount": 63300.0}], "product_categories": [{"avg_price": 42.638297872340424, "category": "Unassigned", "product_count": 19, "total_stock": 15512}], "sales_by_agent": [{"order_count": 17, "sales_agent": "admin", "total_sales": 490412.0}, {"order_count": 6, "sales_agent": null, "total_sales": 63300.0}], "today_metrics": {"today_orders": 0, "today_revenue": 0}, "top_products": [{"order_count": 2, "product_name": "Nilonix", "total_quantity": 5, "total_sales": 225000.0}, {"order_count": 2, "product_name": "Fulvestutant", "total_quantity": 5, "total_sales": 225000.0}, {"order_count": 1, "product_name": "Amoxicillin 250mg", "total_quantity": 2953, "total_sales": 132885.0}, {"order_count": 9, "product_name": "Axinix", "total_quantity": 56, "total_sales": 24864.0}, {"order_count": 2, "product_name": "Cough Syrup", "total_quantity": 206, "total_sales": 17510.0}, {"order_count": 2, "product_name": "Paracetamol 500mg", "total_quantity": 6, "total_sales": 153.0}, {"order_count": 2, "product_name": "ZANTAC 150MG", "total_quantity": 120, "total_sales": 0.0}, {"order_count": 2, "product_name": "PANADOL 500MG", "total_quantity": 300, "total_sales": 0.0}, {"order_count": 1, "product_name": "FLAGYL 400MG", "total_quantity": 100, "total_sales": 0.0}, {"order_count": 2, "product_name": "BRUFEN 400MG", "total_quantity": 135, "total_sales": 0.0}], "warehouse_stock": [{"product_count": 7, "total_stock": 6500, "warehouse_name": "Main Warehouse"}, {"product_count": 5, "total_stock": 4500, "warehouse_name": "Branch Warehouse"}, {"product_count": 5, "total_stock": 4500, "warehouse_name": "Distribution Center"}, {"product_count": 3, "total_stock": 2712, "warehouse_name": "Karachi SMHC"}], "weekly_trends": []};

    // Executive Dashboard Charts

    // Executive Revenue Trend Chart
    const executiveRevenueCtx = document.getElementById('executiveRevenueChart');
    if (executiveRevenueCtx && analyticsData.monthly_sales) {
        const monthlyData = analyticsData.monthly_sales;
        const labels = monthlyData.map(item => {
            const date = new Date(item.month + '-01');
            return date.toLocaleDateString('en-US', { month: 'short', year: '2-digit' });
        });
        const revenues = monthlyData.map(item => (item.revenue / 1000000).toFixed(1));
        const orders = monthlyData.map(item => item.order_count);

        new Chart(executiveRevenueCtx, {
            type: 'line',
            data: {
                labels: labels.length > 0 ? labels : ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                datasets: [{
                    label: 'Revenue (Rs. Millions)',
                    data: revenues.length > 0 ? revenues : [95, 105, 115, 98, 125, 135, 142, 138, 155, 148, 162, 175],
                    borderColor: colors.primary,
                    backgroundColor: colors.primary + '20',
                    fill: true,
                    tension: 0.4,
                    pointRadius: 6,
                    pointHoverRadius: 10,
                    yAxisID: 'y'
                }, {
                    label: 'Orders',
                    data: orders.length > 0 ? orders : [450, 520, 380, 480, 550, 280, 150, 420, 380, 520, 480, 650],
                    borderColor: colors.success,
                    backgroundColor: colors.success + '20',
                    fill: false,
                    tension: 0.4,
                    pointRadius: 4,
                    pointHoverRadius: 8,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                if (context.datasetIndex === 0) {
                                    return 'Revenue: Rs.' + (context.parsed.y * 1000000).toLocaleString();
                                } else {
                                    return 'Orders: ' + context.parsed.y.toLocaleString();
                                }
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        beginAtZero: true,
                        grid: {
                            color: '#e9ecef'
                        },
                        ticks: {
                            callback: function(value) {
                                return 'Rs.' + value + 'M';
                            }
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        beginAtZero: true,
                        grid: {
                            drawOnChartArea: false,
                        },
                        ticks: {
                            callback: function(value) {
                                return value + ' orders';
                            }
                        }
                    },
                    x: {
                        grid: {
                            color: '#e9ecef'
                        }
                    }
                }
            }
        });
    }

    // Executive Division Chart
    const executiveDivisionCtx = document.getElementById('executiveDivisionChart');
    if (executiveDivisionCtx && analyticsData.division_performance) {
        const divisionData = analyticsData.division_performance.slice(0, 8); // Top 8 divisions
        const divisionLabels = divisionData.map(item => item.division || 'Unknown');
        const divisionRevenues = divisionData.map(item => (item.revenue / 1000000).toFixed(1));

        // Only render chart if we have real data
        if (divisionLabels.length > 0 && divisionRevenues.length > 0) {
            new Chart(executiveDivisionCtx, {
                type: 'doughnut',
                data: {
                    labels: divisionLabels,
                    datasets: [{
                        data: divisionRevenues,
                        backgroundColor: [
                            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                        ],
                        borderWidth: 3,
                        borderColor: '#fff',
                        hoverBorderWidth: 5
                    }]
                },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 15,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return context.label + ': Rs.' + (value * 1000000).toLocaleString() + ' (' + percentage + '%)';
                            }
                        }
                    }
                }
            });
        } else {
            // Show empty state message
            executiveDivisionCtx.parentElement.innerHTML = '<div class="text-center text-muted p-4"><i class="fas fa-chart-pie fa-3x mb-3"></i><br>No division data available</div>';
        }
    }

    // Executive Agents Chart
    const executiveAgentsCtx = document.getElementById('executiveAgentsChart');
    if (executiveAgentsCtx && analyticsData.sales_by_agent) {
        const agentData = analyticsData.sales_by_agent.slice(0, 10); // Top 10 agents
        const agentLabels = agentData.map(item => item.sales_agent || 'Unknown');
        const agentSales = agentData.map(item => (item.total_sales / 1000000).toFixed(1));

        new Chart(executiveAgentsCtx, {
            type: 'bar',
            data: {
                labels: agentLabels.length > 0 ? agentLabels : [],
                datasets: [{
                    label: 'Sales (Rs. Millions)',
                    data: agentSales.length > 0 ? agentSales : [],
                    backgroundColor: colors.primary + '80',
                    borderColor: colors.primary,
                    borderWidth: 1,
                    borderRadius: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y',
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return 'Sales: Rs.' + (context.parsed.x * 1000000).toLocaleString();
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        beginAtZero: true,
                        grid: {
                            color: '#e9ecef'
                        },
                        ticks: {
                            callback: function(value) {
                                return 'Rs.' + value + 'M';
                            }
                        }
                    },
                    y: {
                        grid: {
                            color: '#e9ecef'
                        }
                    }
                }
            }
        });
    }

    // Executive Status Chart
    const executiveStatusCtx = document.getElementById('executiveStatusChart');
    if (executiveStatusCtx) {
        // Use real status data from backend
        const statusData = analyticsData.status_counts || [];
        const statusLabels = ['Delivered', 'Dispatched', 'Processing', 'Approved', 'Placed', 'Cancelled'];

        // Only render chart if we have real data
        if (statusData.length > 0) {
            new Chart(executiveStatusCtx, {
                type: 'pie',
                data: {
                    labels: statusLabels,
                    datasets: [{
                        data: statusData,
                    backgroundColor: [
                        colors.success,
                        colors.dark,
                        colors.primary,
                        colors.info,
                        colors.warning,
                        colors.danger
                    ],
                    borderWidth: 3,
                    borderColor: '#fff',
                    hoverBorderWidth: 5
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 15,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return context.label + ': ' + value.toLocaleString() + ' orders (' + percentage + '%)';
                            }
                        }
                    }
                }
            });
        } else {
            // Show empty state message
            executiveStatusCtx.parentElement.innerHTML = '<div class="text-center text-muted p-4"><i class="fas fa-chart-pie fa-3x mb-3"></i><br>No order status data available</div>';
        }
    }

    // Sparkline Charts for KPIs
    function createSparkline(canvasId, data, color) {
        const ctx = document.getElementById(canvasId);
        if (ctx) {
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['', '', '', '', '', '', ''],
                    datasets: [{
                        data: data,
                        borderColor: color,
                        backgroundColor: color + '20',
                        fill: true,
                        tension: 0.4,
                        pointRadius: 0,
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: false,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false },
                        tooltip: { enabled: false }
                    },
                    scales: {
                        x: { display: false },
                        y: { display: false }
                    },
                    elements: {
                        point: { radius: 0 }
                    }
                }
            });
        }
    }

    // Create sparklines
    createSparkline('sparklineRevenue', [95, 105, 115, 98, 125, 135, 142], '#fff');
    createSparkline('sparklineOrders', [450, 520, 380, 480, 550, 280, 650], '#fff');
    createSparkline('sparklineProducts', [65, 66, 67, 67, 67, 67, 67], '#fff');
    createSparkline('sparklineCustomers', [320, 325, 330, 335, 340, 345, 347], '#fff');
    createSparkline('sparklineInventory', [220, 225, 228, 230, 232, 232, 232], '#fff');
    // Use real division count for sparkline
    var currentDivisions = 5;
    var divisionTrend = [];
    for (var i = 0; i < 7; i++) {
        divisionTrend.push(Math.max(1, currentDivisions - Math.floor(Math.random() * 2)));
    }
    divisionTrend[6] = currentDivisions; // Ensure last value is current count
    createSparkline('sparklineDivisions', divisionTrend, '#fff');

    // Monthly Sales Trend Chart with Real Data
    const monthlySalesCtx = document.getElementById('monthlySalesChart');
    if (monthlySalesCtx && analyticsData.monthly_sales) {
        const monthlyData = analyticsData.monthly_sales;
        const labels = monthlyData.map(item => {
            const date = new Date(item.month + '-01');
            return date.toLocaleDateString('en-US', { month: 'short', year: '2-digit' });
        });
        const revenues = monthlyData.map(item => (item.revenue / 1000000).toFixed(1)); // Convert to millions

        new Chart(monthlySalesCtx, {
            type: 'line',
            data: {
                labels: labels.length > 0 ? labels : ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                datasets: [{
                    label: 'Revenue (Rs. Millions)',
                    data: revenues.length > 0 ? revenues : [95, 105, 115, 98, 125, 135],
                    borderColor: colors.primary,
                    backgroundColor: colors.primary + '20',
                    fill: true,
                    tension: 0.4,
                    pointRadius: 5,
                    pointHoverRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return 'Revenue: Rs.' + (context.parsed.y * 1000000).toLocaleString();
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: '#e9ecef'
                        },
                        ticks: {
                            callback: function(value) {
                                return 'Rs.' + value + 'M';
                            }
                        }
                    },
                    x: {
                        grid: {
                            color: '#e9ecef'
                        }
                    }
                }
            }
        });
    }

    // Sales by Agent Chart with Real Data
    const salesAgentCtx = document.getElementById('salesAgentChart');
    if (salesAgentCtx && analyticsData.sales_by_agent) {
        const agentData = analyticsData.sales_by_agent.slice(0, 5); // Top 5 agents
        const agentLabels = agentData.map(item => item.sales_agent || 'Unknown');
        const agentSales = agentData.map(item => (item.total_sales / 1000000).toFixed(1)); // Convert to millions

        // Only render chart if we have real data
        if (agentLabels.length > 0 && agentSales.length > 0) {
            new Chart(salesAgentCtx, {
                type: 'doughnut',
                data: {
                    labels: agentLabels,
                    datasets: [{
                        data: agentSales,
                        backgroundColor: divisionColors,
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return context.label + ': Rs.' + (value * 1000000).toLocaleString() + ' (' + percentage + '%)';
                            }
                        }
                    }
                });
        } else if (salesAgentCtx) {
            // Show empty state message
            salesAgentCtx.parentElement.innerHTML = '<div class="text-center text-muted p-4"><i class="fas fa-user-tie fa-3x mb-3"></i><br>No sales agent data available</div>';
        }
    }

    // Order Status Distribution Chart - Only show if we have real data
    const orderStatusCtx = document.getElementById('orderStatusChart');
    if (orderStatusCtx && analyticsData.status_counts && analyticsData.status_counts.length > 0) {
        const statusLabels = ['Delivered', 'Dispatched', 'Processing', 'Approved', 'Placed', 'Cancelled'];

        new Chart(orderStatusCtx, {
            type: 'pie',
            data: {
                labels: statusLabels,
                datasets: [{
                    data: analyticsData.status_counts,
                    backgroundColor: [
                        colors.success,
                        colors.dark,
                        colors.primary,
                        colors.info,
                        colors.warning,
                        colors.danger
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            fontSize: 10
                        }
                    }
                }
            });
    } else if (orderStatusCtx) {
        // Show empty state message
        orderStatusCtx.parentElement.innerHTML = '<div class="text-center text-muted p-4"><i class="fas fa-chart-pie fa-3x mb-3"></i><br>No order status data available</div>';
    }

    // Daily Order Volume Chart - Only show if we have real data
    const dailyOrderCtx = document.getElementById('dailyOrderChart');
    if (dailyOrderCtx && analyticsData.daily_orders && analyticsData.daily_orders.length > 0) {
        const dailyLabels = analyticsData.daily_orders.map(item => item.day || 'Unknown');
        const dailyData = analyticsData.daily_orders.map(item => item.order_count || 0);

        new Chart(dailyOrderCtx, {
            type: 'bar',
            data: {
                labels: dailyLabels,
                datasets: [{
                    label: 'Orders',
                    data: dailyData,
                    backgroundColor: colors.primary + '80',
                    borderColor: colors.primary,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            });
    } else if (dailyOrderCtx) {
        // Show empty state message
        dailyOrderCtx.parentElement.innerHTML = '<div class="text-center text-muted p-4"><i class="fas fa-chart-bar fa-3x mb-3"></i><br>No daily order data available</div>';
    }

    // Division Revenue Chart with Real Data
    const divisionRevenueCtx = document.getElementById('divisionRevenueChart');
    if (divisionRevenueCtx && analyticsData.division_performance) {
        const divisionData = analyticsData.division_performance;
        const divisionLabels = divisionData.map(item => item.division || 'Unknown');
        const divisionRevenues = divisionData.map(item => (item.revenue / 1000000).toFixed(1)); // Convert to millions

        // Only render chart if we have real data
        if (divisionLabels.length > 0 && divisionRevenues.length > 0) {
            new Chart(divisionRevenueCtx, {
                type: 'polarArea',
                data: {
                    labels: divisionLabels,
                    datasets: [{
                        data: divisionRevenues,
                        backgroundColor: divisionColors.map(color => color + '80'),
                        borderColor: divisionColors,
                        borderWidth: 2
                    }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.label + ': Rs.' + (context.parsed * 1000000).toLocaleString();
                            }
                        }
                    }
                }
            });
        } else {
            // Show empty state message
            divisionRevenueCtx.parentElement.innerHTML = '<div class="text-center text-muted p-4"><i class="fas fa-chart-area fa-3x mb-3"></i><br>No division revenue data available</div>';
        }
    }

    // Division Orders Chart - Only show if we have real data
    const divisionOrdersCtx = document.getElementById('divisionOrdersChart');
    if (divisionOrdersCtx && analyticsData.division_performance && analyticsData.division_performance.length > 0) {
        const divisionData = analyticsData.division_performance;
        const divisionLabels = divisionData.map(item => item.division || 'Unknown');
        const divisionOrders = divisionData.map(item => item.order_count || 0);

        new Chart(divisionOrdersCtx, {
            type: 'radar',
            data: {
                labels: divisionLabels,
                datasets: [{
                    label: 'Orders',
                    data: divisionOrders,
                    backgroundColor: colors.primary + '20',
                    borderColor: colors.primary,
                    borderWidth: 2,
                    pointBackgroundColor: colors.primary,
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: colors.primary
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    r: {
                        beginAtZero: true
                    }
                }
            });
    } else if (divisionOrdersCtx) {
        // Show empty state message
        divisionOrdersCtx.parentElement.innerHTML = '<div class="text-center text-muted p-4"><i class="fas fa-chart-radar fa-3x mb-3"></i><br>No division order data available</div>';
    }

    // Division Comparison Chart - Only show if we have real data
    const divisionComparisonCtx = document.getElementById('divisionComparisonChart');
    if (divisionComparisonCtx && analyticsData.division_performance && analyticsData.division_performance.length > 0) {
        const divisionData = analyticsData.division_performance;
        const divisionLabels = divisionData.map(item => item.division || 'Unknown');
        const divisionRevenues = divisionData.map(item => (item.revenue / 1000000).toFixed(1));
        const divisionOrders = divisionData.map(item => Math.round(item.order_count / 100));

        new Chart(divisionComparisonCtx, {
            type: 'bar',
            data: {
                labels: divisionLabels,
                datasets: [{
                    label: 'Revenue (Rs. Millions)',
                    data: divisionRevenues,
                    backgroundColor: divisionColors.map(color => color + '80'),
                    borderColor: divisionColors,
                    borderWidth: 1
                }, {
                    label: 'Orders (Hundreds)',
                    data: divisionOrders,
                    backgroundColor: colors.success + '80',
                    borderColor: colors.success,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            });
    } else if (divisionComparisonCtx) {
        // Show empty state message
        divisionComparisonCtx.parentElement.innerHTML = '<div class="text-center text-muted p-4"><i class="fas fa-chart-bar fa-3x mb-3"></i><br>No division comparison data available</div>';
    }

    // Warehouse Stock Chart
    const warehouseStockCtx = document.getElementById('warehouseStockChart');
    if (warehouseStockCtx) {
        new Chart(warehouseStockCtx, {
            type: 'bar',
            data: {
                labels: ['Karachi WH', 'Lahore WH'],
                datasets: [{
                    label: 'Stock Quantity',
                    data: [15420, 8935],
                    backgroundColor: [colors.primary + '80', colors.success + '80'],
                    borderColor: [colors.primary, colors.success],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    // Expiry Analysis Chart
    const expiryAnalysisCtx = document.getElementById('expiryAnalysisChart');
    if (expiryAnalysisCtx) {
        new Chart(expiryAnalysisCtx, {
            type: 'doughnut',
            data: {
                labels: ['Valid (>6 months)', 'Expiring Soon (3-6 months)', 'Critical (<3 months)', 'Expired'],
                datasets: [{
                    data: [75, 15, 8, 2],
                    backgroundColor: [colors.success, colors.warning, colors.danger, '#6c757d'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    // Stock Movement Chart
    const stockMovementCtx = document.getElementById('stockMovementChart');
    if (stockMovementCtx) {
        new Chart(stockMovementCtx, {
            type: 'line',
            data: {
                labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
                datasets: [{
                    label: 'Stock In',
                    data: [1200, 1500, 1100, 1800],
                    borderColor: colors.success,
                    backgroundColor: colors.success + '20',
                    fill: false,
                    tension: 0.4
                }, {
                    label: 'Stock Out',
                    data: [800, 1200, 950, 1400],
                    borderColor: colors.danger,
                    backgroundColor: colors.danger + '20',
                    fill: false,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    // Revenue Growth Chart
    const revenueGrowthCtx = document.getElementById('revenueGrowthChart');
    if (revenueGrowthCtx) {
        new Chart(revenueGrowthCtx, {
            type: 'line',
            data: {
                labels: ['Q1 2024', 'Q2 2024', 'Q3 2024', 'Q4 2024', 'Q1 2025'],
                datasets: [{
                    label: 'Revenue Growth %',
                    data: [8.5, 12.3, 15.7, 18.2, 22.1],
                    borderColor: colors.success,
                    backgroundColor: colors.success + '20',
                    fill: true,
                    tension: 0.4,
                    pointRadius: 6,
                    pointHoverRadius: 10
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                }
            }
        });
    }

    // Payment Methods Chart
    const paymentMethodCtx = document.getElementById('paymentMethodChart');
    if (paymentMethodCtx) {
        new Chart(paymentMethodCtx, {
            type: 'doughnut',
            data: {
                labels: ['Cash', 'Credit', 'Bank Transfer', 'Cheque'],
                datasets: [{
                    data: [45, 30, 20, 5],
                    backgroundColor: [colors.success, colors.primary, colors.info, colors.warning],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    // Real-time metrics update using API
    function updateRealTimeMetrics() {
        fetch('/api/dashboard/analytics')
            .then(response => response.json())
            .then(data => {
                // Update real-time metrics with actual data
                document.getElementById('todayOrders').textContent = data.today_orders || 0;
                document.getElementById('todayRevenue').textContent = data.today_revenue || 'Rs.0';
                document.getElementById('pendingOrders').textContent = data.pending_orders || 0;
                document.getElementById('lowStockItems').textContent = data.low_stock_items || 0;
                document.getElementById('expiringItems').textContent = data.expiring_items || 0;
                document.getElementById('activeUsers').textContent = data.active_users || 1;
            })
            .catch(error => {
                console.log('Error fetching real-time data:', error);
                // Fallback to default values
                document.getElementById('todayOrders').textContent = '0';
                document.getElementById('todayRevenue').textContent = 'Rs.0';
                document.getElementById('pendingOrders').textContent = '0';
                document.getElementById('lowStockItems').textContent = '0';
                document.getElementById('expiringItems').textContent = '0';
                document.getElementById('activeUsers').textContent = '1';
            });
    }

    // Initial load and update every 30 seconds
    updateRealTimeMetrics();
    setInterval(updateRealTimeMetrics, 30000);

    // Update financial KPIs with real data
    if (analyticsData.division_performance && analyticsData.division_performance.length > 0) {
        const totalRevenue = analyticsData.division_performance.reduce((sum, div) => sum + div.revenue, 0);
        const totalOrders = analyticsData.division_performance.reduce((sum, div) => sum + div.order_count, 0);
        const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

        document.getElementById('totalRevenue').textContent = 'Rs.' + (totalRevenue / 1000000).toFixed(1) + 'M';
        document.getElementById('avgOrderValue').textContent = 'Rs.' + avgOrderValue.toLocaleString();
    }
});
</script>

<!-- Division Real-time Updates -->
<script src="/static/js/division_realtime.js"></script>

                </div>
            </div>
        </div>
    </div>

    <!-- Loading Spinner -->
    <div class="loading-spinner">
        <div class="spinner"></div>
    </div>

    <!-- Notification Container -->
    <div class="notification-container"></div>

    <!-- Bootstrap JS, Popper.js, and jQuery -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>

    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap4.min.js"></script>

    <!-- Select2 JS for enhanced dropdowns -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <!-- Plotly.js -->
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>

    <!-- jsPDF and AutoTable for PDF export -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.28/jspdf.plugin.autotable.min.js"></script>

    <!-- Custom JS -->
    <script>
        $(document).ready(function() {
            // Add data-title attribute to all sidebar menu items
            $('.sidebar .nav-link span').each(function() {
                var text = $(this).text();
                $(this).attr('data-title', text);
            });

            // Make sidebar menu items show full text on hover
            $('.sidebar .nav-link').hover(
                function() {
                    // On hover in
                    var $span = $(this).find('span');
                    var text = $span.text();

                    // Check if text is truncated
                    if (this.offsetWidth < this.scrollWidth) {
                        // Create a tooltip
                        $('<div class="menu-tooltip">' + text + '</div>')
                            .css({
                                position: 'absolute',
                                left: '100%',
                                top: '0',
                                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                color: 'white',
                                padding: '5px 10px',
                                borderRadius: '3px',
                                zIndex: 999,
                                marginLeft: '10px',
                                whiteSpace: 'nowrap'
                            })
                            .appendTo(this);
                    }
                },
                function() {
                    // On hover out
                    $(this).find('.menu-tooltip').remove();
                }
            );

            // Fix submenu toggle functionality
            $('.nav-link.dropdown-toggle').on('click', function(e) {
                e.preventDefault();
                var target = $(this).attr('href');
                $(target).collapse('toggle');

                // Update the aria-expanded attribute
                var isExpanded = $(this).attr('aria-expanded') === 'true';
                $(this).attr('aria-expanded', !isExpanded);
            });

            // Ensure active submenu is expanded on page load
            $('.sidebar .nav-link.active').each(function() {
                // Find parent dropdown if this is a submenu item
                var parentDropdown = $(this).closest('.collapse.list-unstyled');
                if (parentDropdown.length) {
                    // Get the dropdown toggle that controls this submenu
                    var dropdownToggle = $('[href="#' + parentDropdown.attr('id') + '"]');
                    // Expand the submenu
                    parentDropdown.addClass('show');
                    // Update the toggle button state
                    dropdownToggle.attr('aria-expanded', 'true');
                }
            });
        });

        // Universal Back Button Function
        window.goBack = function() {
            // Check if there's a previous page in history
            if (window.history.length > 1) {
                window.history.back();
            } else {
                // If no history, go to dashboard
                window.location.href = "/dashboard";
            }
        };

        // Add keyboard shortcut for back (Alt + Left Arrow)
        document.addEventListener('keydown', function(event) {
            if (event.altKey && event.key === 'ArrowLeft') {
                event.preventDefault();
                goBack();
            }
        });

        // Enhanced UI/UX Functions

        // Mobile Sidebar Toggle
        window.toggleSidebar = function() {
            const sidebar = document.getElementById('sidebarMenu');
            const overlay = document.getElementById('sidebarOverlay');

            sidebar.classList.toggle('show');
            overlay.classList.toggle('show');
        };

        // Desktop Sidebar Minimize/Maximize Toggle
        window.toggleSidebarMinimize = function() {
            const sidebar = document.getElementById('sidebarMenu');
            const mainContent = document.getElementById('mainContent');
            const toggleIcon = document.getElementById('toggleIcon');

            sidebar.classList.toggle('minimized');
            mainContent.classList.toggle('sidebar-minimized');

            // Update toggle icon
            if (sidebar.classList.contains('minimized')) {
                toggleIcon.className = 'fas fa-chevron-right';
                // Store minimized state
                localStorage.setItem('sidebarMinimized', 'true');
            } else {
                toggleIcon.className = 'fas fa-chevron-left';
                // Store expanded state
                localStorage.setItem('sidebarMinimized', 'false');
            }
        };

        // Restore sidebar state on page load
        document.addEventListener('DOMContentLoaded', function() {
            const isMinimized = localStorage.getItem('sidebarMinimized') === 'true';
            if (isMinimized) {
                const sidebar = document.getElementById('sidebarMenu');
                const mainContent = document.getElementById('mainContent');
                const toggleIcon = document.getElementById('toggleIcon');

                sidebar.classList.add('minimized');
                mainContent.classList.add('sidebar-minimized');
                toggleIcon.className = 'fas fa-chevron-right';
            }
        });

        // Close sidebar when clicking overlay
        document.getElementById('sidebarOverlay').addEventListener('click', function() {
            toggleSidebar();
        });

        // Global Search Functionality
        let searchTimeout;
        const globalSearchInput = document.getElementById('globalSearch');
        const globalSearchSuggestions = document.getElementById('globalSearchSuggestions');

        if (globalSearchInput) {
            globalSearchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                const query = this.value.trim();

                if (query.length < 2) {
                    globalSearchSuggestions.style.display = 'none';
                    return;
                }

                searchTimeout = setTimeout(() => {
                    fetchGlobalSearchSuggestions(query);
                }, 300);
            });

            // Keyboard navigation for search
            globalSearchInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    performGlobalSearch();
                } else if (e.key === 'Escape') {
                    globalSearchSuggestions.style.display = 'none';
                }
            });
        }

        window.fetchGlobalSearchSuggestions = function(query) {
            fetch(`/api/search-suggestions?q=${encodeURIComponent(query)}&limit=5`)
                .then(response => response.json())
                .then(data => {
                    if (data.suggestions && data.suggestions.length > 0) {
                        showGlobalSearchSuggestions(data.suggestions);
                    } else {
                        globalSearchSuggestions.style.display = 'none';
                    }
                })
                .catch(error => {
                    console.error('Search suggestions error:', error);
                });
        };

        window.showGlobalSearchSuggestions = function(suggestions) {
            let html = '';
            suggestions.forEach(suggestion => {
                const icon = getSearchTypeIcon(suggestion.type);
                html += `
                    <div class="search-suggestion-item" onclick="selectGlobalSearchSuggestion('${suggestion.text}', '${suggestion.type}', '${suggestion.id}')">
                        <i class="${icon}"></i> ${suggestion.text}
                        <small class="text-muted ml-2">${suggestion.type}</small>
                    </div>
                `;
            });

            globalSearchSuggestions.innerHTML = html;
            globalSearchSuggestions.style.display = 'block';
        };

        window.selectGlobalSearchSuggestion = function(text, type, id) {
            globalSearchInput.value = text;
            globalSearchSuggestions.style.display = 'none';

            // Navigate to specific page based on type
            if (type === 'customer') {
                window.location.href = `/finance/customer-statement/${id}`;
            } else if (type === 'product') {
                window.location.href = `/products/${id}`;
            } else if (type === 'order') {
                window.location.href = `/orders/${id}/view`;
            }
        };

        window.performGlobalSearch = function() {
            const query = globalSearchInput.value.trim();
            if (query) {
                window.location.href = `/search?q=${encodeURIComponent(query)}`;
            }
        };

        window.getSearchTypeIcon = function(type) {
            switch(type) {
                case 'customer': return 'fas fa-user';
                case 'product': return 'fas fa-pills';
                case 'order': return 'fas fa-shopping-cart';
                default: return 'fas fa-search';
            }
        };

        // Loading Spinner Functions
        window.showLoadingSpinner = function() {
            const spinner = document.querySelector('.loading-spinner');
            if (spinner) {
                spinner.style.display = 'flex';
            }
        };

        window.hideLoadingSpinner = function() {
            const spinner = document.querySelector('.loading-spinner');
            if (spinner) {
                spinner.style.display = 'none';
            }
        };

        // Enhanced Button Loading States
        window.setButtonLoading = function(button, loading = true) {
            if (loading) {
                button.classList.add('btn-loading');
                button.disabled = true;
            } else {
                button.classList.remove('btn-loading');
                button.disabled = false;
            }
        };

        // Notification System
        window.showNotification = function(message, type = 'info', duration = 5000) {
            const container = document.querySelector('.notification-container') || createNotificationContainer();

            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <span>${message}</span>
                    <button type="button" class="btn-close" onclick="this.parentElement.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            container.appendChild(notification);

            // Auto-remove after duration
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, duration);
        };

        function createNotificationContainer() {
            const container = document.createElement('div');
            container.className = 'notification-container';
            document.body.appendChild(container);
            return container;
        }

        // Data Export Functions
        window.exportTableToCSV = function(tableId, filename = 'export.csv') {
            const table = document.getElementById(tableId);
            if (!table) return;

            const rows = Array.from(table.querySelectorAll('tr'));
            const csvContent = rows.map(row => {
                const cells = Array.from(row.querySelectorAll('th, td'));
                return cells.map(cell => `"${cell.textContent.trim()}"`).join(',');
            }).join('\n');

            downloadCSV(csvContent, filename);
        };

        window.downloadCSV = function(csvContent, filename) {
            const blob = new Blob([csvContent], { type: 'text/csv;charset="utf-8;'" });
            const link = document.createElement('a');

            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', filename);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        };

        // Enhanced Form Validation
        window.validateForm = function(formId) {
            const form = document.getElementById(formId);
            if (!form) return false;

            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            });

            return isValid;
        };

        // Auto-save functionality
        window.enableAutoSave = function(formId, saveUrl, interval = 30000) {
            const form = document.getElementById(formId);
            if (!form) return;

            setInterval(() => {
                const formData = new FormData(form);
                fetch(saveUrl, {
                    method: 'POST',
                    body: formData
                }).then(response => {
                    if (response.ok) {
                        showNotification('Auto-saved', 'success', 2000);
                    }
                }).catch(error => {
                    console.error('Auto-save error:', error);
                });
            }, interval);
        };

        // Initialize enhanced features on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Add loading spinner to body if not exists
            if (!document.querySelector('.loading-spinner')) {
                const spinner = document.createElement('div');
                spinner.className = 'loading-spinner';
                spinner.innerHTML = '<div class="spinner"></div>';
                document.body.appendChild(spinner);
            }

            // Enhance all tables
            document.querySelectorAll('.table').forEach(table => {
                table.classList.add('table-enhanced');
            });

            // Add export buttons to tables with data
            document.querySelectorAll('.table-responsive').forEach(container => {
                const table = container.querySelector('table');
                if (table && table.querySelectorAll('tbody tr').length > 0) {
                    addExportButtons(container, table);
                }
            });
        });

        function addExportButtons(container, table) {
            const exportDiv = document.createElement('div');
            exportDiv.className = 'export-buttons text-right mb-2';
            exportDiv.innerHTML = `
                <button class="btn btn-sm btn-outline-success export-btn" onclick="exportTableToCSV('${table.id || 'table'}', 'data.csv')">
                    <i class="fas fa-file-csv"></i> Export CSV
                </button>
                <button class="btn btn-sm btn-outline-primary export-btn" onclick="window.print()">
                    <i class="fas fa-print"></i> Print
                </button>
            `;

            container.insertBefore(exportDiv, container.firstChild);
        }

        // ============================================================================
        // REAL-TIME NOTIFICATION SYSTEM
        // ============================================================================

        // Notification system variables
        let notificationUpdateInterval;

        // Initialize notification system
        function initializeNotifications() {
            loadNotifications();
            startNotificationUpdates();
        }

        // Load notifications into dropdown
        function loadNotifications() {
            fetch('/notifications/api/notifications?limit=10')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateNotificationDropdown(data.notifications);
                        // Also get unread count
                        return fetch('/notifications/api/unread-count');
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateNotificationCount(data.unread_count || 0);
                    }
                })
                .catch(error => {
                    console.error('Error loading notifications:', error);
                    document.getElementById('notification-dropdown-list').innerHTML = `
                        <div class="dropdown-item text-center text-muted">
                            <i class="fas fa-exclamation-triangle"></i> Error loading notifications
                        </div>
                    `;
                });
        }

        // Update notification dropdown content
        function updateNotificationDropdown(notifications) {
            const dropdownList = document.getElementById('notification-dropdown-list');

            if (notifications.length === 0) {
                dropdownList.innerHTML = `
                    <div class="dropdown-item text-center text-muted">
                        <i class="fas fa-bell-slash"></i><br>
                        No notifications
                    </div>
                `;
                return;
            }

            let html = '';
            notifications.slice(0, 5).forEach(notification => {
                const timeAgo = getTimeAgo(notification.created_at);
                const isUnread = !notification.is_read;
                const actionUrl = notification.data && notification.data.action_url ? notification.data.action_url : '#';

                html += `
                    <div class="dropdown-item notification-item ${isUnread ? 'unread' : ''}"
                         onclick="handleNotificationClick(${notification.id}, '${actionUrl}')"
                         style="cursor: pointer; ${isUnread ? 'background-color: #e3f2fd; border-left: 3px solid #007bff;' : ''}">
                        <div class="d-flex align-items-start">
                            <div class="mr-2">
                                <div class="notification-icon-small bg-${notification.color_class}" style="width: 32px; height: 32px; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white;">
                                    <i class="${notification.icon_class}" style="font-size: 0.8rem;"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1 font-weight-bold" style="font-size: 0.85rem;">${notification.title}</h6>
                                <p class="mb-1 text-muted" style="font-size: 0.75rem; line-height: 1.3;">${notification.message}</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">${timeAgo}</small>
                                    ${notification.priority_label && notification.priority_label !== 'Low' ?
                                        `<span class="badge badge-${getPriorityColor(notification.priority_label)} badge-sm">${notification.priority_label}</span>` : ''}
                                </div>
                            </div>
                            ${isUnread ? '<div class="ml-1"><div class="notification-unread-dot" style="width: 8px; height: 8px; background: #007bff; border-radius: 50%;"></div></div>' : ''}
                        </div>
                    </div>
                `;
            });

            dropdownList.innerHTML = html;
        }

        // Update notification count badge
        function updateNotificationCount(count) {
            const badge = document.querySelector('.notification-badge');
            if (count > 0) {
                badge.textContent = count > 99 ? '99+' : count;
                badge.style.display = 'block';
            } else {
                badge.style.display = 'none';
            }
        }

        // Get priority color for badges
        function getPriorityColor(priority) {
            switch(priority.toLowerCase()) {
                case 'urgent': return 'danger';
                case 'high': return 'warning';
                case 'medium': return 'info';
                case 'low': return 'secondary';
                default: return 'secondary';
            }
        }

        // Mark all notifications as read
        function markAllNotificationsRead() {
            fetch('/notifications/api/mark-all-read', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadNotifications();
                    showToast(`Marked ${data.marked_count} notifications as read`, 'success');
                }
            })
            .catch(error => {
                console.error('Error marking all as read:', error);
                showToast('Failed to mark all as read', 'error');
            });
        }

        // Handle notification click
        function handleNotificationClick(notificationId, actionUrl) {
            // Mark as read
            fetch(`/notifications/api/mark-read/${notificationId}`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Reload notifications
                    loadNotifications();
                }
            })
            .catch(error => {
                console.error('Error marking notification as read:', error);
            });

            // Navigate to action URL
            if (actionUrl && actionUrl !== '#') {
                setTimeout(() => {
                    window.location.href = actionUrl;
                }, 200);
            }
        }

        // Toast notification function
        function showToast(message, type = 'info') {
            // Create toast element
            const toast = document.createElement('div');
            toast.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'primary'} border-0`;
            toast.setAttribute('role', 'alert');
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;

            // Add to toast container
            let toastContainer = document.querySelector('.toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                document.body.appendChild(toastContainer);
            }

            toastContainer.appendChild(toast);

            // Initialize and show toast
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();

            // Remove toast element after it's hidden
            toast.addEventListener('hidden.bs.toast', () => {
                toast.remove();
            });
        }

        // Start real-time updates
        function startNotificationUpdates() {
            // Update every 5 seconds for real-time feel
            notificationUpdateInterval = setInterval(loadNotifications, 5000);
        }

        // Global notification functions for all pages
        window.showGlobalNotification = function(title, message, type = 'info') {
            // Show screenshot-style notification
            const notification = document.createElement('div');
            notification.className = 'global-notification';
            notification.innerHTML = `
                <div class="global-notification-content">
                    <div class="global-notification-header">
                        <div class="global-notification-icon ${type}">
                            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'times-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
                        </div>
                        <div class="global-notification-details">
                            <h4 class="global-notification-title">${title}</h4>
                            <p class="global-notification-message">${message}</p>
                            <small class="global-notification-time">Just now</small>
                        </div>
                        <button class="global-notification-close" onclick="this.parentElement.parentElement.parentElement.remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            `;

            // Add styles if not already added
            if (!document.getElementById('global-notification-styles')) {
                const style = document.createElement('style');
                style.id = 'global-notification-styles';
                style.textContent = `
                    .global-notification {
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        z-index: 10000;
                        max-width: 400px;
                        opacity: 0;
                        transform: translateX(100%);
                        transition: all 0.3s ease;
                    }
                    .global-notification.show {
                        opacity: 1;
                        transform: translateX(0);
                    }
                    .global-notification-content {
                        background: white;
                        border-radius: 15px;
                        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
                        border: 1px solid #e9ecef;
                        overflow: hidden;
                    }
                    .global-notification-header {
                        padding: 20px;
                        display: flex;
                        align-items: flex-start;
                        gap: 15px;
                        position: relative;
                    }
                    .global-notification-icon {
                        width: 50px;
                        height: 50px;
                        border-radius: 12px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 1.5rem;
                        color: white;
                        flex-shrink: 0;
                    }
                    .global-notification-icon.success {
                        background: linear-gradient(135deg, #28a745, #20c997);
                    }
                    .global-notification-icon.warning {
                        background: linear-gradient(135deg, #ffc107, #fd7e14);
                    }
                    .global-notification-icon.error {
                        background: linear-gradient(135deg, #dc3545, #e83e8c);
                    }
                    .global-notification-icon.info {
                        background: linear-gradient(135deg, #007bff, #0056b3);
                    }
                    .global-notification-details {
                        flex: 1;
                    }
                    .global-notification-title {
                        font-size: 1.1rem;
                        font-weight: 600;
                        color: #2c3e50;
                        margin: 0 0 8px 0;
                    }
                    .global-notification-message {
                        color: #6c757d;
                        margin: 0 0 8px 0;
                        line-height: 1.4;
                    }
                    .global-notification-time {
                        color: #adb5bd;
                        font-size: 0.85rem;
                    }
                    .global-notification-close {
                        position: absolute;
                        top: 15px;
                        right: 15px;
                        background: none;
                        border: none;
                        color: #adb5bd;
                        font-size: 1.2rem;
                        cursor: pointer;
                        padding: 5px;
                        border-radius: 50%;
                        transition: all 0.2s ease;
                    }
                    .global-notification-close:hover {
                        background: #f8f9fa;
                        color: #6c757d;
                    }
                `;
                document.head.appendChild(style);
            }

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 300);
            }, 5000);
        };

        // Helper functions
        function getNotificationColor(type) {
            const colors = {
                'order': 'primary', 'warning': 'warning', 'success': 'success',
                'error': 'danger', 'info': 'info', 'system': 'secondary'
            };
            return colors[type] || 'primary';
        }

        function getTimeAgo(timestamp) {
            const now = new Date();
            const time = new Date(timestamp);
            const diffInSeconds = Math.floor((now - time) / 1000);

            if (diffInSeconds < 60) return 'Just now';
            if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
            if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
            return `${Math.floor(diffInSeconds / 86400)}d ago`;
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeNotifications();
        });

        // ==========================================
        // GOOGLE MAPS INTEGRATION FUNCTIONS
        // ==========================================

        // Enhanced Google Maps integration with error handling and multiple options
        window.openGoogleMaps = function(location, options = {}) {
            if (!location || location.trim() === '') {
                showNotification('No location specified', 'warning');
                return;
            }

            const encodedLocation = encodeURIComponent(location.trim());
            let googleMapsUrl;

            if (options.type === 'directions' && options.destination) {
                // For directions between two points
                const encodedDestination = encodeURIComponent(options.destination.trim());
                googleMapsUrl = `https://www.google.com/maps/dir/?api=1&origin=${encodedLocation}&destination=${encodedDestination}&travelmode=${options.travelmode || 'driving'}`;
            } else {
                // For single location search
                googleMapsUrl = `https://www.google.com/maps/search/?api=1&query=${encodedLocation}`;
            }

            // Open in new tab
            window.open(googleMapsUrl, '_blank');

            // Show notification
            const message = options.type === 'directions'
                ? `Opening directions from ${location} to ${options.destination}`
                : `Opening Google Maps for: ${location}`;
            showNotification(message, 'info');
        };

        // Specific function for rider locations
        window.openRiderLocationOnMap = function(location, riderName) {
            if (!location || location.trim() === '') {
                showNotification(`No location available for rider ${riderName}`, 'warning');
                return;
            }
            openGoogleMaps(location, { context: `rider ${riderName}` });
        };

        // Specific function for customer addresses
        window.openCustomerAddressOnMap = function(address, customerName) {
            if (!address || address.trim() === '') {
                showNotification(`No address available for customer ${customerName}`, 'warning');
                return;
            }
            openGoogleMaps(address, { context: `customer ${customerName}` });
        };

        // Function for delivery routes with optimization
        window.openDeliveryRoute = function(origin, destination, riderName) {
            if (!origin || !destination) {
                showNotification('Origin and destination required for route', 'warning');
                return;
            }
            openGoogleMaps(origin, {
                type: 'directions',
                destination: destination,
                travelmode: 'driving',
                context: `delivery route for ${riderName}`
            });
        };

        // Function for route optimization (multiple stops)
        window.openOptimizedRoute = function(stops, riderName) {
            if (!stops || stops.length < 2) {
                showNotification('At least 2 stops required for route optimization', 'warning');
                return;
            }

            // Create waypoints string for Google Maps
            const origin = encodeURIComponent(stops[0]);
            const destination = encodeURIComponent(stops[stops.length - 1]);
            const waypoints = stops.slice(1, -1).map(stop => encodeURIComponent(stop)).join('|');

            let googleMapsUrl = `https://www.google.com/maps/dir/?api=1&origin=${origin}&destination=${destination}&travelmode=driving`;
            if (waypoints) {
                googleMapsUrl += `&waypoints=${waypoints}`;
            }

            window.open(googleMapsUrl, '_blank');
            showNotification(`Opening optimized route for ${riderName} with ${stops.length} stops`, 'info');
        };
    </script>

    <!-- Real-Time Updates Scripts -->
    <script src="/static/js/realtime-updates.js"></script>
    <script src="/static/js/realtime-settings.js"></script>

    <!-- User Activity Tracking Script -->
    
    <script src="/static/js/activity-tracker.js"></script>
    <script>
        // Add logged-in class to body for activity tracker detection
        document.body.classList.add('logged-in');
    </script>
    

    
</body>
</html>