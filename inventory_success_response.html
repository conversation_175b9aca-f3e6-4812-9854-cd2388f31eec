<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inventory - Medivent Pharmaceuticals ERP</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">

    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">

    <!-- Select2 CSS for enhanced dropdowns -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">

    <!-- Animate.css for animations -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <!-- Custom CSS -->
    <style>
        :root {
            --primary: #0572CE;
            --secondary: #3A7CA5;
            --accent: #F7941D;
            --background: #F5F5F5;
            --card: #FFFFFF;
            --text: #333333;
            --text-light: #666666;
            --success: #4CAF50;
            --warning: #FFC107;
            --error: #F44336;
            --border: #E0E0E0;
        }

        body {
            background-color: var(--background);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .sidebar {
            background-color: var(--primary);
            color: white;
            min-height: 100vh;
            padding-top: 20px;
            width: 100%;
            overflow-y: auto;
            overflow-x: hidden;
            max-height: 100vh;
            position: relative;
            transition: all 0.3s ease;
        }

        /* Minimized sidebar styles */
        .sidebar.minimized {
            width: 60px;
            min-width: 60px;
        }

        .sidebar.minimized .sidebar-header h4,
        .sidebar.minimized .nav-link span,
        .sidebar.minimized .collapse {
            display: none;
        }

        .sidebar.minimized .nav-link {
            text-align: center;
            padding: 12px 0;
            justify-content: center;
        }

        .sidebar.minimized .nav-link i {
            margin-right: 0;
            font-size: 1.2rem;
        }

        /* Sidebar toggle button */
        .sidebar-toggle {
            position: absolute;
            top: 10px;
            right: -15px;
            background: var(--primary);
            color: white;
            border: 2px solid white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 1000;
            transition: all 0.3s ease;
            font-size: 12px;
        }

        .sidebar-toggle:hover {
            background: white;
            color: var(--primary);
            transform: scale(1.1);
        }

        /* Adjust main content when sidebar is minimized */
        .main-content {
            transition: margin-left 0.3s ease;
        }

        .main-content.sidebar-minimized {
            margin-left: -140px; /* Adjust based on sidebar width difference */
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 8px 12px;
            margin-bottom: 3px;
            border-radius: 5px;
            font-size: 0.9rem;
            white-space: nowrap;
            overflow: visible; /* Changed from hidden to visible */
            text-overflow: clip; /* Changed from ellipsis to clip */
            width: 100%;
            position: relative;
        }

        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.2);
            width: 100%;
            display: block;
        }

        /* Add tooltip-style hover effect for menu items */
        .sidebar .nav-link span {
            position: relative;
            z-index: 1;
        }

        .sidebar .nav-link:hover span::after {
            content: attr(data-title);
            position: absolute;
            left: 100%;
            top: 0;
            white-space: nowrap;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 3px;
            z-index: 999;
            margin-left: 10px;
            display: none; /* Will be shown via JavaScript */
        }

        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.2);
        }

        .sidebar .nav-link i {
            margin-right: 8px;
            width: 16px;
            text-align: center;
        }

        /* Submenu styling */
        .sidebar .collapse.list-unstyled {
            padding-left: 0;
            width: 100%;
            margin-left: 0;
            transition: all 0.3s ease;
        }

        .sidebar .collapse.list-unstyled .nav-link {
            padding-left: 25px; /* Further reduced padding for better text display */
            font-size: 0.85rem;
            width: 100%;
            text-overflow: ellipsis;
            overflow: hidden;
            position: relative; /* For icon positioning */
            transition: all 0.2s ease;
        }

        .sidebar .collapse.list-unstyled .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.25);
            transform: translateX(3px);
        }

        /* Dropdown toggle icon */
        .sidebar .nav-link.dropdown-toggle::after {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.3s ease;
        }

        .sidebar .nav-link.dropdown-toggle[aria-expanded="true"]::after {
            transform: translateY(-50%) rotate(180deg);
        }

        /* Position icons consistently */
        .sidebar .nav-link i {
            width: 14px;
            text-align: center;
            margin-right: 3px;
            position: absolute;
            left: 8px;
        }

        /* Add padding for text to accommodate icons */
        .sidebar .nav-link span {
            padding-left: 16px;
        }

        /* Prevent horizontal scrolling in sidebar */
        .sidebar .nav {
            width: 100%;
        }

        .sidebar .nav-item {
            width: 100%;
        }

        .content {
            padding: 20px;
        }

        .navbar {
            background-color: white;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        .card-header {
            border-radius: 10px 10px 0 0 !important;
            font-weight: bold;
        }

        .btn-primary {
            background-color: var(--primary);
            border-color: var(--primary);
        }

        .btn-primary:hover {
            background-color: var(--secondary);
            border-color: var(--secondary);
        }

        .table th {
            font-weight: 600;
        }

        .badge {
            padding: 5px 10px;
            font-weight: 500;
        }

        /* Modern UI/UX Improvements */

        /* Loading Spinner */
        .loading-spinner {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid var(--primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Progress Indicators */
        .progress-container {
            position: relative;
            margin: 20px 0;
        }

        .progress-bar-animated {
            animation: progress-bar-stripes 1s linear infinite;
        }

        /* Enhanced Buttons */
        .btn {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .btn-loading {
            pointer-events: none;
            opacity: 0.7;
        }

        .btn-loading::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            margin: auto;
            border: 2px solid transparent;
            border-top-color: #ffffff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
        }

        /* Enhanced Cards */
        .card {
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .card-enhanced {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        }

        /* Mobile Responsive Improvements */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -100%;
                top: 0;
                width: 280px;
                height: 100vh;
                z-index: 1000;
                transition: left 0.3s ease;
            }

            .sidebar.show {
                left: 0;
            }

            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 999;
                display: none;
            }

            .sidebar-overlay.show {
                display: block;
            }

            .content {
                padding: 10px;
            }

            .table-responsive {
                font-size: 0.875rem;
            }

            .btn-group-vertical .btn {
                margin-bottom: 5px;
            }
        }

        /* Enhanced Tables */
        .table-enhanced {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .table-enhanced thead th {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            color: white;
            border: none;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.875rem;
            letter-spacing: 0.5px;
        }

        .table-enhanced tbody tr {
            transition: all 0.2s ease;
        }

        .table-enhanced tbody tr:hover {
            background-color: rgba(5, 114, 206, 0.05);
            transform: scale(1.01);
        }

        /* Search Enhancement */
        .search-container {
            position: relative;
        }

        .search-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-radius: 0 0 8px 8px;
            max-height: 300px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .search-suggestion-item {
            padding: 10px 15px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.2s ease;
        }

        .search-suggestion-item:hover,
        .search-suggestion-item.active {
            background-color: var(--primary);
            color: white;
        }

        /* Notification Styles */
        .notification-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            max-width: 400px;
        }

        .notification {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--primary);
            animation: slideInRight 0.3s ease;
        }

        .notification.success {
            border-left-color: var(--success);
        }

        .notification.warning {
            border-left-color: var(--warning);
        }

        .notification.error {
            border-left-color: var(--error);
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* Export Button Styles */
        .export-buttons {
            margin: 20px 0;
        }

        .export-btn {
            margin-right: 10px;
            margin-bottom: 10px;
        }

        /* Dark Mode Support */
        @media (prefers-color-scheme: dark) {
            :root {
                --background: #1a1a1a;
                --card: #2d2d2d;
                --text: #ffffff;
                --text-light: #cccccc;
                --border: #404040;
            }

            body {
                background-color: var(--background);
                color: var(--text);
            }

            .card {
                background-color: var(--card);
                color: var(--text);
            }

            .table {
                color: var(--text);
            }

            .table-enhanced tbody tr:hover {
                background-color: rgba(255, 255, 255, 0.05);
            }
        }
    </style>

    
<style>
    .status-toggle {
        text-decoration: none !important;
        cursor: pointer;
    }

    .status-toggle .badge {
        transition: all 0.3s ease;
    }

    .status-toggle:hover .badge {
        transform: scale(1.1);
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }

    .status-toggle .badge {
        padding: 6px 12px;
        font-size: 0.9rem;
    }

    /* Make the badge wider for better visibility */
    .status-toggle .badge {
        min-width: 80px;
        text-align: center;
    }
</style>

</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Mobile Sidebar Overlay -->
            <div class="sidebar-overlay" id="sidebarOverlay"></div>

            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar d-none d-md-block" id="sidebarMenu">
                <!-- Sidebar Toggle Button -->
                <div class="sidebar-toggle" id="sidebarToggle" onclick="toggleSidebarMinimize()" title="Minimize/Maximize Sidebar">
                    <i class="fas fa-chevron-left" id="toggleIcon"></i>
                </div>

                <div class="text-center mb-4 sidebar-header">
                    <h4>Medivent ERP</h4>
                </div>
                <ul class="nav flex-column">
                    
                    <li class="nav-item">
                        <a class="nav-link " href="/dashboard">
                            <i class="fas fa-tachometer-alt"></i><span>Dashboard</span>
                        </a>
                    </li>
                    

                    
                    <li class="nav-item">
                        <a class="nav-link " href="/dashboard?view=ceo">
                            <i class="fas fa-chart-line"></i><span>Executive Dashboard</span>
                        </a>
                    </li>
                    

                    <!-- Order Management -->
                    
                    <li class="nav-item">
                        <a class="nav-link dropdown-toggle " href="#orderSubmenu" data-toggle="collapse" aria-expanded="false">
                            <i class="fas fa-shopping-cart"></i><span>Order Management</span>
                        </a>
                        <ul class="collapse list-unstyled ml-3" id="orderSubmenu">
                            <li>
                                <a class="nav-link " href="/orders/new">
                                    <i class="fas fa-plus-circle"></i><span>Place New Order</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/orders/">
                                    <i class="fas fa-edit"></i><span>Update Order</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/orders/?search=true">
                                    <i class="fas fa-search"></i><span>Search Orders</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/orders/workflow">
                                    <i class="fas fa-tasks"></i><span>Order Workflow</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/orders/workflow?status=Ready+for+Pickup">
                                    <i class="fas fa-truck"></i><span>Dispatch Orders</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/orders/workflow?status=Dispatched">
                                    <i class="fas fa-check-circle"></i><span>Deliver Orders</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/orders/?history=true">
                                    <i class="fas fa-history"></i><span>Order History</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/orders/?invoice=true">
                                    <i class="fas fa-file-invoice"></i><span>Fetch Invoice/Challan</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    

                    <!-- Product -->
                    
                    <li class="nav-item">
                        <a class="nav-link dropdown-toggle " href="#productSubmenu" data-toggle="collapse" aria-expanded="false">
                            <i class="fas fa-pills"></i><span>Product</span>
                        </a>
                        <ul class="collapse list-unstyled ml-3" id="productSubmenu">
                            <li>
                                <a class="nav-link " href="/products/">
                                    <i class="fas fa-th-large"></i><span>Products Gallery</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/product_management">
                                    <i class="fas fa-cogs"></i><span>Product Management</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/products/new">
                                    <i class="fas fa-plus-circle"></i><span>Add Product</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/products/update_selection">
                                    <i class="fas fa-edit"></i><span>Update Product</span>
                                </a>
                            </li>

                            <li>
                                <a class="nav-link " href="/products/view_all/">
                                    <i class="fas fa-list"></i><span>View All Products</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    

                    <!-- Warehouse Management -->
                    
                    <li class="nav-item">
                        <a class="nav-link dropdown-toggle active" href="#warehouseSubmenu" data-toggle="collapse" aria-expanded="false">
                            <i class="fas fa-warehouse"></i><span>Warehouse Management</span>
                        </a>
                        <ul class="collapse list-unstyled ml-3" id="warehouseSubmenu">
                            <li>
                                <a class="nav-link " href="/warehouse-management/add">
                                    <i class="fas fa-plus-circle"></i><span>Add Warehouse</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/warehouse-management/manage">
                                    <i class="fas fa-warehouse"></i><span>Manage Warehouses</span>
                                </a>
                            </li>

                            <li>
                                <a class="nav-link " href="/dc-pending">
                                    <i class="fas fa-clock"></i><span>DC Pending</span>
                                </a>
                            </li>

                            <li>
                                <a class="nav-link active" href="/inventory/">
                                    <i class="fas fa-boxes"></i><span>Inventory Management</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/delivery_challans">
                                    <i class="fas fa-truck"></i><span>All Delivery Challans</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/warehouse/reports">
                                    <i class="fas fa-chart-line"></i><span>Warehouse Reports</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    

                    <!-- Packing Operations -->
                    
                    <li class="nav-item">
                        <a class="nav-link dropdown-toggle " href="#packingSubmenu" data-toggle="collapse" aria-expanded="false">
                            <i class="fas fa-box-open"></i><span>Packing Operations</span>
                        </a>
                        <ul class="collapse list-unstyled ml-3" id="packingSubmenu">
                            <li>
                                <a class="nav-link " href="/warehouse/packing">
                                    <i class="fas fa-box-open"></i><span>Packing Dashboard</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/warehouse/orders">
                                    <i class="fas fa-list-ul"></i><span>Orders Queue</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    

                    <!-- Rider Management - Comprehensive Menu -->
                    
                    <li class="nav-item">
                        <a class="nav-link dropdown-toggle " href="#riderSubmenu" data-toggle="collapse" aria-expanded="false">
                            <i class="fas fa-motorcycle"></i><span>Rider Management</span>
                        </a>
                        <ul class="collapse list-unstyled ml-3" id="riderSubmenu">
                            <li>
                                <a class="nav-link " href="/riders/dashboard">
                                    <i class="fas fa-users"></i><span>All Riders</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/riders/register">
                                    <i class="fas fa-user-plus"></i><span>Register Rider</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/rider/delivery_routes">
                                    <i class="fas fa-route"></i><span>Delivery Routes</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/rider/performance">
                                    <i class="fas fa-chart-line"></i><span>Rider Performance</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/riders/reports">
                                    <i class="fas fa-chart-bar"></i><span>Reports</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/riders/assignment-dashboard">
                                    <i class="fas fa-clipboard-list"></i><span>Assignment Dashboard</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/riders/dashboard">
                                    <i class="fas fa-tachometer-alt"></i><span>Professional Dashboard</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/admin/rider_tracking">
                                    <i class="fas fa-map-marker-alt"></i><span>Admin Tracking</span>
                                </a>
                            </li>

                        </ul>
                    </li>
                    

                    <!-- TCS Tracking -->
                    <li class="nav-item">
                        <a class="nav-link " href="/track">
                            <i class="fas fa-shipping-fast"></i><span>TCS Tracking</span>
                        </a>
                    </li>

                    <!-- Reports -->
                    
                    <li class="nav-item">
                        <a class="nav-link dropdown-toggle " href="#reportsSubmenu" data-toggle="collapse" aria-expanded="false">
                            <i class="fas fa-chart-bar"></i><span>Reports</span>
                        </a>
                        <ul class="collapse list-unstyled ml-3" id="reportsSubmenu">
                            <li>
                                <a class="nav-link " href="/sales_report/daily">
                                    <i class="fas fa-calendar-day"></i><span>Daily Sales Report</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/weekly_sales_report">
                                    <i class="fas fa-calendar-week"></i><span>Weekly Sales Report</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/sales_report/monthly">
                                    <i class="fas fa-calendar-alt"></i><span>Monthly Sales Report</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/reports/custom-date-range">
                                    <i class="fas fa-calendar"></i><span>Custom Date Range Report</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/reports/sales-by-agent">
                                    <i class="fas fa-user-tie"></i><span>Sales by Agent Report</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/reports/product-performance">
                                    <i class="fas fa-chart-line"></i><span>Product Performance Report</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/inventory_report">
                                    <i class="fas fa-boxes"></i><span>Inventory Reports</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/reports">
                                    <i class="fas fa-chart-pie"></i><span>All Reports</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    

                    <!-- Finance/Accounts -->
                    
                    <li class="nav-item">
                        <a class="nav-link dropdown-toggle " href="#financeSubmenu" data-toggle="collapse" aria-expanded="false">
                            <i class="fas fa-money-bill-wave"></i><span>Finance/Accounts</span>
                        </a>
                        <ul class="collapse list-unstyled ml-3" id="financeSubmenu">
                            <li>
                                <a class="nav-link " href="/finance/dashboard">
                                    <i class="fas fa-tachometer-alt"></i><span>Finance Dashboard</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/finance/pending-invoices">
                                    <i class="fas fa-file-invoice-dollar"></i><span>Pending Invoices</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/finance/customer-ledger">
                                    <i class="fas fa-users"></i><span>Customer Ledger</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/finance/payment-collection">
                                    <i class="fas fa-hand-holding-usd"></i><span>Payment Collection</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/finance/financial-reports">
                                    <i class="fas fa-chart-pie"></i><span>Financial Reports</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/finance/comprehensive-reports">
                                    <i class="fas fa-file-alt"></i><span>Comprehensive Reports</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    



                    <!-- Sales Team Management -->
                    <li class="nav-item">
                        <a class="nav-link dropdown-toggle " href="#salesSubmenu" data-toggle="collapse" aria-expanded="false">
                            <i class="fas fa-users"></i><span>Sales Team</span>
                        </a>
                        <ul class="collapse list-unstyled ml-3" id="salesSubmenu">
                            <li>
                                <a class="nav-link " href="/sales-team">
                                    <i class="fas fa-chart-line"></i><span>Sales Dashboard</span>
                                </a>
                            </li>
                            <!-- Dynamic Division Submenus -->
                            
                            <li>
                                <a class="nav-link" href="/sales-team/division/Aqvida">
                                    <i class="fas fa-building"></i><span>Aqvida Division</span>
                                </a>
                            </li>
                            
                            <li>
                                <a class="nav-link" href="/sales-team/division/Finance%20Division">
                                    <i class="fas fa-building"></i><span>Finance Division Division</span>
                                </a>
                            </li>
                            
                            <li>
                                <a class="nav-link" href="/sales-team/division/Sales%20Division">
                                    <i class="fas fa-building"></i><span>Sales Division Division</span>
                                </a>
                            </li>
                            
                            <li>
                                <a class="nav-link" href="/sales-team/division/new%20divison">
                                    <i class="fas fa-building"></i><span>new divison Division</span>
                                </a>
                            </li>
                            
                            <li>
                                <a class="nav-link" href="/sales-team/division/test05">
                                    <i class="fas fa-building"></i><span>test05 Division</span>
                                </a>
                            </li>
                            
                            <li>
                                <a class="nav-link" href="/reports/sales-by-agent">
                                    <i class="fas fa-user-tie"></i><span>Agent Performance</span>
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- User Management -->
                    
                    <li class="nav-item">
                        <a class="nav-link dropdown-toggle " href="#userSubmenu" data-toggle="collapse" aria-expanded="false">
                            <i class="fas fa-users-cog"></i><span>User Management</span>
                        </a>
                        <ul class="collapse list-unstyled ml-3" id="userSubmenu">
                            <li>
                                <a class="nav-link " href="/users">
                                    <i class="fas fa-users"></i><span>View All Users</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/users/new">
                                    <i class="fas fa-user-plus"></i><span>Add New User</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/users/permissions">
                                    <i class="fas fa-user-tag"></i><span>Manage Roles</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/settings">
                                    <i class="fas fa-key"></i><span>Change Password</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/users/permissions">
                                    <i class="fas fa-clipboard-list"></i><span>View User Activity Logs</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    

                    <!-- Division Management -->
                    
                    <li class="nav-item">
                        <a class="nav-link dropdown-toggle " href="#divisionSubmenu" data-toggle="collapse" aria-expanded="false">
                            <i class="fas fa-building"></i><span>Division Management</span>
                        </a>
                        <ul class="collapse list-unstyled ml-3" id="divisionSubmenu">
                            <li>
                                <a class="nav-link " href="/divisions/">
                                    <i class="fas fa-list"></i><span>All Divisions</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/divisions/analytics">
                                    <i class="fas fa-chart-line"></i><span>Analytics</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link" href="/divisions/export">
                                    <i class="fas fa-download"></i><span>Export Data</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    

                    <!-- Customer Management -->
                    
                    <li class="nav-item">
                        <a class="nav-link dropdown-toggle " href="#customerSubmenu" data-toggle="collapse" aria-expanded="false">
                            <i class="fas fa-user-tag"></i><span>Customer Management</span>
                        </a>
                        <ul class="collapse list-unstyled ml-3" id="customerSubmenu">
                            <li>
                                <a class="nav-link " href="/customers">
                                    <i class="fas fa-users"></i><span>View All Customers</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/customers?view=by_type">
                                    <i class="fas fa-user-tag"></i><span>View Customers by Type</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/customers?action=add">
                                    <i class="fas fa-user-plus"></i><span>Add New Customer</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/customers?view=pricing">
                                    <i class="fas fa-tags"></i><span>View Customer Pricing</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/customers?action=set_pricing">
                                    <i class="fas fa-dollar-sign"></i><span>Set Customer Pricing</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link " href="/customers?view=orders">
                                    <i class="fas fa-history"></i><span>View Customer Order History</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    



                    <!-- Import Data -->
                    
                    <li class="nav-item">
                        <a class="nav-link " href="/import_data">
                            <i class="fas fa-file-import"></i><span>Import Data</span>
                        </a>
                    </li>
                    

                    <!-- Settings -->
                    
                    <li class="nav-item">
                        <a class="nav-link " href="/settings">
                            <i class="fas fa-cog"></i><span>Settings</span>
                        </a>
                    </li>
                    
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 ml-sm-auto px-0 main-content" id="mainContent">
                <!-- Top Navbar -->
                <nav class="navbar navbar-expand-lg navbar-light">
                    <button class="navbar-toggler d-md-none" type="button" onclick="toggleSidebar()">
                        <span class="navbar-toggler-icon"></span>
                    </button>

                    <!-- Intelligent Search Bar -->
                    <div class="flex-grow-1 mx-3">
                        <!-- Intelligent Search Component -->
<div class="intelligent-search-container">
    <div class="search-input-wrapper">
        <div class="input-group">
            <span class="input-group-text search-icon">
                <i class="fas fa-search"></i>
            </span>
            <input type="text" 
                   class="form-control intelligent-search-input" 
                   id="intelligentSearchInput"
                   placeholder="Search orders, customers, products, finance..." 
                   autocomplete="off">
            <div class="input-group-append">
                <button class="btn btn-outline-secondary dropdown-toggle" 
                        type="button" 
                        data-bs-toggle="dropdown" 
                        aria-expanded="false">
                    <span id="searchTypeLabel">All</span>
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" data-type="all">All Results</a></li>
                    <li><a class="dropdown-item" href="#" data-type="customers">Customers</a></li>
                    <li><a class="dropdown-item" href="#" data-type="orders">Orders</a></li>
                    <li><a class="dropdown-item" href="#" data-type="products">Products</a></li>
                    <li><a class="dropdown-item" href="#" data-type="finance">Finance</a></li>
                </ul>
            </div>
        </div>
    </div>
    
    <!-- Search Results Dropdown -->
    <div class="search-results-dropdown" id="searchResultsDropdown" style="display: none;">
        <div class="search-results-header">
            <div class="d-flex justify-content-between align-items-center">
                <span class="search-results-title">Search Results</span>
                <span class="search-results-count" id="searchResultsCount">0 results</span>
            </div>
        </div>
        
        <!-- Suggestions Section -->
        <div class="search-suggestions-section" id="searchSuggestionsSection" style="display: none;">
            <div class="search-section-title">
                <i class="fas fa-lightbulb me-2"></i>Suggestions
            </div>
            <div class="search-suggestions" id="searchSuggestions"></div>
        </div>
        
        <!-- Results Section -->
        <div class="search-results-section" id="searchResultsSection">
            <div class="search-results" id="searchResults"></div>
        </div>
        
        <!-- No Results -->
        <div class="no-search-results" id="noSearchResults" style="display: none;">
            <div class="text-center py-4">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h6 class="text-muted">No results found</h6>
                <p class="text-muted small">Try different keywords or check spelling</p>
            </div>
        </div>
        
        <!-- Loading -->
        <div class="search-loading" id="searchLoading" style="display: none;">
            <div class="text-center py-4">
                <div class="spinner-border spinner-border-sm text-primary me-2" role="status"></div>
                <span>Searching...</span>
            </div>
        </div>
    </div>
</div>

<style>
.intelligent-search-container {
    position: relative;
    width: 100%;
    max-width: 500px;
}

.search-input-wrapper {
    position: relative;
}

.intelligent-search-input {
    border-radius: 25px;
    padding: 12px 20px;
    font-size: 14px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.intelligent-search-input:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.search-icon {
    background: transparent;
    border: none;
    color: #6c757d;
}

.search-results-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    z-index: 1050;
    max-height: 500px;
    overflow-y: auto;
    margin-top: 5px;
}

.search-results-header {
    padding: 15px 20px 10px;
    border-bottom: 1px solid #f8f9fa;
    background: #f8f9fa;
    border-radius: 15px 15px 0 0;
}

.search-results-title {
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.search-results-count {
    font-size: 12px;
    color: #6c757d;
}

.search-section-title {
    padding: 10px 20px 5px;
    font-size: 12px;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.search-suggestions {
    padding: 0 10px 10px;
}

.search-suggestion-item {
    display: flex;
    align-items: center;
    padding: 8px 15px;
    margin: 2px 0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
}

.search-suggestion-item:hover {
    background: #f8f9fa;
}

.search-suggestion-icon {
    width: 20px;
    margin-right: 10px;
    color: #6c757d;
    font-size: 12px;
}

.search-results {
    padding: 0 10px 10px;
}

.search-result-item {
    display: flex;
    align-items: flex-start;
    padding: 12px 15px;
    margin: 2px 0;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    color: inherit;
}

.search-result-item:hover {
    background: #f8f9fa;
    text-decoration: none;
    color: inherit;
    transform: translateY(-1px);
}

.search-result-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 16px;
    color: white;
    flex-shrink: 0;
}

.search-result-icon.customer { background: linear-gradient(135deg, #007bff, #0056b3); }
.search-result-icon.order { background: linear-gradient(135deg, #28a745, #1e7e34); }
.search-result-icon.product { background: linear-gradient(135deg, #ffc107, #e0a800); }
.search-result-icon.invoice { background: linear-gradient(135deg, #dc3545, #c82333); }
.search-result-icon.payment { background: linear-gradient(135deg, #6f42c1, #5a32a3); }

.search-result-content {
    flex: 1;
    min-width: 0;
}

.search-result-title {
    font-weight: 600;
    font-size: 14px;
    color: #212529;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.search-result-subtitle {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.search-result-description {
    font-size: 11px;
    color: #868e96;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.search-result-type {
    position: absolute;
    top: 8px;
    right: 8px;
    background: var(--primary);
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    text-transform: uppercase;
    font-weight: 500;
}

.no-search-results {
    padding: 20px;
}

.search-loading {
    padding: 20px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .intelligent-search-container {
        max-width: 100%;
    }
    
    .search-results-dropdown {
        max-height: 400px;
    }
}

/* Highlight matching text */
.search-highlight {
    background: rgba(255, 193, 7, 0.3);
    font-weight: 600;
    padding: 1px 2px;
    border-radius: 2px;
}

/* Animation for dropdown */
.search-results-dropdown {
    animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

<script>
class IntelligentSearch {
    constructor() {
        this.searchInput = document.getElementById('intelligentSearchInput');
        this.searchResults = document.getElementById('searchResults');
        this.searchSuggestions = document.getElementById('searchSuggestions');
        this.searchResultsDropdown = document.getElementById('searchResultsDropdown');
        this.searchResultsCount = document.getElementById('searchResultsCount');
        this.searchSuggestionsSection = document.getElementById('searchSuggestionsSection');
        this.searchResultsSection = document.getElementById('searchResultsSection');
        this.noSearchResults = document.getElementById('noSearchResults');
        this.searchLoading = document.getElementById('searchLoading');
        this.searchTypeLabel = document.getElementById('searchTypeLabel');
        
        this.currentSearchType = 'all';
        this.searchTimeout = null;
        this.currentQuery = '';
        
        this.init();
    }
    
    init() {
        // Search input events
        this.searchInput.addEventListener('input', (e) => {
            this.handleSearchInput(e.target.value);
        });
        
        this.searchInput.addEventListener('focus', () => {
            if (this.currentQuery.length >= 2) {
                this.showDropdown();
            }
        });
        
        this.searchInput.addEventListener('keydown', (e) => {
            this.handleKeydown(e);
        });
        
        // Search type dropdown
        document.querySelectorAll('.dropdown-item[data-type]').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                this.setSearchType(e.target.dataset.type, e.target.textContent);
            });
        });
        
        // Click outside to close
        document.addEventListener('click', (e) => {
            if (!this.searchInput.contains(e.target) && !this.searchResultsDropdown.contains(e.target)) {
                this.hideDropdown();
            }
        });
    }
    
    handleSearchInput(query) {
        this.currentQuery = query.trim();
        
        // Clear previous timeout
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }
        
        if (this.currentQuery.length < 2) {
            this.hideDropdown();
            return;
        }
        
        // Debounce search
        this.searchTimeout = setTimeout(() => {
            this.performSearch(this.currentQuery);
        }, 300);
    }
    
    async performSearch(query) {
        this.showLoading();
        
        try {
            const response = await fetch(`/api/intelligent-search?q=${encodeURIComponent(query)}&type=${this.currentSearchType}&limit=10`);
            const data = await response.json();
            
            if (data.error) {
                throw new Error(data.error);
            }
            
            this.displayResults(data);
            
        } catch (error) {
            console.error('Search error:', error);
            this.showNoResults();
        }
    }
    
    displayResults(data) {
        this.hideLoading();
        
        // Update results count
        this.searchResultsCount.textContent = `${data.total_found} result${data.total_found !== 1 ? 's' : ''}`;
        
        // Display suggestions
        if (data.suggestions && data.suggestions.length > 0) {
            this.displaySuggestions(data.suggestions);
            this.searchSuggestionsSection.style.display = 'block';
        } else {
            this.searchSuggestionsSection.style.display = 'none';
        }
        
        // Display results
        if (data.results && data.results.length > 0) {
            this.displaySearchResults(data.results);
            this.searchResultsSection.style.display = 'block';
            this.noSearchResults.style.display = 'none';
        } else {
            this.searchResultsSection.style.display = 'none';
            this.noSearchResults.style.display = 'block';
        }
        
        this.showDropdown();
    }
    
    displaySuggestions(suggestions) {
        this.searchSuggestions.innerHTML = suggestions.map(suggestion => `
            <div class="search-suggestion-item" onclick="intelligentSearch.selectSuggestion('${suggestion.text}')">
                <div class="search-suggestion-icon">
                    <i class="${suggestion.icon}"></i>
                </div>
                <span>${this.highlightMatch(suggestion.text, this.currentQuery)}</span>
            </div>
        `).join('');
    }
    
    displaySearchResults(results) {
        this.searchResults.innerHTML = results.map(result => `
            <a href="${result.url}" class="search-result-item" onclick="intelligentSearch.hideDropdown()">
                <div class="search-result-icon ${result.type}">
                    <i class="${result.icon}"></i>
                </div>
                <div class="search-result-content">
                    <div class="search-result-title">${this.highlightMatch(result.title, this.currentQuery)}</div>
                    <div class="search-result-subtitle">${this.highlightMatch(result.subtitle, this.currentQuery)}</div>
                    <div class="search-result-description">${result.description}</div>
                </div>
            </a>
        `).join('');
    }
    
    highlightMatch(text, query) {
        if (!query || !text) return text;
        
        const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
        return text.replace(regex, '<span class="search-highlight">$1</span>');
    }
    
    selectSuggestion(suggestion) {
        this.searchInput.value = suggestion;
        this.currentQuery = suggestion;
        this.performSearch(suggestion);
    }
    
    setSearchType(type, label) {
        this.currentSearchType = type;
        this.searchTypeLabel.textContent = label;
        
        if (this.currentQuery.length >= 2) {
            this.performSearch(this.currentQuery);
        }
    }
    
    showDropdown() {
        this.searchResultsDropdown.style.display = 'block';
    }
    
    hideDropdown() {
        this.searchResultsDropdown.style.display = 'none';
    }
    
    showLoading() {
        this.searchLoading.style.display = 'block';
        this.searchResultsSection.style.display = 'none';
        this.searchSuggestionsSection.style.display = 'none';
        this.noSearchResults.style.display = 'none';
        this.showDropdown();
    }
    
    hideLoading() {
        this.searchLoading.style.display = 'none';
    }
    
    showNoResults() {
        this.hideLoading();
        this.searchResultsSection.style.display = 'none';
        this.searchSuggestionsSection.style.display = 'none';
        this.noSearchResults.style.display = 'block';
        this.searchResultsCount.textContent = '0 results';
        this.showDropdown();
    }
    
    handleKeydown(e) {
        if (e.key === 'Escape') {
            this.hideDropdown();
        }
        // Add arrow key navigation here if needed
    }
}

// Initialize search when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.intelligentSearch = new IntelligentSearch();
});
</script>
                    </div>

                    <div class="collapse navbar-collapse" id="navbarSupportedContent">
                        <ul class="navbar-nav mr-auto">
                            <li class="nav-item">
                                <span class="navbar-text">
                                    <i class="far fa-calendar-alt"></i> Today: 
                                </span>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/track-order" title="Track Order">
                                    <i class="fas fa-search"></i> <small>Track Order</small>
                                </a>
                            </li>
                        </ul>
                        <ul class="navbar-nav">
                            <!-- Notifications Dropdown -->
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle position-relative" href="#" id="notificationsDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <i class="fas fa-bell"></i>
                                    <span class="badge badge-danger badge-pill position-absolute notification-badge" style="top: 0; right: 5px; font-size: 0.6rem; display: none;">0</span>
                                </a>
                                <div class="dropdown-menu dropdown-menu-right notification-dropdown" aria-labelledby="notificationsDropdown" style="width: 350px; max-height: 400px; overflow-y: auto;">
                                    <div class="dropdown-header d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-bell"></i> Notifications</span>
                                        <div>
                                            <button class="btn btn-sm btn-outline-secondary me-1" onclick="markAllNotificationsRead()" title="Mark all as read">
                                                <i class="fas fa-check-double"></i>
                                            </button>
                                            <a href="/notifications" class="btn btn-sm btn-outline-primary">View All</a>
                                        </div>
                                    </div>
                                    <div class="dropdown-divider"></div>
                                    <div id="notification-dropdown-list">
                                        <div class="dropdown-item text-center text-muted">
                                            <i class="fas fa-spinner fa-spin"></i> Loading...
                                        </div>
                                    </div>
                                    <div class="dropdown-divider"></div>
                                    <div class="dropdown-footer text-center">
                                        <a href="/notifications" class="btn btn-sm btn-primary btn-block">
                                            <i class="fas fa-bell"></i> View All Notifications
                                        </a>
                                    </div>
                                </div>
                            </li>

                            <!-- User Dropdown -->
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <i class="fas fa-user-circle"></i> admin
                                </a>
                                <div class="dropdown-menu dropdown-menu-right" aria-labelledby="navbarDropdown">
                                    <a class="dropdown-item" href="#">
                                        <i class="fas fa-user"></i> Profile
                                    </a>
                                    <div class="dropdown-divider"></div>
                                    <a class="dropdown-item" href="/logout">
                                        <i class="fas fa-sign-out-alt"></i> Logout
                                    </a>
                                </div>
                            </li>
                        </ul>
                    </div>
                </nav>

                <!-- Mobile Sidebar -->
                <div class="collapse d-md-none" id="sidebarMenu">
                    <ul class="nav flex-column bg-light p-2">
                        
                        <li class="nav-item">
                            <a class="nav-link text-dark " href="/dashboard">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        

                        
                        <li class="nav-item">
                            <a class="nav-link text-dark " href="/orders/">
                                <i class="fas fa-shopping-cart"></i> Orders
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-dark " href="/orders/workflow">
                                <i class="fas fa-tasks"></i> Order Workflow
                            </a>
                        </li>
                        

                        
                        <li class="nav-item">
                            <a class="nav-link text-dark " href="/products/">
                                <i class="fas fa-pills"></i> Products
                            </a>
                        </li>
                        

                        
                        <li class="nav-item">
                            <a class="nav-link text-dark active" href="/inventory/">
                                <i class="fas fa-warehouse"></i> Inventory
                            </a>
                        </li>
                        

                        
                        <li class="nav-item">
                            <a class="nav-link text-dark " href="/reports">
                                <i class="fas fa-chart-bar"></i> Reports
                            </a>
                        </li>
                        

                        <!-- Sales Team Mobile Menu -->
                        <li class="nav-item">
                            <a class="nav-link text-dark " href="/sales-team">
                                <i class="fas fa-users"></i> Sales Team
                            </a>
                        </li>
                        <!-- Dynamic Division Links for Mobile -->
                        
                        <li class="nav-item ml-3">
                            <a class="nav-link text-dark" href="/sales-team/division/Aqvida">
                                <i class="fas fa-building"></i> Aqvida
                            </a>
                        </li>
                        
                        <li class="nav-item ml-3">
                            <a class="nav-link text-dark" href="/sales-team/division/Finance%20Division">
                                <i class="fas fa-building"></i> Finance Division
                            </a>
                        </li>
                        
                        <li class="nav-item ml-3">
                            <a class="nav-link text-dark" href="/sales-team/division/Sales%20Division">
                                <i class="fas fa-building"></i> Sales Division
                            </a>
                        </li>
                        
                        <li class="nav-item ml-3">
                            <a class="nav-link text-dark" href="/sales-team/division/new%20divison">
                                <i class="fas fa-building"></i> new divison
                            </a>
                        </li>
                        
                        <li class="nav-item ml-3">
                            <a class="nav-link text-dark" href="/sales-team/division/test05">
                                <i class="fas fa-building"></i> test05
                            </a>
                        </li>
                        

                        
                        <li class="nav-item">
                            <a class="nav-link text-dark " href="/dashboard?view=finance_overview">
                                <i class="fas fa-money-bill-wave"></i> Finance/Accounts
                            </a>
                        </li>
                        

                        
                        <li class="nav-item">
                            <a class="nav-link text-dark " href="/settings">
                                <i class="fas fa-cog"></i> Settings
                            </a>
                        </li>
                        
                    </ul>
                </div>

                <!-- Content Area -->
                <div class="content">
                    <!-- Flash Messages -->
                    
                        
                    

                    <!-- Main Content -->
                    
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Inventory Management</h4>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <form action="/inventory/" method="get" class="form-inline">
                                <div class="input-group w-100">
                                    <input type="text" name="q" class="form-control" placeholder="Search by Product, Batch, Country, Manufacturer, or Division" value="">
                                    <div class="input-group-append">
                                        <button class="btn btn-primary" type="submit">Search</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="col-md-6 text-right">
                            
                            <a href="/export_inventory_excel" class="btn btn-secondary mr-2">
                                <i class="fas fa-file-excel"></i> Export to Excel
                            </a>
                            
                            
                            <a href="/inventory/new" class="btn btn-success">
                                <i class="fas fa-plus-circle"></i> Add New Stock
                            </a>
                            
                            
                            <a href="/inventory/transfer" class="btn btn-info ml-2">
                                <i class="fas fa-exchange-alt"></i> Stock Transfer
                            </a>
                            
                        </div>
                    </div>

                    <!-- Products Summary Section -->
                    
                    <div class="mb-4">
                        <h5 class="text-primary">📦 Products Overview</h5>
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered">
                                <thead class="thead-light">
                                    <tr>
                                        <th>Product</th>
                                        <th>Strength</th>
                                        <th>Division</th>
                                        <th>Total Stock</th>
                                        <th>Available Stock</th>
                                        <th>Batches</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    
                                    <tr>
                                        <td><strong>Amoxicillin 250mg</strong></td>
                                        <td>250mg</td>
                                        <td>Sales Division</td>
                                        <td>
                                            
                                                <span class="badge badge-success">3011 units</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-secondary">0 available</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-info">3 batches</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-success">In Stock</span>
                                            
                                        </td>
                                        <td>
                                            
                                            <a href="/inventory/new?product_id=P002" class="btn btn-sm btn-success">
                                                <i class="fas fa-plus"></i> Add Stock
                                            </a>
                                            
                                        </td>
                                    </tr>
                                    
                                    <tr>
                                        <td><strong>Aspirin 75mg</strong></td>
                                        <td>75mg</td>
                                        <td>Sales Division</td>
                                        <td>
                                            
                                                <span class="badge badge-warning">No Stock</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-secondary">0 available</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-light">No batches</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-info">Ready for Stock</span>
                                            
                                        </td>
                                        <td>
                                            
                                            <a href="/inventory/new?product_id=P006" class="btn btn-sm btn-success">
                                                <i class="fas fa-plus"></i> Add Stock
                                            </a>
                                            
                                        </td>
                                    </tr>
                                    
                                    <tr>
                                        <td><strong>Azithromycin 250mg</strong></td>
                                        <td>250mg</td>
                                        <td>Sales Division</td>
                                        <td>
                                            
                                                <span class="badge badge-warning">No Stock</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-secondary">0 available</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-light">No batches</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-info">Ready for Stock</span>
                                            
                                        </td>
                                        <td>
                                            
                                            <a href="/inventory/new?product_id=P014" class="btn btn-sm btn-success">
                                                <i class="fas fa-plus"></i> Add Stock
                                            </a>
                                            
                                        </td>
                                    </tr>
                                    
                                    <tr>
                                        <td><strong>Calcium Carbonate 500mg</strong></td>
                                        <td>500mg</td>
                                        <td>Sales Division</td>
                                        <td>
                                            
                                                <span class="badge badge-warning">No Stock</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-secondary">0 available</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-light">No batches</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-info">Ready for Stock</span>
                                            
                                        </td>
                                        <td>
                                            
                                            <a href="/inventory/new?product_id=P013" class="btn btn-sm btn-success">
                                                <i class="fas fa-plus"></i> Add Stock
                                            </a>
                                            
                                        </td>
                                    </tr>
                                    
                                    <tr>
                                        <td><strong>Cetirizine 10mg</strong></td>
                                        <td>10mg</td>
                                        <td>Sales Division</td>
                                        <td>
                                            
                                                <span class="badge badge-warning">No Stock</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-secondary">0 available</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-light">No batches</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-info">Ready for Stock</span>
                                            
                                        </td>
                                        <td>
                                            
                                            <a href="/inventory/new?product_id=P010" class="btn btn-sm btn-success">
                                                <i class="fas fa-plus"></i> Add Stock
                                            </a>
                                            
                                        </td>
                                    </tr>
                                    
                                    <tr>
                                        <td><strong>Cough Syrup</strong></td>
                                        <td>100ml</td>
                                        <td>Sales Division</td>
                                        <td>
                                            
                                                <span class="badge badge-success">266 units</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-secondary">0 available</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-info">6 batches</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-success">In Stock</span>
                                            
                                        </td>
                                        <td>
                                            
                                            <a href="/inventory/new?product_id=P003" class="btn btn-sm btn-success">
                                                <i class="fas fa-plus"></i> Add Stock
                                            </a>
                                            
                                        </td>
                                    </tr>
                                    
                                    <tr>
                                        <td><strong>Ibuprofen 400mg</strong></td>
                                        <td>400mg</td>
                                        <td>Sales Division</td>
                                        <td>
                                            
                                                <span class="badge badge-warning">No Stock</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-secondary">0 available</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-light">No batches</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-info">Ready for Stock</span>
                                            
                                        </td>
                                        <td>
                                            
                                            <a href="/inventory/new?product_id=P009" class="btn btn-sm btn-success">
                                                <i class="fas fa-plus"></i> Add Stock
                                            </a>
                                            
                                        </td>
                                    </tr>
                                    
                                    <tr>
                                        <td><strong>Insulin Injection</strong></td>
                                        <td>10ml</td>
                                        <td>Sales Division</td>
                                        <td>
                                            
                                                <span class="badge badge-warning">No Stock</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-secondary">0 available</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-light">No batches</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-info">Ready for Stock</span>
                                            
                                        </td>
                                        <td>
                                            
                                            <a href="/inventory/new?product_id=P005" class="btn btn-sm btn-success">
                                                <i class="fas fa-plus"></i> Add Stock
                                            </a>
                                            
                                        </td>
                                    </tr>
                                    
                                    <tr>
                                        <td><strong>Loratadine 10mg</strong></td>
                                        <td>10mg</td>
                                        <td>Sales Division</td>
                                        <td>
                                            
                                                <span class="badge badge-warning">No Stock</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-secondary">0 available</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-light">No batches</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-info">Ready for Stock</span>
                                            
                                        </td>
                                        <td>
                                            
                                            <a href="/inventory/new?product_id=P012" class="btn btn-sm btn-success">
                                                <i class="fas fa-plus"></i> Add Stock
                                            </a>
                                            
                                        </td>
                                    </tr>
                                    
                                    <tr>
                                        <td><strong>Metformin 500mg</strong></td>
                                        <td>500mg</td>
                                        <td>Sales Division</td>
                                        <td>
                                            
                                                <span class="badge badge-warning">No Stock</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-secondary">0 available</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-light">No batches</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-info">Ready for Stock</span>
                                            
                                        </td>
                                        <td>
                                            
                                            <a href="/inventory/new?product_id=P011" class="btn btn-sm btn-success">
                                                <i class="fas fa-plus"></i> Add Stock
                                            </a>
                                            
                                        </td>
                                    </tr>
                                    
                                    <tr>
                                        <td><strong>Multivitamin Syrup</strong></td>
                                        <td>200ml</td>
                                        <td>Sales Division</td>
                                        <td>
                                            
                                                <span class="badge badge-warning">No Stock</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-secondary">0 available</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-light">No batches</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-info">Ready for Stock</span>
                                            
                                        </td>
                                        <td>
                                            
                                            <a href="/inventory/new?product_id=P008" class="btn btn-sm btn-success">
                                                <i class="fas fa-plus"></i> Add Stock
                                            </a>
                                            
                                        </td>
                                    </tr>
                                    
                                    <tr>
                                        <td><strong>Omeprazole 20mg</strong></td>
                                        <td>20mg</td>
                                        <td>Sales Division</td>
                                        <td>
                                            
                                                <span class="badge badge-warning">No Stock</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-secondary">0 available</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-light">No batches</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-info">Ready for Stock</span>
                                            
                                        </td>
                                        <td>
                                            
                                            <a href="/inventory/new?product_id=P007" class="btn btn-sm btn-success">
                                                <i class="fas fa-plus"></i> Add Stock
                                            </a>
                                            
                                        </td>
                                    </tr>
                                    
                                    <tr>
                                        <td><strong>Paracetamol 500mg</strong></td>
                                        <td>500mg</td>
                                        <td>Sales Division</td>
                                        <td>
                                            
                                                <span class="badge badge-success">1435 units</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-primary">1319 available</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-info">2 batches</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-success">In Stock</span>
                                            
                                        </td>
                                        <td>
                                            
                                            <a href="/inventory/new?product_id=P001" class="btn btn-sm btn-success">
                                                <i class="fas fa-plus"></i> Add Stock
                                            </a>
                                            
                                        </td>
                                    </tr>
                                    
                                    <tr>
                                        <td><strong>Simvastatin 20mg</strong></td>
                                        <td>20mg</td>
                                        <td>Sales Division</td>
                                        <td>
                                            
                                                <span class="badge badge-warning">No Stock</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-secondary">0 available</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-light">No batches</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-info">Ready for Stock</span>
                                            
                                        </td>
                                        <td>
                                            
                                            <a href="/inventory/new?product_id=P015" class="btn btn-sm btn-success">
                                                <i class="fas fa-plus"></i> Add Stock
                                            </a>
                                            
                                        </td>
                                    </tr>
                                    
                                    <tr>
                                        <td><strong>Vitamin C 1000mg</strong></td>
                                        <td>1000mg</td>
                                        <td>Sales Division</td>
                                        <td>
                                            
                                                <span class="badge badge-warning">No Stock</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-secondary">0 available</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-light">No batches</span>
                                            
                                        </td>
                                        <td>
                                            
                                                <span class="badge badge-info">Ready for Stock</span>
                                            
                                        </td>
                                        <td>
                                            
                                            <a href="/inventory/new?product_id=P004" class="btn btn-sm btn-success">
                                                <i class="fas fa-plus"></i> Add Stock
                                            </a>
                                            
                                        </td>
                                    </tr>
                                    
                                </tbody>
                            </table>
                        </div>
                    </div>
                    

                    <!-- Inventory Records Section -->
                    <h5 class="text-primary">📋 Inventory Records</h5>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Product</th>
                                    <th>Strength</th>
                                    <th>Batch #</th>
                                    <th>Country</th>
                                    <th>Manufacturer</th>
                                    <th>Division</th>
                                    <th>Expiry Date</th>
                                    <th>Warehouse</th>
                                    <th>Quantity</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                
                                    
                                    <tr>
                                        <td>Cough Syrup</td>
                                        <td>100ml</td>
                                        <td><strong>02135</strong></td>
                                        <td>N/A</td>
                                        <td>Generic</td>
                                        <td>Sales Division</td>
                                        <td></td>
                                        <td>Karachi SMHC</td>
                                        <td>33 </td>
                                    <td>
                                        
                                        <a href="/inventory/INV20250728221554/toggle-status"
                                           class="status-toggle"
                                           data-toggle="tooltip"
                                           title="Click to toggle status between active and deactive">
                                            <span class="badge
                                                badge-success
                                                ">
                                                active
                                            </span>
                                        </a>
                                        
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="/inventory/INV20250728221554/history" class="btn btn-sm btn-info">History</a>
                                            
                                            <a href="/inventory/new" class="btn btn-sm btn-success">Add Stock</a>
                                            
                                            
                                            <a href="/inventory/transfer" class="btn btn-sm btn-primary">Transfer</a>
                                            
                                        </div>
                                    </td>
                                    </tr>
                                    
                                    <tr>
                                        <td>Cough Syrup</td>
                                        <td>100ml</td>
                                        <td><strong>233</strong></td>
                                        <td>N/A</td>
                                        <td>Generic</td>
                                        <td>Sales Division</td>
                                        <td></td>
                                        <td>Karachi SMHC</td>
                                        <td>3 </td>
                                    <td>
                                        
                                        <a href="/inventory/INV20250728221301/toggle-status"
                                           class="status-toggle"
                                           data-toggle="tooltip"
                                           title="Click to toggle status between active and deactive">
                                            <span class="badge
                                                badge-success
                                                ">
                                                active
                                            </span>
                                        </a>
                                        
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="/inventory/INV20250728221301/history" class="btn btn-sm btn-info">History</a>
                                            
                                            <a href="/inventory/new" class="btn btn-sm btn-success">Add Stock</a>
                                            
                                            
                                            <a href="/inventory/transfer" class="btn btn-sm btn-primary">Transfer</a>
                                            
                                        </div>
                                    </td>
                                    </tr>
                                    
                                    <tr>
                                        <td>Cough Syrup</td>
                                        <td>100ml</td>
                                        <td><strong>1655</strong></td>
                                        <td>N/A</td>
                                        <td>Generic</td>
                                        <td>Sales Division</td>
                                        <td></td>
                                        <td>Karachi SMHC</td>
                                        <td>4 </td>
                                    <td>
                                        
                                        <a href="/inventory/INV20250728220741/toggle-status"
                                           class="status-toggle"
                                           data-toggle="tooltip"
                                           title="Click to toggle status between active and deactive">
                                            <span class="badge
                                                badge-success
                                                ">
                                                active
                                            </span>
                                        </a>
                                        
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="/inventory/INV20250728220741/history" class="btn btn-sm btn-info">History</a>
                                            
                                            <a href="/inventory/new" class="btn btn-sm btn-success">Add Stock</a>
                                            
                                            
                                            <a href="/inventory/transfer" class="btn btn-sm btn-primary">Transfer</a>
                                            
                                        </div>
                                    </td>
                                    </tr>
                                    
                                    <tr>
                                        <td>Cough Syrup</td>
                                        <td>100ml</td>
                                        <td><strong>00302135</strong></td>
                                        <td>N/A</td>
                                        <td>Generic</td>
                                        <td>Sales Division</td>
                                        <td></td>
                                        <td>Karachi SMHC</td>
                                        <td>13 </td>
                                    <td>
                                        
                                        <a href="/inventory/INV20250728214056/toggle-status"
                                           class="status-toggle"
                                           data-toggle="tooltip"
                                           title="Click to toggle status between active and deactive">
                                            <span class="badge
                                                badge-success
                                                ">
                                                active
                                            </span>
                                        </a>
                                        
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="/inventory/INV20250728214056/history" class="btn btn-sm btn-info">History</a>
                                            
                                            <a href="/inventory/new" class="btn btn-sm btn-success">Add Stock</a>
                                            
                                            
                                            <a href="/inventory/transfer" class="btn btn-sm btn-primary">Transfer</a>
                                            
                                        </div>
                                    </td>
                                    </tr>
                                    
                                    <tr>
                                        <td>Cough Syrup</td>
                                        <td>100ml</td>
                                        <td><strong>33331</strong></td>
                                        <td>N/A</td>
                                        <td>Generic</td>
                                        <td>Sales Division</td>
                                        <td></td>
                                        <td>Karachi SMHC</td>
                                        <td>14 </td>
                                    <td>
                                        
                                        <a href="/inventory/INV20250728214015/toggle-status"
                                           class="status-toggle"
                                           data-toggle="tooltip"
                                           title="Click to toggle status between active and deactive">
                                            <span class="badge
                                                badge-success
                                                ">
                                                active
                                            </span>
                                        </a>
                                        
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="/inventory/INV20250728214015/history" class="btn btn-sm btn-info">History</a>
                                            
                                            <a href="/inventory/new" class="btn btn-sm btn-success">Add Stock</a>
                                            
                                            
                                            <a href="/inventory/transfer" class="btn btn-sm btn-primary">Transfer</a>
                                            
                                        </div>
                                    </td>
                                    </tr>
                                    
                                    <tr>
                                        <td>Cough Syrup</td>
                                        <td>100ml</td>
                                        <td><strong>3333</strong></td>
                                        <td>N/A</td>
                                        <td>Generic</td>
                                        <td>Sales Division</td>
                                        <td></td>
                                        <td>Karachi SMHC</td>
                                        <td>199 </td>
                                    <td>
                                        
                                        <a href="/inventory/INV20250728212443/toggle-status"
                                           class="status-toggle"
                                           data-toggle="tooltip"
                                           title="Click to toggle status between active and deactive">
                                            <span class="badge
                                                badge-success
                                                ">
                                                active
                                            </span>
                                        </a>
                                        
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="/inventory/INV20250728212443/history" class="btn btn-sm btn-info">History</a>
                                            
                                            <a href="/inventory/new" class="btn btn-sm btn-success">Add Stock</a>
                                            
                                            
                                            <a href="/inventory/transfer" class="btn btn-sm btn-primary">Transfer</a>
                                            
                                        </div>
                                    </td>
                                    </tr>
                                    
                                    <tr>
                                        <td>Amoxicillin 250mg</td>
                                        <td>250mg</td>
                                        <td><strong>1655</strong></td>
                                        <td>N/A</td>
                                        <td>Generic</td>
                                        <td>Sales Division</td>
                                        <td></td>
                                        <td>Karachi SMHC</td>
                                        <td>11 </td>
                                    <td>
                                        
                                        <a href="/inventory/INV20250728134309/toggle-status"
                                           class="status-toggle"
                                           data-toggle="tooltip"
                                           title="Click to toggle status between active and deactive">
                                            <span class="badge
                                                badge-success
                                                ">
                                                active
                                            </span>
                                        </a>
                                        
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="/inventory/INV20250728134309/history" class="btn btn-sm btn-info">History</a>
                                            
                                            <a href="/inventory/new" class="btn btn-sm btn-success">Add Stock</a>
                                            
                                            
                                            <a href="/inventory/transfer" class="btn btn-sm btn-primary">Transfer</a>
                                            
                                        </div>
                                    </td>
                                    </tr>
                                    
                                    <tr>
                                        <td>Paracetamol 500mg</td>
                                        <td>500mg</td>
                                        <td><strong>02135</strong></td>
                                        <td>N/A</td>
                                        <td>Generic</td>
                                        <td>Sales Division</td>
                                        <td></td>
                                        <td>Karachi SMHC</td>
                                        <td>435 </td>
                                    <td>
                                        
                                        <a href="/inventory/INV20250725113138/toggle-status"
                                           class="status-toggle"
                                           data-toggle="tooltip"
                                           title="Click to toggle status between active and deactive">
                                            <span class="badge
                                                badge-success
                                                ">
                                                active
                                            </span>
                                        </a>
                                        
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="/inventory/INV20250725113138/history" class="btn btn-sm btn-info">History</a>
                                            
                                            <a href="/inventory/new" class="btn btn-sm btn-success">Add Stock</a>
                                            
                                            
                                            <a href="/inventory/transfer" class="btn btn-sm btn-primary">Transfer</a>
                                            
                                        </div>
                                    </td>
                                    </tr>
                                    
                                    <tr>
                                        <td>Amoxicillin 250mg</td>
                                        <td>250mg</td>
                                        <td><strong>02135</strong></td>
                                        <td>N/A</td>
                                        <td>Generic</td>
                                        <td>Sales Division</td>
                                        <td></td>
                                        <td>Karachi SMHC</td>
                                        <td>2000 </td>
                                    <td>
                                        
                                        <a href="/inventory/INV20250725111623/toggle-status"
                                           class="status-toggle"
                                           data-toggle="tooltip"
                                           title="Click to toggle status between active and deactive">
                                            <span class="badge
                                                badge-success
                                                ">
                                                active
                                            </span>
                                        </a>
                                        
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="/inventory/INV20250725111623/history" class="btn btn-sm btn-info">History</a>
                                            
                                            <a href="/inventory/new" class="btn btn-sm btn-success">Add Stock</a>
                                            
                                            
                                            <a href="/inventory/transfer" class="btn btn-sm btn-primary">Transfer</a>
                                            
                                        </div>
                                    </td>
                                    </tr>
                                    
                                    <tr>
                                        <td>Paracetamol 500mg</td>
                                        <td>500mg</td>
                                        <td><strong>BATCHP001001</strong></td>
                                        <td>N/A</td>
                                        <td>Generic</td>
                                        <td>Sales Division</td>
                                        <td></td>
                                        <td>Main Warehouse</td>
                                        <td>1000 </td>
                                    <td>
                                        
                                        <a href="/inventory/INV_P001_WH001_001/toggle-status"
                                           class="status-toggle"
                                           data-toggle="tooltip"
                                           title="Click to toggle status between active and deactive">
                                            <span class="badge
                                                badge-success
                                                ">
                                                active
                                            </span>
                                        </a>
                                        
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="/inventory/INV_P001_WH001_001/history" class="btn btn-sm btn-info">History</a>
                                            
                                            <a href="/inventory/new" class="btn btn-sm btn-success">Add Stock</a>
                                            
                                            
                                            <a href="/inventory/transfer" class="btn btn-sm btn-primary">Transfer</a>
                                            
                                        </div>
                                    </td>
                                    </tr>
                                    
                                    <tr>
                                        <td>Amoxicillin 250mg</td>
                                        <td>250mg</td>
                                        <td><strong>BATCHP002001</strong></td>
                                        <td>N/A</td>
                                        <td>Generic</td>
                                        <td>Sales Division</td>
                                        <td></td>
                                        <td>Main Warehouse</td>
                                        <td>1000 </td>
                                    <td>
                                        
                                        <a href="/inventory/INV_P002_WH001_002/toggle-status"
                                           class="status-toggle"
                                           data-toggle="tooltip"
                                           title="Click to toggle status between active and deactive">
                                            <span class="badge
                                                badge-success
                                                ">
                                                active
                                            </span>
                                        </a>
                                        
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="/inventory/INV_P002_WH001_002/history" class="btn btn-sm btn-info">History</a>
                                            
                                            <a href="/inventory/new" class="btn btn-sm btn-success">Add Stock</a>
                                            
                                            
                                            <a href="/inventory/transfer" class="btn btn-sm btn-primary">Transfer</a>
                                            
                                        </div>
                                    </td>
                                    </tr>
                                    
                                
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

                </div>
            </div>
        </div>
    </div>

    <!-- Loading Spinner -->
    <div class="loading-spinner">
        <div class="spinner"></div>
    </div>

    <!-- Notification Container -->
    <div class="notification-container"></div>

    <!-- Bootstrap JS, Popper.js, and jQuery -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>

    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap4.min.js"></script>

    <!-- Select2 JS for enhanced dropdowns -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <!-- Plotly.js -->
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>

    <!-- jsPDF and AutoTable for PDF export -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.28/jspdf.plugin.autotable.min.js"></script>

    <!-- Custom JS -->
    <script>
        $(document).ready(function() {
            // Add data-title attribute to all sidebar menu items
            $('.sidebar .nav-link span').each(function() {
                var text = $(this).text();
                $(this).attr('data-title', text);
            });

            // Make sidebar menu items show full text on hover
            $('.sidebar .nav-link').hover(
                function() {
                    // On hover in
                    var $span = $(this).find('span');
                    var text = $span.text();

                    // Check if text is truncated
                    if (this.offsetWidth < this.scrollWidth) {
                        // Create a tooltip
                        $('<div class="menu-tooltip">' + text + '</div>')
                            .css({
                                position: 'absolute',
                                left: '100%',
                                top: '0',
                                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                color: 'white',
                                padding: '5px 10px',
                                borderRadius: '3px',
                                zIndex: 999,
                                marginLeft: '10px',
                                whiteSpace: 'nowrap'
                            })
                            .appendTo(this);
                    }
                },
                function() {
                    // On hover out
                    $(this).find('.menu-tooltip').remove();
                }
            );

            // Fix submenu toggle functionality
            $('.nav-link.dropdown-toggle').on('click', function(e) {
                e.preventDefault();
                var target = $(this).attr('href');
                $(target).collapse('toggle');

                // Update the aria-expanded attribute
                var isExpanded = $(this).attr('aria-expanded') === 'true';
                $(this).attr('aria-expanded', !isExpanded);
            });

            // Ensure active submenu is expanded on page load
            $('.sidebar .nav-link.active').each(function() {
                // Find parent dropdown if this is a submenu item
                var parentDropdown = $(this).closest('.collapse.list-unstyled');
                if (parentDropdown.length) {
                    // Get the dropdown toggle that controls this submenu
                    var dropdownToggle = $('[href="#' + parentDropdown.attr('id') + '"]');
                    // Expand the submenu
                    parentDropdown.addClass('show');
                    // Update the toggle button state
                    dropdownToggle.attr('aria-expanded', 'true');
                }
            });
        });

        // Universal Back Button Function
        window.goBack = function() {
            // Check if there's a previous page in history
            if (window.history.length > 1) {
                window.history.back();
            } else {
                // If no history, go to dashboard
                window.location.href = "/dashboard";
            }
        };

        // Add keyboard shortcut for back (Alt + Left Arrow)
        document.addEventListener('keydown', function(event) {
            if (event.altKey && event.key === 'ArrowLeft') {
                event.preventDefault();
                goBack();
            }
        });

        // Enhanced UI/UX Functions

        // Mobile Sidebar Toggle
        window.toggleSidebar = function() {
            const sidebar = document.getElementById('sidebarMenu');
            const overlay = document.getElementById('sidebarOverlay');

            sidebar.classList.toggle('show');
            overlay.classList.toggle('show');
        };

        // Desktop Sidebar Minimize/Maximize Toggle
        window.toggleSidebarMinimize = function() {
            const sidebar = document.getElementById('sidebarMenu');
            const mainContent = document.getElementById('mainContent');
            const toggleIcon = document.getElementById('toggleIcon');

            sidebar.classList.toggle('minimized');
            mainContent.classList.toggle('sidebar-minimized');

            // Update toggle icon
            if (sidebar.classList.contains('minimized')) {
                toggleIcon.className = 'fas fa-chevron-right';
                // Store minimized state
                localStorage.setItem('sidebarMinimized', 'true');
            } else {
                toggleIcon.className = 'fas fa-chevron-left';
                // Store expanded state
                localStorage.setItem('sidebarMinimized', 'false');
            }
        };

        // Restore sidebar state on page load
        document.addEventListener('DOMContentLoaded', function() {
            const isMinimized = localStorage.getItem('sidebarMinimized') === 'true';
            if (isMinimized) {
                const sidebar = document.getElementById('sidebarMenu');
                const mainContent = document.getElementById('mainContent');
                const toggleIcon = document.getElementById('toggleIcon');

                sidebar.classList.add('minimized');
                mainContent.classList.add('sidebar-minimized');
                toggleIcon.className = 'fas fa-chevron-right';
            }
        });

        // Close sidebar when clicking overlay
        document.getElementById('sidebarOverlay').addEventListener('click', function() {
            toggleSidebar();
        });

        // Global Search Functionality
        let searchTimeout;
        const globalSearchInput = document.getElementById('globalSearch');
        const globalSearchSuggestions = document.getElementById('globalSearchSuggestions');

        if (globalSearchInput) {
            globalSearchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                const query = this.value.trim();

                if (query.length < 2) {
                    globalSearchSuggestions.style.display = 'none';
                    return;
                }

                searchTimeout = setTimeout(() => {
                    fetchGlobalSearchSuggestions(query);
                }, 300);
            });

            // Keyboard navigation for search
            globalSearchInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    performGlobalSearch();
                } else if (e.key === 'Escape') {
                    globalSearchSuggestions.style.display = 'none';
                }
            });
        }

        window.fetchGlobalSearchSuggestions = function(query) {
            fetch(`/api/search-suggestions?q=${encodeURIComponent(query)}&limit=5`)
                .then(response => response.json())
                .then(data => {
                    if (data.suggestions && data.suggestions.length > 0) {
                        showGlobalSearchSuggestions(data.suggestions);
                    } else {
                        globalSearchSuggestions.style.display = 'none';
                    }
                })
                .catch(error => {
                    console.error('Search suggestions error:', error);
                });
        };

        window.showGlobalSearchSuggestions = function(suggestions) {
            let html = '';
            suggestions.forEach(suggestion => {
                const icon = getSearchTypeIcon(suggestion.type);
                html += `
                    <div class="search-suggestion-item" onclick="selectGlobalSearchSuggestion('${suggestion.text}', '${suggestion.type}', '${suggestion.id}')">
                        <i class="${icon}"></i> ${suggestion.text}
                        <small class="text-muted ml-2">${suggestion.type}</small>
                    </div>
                `;
            });

            globalSearchSuggestions.innerHTML = html;
            globalSearchSuggestions.style.display = 'block';
        };

        window.selectGlobalSearchSuggestion = function(text, type, id) {
            globalSearchInput.value = text;
            globalSearchSuggestions.style.display = 'none';

            // Navigate to specific page based on type
            if (type === 'customer') {
                window.location.href = `/finance/customer-statement/${id}`;
            } else if (type === 'product') {
                window.location.href = `/products/${id}`;
            } else if (type === 'order') {
                window.location.href = `/orders/${id}/view`;
            }
        };

        window.performGlobalSearch = function() {
            const query = globalSearchInput.value.trim();
            if (query) {
                window.location.href = `/search?q=${encodeURIComponent(query)}`;
            }
        };

        window.getSearchTypeIcon = function(type) {
            switch(type) {
                case 'customer': return 'fas fa-user';
                case 'product': return 'fas fa-pills';
                case 'order': return 'fas fa-shopping-cart';
                default: return 'fas fa-search';
            }
        };

        // Loading Spinner Functions
        window.showLoadingSpinner = function() {
            const spinner = document.querySelector('.loading-spinner');
            if (spinner) {
                spinner.style.display = 'flex';
            }
        };

        window.hideLoadingSpinner = function() {
            const spinner = document.querySelector('.loading-spinner');
            if (spinner) {
                spinner.style.display = 'none';
            }
        };

        // Enhanced Button Loading States
        window.setButtonLoading = function(button, loading = true) {
            if (loading) {
                button.classList.add('btn-loading');
                button.disabled = true;
            } else {
                button.classList.remove('btn-loading');
                button.disabled = false;
            }
        };

        // Notification System
        window.showNotification = function(message, type = 'info', duration = 5000) {
            const container = document.querySelector('.notification-container') || createNotificationContainer();

            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <span>${message}</span>
                    <button type="button" class="btn-close" onclick="this.parentElement.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            container.appendChild(notification);

            // Auto-remove after duration
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, duration);
        };

        function createNotificationContainer() {
            const container = document.createElement('div');
            container.className = 'notification-container';
            document.body.appendChild(container);
            return container;
        }

        // Data Export Functions
        window.exportTableToCSV = function(tableId, filename = 'export.csv') {
            const table = document.getElementById(tableId);
            if (!table) return;

            const rows = Array.from(table.querySelectorAll('tr'));
            const csvContent = rows.map(row => {
                const cells = Array.from(row.querySelectorAll('th, td'));
                return cells.map(cell => `"${cell.textContent.trim()}"`).join(',');
            }).join('\n');

            downloadCSV(csvContent, filename);
        };

        window.downloadCSV = function(csvContent, filename) {
            const blob = new Blob([csvContent], { type: 'text/csv;charset="utf-8;'" });
            const link = document.createElement('a');

            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', filename);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        };

        // Enhanced Form Validation
        window.validateForm = function(formId) {
            const form = document.getElementById(formId);
            if (!form) return false;

            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            });

            return isValid;
        };

        // Auto-save functionality
        window.enableAutoSave = function(formId, saveUrl, interval = 30000) {
            const form = document.getElementById(formId);
            if (!form) return;

            setInterval(() => {
                const formData = new FormData(form);
                fetch(saveUrl, {
                    method: 'POST',
                    body: formData
                }).then(response => {
                    if (response.ok) {
                        showNotification('Auto-saved', 'success', 2000);
                    }
                }).catch(error => {
                    console.error('Auto-save error:', error);
                });
            }, interval);
        };

        // Initialize enhanced features on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Add loading spinner to body if not exists
            if (!document.querySelector('.loading-spinner')) {
                const spinner = document.createElement('div');
                spinner.className = 'loading-spinner';
                spinner.innerHTML = '<div class="spinner"></div>';
                document.body.appendChild(spinner);
            }

            // Enhance all tables
            document.querySelectorAll('.table').forEach(table => {
                table.classList.add('table-enhanced');
            });

            // Add export buttons to tables with data
            document.querySelectorAll('.table-responsive').forEach(container => {
                const table = container.querySelector('table');
                if (table && table.querySelectorAll('tbody tr').length > 0) {
                    addExportButtons(container, table);
                }
            });
        });

        function addExportButtons(container, table) {
            const exportDiv = document.createElement('div');
            exportDiv.className = 'export-buttons text-right mb-2';
            exportDiv.innerHTML = `
                <button class="btn btn-sm btn-outline-success export-btn" onclick="exportTableToCSV('${table.id || 'table'}', 'data.csv')">
                    <i class="fas fa-file-csv"></i> Export CSV
                </button>
                <button class="btn btn-sm btn-outline-primary export-btn" onclick="window.print()">
                    <i class="fas fa-print"></i> Print
                </button>
            `;

            container.insertBefore(exportDiv, container.firstChild);
        }

        // ============================================================================
        // REAL-TIME NOTIFICATION SYSTEM
        // ============================================================================

        // Notification system variables
        let notificationUpdateInterval;

        // Initialize notification system
        function initializeNotifications() {
            loadNotifications();
            startNotificationUpdates();
        }

        // Load notifications into dropdown
        function loadNotifications() {
            fetch('/notifications/api/notifications?limit=10')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateNotificationDropdown(data.notifications);
                        // Also get unread count
                        return fetch('/notifications/api/unread-count');
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateNotificationCount(data.unread_count || 0);
                    }
                })
                .catch(error => {
                    console.error('Error loading notifications:', error);
                    document.getElementById('notification-dropdown-list').innerHTML = `
                        <div class="dropdown-item text-center text-muted">
                            <i class="fas fa-exclamation-triangle"></i> Error loading notifications
                        </div>
                    `;
                });
        }

        // Update notification dropdown content
        function updateNotificationDropdown(notifications) {
            const dropdownList = document.getElementById('notification-dropdown-list');

            if (notifications.length === 0) {
                dropdownList.innerHTML = `
                    <div class="dropdown-item text-center text-muted">
                        <i class="fas fa-bell-slash"></i><br>
                        No notifications
                    </div>
                `;
                return;
            }

            let html = '';
            notifications.slice(0, 5).forEach(notification => {
                const timeAgo = getTimeAgo(notification.created_at);
                const isUnread = !notification.is_read;
                const actionUrl = notification.data && notification.data.action_url ? notification.data.action_url : '#';

                html += `
                    <div class="dropdown-item notification-item ${isUnread ? 'unread' : ''}"
                         onclick="handleNotificationClick(${notification.id}, '${actionUrl}')"
                         style="cursor: pointer; ${isUnread ? 'background-color: #e3f2fd; border-left: 3px solid #007bff;' : ''}">
                        <div class="d-flex align-items-start">
                            <div class="mr-2">
                                <div class="notification-icon-small bg-${notification.color_class}" style="width: 32px; height: 32px; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white;">
                                    <i class="${notification.icon_class}" style="font-size: 0.8rem;"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1 font-weight-bold" style="font-size: 0.85rem;">${notification.title}</h6>
                                <p class="mb-1 text-muted" style="font-size: 0.75rem; line-height: 1.3;">${notification.message}</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">${timeAgo}</small>
                                    ${notification.priority_label && notification.priority_label !== 'Low' ?
                                        `<span class="badge badge-${getPriorityColor(notification.priority_label)} badge-sm">${notification.priority_label}</span>` : ''}
                                </div>
                            </div>
                            ${isUnread ? '<div class="ml-1"><div class="notification-unread-dot" style="width: 8px; height: 8px; background: #007bff; border-radius: 50%;"></div></div>' : ''}
                        </div>
                    </div>
                `;
            });

            dropdownList.innerHTML = html;
        }

        // Update notification count badge
        function updateNotificationCount(count) {
            const badge = document.querySelector('.notification-badge');
            if (count > 0) {
                badge.textContent = count > 99 ? '99+' : count;
                badge.style.display = 'block';
            } else {
                badge.style.display = 'none';
            }
        }

        // Get priority color for badges
        function getPriorityColor(priority) {
            switch(priority.toLowerCase()) {
                case 'urgent': return 'danger';
                case 'high': return 'warning';
                case 'medium': return 'info';
                case 'low': return 'secondary';
                default: return 'secondary';
            }
        }

        // Mark all notifications as read
        function markAllNotificationsRead() {
            fetch('/notifications/api/mark-all-read', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadNotifications();
                    showToast(`Marked ${data.marked_count} notifications as read`, 'success');
                }
            })
            .catch(error => {
                console.error('Error marking all as read:', error);
                showToast('Failed to mark all as read', 'error');
            });
        }

        // Handle notification click
        function handleNotificationClick(notificationId, actionUrl) {
            // Mark as read
            fetch(`/notifications/api/mark-read/${notificationId}`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Reload notifications
                    loadNotifications();
                }
            })
            .catch(error => {
                console.error('Error marking notification as read:', error);
            });

            // Navigate to action URL
            if (actionUrl && actionUrl !== '#') {
                setTimeout(() => {
                    window.location.href = actionUrl;
                }, 200);
            }
        }

        // Toast notification function
        function showToast(message, type = 'info') {
            // Create toast element
            const toast = document.createElement('div');
            toast.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'primary'} border-0`;
            toast.setAttribute('role', 'alert');
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;

            // Add to toast container
            let toastContainer = document.querySelector('.toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                document.body.appendChild(toastContainer);
            }

            toastContainer.appendChild(toast);

            // Initialize and show toast
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();

            // Remove toast element after it's hidden
            toast.addEventListener('hidden.bs.toast', () => {
                toast.remove();
            });
        }

        // Start real-time updates
        function startNotificationUpdates() {
            // Update every 5 seconds for real-time feel
            notificationUpdateInterval = setInterval(loadNotifications, 5000);
        }

        // Global notification functions for all pages
        window.showGlobalNotification = function(title, message, type = 'info') {
            // Show screenshot-style notification
            const notification = document.createElement('div');
            notification.className = 'global-notification';
            notification.innerHTML = `
                <div class="global-notification-content">
                    <div class="global-notification-header">
                        <div class="global-notification-icon ${type}">
                            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'times-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
                        </div>
                        <div class="global-notification-details">
                            <h4 class="global-notification-title">${title}</h4>
                            <p class="global-notification-message">${message}</p>
                            <small class="global-notification-time">Just now</small>
                        </div>
                        <button class="global-notification-close" onclick="this.parentElement.parentElement.parentElement.remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            `;

            // Add styles if not already added
            if (!document.getElementById('global-notification-styles')) {
                const style = document.createElement('style');
                style.id = 'global-notification-styles';
                style.textContent = `
                    .global-notification {
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        z-index: 10000;
                        max-width: 400px;
                        opacity: 0;
                        transform: translateX(100%);
                        transition: all 0.3s ease;
                    }
                    .global-notification.show {
                        opacity: 1;
                        transform: translateX(0);
                    }
                    .global-notification-content {
                        background: white;
                        border-radius: 15px;
                        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
                        border: 1px solid #e9ecef;
                        overflow: hidden;
                    }
                    .global-notification-header {
                        padding: 20px;
                        display: flex;
                        align-items: flex-start;
                        gap: 15px;
                        position: relative;
                    }
                    .global-notification-icon {
                        width: 50px;
                        height: 50px;
                        border-radius: 12px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 1.5rem;
                        color: white;
                        flex-shrink: 0;
                    }
                    .global-notification-icon.success {
                        background: linear-gradient(135deg, #28a745, #20c997);
                    }
                    .global-notification-icon.warning {
                        background: linear-gradient(135deg, #ffc107, #fd7e14);
                    }
                    .global-notification-icon.error {
                        background: linear-gradient(135deg, #dc3545, #e83e8c);
                    }
                    .global-notification-icon.info {
                        background: linear-gradient(135deg, #007bff, #0056b3);
                    }
                    .global-notification-details {
                        flex: 1;
                    }
                    .global-notification-title {
                        font-size: 1.1rem;
                        font-weight: 600;
                        color: #2c3e50;
                        margin: 0 0 8px 0;
                    }
                    .global-notification-message {
                        color: #6c757d;
                        margin: 0 0 8px 0;
                        line-height: 1.4;
                    }
                    .global-notification-time {
                        color: #adb5bd;
                        font-size: 0.85rem;
                    }
                    .global-notification-close {
                        position: absolute;
                        top: 15px;
                        right: 15px;
                        background: none;
                        border: none;
                        color: #adb5bd;
                        font-size: 1.2rem;
                        cursor: pointer;
                        padding: 5px;
                        border-radius: 50%;
                        transition: all 0.2s ease;
                    }
                    .global-notification-close:hover {
                        background: #f8f9fa;
                        color: #6c757d;
                    }
                `;
                document.head.appendChild(style);
            }

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 300);
            }, 5000);
        };

        // Helper functions
        function getNotificationColor(type) {
            const colors = {
                'order': 'primary', 'warning': 'warning', 'success': 'success',
                'error': 'danger', 'info': 'info', 'system': 'secondary'
            };
            return colors[type] || 'primary';
        }

        function getTimeAgo(timestamp) {
            const now = new Date();
            const time = new Date(timestamp);
            const diffInSeconds = Math.floor((now - time) / 1000);

            if (diffInSeconds < 60) return 'Just now';
            if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
            if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
            return `${Math.floor(diffInSeconds / 86400)}d ago`;
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeNotifications();
        });

        // ==========================================
        // GOOGLE MAPS INTEGRATION FUNCTIONS
        // ==========================================

        // Enhanced Google Maps integration with error handling and multiple options
        window.openGoogleMaps = function(location, options = {}) {
            if (!location || location.trim() === '') {
                showNotification('No location specified', 'warning');
                return;
            }

            const encodedLocation = encodeURIComponent(location.trim());
            let googleMapsUrl;

            if (options.type === 'directions' && options.destination) {
                // For directions between two points
                const encodedDestination = encodeURIComponent(options.destination.trim());
                googleMapsUrl = `https://www.google.com/maps/dir/?api=1&origin=${encodedLocation}&destination=${encodedDestination}&travelmode=${options.travelmode || 'driving'}`;
            } else {
                // For single location search
                googleMapsUrl = `https://www.google.com/maps/search/?api=1&query=${encodedLocation}`;
            }

            // Open in new tab
            window.open(googleMapsUrl, '_blank');

            // Show notification
            const message = options.type === 'directions'
                ? `Opening directions from ${location} to ${options.destination}`
                : `Opening Google Maps for: ${location}`;
            showNotification(message, 'info');
        };

        // Specific function for rider locations
        window.openRiderLocationOnMap = function(location, riderName) {
            if (!location || location.trim() === '') {
                showNotification(`No location available for rider ${riderName}`, 'warning');
                return;
            }
            openGoogleMaps(location, { context: `rider ${riderName}` });
        };

        // Specific function for customer addresses
        window.openCustomerAddressOnMap = function(address, customerName) {
            if (!address || address.trim() === '') {
                showNotification(`No address available for customer ${customerName}`, 'warning');
                return;
            }
            openGoogleMaps(address, { context: `customer ${customerName}` });
        };

        // Function for delivery routes with optimization
        window.openDeliveryRoute = function(origin, destination, riderName) {
            if (!origin || !destination) {
                showNotification('Origin and destination required for route', 'warning');
                return;
            }
            openGoogleMaps(origin, {
                type: 'directions',
                destination: destination,
                travelmode: 'driving',
                context: `delivery route for ${riderName}`
            });
        };

        // Function for route optimization (multiple stops)
        window.openOptimizedRoute = function(stops, riderName) {
            if (!stops || stops.length < 2) {
                showNotification('At least 2 stops required for route optimization', 'warning');
                return;
            }

            // Create waypoints string for Google Maps
            const origin = encodeURIComponent(stops[0]);
            const destination = encodeURIComponent(stops[stops.length - 1]);
            const waypoints = stops.slice(1, -1).map(stop => encodeURIComponent(stop)).join('|');

            let googleMapsUrl = `https://www.google.com/maps/dir/?api=1&origin=${origin}&destination=${destination}&travelmode=driving`;
            if (waypoints) {
                googleMapsUrl += `&waypoints=${waypoints}`;
            }

            window.open(googleMapsUrl, '_blank');
            showNotification(`Opening optimized route for ${riderName} with ${stops.length} stops`, 'info');
        };
    </script>

    <!-- Real-Time Updates Scripts -->
    <script src="/static/js/realtime-updates.js"></script>
    <script src="/static/js/realtime-settings.js"></script>

    <!-- User Activity Tracking Script -->
    
    <script src="/static/js/activity-tracker.js"></script>
    <script>
        // Add logged-in class to body for activity tracker detection
        document.body.classList.add('logged-in');
    </script>
    

    
<script>
    $(document).ready(function(){
        // Initialize tooltips
        $('[data-toggle="tooltip"]').tooltip();
    });
</script>

</body>
</html>