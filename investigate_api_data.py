#!/usr/bin/env python3
"""
Investigate API Data Loading Issues
"""

import requests
import json

def test_order_details_api():
    """Test the order details API endpoint"""
    print("🔍 TESTING ORDER DETAILS API")
    print("=" * 50)
    
    try:
        # Test with ORD00000165 (visible in the modal screenshot)
        order_id = 'ORD00000165'
        url = f'http://127.0.0.1:5001/api/order-details/{order_id}'
        
        print(f"Testing: {url}")
        response = requests.get(url, timeout=10)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"Success: {data.get('success', False)}")
                
                if data.get('success'):
                    print("\n📊 ORDER DATA:")
                    order = data.get('order', {})
                    print(f"   Order ID: {order.get('order_id', 'N/A')}")
                    print(f"   Customer: {order.get('customer_name', 'N/A')}")
                    print(f"   Status: {order.get('status', 'N/A')}")
                    print(f"   Amount: {order.get('order_amount', 'N/A')}")
                    
                    print("\n📦 ORDER ITEMS:")
                    items = data.get('order_items', [])
                    print(f"   Total items: {len(items)}")
                    
                    for i, item in enumerate(items[:3]):  # Show first 3 items
                        print(f"   Item {i+1}:")
                        print(f"     Product: {item.get('product_name', 'N/A')}")
                        print(f"     Quantity: {item.get('quantity', 'N/A')}")
                        print(f"     Price: {item.get('unit_price', 'N/A')}")
                    
                    if len(items) > 3:
                        print(f"   ... and {len(items) - 3} more items")
                    
                    print("\n📋 SUMMARY:")
                    summary = data.get('summary', {})
                    print(f"   Total Items: {summary.get('total_items', 'N/A')}")
                    print(f"   Total Quantity: {summary.get('total_quantity', 'N/A')}")
                    print(f"   Subtotal: {summary.get('subtotal', 'N/A')}")
                    
                    return True
                else:
                    print(f"❌ API Error: {data.get('message', 'Unknown error')}")
                    return False
                    
            except json.JSONDecodeError:
                print("❌ Invalid JSON response")
                print(f"Response: {response.text[:200]}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_qr_code_api():
    """Test the QR code API endpoint"""
    print("\n🔍 TESTING QR CODE API")
    print("=" * 50)
    
    try:
        order_id = 'ORD00000165'
        url = f'http://127.0.0.1:5001/api/order-qr-code/{order_id}'
        
        print(f"Testing: {url}")
        response = requests.get(url, timeout=10)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            content_type = response.headers.get('content-type', '')
            print(f"Content-Type: {content_type}")
            print(f"Content-Length: {len(response.content)} bytes")
            
            if 'image' in content_type:
                print("✅ QR Code image generated successfully")
                return True
            else:
                print("❌ Response is not an image")
                print(f"Response: {response.text[:200]}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_database_direct():
    """Test database directly to see if data exists"""
    print("\n🔍 TESTING DATABASE DIRECTLY")
    print("=" * 50)
    
    try:
        import sqlite3
        import os
        
        db_path = 'instance/medivent.db'
        if not os.path.exists(db_path):
            print(f"❌ Database not found: {db_path}")
            return False
            
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Check order ORD00000165
        order_id = 'ORD00000165'
        print(f"Checking order: {order_id}")
        
        cursor.execute("SELECT * FROM orders WHERE order_id = ?", (order_id,))
        order = cursor.fetchone()
        
        if order:
            print("✅ Order found in database:")
            print(f"   Customer: {order['customer_name']}")
            print(f"   Status: {order['status']}")
            print(f"   Amount: {order['order_amount']}")
            
            # Check order items
            cursor.execute("""
                SELECT oi.*, p.name as product_name, p.strength, p.manufacturer
                FROM order_items oi
                LEFT JOIN products p ON oi.product_id = p.product_id
                WHERE oi.order_id = ?
            """, (order_id,))
            
            items = cursor.fetchall()
            print(f"\n📦 Order Items: {len(items)} found")
            
            for i, item in enumerate(items[:3]):
                print(f"   Item {i+1}:")
                print(f"     Product: {item['product_name'] or 'Unknown'}")
                print(f"     Quantity: {item['quantity']}")
                print(f"     Price: {item['unit_price']}")
            
            conn.close()
            return True
        else:
            print(f"❌ Order {order_id} not found in database")
            
            # List available orders
            cursor.execute("SELECT order_id, customer_name FROM orders LIMIT 5")
            orders = cursor.fetchall()
            print("\n📋 Available orders:")
            for order in orders:
                print(f"   {order['order_id']} - {order['customer_name']}")
            
            conn.close()
            return False
            
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def main():
    """Run comprehensive API investigation"""
    print("🚀 COMPREHENSIVE API INVESTIGATION")
    print("=" * 70)
    
    # Test database first
    db_ok = test_database_direct()
    
    # Test API endpoints
    api_ok = test_order_details_api()
    qr_ok = test_qr_code_api()
    
    print("\n" + "=" * 70)
    print("📊 INVESTIGATION RESULTS")
    print("=" * 70)
    
    print(f"Database: {'✅ OK' if db_ok else '❌ ISSUES'}")
    print(f"Order Details API: {'✅ OK' if api_ok else '❌ ISSUES'}")
    print(f"QR Code API: {'✅ OK' if qr_ok else '❌ ISSUES'}")
    
    if api_ok and qr_ok:
        print("\n🎯 ISSUE IDENTIFIED:")
        print("   ✅ APIs are working correctly")
        print("   ❌ JavaScript is not calling the APIs")
        print("   📋 Need to modify JavaScript to fetch real data")
    else:
        print("\n🎯 ISSUES IDENTIFIED:")
        if not api_ok:
            print("   ❌ Order Details API not working")
        if not qr_ok:
            print("   ❌ QR Code API not working")
        if not db_ok:
            print("   ❌ Database issues")

if __name__ == "__main__":
    main()
