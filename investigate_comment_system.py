#!/usr/bin/env python3
"""
Comprehensive investigation of comment and history system
"""

import sqlite3
from datetime import datetime

def investigate_comment_system():
    print("🔍 COMPREHENSIVE COMMENT SYSTEM INVESTIGATION")
    print("=" * 80)
    
    conn = sqlite3.connect('instance/medivent.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    # Step 1: Check all comment-related tables
    print("\n📋 Step 1: Comment-Related Tables")
    print("-" * 50)
    
    # Check for comment tables
    cursor.execute("""
        SELECT name FROM sqlite_master 
        WHERE type='table' AND (
            name LIKE '%comment%' OR 
            name LIKE '%activity%' OR 
            name LIKE '%log%' OR 
            name LIKE '%history%' OR
            name LIKE '%hold%'
        )
        ORDER BY name
    """)
    
    comment_tables = cursor.fetchall()
    print("Found comment/history related tables:")
    for table in comment_tables:
        print(f"  ✅ {table['name']}")
        
        # Get table schema
        cursor.execute(f"PRAGMA table_info({table['name']})")
        columns = cursor.fetchall()
        print(f"     Columns: {', '.join([col['name'] for col in columns])}")
        
        # Get record count
        cursor.execute(f"SELECT COUNT(*) as count FROM {table['name']}")
        count = cursor.fetchone()['count']
        print(f"     Records: {count}")
        print()
    
    # Step 2: Check invoice_holds table for comment fields
    print("\n📋 Step 2: Invoice Holds Comment Fields")
    print("-" * 50)
    
    try:
        cursor.execute("PRAGMA table_info(invoice_holds)")
        holds_columns = cursor.fetchall()
        
        comment_fields = [col for col in holds_columns if 'comment' in col['name'].lower()]
        print("Comment fields in invoice_holds:")
        for field in comment_fields:
            print(f"  ✅ {field['name']} ({field['type']})")
        
        # Check sample data
        cursor.execute("""
            SELECT order_id, hold_comments, release_comments, status 
            FROM invoice_holds 
            ORDER BY hold_date DESC 
            LIMIT 5
        """)
        sample_holds = cursor.fetchall()
        
        print("\nSample hold records:")
        for hold in sample_holds:
            print(f"  Order: {hold['order_id']}")
            print(f"    Hold Comments: {hold['hold_comments']}")
            print(f"    Release Comments: {hold['release_comments']}")
            print(f"    Status: {hold['status']}")
            print()
            
    except Exception as e:
        print(f"❌ Error checking invoice_holds: {e}")
    
    # Step 3: Check finance_comments table
    print("\n📋 Step 3: Finance Comments Table")
    print("-" * 50)
    
    try:
        cursor.execute("SELECT COUNT(*) as count FROM finance_comments")
        comment_count = cursor.fetchone()['count']
        print(f"Total finance comments: {comment_count}")
        
        if comment_count > 0:
            cursor.execute("""
                SELECT entity_type, entity_id, comment_text, comment_type, created_by, created_at
                FROM finance_comments 
                ORDER BY created_at DESC 
                LIMIT 10
            """)
            recent_comments = cursor.fetchall()
            
            print("\nRecent finance comments:")
            for comment in recent_comments:
                print(f"  {comment['entity_type']} {comment['entity_id']}: {comment['comment_text'][:50]}...")
                print(f"    By: {comment['created_by']} | Type: {comment['comment_type']} | Date: {comment['created_at']}")
                print()
        
    except Exception as e:
        print(f"❌ Error checking finance_comments: {e}")
    
    # Step 4: Check activity_logs table
    print("\n📋 Step 4: Activity Logs Table")
    print("-" * 50)
    
    try:
        cursor.execute("SELECT COUNT(*) as count FROM activity_logs")
        log_count = cursor.fetchone()['count']
        print(f"Total activity logs: {log_count}")
        
        if log_count > 0:
            cursor.execute("""
                SELECT username, action, entity_id, details, module, timestamp
                FROM activity_logs 
                WHERE action LIKE '%HOLD%' OR action LIKE '%RELEASE%'
                ORDER BY timestamp DESC 
                LIMIT 10
            """)
            hold_logs = cursor.fetchall()
            
            print("\nHold/Release activity logs:")
            for log in hold_logs:
                print(f"  {log['action']} on {log['entity_id']}")
                print(f"    By: {log['username']} | Details: {log['details']}")
                print(f"    Module: {log['module']} | Time: {log['timestamp']}")
                print()
        
    except Exception as e:
        print(f"❌ Error checking activity_logs: {e}")
    
    # Step 5: Check specific order ORD00000243
    print("\n📋 Step 5: Order ORD00000243 Comment History")
    print("-" * 50)
    
    order_id = 'ORD00000243'
    
    # Check hold comments
    try:
        cursor.execute("""
            SELECT hold_comments, release_comments, status, hold_date, release_date
            FROM invoice_holds 
            WHERE order_id = ?
        """, (order_id,))
        hold_data = cursor.fetchone()
        
        if hold_data:
            print(f"Hold record for {order_id}:")
            print(f"  Hold Comments: {hold_data['hold_comments']}")
            print(f"  Release Comments: {hold_data['release_comments']}")
            print(f"  Status: {hold_data['status']}")
            print(f"  Hold Date: {hold_data['hold_date']}")
            print(f"  Release Date: {hold_data['release_date']}")
        else:
            print(f"❌ No hold record found for {order_id}")
    except Exception as e:
        print(f"❌ Error checking hold data: {e}")
    
    # Check finance comments
    try:
        cursor.execute("""
            SELECT comment_text, comment_type, created_by, created_at
            FROM finance_comments 
            WHERE entity_id = ?
            ORDER BY created_at DESC
        """, (order_id,))
        order_comments = cursor.fetchall()
        
        print(f"\nFinance comments for {order_id}:")
        if order_comments:
            for comment in order_comments:
                print(f"  {comment['comment_type']}: {comment['comment_text']}")
                print(f"    By: {comment['created_by']} | Date: {comment['created_at']}")
                print()
        else:
            print(f"  ❌ No finance comments found for {order_id}")
    except Exception as e:
        print(f"❌ Error checking finance comments: {e}")
    
    # Check activity logs
    try:
        cursor.execute("""
            SELECT username, action, details, timestamp
            FROM activity_logs 
            WHERE entity_id = ?
            ORDER BY timestamp DESC
        """, (order_id,))
        order_logs = cursor.fetchall()
        
        print(f"\nActivity logs for {order_id}:")
        if order_logs:
            for log in order_logs:
                print(f"  {log['action']}: {log['details']}")
                print(f"    By: {log['username']} | Date: {log['timestamp']}")
                print()
        else:
            print(f"  ❌ No activity logs found for {order_id}")
    except Exception as e:
        print(f"❌ Error checking activity logs: {e}")
    
    conn.close()
    
    print("\n✅ Comment system investigation complete")
    return True

if __name__ == "__main__":
    investigate_comment_system()
