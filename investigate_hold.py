#!/usr/bin/env python3
import sqlite3

def investigate_hold_issue():
    conn = sqlite3.connect('instance/medivent.db')
    cursor = conn.cursor()
    
    print("=== INVESTIGATING ORD00000243 HOLD STATUS ===")
    
    # Check if order exists and its current status
    cursor.execute('SELECT order_id, customer_name, status, notes FROM orders WHERE order_id = ?', ('ORD00000243',))
    order = cursor.fetchone()
    if order:
        print(f'Order Status: {order[0]} | {order[1]} | {order[2]} | {order[3]}')
    else:
        print('Order ORD00000243 not found in orders table')
    
    # Check if hold record exists
    cursor.execute('SELECT * FROM invoice_holds WHERE order_id = ?', ('ORD00000243',))
    hold = cursor.fetchone()
    if hold:
        print(f'Hold Record Found: {hold}')
    else:
        print('No hold record found for ORD00000243')
    
    # Check all recent holds
    cursor.execute('SELECT order_id, hold_reason, status, hold_date FROM invoice_holds ORDER BY hold_date DESC LIMIT 5')
    recent_holds = cursor.fetchall()
    print('\nRecent holds:')
    for h in recent_holds:
        print(f'  {h[0]} | {h[1]} | {h[2]} | {h[3]}')
    
    conn.close()

if __name__ == "__main__":
    investigate_hold_issue()
