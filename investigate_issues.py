#!/usr/bin/env python3
"""
Comprehensive investigation of both database and routing issues
"""

import sqlite3
import os
import re

def investigate_database_schema():
    """Check the current database schema for missing columns"""
    print("🔍 INVESTIGATING DATABASE SCHEMA")
    print("=" * 60)
    
    db_path = 'instance/medivent.db'
    if not os.path.exists(db_path):
        print(f"❌ Database not found: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check orders table schema
        print("\n📋 ORDERS TABLE SCHEMA:")
        cursor.execute("PRAGMA table_info(orders)")
        orders_columns = cursor.fetchall()
        
        column_names = [col[1] for col in orders_columns]
        print(f"   Found {len(orders_columns)} columns:")
        for col in orders_columns:
            print(f"   - {col[1]} ({col[2]})")
        
        # Check for rating column specifically
        if 'rating' in column_names:
            print("   ✅ 'rating' column EXISTS in orders table")
        else:
            print("   ❌ 'rating' column MISSING from orders table")
        
        # Check for delivery_rating column
        if 'delivery_rating' in column_names:
            print("   ✅ 'delivery_rating' column EXISTS in orders table")
        else:
            print("   ❌ 'delivery_rating' column MISSING from orders table")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def investigate_routing_issue():
    """Check for dispatch_order vs dispatch_orders routing conflicts"""
    print("\n🚚 INVESTIGATING ROUTING ISSUE")
    print("=" * 60)
    
    # Check app.py for route definitions
    routes_found = []
    
    try:
        # Check main app.py
        if os.path.exists('app.py'):
            with open('app.py', 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Look for dispatch-related routes
            dispatch_patterns = [
                r"@app\.route\(['\"][^'\"]*dispatch[^'\"]*['\"].*?\)\s*\ndef\s+(\w+)",
                r"def\s+(dispatch_\w+)\s*\(",
            ]
            
            for pattern in dispatch_patterns:
                matches = re.findall(pattern, content, re.MULTILINE | re.DOTALL)
                for match in matches:
                    routes_found.append(('app.py', match))
        
        # Check routes directory
        routes_dir = 'routes'
        if os.path.exists(routes_dir):
            for filename in os.listdir(routes_dir):
                if filename.endswith('.py'):
                    filepath = os.path.join(routes_dir, filename)
                    try:
                        with open(filepath, 'r', encoding='utf-8') as f:
                            content = f.read()
                        
                        # Look for dispatch routes in blueprints
                        dispatch_patterns = [
                            r"@\w+_bp\.route\(['\"][^'\"]*dispatch[^'\"]*['\"].*?\)\s*\ndef\s+(\w+)",
                            r"def\s+(dispatch_\w+)\s*\(",
                        ]
                        
                        for pattern in dispatch_patterns:
                            matches = re.findall(pattern, content, re.MULTILINE | re.DOTALL)
                            for match in matches:
                                routes_found.append((filename, match))
                                
                    except Exception as e:
                        print(f"   ⚠️ Error reading {filepath}: {e}")
        
        print(f"\n📍 DISPATCH-RELATED ROUTES FOUND:")
        if routes_found:
            for file, route_func in routes_found:
                print(f"   • {file}: {route_func}")
        else:
            print("   ❌ No dispatch routes found")
        
        return routes_found
        
    except Exception as e:
        print(f"❌ Routing investigation error: {e}")
        return []

def find_template_references():
    """Find all template references to dispatch_order"""
    print("\n📄 INVESTIGATING TEMPLATE REFERENCES")
    print("=" * 60)
    
    template_refs = []
    
    # Search in templates directory
    templates_dir = 'templates'
    if os.path.exists(templates_dir):
        for root, dirs, files in os.walk(templates_dir):
            for file in files:
                if file.endswith(('.html', '.htm', '.jinja2')):
                    filepath = os.path.join(root, file)
                    try:
                        with open(filepath, 'r', encoding='utf-8') as f:
                            lines = f.readlines()
                        
                        for line_num, line in enumerate(lines, 1):
                            if 'dispatch_order' in line:
                                template_refs.append({
                                    'file': filepath,
                                    'line': line_num,
                                    'content': line.strip()
                                })
                                
                    except Exception as e:
                        print(f"   ⚠️ Error reading {filepath}: {e}")
    
    print(f"\n📍 TEMPLATE REFERENCES TO 'dispatch_order':")
    if template_refs:
        for ref in template_refs:
            print(f"   • {ref['file']}:{ref['line']}")
            print(f"     {ref['content']}")
    else:
        print("   ❌ No template references found")
    
    return template_refs

def find_rating_references():
    """Find all references to o.rating in the codebase"""
    print("\n⭐ INVESTIGATING 'o.rating' REFERENCES")
    print("=" * 60)
    
    rating_refs = []
    
    # Search in all Python files
    for root, dirs, files in os.walk('.'):
        # Skip certain directories
        if any(skip_dir in root for skip_dir in ['__pycache__', '.git', 'node_modules', 'venv']):
            continue
            
        for file in files:
            if file.endswith('.py'):
                filepath = os.path.join(root, file)
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Look for o.rating references
                    if 'o.rating' in content:
                        lines = content.split('\n')
                        for line_num, line in enumerate(lines, 1):
                            if 'o.rating' in line:
                                rating_refs.append({
                                    'file': filepath,
                                    'line': line_num,
                                    'content': line.strip()
                                })
                                
                except Exception as e:
                    print(f"   ⚠️ Error reading {filepath}: {e}")
    
    print(f"\n📍 'o.rating' REFERENCES FOUND:")
    if rating_refs:
        for ref in rating_refs:
            print(f"   • {ref['file']}:{ref['line']}")
            print(f"     {ref['content']}")
    else:
        print("   ❌ No 'o.rating' references found")
    
    return rating_refs

def main():
    """Run comprehensive investigation"""
    print("🚀 COMPREHENSIVE ISSUE INVESTIGATION")
    print("=" * 60)
    
    # 1. Database schema investigation
    db_ok = investigate_database_schema()
    
    # 2. Routing issue investigation
    routes = investigate_routing_issue()
    
    # 3. Template references investigation
    template_refs = find_template_references()
    
    # 4. Rating references investigation
    rating_refs = find_rating_references()
    
    # Summary
    print("\n📊 INVESTIGATION SUMMARY")
    print("=" * 60)
    print(f"🗄️ Database Schema: {'✅ OK' if db_ok else '❌ ISSUES'}")
    print(f"🚚 Dispatch Routes Found: {len(routes)}")
    print(f"📄 Template References: {len(template_refs)}")
    print(f"⭐ Rating References: {len(rating_refs)}")
    
    # Recommendations
    print("\n💡 RECOMMENDATIONS")
    print("=" * 60)
    
    if not db_ok:
        print("1. ❌ Fix database schema issues")
    
    if template_refs:
        print("2. ❌ Fix template routing references")
        for ref in template_refs:
            if 'dispatch_order' in ref['content'] and 'url_for' in ref['content']:
                print(f"   - Update {ref['file']}:{ref['line']}")
    
    if rating_refs:
        print("3. ❌ Fix database rating column references")
        for ref in rating_refs:
            print(f"   - Check {ref['file']}:{ref['line']}")
    
    return db_ok and len(template_refs) == 0 and len(rating_refs) == 0

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
