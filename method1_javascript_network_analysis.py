#!/usr/bin/env python3
"""
Method 1: JavaScript Console & Network Analysis
Deep investigation of JavaScript execution and network requests
"""

import os
import re

def analyze_enhanced_modal_javascript():
    """Analyze the enhanced modal JavaScript for potential issues"""
    print("🔍 METHOD 1: JAVASCRIPT & NETWORK ANALYSIS")
    print("=" * 60)
    
    print("\n1️⃣ ANALYZING ENHANCED MODAL JAVASCRIPT")
    print("-" * 40)
    
    try:
        with open('static/js/enhanced_modal.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        # Check for critical functions
        critical_functions = [
            'showOrderDetails',
            'loadOrderData',
            'showEnhancedOrderDetails',
            'populateOrderDetails',
            'showErrorState',
            'showLoadingState'
        ]
        
        print("🔍 Checking critical JavaScript functions:")
        for func in critical_functions:
            if f'function {func}' in js_content or f'{func}(' in js_content:
                print(f"   ✅ {func} - Found")
            else:
                print(f"   ❌ {func} - Missing")
        
        # Check API endpoint calls
        print("\n🔍 Checking API endpoint calls:")
        api_patterns = [
            r'/api/order-details/\$\{.*?\}',
            r'/api/order-qr-code/\$\{.*?\}',
            r'fetch\([\'"`]([^\'"`]+)[\'"`]\)',
        ]
        
        for pattern in api_patterns:
            matches = re.findall(pattern, js_content)
            if matches:
                print(f"   ✅ Found API calls: {matches}")
            else:
                print(f"   ⚠️ Pattern {pattern} not found")
        
        # Check error handling
        print("\n🔍 Checking error handling:")
        error_patterns = [
            'catch.*error',
            'showErrorState',
            'console.error',
            'try.*catch'
        ]
        
        for pattern in error_patterns:
            if re.search(pattern, js_content, re.IGNORECASE):
                print(f"   ✅ {pattern} - Found")
            else:
                print(f"   ❌ {pattern} - Missing")
        
        return True
        
    except Exception as e:
        print(f"❌ Error analyzing JavaScript: {e}")
        return False

def analyze_warehouse_packing_template():
    """Analyze the warehouse packing template for JavaScript integration"""
    print("\n2️⃣ ANALYZING WAREHOUSE PACKING TEMPLATE")
    print("-" * 40)
    
    try:
        with open('templates/warehouse/packing_dashboard.html', 'r', encoding='utf-8') as f:
            template_content = f.read()
        
        # Check for enhanced modal inclusion
        print("🔍 Checking enhanced modal integration:")
        
        if 'enhanced_modal.js' in template_content:
            print("   ✅ Enhanced modal JS included")
        else:
            print("   ❌ Enhanced modal JS not included")
            
        if 'enhanced_modal.css' in template_content:
            print("   ✅ Enhanced modal CSS included")
        else:
            print("   ❌ Enhanced modal CSS not included")
            
        if 'enhanced_order_modal.html' in template_content:
            print("   ✅ Enhanced modal template included")
        else:
            print("   ❌ Enhanced modal template not included")
        
        # Check for viewOrderDetails function calls
        print("\n🔍 Checking function calls:")
        
        if 'viewOrderDetails(' in template_content:
            print("   ✅ viewOrderDetails function called")
            # Extract the calls
            calls = re.findall(r'viewOrderDetails\([\'"`]([^\'"`]+)[\'"`]\)', template_content)
            print(f"   📋 Function calls found: {calls}")
        else:
            print("   ❌ viewOrderDetails function not called")
            
        if 'showEnhancedOrderDetails' in template_content:
            print("   ✅ showEnhancedOrderDetails referenced")
        else:
            print("   ❌ showEnhancedOrderDetails not referenced")
        
        # Check for modal HTML structure
        print("\n🔍 Checking modal HTML structure:")
        
        if 'enhancedOrderModal' in template_content:
            print("   ✅ Enhanced order modal ID found")
        else:
            print("   ❌ Enhanced order modal ID not found")
            
        return True
        
    except Exception as e:
        print(f"❌ Error analyzing template: {e}")
        return False

def check_javascript_dependencies():
    """Check JavaScript dependencies and jQuery"""
    print("\n3️⃣ CHECKING JAVASCRIPT DEPENDENCIES")
    print("-" * 40)
    
    try:
        with open('templates/warehouse/packing_dashboard.html', 'r', encoding='utf-8') as f:
            template_content = f.read()
        
        # Check for jQuery
        if 'jquery' in template_content.lower() or '$(' in template_content:
            print("   ✅ jQuery detected")
        else:
            print("   ❌ jQuery not detected")
            
        # Check for Bootstrap
        if 'bootstrap' in template_content.lower():
            print("   ✅ Bootstrap detected")
        else:
            print("   ❌ Bootstrap not detected")
            
        # Check script loading order
        print("\n🔍 Checking script loading order:")
        script_matches = re.findall(r'<script[^>]*src=[\'"`]([^\'"`]+)[\'"`]', template_content)
        
        for i, script in enumerate(script_matches):
            print(f"   {i+1}. {script}")
            
        return True
        
    except Exception as e:
        print(f"❌ Error checking dependencies: {e}")
        return False

def analyze_console_errors():
    """Create a JavaScript snippet to check for console errors"""
    print("\n4️⃣ CREATING CONSOLE ERROR CHECKER")
    print("-" * 40)
    
    console_checker = """
    // Console Error Checker for Order Details Modal
    console.log('🔍 Starting Order Details Debug...');
    
    // Check if enhanced modal functions exist
    if (typeof showEnhancedOrderDetails === 'function') {
        console.log('✅ showEnhancedOrderDetails function exists');
    } else {
        console.error('❌ showEnhancedOrderDetails function missing');
    }
    
    if (typeof viewOrderDetails === 'function') {
        console.log('✅ viewOrderDetails function exists');
    } else {
        console.error('❌ viewOrderDetails function missing');
    }
    
    // Check if enhanced modal object exists
    if (typeof enhancedOrderModal !== 'undefined') {
        console.log('✅ enhancedOrderModal object exists');
    } else {
        console.error('❌ enhancedOrderModal object missing');
    }
    
    // Test API endpoint directly
    fetch('/api/order-details/ORD00000155')
        .then(response => {
            console.log('📡 API Response Status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('📊 API Response Data:', data);
            if (data.success) {
                console.log('✅ API call successful');
            } else {
                console.error('❌ API call failed:', data.message);
            }
        })
        .catch(error => {
            console.error('❌ API call error:', error);
        });
    
    // Check modal HTML elements
    const modal = document.getElementById('enhancedOrderModal');
    if (modal) {
        console.log('✅ Enhanced modal element found');
    } else {
        console.error('❌ Enhanced modal element not found');
    }
    """
    
    try:
        with open('static/js/debug_console_checker.js', 'w', encoding='utf-8') as f:
            f.write(console_checker)
        print("   ✅ Console checker created: static/js/debug_console_checker.js")
        print("   📋 Add this to your browser console to debug")
        return True
    except Exception as e:
        print(f"   ❌ Error creating console checker: {e}")
        return False

def main():
    """Run all JavaScript and network analysis methods"""
    print("🚀 DEEP JAVASCRIPT & NETWORK INVESTIGATION")
    print("=" * 80)
    
    js_ok = analyze_enhanced_modal_javascript()
    template_ok = analyze_warehouse_packing_template()
    deps_ok = check_javascript_dependencies()
    console_ok = analyze_console_errors()
    
    print(f"\n📊 METHOD 1 RESULTS")
    print("=" * 40)
    print(f"JavaScript Analysis: {'✅ PASS' if js_ok else '❌ FAIL'}")
    print(f"Template Analysis: {'✅ PASS' if template_ok else '❌ FAIL'}")
    print(f"Dependencies Check: {'✅ PASS' if deps_ok else '❌ FAIL'}")
    print(f"Console Checker: {'✅ CREATED' if console_ok else '❌ FAILED'}")
    
    if not all([js_ok, template_ok, deps_ok]):
        print("\n⚠️ ISSUES FOUND - Need to investigate further")
    else:
        print("\n✅ JavaScript structure looks good - issue may be elsewhere")

if __name__ == "__main__":
    main()
