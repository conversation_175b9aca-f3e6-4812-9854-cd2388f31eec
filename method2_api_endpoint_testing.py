#!/usr/bin/env python3
"""
Method 2: Direct API Endpoint Testing
Test all API endpoints individually to identify exact failure points
"""

import requests
import json
import sqlite3
import os
from datetime import datetime

def test_flask_server_running():
    """Test if Flask server is running"""
    print("🔍 METHOD 2: API ENDPOINT TESTING")
    print("=" * 60)
    
    print("\n1️⃣ TESTING FLASK SERVER STATUS")
    print("-" * 40)
    
    try:
        response = requests.get('http://127.0.0.1:5001/', timeout=5)
        print(f"   ✅ Flask server running - Status: {response.status_code}")
        return True
    except requests.exceptions.ConnectionError:
        print("   ❌ Flask server not running")
        return False
    except Exception as e:
        print(f"   ❌ Server test error: {e}")
        return False

def test_order_details_api():
    """Test the order details API endpoint"""
    print("\n2️⃣ TESTING ORDER DETAILS API")
    print("-" * 40)
    
    test_order_ids = ['ORD00000155', 'ORD00000157', 'ORD00000165']
    
    for order_id in test_order_ids:
        try:
            url = f'http://127.0.0.1:5001/api/order-details/{order_id}'
            print(f"\n🔍 Testing: {url}")
            
            response = requests.get(url, timeout=10)
            print(f"   📡 Status Code: {response.status_code}")
            print(f"   📡 Headers: {dict(response.headers)}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   ✅ JSON Response: {json.dumps(data, indent=2)[:200]}...")
                    
                    if data.get('success'):
                        print(f"   ✅ API Success: True")
                        print(f"   📊 Order Data: {data.get('order', {}).get('order_id', 'N/A')}")
                    else:
                        print(f"   ❌ API Success: False - {data.get('message', 'No message')}")
                        
                except json.JSONDecodeError as e:
                    print(f"   ❌ JSON Decode Error: {e}")
                    print(f"   📄 Raw Response: {response.text[:200]}...")
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
                print(f"   📄 Response: {response.text[:200]}...")
                
        except requests.exceptions.Timeout:
            print(f"   ❌ Timeout error for {order_id}")
        except requests.exceptions.ConnectionError:
            print(f"   ❌ Connection error for {order_id}")
        except Exception as e:
            print(f"   ❌ Unexpected error for {order_id}: {e}")

def test_qr_code_api():
    """Test the QR code API endpoint"""
    print("\n3️⃣ TESTING QR CODE API")
    print("-" * 40)
    
    try:
        url = 'http://127.0.0.1:5001/api/order-qr-code/ORD00000155'
        print(f"🔍 Testing: {url}")
        
        response = requests.get(url, timeout=10)
        print(f"   📡 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"   ✅ QR API Success: {data.get('success', False)}")
                if data.get('qr_code_base64'):
                    print(f"   ✅ QR Code Generated: {len(data['qr_code_base64'])} characters")
                else:
                    print(f"   ❌ No QR Code in response")
            except json.JSONDecodeError:
                print(f"   ❌ QR API JSON decode error")
        else:
            print(f"   ❌ QR API HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ QR API error: {e}")

def test_fallback_routes():
    """Test the fallback routes"""
    print("\n4️⃣ TESTING FALLBACK ROUTES")
    print("-" * 40)
    
    fallback_routes = [
        '/orders/ORD00000155/details',
        '/orders/ORD00000155/print-address'
    ]
    
    for route in fallback_routes:
        try:
            url = f'http://127.0.0.1:5001{route}'
            print(f"\n🔍 Testing: {url}")
            
            response = requests.get(url, timeout=10)
            print(f"   📡 Status Code: {response.status_code}")
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '')
                print(f"   ✅ Success - Content-Type: {content_type}")
                
                if 'application/json' in content_type:
                    try:
                        data = response.json()
                        print(f"   📊 JSON Success: {data.get('success', False)}")
                    except json.JSONDecodeError:
                        print(f"   ❌ JSON decode error")
                elif 'text/html' in content_type:
                    print(f"   ✅ HTML Response: {len(response.text)} characters")
                    
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
                print(f"   📄 Response: {response.text[:200]}...")
                
        except Exception as e:
            print(f"   ❌ Route error: {e}")

def test_warehouse_packing_page():
    """Test the warehouse packing page"""
    print("\n5️⃣ TESTING WAREHOUSE PACKING PAGE")
    print("-" * 40)
    
    try:
        url = 'http://127.0.0.1:5001/warehouse/packing'
        print(f"🔍 Testing: {url}")
        
        response = requests.get(url, timeout=10)
        print(f"   📡 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            html_content = response.text
            print(f"   ✅ Page loaded: {len(html_content)} characters")
            
            # Check for critical elements
            checks = [
                ('enhanced_modal.js', 'Enhanced Modal JS'),
                ('enhanced_modal.css', 'Enhanced Modal CSS'),
                ('viewOrderDetails(', 'View Details Function'),
                ('ORD00000155', 'Test Order ID'),
                ('enhancedOrderModal', 'Modal Element ID')
            ]
            
            for check, description in checks:
                if check in html_content:
                    print(f"   ✅ {description}: Found")
                else:
                    print(f"   ❌ {description}: Missing")
                    
        else:
            print(f"   ❌ Page load error: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Page test error: {e}")

def verify_database_directly():
    """Verify database content directly"""
    print("\n6️⃣ TESTING DATABASE DIRECTLY")
    print("-" * 40)
    
    try:
        if not os.path.exists('instance/medivent.db'):
            print("   ❌ Database file not found")
            return False
            
        conn = sqlite3.connect('instance/medivent.db')
        conn.row_factory = sqlite3.Row
        
        # Test order existence
        order = conn.execute('SELECT * FROM orders WHERE order_id = ?', ('ORD00000155',)).fetchone()
        
        if order:
            print(f"   ✅ Order ORD00000155 exists")
            print(f"   📊 Customer: {order['customer_name']}")
            print(f"   📊 Amount: Rs.{order['order_amount']}")
            print(f"   📊 Status: {order['status']}")
        else:
            print(f"   ❌ Order ORD00000155 not found")
            
        # Test order items
        items = conn.execute('SELECT * FROM order_items WHERE order_id = ?', ('ORD00000155',)).fetchall()
        print(f"   📊 Order Items: {len(items)} items")
        
        for item in items:
            print(f"      - {item['product_name']}: {item['quantity']} x Rs.{item['unit_price']}")
            
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Database error: {e}")
        return False

def main():
    """Run all API endpoint tests"""
    print("🚀 COMPREHENSIVE API ENDPOINT TESTING")
    print("=" * 80)
    
    server_ok = test_flask_server_running()
    
    if server_ok:
        test_order_details_api()
        test_qr_code_api()
        test_fallback_routes()
        test_warehouse_packing_page()
    else:
        print("\n⚠️ Flask server not running - start server first:")
        print("   python app.py")
        
    db_ok = verify_database_directly()
    
    print(f"\n📊 METHOD 2 RESULTS")
    print("=" * 40)
    print(f"Flask Server: {'✅ RUNNING' if server_ok else '❌ NOT RUNNING'}")
    print(f"Database: {'✅ ACCESSIBLE' if db_ok else '❌ ISSUES'}")
    
    if server_ok and db_ok:
        print("\n✅ Infrastructure looks good - issue may be in frontend integration")
    else:
        print("\n⚠️ Infrastructure issues found - need to fix these first")

if __name__ == "__main__":
    main()
