#!/usr/bin/env python3
"""
Method 3: Database Query Verification
Deep analysis of database structure, queries, and data integrity
"""

import sqlite3
import os
import json
from datetime import datetime

def analyze_database_structure():
    """Analyze complete database structure"""
    print("🔍 METHOD 3: DATABASE VERIFICATION")
    print("=" * 60)
    
    print("\n1️⃣ ANALYZING DATABASE STRUCTURE")
    print("-" * 40)
    
    try:
        if not os.path.exists('instance/medivent.db'):
            print("   ❌ Database file not found")
            return False
            
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"   📊 Total tables: {len(tables)}")
        
        for table in tables:
            table_name = table[0]
            print(f"\n   🔍 Table: {table_name}")
            
            # Get table schema
            cursor.execute(f"PRAGMA table_info({table_name});")
            columns = cursor.fetchall()
            
            for col in columns:
                col_id, col_name, col_type, not_null, default, pk = col
                pk_marker = " (PK)" if pk else ""
                null_marker = " NOT NULL" if not_null else ""
                print(f"      - {col_name}: {col_type}{pk_marker}{null_marker}")
                
            # Get row count
            cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
            count = cursor.fetchone()[0]
            print(f"      📊 Rows: {count}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Database structure error: {e}")
        return False

def verify_orders_table():
    """Verify orders table specifically"""
    print("\n2️⃣ VERIFYING ORDERS TABLE")
    print("-" * 40)
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        conn.row_factory = sqlite3.Row
        
        # Check orders table structure
        cursor = conn.cursor()
        cursor.execute("PRAGMA table_info(orders);")
        columns = cursor.fetchall()
        
        required_columns = ['order_id', 'customer_name', 'customer_phone', 'customer_address', 'order_amount', 'status']
        
        print("   🔍 Checking required columns:")
        existing_columns = [col[1] for col in columns]
        
        for req_col in required_columns:
            if req_col in existing_columns:
                print(f"      ✅ {req_col}")
            else:
                print(f"      ❌ {req_col} - MISSING")
        
        # Check specific test order
        print("\n   🔍 Checking test order ORD00000155:")
        order = conn.execute('SELECT * FROM orders WHERE order_id = ?', ('ORD00000155',)).fetchone()
        
        if order:
            print("      ✅ Order exists")
            for key in order.keys():
                value = order[key]
                if value is not None:
                    print(f"         {key}: {value}")
                else:
                    print(f"         {key}: NULL")
        else:
            print("      ❌ Order ORD00000155 not found")
            
            # Show available orders
            orders = conn.execute('SELECT order_id, customer_name FROM orders LIMIT 5').fetchall()
            print("      📋 Available orders:")
            for ord in orders:
                print(f"         - {ord['order_id']}: {ord['customer_name']}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Orders table error: {e}")
        return False

def verify_order_items_table():
    """Verify order_items table"""
    print("\n3️⃣ VERIFYING ORDER_ITEMS TABLE")
    print("-" * 40)
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        conn.row_factory = sqlite3.Row
        
        # Check order items for test order
        items = conn.execute('SELECT * FROM order_items WHERE order_id = ?', ('ORD00000155',)).fetchall()
        
        print(f"   📊 Order items for ORD00000155: {len(items)}")
        
        for item in items:
            print(f"      - Product: {item['product_name']}")
            print(f"        Quantity: {item['quantity']}")
            print(f"        Unit Price: Rs.{item['unit_price']}")
            print(f"        Total: Rs.{item['total_price']}")
        
        # Test the exact query used by API
        print("\n   🔍 Testing API query:")
        api_query = '''
            SELECT oi.*, p.name as product_name 
            FROM order_items oi 
            LEFT JOIN products p ON oi.product_id = p.product_id 
            WHERE oi.order_id = ?
        '''
        
        try:
            api_items = conn.execute(api_query, ('ORD00000155',)).fetchall()
            print(f"      ✅ API query successful: {len(api_items)} items")
            
            for item in api_items:
                print(f"         - {item['product_name']}: {item['quantity']}")
                
        except Exception as e:
            print(f"      ❌ API query failed: {e}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Order items error: {e}")
        return False

def test_api_queries_directly():
    """Test the exact queries used by API endpoints"""
    print("\n4️⃣ TESTING API QUERIES DIRECTLY")
    print("-" * 40)
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        conn.row_factory = sqlite3.Row
        
        # Test order details query (from api_endpoints.py)
        print("   🔍 Testing order details query:")
        
        order_query = 'SELECT * FROM orders WHERE order_id = ?'
        order = conn.execute(order_query, ('ORD00000155',)).fetchone()
        
        if order:
            print("      ✅ Order query successful")
            order_dict = dict(order)
            print(f"      📊 Order data: {json.dumps(order_dict, indent=2, default=str)}")
        else:
            print("      ❌ Order query returned no results")
        
        # Test order items query
        print("\n   🔍 Testing order items query:")
        
        items_query = '''
            SELECT oi.*, p.name as product_name 
            FROM order_items oi 
            LEFT JOIN products p ON oi.product_id = p.product_id 
            WHERE oi.order_id = ?
        '''
        
        items = conn.execute(items_query, ('ORD00000155',)).fetchall()
        print(f"      ✅ Items query successful: {len(items)} items")
        
        items_list = [dict(item) for item in items]
        print(f"      📊 Items data: {json.dumps(items_list, indent=2, default=str)}")
        
        # Test summary calculation
        print("\n   🔍 Testing summary calculation:")
        
        if order and items:
            summary = {
                'total_items': len(items),
                'total_quantity': sum(item['quantity'] for item in items),
                'order_amount': order['order_amount']
            }
            print(f"      ✅ Summary: {json.dumps(summary, indent=2)}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ API queries error: {e}")
        return False

def create_test_data_if_missing():
    """Create test data if missing"""
    print("\n5️⃣ CREATING TEST DATA IF MISSING")
    print("-" * 40)
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        conn.row_factory = sqlite3.Row
        
        # Check if order exists
        order = conn.execute('SELECT * FROM orders WHERE order_id = ?', ('ORD00000155',)).fetchone()
        
        if not order:
            print("   🔧 Creating missing test order...")
            
            # Create order
            conn.execute('''
                INSERT INTO orders 
                (order_id, customer_name, customer_phone, customer_address, 
                 order_amount, order_date, status, created_at, priority_level)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                'ORD00000155',
                '3Minur',
                '00211111',
                '4',
                444.0,
                '2025-07-20 00:01',
                'Normal',
                datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                1
            ))
            
            print("      ✅ Order created")
        else:
            print("   ✅ Test order already exists")
        
        # Check if order items exist
        items = conn.execute('SELECT * FROM order_items WHERE order_id = ?', ('ORD00000155',)).fetchall()
        
        if not items:
            print("   🔧 Creating missing order items...")
            
            # Create order items
            conn.execute('''
                INSERT INTO order_items 
                (order_id, product_id, product_name, quantity, unit_price, total_price)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', ('ORD00000155', 'P001', 'Sample Medicine', 1, 444.0, 444.0))
            
            print("      ✅ Order items created")
        else:
            print("   ✅ Order items already exist")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Test data creation error: {e}")
        return False

def main():
    """Run all database verification methods"""
    print("🚀 COMPREHENSIVE DATABASE VERIFICATION")
    print("=" * 80)
    
    structure_ok = analyze_database_structure()
    orders_ok = verify_orders_table()
    items_ok = verify_order_items_table()
    queries_ok = test_api_queries_directly()
    data_ok = create_test_data_if_missing()
    
    print(f"\n📊 METHOD 3 RESULTS")
    print("=" * 40)
    print(f"Database Structure: {'✅ VALID' if structure_ok else '❌ ISSUES'}")
    print(f"Orders Table: {'✅ VALID' if orders_ok else '❌ ISSUES'}")
    print(f"Order Items Table: {'✅ VALID' if items_ok else '❌ ISSUES'}")
    print(f"API Queries: {'✅ WORKING' if queries_ok else '❌ FAILING'}")
    print(f"Test Data: {'✅ READY' if data_ok else '❌ MISSING'}")
    
    if all([structure_ok, orders_ok, items_ok, queries_ok, data_ok]):
        print("\n✅ Database is fully functional - issue is not in database layer")
    else:
        print("\n⚠️ Database issues found - these need to be fixed first")

if __name__ == "__main__":
    main()
