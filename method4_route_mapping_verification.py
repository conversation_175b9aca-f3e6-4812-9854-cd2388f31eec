#!/usr/bin/env python3
"""
Method 4: Route Mapping Verification
Deep analysis of Flask routes, blueprints, and URL mappings
"""

import os
import re
import ast
import importlib.util

def analyze_app_py_structure():
    """Analyze the main app.py file for blueprint registration"""
    print("🔍 METHOD 4: ROUTE MAPPING VERIFICATION")
    print("=" * 60)
    
    print("\n1️⃣ ANALYZING APP.PY STRUCTURE")
    print("-" * 40)
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            app_content = f.read()
        
        # Check for blueprint imports
        print("🔍 Checking blueprint imports:")
        
        blueprint_imports = [
            ('api_endpoints', 'api_bp'),
            ('routes.orders', 'orders_bp'),
            ('routes.warehouse', 'warehouse_bp'),
        ]
        
        for module, blueprint in blueprint_imports:
            if f'from {module} import {blueprint}' in app_content:
                print(f"   ✅ {module}.{blueprint} imported")
            else:
                print(f"   ❌ {module}.{blueprint} not imported")
        
        # Check for blueprint registration
        print("\n🔍 Checking blueprint registration:")
        
        registrations = [
            ("app.register_blueprint(api_bp", "API Blueprint"),
            ("app.register_blueprint(orders_bp", "Orders Blueprint"),
            ("app.register_blueprint(warehouse_bp", "Warehouse Blueprint"),
        ]
        
        for pattern, description in registrations:
            if pattern in app_content:
                print(f"   ✅ {description} registered")
                # Extract URL prefix
                match = re.search(f"{re.escape(pattern)}.*?url_prefix=['\"]([^'\"]+)['\"]", app_content)
                if match:
                    print(f"      📍 URL Prefix: {match.group(1)}")
            else:
                print(f"   ❌ {description} not registered")
        
        return True
        
    except Exception as e:
        print(f"   ❌ App.py analysis error: {e}")
        return False

def analyze_api_endpoints():
    """Analyze API endpoints file"""
    print("\n2️⃣ ANALYZING API ENDPOINTS")
    print("-" * 40)
    
    try:
        with open('api_endpoints.py', 'r', encoding='utf-8') as f:
            api_content = f.read()
        
        # Check for blueprint creation
        if 'api_bp = Blueprint(' in api_content:
            print("   ✅ API Blueprint created")
        else:
            print("   ❌ API Blueprint not created")
        
        # Check for specific routes
        api_routes = [
            ('@api_bp.route(\'/order-details/<order_id>\', methods=[\'GET\'])', 'Order Details API'),
            ('@api_bp.route(\'/order-qr-code/<order_id>\', methods=[\'GET\'])', 'QR Code API'),
            ('def get_order_details(order_id):', 'Order Details Function'),
            ('def get_order_qr_code(order_id):', 'QR Code Function'),
        ]
        
        for route_pattern, description in api_routes:
            if route_pattern in api_content:
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ {description} - MISSING")
        
        # Check for database imports
        db_imports = [
            'from database import get_db',
            'import sqlite3',
            'from utils.qr_code_generator import generate_order_qr_code'
        ]
        
        print("\n🔍 Checking imports:")
        for import_stmt in db_imports:
            if import_stmt in api_content:
                print(f"   ✅ {import_stmt}")
            else:
                print(f"   ❌ {import_stmt} - MISSING")
        
        return True
        
    except Exception as e:
        print(f"   ❌ API endpoints analysis error: {e}")
        return False

def analyze_orders_routes():
    """Analyze orders routes file"""
    print("\n3️⃣ ANALYZING ORDERS ROUTES")
    print("-" * 40)
    
    try:
        with open('routes/orders.py', 'r', encoding='utf-8') as f:
            orders_content = f.read()
        
        # Check for blueprint creation
        if 'orders_bp = Blueprint(' in orders_content:
            print("   ✅ Orders Blueprint created")
        else:
            print("   ❌ Orders Blueprint not created")
        
        # Check for new routes we added
        new_routes = [
            ('@orders_bp.route(\'/<order_id>/print-address\')', 'Print Address Route'),
            ('@orders_bp.route(\'/<order_id>/details\')', 'Order Details JSON Route'),
            ('def print_address_label(order_id):', 'Print Address Function'),
            ('def get_order_details_json(order_id):', 'Order Details JSON Function'),
        ]
        
        for route_pattern, description in new_routes:
            if route_pattern in orders_content:
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ {description} - MISSING")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Orders routes analysis error: {e}")
        return False

def analyze_warehouse_routes():
    """Analyze warehouse routes file"""
    print("\n4️⃣ ANALYZING WAREHOUSE ROUTES")
    print("-" * 40)
    
    try:
        with open('routes/warehouse.py', 'r', encoding='utf-8') as f:
            warehouse_content = f.read()
        
        # Check for packing dashboard route
        if '@warehouse_bp.route(\'/packing\')' in warehouse_content:
            print("   ✅ Packing Dashboard Route exists")
        else:
            print("   ❌ Packing Dashboard Route missing")
        
        # Check for function
        if 'def packing_dashboard():' in warehouse_content:
            print("   ✅ Packing Dashboard Function exists")
        else:
            print("   ❌ Packing Dashboard Function missing")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Warehouse routes analysis error: {e}")
        return False

def test_route_url_generation():
    """Test URL generation for routes"""
    print("\n5️⃣ TESTING URL GENERATION")
    print("-" * 40)
    
    # Expected URLs based on blueprint registration
    expected_urls = [
        ('/api/order-details/ORD00000155', 'Order Details API'),
        ('/api/order-qr-code/ORD00000155', 'QR Code API'),
        ('/orders/ORD00000155/details', 'Order Details JSON'),
        ('/orders/ORD00000155/print-address', 'Print Address'),
        ('/warehouse/packing', 'Warehouse Packing'),
    ]
    
    for url, description in expected_urls:
        print(f"   📍 {description}: {url}")
    
    return True

def check_template_route_references():
    """Check if templates reference the correct routes"""
    print("\n6️⃣ CHECKING TEMPLATE ROUTE REFERENCES")
    print("-" * 40)
    
    try:
        # Check warehouse packing template
        with open('templates/warehouse/packing_dashboard.html', 'r', encoding='utf-8') as f:
            template_content = f.read()
        
        # Check for viewOrderDetails function
        if 'viewOrderDetails(' in template_content:
            print("   ✅ viewOrderDetails function called in template")
            
            # Extract order IDs being passed
            order_calls = re.findall(r'viewOrderDetails\([\'"`]([^\'"`]+)[\'"`]\)', template_content)
            print(f"   📋 Order IDs in calls: {order_calls}")
        else:
            print("   ❌ viewOrderDetails function not called")
        
        # Check for enhanced modal inclusion
        if 'enhanced_order_modal.html' in template_content:
            print("   ✅ Enhanced modal template included")
        else:
            print("   ❌ Enhanced modal template not included")
        
        # Check for JavaScript inclusion
        if 'enhanced_modal.js' in template_content:
            print("   ✅ Enhanced modal JavaScript included")
        else:
            print("   ❌ Enhanced modal JavaScript not included")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Template analysis error: {e}")
        return False

def verify_javascript_function_mapping():
    """Verify JavaScript function mapping"""
    print("\n7️⃣ VERIFYING JAVASCRIPT FUNCTION MAPPING")
    print("-" * 40)
    
    try:
        # Check enhanced modal JavaScript
        with open('static/js/enhanced_modal.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        # Check for global function definition
        if 'function showEnhancedOrderDetails(' in js_content:
            print("   ✅ showEnhancedOrderDetails function defined")
        else:
            print("   ❌ showEnhancedOrderDetails function missing")
        
        # Check for API endpoint calls
        api_calls = re.findall(r'fetch\([\'"`]([^\'"`]+)[\'"`]\)', js_content)
        print(f"   📋 API calls found: {api_calls}")
        
        # Check for specific API endpoints
        if '/api/order-details/' in js_content:
            print("   ✅ Order details API call found")
        else:
            print("   ❌ Order details API call missing")
        
        if '/api/order-qr-code/' in js_content:
            print("   ✅ QR code API call found")
        else:
            print("   ❌ QR code API call missing")
        
        return True
        
    except Exception as e:
        print(f"   ❌ JavaScript analysis error: {e}")
        return False

def main():
    """Run all route mapping verification methods"""
    print("🚀 COMPREHENSIVE ROUTE MAPPING VERIFICATION")
    print("=" * 80)
    
    app_ok = analyze_app_py_structure()
    api_ok = analyze_api_endpoints()
    orders_ok = analyze_orders_routes()
    warehouse_ok = analyze_warehouse_routes()
    urls_ok = test_route_url_generation()
    template_ok = check_template_route_references()
    js_ok = verify_javascript_function_mapping()
    
    print(f"\n📊 METHOD 4 RESULTS")
    print("=" * 40)
    print(f"App Structure: {'✅ VALID' if app_ok else '❌ ISSUES'}")
    print(f"API Endpoints: {'✅ VALID' if api_ok else '❌ ISSUES'}")
    print(f"Orders Routes: {'✅ VALID' if orders_ok else '❌ ISSUES'}")
    print(f"Warehouse Routes: {'✅ VALID' if warehouse_ok else '❌ ISSUES'}")
    print(f"URL Generation: {'✅ VALID' if urls_ok else '❌ ISSUES'}")
    print(f"Template References: {'✅ VALID' if template_ok else '❌ ISSUES'}")
    print(f"JavaScript Mapping: {'✅ VALID' if js_ok else '❌ ISSUES'}")
    
    if all([app_ok, api_ok, orders_ok, warehouse_ok, urls_ok, template_ok, js_ok]):
        print("\n✅ All route mappings are correct - issue may be in execution flow")
    else:
        print("\n⚠️ Route mapping issues found - these need to be fixed")

if __name__ == "__main__":
    main()
