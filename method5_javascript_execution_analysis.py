#!/usr/bin/env python3
"""
Method 5: JavaScript Execution Flow Analysis
Deep analysis of the JavaScript execution flow and potential failure points
"""

import os
import re

def analyze_javascript_execution_flow():
    """Analyze the complete JavaScript execution flow"""
    print("🔍 METHOD 5: JAVASCRIPT EXECUTION FLOW ANALYSIS")
    print("=" * 60)
    
    print("\n1️⃣ ANALYZING JAVASCRIPT EXECUTION FLOW")
    print("-" * 40)
    
    # Step 1: Check warehouse packing template JavaScript calls
    try:
        with open('templates/warehouse/packing_dashboard.html', 'r', encoding='utf-8') as f:
            template_content = f.read()
        
        print("🔍 Step 1: Template JavaScript Function Calls")
        
        # Find viewOrderDetails calls
        view_details_calls = re.findall(r'onclick="viewOrderDetails\([\'"`]([^\'"`]+)[\'"`]\)"', template_content)
        print(f"   📋 viewOrderDetails calls found: {len(view_details_calls)}")
        for call in view_details_calls:
            print(f"      - Order ID: {call}")
        
        # Check if viewOrderDetails function is defined in template
        if 'function viewOrderDetails(' in template_content:
            print("   ✅ viewOrderDetails function defined in template")
            
            # Extract the function definition
            func_match = re.search(r'function viewOrderDetails\(([^)]*)\)\s*{([^}]+)}', template_content, re.DOTALL)
            if func_match:
                func_body = func_match.group(2).strip()
                print(f"   📄 Function body preview: {func_body[:100]}...")
                
                # Check what the function does
                if 'showEnhancedOrderDetails' in func_body:
                    print("   ✅ Calls showEnhancedOrderDetails")
                else:
                    print("   ❌ Does not call showEnhancedOrderDetails")
                    
                if 'viewOrderDetailsBasic' in func_body:
                    print("   ✅ Has fallback to viewOrderDetailsBasic")
                else:
                    print("   ❌ No fallback mechanism")
        else:
            print("   ❌ viewOrderDetails function not defined in template")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Template analysis error: {e}")
        return False

def analyze_enhanced_modal_javascript():
    """Analyze the enhanced modal JavaScript file"""
    print("\n2️⃣ ANALYZING ENHANCED MODAL JAVASCRIPT")
    print("-" * 40)
    
    try:
        with open('static/js/enhanced_modal.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        print("🔍 Step 2: Enhanced Modal JavaScript Analysis")
        
        # Check for global function definition
        if 'function showEnhancedOrderDetails(' in js_content:
            print("   ✅ showEnhancedOrderDetails function defined")
        elif 'showEnhancedOrderDetails =' in js_content:
            print("   ✅ showEnhancedOrderDetails assigned as variable")
        else:
            print("   ❌ showEnhancedOrderDetails function not found")
        
        # Check for window assignment (global scope)
        if 'window.showEnhancedOrderDetails' in js_content:
            print("   ✅ Function assigned to window object")
        else:
            print("   ❌ Function not assigned to window object")
        
        # Check for API endpoint calls
        api_calls = re.findall(r'fetch\([\'"`]([^\'"`]+)[\'"`]\)', js_content)
        print(f"   📋 API calls found: {len(api_calls)}")
        for call in api_calls:
            print(f"      - {call}")
        
        # Check for error handling
        error_patterns = [
            'catch.*error',
            'showErrorState',
            'console.error'
        ]
        
        print("   🔍 Error handling mechanisms:")
        for pattern in error_patterns:
            matches = re.findall(pattern, js_content, re.IGNORECASE)
            if matches:
                print(f"      ✅ {pattern}: {len(matches)} instances")
            else:
                print(f"      ❌ {pattern}: Not found")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Enhanced modal JS analysis error: {e}")
        return False

def analyze_script_loading_order():
    """Analyze script loading order in template"""
    print("\n3️⃣ ANALYZING SCRIPT LOADING ORDER")
    print("-" * 40)
    
    try:
        with open('templates/warehouse/packing_dashboard.html', 'r', encoding='utf-8') as f:
            template_content = f.read()
        
        print("🔍 Step 3: Script Loading Order Analysis")
        
        # Find all script tags
        script_tags = re.findall(r'<script[^>]*(?:src=[\'"`]([^\'"`]+)[\'"`])?[^>]*>(.*?)</script>', template_content, re.DOTALL)
        
        print(f"   📋 Scripts found: {len(script_tags)}")
        
        for i, (src, inline) in enumerate(script_tags):
            if src:
                print(f"   {i+1}. External: {src}")
            else:
                inline_preview = inline.strip()[:50].replace('\n', ' ')
                print(f"   {i+1}. Inline: {inline_preview}...")
        
        # Check for jQuery loading
        jquery_found = False
        enhanced_modal_found = False
        
        for src, _ in script_tags:
            if src and 'jquery' in src.lower():
                jquery_found = True
                print(f"   ✅ jQuery loaded: {src}")
            elif src and 'enhanced_modal.js' in src:
                enhanced_modal_found = True
                print(f"   ✅ Enhanced modal loaded: {src}")
        
        if not jquery_found:
            print("   ⚠️ jQuery not explicitly loaded in this template")
        
        if not enhanced_modal_found:
            print("   ❌ Enhanced modal JS not loaded")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Script loading analysis error: {e}")
        return False

def create_javascript_debug_snippet():
    """Create a JavaScript debug snippet to test the execution flow"""
    print("\n4️⃣ CREATING JAVASCRIPT DEBUG SNIPPET")
    print("-" * 40)
    
    debug_snippet = """
// Enhanced Modal Debug Snippet
// Paste this into browser console on warehouse packing page

console.log('🔍 Starting Enhanced Modal Debug...');

// Test 1: Check if functions exist
console.log('\\n1️⃣ FUNCTION EXISTENCE CHECK');
console.log('viewOrderDetails exists:', typeof viewOrderDetails === 'function');
console.log('showEnhancedOrderDetails exists:', typeof showEnhancedOrderDetails === 'function');
console.log('viewOrderDetailsBasic exists:', typeof viewOrderDetailsBasic === 'function');

// Test 2: Check if enhanced modal object exists
console.log('\\n2️⃣ ENHANCED MODAL OBJECT CHECK');
if (typeof enhancedOrderModal !== 'undefined') {
    console.log('✅ enhancedOrderModal object exists');
    console.log('Modal methods:', Object.getOwnPropertyNames(enhancedOrderModal));
} else {
    console.error('❌ enhancedOrderModal object missing');
}

// Test 3: Check modal HTML element
console.log('\\n3️⃣ MODAL HTML ELEMENT CHECK');
const modalElement = document.getElementById('enhancedOrderModal');
if (modalElement) {
    console.log('✅ Enhanced modal HTML element found');
    console.log('Modal classes:', modalElement.className);
} else {
    console.error('❌ Enhanced modal HTML element not found');
}

// Test 4: Test API endpoint directly
console.log('\\n4️⃣ API ENDPOINT TEST');
fetch('/api/order-details/ORD00000155')
    .then(response => {
        console.log('API Response Status:', response.status);
        console.log('API Response Headers:', Object.fromEntries(response.headers.entries()));
        return response.json();
    })
    .then(data => {
        console.log('API Response Data:', data);
        if (data.success) {
            console.log('✅ API call successful');
            console.log('Order data:', data.order);
        } else {
            console.error('❌ API call failed:', data.message);
        }
    })
    .catch(error => {
        console.error('❌ API call error:', error);
    });

// Test 5: Try to manually trigger enhanced modal
console.log('\\n5️⃣ MANUAL ENHANCED MODAL TEST');
if (typeof showEnhancedOrderDetails === 'function') {
    console.log('Attempting to show enhanced modal for ORD00000155...');
    try {
        showEnhancedOrderDetails('ORD00000155');
        console.log('✅ Enhanced modal function called successfully');
    } catch (error) {
        console.error('❌ Enhanced modal function error:', error);
    }
} else {
    console.error('❌ Cannot test - showEnhancedOrderDetails function not available');
}

// Test 6: Check for JavaScript errors
console.log('\\n6️⃣ JAVASCRIPT ERROR CHECK');
window.addEventListener('error', function(e) {
    console.error('JavaScript Error Detected:', e.error);
    console.error('Error Message:', e.message);
    console.error('Error Source:', e.filename + ':' + e.lineno);
});

console.log('🎯 Debug complete! Check the results above.');
"""
    
    try:
        with open('static/js/debug_enhanced_modal.js', 'w', encoding='utf-8') as f:
            f.write(debug_snippet)
        
        print("   ✅ Debug snippet created: static/js/debug_enhanced_modal.js")
        print("   📋 Copy and paste this into browser console for debugging")
        return True
        
    except Exception as e:
        print(f"   ❌ Debug snippet creation error: {e}")
        return False

def analyze_potential_failure_points():
    """Analyze potential failure points in the execution flow"""
    print("\n5️⃣ ANALYZING POTENTIAL FAILURE POINTS")
    print("-" * 40)
    
    failure_points = [
        {
            'point': 'jQuery Not Loaded',
            'description': 'If jQuery is not loaded before enhanced modal JS',
            'symptoms': 'TypeError: $ is not defined',
            'check': 'typeof $ === "undefined"'
        },
        {
            'point': 'Enhanced Modal JS Not Loaded',
            'description': 'If enhanced_modal.js file fails to load',
            'symptoms': 'showEnhancedOrderDetails is not defined',
            'check': 'typeof showEnhancedOrderDetails === "undefined"'
        },
        {
            'point': 'Modal HTML Missing',
            'description': 'If enhanced_order_modal.html template not included',
            'symptoms': 'Modal does not appear when triggered',
            'check': 'document.getElementById("enhancedOrderModal") === null'
        },
        {
            'point': 'API Endpoint Failure',
            'description': 'If /api/order-details/ endpoint returns error',
            'symptoms': 'Network error or 404/500 response',
            'check': 'fetch("/api/order-details/ORD00000155").then(r => r.status !== 200)'
        },
        {
            'point': 'CORS Issues',
            'description': 'If there are cross-origin request issues',
            'symptoms': 'CORS policy error in console',
            'check': 'Check browser network tab for CORS errors'
        },
        {
            'point': 'JavaScript Execution Order',
            'description': 'If scripts execute before DOM is ready',
            'symptoms': 'Elements not found when scripts run',
            'check': 'DOMContentLoaded event timing'
        }
    ]
    
    print("🔍 Potential Failure Points:")
    for i, point in enumerate(failure_points, 1):
        print(f"\n   {i}. {point['point']}")
        print(f"      Description: {point['description']}")
        print(f"      Symptoms: {point['symptoms']}")
        print(f"      Check: {point['check']}")
    
    return True

def main():
    """Run all JavaScript execution flow analysis methods"""
    print("🚀 COMPREHENSIVE JAVASCRIPT EXECUTION FLOW ANALYSIS")
    print("=" * 80)
    
    flow_ok = analyze_javascript_execution_flow()
    modal_ok = analyze_enhanced_modal_javascript()
    scripts_ok = analyze_script_loading_order()
    debug_ok = create_javascript_debug_snippet()
    failure_ok = analyze_potential_failure_points()
    
    print(f"\n📊 METHOD 5 RESULTS")
    print("=" * 40)
    print(f"Execution Flow: {'✅ ANALYZED' if flow_ok else '❌ FAILED'}")
    print(f"Modal JavaScript: {'✅ ANALYZED' if modal_ok else '❌ FAILED'}")
    print(f"Script Loading: {'✅ ANALYZED' if scripts_ok else '❌ FAILED'}")
    print(f"Debug Tools: {'✅ CREATED' if debug_ok else '❌ FAILED'}")
    print(f"Failure Points: {'✅ IDENTIFIED' if failure_ok else '❌ FAILED'}")
    
    if all([flow_ok, modal_ok, scripts_ok, debug_ok, failure_ok]):
        print("\n✅ JavaScript execution flow fully analyzed")
        print("📋 Use the debug snippet in browser console to identify the exact issue")
    else:
        print("\n⚠️ JavaScript analysis incomplete - check errors above")

if __name__ == "__main__":
    main()
