#!/usr/bin/env python3
"""
Database migration script to add rejection columns to orders table
This script safely adds the missing rejection columns without breaking existing data
"""

import sqlite3
import sys
import os
from datetime import datetime

def backup_database(db_path):
    """Create a backup of the database before migration"""
    backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    try:
        # Copy database file
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"✅ Database backed up to: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"❌ Failed to backup database: {e}")
        return None

def check_columns_exist(db_path):
    """Check if rejection columns already exist"""
    try:
        db = sqlite3.connect(db_path)
        cursor = db.execute("PRAGMA table_info(orders)")
        columns = [row[1] for row in cursor.fetchall()]
        db.close()
        
        required_columns = ['rejection_date', 'rejected_by', 'rejection_notes', 'approval_notes']
        missing_columns = [col for col in required_columns if col not in columns]
        
        return missing_columns, columns
    except Exception as e:
        print(f"❌ Error checking columns: {e}")
        return None, None

def add_rejection_columns(db_path):
    """Add rejection columns to orders table"""
    try:
        db = sqlite3.connect(db_path)
        
        # Add columns one by one to avoid issues
        columns_to_add = [
            ('rejection_date', 'TIMESTAMP'),
            ('rejected_by', 'TEXT'),
            ('rejection_notes', 'TEXT'),
            ('approval_notes', 'TEXT')
        ]
        
        for column_name, column_type in columns_to_add:
            try:
                db.execute(f"ALTER TABLE orders ADD COLUMN {column_name} {column_type}")
                print(f"✅ Added column: {column_name}")
            except sqlite3.OperationalError as e:
                if "duplicate column name" in str(e).lower():
                    print(f"⚠️  Column {column_name} already exists, skipping")
                else:
                    raise e
        
        db.commit()
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Error adding columns: {e}")
        return False

def verify_migration(db_path):
    """Verify that all columns were added successfully"""
    missing_columns, all_columns = check_columns_exist(db_path)
    
    if missing_columns:
        print(f"❌ Migration incomplete. Still missing: {missing_columns}")
        return False
    else:
        print("✅ All rejection columns successfully added!")
        print(f"📊 Total columns in orders table: {len(all_columns)}")
        return True

def main():
    """Main migration function"""
    print("🔧 Order Rejection Columns Migration")
    print("=" * 50)
    
    # Check database path
    db_path = 'instance/medivent.db'
    if not os.path.exists(db_path):
        print(f"❌ Database not found at: {db_path}")
        
        # Check alternative paths
        alt_paths = ['medivent.db', 'database/medivent.db', 'data/medivent.db']
        for alt_path in alt_paths:
            if os.path.exists(alt_path):
                db_path = alt_path
                print(f"✅ Found database at: {db_path}")
                break
        else:
            print("❌ No database found. Please ensure the database exists.")
            return False
    
    print(f"📍 Using database: {db_path}")
    
    # Step 1: Check current state
    print("\n1. Checking current database state...")
    missing_columns, current_columns = check_columns_exist(db_path)
    
    if missing_columns is None:
        print("❌ Failed to check database state")
        return False
    
    if not missing_columns:
        print("✅ All rejection columns already exist!")
        return True
    
    print(f"📋 Missing columns: {missing_columns}")
    
    # Step 2: Create backup
    print("\n2. Creating database backup...")
    backup_path = backup_database(db_path)
    if not backup_path:
        print("❌ Failed to create backup. Aborting migration.")
        return False
    
    # Step 3: Add missing columns
    print("\n3. Adding missing columns...")
    if not add_rejection_columns(db_path):
        print("❌ Migration failed!")
        return False
    
    # Step 4: Verify migration
    print("\n4. Verifying migration...")
    if not verify_migration(db_path):
        print("❌ Migration verification failed!")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 Migration completed successfully!")
    print(f"📁 Backup saved at: {backup_path}")
    print("✅ Order rejection system is now ready!")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
