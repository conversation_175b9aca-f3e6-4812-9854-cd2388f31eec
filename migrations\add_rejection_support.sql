-- Migration script to add rejection support to orders table
-- Run this script to update existing databases with rejection functionality

-- Add rejection-related columns to orders table
ALTER TABLE orders ADD COLUMN rejection_date TIMESTAMP;
ALTER TABLE orders ADD COLUMN rejected_by TEXT;
ALTER TABLE orders ADD COLUMN rejection_notes TEXT;
ALTER TABLE orders ADD COLUMN approval_notes TEXT;

-- Create index for better performance on rejection queries
CREATE INDEX IF NOT EXISTS idx_orders_rejection_date ON orders(rejection_date);
CREATE INDEX IF NOT EXISTS idx_orders_rejected_by ON orders(rejected_by);
CREATE INDEX IF NOT EXISTS idx_orders_status_sales_agent ON orders(status, sales_agent);

-- Update any existing 'Pending' status orders to 'Placed' for consistency
UPDATE orders SET status = 'Placed' WHERE status = 'Pending';

-- Insert activity log entry for migration
INSERT INTO activity_logs (username, action, entity_id, details, module)
VALUES ('system', 'SCHEMA_MIGRATION', 'orders', 'Added rejection support columns to orders table', 'migration');
