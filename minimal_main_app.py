#!/usr/bin/env python3
"""
Minimal version of main app with API blueprint
"""

from flask import Flask, render_template, redirect, url_for
from flask_login import LoginManager
import os

# Initialize Flask app
app = Flask(__name__)
app.url_map.strict_slashes = False
app.config['SECRET_KEY'] = 'dev-key-for-testing'
app.config['DATABASE'] = os.path.join('instance', 'medivent.db')

# Initialize Flask-Login
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'auth.login'

@login_manager.user_loader
def load_user(user_id):
    """Load user for Flask-Login"""
    return None  # Simplified for testing

# Basic routes
@app.route('/')
def index():
    """Home page"""
    return """
    <h1>Minimal Main App</h1>
    <p>Testing API blueprint registration</p>
    <ul>
        <li><a href="/api/order-details/ORD00000155">Test Order Details API</a></li>
        <li><a href="/api/order-qr-code/ORD00000155">Test QR Code API</a></li>
        <li><a href="/warehouse/packing">Warehouse Packing</a></li>
    </ul>
    """

@app.route('/warehouse/packing')
def warehouse_packing():
    """Warehouse packing page"""
    return """
    <h1>Warehouse Packing</h1>
    <p>This is where the enhanced modal would be tested</p>
    <button onclick="testOrderDetails()">Test Order Details</button>
    <div id="result"></div>
    
    <script>
    async function testOrderDetails() {
        try {
            const response = await fetch('/api/order-details/ORD00000155');
            const data = await response.json();
            document.getElementById('result').innerHTML = 
                '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
        } catch (error) {
            document.getElementById('result').innerHTML = 
                '<p style="color: red;">Error: ' + error.message + '</p>';
        }
    }
    </script>
    """

# Register API blueprint
try:
    from api_endpoints import api_bp
    app.register_blueprint(api_bp)
    print(f"✅ API blueprint registered: {api_bp.name}")
    print(f"   URL prefix: {api_bp.url_prefix}")
    print(f"   Routes: {len(api_bp.deferred_functions)}")
    
    # List all registered routes
    with app.app_context():
        rules = list(app.url_map.iter_rules())
        api_routes = [rule for rule in rules if rule.rule.startswith('/api/')]
        print(f"✅ Found {len(api_routes)} API routes:")
        for rule in api_routes:
            print(f"   {rule.rule} -> {rule.endpoint}")
            
except Exception as e:
    print(f"❌ API blueprint registration failed: {e}")
    import traceback
    traceback.print_exc()

if __name__ == '__main__':
    print("🚀 Starting Minimal Main App...")
    print("📍 Available endpoints:")
    print("   GET /")
    print("   GET /warehouse/packing")
    print("   GET /api/order-details/<order_id>")
    print("   GET /api/order-qr-code/<order_id>")
    print("🌐 Server URL: http://127.0.0.1:5004")
    
    app.run(host='127.0.0.1', port=5004, debug=True, use_reloader=False)
