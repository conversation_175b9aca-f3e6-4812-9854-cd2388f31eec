#!/usr/bin/env python3
"""
Phase 1: Comprehensive Analysis of Order Creation System
Deep investigation to identify root cause of UNIQUE constraint error
"""

import os
import re
import sqlite3
from pathlib import Path

def analyze_order_routes():
    """Analyze all order-related routes in the codebase"""
    print("🔍 ANALYZING ORDER-RELATED ROUTES")
    print("=" * 80)
    
    routes_found = []
    
    # Files to analyze
    files_to_check = [
        'app.py',
        'routes/orders.py', 
        'routes/orders_minimal.py',
        'routes/orders_enhanced.py'
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"\n📁 Analyzing {file_path}:")
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Find route decorators
                route_patterns = [
                    r'@app\.route\([\'"]([^\'"]*orders[^\'"]*)[\'"].*?\)',
                    r'@[a-zA-Z_]+\.route\([\'"]([^\'"]*)[\'"].*?\)',
                    r'@orders_bp\.route\([\'"]([^\'"]*)[\'"].*?\)',
                    r'@orders_enhanced_bp\.route\([\'"]([^\'"]*)[\'"].*?\)'
                ]
                
                for pattern in route_patterns:
                    matches = re.findall(pattern, content, re.MULTILINE)
                    for match in matches:
                        # Find the function name that follows
                        route_def = re.search(rf'{re.escape(match)}.*?\ndef\s+(\w+)', content, re.DOTALL)
                        func_name = route_def.group(1) if route_def else "Unknown"
                        
                        routes_found.append({
                            'file': file_path,
                            'route': match,
                            'function': func_name,
                            'full_pattern': pattern
                        })
                        print(f"   📍 {match} -> {func_name}")
                
            except Exception as e:
                print(f"   ❌ Error reading {file_path}: {e}")
    
    return routes_found

def analyze_database_schema():
    """Analyze database schema for order-related tables"""
    print("\n🗄️  ANALYZING DATABASE SCHEMA")
    print("=" * 80)
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        order_tables = []
        for (table_name,) in tables:
            if 'order' in table_name.lower():
                order_tables.append(table_name)
        
        print(f"📋 Order-related tables found: {order_tables}")
        
        # Analyze each order table
        schema_info = {}
        for table in order_tables:
            print(f"\n📊 Table: {table}")
            
            # Get table schema
            cursor.execute(f'PRAGMA table_info({table})')
            columns = cursor.fetchall()
            
            schema_info[table] = {
                'columns': columns,
                'constraints': [],
                'indexes': []
            }
            
            print("   Columns:")
            for col in columns:
                col_name, col_type, not_null, default, pk = col[1], col[2], col[3], col[4], col[5]
                constraints = []
                if not_null: constraints.append("NOT NULL")
                if pk: constraints.append("PRIMARY KEY")
                if default: constraints.append(f"DEFAULT {default}")
                
                constraint_str = " ".join(constraints) if constraints else ""
                print(f"     {col_name:20} {col_type:15} {constraint_str}")
            
            # Get indexes
            cursor.execute(f"PRAGMA index_list({table})")
            indexes = cursor.fetchall()
            for index in indexes:
                schema_info[table]['indexes'].append(index)
                print(f"   Index: {index[1]} (unique: {index[2]})")
            
            # Get foreign keys
            cursor.execute(f"PRAGMA foreign_key_list({table})")
            fks = cursor.fetchall()
            for fk in fks:
                print(f"   Foreign Key: {fk[3]} -> {fk[2]}.{fk[4]}")
        
        conn.close()
        return schema_info
        
    except Exception as e:
        print(f"❌ Error analyzing database: {e}")
        return {}

def analyze_order_id_generation():
    """Analyze order ID generation mechanisms"""
    print("\n🆔 ANALYZING ORDER ID GENERATION")
    print("=" * 80)
    
    generation_methods = []
    
    # Check for order ID generation functions
    files_to_check = [
        'app.py',
        'routes/orders.py',
        'routes/orders_minimal.py', 
        'routes/orders_enhanced.py',
        'utils/order_utils.py'
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"\n📁 Checking {file_path}:")
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Look for order ID generation patterns
                patterns = [
                    r'def\s+generate_order_id\s*\(',
                    r'order_id\s*=.*ORD',
                    r'f["\']ORD\{.*?\}["\']',
                    r'order_sequence',
                    r'UNIQUE.*order_id'
                ]
                
                for pattern in patterns:
                    matches = re.finditer(pattern, content, re.IGNORECASE)
                    for match in matches:
                        # Get surrounding context
                        start = max(0, match.start() - 100)
                        end = min(len(content), match.end() + 100)
                        context = content[start:end].replace('\n', ' ')
                        
                        generation_methods.append({
                            'file': file_path,
                            'pattern': pattern,
                            'match': match.group(),
                            'context': context[:200] + "..." if len(context) > 200 else context
                        })
                        print(f"   🔍 Found: {match.group()}")
                        print(f"      Context: {context[:100]}...")
                
            except Exception as e:
                print(f"   ❌ Error reading {file_path}: {e}")
    
    return generation_methods

def analyze_blueprint_registrations():
    """Analyze blueprint registrations for conflicts"""
    print("\n🔗 ANALYZING BLUEPRINT REGISTRATIONS")
    print("=" * 80)
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find blueprint registrations
        blueprint_pattern = r'app\.register_blueprint\(([^)]+)\)'
        matches = re.findall(blueprint_pattern, content)
        
        registrations = []
        for match in matches:
            # Clean up the match
            blueprint_name = match.split(',')[0].strip()
            registrations.append(blueprint_name)
            print(f"   📌 Registered: {blueprint_name}")
        
        # Look for order-related blueprints
        order_blueprints = [bp for bp in registrations if 'order' in bp.lower()]
        print(f"\n📋 Order-related blueprints: {order_blueprints}")
        
        if len(order_blueprints) > 1:
            print("   ⚠️  POTENTIAL CONFLICT: Multiple order blueprints registered!")
        
        return registrations
        
    except Exception as e:
        print(f"❌ Error analyzing blueprints: {e}")
        return []

def analyze_template_form_fields():
    """Analyze template form fields for mismatches"""
    print("\n📝 ANALYZING TEMPLATE FORM FIELDS")
    print("=" * 80)
    
    template_path = 'templates/orders/new.html'
    
    if os.path.exists(template_path):
        try:
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Find form action
            action_match = re.search(r'action=["\']([^"\']+)["\']', content)
            if action_match:
                print(f"📍 Form action: {action_match.group(1)}")
            
            # Find form fields
            field_patterns = [
                r'name=["\']([^"\']+)["\']',
                r'required[^>]*name=["\']([^"\']+)["\']',
                r'name=["\']([^"\']+)["\'][^>]*required'
            ]
            
            all_fields = set()
            required_fields = set()
            
            for pattern in field_patterns:
                matches = re.findall(pattern, content)
                for match in matches:
                    all_fields.add(match)
                    if 'required' in pattern:
                        required_fields.add(match)
            
            print(f"📋 All form fields: {sorted(all_fields)}")
            print(f"⚠️  Required fields: {sorted(required_fields)}")
            
            return {
                'all_fields': all_fields,
                'required_fields': required_fields,
                'action': action_match.group(1) if action_match else None
            }
            
        except Exception as e:
            print(f"❌ Error analyzing template: {e}")
            return {}
    else:
        print("❌ Template not found")
        return {}

def check_database_data_integrity():
    """Check database for data integrity issues"""
    print("\n🔍 CHECKING DATABASE DATA INTEGRITY")
    print("=" * 80)
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        issues = []
        
        # Check for duplicate order IDs
        print("1. Checking for duplicate order IDs...")
        cursor.execute('''
            SELECT order_id, COUNT(*) as count 
            FROM orders 
            GROUP BY order_id 
            HAVING COUNT(*) > 1
        ''')
        duplicates = cursor.fetchall()
        
        if duplicates:
            print(f"   ❌ Found {len(duplicates)} duplicate order IDs:")
            for order_id, count in duplicates:
                print(f"      {order_id}: {count} occurrences")
                issues.append(f"Duplicate order_id: {order_id}")
        else:
            print("   ✅ No duplicate order IDs found")
        
        # Check for invalid order ID formats
        print("\n2. Checking for invalid order ID formats...")
        cursor.execute('SELECT order_id, customer_name FROM orders')
        orders = cursor.fetchall()
        
        invalid_formats = []
        for order_id, customer_name in orders:
            if not re.match(r'^ORD\d{8}$', order_id):
                invalid_formats.append((order_id, customer_name))
        
        if invalid_formats:
            print(f"   ❌ Found {len(invalid_formats)} invalid order ID formats:")
            for order_id, customer_name in invalid_formats[:5]:  # Show first 5
                print(f"      {order_id}: {customer_name}")
                issues.append(f"Invalid format: {order_id}")
        else:
            print("   ✅ All order IDs have valid format")
        
        # Check order sequence table
        print("\n3. Checking order sequence table...")
        try:
            cursor.execute('SELECT COUNT(*), MAX(id) FROM order_sequence')
            seq_count, max_seq = cursor.fetchone()
            print(f"   📊 Sequence entries: {seq_count}, Max ID: {max_seq}")
            
            # Check if sequence is ahead of orders
            cursor.execute("SELECT MAX(CAST(SUBSTR(order_id, 4) AS INTEGER)) FROM orders WHERE order_id REGEXP '^ORD[0-9]{8}$'")
            max_order_num = cursor.fetchone()[0] or 0
            print(f"   📊 Max order number: {max_order_num}")
            
            if max_seq < max_order_num:
                print(f"   ❌ Sequence behind orders: {max_seq} < {max_order_num}")
                issues.append(f"Sequence out of sync: {max_seq} < {max_order_num}")
            else:
                print(f"   ✅ Sequence synchronized: {max_seq} >= {max_order_num}")
                
        except Exception as e:
            print(f"   ❌ Error checking sequence: {e}")
            issues.append(f"Sequence table error: {e}")
        
        conn.close()
        return issues
        
    except Exception as e:
        print(f"❌ Error checking database integrity: {e}")
        return [f"Database error: {e}"]

def main():
    """Run comprehensive Phase 1 analysis"""
    print("🔬 PHASE 1: COMPREHENSIVE ANALYSIS")
    print("=" * 100)
    
    # 1. Analyze routes
    routes = analyze_order_routes()
    
    # 2. Analyze database schema
    schema = analyze_database_schema()
    
    # 3. Analyze order ID generation
    generation = analyze_order_id_generation()
    
    # 4. Analyze blueprint registrations
    blueprints = analyze_blueprint_registrations()
    
    # 5. Analyze template fields
    template_fields = analyze_template_form_fields()
    
    # 6. Check data integrity
    integrity_issues = check_database_data_integrity()
    
    # Generate comprehensive report
    print("\n" + "=" * 100)
    print("📊 PHASE 1 ANALYSIS REPORT")
    print("=" * 100)
    
    print(f"🔍 Routes found: {len(routes)}")
    print(f"🗄️  Database tables: {len(schema)}")
    print(f"🆔 ID generation methods: {len(generation)}")
    print(f"🔗 Blueprint registrations: {len(blueprints)}")
    print(f"📝 Template fields: {len(template_fields.get('all_fields', []))}")
    print(f"⚠️  Integrity issues: {len(integrity_issues)}")
    
    # Identify potential conflicts
    print("\n🚨 POTENTIAL CONFLICTS IDENTIFIED:")
    
    # Route conflicts
    route_paths = {}
    for route in routes:
        path = route['route']
        if path in route_paths:
            route_paths[path].append(route)
        else:
            route_paths[path] = [route]
    
    conflicts = {path: routes for path, routes in route_paths.items() if len(routes) > 1}
    
    if conflicts:
        print("   📍 Route conflicts:")
        for path, conflicting_routes in conflicts.items():
            print(f"      {path}:")
            for route in conflicting_routes:
                print(f"        - {route['file']}:{route['function']}")
    
    # Blueprint conflicts
    order_blueprints = [bp for bp in blueprints if 'order' in bp.lower()]
    if len(order_blueprints) > 1:
        print(f"   🔗 Blueprint conflicts: {order_blueprints}")
    
    # Data integrity issues
    if integrity_issues:
        print("   🗄️  Data integrity issues:")
        for issue in integrity_issues:
            print(f"      - {issue}")
    
    print("\n💡 RECOMMENDED NEXT STEPS:")
    print("   1. Resolve route conflicts by removing duplicates")
    print("   2. Fix blueprint registration conflicts")
    print("   3. Clean up invalid order IDs in database")
    print("   4. Synchronize order sequence table")
    print("   5. Align template fields with route expectations")
    
    print("=" * 100)

if __name__ == "__main__":
    main()
