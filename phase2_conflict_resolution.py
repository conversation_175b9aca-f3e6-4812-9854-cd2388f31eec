#!/usr/bin/env python3
"""
Phase 2: Systematic Conflict Resolution & Cleanup
Remove duplicates, fix conflicts, clean database, synchronize sequences
"""

import sqlite3
import re
import os
from datetime import datetime

def fix_blueprint_conflicts():
    """Fix blueprint registration conflicts"""
    print("🔧 FIXING BLUEPRINT CONFLICTS")
    print("=" * 60)
    
    try:
        # Read app.py
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find the orders_enhanced_bp registration
        enhanced_pattern = r'from routes\.orders_enhanced import orders_enhanced_bp\s*\n\s*app\.register_blueprint\(orders_enhanced_bp\)'
        
        if re.search(enhanced_pattern, content):
            print("   📍 Found orders_enhanced_bp registration")
            
            # Comment out the enhanced blueprint registration
            new_content = re.sub(
                enhanced_pattern,
                '# DISABLED: Conflicting with main orders_bp\n# from routes.orders_enhanced import orders_enhanced_bp\n# app.register_blueprint(orders_enhanced_bp)',
                content
            )
            
            # Write back to file
            with open('app.py', 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("   ✅ Disabled orders_enhanced_bp registration")
            return True
        else:
            print("   ⚠️  orders_enhanced_bp registration not found")
            return False
            
    except Exception as e:
        print(f"   ❌ Error fixing blueprint conflicts: {e}")
        return False

def clean_invalid_order_ids():
    """Clean up invalid order IDs in the database"""
    print("\n🧹 CLEANING INVALID ORDER IDs")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        # Find invalid order IDs
        cursor.execute('SELECT order_id, customer_name FROM orders')
        orders = cursor.fetchall()
        
        invalid_orders = []
        valid_orders = []
        
        for order_id, customer_name in orders:
            if re.match(r'^ORD\d{8}$', order_id):
                valid_orders.append((order_id, customer_name))
            else:
                invalid_orders.append((order_id, customer_name))
        
        print(f"   📊 Valid orders: {len(valid_orders)}")
        print(f"   📊 Invalid orders: {len(invalid_orders)}")
        
        if invalid_orders:
            print("   🔧 Fixing invalid order IDs...")
            
            # Get the highest valid order number
            valid_numbers = []
            for order_id, _ in valid_orders:
                try:
                    num = int(order_id[3:])
                    valid_numbers.append(num)
                except:
                    pass
            
            next_number = max(valid_numbers) + 1 if valid_numbers else 1
            print(f"   📈 Starting new order numbers from: {next_number}")
            
            # Fix each invalid order
            for i, (old_order_id, customer_name) in enumerate(invalid_orders):
                new_order_num = next_number + i
                new_order_id = f"ORD{new_order_num:08d}"
                
                print(f"   🔄 {old_order_id} -> {new_order_id}")
                
                # Update orders table
                cursor.execute('UPDATE orders SET order_id = ? WHERE order_id = ?', 
                             (new_order_id, old_order_id))
                
                # Update order_items table
                cursor.execute('UPDATE order_items SET order_id = ? WHERE order_id = ?', 
                             (new_order_id, old_order_id))
                
                # Update other related tables
                for table in ['invoices', 'challans', 'delivery_challans', 'order_status_history']:
                    try:
                        cursor.execute(f'UPDATE {table} SET order_id = ? WHERE order_id = ?', 
                                     (new_order_id, old_order_id))
                    except sqlite3.OperationalError:
                        # Table might not exist or have order_id column
                        pass
            
            conn.commit()
            print(f"   ✅ Fixed {len(invalid_orders)} invalid order IDs")
        else:
            print("   ✅ No invalid order IDs found")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Error cleaning order IDs: {e}")
        return False

def synchronize_order_sequence():
    """Synchronize order sequence table with existing orders"""
    print("\n🔄 SYNCHRONIZING ORDER SEQUENCE")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        # Get the highest order number
        cursor.execute("SELECT order_id FROM orders WHERE order_id LIKE 'ORD%' ORDER BY order_id DESC LIMIT 1")
        latest_order = cursor.fetchone()
        
        if latest_order:
            latest_order_id = latest_order[0]
            try:
                latest_order_num = int(latest_order_id[3:])
                print(f"   📊 Latest order number: {latest_order_num}")
            except:
                print("   ⚠️  Cannot parse latest order number")
                latest_order_num = 0
        else:
            print("   📊 No orders found")
            latest_order_num = 0
        
        # Clear and rebuild sequence table
        print("   🔧 Rebuilding sequence table...")
        cursor.execute('DELETE FROM order_sequence')
        
        # Insert entries up to latest order + buffer
        target_count = latest_order_num + 50  # Add buffer for future orders
        for i in range(1, target_count + 1):
            cursor.execute('INSERT INTO order_sequence (id) VALUES (?)', (i,))
        
        conn.commit()
        print(f"   ✅ Rebuilt sequence table with {target_count} entries")
        
        # Verify
        cursor.execute('SELECT COUNT(*), MAX(id) FROM order_sequence')
        count, max_id = cursor.fetchone()
        print(f"   📊 Sequence table: {count} entries, max ID: {max_id}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Error synchronizing sequence: {e}")
        return False

def fix_template_route_mismatch():
    """Fix template-route field name mismatches"""
    print("\n📝 FIXING TEMPLATE-ROUTE MISMATCH")
    print("=" * 60)
    
    try:
        # Fix the route to accept payment_mode instead of payment_method
        with open('routes/orders.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Replace payment_method with payment_mode in the route
        if "payment_method = request.form.get('payment_method'" in content:
            print("   🔧 Updating route to accept payment_mode...")
            
            new_content = content.replace(
                "payment_method = request.form.get('payment_method'",
                "payment_method = request.form.get('payment_mode'"
            )
            
            with open('routes/orders.py', 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("   ✅ Updated route to accept payment_mode field")
            return True
        else:
            print("   ⚠️  payment_method field not found in route")
            return False
            
    except Exception as e:
        print(f"   ❌ Error fixing template mismatch: {e}")
        return False

def remove_duplicate_routes():
    """Remove duplicate routes from app.py"""
    print("\n🗑️  REMOVING DUPLICATE ROUTES")
    print("=" * 60)
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Count of routes before
        route_count_before = len(re.findall(r'@app\.route\(', content))
        print(f"   📊 Routes before cleanup: {route_count_before}")
        
        # Remove specific duplicate order routes that conflict with blueprints
        duplicate_patterns = [
            # Remove old order creation routes
            r'@app\.route\([\'\"]/orders/new[\'\"]\).*?def\s+\w+.*?(?=@app\.route|def\s+\w+|$)',
            r'@app\.route\([\'\"]/new-order[\'\"]\).*?def\s+\w+.*?(?=@app\.route|def\s+\w+|$)',
        ]
        
        routes_removed = 0
        for pattern in duplicate_patterns:
            matches = re.findall(pattern, content, re.DOTALL)
            if matches:
                print(f"   🗑️  Removing {len(matches)} duplicate routes")
                content = re.sub(pattern, '', content, flags=re.DOTALL)
                routes_removed += len(matches)
        
        # Clean up extra whitespace
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
        
        # Count routes after
        route_count_after = len(re.findall(r'@app\.route\(', content))
        print(f"   📊 Routes after cleanup: {route_count_after}")
        print(f"   ✅ Removed {route_count_before - route_count_after} duplicate routes")
        
        # Write back to file
        with open('app.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error removing duplicate routes: {e}")
        return False

def test_order_id_generation():
    """Test order ID generation after fixes"""
    print("\n🧪 TESTING ORDER ID GENERATION")
    print("=" * 60)
    
    try:
        from app import app
        with app.app_context():
            from routes.orders import generate_order_id
            
            print("   🔧 Generating test order IDs...")
            for i in range(5):
                order_id = generate_order_id()
                if order_id and re.match(r'^ORD\d{8}$', order_id):
                    print(f"   ✅ Generated: {order_id}")
                else:
                    print(f"   ❌ Invalid: {order_id}")
                    return False
            
            print("   ✅ Order ID generation working correctly")
            return True
            
    except Exception as e:
        print(f"   ❌ Error testing order ID generation: {e}")
        return False

def verify_database_integrity():
    """Verify database integrity after cleanup"""
    print("\n🔍 VERIFYING DATABASE INTEGRITY")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        # Check for duplicate order IDs
        cursor.execute('''
            SELECT order_id, COUNT(*) as count 
            FROM orders 
            GROUP BY order_id 
            HAVING COUNT(*) > 1
        ''')
        duplicates = cursor.fetchall()
        
        if duplicates:
            print(f"   ❌ Found {len(duplicates)} duplicate order IDs")
            return False
        else:
            print("   ✅ No duplicate order IDs")
        
        # Check order ID formats
        cursor.execute('SELECT order_id FROM orders')
        order_ids = cursor.fetchall()
        
        invalid_count = 0
        for (order_id,) in order_ids:
            if not re.match(r'^ORD\d{8}$', order_id):
                invalid_count += 1
        
        if invalid_count > 0:
            print(f"   ❌ Found {invalid_count} invalid order ID formats")
            return False
        else:
            print("   ✅ All order IDs have valid format")
        
        # Check sequence table
        cursor.execute('SELECT COUNT(*), MAX(id) FROM order_sequence')
        seq_count, max_seq = cursor.fetchone()
        print(f"   📊 Sequence table: {seq_count} entries, max: {max_seq}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Error verifying integrity: {e}")
        return False

def main():
    """Run Phase 2 conflict resolution and cleanup"""
    print("🔧 PHASE 2: CONFLICT RESOLUTION & CLEANUP")
    print("=" * 80)
    
    results = {}
    
    # Step 1: Fix blueprint conflicts
    results['blueprint_fix'] = fix_blueprint_conflicts()
    
    # Step 2: Clean invalid order IDs
    results['order_id_cleanup'] = clean_invalid_order_ids()
    
    # Step 3: Synchronize order sequence
    results['sequence_sync'] = synchronize_order_sequence()
    
    # Step 4: Fix template-route mismatch
    results['template_fix'] = fix_template_route_mismatch()
    
    # Step 5: Remove duplicate routes
    results['route_cleanup'] = remove_duplicate_routes()
    
    # Step 6: Test order ID generation
    results['id_generation_test'] = test_order_id_generation()
    
    # Step 7: Verify database integrity
    results['integrity_check'] = verify_database_integrity()
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 PHASE 2 CLEANUP RESULTS")
    print("=" * 80)
    
    for task, success in results.items():
        status = "✅ SUCCESS" if success else "❌ FAILED"
        print(f"{task.replace('_', ' ').title()}: {status}")
    
    total_success = sum(results.values())
    total_tasks = len(results)
    
    print(f"\n🎯 Overall Success Rate: {total_success}/{total_tasks} ({total_success/total_tasks*100:.1f}%)")
    
    if total_success == total_tasks:
        print("\n🎉 PHASE 2 COMPLETE!")
        print("✅ All conflicts resolved")
        print("✅ Database cleaned and synchronized")
        print("✅ Template-route alignment fixed")
        print("✅ Ready for Phase 3 testing")
    else:
        print("\n⚠️  PHASE 2 PARTIAL SUCCESS")
        print("💡 Some issues remain - manual intervention may be needed")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
