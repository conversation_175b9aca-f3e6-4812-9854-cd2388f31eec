#!/usr/bin/env python3
"""
Phase 2: Database Design for Partial DC Management System
Create enhanced database schema with new tables and relationships
"""

import sqlite3
import os
from datetime import datetime

def create_enhanced_partial_dc_schema():
    """Create enhanced database schema for partial DC management"""
    print("🗄️  PHASE 2: DATABASE DESIGN")
    print("=" * 80)
    
    try:
        # Connect to database
        db_path = 'instance/medivent.db'
        if not os.path.exists(db_path):
            print(f"❌ Database not found at {db_path}")
            return False
            
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔧 Creating enhanced database schema...")
        
        # 1. Enhanced Partial DC Tracking Table
        print("\n1️⃣ Creating partial_dc_tracking table...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS partial_dc_tracking (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id TEXT NOT NULL,
                dc_number TEXT,
                product_id TEXT NOT NULL,
                product_name TEXT,
                strength TEXT,
                original_quantity INTEGER NOT NULL,
                delivered_quantity INTEGER DEFAULT 0,
                pending_quantity INTEGER NOT NULL,
                status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'partially_fulfilled', 'completed', 'cancelled')),
                priority_level INTEGER DEFAULT 1 CHECK (priority_level BETWEEN 1 AND 5),
                expected_fulfillment_date DATE,
                last_stock_check TIMESTAMP,
                stock_available INTEGER DEFAULT 0,
                reorder_suggested BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                notes TEXT,
                FOREIGN KEY (order_id) REFERENCES orders(order_id),
                FOREIGN KEY (product_id) REFERENCES products(product_id),
                FOREIGN KEY (dc_number) REFERENCES delivery_challans(dc_number)
            )
        ''')
        print("   ✅ partial_dc_tracking table created")
        
        # 2. Inventory Notifications Table
        print("\n2️⃣ Creating inventory_notifications table...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS inventory_notifications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id TEXT NOT NULL,
                notification_type TEXT NOT NULL CHECK (notification_type IN ('stock_available', 'low_stock', 'out_of_stock', 'reorder_suggestion')),
                title TEXT NOT NULL,
                message TEXT NOT NULL,
                priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
                is_read BOOLEAN DEFAULT FALSE,
                is_dismissed BOOLEAN DEFAULT FALSE,
                target_user TEXT,
                related_order_id TEXT,
                related_dc_number TEXT,
                action_required BOOLEAN DEFAULT FALSE,
                action_url TEXT,
                expires_at TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                read_at TIMESTAMP,
                dismissed_at TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products(product_id),
                FOREIGN KEY (related_order_id) REFERENCES orders(order_id),
                FOREIGN KEY (related_dc_number) REFERENCES delivery_challans(dc_number)
            )
        ''')
        print("   ✅ inventory_notifications table created")
        
        # 3. AI Predictions Table
        print("\n3️⃣ Creating ai_predictions table...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS ai_predictions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id TEXT NOT NULL,
                prediction_type TEXT NOT NULL CHECK (prediction_type IN ('demand_forecast', 'reorder_point', 'delivery_schedule', 'stock_optimization')),
                prediction_value REAL NOT NULL,
                confidence_score REAL CHECK (confidence_score BETWEEN 0 AND 1),
                prediction_period TEXT, -- e.g., 'next_7_days', 'next_month'
                model_version TEXT,
                input_features TEXT, -- JSON string of features used
                prediction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                actual_value REAL, -- For model accuracy tracking
                accuracy_score REAL,
                is_active BOOLEAN DEFAULT TRUE,
                notes TEXT,
                FOREIGN KEY (product_id) REFERENCES products(product_id)
            )
        ''')
        print("   ✅ ai_predictions table created")
        
        # 4. Real-time Inventory Status Table
        print("\n4️⃣ Creating realtime_inventory_status table...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS realtime_inventory_status (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id TEXT NOT NULL,
                warehouse_id TEXT NOT NULL,
                current_stock INTEGER NOT NULL DEFAULT 0,
                reserved_stock INTEGER DEFAULT 0,
                available_stock INTEGER DEFAULT 0,
                incoming_stock INTEGER DEFAULT 0,
                expected_arrival_date DATE,
                last_movement_date TIMESTAMP,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_by TEXT,
                status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'discontinued')),
                alert_threshold INTEGER DEFAULT 10,
                reorder_point INTEGER DEFAULT 20,
                max_stock_level INTEGER DEFAULT 1000,
                FOREIGN KEY (product_id) REFERENCES products(product_id),
                FOREIGN KEY (warehouse_id) REFERENCES warehouses(warehouse_id),
                UNIQUE(product_id, warehouse_id)
            )
        ''')
        print("   ✅ realtime_inventory_status table created")
        
        # 5. Partial DC Analytics Table
        print("\n5️⃣ Creating partial_dc_analytics table...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS partial_dc_analytics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date DATE NOT NULL,
                total_partial_orders INTEGER DEFAULT 0,
                total_pending_items INTEGER DEFAULT 0,
                total_pending_value REAL DEFAULT 0,
                avg_fulfillment_time REAL, -- in days
                fulfillment_rate REAL, -- percentage
                top_pending_product_id TEXT,
                top_pending_quantity INTEGER,
                reorder_suggestions_count INTEGER DEFAULT 0,
                stock_alerts_count INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (top_pending_product_id) REFERENCES products(product_id)
            )
        ''')
        print("   ✅ partial_dc_analytics table created")
        
        # 6. Create Performance Indexes
        print("\n6️⃣ Creating performance indexes...")
        
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_partial_dc_order_id ON partial_dc_tracking(order_id)",
            "CREATE INDEX IF NOT EXISTS idx_partial_dc_product_id ON partial_dc_tracking(product_id)",
            "CREATE INDEX IF NOT EXISTS idx_partial_dc_status ON partial_dc_tracking(status)",
            "CREATE INDEX IF NOT EXISTS idx_partial_dc_pending_qty ON partial_dc_tracking(pending_quantity)",
            "CREATE INDEX IF NOT EXISTS idx_inventory_notifications_product ON inventory_notifications(product_id)",
            "CREATE INDEX IF NOT EXISTS idx_inventory_notifications_unread ON inventory_notifications(is_read, created_at)",
            "CREATE INDEX IF NOT EXISTS idx_ai_predictions_product ON ai_predictions(product_id)",
            "CREATE INDEX IF NOT EXISTS idx_ai_predictions_active ON ai_predictions(is_active, prediction_date)",
            "CREATE INDEX IF NOT EXISTS idx_realtime_inventory_product ON realtime_inventory_status(product_id)",
            "CREATE INDEX IF NOT EXISTS idx_realtime_inventory_warehouse ON realtime_inventory_status(warehouse_id)",
            "CREATE INDEX IF NOT EXISTS idx_partial_analytics_date ON partial_dc_analytics(date)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
            index_name = index_sql.split("idx_")[1].split(" ")[0]
            print(f"   ✅ Index created: idx_{index_name}")
        
        # 7. Create Triggers for Data Consistency
        print("\n7️⃣ Creating database triggers...")
        
        # Trigger to update partial_dc_tracking when inventory changes
        cursor.execute('''
            CREATE TRIGGER IF NOT EXISTS update_partial_dc_stock_status
            AFTER UPDATE OF current_stock ON realtime_inventory_status
            BEGIN
                UPDATE partial_dc_tracking 
                SET stock_available = NEW.current_stock,
                    last_stock_check = CURRENT_TIMESTAMP,
                    updated_at = CURRENT_TIMESTAMP
                WHERE product_id = NEW.product_id 
                AND status = 'pending';
                
                -- Create notification if stock becomes available
                INSERT INTO inventory_notifications (
                    product_id, notification_type, title, message, priority, action_required
                )
                SELECT NEW.product_id, 'stock_available', 
                       'Stock Available', 
                       'Product ' || NEW.product_id || ' is now in stock (' || NEW.current_stock || ' units available)',
                       'high', TRUE
                WHERE NEW.current_stock > 0 AND OLD.current_stock = 0
                AND EXISTS (
                    SELECT 1 FROM partial_dc_tracking 
                    WHERE product_id = NEW.product_id AND status = 'pending'
                );
            END
        ''')
        print("   ✅ Trigger created: update_partial_dc_stock_status")
        
        # Trigger to update analytics when partial DC status changes
        cursor.execute('''
            CREATE TRIGGER IF NOT EXISTS update_partial_dc_analytics
            AFTER UPDATE OF status ON partial_dc_tracking
            BEGIN
                -- Update daily analytics
                INSERT OR REPLACE INTO partial_dc_analytics (
                    date, total_partial_orders, total_pending_items, 
                    total_pending_value, created_at
                )
                SELECT 
                    DATE('now') as date,
                    COUNT(DISTINCT order_id) as total_partial_orders,
                    COUNT(*) as total_pending_items,
                    SUM(pending_quantity * 25.0) as total_pending_value, -- Assuming avg price
                    CURRENT_TIMESTAMP
                FROM partial_dc_tracking 
                WHERE status = 'pending';
            END
        ''')
        print("   ✅ Trigger created: update_partial_dc_analytics")
        
        # 8. Insert Sample Data for Testing
        print("\n8️⃣ Inserting sample data...")
        
        # Sample partial DC tracking data
        sample_partial_data = [
            ('ORD00000001', 'P001', 'Paracetamol 500mg', '500mg', 100, 60, 40, 'pending', 3),
            ('ORD00000002', 'P002', 'Amoxicillin 250mg', '250mg', 50, 30, 20, 'pending', 2),
            ('ORD00000003', 'P001', 'Paracetamol 500mg', '500mg', 75, 75, 0, 'completed', 1)
        ]
        
        for order_id, product_id, product_name, strength, orig_qty, del_qty, pend_qty, status, priority in sample_partial_data:
            cursor.execute('''
                INSERT OR IGNORE INTO partial_dc_tracking (
                    order_id, product_id, product_name, strength,
                    original_quantity, delivered_quantity, pending_quantity,
                    status, priority_level, created_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'system')
            ''', (order_id, product_id, product_name, strength, orig_qty, del_qty, pend_qty, status, priority))
        
        print("   ✅ Sample partial DC tracking data inserted")
        
        # Sample inventory notifications
        sample_notifications = [
            ('P001', 'stock_available', 'Stock Available', 'Paracetamol 500mg is now in stock', 'high', True),
            ('P002', 'low_stock', 'Low Stock Alert', 'Amoxicillin 250mg is running low', 'medium', False),
            ('P003', 'reorder_suggestion', 'Reorder Suggested', 'Consider reordering Ibuprofen 400mg', 'low', True)
        ]
        
        for product_id, notif_type, title, message, priority, action_req in sample_notifications:
            cursor.execute('''
                INSERT OR IGNORE INTO inventory_notifications (
                    product_id, notification_type, title, message, priority, action_required
                ) VALUES (?, ?, ?, ?, ?, ?)
            ''', (product_id, notif_type, title, message, priority, action_req))
        
        print("   ✅ Sample notification data inserted")
        
        # 9. Verify Schema Creation
        print("\n9️⃣ Verifying schema creation...")
        
        new_tables = [
            'partial_dc_tracking',
            'inventory_notifications', 
            'ai_predictions',
            'realtime_inventory_status',
            'partial_dc_analytics'
        ]
        
        for table in new_tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"   ✅ {table}: {count} records")
        
        # Commit all changes
        conn.commit()
        conn.close()
        
        print("\n✅ PHASE 2 DATABASE DESIGN COMPLETE!")
        print("   All tables, indexes, and triggers created successfully")
        return True
        
    except Exception as e:
        print(f"❌ Database design error: {e}")
        if conn:
            conn.rollback()
            conn.close()
        return False

def verify_database_integrity():
    """Verify database integrity and relationships"""
    print("\n🔍 VERIFYING DATABASE INTEGRITY")
    print("-" * 50)
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        # Check foreign key constraints
        cursor.execute("PRAGMA foreign_key_check")
        fk_violations = cursor.fetchall()
        
        if fk_violations:
            print("❌ Foreign key violations found:")
            for violation in fk_violations:
                print(f"   {violation}")
        else:
            print("✅ No foreign key violations")
        
        # Check table relationships
        relationships = [
            ("partial_dc_tracking", "orders", "order_id"),
            ("partial_dc_tracking", "products", "product_id"),
            ("inventory_notifications", "products", "product_id"),
            ("ai_predictions", "products", "product_id"),
            ("realtime_inventory_status", "products", "product_id")
        ]
        
        for child_table, parent_table, key_column in relationships:
            cursor.execute(f'''
                SELECT COUNT(*) FROM {child_table} c
                LEFT JOIN {parent_table} p ON c.{key_column} = p.{key_column}
                WHERE p.{key_column} IS NULL AND c.{key_column} IS NOT NULL
            ''')
            orphaned = cursor.fetchone()[0]
            
            if orphaned > 0:
                print(f"⚠️  {orphaned} orphaned records in {child_table}")
            else:
                print(f"✅ {child_table} → {parent_table} relationship intact")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Integrity check error: {e}")
        return False

def main():
    """Run Phase 2 database design"""
    print("🗄️  PARTIAL DC MANAGEMENT - PHASE 2: DATABASE DESIGN")
    print("=" * 80)
    print(f"Design Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Create enhanced schema
    schema_success = create_enhanced_partial_dc_schema()
    
    # Verify integrity
    if schema_success:
        integrity_success = verify_database_integrity()
    else:
        integrity_success = False
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 PHASE 2 DATABASE DESIGN SUMMARY")
    print("=" * 80)
    
    if schema_success and integrity_success:
        print("🎉 PHASE 2 COMPLETE SUCCESS!")
        print("✅ Enhanced database schema created")
        print("✅ All tables and relationships established")
        print("✅ Performance indexes created")
        print("✅ Database triggers implemented")
        print("✅ Sample data inserted")
        print("✅ Database integrity verified")
        print("\n🚀 Ready for Phase 3: Backend Development")
    else:
        print("⚠️  PHASE 2 PARTIAL SUCCESS")
        print("💡 Some components may need manual intervention")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
