#!/usr/bin/env python3
"""
Phase 3: Comprehensive Testing & Verification
Test terminal and web interface order creation systematically
"""

import requests
import sqlite3
import time
import json
from datetime import datetime

def test_terminal_order_creation():
    """Test order creation via terminal (direct function calls)"""
    print("🖥️  TESTING TERMINAL ORDER CREATION")
    print("=" * 60)
    
    try:
        from app import app
        with app.app_context():
            from database import get_db
            from routes.orders import generate_order_id, generate_order_item_id
            
            db = get_db()
            
            # Get initial count
            cursor = db.execute('SELECT COUNT(*) FROM orders')
            initial_count = cursor.fetchone()[0]
            print(f"   📊 Initial order count: {initial_count}")
            
            # Generate IDs
            order_id = generate_order_id()
            order_item_id = generate_order_item_id()
            
            print(f"   🆔 Generated order ID: {order_id}")
            print(f"   🆔 Generated order item ID: {order_item_id}")
            
            if not order_id or not order_item_id:
                print("   ❌ Failed to generate IDs")
                return False
            
            # Create order
            db.execute('BEGIN IMMEDIATE TRANSACTION')
            
            # Insert order
            db.execute('''
                INSERT INTO orders (
                    order_id, customer_name, customer_address, customer_phone,
                    payment_method, status, sales_agent, updated_by, order_date, last_updated
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                order_id, 'Terminal Test Customer', 'Terminal Test Address', '555-TERMINAL-TEST',
                'cash', 'Placed', 'admin', 'admin', datetime.now(), datetime.now()
            ))
            
            # Insert order item
            db.execute('''
                INSERT INTO order_items (
                    order_item_id, order_id, product_id, product_name, strength,
                    quantity, foc_quantity, unit_price, line_total, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                order_item_id, order_id, 'P001', 'Paracetamol 500mg', '500mg',
                1, 0, 25.5, 25.5, 'Placed'
            ))
            
            # Update order total
            db.execute('UPDATE orders SET order_amount = ? WHERE order_id = ?', (25.5, order_id))
            
            # Commit
            db.execute('COMMIT')
            
            # Verify creation
            cursor = db.execute('SELECT COUNT(*) FROM orders')
            final_count = cursor.fetchone()[0]
            
            if final_count > initial_count:
                print(f"   ✅ Terminal order created successfully: {order_id}")
                return True
            else:
                print("   ❌ Order not found after creation")
                return False
            
    except Exception as e:
        print(f"   ❌ Terminal order creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_web_interface_order_creation():
    """Test order creation via web interface"""
    print("\n🌐 TESTING WEB INTERFACE ORDER CREATION")
    print("=" * 60)
    
    base_url = "http://localhost:5001"
    session = requests.Session()
    
    try:
        # Wait for server to start
        print("   ⏳ Waiting for server to start...")
        time.sleep(3)
        
        # Test server connectivity
        try:
            response = session.get(f"{base_url}/", timeout=5)
            print(f"   🌐 Server connectivity: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"   ❌ Server not accessible: {e}")
            return False
        
        # Login
        print("   🔐 Logging in...")
        login_data = {'username': 'admin', 'password': 'admin123'}
        response = session.post(f"{base_url}/login", data=login_data, allow_redirects=False)
        
        if response.status_code != 302:
            print(f"   ❌ Login failed: {response.status_code}")
            return False
        
        print("   ✅ Login successful")
        
        # Get initial order count
        print("   📊 Getting initial order count...")
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM orders')
        initial_count = cursor.fetchone()[0]
        print(f"   📊 Initial order count: {initial_count}")
        conn.close()
        
        # Get order form page
        print("   📝 Accessing order form...")
        response = session.get(f"{base_url}/orders/new", timeout=10)
        
        if response.status_code != 200:
            print(f"   ❌ Cannot access order form: {response.status_code}")
            return False
        
        print("   ✅ Order form accessible")
        
        # Submit order with correct field names
        print("   📤 Submitting order...")
        order_data = {
            'customer_name': 'Web Interface Test Customer',
            'customer_address': 'Web Interface Test Address',
            'customer_phone': '555-WEB-INTERFACE',
            'payment_mode': 'cash',  # Using payment_mode (not payment_method)
            'po_number': 'WEB-INTERFACE-001',
            'sales_agent': 'admin',
            'product_id[]': ['P001'],
            'quantity[]': ['1'],
            'rate[]': ['25.50'],  # Including rate field
            'foc_quantity[]': ['0']
        }
        
        response = session.post(f"{base_url}/orders/new", 
                               data=order_data, 
                               timeout=15,
                               allow_redirects=False)
        
        print(f"   📤 Response status: {response.status_code}")
        
        if response.status_code == 302:
            redirect_url = response.headers.get('Location', '')
            print(f"   🔄 Redirect URL: {redirect_url}")
            
            # Check if redirected to order view (success) or back to form (error)
            if '/orders/' in redirect_url and redirect_url != '/orders/new':
                print("   ✅ Successful redirect to order view")
                
                # Verify order was created
                time.sleep(2)
                conn = sqlite3.connect('instance/medivent.db')
                cursor = conn.cursor()
                cursor.execute('SELECT COUNT(*) FROM orders')
                final_count = cursor.fetchone()[0]
                
                cursor.execute('SELECT order_id, customer_name FROM orders ORDER BY order_date DESC LIMIT 1')
                new_order = cursor.fetchone()
                conn.close()
                
                if final_count > initial_count:
                    order_id, customer_name = new_order
                    print(f"   ✅ Web order created: {order_id} - {customer_name}")
                    return True
                else:
                    print("   ❌ Order not found in database")
                    return False
            else:
                print("   ⚠️  Redirected back to form - checking for errors...")
                
                # Follow redirect to check for errors
                response = session.get(f"{base_url}{redirect_url}", timeout=10)
                if 'alert-danger' in response.text:
                    import re
                    error_match = re.search(r'alert-danger[^>]*>([^<]+)', response.text)
                    if error_match:
                        print(f"   ❌ Error: {error_match.group(1).strip()}")
                
                return False
        else:
            print(f"   ❌ Unexpected response status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Web interface test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_order_workflow_routes():
    """Test all order-related routes individually"""
    print("\n🔗 TESTING ORDER WORKFLOW ROUTES")
    print("=" * 60)
    
    base_url = "http://localhost:5001"
    session = requests.Session()
    
    try:
        # Login first
        login_data = {'username': 'admin', 'password': 'admin123'}
        session.post(f"{base_url}/login", data=login_data)
        
        # Get a test order ID
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        cursor.execute('SELECT order_id FROM orders ORDER BY order_date DESC LIMIT 1')
        test_order = cursor.fetchone()
        conn.close()
        
        if not test_order:
            print("   ⚠️  No orders found for testing")
            return False
        
        test_order_id = test_order[0]
        print(f"   🆔 Using test order: {test_order_id}")
        
        # Test routes
        routes_to_test = [
            ('/orders', 'Orders index'),
            ('/orders/new', 'New order form'),
            (f'/orders/{test_order_id}', 'Order view'),
            ('/orders/search', 'Order search'),
        ]
        
        results = {}
        for route, description in routes_to_test:
            try:
                response = session.get(f"{base_url}{route}", timeout=10)
                success = response.status_code == 200
                results[route] = success
                status = "✅" if success else "❌"
                print(f"   {status} {route} ({description}): {response.status_code}")
            except Exception as e:
                results[route] = False
                print(f"   ❌ {route} ({description}): {e}")
        
        success_rate = sum(results.values()) / len(results) * 100
        print(f"   📊 Route success rate: {success_rate:.1f}%")
        
        return success_rate >= 75  # 75% success rate threshold
        
    except Exception as e:
        print(f"   ❌ Route testing failed: {e}")
        return False

def test_database_operations():
    """Test database operations after order creation"""
    print("\n🗄️  TESTING DATABASE OPERATIONS")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        # Test 1: Check order count
        cursor.execute('SELECT COUNT(*) FROM orders')
        order_count = cursor.fetchone()[0]
        print(f"   📊 Total orders: {order_count}")
        
        # Test 2: Check order ID formats
        cursor.execute('SELECT order_id FROM orders')
        order_ids = cursor.fetchall()
        
        valid_formats = 0
        for (order_id,) in order_ids:
            if order_id.startswith('ORD') and len(order_id) == 11:
                valid_formats += 1
        
        format_success_rate = valid_formats / len(order_ids) * 100
        print(f"   📊 Valid order ID formats: {valid_formats}/{len(order_ids)} ({format_success_rate:.1f}%)")
        
        # Test 3: Check sequence table
        cursor.execute('SELECT COUNT(*), MAX(id) FROM order_sequence')
        seq_count, max_seq = cursor.fetchone()
        print(f"   📊 Sequence table: {seq_count} entries, max: {max_seq}")
        
        # Test 4: Check for orphaned order items
        cursor.execute('''
            SELECT COUNT(*) FROM order_items oi 
            LEFT JOIN orders o ON oi.order_id = o.order_id 
            WHERE o.order_id IS NULL
        ''')
        orphaned_items = cursor.fetchone()[0]
        print(f"   📊 Orphaned order items: {orphaned_items}")
        
        conn.close()
        
        # Success criteria
        success = (
            order_count > 0 and
            format_success_rate >= 95 and
            seq_count > 0 and
            orphaned_items == 0
        )
        
        if success:
            print("   ✅ Database operations successful")
        else:
            print("   ❌ Database operations have issues")
        
        return success
        
    except Exception as e:
        print(f"   ❌ Database testing failed: {e}")
        return False

def test_frontend_backend_integration():
    """Test frontend-backend integration"""
    print("\n🔗 TESTING FRONTEND-BACKEND INTEGRATION")
    print("=" * 60)
    
    base_url = "http://localhost:5001"
    session = requests.Session()
    
    try:
        # Login
        login_data = {'username': 'admin', 'password': 'admin123'}
        session.post(f"{base_url}/login", data=login_data)
        
        # Test 1: Form field validation
        print("   📝 Testing form field validation...")
        response = session.get(f"{base_url}/orders/new")
        
        if 'customer_name' in response.text and 'payment_mode' in response.text:
            print("   ✅ Form fields present")
            field_test = True
        else:
            print("   ❌ Form fields missing")
            field_test = False
        
        # Test 2: JavaScript functionality
        print("   🔧 Testing JavaScript functionality...")
        js_elements = ['orderForm', 'product-select', 'quantity-input']
        js_test = all(element in response.text for element in js_elements)
        
        if js_test:
            print("   ✅ JavaScript elements present")
        else:
            print("   ❌ JavaScript elements missing")
        
        # Test 3: Template rendering
        print("   🎨 Testing template rendering...")
        template_elements = ['Place New Order', 'Customer Name', 'Payment Method']
        template_test = all(element in response.text for element in template_elements)
        
        if template_test:
            print("   ✅ Template rendering correctly")
        else:
            print("   ❌ Template rendering issues")
        
        integration_success = field_test and js_test and template_test
        
        if integration_success:
            print("   ✅ Frontend-backend integration successful")
        else:
            print("   ❌ Frontend-backend integration issues")
        
        return integration_success
        
    except Exception as e:
        print(f"   ❌ Integration testing failed: {e}")
        return False

def main():
    """Run comprehensive Phase 3 testing"""
    print("🧪 PHASE 3: COMPREHENSIVE TESTING & VERIFICATION")
    print("=" * 80)
    
    results = {}
    
    # Test 1: Terminal order creation
    results['terminal_creation'] = test_terminal_order_creation()
    
    # Test 2: Web interface order creation
    results['web_creation'] = test_web_interface_order_creation()
    
    # Test 3: Order workflow routes
    results['workflow_routes'] = test_order_workflow_routes()
    
    # Test 4: Database operations
    results['database_ops'] = test_database_operations()
    
    # Test 5: Frontend-backend integration
    results['integration'] = test_frontend_backend_integration()
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 PHASE 3 TESTING RESULTS")
    print("=" * 80)
    
    for test, success in results.items():
        status = "✅ SUCCESS" if success else "❌ FAILED"
        print(f"{test.replace('_', ' ').title()}: {status}")
    
    total_success = sum(results.values())
    total_tests = len(results)
    success_rate = total_success / total_tests * 100
    
    print(f"\n🎯 Overall Success Rate: {total_success}/{total_tests} ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        print("\n🎉 PHASE 3 SUCCESSFUL!")
        print("✅ Terminal order creation working")
        print("✅ Web interface order creation working")
        print("✅ Order workflow routes functional")
        print("✅ Database operations stable")
        print("✅ Frontend-backend integration solid")
        print("✅ UNIQUE constraint error RESOLVED!")
    else:
        print("\n⚠️  PHASE 3 PARTIAL SUCCESS")
        print(f"💡 Success rate: {success_rate:.1f}% (target: 80%)")
        print("💡 Some components may need additional fixes")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
