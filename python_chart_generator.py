#!/usr/bin/env python3
"""
Python Chart Generator Module
Enhanced chart generation with Python-specific features for Medivent ERP
"""

import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
import io
import base64
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# Set style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class PythonChartGenerator:
    """
    Advanced Python chart generator with Plotly and Matplotlib support
    """
    
    def __init__(self):
        self.default_colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', 
                              '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']
        
    def generate_interactive_sales_dashboard(self, data: List[Dict]) -> str:
        """
        Generate interactive sales dashboard with Plotly
        
        Args:
            data: Sales data with date, amount, product info
            
        Returns:
            HTML string with interactive dashboard
        """
        try:
            if not data:
                return self._generate_no_data_html("No sales data available")
            
            df = pd.DataFrame(data)
            df['date'] = pd.to_datetime(df['date'])
            
            # Create subplots
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=('Sales Trend', 'Daily Revenue', 'Top Products', 'Sales Distribution'),
                specs=[[{"secondary_y": True}, {"type": "bar"}],
                       [{"type": "pie"}, {"type": "histogram"}]]
            )
            
            # Sales trend line
            fig.add_trace(
                go.Scatter(x=df['date'], y=df['amount'], mode='lines+markers',
                          name='Sales', line=dict(color='#1f77b4', width=3)),
                row=1, col=1
            )
            
            # Daily revenue bars
            daily_revenue = df.groupby('date')['amount'].sum().reset_index()
            fig.add_trace(
                go.Bar(x=daily_revenue['date'], y=daily_revenue['amount'],
                      name='Daily Revenue', marker_color='#ff7f0e'),
                row=1, col=2
            )
            
            # Top products pie chart
            if 'product_name' in df.columns:
                product_sales = df.groupby('product_name')['amount'].sum().head(5)
                fig.add_trace(
                    go.Pie(labels=product_sales.index, values=product_sales.values,
                          name="Top Products"),
                    row=2, col=1
                )
            
            # Sales distribution histogram
            fig.add_trace(
                go.Histogram(x=df['amount'], name='Sales Distribution',
                           marker_color='#2ca02c'),
                row=2, col=2
            )
            
            # Update layout
            fig.update_layout(
                title_text="Sales Analytics Dashboard",
                showlegend=True,
                height=800,
                template="plotly_white"
            )
            
            return fig.to_html(include_plotlyjs='cdn')
            
        except Exception as e:
            return self._generate_error_html(f"Error generating sales dashboard: {str(e)}")
    
    def generate_inventory_heatmap(self, data: List[Dict]) -> str:
        """
        Generate inventory heatmap with Plotly
        
        Args:
            data: Inventory data with product, category, stock levels
            
        Returns:
            HTML string with interactive heatmap
        """
        try:
            if not data:
                return self._generate_no_data_html("No inventory data available")
            
            df = pd.DataFrame(data)
            
            # Create pivot table for heatmap
            if 'category' in df.columns and 'product_name' in df.columns:
                pivot_data = df.pivot_table(
                    values='current_stock', 
                    index='category', 
                    columns='product_name', 
                    fill_value=0
                )
                
                fig = go.Figure(data=go.Heatmap(
                    z=pivot_data.values,
                    x=pivot_data.columns,
                    y=pivot_data.index,
                    colorscale='RdYlGn',
                    text=pivot_data.values,
                    texttemplate="%{text}",
                    textfont={"size": 10},
                    hoverongaps=False
                ))
                
                fig.update_layout(
                    title="Inventory Stock Levels Heatmap",
                    xaxis_title="Products",
                    yaxis_title="Categories",
                    height=600,
                    template="plotly_white"
                )
                
                return fig.to_html(include_plotlyjs='cdn')
            else:
                return self._generate_error_html("Missing required columns for heatmap")
                
        except Exception as e:
            return self._generate_error_html(f"Error generating inventory heatmap: {str(e)}")
    
    def generate_3d_sales_analysis(self, data: List[Dict]) -> str:
        """
        Generate 3D sales analysis chart
        
        Args:
            data: Sales data with date, amount, product, region
            
        Returns:
            HTML string with 3D chart
        """
        try:
            if not data:
                return self._generate_no_data_html("No sales data available")
            
            df = pd.DataFrame(data)
            
            if all(col in df.columns for col in ['date', 'amount', 'product_name']):
                # Create 3D scatter plot
                fig = go.Figure(data=go.Scatter3d(
                    x=df['date'],
                    y=df['product_name'],
                    z=df['amount'],
                    mode='markers',
                    marker=dict(
                        size=8,
                        color=df['amount'],
                        colorscale='Viridis',
                        showscale=True,
                        colorbar=dict(title="Sales Amount")
                    ),
                    text=df.apply(lambda row: f"Product: {row['product_name']}<br>Date: {row['date']}<br>Amount: {row['amount']}", axis=1),
                    hovertemplate='%{text}<extra></extra>'
                ))
                
                fig.update_layout(
                    title="3D Sales Analysis",
                    scene=dict(
                        xaxis_title="Date",
                        yaxis_title="Product",
                        zaxis_title="Sales Amount"
                    ),
                    height=700,
                    template="plotly_white"
                )
                
                return fig.to_html(include_plotlyjs='cdn')
            else:
                return self._generate_error_html("Missing required columns for 3D analysis")
                
        except Exception as e:
            return self._generate_error_html(f"Error generating 3D sales analysis: {str(e)}")
    
    def generate_advanced_matplotlib_chart(self, data: List[Dict], chart_type: str = 'advanced_sales') -> str:
        """
        Generate advanced matplotlib charts with custom styling
        
        Args:
            data: Chart data
            chart_type: Type of advanced chart to generate
            
        Returns:
            Base64 encoded chart image
        """
        try:
            if not data:
                return self._generate_no_data_chart("No data available")
            
            df = pd.DataFrame(data)
            
            # Set up the matplotlib style
            plt.style.use('seaborn-v0_8-darkgrid')
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle('Advanced Sales Analytics', fontsize=20, fontweight='bold')
            
            if chart_type == 'advanced_sales' and 'amount' in df.columns:
                # Sales trend with confidence interval
                if 'date' in df.columns:
                    df['date'] = pd.to_datetime(df['date'])
                    daily_sales = df.groupby('date')['amount'].agg(['mean', 'std']).reset_index()
                    
                    axes[0, 0].plot(daily_sales['date'], daily_sales['mean'], 
                                   color='#1f77b4', linewidth=2, label='Average Sales')
                    axes[0, 0].fill_between(daily_sales['date'], 
                                          daily_sales['mean'] - daily_sales['std'],
                                          daily_sales['mean'] + daily_sales['std'],
                                          alpha=0.3, color='#1f77b4', label='Confidence Interval')
                    axes[0, 0].set_title('Sales Trend with Confidence Interval')
                    axes[0, 0].legend()
                    axes[0, 0].tick_params(axis='x', rotation=45)
                
                # Sales distribution
                axes[0, 1].hist(df['amount'], bins=20, alpha=0.7, color='#ff7f0e', edgecolor='black')
                axes[0, 1].axvline(df['amount'].mean(), color='red', linestyle='--', 
                                  label=f'Mean: {df["amount"].mean():.2f}')
                axes[0, 1].set_title('Sales Distribution')
                axes[0, 1].legend()
                
                # Box plot for sales by category (if available)
                if 'category' in df.columns:
                    df.boxplot(column='amount', by='category', ax=axes[1, 0])
                    axes[1, 0].set_title('Sales by Category')
                    axes[1, 0].set_xlabel('Category')
                else:
                    axes[1, 0].text(0.5, 0.5, 'Category data not available', 
                                   ha='center', va='center', transform=axes[1, 0].transAxes)
                
                # Correlation heatmap (if multiple numeric columns)
                numeric_cols = df.select_dtypes(include=[np.number]).columns
                if len(numeric_cols) > 1:
                    corr_matrix = df[numeric_cols].corr()
                    im = axes[1, 1].imshow(corr_matrix, cmap='coolwarm', aspect='auto')
                    axes[1, 1].set_xticks(range(len(corr_matrix.columns)))
                    axes[1, 1].set_yticks(range(len(corr_matrix.columns)))
                    axes[1, 1].set_xticklabels(corr_matrix.columns, rotation=45)
                    axes[1, 1].set_yticklabels(corr_matrix.columns)
                    axes[1, 1].set_title('Correlation Matrix')
                    
                    # Add correlation values
                    for i in range(len(corr_matrix.columns)):
                        for j in range(len(corr_matrix.columns)):
                            axes[1, 1].text(j, i, f'{corr_matrix.iloc[i, j]:.2f}',
                                           ha='center', va='center', color='white')
                else:
                    axes[1, 1].text(0.5, 0.5, 'Insufficient numeric data for correlation', 
                                   ha='center', va='center', transform=axes[1, 1].transAxes)
            
            plt.tight_layout()
            return self._chart_to_base64(fig)
            
        except Exception as e:
            return self._generate_error_chart(f"Error generating advanced chart: {str(e)}")
    
    def generate_real_time_dashboard(self, data: List[Dict]) -> str:
        """
        Generate real-time dashboard template
        
        Args:
            data: Real-time data
            
        Returns:
            HTML string with real-time dashboard
        """
        try:
            dashboard_html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Real-Time Dashboard</title>
                <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .dashboard-container {{ display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }}
                    .chart-container {{ border: 1px solid #ddd; padding: 15px; border-radius: 8px; }}
                    .metric-card {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                                   color: white; padding: 20px; border-radius: 8px; text-align: center; }}
                </style>
            </head>
            <body>
                <h1>🏥 Medivent ERP - Real-Time Dashboard</h1>
                
                <div class="dashboard-container">
                    <div class="metric-card">
                        <h3>Total Sales Today</h3>
                        <h2 id="total-sales">₹{sum(item.get('amount', 0) for item in data):,.2f}</h2>
                    </div>
                    
                    <div class="metric-card">
                        <h3>Orders Count</h3>
                        <h2 id="orders-count">{len(data)}</h2>
                    </div>
                    
                    <div class="chart-container">
                        <div id="sales-chart"></div>
                    </div>
                    
                    <div class="chart-container">
                        <div id="status-chart"></div>
                    </div>
                </div>
                
                <script>
                    // Initialize charts
                    var salesData = {json.dumps(data)};
                    
                    // Sales trend chart
                    var salesTrace = {{
                        x: salesData.map(d => d.date),
                        y: salesData.map(d => d.amount),
                        type: 'scatter',
                        mode: 'lines+markers',
                        name: 'Sales'
                    }};
                    
                    Plotly.newPlot('sales-chart', [salesTrace], {{
                        title: 'Sales Trend',
                        xaxis: {{ title: 'Date' }},
                        yaxis: {{ title: 'Amount (₹)' }}
                    }});
                    
                    // Auto-refresh every 30 seconds
                    setInterval(function() {{
                        // In a real implementation, this would fetch new data
                        console.log('Refreshing dashboard data...');
                    }}, 30000);
                </script>
            </body>
            </html>
            """
            
            return dashboard_html
            
        except Exception as e:
            return self._generate_error_html(f"Error generating real-time dashboard: {str(e)}")
    
    def _chart_to_base64(self, fig) -> str:
        """Convert matplotlib figure to base64 string"""
        img_buffer = io.BytesIO()
        fig.savefig(img_buffer, format='png', bbox_inches='tight', 
                   facecolor='white', edgecolor='none', dpi=150)
        img_buffer.seek(0)
        img_str = base64.b64encode(img_buffer.getvalue()).decode()
        plt.close(fig)
        return f"data:image/png;base64,{img_str}"
    
    def _generate_no_data_chart(self, message: str) -> str:
        """Generate a chart showing no data message"""
        fig, ax = plt.subplots(figsize=(10, 6))
        ax.text(0.5, 0.5, message, ha='center', va='center', 
               fontsize=16, transform=ax.transAxes,
               bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.7))
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        return self._chart_to_base64(fig)
    
    def _generate_error_chart(self, error_message: str) -> str:
        """Generate a chart showing error message"""
        fig, ax = plt.subplots(figsize=(10, 6))
        ax.text(0.5, 0.5, f"Chart Generation Error:\n{error_message}", 
               ha='center', va='center', fontsize=12, transform=ax.transAxes,
               bbox=dict(boxstyle="round,pad=0.3", facecolor="lightcoral", alpha=0.7))
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        return self._chart_to_base64(fig)

    def generate_finance_charts(self, chart_data: Dict) -> str:
        """
        Generate interactive finance charts with Plotly

        Args:
            chart_data: Chart data with status, daily, weekly, monthly data

        Returns:
            HTML string with interactive finance charts
        """
        try:
            # Create subplots for finance dashboard
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=('Order Status Distribution', 'Daily Invoice Generation',
                               'Weekly Trends', 'Monthly Overview'),
                specs=[[{"type": "pie"}, {"type": "bar"}],
                       [{"type": "scatter"}, {"type": "bar"}]]
            )

            # 1. Order Status Distribution (Pie Chart)
            status_data = chart_data.get('status', {})
            if status_data.get('labels') and status_data.get('data'):
                fig.add_trace(
                    go.Pie(
                        labels=status_data['labels'],
                        values=status_data['data'],
                        name="Status Distribution",
                        hole=0.4,
                        marker_colors=['#ffc107', '#28a745', '#dc3545', '#007bff']
                    ),
                    row=1, col=1
                )

            # 2. Daily Invoice Generation (Bar Chart)
            daily_data = chart_data.get('daily', {})
            if daily_data.get('labels') and daily_data.get('data'):
                fig.add_trace(
                    go.Bar(
                        x=daily_data['labels'],
                        y=daily_data['data'],
                        name="Daily Generation",
                        marker_color='#007bff'
                    ),
                    row=1, col=2
                )

            # 3. Weekly Trends (Line Chart)
            weekly_data = chart_data.get('weekly', {})
            if weekly_data.get('labels') and weekly_data.get('data'):
                fig.add_trace(
                    go.Scatter(
                        x=weekly_data['labels'],
                        y=weekly_data['data'],
                        mode='lines+markers',
                        name="Weekly Trend",
                        line=dict(color='#28a745', width=3),
                        marker=dict(size=8)
                    ),
                    row=2, col=1
                )

            # 4. Monthly Overview (Bar Chart)
            monthly_data = chart_data.get('monthly', {})
            if monthly_data.get('labels') and monthly_data.get('data'):
                fig.add_trace(
                    go.Bar(
                        x=monthly_data['labels'],
                        y=monthly_data['data'],
                        name="Monthly Overview",
                        marker_color='#ffc107'
                    ),
                    row=2, col=2
                )

            # Update layout
            fig.update_layout(
                height=600,
                showlegend=False,
                title_text="Finance Dashboard - Real-time Analytics",
                title_x=0.5,
                font=dict(family="Arial, sans-serif", size=12),
                plot_bgcolor='rgba(0,0,0,0)',
                paper_bgcolor='rgba(0,0,0,0)'
            )

            # Convert to HTML
            chart_html = fig.to_html(
                include_plotlyjs='cdn',
                div_id="financeCharts",
                config={'displayModeBar': True, 'responsive': True}
            )

            return chart_html

        except Exception as e:
            print(f"Error generating finance charts: {e}")
            return self._generate_error_html(f"Finance chart generation error: {str(e)}")

    def generate_simple_status_chart(self, chart_data: Dict) -> str:
        """
        Generate a simple status chart that fits in a small container

        Args:
            chart_data: Chart data with status information

        Returns:
            HTML string with simple status chart
        """
        try:
            # Get status data
            status_data = chart_data.get('status', {})
            if not status_data.get('labels') or not status_data.get('data'):
                return self._generate_no_data_html("No status data available")

            # Create a simple pie chart
            fig = go.Figure(data=[go.Pie(
                labels=status_data['labels'],
                values=status_data['data'],
                hole=0.4,
                marker_colors=['#ffc107', '#28a745', '#dc3545', '#007bff'],
                textinfo='label+percent',
                textposition='inside',
                showlegend=False
            )])

            # Update layout for small container
            fig.update_layout(
                height=200,
                width=300,
                margin=dict(l=10, r=10, t=10, b=10),
                font=dict(size=10),
                plot_bgcolor='rgba(0,0,0,0)',
                paper_bgcolor='rgba(0,0,0,0)'
            )

            # Convert to HTML
            chart_html = fig.to_html(
                include_plotlyjs='cdn',
                div_id="statusChart",
                config={'displayModeBar': False, 'responsive': True}
            )

            return chart_html

        except Exception as e:
            print(f"Error generating simple status chart: {e}")
            return self._generate_error_html(f"Status chart generation error: {str(e)}")

    def _generate_no_data_html(self, message: str) -> str:
        """Generate HTML for no data"""
        return f"""
        <div style="text-align: center; padding: 50px; background-color: #f8f9fa; border-radius: 8px;">
            <h3 style="color: #6c757d;">{message}</h3>
            <p>Please check your data source and try again.</p>
        </div>
        """
    
    def _generate_error_html(self, error_message: str) -> str:
        """Generate HTML for errors"""
        return f"""
        <div style="text-align: center; padding: 50px; background-color: #f8d7da; border-radius: 8px; color: #721c24;">
            <h3>Chart Generation Error</h3>
            <p>{error_message}</p>
        </div>
        """

# Convenience functions for backward compatibility
def generate_interactive_dashboard(data):
    """Generate interactive dashboard - backward compatibility function"""
    generator = PythonChartGenerator()
    return generator.generate_interactive_sales_dashboard(data)

def generate_advanced_chart(data, chart_type='advanced_sales'):
    """Generate advanced chart - backward compatibility function"""
    generator = PythonChartGenerator()
    return generator.generate_advanced_matplotlib_chart(data, chart_type)
