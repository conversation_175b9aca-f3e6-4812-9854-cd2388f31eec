import sys
import os

print("=== MEDIVENT ERP SYSTEM CHECK ===")
print(f"Python: {sys.version}")
print(f"Working Directory: {os.getcwd()}")
print()

# Check key imports
modules_to_check = [
    'flask', 'sqlite3', 'pandas', 'numpy', 'matplotlib', 
    'reportlab', 'PIL', 'requests', 'bleach'
]

print("CHECKING CORE MODULES:")
for module in modules_to_check:
    try:
        if module == 'PIL':
            import PIL
            print(f"[OK] {module}")
        else:
            __import__(module)
            print(f"[OK] {module}")
    except ImportError:
        print(f"[MISSING] {module}")

print()

# Check database
print("CHECKING DATABASE:")
db_path = os.path.join('instance', 'medivent.db')
if os.path.exists(db_path):
    print(f"[OK] Database found: {db_path}")
    try:
        import sqlite3
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' LIMIT 5;")
        tables = cursor.fetchall()
        print(f"[OK] Database accessible, sample tables: {[t[0] for t in tables]}")
        conn.close()
    except Exception as e:
        print(f"[ERROR] Database connection failed: {e}")
else:
    print(f"[MISSING] Database not found at: {db_path}")

print()

# Check if app can be imported
print("CHECKING APPLICATION:")
try:
    # Try to import the main app module
    import app
    print("[OK] Main application module can be imported")
except Exception as e:
    print(f"[ERROR] Cannot import app: {e}")

print()
print("=== CHECK COMPLETE ===")
