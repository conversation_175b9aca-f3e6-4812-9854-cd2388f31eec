#!/usr/bin/env python3
"""
Quick database analysis for rider tables and report issues
"""

import sqlite3
import os

def analyze_database():
    """Analyze the database for rider tables and report issues"""
    try:
        # Connect to database
        db_path = 'instance/medivent.db'
        if not os.path.exists(db_path):
            print(f"❌ Database not found at {db_path}")
            return False
            
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 DATABASE ANALYSIS")
        print("=" * 50)
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        all_tables = [table[0] for table in cursor.fetchall()]
        
        print(f"📊 Total tables: {len(all_tables)}")
        
        # Find rider-related tables
        rider_tables = [table for table in all_tables if 'rider' in table.lower()]
        print(f"\n🏍️ Rider-related tables: {len(rider_tables)}")
        for table in rider_tables:
            print(f"  - {table}")
        
        # Check if riders table exists
        if 'riders' in all_tables:
            print(f"\n✅ riders table EXISTS")
            cursor.execute("PRAGMA table_info(riders)")
            columns = cursor.fetchall()
            print(f"   Columns ({len(columns)}):")
            for col in columns:
                col_id, name, data_type, not_null, default_val, pk = col
                print(f"     {name}: {data_type}")
        else:
            print(f"\n❌ riders table MISSING")
        
        # Check orders table for rider_id column
        if 'orders' in all_tables:
            print(f"\n📋 orders table analysis:")
            cursor.execute("PRAGMA table_info(orders)")
            columns = cursor.fetchall()
            rider_cols = [col for col in columns if 'rider' in col[1].lower()]
            if rider_cols:
                print(f"   Rider-related columns:")
                for col in rider_cols:
                    print(f"     {col[1]}: {col[2]}")
            else:
                print(f"   ❌ No rider-related columns found")
        
        # Check for report template files
        print(f"\n📄 Template analysis:")
        template_path = 'templates/riders/reports'
        if os.path.exists(template_path):
            files = os.listdir(template_path)
            print(f"   Existing report templates ({len(files)}):")
            for file in files:
                print(f"     - {file}")
        else:
            print(f"   ❌ Reports template directory missing")
        
        conn.close()
        print(f"\n✅ Analysis completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        return False

if __name__ == "__main__":
    analyze_database()
