import sqlite3

try:
    conn = sqlite3.connect('instance/medivent.db')
    cursor = conn.cursor()
    
    print("Database connection successful!")
    
    # Test products table
    cursor.execute("SELECT COUNT(*) FROM products WHERE status = 'active' AND is_active = 1")
    active_products = cursor.fetchone()[0]
    print(f"Active products: {active_products}")
    
    # Test dc_pending_quantities table
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='dc_pending_quantities'")
    table_exists = cursor.fetchone() is not None
    print(f"DC pending table exists: {table_exists}")
    
    if not table_exists:
        print("Creating dc_pending_quantities table...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS dc_pending_quantities (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id TEXT NOT NULL,
                product_id TEXT NOT NULL,
                pending_quantity REAL NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status TEXT DEFAULT 'pending',
                notes TEXT,
                FOREIGN KEY (order_id) REFERENCES orders(order_id),
                FOREIGN KEY (product_id) REFERENCES products(product_id),
                UNIQUE(order_id, product_id)
            )
        ''')
        conn.commit()
        print("Table created successfully!")
    
    conn.close()
    print("All tests passed!")
    
except Exception as e:
    print(f"Error: {e}")
