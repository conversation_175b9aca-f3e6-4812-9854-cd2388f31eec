import sqlite3
import uuid
from datetime import datetime

# Quick finance fixes
conn = sqlite3.connect('instance/medivent.db')
cursor = conn.cursor()

print("=== QUICK FINANCE FIXES ===")

try:
    # 1. Create sample held invoices
    print("1. Creating held invoices...")
    
    # Get some orders to put on hold
    cursor.execute('''
        SELECT order_id, customer_name, order_amount 
        FROM orders 
        WHERE status != 'Cancelled' 
        LIMIT 3
    ''')
    orders = cursor.fetchall()
    
    for order in orders:
        hold_id = f"HOLD{datetime.now().strftime('%Y%m%d')}{str(uuid.uuid4())[:6].upper()}"
        
        cursor.execute('''
            INSERT OR IGNORE INTO invoice_holds 
            (hold_id, order_id, hold_reason, hold_comments, hold_date, hold_by, priority_level, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            hold_id,
            order[0],  # order_id
            'Credit verification required',
            f'Order {order[0]} put on hold for verification',
            datetime.now().isoformat(),
            'Admin',
            'normal',
            'active'
        ))
        print(f"  Created hold: {hold_id} for order {order[0]}")
    
    # 2. Ensure some orders have pending payment status
    print("2. Setting some orders to pending payment...")
    
    cursor.execute('''
        UPDATE orders 
        SET payment_status = 'pending' 
        WHERE order_id IN (
            SELECT order_id FROM orders 
            WHERE status != 'Cancelled' 
            LIMIT 5
        )
    ''')
    print(f"  Updated orders to pending payment status")
    
    # 3. Create accounts receivable table if missing
    print("3. Creating accounts receivable table...")
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS accounts_receivable (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            customer_name TEXT NOT NULL,
            order_id TEXT NOT NULL,
            invoice_amount REAL NOT NULL,
            paid_amount REAL DEFAULT 0,
            outstanding_amount REAL NOT NULL,
            due_date DATE,
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status TEXT DEFAULT 'pending'
        )
    ''')
    print("  Accounts receivable table created")
    
    # Commit changes
    conn.commit()
    print("✅ All fixes applied successfully!")
    
    # Verification
    cursor.execute('SELECT COUNT(*) FROM invoice_holds WHERE status = "active"')
    held_count = cursor.fetchone()[0]
    print(f"Active held invoices: {held_count}")
    
    cursor.execute('SELECT COUNT(*) FROM orders WHERE payment_status = "pending"')
    pending_count = cursor.fetchone()[0]
    print(f"Orders with pending payments: {pending_count}")
    
except Exception as e:
    print(f"Error: {e}")
    conn.rollback()

finally:
    conn.close()

print("=== FIXES COMPLETE ===")
