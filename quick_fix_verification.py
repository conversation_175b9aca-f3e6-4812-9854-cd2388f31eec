#!/usr/bin/env python3
"""
Quick verification of the three critical error fixes
"""

import os
import re

def check_template_max_filters():
    """Check if templates have proper conditional checks for max filters"""
    print("🔍 CHECKING TEMPLATE MAX FILTER FIXES...")
    
    template_files = [
        "templates/riders/reports/delivery_report.html",
        "templates/riders/reports/efficiency_report.html", 
        "templates/riders/reports/geographic_report.html",
        "templates/riders/reports/time_analysis_report.html"
    ]
    
    all_fixed = True
    
    for template_file in template_files:
        if os.path.exists(template_file):
            with open(template_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for unsafe max filter usage (without length check)
            unsafe_patterns = [
                r'{% set .* = .* \| max\(.*\) %}',  # max without conditional
                r'report_data\.\w+ \| max\('  # direct max usage
            ]
            
            # Check for safe patterns (with length check)
            safe_patterns = [
                r'{% if .* and .*\|length > 0 %}',  # proper conditional
                r'{% if .* and .*\|length > 0 %}\s*{% set .* = .* \| max\('  # safe max usage
            ]
            
            has_unsafe = False
            for pattern in unsafe_patterns:
                if re.search(pattern, content):
                    # Check if it's actually safe (has conditional)
                    lines = content.split('\n')
                    for i, line in enumerate(lines):
                        if re.search(pattern, line):
                            # Check previous lines for conditional
                            prev_lines = '\n'.join(lines[max(0, i-3):i+1])
                            if not re.search(r'{% if .* and .*\|length > 0 %}', prev_lines):
                                has_unsafe = True
                                break
            
            if has_unsafe:
                print(f"❌ {template_file} - Still has unsafe max filter usage")
                all_fixed = False
            else:
                print(f"✅ {template_file} - Max filter usage is safe")
        else:
            print(f"⚠️ {template_file} - File not found")
    
    return all_fixed

def check_json_serialization_fixes():
    """Check if JSON serialization is properly handled"""
    print("\n🔍 CHECKING JSON SERIALIZATION FIXES...")
    
    # Check routes/modern_riders.py for proper dict conversion
    routes_file = "routes/modern_riders.py"
    
    if os.path.exists(routes_file):
        with open(routes_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for safe_datetime_format usage
        if 'safe_datetime_format' in content:
            print(f"✅ {routes_file} - Uses safe_datetime_format function")
        else:
            print(f"❌ {routes_file} - Missing safe_datetime_format function")
            return False
        
        # Check for dict conversion in API endpoints
        if 'dict(rider)' in content or 'dict(row)' in content:
            print(f"✅ {routes_file} - Has proper Row to dict conversion")
        else:
            print(f"⚠️ {routes_file} - May need Row to dict conversion")
        
        return True
    else:
        print(f"❌ {routes_file} - File not found")
        return False

def check_datetime_fixes():
    """Check if datetime handling is properly implemented"""
    print("\n🔍 CHECKING DATETIME FIXES...")
    
    # Check if safe_datetime_format function exists
    routes_file = "routes/modern_riders.py"
    
    if os.path.exists(routes_file):
        with open(routes_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for safe_datetime_format function definition
        if 'def safe_datetime_format(' in content:
            print(f"✅ {routes_file} - safe_datetime_format function defined")
        else:
            print(f"❌ {routes_file} - safe_datetime_format function missing")
            return False
        
        # Check for usage of safe_datetime_format
        usage_count = content.count('safe_datetime_format(')
        if usage_count > 0:
            print(f"✅ {routes_file} - safe_datetime_format used {usage_count} times")
        else:
            print(f"⚠️ {routes_file} - safe_datetime_format not used")
        
        return True
    else:
        print(f"❌ {routes_file} - File not found")
        return False

def check_app_template_filters():
    """Check if app.py has proper template filters"""
    print("\n🔍 CHECKING APP.PY TEMPLATE FILTERS...")
    
    app_file = "app.py"
    
    if os.path.exists(app_file):
        with open(app_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for format_datetime filter
        if 'def format_datetime(' in content:
            print(f"✅ {app_file} - format_datetime filter defined")
        else:
            print(f"❌ {app_file} - format_datetime filter missing")
            return False
        
        # Check if filter is registered
        if 'app.jinja_env.filters[' in content and 'format_datetime' in content:
            print(f"✅ {app_file} - format_datetime filter registered")
        else:
            print(f"⚠️ {app_file} - format_datetime filter may not be registered")
        
        return True
    else:
        print(f"❌ {app_file} - File not found")
        return False

def main():
    """Run all verification checks"""
    print("🎯 QUICK FIX VERIFICATION")
    print("="*50)
    print("Verifying fixes for:")
    print("1. JSON Serialization Issue")
    print("2. 'max' Undefined Error")
    print("3. DateTime strftime Error")
    print("="*50)
    
    # Run all checks
    check1 = check_json_serialization_fixes()
    check2 = check_template_max_filters()
    check3 = check_datetime_fixes()
    check4 = check_app_template_filters()
    
    # Summary
    print("\n" + "="*50)
    print("📊 VERIFICATION SUMMARY")
    print("="*50)
    
    total_checks = 4
    passed_checks = sum([check1, check2, check3, check4])
    
    print(f"✅ Passed: {passed_checks}/{total_checks}")
    print(f"❌ Failed: {total_checks - passed_checks}/{total_checks}")
    
    if passed_checks == total_checks:
        print("\n🎉 ALL FIXES VERIFIED SUCCESSFULLY!")
        print("The three critical errors should now be resolved.")
    else:
        print("\n⚠️ Some fixes may need attention.")
    
    return passed_checks == total_checks

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
