#!/usr/bin/env python3
"""
Quick test for invoice generation fix
"""

import sqlite3
import requests
import json

def main():
    print("🧾 QUICK INVOICE GENERATION TEST")
    print("=" * 40)
    
    # Check database
    try:
        db = sqlite3.connect('instance/medivent.db')
        db.row_factory = sqlite3.Row
        
        # Find any order for testing
        order = db.execute('''
            SELECT * FROM orders 
            WHERE status IN ('Finance Pending', 'Approved', 'Processing')
            LIMIT 1
        ''').fetchone()
        
        if not order:
            print("❌ No suitable orders found for testing")
            return False
            
        print(f"✅ Testing with order: {order['order_id']}")
        print(f"   Customer: {order['customer_name']}")
        print(f"   Amount: Rs.{order['order_amount']}")
        
        db.close()
        
        # Test API call
        test_data = {
            "order_id": order['order_id'],
            "customer_name": order['customer_name'],
            "order_amount": float(order['order_amount']),
            "finance_user_approved": True
        }
        
        print(f"\n📤 Testing API call...")
        
        response = requests.post(
            "http://127.0.0.1:5001/finance/api/generate-invoice",
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"📥 Response: HTTP {response.status_code}")
        
        if response.status_code == 200:
            print("✅ SUCCESS - Invoice generation API working!")
            print("✅ sqlite3.Row .get() error FIXED!")
            return True
        else:
            print(f"❌ FAILED - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == '__main__':
    success = main()
    print(f"\n🎯 Result: {'PASS' if success else 'FAIL'}")
