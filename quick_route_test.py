#!/usr/bin/env python3
"""
Quick test to verify the packed_at fix is working
"""

import requests
import time

def quick_test():
    """Quick test of the fixed routes"""
    print("=== QUICK ROUTE TEST ===")
    print("Testing the fixed warehouse routes")
    print()
    
    base_url = "http://127.0.0.1:5000"
    
    # Test the routes that were failing
    test_routes = [
        (f"{base_url}/", "Main Dashboard"),
        (f"{base_url}/warehouse/packing", "Warehouse Packing Dashboard"),
        (f"{base_url}/warehouse/orders", "Warehouse Orders"),
    ]
    
    for url, name in test_routes:
        try:
            print(f"Testing {name}...")
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                print(f"✅ {name}: Working (HTTP 200)")
                print(f"   Response length: {len(response.text)} characters")
            else:
                print(f"❌ {name}: HTTP {response.status_code}")
        except requests.exceptions.ConnectionError:
            print(f"❌ {name}: Connection refused - server not running")
        except Exception as e:
            print(f"❌ {name}: Error - {e}")
        
        time.sleep(1)
    
    print("\n" + "="*40)
    print("Quick test completed!")

if __name__ == "__main__":
    quick_test()
