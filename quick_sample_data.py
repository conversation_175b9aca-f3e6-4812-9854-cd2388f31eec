#!/usr/bin/env python3
"""
Quick Sample Data Creation
Simple script to create minimal sample data for testing
"""

import sqlite3

def create_quick_sample_data():
    """Create minimal sample data quickly"""
    print("🔧 Creating quick sample data...")
    
    try:
        conn = sqlite3.connect('ledger.db')
        cursor = conn.cursor()
        
        # 1. Create orders table
        print("1️⃣ Creating orders table...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS orders (
                order_id TEXT PRIMARY KEY,
                customer_name TEXT NOT NULL,
                customer_address TEXT,
                customer_phone TEXT,
                order_date DATE,
                order_amount DECIMAL(10,2),
                status TEXT DEFAULT 'pending'
            )
        ''')
        
        # 2. Create partial_dc_tracking table
        print("2️⃣ Creating partial_dc_tracking table...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS partial_dc_tracking (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id TEXT NOT NULL,
                dc_number TEXT,
                product_id TEXT NOT NULL,
                product_name TEXT,
                strength TEXT,
                original_quantity INTEGER NOT NULL,
                delivered_quantity INTEGER DEFAULT 0,
                pending_quantity INTEGER NOT NULL,
                status TEXT DEFAULT 'pending',
                priority_level INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT
            )
        ''')
        
        # 3. Insert sample orders
        print("3️⃣ Inserting sample orders...")
        orders = [
            ('ORD-001', 'John Doe', '123 Main St', '9876543210', '2024-01-15', 2500.00, 'partial'),
            ('ORD-002', 'Jane Smith', '456 Oak Ave', '9876543211', '2024-01-16', 1875.00, 'partial'),
            ('ORD-003', 'Bob Johnson', '789 Pine Rd', '9876543212', '2024-01-17', 5000.00, 'partial'),
        ]
        
        for order in orders:
            cursor.execute('''
                INSERT OR REPLACE INTO orders 
                (order_id, customer_name, customer_address, customer_phone, order_date, order_amount, status)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', order)
        
        # 4. Insert sample partial tracking
        print("4️⃣ Inserting sample partial tracking...")
        partial_items = [
            ('ORD-001', 'DC-001', 'PROD-001', 'Paracetamol Tablets', '500mg', 100, 60, 40, 'pending', 4),
            ('ORD-001', 'DC-001', 'PROD-002', 'Ibuprofen Tablets', '400mg', 50, 30, 20, 'pending', 3),
            ('ORD-002', 'DC-002', 'PROD-003', 'Amoxicillin Capsules', '250mg', 75, 25, 50, 'pending', 5),
            ('ORD-003', 'DC-003', 'PROD-004', 'Vitamin C Tablets', '1000mg', 200, 150, 50, 'pending', 2),
        ]
        
        for item in partial_items:
            cursor.execute('''
                INSERT OR REPLACE INTO partial_dc_tracking 
                (order_id, dc_number, product_id, product_name, strength, original_quantity, 
                 delivered_quantity, pending_quantity, status, priority_level, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'system')
            ''', item)
        
        # 5. Create other tables (optional)
        print("5️⃣ Creating optional tables...")
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS realtime_inventory_status (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id TEXT NOT NULL,
                warehouse_id TEXT DEFAULT 'MAIN',
                current_stock INTEGER DEFAULT 0,
                available_stock INTEGER DEFAULT 0,
                reserved_stock INTEGER DEFAULT 0,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS inventory_notifications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id TEXT NOT NULL,
                notification_type TEXT NOT NULL,
                title TEXT NOT NULL,
                message TEXT NOT NULL,
                priority TEXT DEFAULT 'medium',
                is_read BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 6. Insert sample inventory
        inventory_items = [
            ('PROD-001', 'MAIN', 150, 120, 30),
            ('PROD-002', 'MAIN', 80, 60, 20),
            ('PROD-003', 'MAIN', 25, 15, 10),
            ('PROD-004', 'MAIN', 300, 280, 20),
        ]
        
        for inv in inventory_items:
            cursor.execute('''
                INSERT OR REPLACE INTO realtime_inventory_status 
                (product_id, warehouse_id, current_stock, available_stock, reserved_stock)
                VALUES (?, ?, ?, ?, ?)
            ''', inv)
        
        # 7. Insert sample notifications
        notifications = [
            ('PROD-001', 'stock_available', 'Stock Available', 'Paracetamol 500mg now available', 'high'),
            ('PROD-003', 'low_stock', 'Low Stock Alert', 'Amoxicillin 250mg running low', 'urgent'),
        ]
        
        for notif in notifications:
            cursor.execute('''
                INSERT OR REPLACE INTO inventory_notifications 
                (product_id, notification_type, title, message, priority)
                VALUES (?, ?, ?, ?, ?)
            ''', notif)
        
        conn.commit()
        print("✅ Quick sample data created successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    success = create_quick_sample_data()
    if success:
        print("\n🎉 Sample data ready!")
        print("🌐 Visit: http://127.0.0.1:5001/partial-pending/")
    else:
        print("\n💥 Failed to create sample data")
