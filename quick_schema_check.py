import sqlite3

conn = sqlite3.connect('instance/medivent.db')
cursor = conn.cursor()

print("=== INVOICES TABLE SCHEMA ===")
cursor.execute("PRAGMA table_info(invoices)")
invoice_cols = cursor.fetchall()
for col in invoice_cols:
    print(f"{col[1]} ({col[2]})")

print("\n=== CHALLANS TABLE SCHEMA ===")
cursor.execute("PRAGMA table_info(challans)")
challan_cols = cursor.fetchall()
for col in challan_cols:
    print(f"{col[1]} ({col[2]})")

conn.close()
