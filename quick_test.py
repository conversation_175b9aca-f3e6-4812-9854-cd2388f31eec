import requests

try:
    response = requests.get('http://127.0.0.1:5001/warehouse/packing', timeout=5)
    print(f'Status: {response.status_code}')
    if response.status_code == 200:
        print('✅ Warehouse packing page loads')
        content = response.text
        if 'printAddress' in content:
            print('✅ printAddress function found')
        if 'packOrder' in content:
            print('✅ packOrder function found')
        if 'QR Code' in content:
            print('❌ QR Code still present')
        else:
            print('✅ QR Code removed')
    else:
        print(f'❌ Error: {response.status_code}')
except Exception as e:
    print(f'❌ Connection error: {e}')
