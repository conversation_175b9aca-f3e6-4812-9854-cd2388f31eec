#!/usr/bin/env python3
"""
Quick test to verify invoice generation fix
"""

import requests
import json

def quick_test():
    """Quick test of the invoice generation fix"""
    
    print("⚡ QUICK INVOICE GENERATION FIX TEST")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5001"
    
    # Test 1: Check if app is running
    print("🌐 Testing Flask app...")
    try:
        response = requests.get(base_url, timeout=5)
        if response.status_code == 200:
            print("✅ Flask app is running")
        else:
            print(f"⚠️  Flask app returned {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("❌ Flask app is not running")
        print("Please start the app with: python app.py")
        return
    except Exception as e:
        print(f"❌ Error: {e}")
        return
    
    # Test 2: Check pending invoices page
    print("\n📋 Testing pending invoices page...")
    try:
        response = requests.get(f"{base_url}/finance/pending-invoices")
        if response.status_code == 200:
            print("✅ Pending invoices page loads")
        else:
            print(f"❌ Pending invoices page error: {response.status_code}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 3: Login and test invoice generation
    print("\n🔐 Testing login and invoice generation...")
    session = requests.Session()
    
    try:
        # Login
        login_data = {'username': 'admin', 'password': 'admin123'}
        login_response = session.post(f'{base_url}/login', data=login_data)
        
        if login_response.status_code == 200 and 'dashboard' in login_response.url:
            print("✅ Login successful")
            
            # Test invoice generation
            invoice_data = {
                'order_id': 'ORD00000147',
                'customer_name': 'Munir Shah',
                'order_amount': 90000.0,
                'finance_user_approved': True,
                'timestamp': '2025-08-05T16:30:00.000Z'
            }
            
            response = session.post(
                f'{base_url}/finance/api/generate-invoice',
                json=invoice_data,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"✅ Invoice generation SUCCESS: {result.get('invoice_id')}")
                    print("🎉 FIX IS WORKING!")
                else:
                    print(f"❌ Invoice generation failed: {result.get('error')}")
                    print("🔧 Fix may need adjustment")
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                
        else:
            print("❌ Login failed")
            
    except Exception as e:
        print(f"❌ Test error: {e}")
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    quick_test()
