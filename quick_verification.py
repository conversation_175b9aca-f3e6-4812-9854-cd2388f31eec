#!/usr/bin/env python3
"""
Quick Verification of Fixes
"""

import sqlite3
import os
from datetime import datetime

def quick_verify():
    print("QUICK VERIFICATION OF FIXES")
    print("=" * 50)
    
    db_path = 'instance/medivent.db'
    if not os.path.exists(db_path):
        print("❌ Database not found")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 1. Check if estimated_delivery_date field exists
        cursor.execute('PRAGMA table_info(orders)')
        columns = cursor.fetchall()
        
        delivery_field_exists = False
        for col in columns:
            if 'estimated_delivery_date' in col[1]:
                delivery_field_exists = True
                print(f"✅ Found field: {col[1]} ({col[2]})")
                break
        
        if not delivery_field_exists:
            print("❌ estimated_delivery_date field not found")
        
        # 2. Check recent orders
        cursor.execute('''
            SELECT order_id, customer_name, estimated_delivery_date, order_date
            FROM orders 
            ORDER BY order_date DESC 
            LIMIT 5
        ''')
        orders = cursor.fetchall()
        
        print(f"\nRecent Orders ({len(orders)}):")
        for order in orders:
            delivery_status = "✅" if order['estimated_delivery_date'] else "❌"
            print(f"  {delivery_status} {order['order_id']}: {order['estimated_delivery_date'] or 'No delivery date'}")
        
        # 3. Check inventory vs products conflicts
        cursor.execute('''
            SELECT p.product_id, p.stock_quantity,
                   COALESCE(SUM(i.stock_quantity - i.allocated_quantity), 0) as available
            FROM products p
            LEFT JOIN inventory i ON p.product_id = i.product_id AND i.status = 'active'
            WHERE p.stock_quantity > 0
            GROUP BY p.product_id, p.stock_quantity
            LIMIT 5
        ''')
        stock_data = cursor.fetchall()
        
        print(f"\nStock Comparison ({len(stock_data)} products):")
        conflicts = 0
        for item in stock_data:
            if item['stock_quantity'] != item['available']:
                conflicts += 1
                print(f"  ❌ {item['product_id']}: Products={item['stock_quantity']}, Inventory={item['available']}")
            else:
                print(f"  ✅ {item['product_id']}: {item['stock_quantity']} (consistent)")
        
        print(f"\nConflicts found: {conflicts}")
        
        conn.close()
        
        # Summary
        print("\nSUMMARY:")
        print(f"✅ Delivery date field exists: {delivery_field_exists}")
        print(f"✅ Stock conflicts: {conflicts}")
        
        if delivery_field_exists and conflicts == 0:
            print("🎉 All fixes appear to be working!")
        else:
            print("❌ Some issues remain")
            
    except Exception as e:
        print(f"❌ Verification failed: {str(e)}")

if __name__ == "__main__":
    quick_verify()
