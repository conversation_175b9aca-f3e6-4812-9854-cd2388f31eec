import sqlite3
conn = sqlite3.connect('instance/medivent.db')
cursor = conn.cursor()

print("Quick verification:")
cursor.execute('SELECT COUNT(*) FROM invoice_holds WHERE status = "active"')
holds = cursor.fetchone()[0]
print(f"Active holds: {holds}")

cursor.execute('SELECT order_id FROM invoice_holds WHERE status = "active" LIMIT 3')
orders = cursor.fetchall()
print("Hold order IDs:", [o[0] for o in orders])

conn.close()
