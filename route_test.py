#!/usr/bin/env python3
"""
Test the route fixes
"""

import requests

def test_update_selection_route():
    """Test the update selection route"""
    try:
        print("🧪 Testing /products/update_selection route...")
        
        # Test the route
        url = "http://192.168.99.34:5001/products/update_selection"
        
        # Make request with timeout
        response = requests.get(url, timeout=10)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Route is accessible!")
            
            # Check if the response contains expected content
            content = response.text
            if 'stats' in content.lower():
                print("✅ Page contains stats variable")
            if 'products' in content.lower():
                print("✅ Page contains products")
            if 'error loading products' in content.lower():
                print("❌ Page shows error message")
                return False
                
            return True
        else:
            print(f"❌ Route returned status {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to the server. Is the Flask app running?")
        return False
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 TESTING ROUTE FIX")
    print("=" * 40)
    
    success = test_update_selection_route()
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 TEST PASSED!")
    else:
        print("❌ TEST FAILED!")
    print("=" * 40)
