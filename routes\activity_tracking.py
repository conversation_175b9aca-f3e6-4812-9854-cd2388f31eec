"""
Activity Tracking Routes for Medivent ERP
Handles user activity monitoring, session management, and auto-logout
"""

from flask import Blueprint, request, jsonify, session, current_app
from flask_login import login_required, current_user
from datetime import datetime
import json
from utils.session_manager import SessionManager

# Create blueprint
activity_bp = Blueprint('activity', __name__)

# Initialize session manager
session_manager = SessionManager()

@activity_bp.route('/api/activity/heartbeat', methods=['POST'])
@login_required
def heartbeat():
    """Update user activity timestamp (heartbeat signal)"""
    try:
        data = request.get_json() or {}
        
        # Update activity in session manager
        success = session_manager.update_activity(
            activity_type='heartbeat',
            page_url=data.get('page_url'),
            metadata={'timestamp': data.get('timestamp')}
        )
        
        if success:
            return jsonify({
                'success': True,
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to update activity'
            }), 500
            
    except Exception as e:
        current_app.logger.error(f"Heartbeat error: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500

@activity_bp.route('/api/activity/track', methods=['POST'])
@login_required
def track_activity():
    """Track specific user activity"""
    try:
        data = request.get_json() or {}
        
        activity_type = data.get('activity_type', 'unknown')
        page_url = data.get('page_url')
        metadata = data.get('metadata', {})
        
        # Add request info to metadata
        metadata.update({
            'user_agent': request.headers.get('User-Agent', ''),
            'ip_address': request.remote_addr,
            'referer': request.headers.get('Referer', '')
        })
        
        # Update activity in session manager
        success = session_manager.update_activity(
            activity_type=activity_type,
            page_url=page_url,
            metadata=metadata
        )
        
        if success:
            return jsonify({
                'success': True,
                'activity_type': activity_type,
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to track activity'
            }), 500
            
    except Exception as e:
        current_app.logger.error(f"Activity tracking error: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500

@activity_bp.route('/api/session/status', methods=['GET'])
@login_required
def session_status():
    """Get current session status and remaining time"""
    try:
        status = session_manager.get_session_status()
        
        return jsonify({
            'success': True,
            **status
        })
        
    except Exception as e:
        current_app.logger.error(f"Session status error: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'status': 'error',
            'remaining_time': 0
        }), 500

@activity_bp.route('/api/session/extend', methods=['POST'])
@login_required
def extend_session():
    """Extend current session"""
    try:
        # Update activity to extend session
        success = session_manager.update_activity(
            activity_type='session_extend',
            metadata={'reason': 'user_request'}
        )
        
        if success:
            return jsonify({
                'success': True,
                'message': 'Session extended successfully',
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to extend session'
            }), 500
            
    except Exception as e:
        current_app.logger.error(f"Session extend error: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500

@activity_bp.route('/api/session/info', methods=['GET'])
@login_required
def session_info():
    """Get detailed session information"""
    try:
        session_id = session.get('activity_session_id')
        
        if not session_id:
            return jsonify({
                'success': False,
                'error': 'No active session found'
            }), 404
        
        # Get session data
        sessions = session_manager.get_user_sessions(current_user.id, limit=1)
        activities = session_manager.get_session_activities(session_id, limit=20)
        
        current_session = sessions[0] if sessions else None
        
        return jsonify({
            'success': True,
            'session': current_session,
            'recent_activities': activities,
            'session_id': session_id
        })
        
    except Exception as e:
        current_app.logger.error(f"Session info error: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500

@activity_bp.route('/api/user/activity-summary', methods=['GET'])
@login_required
def user_activity_summary():
    """Get user activity summary"""
    try:
        # Get recent sessions for current user
        sessions = session_manager.get_user_sessions(current_user.id, limit=10)
        
        # Calculate summary statistics
        total_sessions = len(sessions)
        total_screen_time = sum(s.get('total_screen_time', 0) for s in sessions)
        total_active_time = sum(s.get('total_active_time', 0) for s in sessions)
        total_session_duration = sum(s.get('session_duration', 0) for s in sessions)
        
        # Get current session status
        current_status = session_manager.get_session_status()
        
        return jsonify({
            'success': True,
            'summary': {
                'total_sessions': total_sessions,
                'total_screen_time': total_screen_time,
                'total_active_time': total_active_time,
                'total_session_duration': total_session_duration,
                'average_session_duration': total_session_duration / max(total_sessions, 1)
            },
            'current_session': current_status,
            'recent_sessions': sessions[:5]
        })
        
    except Exception as e:
        current_app.logger.error(f"Activity summary error: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500

# Middleware to check session timeout on each request
@activity_bp.before_app_request
def check_session_timeout():
    """Check session timeout before each request"""
    try:
        # Skip for non-authenticated users and API endpoints
        if not current_user.is_authenticated:
            return
        
        # Skip for logout and session-related endpoints
        if request.endpoint in ['auth.logout', 'activity.session_status', 'activity.extend_session']:
            return
        
        # Check if session has timed out
        if session_manager.check_session_timeout():
            # Session has timed out, but don't force logout here
            # Let the frontend handle it via session status checks
            pass
        else:
            # Update activity for page views
            if request.method == 'GET' and not request.path.startswith('/api/'):
                session_manager.update_activity(
                    activity_type='page_view',
                    page_url=request.path
                )
                
    except Exception as e:
        current_app.logger.error(f"Session timeout check error: {e}")

# Helper function to initialize session on login
def initialize_user_session(user_id, username):
    """Initialize session when user logs in"""
    try:
        session_id = session_manager.create_session(
            user_id=user_id,
            username=username,
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        
        if session_id:
            current_app.logger.info(f"Session initialized for user {username}: {session_id}")
            return session_id
        else:
            current_app.logger.error(f"Failed to initialize session for user {username}")
            return None
            
    except Exception as e:
        current_app.logger.error(f"Session initialization error: {e}")
        return None

# Helper function to end session on logout
def end_user_session(reason='logout'):
    """End session when user logs out"""
    try:
        success = session_manager.end_session(reason=reason)
        
        if success:
            current_app.logger.info(f"Session ended: {reason}")
        else:
            current_app.logger.error(f"Failed to end session: {reason}")
            
        return success
        
    except Exception as e:
        current_app.logger.error(f"Session end error: {e}")
        return False
