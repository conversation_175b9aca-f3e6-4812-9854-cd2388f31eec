"""
Advanced Payment Management Routes
Unified system for payment collection, invoice knock-off, bulk processing, and analytics
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from datetime import datetime, timedelta
import sqlite3
import json
from werkzeug.utils import secure_filename
import os

advanced_payment_bp = Blueprint('advanced_payment', __name__, url_prefix='/advanced_payment')

def get_db():
    """Get database connection"""
    conn = sqlite3.connect('instance/medivent.db')
    conn.row_factory = sqlite3.Row
    return conn

@advanced_payment_bp.route('/')
@login_required
def dashboard():
    """Advanced Payment Management Dashboard"""
    try:
        db = get_db()
        
        # Get payment overview metrics
        today = datetime.now().date()
        month_start = today.replace(day=1)
        
        # Total payments this month
        monthly_payments = db.execute(
            "SELECT COALESCE(SUM(amount), 0) as total FROM payments WHERE DATE(payment_date) >= ?",
            (month_start,)
        ).fetchone()['total']
        
        # Pending payments
        pending_payments = db.execute(
            """SELECT COALESCE(SUM(o.total_amount - COALESCE(p.paid_amount, 0)), 0) as pending
               FROM orders o
               LEFT JOIN (
                   SELECT order_id, SUM(amount) as paid_amount 
                   FROM payments 
                   GROUP BY order_id
               ) p ON o.order_id = p.order_id
               WHERE o.status != 'Cancelled'"""
        ).fetchone()['pending']
        
        # Recent payments
        recent_payments = db.execute(
            """SELECT p.payment_id, p.amount, p.payment_date, p.payment_method,
                      o.customer_name, o.order_id
               FROM payments p
               LEFT JOIN orders o ON p.order_id = o.order_id
               ORDER BY p.payment_date DESC
               LIMIT 10"""
        ).fetchall()
        
        # Outstanding invoices
        outstanding_invoices = db.execute(
            """SELECT o.order_id, o.customer_name, o.total_amount,
                      COALESCE(p.paid_amount, 0) as paid_amount,
                      (o.total_amount - COALESCE(p.paid_amount, 0)) as outstanding
               FROM orders o
               LEFT JOIN (
                   SELECT order_id, SUM(amount) as paid_amount 
                   FROM payments 
                   GROUP BY order_id
               ) p ON o.order_id = p.order_id
               WHERE (o.total_amount - COALESCE(p.paid_amount, 0)) > 0
               ORDER BY o.order_date DESC
               LIMIT 10"""
        ).fetchall()
        
        context = {
            'title': 'Advanced Payment Management',
            'monthly_payments': monthly_payments,
            'pending_payments': pending_payments,
            'recent_payments': recent_payments,
            'outstanding_invoices': outstanding_invoices,
            'current_user': current_user,
            'now': datetime.now()
        }
        
        return render_template('advanced_payment/dashboard.html', **context)
        
    except Exception as e:
        flash(f'Error loading payment dashboard: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@advanced_payment_bp.route('/bulk_processing')
@login_required
def bulk_processing():
    """Bulk Payment Processing"""
    try:
        db = get_db()
        
        # Get customers with outstanding balances
        customers_with_balance = db.execute(
            """SELECT c.customer_id, c.name, c.email,
                      COALESCE(SUM(o.total_amount - COALESCE(p.paid_amount, 0)), 0) as outstanding_balance
               FROM customers c
               LEFT JOIN orders o ON c.customer_id = o.customer_id
               LEFT JOIN (
                   SELECT order_id, SUM(amount) as paid_amount 
                   FROM payments 
                   GROUP BY order_id
               ) p ON o.order_id = p.order_id
               WHERE o.status != 'Cancelled'
               GROUP BY c.customer_id
               HAVING outstanding_balance > 0
               ORDER BY outstanding_balance DESC"""
        ).fetchall()
        
        context = {
            'title': 'Bulk Payment Processing',
            'customers_with_balance': customers_with_balance,
            'current_user': current_user,
            'now': datetime.now()
        }
        
        return render_template('advanced_payment/bulk_processing.html', **context)
        
    except Exception as e:
        flash(f'Error loading bulk processing: {str(e)}', 'error')
        return redirect(url_for('advanced_payment.dashboard'))

@advanced_payment_bp.route('/automated_matching')
@login_required
def automated_matching():
    """Automated Payment Matching"""
    try:
        db = get_db()
        
        # Get unmatched payments
        unmatched_payments = db.execute(
            """SELECT p.payment_id, p.amount, p.payment_date, p.payment_method,
                      p.reference_number, p.notes
               FROM payments p
               WHERE p.order_id IS NULL OR p.order_id = 0
               ORDER BY p.payment_date DESC"""
        ).fetchall()
        
        # Get unmatched orders (with outstanding amounts)
        unmatched_orders = db.execute(
            """SELECT o.order_id, o.customer_name, o.total_amount,
                      COALESCE(p.paid_amount, 0) as paid_amount,
                      (o.total_amount - COALESCE(p.paid_amount, 0)) as outstanding
               FROM orders o
               LEFT JOIN (
                   SELECT order_id, SUM(amount) as paid_amount 
                   FROM payments 
                   GROUP BY order_id
               ) p ON o.order_id = p.order_id
               WHERE (o.total_amount - COALESCE(p.paid_amount, 0)) > 0
               ORDER BY o.order_date DESC"""
        ).fetchall()
        
        context = {
            'title': 'Automated Payment Matching',
            'unmatched_payments': unmatched_payments,
            'unmatched_orders': unmatched_orders,
            'current_user': current_user,
            'now': datetime.now()
        }
        
        return render_template('advanced_payment/automated_matching.html', **context)
        
    except Exception as e:
        flash(f'Error loading automated matching: {str(e)}', 'error')
        return redirect(url_for('advanced_payment.dashboard'))

@advanced_payment_bp.route('/file_upload')
@login_required
def file_upload():
    """File Upload for Payment Processing"""
    try:
        context = {
            'title': 'Payment File Upload',
            'current_user': current_user,
            'now': datetime.now()
        }
        
        return render_template('advanced_payment/file_upload.html', **context)
        
    except Exception as e:
        flash(f'Error loading file upload: {str(e)}', 'error')
        return redirect(url_for('advanced_payment.dashboard'))

@advanced_payment_bp.route('/reconciliation')
@login_required
def reconciliation():
    """Payment Reconciliation Tools"""
    try:
        db = get_db()
        
        # Get reconciliation summary
        today = datetime.now().date()
        month_start = today.replace(day=1)
        
        # Bank statement vs system comparison
        system_total = db.execute(
            "SELECT COALESCE(SUM(amount), 0) as total FROM payments WHERE DATE(payment_date) >= ?",
            (month_start,)
        ).fetchone()['total']
        
        # Payment methods breakdown
        payment_methods = db.execute(
            """SELECT payment_method, COUNT(*) as count, SUM(amount) as total
               FROM payments
               WHERE DATE(payment_date) >= ?
               GROUP BY payment_method
               ORDER BY total DESC""",
            (month_start,)
        ).fetchall()
        
        # Discrepancies (payments without orders or orders without payments)
        discrepancies = db.execute(
            """SELECT 'Payment without Order' as type, payment_id as id, amount, payment_date
               FROM payments
               WHERE order_id IS NULL OR order_id = 0
               UNION ALL
               SELECT 'Order without Payment' as type, order_id as id, total_amount as amount, order_date
               FROM orders
               WHERE order_id NOT IN (SELECT DISTINCT order_id FROM payments WHERE order_id IS NOT NULL)
               AND status != 'Cancelled'
               ORDER BY payment_date DESC"""
        ).fetchall()
        
        context = {
            'title': 'Payment Reconciliation',
            'system_total': system_total,
            'payment_methods': payment_methods,
            'discrepancies': discrepancies,
            'current_user': current_user,
            'now': datetime.now()
        }
        
        return render_template('advanced_payment/reconciliation.html', **context)
        
    except Exception as e:
        flash(f'Error loading reconciliation: {str(e)}', 'error')
        return redirect(url_for('advanced_payment.dashboard'))

@advanced_payment_bp.route('/analytics')
@login_required
def analytics():
    """Payment Analytics and Reporting"""
    try:
        db = get_db()
        
        # Payment trends (last 12 months)
        payment_trends = db.execute(
            """SELECT strftime('%Y-%m', payment_date) as month,
                      COUNT(*) as payment_count,
                      SUM(amount) as total_amount
               FROM payments
               WHERE payment_date >= DATE('now', '-12 months')
               GROUP BY strftime('%Y-%m', payment_date)
               ORDER BY month"""
        ).fetchall()
        
        # Top customers by payment volume
        top_customers = db.execute(
            """SELECT o.customer_name, COUNT(p.payment_id) as payment_count,
                      SUM(p.amount) as total_paid
               FROM payments p
               LEFT JOIN orders o ON p.order_id = o.order_id
               WHERE p.payment_date >= DATE('now', '-6 months')
               GROUP BY o.customer_name
               ORDER BY total_paid DESC
               LIMIT 10"""
        ).fetchall()
        
        # Payment method analysis
        method_analysis = db.execute(
            """SELECT payment_method, 
                      COUNT(*) as transaction_count,
                      SUM(amount) as total_amount,
                      AVG(amount) as avg_amount
               FROM payments
               WHERE payment_date >= DATE('now', '-3 months')
               GROUP BY payment_method
               ORDER BY total_amount DESC"""
        ).fetchall()
        
        context = {
            'title': 'Payment Analytics',
            'payment_trends': payment_trends,
            'top_customers': top_customers,
            'method_analysis': method_analysis,
            'current_user': current_user,
            'now': datetime.now()
        }
        
        return render_template('advanced_payment/analytics.html', **context)
        
    except Exception as e:
        flash(f'Error loading analytics: {str(e)}', 'error')
        return redirect(url_for('advanced_payment.dashboard'))

@advanced_payment_bp.route('/process_bulk_payment', methods=['POST'])
@login_required
def process_bulk_payment():
    """Process bulk payment"""
    try:
        db = get_db()
        
        customer_ids = request.form.getlist('customer_ids')
        payment_amount = float(request.form.get('payment_amount', 0))
        payment_method = request.form.get('payment_method', 'Cash')
        payment_date = request.form.get('payment_date', datetime.now().date())
        notes = request.form.get('notes', '')
        
        if not customer_ids or payment_amount <= 0:
            flash('Please select customers and enter a valid payment amount', 'error')
            return redirect(url_for('advanced_payment.bulk_processing'))
        
        # Process payments for each customer
        processed_count = 0
        for customer_id in customer_ids:
            # Get the oldest unpaid order for this customer
            oldest_order = db.execute(
                """SELECT o.order_id, (o.total_amount - COALESCE(p.paid_amount, 0)) as outstanding
                   FROM orders o
                   LEFT JOIN (
                       SELECT order_id, SUM(amount) as paid_amount 
                       FROM payments 
                       GROUP BY order_id
                   ) p ON o.order_id = p.order_id
                   WHERE o.customer_id = ? AND (o.total_amount - COALESCE(p.paid_amount, 0)) > 0
                   ORDER BY o.order_date ASC
                   LIMIT 1""",
                (customer_id,)
            ).fetchone()
            
            if oldest_order:
                # Create payment record
                db.execute(
                    """INSERT INTO payments (order_id, amount, payment_method, payment_date, notes, created_by)
                       VALUES (?, ?, ?, ?, ?, ?)""",
                    (oldest_order['order_id'], min(payment_amount, oldest_order['outstanding']), 
                     payment_method, payment_date, notes, current_user.username)
                )
                processed_count += 1
        
        db.commit()
        flash(f'Successfully processed {processed_count} bulk payments', 'success')
        
    except Exception as e:
        flash(f'Error processing bulk payment: {str(e)}', 'error')
    
    return redirect(url_for('advanced_payment.bulk_processing'))

@advanced_payment_bp.route('/api/payment_stats')
@login_required
def api_payment_stats():
    """API endpoint for payment statistics"""
    try:
        db = get_db()
        
        # Get payment statistics
        stats = db.execute(
            """SELECT 
                COUNT(*) as total_payments,
                SUM(amount) as total_amount,
                AVG(amount) as avg_amount
               FROM payments
               WHERE DATE(payment_date) >= DATE('now', '-30 days')"""
        ).fetchone()
        
        return jsonify({
            'success': True,
            'data': dict(stats)
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})
