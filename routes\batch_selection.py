#!/usr/bin/env python3
"""
FIXED Batch Selection Routes for DC Generation System
This is a completely rebuilt version that addresses all the HTTP 400 issues
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, send_file
from flask_login import login_required, current_user
from datetime import datetime, timedelta
import json
import sqlite3
import os
from flask import g, current_app

def get_db():
    """Get database connection - local implementation to avoid circular imports"""
    if 'db' not in g:
        g.db = sqlite3.connect(current_app.config.get('DATABASE', 'instance/medivent.db'))
        g.db.row_factory = sqlite3.Row
        g.db.execute("PRAGMA foreign_keys = ON")
    return g.db

batch_selection_bp = Blueprint('batch_selection', __name__)

def generate_dc_number():
    """Generate sequential DC number"""
    db = get_db()
    try:
        # Get the last DC number
        last_dc = db.execute(
            "SELECT dc_number FROM delivery_challans ORDER BY id DESC LIMIT 1"
        ).fetchone()
        
        if last_dc and last_dc['dc_number']:
            # Extract number from DC-001 format
            try:
                last_num = int(last_dc['dc_number'].split('-')[1])
                next_num = last_num + 1
            except (IndexError, ValueError):
                next_num = 1
        else:
            next_num = 1
        
        return f"DC-{next_num:03d}"
    except Exception:
        # Fallback to timestamp-based number
        return f"DC-{int(datetime.now().timestamp())}"

@batch_selection_bp.route('/orders/<order_id>/select-batch', methods=['GET', 'POST'])
@login_required
def select_batch(order_id):
    """Main batch selection interface - FIXED VERSION"""
    try:
        db = get_db()
        
        # Get order details with better error handling
        order = db.execute('''
            SELECT * FROM orders WHERE order_id = ?
        ''', (order_id,)).fetchone()
        
        if not order:
            flash(f'Order {order_id} not found', 'danger')
            return redirect(url_for('warehouse_packing_dashboard'))

        if order['status'] != 'Approved':
            flash(f'Order {order_id} is not approved for DC generation (Status: {order["status"]})', 'warning')
            return redirect(url_for('warehouse_packing_dashboard'))
        
        # Get order items with product details
        order_items = db.execute('''
            SELECT oi.*, p.name as product_name, p.strength, p.manufacturer, p.unit_of_measure
            FROM order_items oi
            LEFT JOIN products p ON oi.product_id = p.product_id
            WHERE oi.order_id = ?
            ORDER BY p.name
        ''', (order_id,)).fetchall()
        
        if not order_items:
            flash(f'No items found for order {order_id}', 'danger')
            return redirect(url_for('warehouse_packing_dashboard'))
        
        # Get available warehouses
        warehouses = db.execute('''
            SELECT * FROM warehouses WHERE status = 'active'
            ORDER BY name
        ''').fetchall()
        
        # Get existing batch selections for this order
        existing_selections = db.execute('''
            SELECT * FROM batch_selections 
            WHERE order_id = ? AND status = 'pending'
        ''', (order_id,)).fetchall()
        
        # Convert to dictionary for easy lookup
        selections_dict = {}
        for selection in existing_selections:
            key = f"{selection['product_id']}_{selection['batch_number']}"
            selections_dict[key] = selection
        
        if request.method == 'POST':
            action = request.form.get('action')
            
            if action == 'apply_method':
                return handle_apply_method(order_id, order_items)
            elif action == 'save_allocations':
                return handle_save_allocations(order_id, order_items)
            elif action == 'generate_dc':
                return handle_generate_dc(order_id, order)
        
        # Get inventory data for each product
        inventory_data = get_inventory_data(order_items, warehouses)

        return render_template('orders/select_batch.html',
                             order=order,
                             order_items=order_items,
                             warehouses=warehouses,
                             inventory_data=inventory_data,
                             existing_selections=selections_dict,
                             now=datetime.now())
        
    except Exception as e:
        flash(f'Error in batch selection: {str(e)}', 'danger')
        return redirect(url_for('warehouse_packing_dashboard'))

def handle_apply_method(order_id, order_items):
    """Handle FIFO allocation method - FIXED VERSION"""
    try:
        db = get_db()
        selection_method = request.form.get('selection_method', 'fifo')
        warehouse_filter = request.form.get('warehouse_filter')
        
        # Clear existing selections
        db.execute('DELETE FROM batch_selections WHERE order_id = ?', (order_id,))
        
        if selection_method == 'fifo':
            # Apply FIFO allocation
            for item in order_items:
                allocate_fifo(db, order_id, item, warehouse_filter)
        
        db.commit()
        flash(f'{selection_method.upper()} allocation applied successfully', 'success')
        
    except Exception as e:
        db.rollback()
        flash(f'Error applying allocation method: {str(e)}', 'danger')
    
    return redirect(url_for('dc_generation.batch_selection', order_id=order_id))

def allocate_fifo(db, order_id, item, warehouse_filter):
    """Allocate inventory using FIFO method - FIXED VERSION"""
    product_id = item['product_id']
    required_quantity = item['quantity']
    allocated_quantity = 0
    
    # Build warehouse filter condition
    warehouse_condition = ""
    params = [product_id]
    
    if warehouse_filter and warehouse_filter != 'all':
        warehouse_condition = "AND i.warehouse_id = ?"
        params.append(warehouse_filter)
    
    # Get available batches ordered by FIFO (oldest first)
    batches = db.execute(f'''
        SELECT i.*,
               (i.stock_quantity - COALESCE(i.allocated_quantity, 0)) as available_quantity
        FROM inventory i
        WHERE i.product_id = ? 
        {warehouse_condition}
        AND i.status = 'active'
        AND (i.stock_quantity - COALESCE(i.allocated_quantity, 0)) > 0
        AND (i.expiry_date IS NULL OR i.expiry_date > date('now'))
        ORDER BY i.manufacturing_date ASC, i.expiry_date ASC
    ''', params).fetchall()
    
    # Allocate from batches
    for batch in batches:
        if allocated_quantity >= required_quantity:
            break
        
        remaining_needed = required_quantity - allocated_quantity
        available = batch['available_quantity']
        to_allocate = min(remaining_needed, available)
        
        if to_allocate > 0:
            # Create batch selection record
            db.execute('''
                INSERT INTO batch_selections (
                    order_id, product_id, batch_number, warehouse_id,
                    allocated_quantity, selection_method, created_by, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                order_id, product_id, batch['batch_number'], batch['warehouse_id'],
                to_allocate, 'fifo', current_user.username, 'pending'
            ))
            
            allocated_quantity += to_allocate

def handle_save_allocations(order_id, order_items):
    """Handle manual allocation saving - FIXED VERSION"""
    try:
        db = get_db()
        
        # Clear existing selections
        db.execute('DELETE FROM batch_selections WHERE order_id = ?', (order_id,))
        
        # Process form data
        allocations_saved = 0
        for key, value in request.form.items():
            if key.startswith('allocation_'):
                # Parse allocation_productId_batchNumber_warehouseId
                parts = key.split('_')
                if len(parts) >= 4:
                    product_id = parts[1]
                    batch_number = parts[2]
                    warehouse_id = parts[3]
                    quantity = float(value) if value else 0
                    
                    if quantity > 0:
                        # Create batch selection record
                        db.execute('''
                            INSERT INTO batch_selections (
                                order_id, product_id, batch_number, warehouse_id,
                                allocated_quantity, selection_method, created_by, status
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            order_id, product_id, batch_number, warehouse_id,
                            quantity, 'manual', current_user.username, 'pending'
                        ))
                        allocations_saved += 1
        
        db.commit()
        
        if allocations_saved > 0:
            flash(f'Manual allocations saved successfully ({allocations_saved} allocations)', 'success')
        else:
            flash('No allocations were saved. Please enter quantities and try again.', 'warning')
        
    except Exception as e:
        db.rollback()
        flash(f'Error saving allocations: {str(e)}', 'danger')
    
    return redirect(url_for('dc_generation.batch_selection', order_id=order_id))

def handle_generate_dc(order_id, order):
    """Generate final delivery challan - COMPLETELY FIXED VERSION"""
    try:
        db = get_db()
        is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'

        # Start transaction
        db.execute('BEGIN')

        # Get batch selections with comprehensive validation
        batch_selections = db.execute('''
            SELECT * FROM batch_selections WHERE order_id = ? AND status = 'pending'
        ''', (order_id,)).fetchall()

        if not batch_selections:
            error_msg = 'No batch allocations found. Please allocate and save batches before generating Delivery Challan.'
            if is_ajax:
                return jsonify({
                    'success': False,
                    'error': 'no_allocations',
                    'message': error_msg,
                    'next_step': 'STEP 1: Click "Apply Method" (FIFO) or enter quantities. STEP 2: Click "Save Allocations". STEP 3: Click "Generate DC".'
                }), 400
            else:
                flash(error_msg, 'danger')
                return redirect(url_for('dc_generation.batch_selection', order_id=order_id))

        # Validate allocations comprehensively
        validation_result = validate_allocations_fixed(db, order_id)
        if not validation_result['valid']:
            if is_ajax:
                return jsonify({
                    'success': False,
                    'error': validation_result.get('error_type', 'validation_failed'),
                    'message': validation_result['message'],
                    'next_step': validation_result.get('next_step', 'Please check your allocations.')
                }), 400
            else:
                flash(validation_result['message'], 'danger')
                return redirect(url_for('dc_generation.batch_selection', order_id=order_id))

        # Generate DC number
        dc_number = generate_dc_number()

        # Prepare batch details
        batch_details = []
        for selection in batch_selections:
            batch_details.append({
                'product_id': selection['product_id'],
                'batch_number': selection['batch_number'],
                'warehouse_id': selection['warehouse_id'],
                'allocated_quantity': selection['allocated_quantity'],
                'selection_method': selection['selection_method']
            })

        # Insert delivery challan
        db.execute('''
            INSERT INTO delivery_challans (
                dc_number, order_id, warehouse_id, customer_name,
                status, created_by, total_items, total_amount, batch_details, created_date
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            dc_number, order_id, 'WH001', order['customer_name'],
            'created', current_user.username,
            validation_result['total_items'], order['order_amount'],
            json.dumps(batch_details), datetime.now()
        ))

        # Update batch selections status
        db.execute('''
            UPDATE batch_selections SET status = 'confirmed' WHERE order_id = ?
        ''', (order_id,))

        # Update inventory allocated quantities
        for selection in batch_selections:
            db.execute('''
                UPDATE inventory SET allocated_quantity = COALESCE(allocated_quantity, 0) + ?
                WHERE product_id = ? AND batch_number = ? AND warehouse_id = ?
            ''', (
                selection['allocated_quantity'], selection['product_id'],
                selection['batch_number'], selection['warehouse_id']
            ))

        # Update order status to Finance Pending after DC generation
        db.execute('''
            UPDATE orders SET status = 'Finance Pending', last_updated = ?, updated_by = ? WHERE order_id = ?
        ''', (datetime.now(), current_user.username, order_id))

        # Commit transaction
        db.commit()

        # Generate PDF after successful DC creation
        try:
            from utils.pdf_generator import generate_dc_pdf
            pdf_path = generate_dc_pdf(dc_number)
            print(f"PDF generated successfully: {pdf_path}")
        except Exception as pdf_error:
            print(f"Warning: PDF generation failed: {pdf_error}")
            # Don't fail the entire DC creation if PDF fails

        success_message = f'Delivery Challan {dc_number} generated successfully!'

        if is_ajax:
            return jsonify({
                'success': True,
                'dc_number': dc_number,
                'message': success_message,
                'dc_url': url_for('dc_generation.view_dc', dc_number=dc_number),
                'pdf_url': url_for('dc_generation.download_dc_pdf', dc_number=dc_number),
                'redirect_url': url_for('warehouse_packing_dashboard')
            })
        else:
            flash(success_message, 'success')
            return redirect(url_for('warehouse_packing_dashboard'))

    except Exception as e:
        db.rollback()
        error_message = f'Error generating DC: {str(e)}'

        if is_ajax:
            return jsonify({
                'success': False,
                'error': 'generation_failed',
                'message': error_message,
                'details': str(e)
            }), 500
        else:
            flash(error_message, 'danger')
            return redirect(url_for('dc_generation.batch_selection', order_id=order_id))

def validate_allocations_fixed(db, order_id):
    """Validate that all products are fully allocated - FIXED VERSION"""
    try:
        # Get required quantities
        order_items = db.execute('''
            SELECT product_id, quantity FROM order_items WHERE order_id = ?
        ''', (order_id,)).fetchall()

        if not order_items:
            return {
                'valid': False,
                'message': 'No order items found for this order',
                'error_type': 'no_items'
            }

        # Get allocated quantities
        allocated = db.execute('''
            SELECT product_id, SUM(allocated_quantity) as total_allocated
            FROM batch_selections
            WHERE order_id = ? AND status = 'pending'
            GROUP BY product_id
        ''', (order_id,)).fetchall()

        allocated_dict = {item['product_id']: item['total_allocated'] for item in allocated}

        # Check if any allocations exist
        if not allocated_dict:
            return {
                'valid': False,
                'message': 'No batch allocations found. Please allocate batches to all products before generating Delivery Challan.',
                'error_type': 'no_allocations',
                'next_step': 'STEP 1: Click "Apply Method" (FIFO) or enter quantities. STEP 2: Click "Save Allocations". STEP 3: Click "Generate DC".'
            }

        unallocated_products = []
        under_allocated_products = []
        total_items = 0

        for item in order_items:
            product_id = item['product_id']
            required = item['quantity']
            allocated_qty = allocated_dict.get(product_id, 0)

            if allocated_qty == 0:
                unallocated_products.append(f'{product_id} (needs {required})')
            elif allocated_qty < required:
                under_allocated_products.append(f'{product_id} (needs {required}, allocated {allocated_qty})')

            total_items += 1

        if unallocated_products or under_allocated_products:
            error_parts = []
            if unallocated_products:
                error_parts.append(f"Unallocated products: {', '.join(unallocated_products)}")
            if under_allocated_products:
                error_parts.append(f"Under-allocated products: {', '.join(under_allocated_products)}")

            return {
                'valid': False,
                'message': f'Batch allocation incomplete. {" | ".join(error_parts)}',
                'error_type': 'incomplete_allocations',
                'next_step': 'Complete batch allocation for all products before generating Delivery Challan.'
            }

        return {
            'valid': True,
            'total_items': total_items,
            'message': 'All products are fully allocated'
        }

    except Exception as e:
        return {
            'valid': False,
            'message': f'Validation error: {str(e)}',
            'error_type': 'validation_error'
        }

def get_inventory_data(order_items, warehouses):
    """Get inventory data for display - FIXED VERSION"""
    db = get_db()
    inventory_data = {}

    for item in order_items:
        product_id = item['product_id']
        inventory_data[product_id] = {}

        for warehouse in warehouses:
            warehouse_id = warehouse['warehouse_id']

            # Get inventory for this product in this warehouse
            inventory = db.execute('''
                SELECT i.*,
                       (i.stock_quantity - COALESCE(i.allocated_quantity, 0)) as available_quantity
                FROM inventory i
                WHERE i.product_id = ? AND i.warehouse_id = ? AND i.status = 'active'
                AND (i.stock_quantity - COALESCE(i.allocated_quantity, 0)) > 0
                ORDER BY i.manufacturing_date ASC, i.expiry_date ASC
            ''', (product_id, warehouse_id)).fetchall()

            # Format the data structure expected by the template
            inventory_data[product_id][warehouse_id] = {
                'warehouse_name': warehouse['name'],
                'batches': []
            }

            # Process each batch with proper formatting
            for batch in inventory:
                batch_data = dict(batch)

                # Format dates
                try:
                    from datetime import datetime
                    if batch_data.get('manufacturing_date'):
                        mfg_date = datetime.strptime(str(batch_data['manufacturing_date']), '%Y-%m-%d')
                        batch_data['manufacturing_date_formatted'] = mfg_date.strftime('%d-%m-%Y')
                    else:
                        batch_data['manufacturing_date_formatted'] = 'N/A'

                    if batch_data.get('expiry_date'):
                        exp_date = datetime.strptime(str(batch_data['expiry_date']), '%Y-%m-%d')
                        batch_data['expiry_date_formatted'] = exp_date.strftime('%d-%m-%Y')

                        # Check if expiring soon (within 30 days)
                        days_to_expiry = (exp_date - datetime.now()).days
                        if days_to_expiry <= 30:
                            batch_data['expiry_date_class'] = 'text-danger'
                        elif days_to_expiry <= 90:
                            batch_data['expiry_date_class'] = 'text-warning'
                        else:
                            batch_data['expiry_date_class'] = 'text-success'
                    else:
                        batch_data['expiry_date_formatted'] = None
                        batch_data['expiry_date_class'] = 'text-muted'
                except:
                    batch_data['manufacturing_date_formatted'] = 'N/A'
                    batch_data['expiry_date_formatted'] = 'N/A'
                    batch_data['expiry_date_class'] = 'text-muted'

                inventory_data[product_id][warehouse_id]['batches'].append(batch_data)

    return inventory_data

# Debug route to add sample inventory
@batch_selection_bp.route('/add-sample-inventory')
@login_required
def add_sample_inventory():
    """Add sample inventory for testing"""
    try:
        db = get_db()

        # Check if inventory already exists
        existing = db.execute("SELECT COUNT(*) FROM inventory WHERE product_id IN ('P001', 'P002')").fetchone()[0]

        if existing > 0:
            flash(f'Inventory already exists ({existing} records)', 'info')
            return redirect(url_for('dc_generation.batch_selection', order_id='ORD175376448075480EC'))

        # Add sample inventory
        today = datetime.now()
        mfg_date = (today - timedelta(days=30)).strftime('%Y-%m-%d')
        exp_date = (today + timedelta(days=365)).strftime('%Y-%m-%d')

        inventory_records = [
            ('INV001', 'P001', 'BATCH001', mfg_date, exp_date, 100, 0, 'WH001', 'A1-01', 'active'),
            ('INV002', 'P001', 'BATCH002', mfg_date, exp_date, 75, 0, 'WH001', 'A1-02', 'active'),
            ('INV003', 'P001', 'BATCH003', mfg_date, exp_date, 50, 0, 'WH002', 'B1-01', 'active'),
            ('INV004', 'P002', 'BATCH004', mfg_date, exp_date, 80, 0, 'WH001', 'A2-01', 'active'),
            ('INV005', 'P002', 'BATCH005', mfg_date, exp_date, 60, 0, 'WH002', 'B2-01', 'active'),
        ]

        for inv in inventory_records:
            db.execute("""
                INSERT INTO inventory
                (inventory_id, product_id, batch_number, manufacturing_date, expiry_date,
                 stock_quantity, allocated_quantity, warehouse_id, location_code, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, inv)

        db.commit()
        flash(f'Successfully added {len(inventory_records)} inventory records!', 'success')

    except Exception as e:
        flash(f'Error adding inventory: {str(e)}', 'error')

    return redirect(url_for('dc_generation.batch_selection', order_id='ORD175376448075480EC'))

# PDF serving route - REMOVED: Now handled by dc_generation blueprint
# Use dc_generation.download_dc_pdf instead
