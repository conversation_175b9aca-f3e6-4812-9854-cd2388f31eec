"""
Delivery Analytics Routes
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from datetime import datetime, timedelta
import sqlite3

delivery_analytics_bp = Blueprint('delivery_analytics', __name__, url_prefix='/delivery_analytics')

def get_db():
    """Get database connection"""
    conn = sqlite3.connect('instance/medivent.db')
    conn.row_factory = sqlite3.Row
    return conn

@delivery_analytics_bp.route('/')
@login_required
def dashboard():
    """Delivery Analytics Dashboard"""
    try:
        db = get_db()
        
        # Get basic metrics
        today = datetime.now().date()
        
        # Count today's deliveries
        today_deliveries = db.execute(
            "SELECT COUNT(*) as count FROM orders WHERE DATE(order_date) = ? AND status = 'Delivered'",
            (today,)
        ).fetchone()['count']
        
        # Count pending deliveries
        pending_deliveries = db.execute(
            "SELECT COUNT(*) as count FROM orders WHERE status IN ('Processing', 'Shipped', 'Out for Delivery')"
        ).fetchone()['count']
        
        # Get top riders
        top_riders = db.execute(
            """SELECT r.name, COUNT(o.order_id) as deliveries 
               FROM riders r 
               LEFT JOIN orders o ON r.rider_id = o.rider_id 
               GROUP BY r.rider_id 
               ORDER BY deliveries DESC 
               LIMIT 5"""
        ).fetchall()
        
        # Recent activities
        recent_activities = db.execute(
            """SELECT order_id, customer_name, status, order_date 
               FROM orders 
               ORDER BY order_date DESC 
               LIMIT 10"""
        ).fetchall()
        
        context = {
            'title': 'Delivery Analytics Dashboard',
            'today_deliveries': today_deliveries,
            'pending_deliveries': pending_deliveries,
            'top_riders': top_riders,
            'recent_activities': recent_activities,
            'current_user': current_user,
            'now': datetime.now()
        }
        
        return render_template('delivery_analytics/dashboard.html', **context)
        
    except Exception as e:
        flash(f'Error loading delivery analytics: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@delivery_analytics_bp.route('/real_time_tracking')
@login_required
def real_time_tracking():
    """Real-time Delivery Tracking"""
    try:
        db = get_db()
        
        # Get active deliveries
        active_deliveries = db.execute(
            """SELECT o.order_id, o.customer_name, o.delivery_address, o.status,
                      r.name as rider_name 
               FROM orders o
               LEFT JOIN riders r ON o.rider_id = r.rider_id
               WHERE o.status IN ('Processing', 'Shipped', 'Out for Delivery')
               ORDER BY o.order_date DESC"""
        ).fetchall()
        
        context = {
            'title': 'Real-time Delivery Tracking',
            'active_deliveries': active_deliveries,
            'current_user': current_user,
            'now': datetime.now()
        }
        
        return render_template('delivery_analytics/real_time_tracking.html', **context)
        
    except Exception as e:
        flash(f'Error loading real-time tracking: {str(e)}', 'error')
        return redirect(url_for('delivery_analytics.dashboard'))

@delivery_analytics_bp.route('/performance_kpis')
@login_required
def performance_kpis():
    """Delivery Performance KPIs"""
    try:
        db = get_db()
        
        # Calculate KPIs
        total_orders = db.execute("SELECT COUNT(*) as count FROM orders").fetchone()['count']
        delivered_orders = db.execute("SELECT COUNT(*) as count FROM orders WHERE status = 'Delivered'").fetchone()['count']
        
        success_rate = (delivered_orders / total_orders * 100) if total_orders > 0 else 0
        
        # Rider performance
        rider_performance = db.execute(
            """SELECT r.name, COUNT(o.order_id) as total_deliveries,
                      AVG(r.rating) as avg_rating
               FROM riders r
               LEFT JOIN orders o ON r.rider_id = o.rider_id
               GROUP BY r.rider_id
               ORDER BY total_deliveries DESC"""
        ).fetchall()
        
        context = {
            'title': 'Delivery Performance KPIs',
            'success_rate': round(success_rate, 1),
            'total_orders': total_orders,
            'delivered_orders': delivered_orders,
            'rider_performance': rider_performance,
            'current_user': current_user,
            'now': datetime.now()
        }
        
        return render_template('delivery_analytics/performance_kpis.html', **context)
        
    except Exception as e:
        flash(f'Error loading performance KPIs: {str(e)}', 'error')
        return redirect(url_for('delivery_analytics.dashboard'))

@delivery_analytics_bp.route('/comprehensive_reports')
@login_required
def comprehensive_reports():
    """Comprehensive Delivery Reports"""
    try:
        # Available report types
        report_types = [
            {'id': 'daily_summary', 'name': 'Daily Delivery Summary', 'icon': 'calendar-day'},
            {'id': 'weekly_performance', 'name': 'Weekly Performance', 'icon': 'calendar-week'},
            {'id': 'monthly_analytics', 'name': 'Monthly Analytics', 'icon': 'calendar-alt'},
            {'id': 'rider_performance', 'name': 'Rider Performance', 'icon': 'user-tie'},
            {'id': 'area_analysis', 'name': 'Area Analysis', 'icon': 'map-marked-alt'},
            {'id': 'cost_analysis', 'name': 'Cost Analysis', 'icon': 'dollar-sign'},
            {'id': 'customer_satisfaction', 'name': 'Customer Satisfaction', 'icon': 'smile'},
            {'id': 'route_efficiency', 'name': 'Route Efficiency', 'icon': 'route'},
            {'id': 'time_analysis', 'name': 'Time Analysis', 'icon': 'clock'},
            {'id': 'trend_analysis', 'name': 'Trend Analysis', 'icon': 'chart-line'}
        ]
        
        context = {
            'title': 'Comprehensive Delivery Reports',
            'report_types': report_types,
            'current_user': current_user,
            'now': datetime.now()
        }
        
        return render_template('delivery_analytics/comprehensive_reports.html', **context)
        
    except Exception as e:
        flash(f'Error loading comprehensive reports: {str(e)}', 'error')
        return redirect(url_for('delivery_analytics.dashboard'))

@delivery_analytics_bp.route('/api/delivery_stats')
@login_required
def api_delivery_stats():
    """API endpoint for delivery statistics"""
    try:
        db = get_db()
        
        # Get basic stats
        stats = db.execute(
            """SELECT 
                COUNT(*) as total_orders,
                COUNT(CASE WHEN status = 'Delivered' THEN 1 END) as delivered_orders,
                COUNT(CASE WHEN status IN ('Processing', 'Shipped', 'Out for Delivery') THEN 1 END) as pending_orders
               FROM orders"""
        ).fetchone()
        
        return jsonify({
            'success': True,
            'data': dict(stats)
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})
