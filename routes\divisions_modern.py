"""
Modern Division Management Blueprint
Complete rebuild with RESTful API design and comprehensive error handling
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for, session
from datetime import datetime, timedelta
import sqlite3
import json
import uuid
import logging
from utils.db import get_db
from utils.unified_division_manager import UnifiedDivisionManager, get_unified_division_manager

# Create blueprint
divisions_bp = Blueprint('divisions', __name__, url_prefix='/divisions')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Division status constants
DIVISION_STATUSES = ['active', 'inactive', 'suspended', 'archived']
DIVISION_CATEGORIES = ['Sales', 'Marketing', 'Operations', 'Finance', 'Management', 'Support', 'Revenue', 'Pharmaceuticals', 'Research', 'Development']

def log_division_action(division_id, action_type, old_values=None, new_values=None, user_id=None):
    """Log division actions for audit trail"""
    try:
        db = get_db()
        db.execute('''
            INSERT INTO division_audit_log (
                division_id, action_type, old_values, new_values, 
                changed_by, ip_address, user_agent
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            division_id, action_type, 
            json.dumps(old_values) if old_values else None,
            json.dumps(new_values) if new_values else None,
            user_id or session.get('user_id', 'anonymous'),
            request.remote_addr,
            request.headers.get('User-Agent', '')
        ))
        db.commit()
    except Exception as e:
        logger.error(f"Failed to log division action: {e}")

def validate_division_data(data, is_update=False):
    """Validate division data with comprehensive checks"""
    errors = []
    
    # Required fields for new divisions
    if not is_update:
        required_fields = ['code', 'name']
        for field in required_fields:
            if not data.get(field, '').strip():
                errors.append(f"{field.title()} is required")
    
    # Code validation
    if 'code' in data and data['code']:
        code = data['code'].strip().upper()
        if len(code) < 2 or len(code) > 10:
            errors.append("Code must be between 2-10 characters")
        if not code.replace('_', '').replace('-', '').isalnum():
            errors.append("Code can only contain letters, numbers, hyphens, and underscores")
    
    # Name validation
    if 'name' in data and data['name']:
        name = data['name'].strip()
        if len(name) < 2 or len(name) > 100:
            errors.append("Name must be between 2-100 characters")
    
    # Budget validation with enhanced error handling
    if 'budget' in data:
        budget_value = data['budget']
        if budget_value is None or budget_value == '' or budget_value == 'None':
            # Set default budget to 0 if empty
            data['budget'] = '0'
        else:
            try:
                budget = float(str(budget_value).strip())
                if budget < 0:
                    errors.append("Budget cannot be negative")
                data['budget'] = str(budget)  # Ensure it's a string for consistency
            except (ValueError, TypeError):
                errors.append("Budget must be a valid number")
                data['budget'] = '0'  # Set default on error
    
    # Status validation
    if 'status' in data and data['status']:
        if data['status'] not in DIVISION_STATUSES:
            errors.append(f"Status must be one of: {', '.join(DIVISION_STATUSES)}")
    
    # Category validation
    if 'category' in data and data['category']:
        if data['category'] not in DIVISION_CATEGORIES:
            errors.append(f"Category must be one of: {', '.join(DIVISION_CATEGORIES)}")
    
    return errors

@divisions_bp.route('/')
def index():
    """Modern division management dashboard"""
    try:
        db = get_db()
        
        # Get all divisions with analytics
        divisions = db.execute('''
            SELECT d.*, 
                   COUNT(da.analytics_id) as metrics_count,
                   COALESCE(SUM(CASE WHEN da.metric_name = 'monthly_revenue' THEN da.metric_value END), 0) as revenue,
                   COALESCE(SUM(CASE WHEN da.metric_name = 'monthly_orders' THEN da.metric_value END), 0) as orders
            FROM divisions d
            LEFT JOIN division_analytics da ON d.division_id = da.division_id
            WHERE d.is_active = 1
            GROUP BY d.division_id
            ORDER BY d.sort_order, d.name
        ''').fetchall()
        
        # Get summary statistics
        stats = {
            'total_divisions': len(divisions),
            'active_divisions': len([d for d in divisions if d['status'] == 'active']),
            'total_budget': sum(float(d['budget'] or '0') for d in divisions if d['budget'] not in [None, '', 'None']),
            'total_revenue': sum(float(d['revenue'] or '0') for d in divisions if d['revenue'] not in [None, '', 'None'])
        }
        
        # Get recent activities
        recent_activities = db.execute('''
            SELECT dal.*, d.name as division_name
            FROM division_audit_log dal
            JOIN divisions d ON dal.division_id = d.division_id
            ORDER BY dal.changed_at DESC
            LIMIT 10
        ''').fetchall()
        
        return render_template('divisions/modern_index.html', 
                             divisions=divisions, 
                             stats=stats,
                             recent_activities=recent_activities,
                             statuses=DIVISION_STATUSES,
                             categories=DIVISION_CATEGORIES)
        
    except Exception as e:
        logger.error(f"Error loading divisions dashboard: {e}")
        flash(f'Error loading divisions: {str(e)}', 'danger')
        return render_template('divisions/modern_index.html', 
                             divisions=[], 
                             stats={},
                             recent_activities=[],
                             statuses=DIVISION_STATUSES,
                             categories=DIVISION_CATEGORIES)

@divisions_bp.route('/api/list')
def api_list():
    """RESTful API endpoint for division list with filtering and pagination"""
    try:
        db = get_db()
        unified_manager = get_unified_division_manager(db)

        # Get query parameters
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))
        search = request.args.get('search', '').strip()
        status_filter = request.args.get('status', '')
        category_filter = request.args.get('category', '')
        sort_by = request.args.get('sort_by', 'name')
        sort_order = request.args.get('sort_order', 'asc')

        # Get all active divisions using unified manager
        all_divisions = unified_manager.get_active_divisions(include_metadata=True)

        # Apply filters
        filtered_divisions = all_divisions

        # Apply search filter
        if search:
            search_lower = search.lower()
            filtered_divisions = [
                div for div in filtered_divisions
                if (search_lower in div.get('name', '').lower() or
                    search_lower in div.get('code', '').lower() or
                    search_lower in div.get('description', '').lower())
            ]

        # Apply status filter
        if status_filter:
            filtered_divisions = [
                div for div in filtered_divisions
                if div.get('status', '').lower() == status_filter.lower()
            ]

        # Apply category filter
        if category_filter:
            filtered_divisions = [
                div for div in filtered_divisions
                if div.get('category', '').lower() == category_filter.lower()
            ]

        # Validate sort column
        valid_sort_columns = ['name', 'code', 'status', 'category', 'budget', 'created_at']
        if sort_by not in valid_sort_columns:
            sort_by = 'name'

        if sort_order.lower() not in ['asc', 'desc']:
            sort_order = 'asc'

        # Sort divisions
        reverse_sort = sort_order.lower() == 'desc'
        filtered_divisions.sort(
            key=lambda x: x.get(sort_by, ''),
            reverse=reverse_sort
        )

        # Get total count
        total_count = len(filtered_divisions)

        # Apply pagination
        offset = (page - 1) * per_page
        paginated_divisions = filtered_divisions[offset:offset + per_page]

        # Format divisions for response
        divisions_list = []
        for division in paginated_divisions:
            div_dict = division.copy()

            # Format dates and numbers
            if div_dict.get('created_at'):
                try:
                    div_dict['created_at_formatted'] = datetime.fromisoformat(str(div_dict['created_at'])).strftime('%Y-%m-%d %H:%M')
                except:
                    div_dict['created_at_formatted'] = str(div_dict['created_at'])

            # Safe float conversion with error handling
            try:
                budget_val = float(div_dict.get('budget', 0) or 0)
                div_dict['budget_formatted'] = f"Rs. {budget_val:,.2f}"
            except (ValueError, TypeError):
                div_dict['budget_formatted'] = "Rs. 0.00"

            # Add revenue formatting (default to 0 since unified manager doesn't include analytics)
            div_dict['revenue'] = 0.0
            div_dict['revenue_formatted'] = "Rs. 0.00"
            div_dict['metrics_count'] = 0

            divisions_list.append(div_dict)
        
        return jsonify({
            'success': True,
            'data': divisions_list,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total_count,
                'pages': (total_count + per_page - 1) // per_page
            },
            'filters': {
                'search': search,
                'status': status_filter,
                'category': category_filter,
                'sort_by': sort_by,
                'sort_order': sort_order
            }
        })
        
    except Exception as e:
        logger.error(f"Error in divisions API list: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'data': [],
            'pagination': {'page': 1, 'per_page': 10, 'total': 0, 'pages': 0}
        }), 500

@divisions_bp.route('/create', methods=['GET', 'POST'])
def create():
    """Create new division with comprehensive validation"""
    if request.method == 'GET':
        return render_template('divisions/create.html', 
                             statuses=DIVISION_STATUSES,
                             categories=DIVISION_CATEGORIES)
    
    try:
        data = request.get_json() if request.is_json else request.form.to_dict()
        
        # Validate input data
        errors = validate_division_data(data)
        if errors:
            if request.is_json:
                return jsonify({'success': False, 'errors': errors}), 400
            else:
                for error in errors:
                    flash(error, 'danger')
                return redirect(url_for('divisions.create'))
        
        db = get_db()
        
        # Check for duplicate code
        existing = db.execute('SELECT division_id FROM divisions WHERE code = ? AND is_active = 1', 
                            (data['code'].strip().upper(),)).fetchone()
        if existing:
            error_msg = f"Division code '{data['code']}' already exists"
            if request.is_json:
                return jsonify({'success': False, 'errors': [error_msg]}), 400
            else:
                flash(error_msg, 'danger')
                return redirect(url_for('divisions.create'))
        
        # Generate new division ID
        division_id = f"DIV{str(uuid.uuid4())[:8].upper()}"
        
        # Prepare data for insertion
        insert_data = {
            'division_id': division_id,
            'code': data['code'].strip().upper(),
            'name': data['name'].strip(),
            'description': data.get('description', '').strip(),
            'status': data.get('status', 'active'),
            'category': data.get('category', ''),
            'budget': float(data.get('budget', '0') or '0'),
            'contact_email': data.get('contact_email', '').strip(),
            'contact_phone': data.get('contact_phone', '').strip(),
            'location': data.get('location', '').strip(),
            'address': data.get('address', '').strip(),
            'city': data.get('city', '').strip(),
            'created_by': session.get('user_id', 'system'),
            'updated_by': session.get('user_id', 'system')
        }
        
        # Insert division
        db.execute('''
            INSERT INTO divisions (
                division_id, code, name, description, status, category, budget,
                contact_email, contact_phone, location, address, city,
                created_by, updated_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            insert_data['division_id'], insert_data['code'], insert_data['name'],
            insert_data['description'], insert_data['status'], insert_data['category'],
            insert_data['budget'], insert_data['contact_email'], insert_data['contact_phone'],
            insert_data['location'], insert_data['address'], insert_data['city'],
            insert_data['created_by'], insert_data['updated_by']
        ))
        
        db.commit()

        # Invalidate division caches for real-time updates
        try:
            from utils.division_realtime_service import invalidate_division_caches
            invalidate_division_caches(db)
        except Exception as e:
            logger.warning(f"Failed to invalidate division caches: {e}")

        # Log the action
        log_division_action(division_id, 'CREATE', None, insert_data)

        success_msg = f"Division '{insert_data['name']}' created successfully"
        
        if request.is_json:
            return jsonify({
                'success': True, 
                'message': success_msg,
                'division_id': division_id,
                'data': insert_data
            })
        else:
            flash(success_msg, 'success')
            return redirect(url_for('divisions.index'))
        
    except Exception as e:
        logger.error(f"Error creating division: {e}")
        error_msg = f"Error creating division: {str(e)}"
        
        if request.is_json:
            return jsonify({'success': False, 'error': error_msg}), 500
        else:
            flash(error_msg, 'danger')
            return redirect(url_for('divisions.create'))

@divisions_bp.route('/<division_id>')
def view(division_id):
    """View division details with analytics"""
    try:
        db = get_db()

        # Get division details
        division = db.execute('''
            SELECT * FROM divisions
            WHERE division_id = ? AND is_active = 1
        ''', (division_id,)).fetchone()

        if not division:
            flash('Division not found', 'danger')
            return redirect(url_for('divisions.index'))

        # Get analytics data
        analytics = db.execute('''
            SELECT * FROM division_analytics
            WHERE division_id = ?
            ORDER BY metric_date DESC, metric_name
        ''', (division_id,)).fetchall()

        # Get recent audit log
        audit_log = db.execute('''
            SELECT * FROM division_audit_log
            WHERE division_id = ?
            ORDER BY changed_at DESC
            LIMIT 20
        ''', (division_id,)).fetchall()

        # Get permissions with enhanced user data
        permissions = db.execute('''
            SELECT dp.*, u.username, u.email, u.role,
                   granter.username as granted_by_username
            FROM division_permissions dp
            LEFT JOIN users u ON dp.user_id = u.id
            LEFT JOIN users granter ON dp.granted_by = granter.id
            WHERE dp.division_id = ? AND dp.is_active = 1
            ORDER BY dp.permission_type, u.username
        ''', (division_id,)).fetchall()

        return render_template('divisions/view.html',
                             division=division,
                             analytics=analytics,
                             audit_log=audit_log,
                             permissions=permissions)

    except Exception as e:
        logger.error(f"Error viewing division {division_id}: {e}")
        flash(f'Error loading division: {str(e)}', 'danger')
        return redirect(url_for('divisions.index'))

@divisions_bp.route('/<division_id>/edit', methods=['GET', 'POST'])
def edit(division_id):
    """Edit division with validation and audit trail"""
    try:
        db = get_db()

        # Get current division data
        division = db.execute('''
            SELECT * FROM divisions
            WHERE division_id = ? AND is_active = 1
        ''', (division_id,)).fetchone()

        if not division:
            if request.is_json:
                return jsonify({'success': False, 'error': 'Division not found'}), 404
            flash('Division not found', 'danger')
            return redirect(url_for('divisions.index'))

        if request.method == 'GET':
            return render_template('divisions/edit.html',
                                 division=division,
                                 statuses=DIVISION_STATUSES,
                                 categories=DIVISION_CATEGORIES)

        # Handle POST request
        data = request.get_json() if request.is_json else request.form.to_dict()

        # Validate input data
        errors = validate_division_data(data, is_update=True)
        if errors:
            if request.is_json:
                return jsonify({'success': False, 'errors': errors}), 400
            else:
                for error in errors:
                    flash(error, 'danger')
                return redirect(url_for('divisions.edit', division_id=division_id))

        # Check for duplicate code (excluding current division)
        if 'code' in data and data['code']:
            existing = db.execute('''
                SELECT division_id FROM divisions
                WHERE code = ? AND division_id != ? AND is_active = 1
            ''', (data['code'].strip().upper(), division_id)).fetchone()

            if existing:
                error_msg = f"Division code '{data['code']}' already exists"
                if request.is_json:
                    return jsonify({'success': False, 'errors': [error_msg]}), 400
                else:
                    flash(error_msg, 'danger')
                    return redirect(url_for('divisions.edit', division_id=division_id))

        # Store old values for audit
        old_values = dict(division)

        # Prepare update data
        update_fields = []
        update_values = []

        updatable_fields = [
            'code', 'name', 'description', 'status', 'category', 'budget',
            'contact_email', 'contact_phone', 'location', 'address', 'city'
        ]

        for field in updatable_fields:
            if field in data:
                if field == 'code':
                    value = data[field].strip().upper()
                elif field == 'budget':
                    value = float(data[field]) if data[field] else 0
                else:
                    value = data[field].strip() if isinstance(data[field], str) else data[field]

                update_fields.append(f"{field} = ?")
                update_values.append(value)

        if update_fields:
            update_fields.append("updated_at = CURRENT_TIMESTAMP")
            update_fields.append("updated_by = ?")
            update_values.append(session.get('user_id', 'system'))
            update_values.append(division_id)

            # Execute update
            update_query = f'''
                UPDATE divisions
                SET {', '.join(update_fields)}
                WHERE division_id = ?
            '''

            db.execute(update_query, update_values)
            db.commit()

            # Invalidate division caches for real-time updates
            try:
                from utils.division_realtime_service import invalidate_division_caches
                invalidate_division_caches(db)
            except Exception as e:
                logger.warning(f"Failed to invalidate division caches: {e}")

            # Log the action
            new_values = {field.split(' = ')[0]: value for field, value in zip(update_fields[:-2], update_values[:-1])}
            log_division_action(division_id, 'UPDATE', old_values, new_values)

            success_msg = f"Division '{data.get('name', division['name'])}' updated successfully"

            if request.is_json:
                return jsonify({'success': True, 'message': success_msg})
            else:
                flash(success_msg, 'success')
                return redirect(url_for('divisions.view', division_id=division_id))
        else:
            if request.is_json:
                return jsonify({'success': False, 'error': 'No fields to update'})
            else:
                flash('No changes made', 'info')
                return redirect(url_for('divisions.view', division_id=division_id))

    except Exception as e:
        logger.error(f"Error updating division {division_id}: {e}")
        error_msg = f"Error updating division: {str(e)}"

        if request.is_json:
            return jsonify({'success': False, 'error': error_msg}), 500
        else:
            flash(error_msg, 'danger')
            return redirect(url_for('divisions.edit', division_id=division_id))

@divisions_bp.route('/<division_id>/delete', methods=['POST'])
def delete(division_id):
    """Soft delete division with audit trail using unified manager"""
    try:
        db = get_db()
        unified_manager = get_unified_division_manager(db)

        # Validate division exists and is active
        is_valid, division_info = unified_manager.validate_division_exists(division_id)

        if not is_valid:
            if request.is_json:
                return jsonify({'success': False, 'error': 'Division not found'}), 404
            flash('Division not found', 'danger')
            return redirect(url_for('divisions.index'))

        # Check if division has dependencies (orders, products, etc.)
        # This would be expanded based on your specific business logic

        # Use unified manager to sync division status across all columns
        success = unified_manager.sync_division_status(division_id, 'inactive')

        if not success:
            if request.is_json:
                return jsonify({'success': False, 'error': 'Failed to delete division'}), 500
            flash('Failed to delete division', 'danger')
            return redirect(url_for('divisions.index'))

        # Invalidate division caches for real-time updates
        try:
            from utils.division_realtime_service import invalidate_division_caches
            invalidate_division_caches(db)
        except Exception as e:
            logger.warning(f"Failed to invalidate division caches: {e}")

        # Log the action
        try:
            log_division_action(division_id, 'DELETE', division_info, {'status': 'Inactive'})
        except:
            pass  # Don't fail deletion if logging fails

        success_msg = f"Division '{division_info['name']}' deleted successfully"

        if request.is_json:
            return jsonify({'success': True, 'message': success_msg})
        else:
            flash(success_msg, 'success')
            return redirect(url_for('divisions.index'))

    except Exception as e:
        logger.error(f"Error deleting division {division_id}: {e}")
        error_msg = f"Error deleting division: {str(e)}"

        if request.is_json:
            return jsonify({'success': False, 'error': error_msg}), 500
        else:
            flash(error_msg, 'danger')
            return redirect(url_for('divisions.index'))

@divisions_bp.route('/analytics')
def analytics():
    """Division analytics dashboard"""
    try:
        db = get_db()

        # Get analytics data
        analytics_data = db.execute('''
            SELECT d.name, d.code, da.metric_name, da.metric_value, da.metric_date
            FROM divisions d
            JOIN division_analytics da ON d.division_id = da.division_id
            WHERE d.is_active = 1
            ORDER BY da.metric_date DESC, d.name
        ''').fetchall()

        # Get summary statistics
        summary_stats = db.execute('''
            SELECT
                COUNT(DISTINCT d.division_id) as total_divisions,
                SUM(d.budget) as total_budget,
                AVG(d.budget) as avg_budget,
                COUNT(DISTINCT CASE WHEN d.status = 'active' THEN d.division_id END) as active_divisions
            FROM divisions d
            WHERE d.is_active = 1
        ''').fetchone()

        return render_template('divisions/analytics.html',
                             analytics_data=analytics_data,
                             summary_stats=summary_stats)

    except Exception as e:
        logger.error(f"Error loading division analytics: {e}")
        flash(f'Error loading analytics: {str(e)}', 'danger')
        return render_template('divisions/analytics.html',
                             analytics_data=[],
                             summary_stats={})

@divisions_bp.route('/export')
def export():
    """Export divisions data to CSV/Excel"""
    try:
        db = get_db()
        format_type = request.args.get('format', 'csv').lower()

        # Get divisions data
        divisions = db.execute('''
            SELECT division_id, code, name, description, status, category,
                   budget, contact_email, contact_phone, location,
                   created_at, updated_at
            FROM divisions
            WHERE is_active = 1
            ORDER BY name
        ''').fetchall()

        if format_type == 'csv':
            import csv
            import io

            output = io.StringIO()
            writer = csv.writer(output)

            # Write header
            writer.writerow([
                'Division ID', 'Code', 'Name', 'Description', 'Status',
                'Category', 'Budget', 'Email', 'Phone', 'Location',
                'Created At', 'Updated At'
            ])

            # Write data
            for division in divisions:
                writer.writerow([
                    division['division_id'], division['code'], division['name'],
                    division['description'], division['status'], division['category'],
                    division['budget'], division['contact_email'], division['contact_phone'],
                    division['location'], division['created_at'], division['updated_at']
                ])

            output.seek(0)

            from flask import Response
            return Response(
                output.getvalue(),
                mimetype='text/csv',
                headers={'Content-Disposition': f'attachment; filename=divisions_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'}
            )

        else:
            return jsonify({'success': False, 'error': 'Unsupported export format'}), 400

    except Exception as e:
        logger.error(f"Error exporting divisions: {e}")
        if request.is_json:
            return jsonify({'success': False, 'error': str(e)}), 500
        else:
            flash(f'Error exporting data: {str(e)}', 'danger')
            return redirect(url_for('divisions.index'))
