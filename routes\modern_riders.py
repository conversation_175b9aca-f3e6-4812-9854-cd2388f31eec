"""
Modern Rider Management Blueprint 2025
Enhanced rider dashboard and tracking with modern architecture
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for, send_file
from flask_login import login_required, current_user
from utils.db import get_db
import json
import time
from datetime import datetime, date, timedelta
import sqlite3
from io import BytesIO
import xlsxwriter

# Create blueprint
riders_bp = Blueprint('riders', __name__, url_prefix='/riders')

# ============================================================================
# DATETIME HELPER FUNCTIONS
# ============================================================================

def safe_datetime_format(datetime_value, format_string='%Y-%m-%d %H:%M'):
    """
    Safely format datetime values that might be strings or datetime objects

    Args:
        datetime_value: String datetime, datetime object, or None
        format_string: strftime format string

    Returns:
        Formatted datetime string or 'N/A' if invalid/None
    """
    if not datetime_value:
        return 'N/A'

    try:
        # If it's already a datetime object
        if isinstance(datetime_value, datetime):
            return datetime_value.strftime(format_string)

        # If it's a string, try to parse it with multiple formats
        if isinstance(datetime_value, str):
            datetime_value = datetime_value.strip()
            if not datetime_value:
                return 'N/A'

            # List of possible datetime formats in the database
            datetime_formats = [
                '%Y-%m-%d %H:%M:%S.%f',      # 2025-07-26 11:17:39.885061
                '%Y-%m-%d %H:%M:%S',         # 2025-07-31 07:38:21
                '%Y-%m-%d %H:%M',            # 2025-07-31 07:38
                '%Y-%m-%d',                  # 2025-07-28
                '%Y-%m-%dT%H:%M:%S.%f',      # ISO format with microseconds
                '%Y-%m-%dT%H:%M:%S',         # ISO format
                '%Y-%m-%dT%H:%M',            # ISO format without seconds
            ]

            dt = None
            for fmt in datetime_formats:
                try:
                    dt = datetime.strptime(datetime_value, fmt)
                    break
                except ValueError:
                    continue

            if dt is None:
                # If all parsing fails, return the original string
                return str(datetime_value)

            return dt.strftime(format_string)

        # If it's neither, return N/A
        return 'N/A'

    except Exception as e:
        print(f"Datetime formatting error: {e} for value: {datetime_value}")
        return str(datetime_value) if datetime_value else 'N/A'

# ============================================================================
# REPORT HELPER FUNCTIONS (moved to top for proper function definition order)
# ============================================================================

def get_financial_report(db, date_filter, rider_filter_clause):
    """Generate financial report data"""
    data = {}

    # Revenue by rider
    data['rider_revenue'] = db.execute(f'''
        SELECT
            r.rider_id,
            r.name,
            COUNT(o.order_id) as total_orders,
            COALESCE(SUM(o.order_amount), 0) as total_revenue,
            COALESCE(AVG(o.order_amount), 0) as avg_order_value,
            COALESCE(SUM(CASE WHEN o.status = 'Delivered' THEN o.order_amount ELSE 0 END), 0) as delivered_revenue
        FROM riders r
        LEFT JOIN orders o ON r.rider_id = o.rider_id
        WHERE 1=1 {date_filter} {rider_filter_clause}
        GROUP BY r.rider_id, r.name
        ORDER BY total_revenue DESC
    ''').fetchall()

    # Monthly revenue trends
    data['monthly_trends'] = db.execute(f'''
        SELECT
            strftime('%Y-%m', o.order_date) as month,
            COUNT(o.order_id) as orders,
            COALESCE(SUM(o.order_amount), 0) as revenue,
            COALESCE(SUM(CASE WHEN o.status = 'Delivered' THEN o.order_amount ELSE 0 END), 0) as delivered_revenue
        FROM orders o
        JOIN riders r ON o.rider_id = r.rider_id
        WHERE 1=1 {date_filter} {rider_filter_clause}
        GROUP BY strftime('%Y-%m', o.order_date)
        ORDER BY month DESC
    ''').fetchall()

    # Payment status breakdown
    data['payment_status'] = db.execute(f'''
        SELECT
            o.payment_status,
            COUNT(*) as count,
            COALESCE(SUM(o.order_amount), 0) as total_amount
        FROM orders o
        JOIN riders r ON o.rider_id = r.rider_id
        WHERE 1=1 {date_filter} {rider_filter_clause}
        GROUP BY o.payment_status
        ORDER BY total_amount DESC
    ''').fetchall()

    return data

def get_customer_report(db, date_filter, rider_filter_clause):
    """Generate customer report data"""
    data = {}

    # Customer delivery statistics
    data['customer_stats'] = db.execute(f'''
        SELECT
            o.customer_name,
            o.customer_phone,
            o.customer_address,
            COUNT(o.order_id) as total_orders,
            COALESCE(SUM(o.order_amount), 0) as total_spent,
            COALESCE(AVG(o.order_amount), 0) as avg_order_value,
            COUNT(CASE WHEN o.status = 'Delivered' THEN 1 END) as delivered_orders,
            MAX(o.order_date) as last_order_date
        FROM orders o
        JOIN riders r ON o.rider_id = r.rider_id
        WHERE 1=1 {date_filter} {rider_filter_clause}
        GROUP BY o.customer_name, o.customer_phone, o.customer_address
        ORDER BY total_spent DESC
        LIMIT 50
    ''').fetchall()

    # Customer satisfaction by delivery status
    data['delivery_success_rate'] = db.execute(f'''
        SELECT
            o.customer_name,
            COUNT(*) as total_orders,
            COUNT(CASE WHEN o.status = 'Delivered' THEN 1 END) as delivered,
            ROUND(COUNT(CASE WHEN o.status = 'Delivered' THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate
        FROM orders o
        JOIN riders r ON o.rider_id = r.rider_id
        WHERE 1=1 {date_filter} {rider_filter_clause}
        GROUP BY o.customer_name
        HAVING COUNT(*) >= 3
        ORDER BY success_rate DESC, total_orders DESC
        LIMIT 20
    ''').fetchall()

    return data

def get_geographic_report(db, date_filter, rider_filter_clause):
    """Generate geographic report data"""
    data = {}

    # Delivery by city/area
    data['city_stats'] = db.execute(f'''
        SELECT
            COALESCE(r.city, 'Unknown') as city,
            COUNT(o.order_id) as total_deliveries,
            COALESCE(SUM(o.order_amount), 0) as total_revenue,
            COUNT(DISTINCT r.rider_id) as active_riders,
            COALESCE(AVG(o.order_amount), 0) as avg_order_value
        FROM orders o
        JOIN riders r ON o.rider_id = r.rider_id
        WHERE 1=1 {date_filter} {rider_filter_clause}
        GROUP BY r.city
        ORDER BY total_deliveries DESC
    ''').fetchall()

    # Customer address analysis
    data['address_stats'] = db.execute(f'''
        SELECT
            CASE
                WHEN o.customer_address LIKE '%Karachi%' THEN 'Karachi'
                WHEN o.customer_address LIKE '%Lahore%' THEN 'Lahore'
                WHEN o.customer_address LIKE '%Islamabad%' THEN 'Islamabad'
                WHEN o.customer_address LIKE '%Rawalpindi%' THEN 'Rawalpindi'
                ELSE 'Other Cities'
            END as delivery_area,
            COUNT(o.order_id) as deliveries,
            COALESCE(SUM(o.order_amount), 0) as revenue
        FROM orders o
        JOIN riders r ON o.rider_id = r.rider_id
        WHERE 1=1 {date_filter} {rider_filter_clause}
        GROUP BY delivery_area
        ORDER BY deliveries DESC
    ''').fetchall()

    # Rider coverage by area
    data['rider_coverage'] = db.execute(f'''
        SELECT
            r.city,
            r.name as rider_name,
            COUNT(o.order_id) as orders_handled,
            COALESCE(SUM(o.order_amount), 0) as revenue_generated
        FROM riders r
        LEFT JOIN orders o ON r.rider_id = o.rider_id
        WHERE 1=1 {date_filter} {rider_filter_clause}
        GROUP BY r.city, r.rider_id, r.name
        ORDER BY r.city, orders_handled DESC
    ''').fetchall()

    return data

def get_time_analysis_report(db, date_filter, rider_filter_clause):
    """Generate time analysis report data"""
    data = {}

    # Delivery time analysis
    data['delivery_times'] = db.execute(f'''
        SELECT
            r.rider_id,
            r.name,
            COUNT(o.order_id) as total_orders,
            COALESCE(AVG(
                CASE
                    WHEN o.delivery_date IS NOT NULL AND o.order_date IS NOT NULL
                    THEN (julianday(o.delivery_date) - julianday(o.order_date)) * 24
                    ELSE NULL
                END
            ), 0) as avg_delivery_hours,
            COALESCE(MIN(
                CASE
                    WHEN o.delivery_date IS NOT NULL AND o.order_date IS NOT NULL
                    THEN (julianday(o.delivery_date) - julianday(o.order_date)) * 24
                    ELSE NULL
                END
            ), 0) as min_delivery_hours,
            COALESCE(MAX(
                CASE
                    WHEN o.delivery_date IS NOT NULL AND o.order_date IS NOT NULL
                    THEN (julianday(o.delivery_date) - julianday(o.order_date)) * 24
                    ELSE NULL
                END
            ), 0) as max_delivery_hours
        FROM riders r
        LEFT JOIN orders o ON r.rider_id = o.rider_id
        WHERE o.status = 'Delivered' {date_filter} {rider_filter_clause}
        GROUP BY r.rider_id, r.name
        HAVING COUNT(o.order_id) > 0
        ORDER BY avg_delivery_hours ASC
    ''').fetchall()

    # Hourly delivery patterns
    data['hourly_patterns'] = db.execute(f'''
        SELECT
            strftime('%H', o.delivery_date) as hour,
            COUNT(*) as deliveries,
            COALESCE(AVG(o.order_amount), 0) as avg_order_value
        FROM orders o
        JOIN riders r ON o.rider_id = r.rider_id
        WHERE o.status = 'Delivered' AND o.delivery_date IS NOT NULL {date_filter} {rider_filter_clause}
        GROUP BY strftime('%H', o.delivery_date)
        ORDER BY hour
    ''').fetchall()

    # Weekly delivery patterns
    data['weekly_patterns'] = db.execute(f'''
        SELECT
            CASE strftime('%w', o.delivery_date)
                WHEN '0' THEN 'Sunday'
                WHEN '1' THEN 'Monday'
                WHEN '2' THEN 'Tuesday'
                WHEN '3' THEN 'Wednesday'
                WHEN '4' THEN 'Thursday'
                WHEN '5' THEN 'Friday'
                WHEN '6' THEN 'Saturday'
            END as day_of_week,
            COUNT(*) as deliveries,
            COALESCE(AVG(o.order_amount), 0) as avg_order_value
        FROM orders o
        JOIN riders r ON o.rider_id = r.rider_id
        WHERE o.status = 'Delivered' AND o.delivery_date IS NOT NULL {date_filter} {rider_filter_clause}
        GROUP BY strftime('%w', o.delivery_date)
        ORDER BY strftime('%w', o.delivery_date)
    ''').fetchall()

    return data

def get_efficiency_report(db, date_filter, rider_filter_clause):
    """Generate efficiency report data"""
    data = {}

    # Rider efficiency metrics
    rider_efficiency_results = db.execute(f'''
        SELECT
            r.rider_id,
            r.name,
            r.rating,
            COUNT(o.order_id) as total_orders,
            COUNT(CASE WHEN o.status = 'Delivered' THEN 1 END) as delivered_orders,
            ROUND(COUNT(CASE WHEN o.status = 'Delivered' THEN 1 END) * 100.0 / COUNT(o.order_id), 2) as success_rate,
            COALESCE(SUM(o.order_amount), 0) as total_revenue,
            COALESCE(SUM(o.order_amount) / COUNT(o.order_id), 0) as revenue_per_order,
            COALESCE(AVG(
                CASE
                    WHEN o.delivery_date IS NOT NULL AND o.order_date IS NOT NULL
                    THEN (julianday(o.delivery_date) - julianday(o.order_date)) * 24
                    ELSE NULL
                END
            ), 0) as avg_delivery_time_hours
        FROM riders r
        LEFT JOIN orders o ON r.rider_id = o.rider_id
        WHERE 1=1 {date_filter} {rider_filter_clause}
        GROUP BY r.rider_id, r.name, r.rating
        HAVING COUNT(o.order_id) > 0
        ORDER BY success_rate DESC, revenue_per_order DESC
    ''').fetchall()
    data['rider_efficiency'] = [dict(row) for row in rider_efficiency_results]

    # Performance comparison
    performance_comparison_results = db.execute(f'''
        SELECT
            'Top Performer' as category,
            MAX(success_rate) as max_success_rate,
            MAX(revenue_per_order) as max_revenue_per_order,
            MIN(avg_delivery_time) as min_delivery_time
        FROM (
            SELECT
                ROUND(COUNT(CASE WHEN o.status = 'Delivered' THEN 1 END) * 100.0 / COUNT(o.order_id), 2) as success_rate,
                COALESCE(SUM(o.order_amount) / COUNT(o.order_id), 0) as revenue_per_order,
                COALESCE(AVG(
                    CASE
                        WHEN o.delivery_date IS NOT NULL AND o.order_date IS NOT NULL
                        THEN (julianday(o.delivery_date) - julianday(o.order_date)) * 24
                        ELSE NULL
                    END
                ), 0) as avg_delivery_time
            FROM riders r
            LEFT JOIN orders o ON r.rider_id = o.rider_id
            WHERE 1=1 {date_filter} {rider_filter_clause}
            GROUP BY r.rider_id
            HAVING COUNT(o.order_id) > 0
        )
        UNION ALL
        SELECT
            'Average' as category,
            AVG(success_rate) as avg_success_rate,
            AVG(revenue_per_order) as avg_revenue_per_order,
            AVG(avg_delivery_time) as avg_delivery_time
        FROM (
            SELECT
                ROUND(COUNT(CASE WHEN o.status = 'Delivered' THEN 1 END) * 100.0 / COUNT(o.order_id), 2) as success_rate,
                COALESCE(SUM(o.order_amount) / COUNT(o.order_id), 0) as revenue_per_order,
                COALESCE(AVG(
                    CASE
                        WHEN o.delivery_date IS NOT NULL AND o.order_date IS NOT NULL
                        THEN (julianday(o.delivery_date) - julianday(o.order_date)) * 24
                        ELSE NULL
                    END
                ), 0) as avg_delivery_time
            FROM riders r
            LEFT JOIN orders o ON r.rider_id = r.rider_id
            WHERE 1=1 {date_filter} {rider_filter_clause}
            GROUP BY r.rider_id
            HAVING COUNT(o.order_id) > 0
        )
    ''').fetchall()
    data['performance_comparison'] = [dict(row) for row in performance_comparison_results]

    return data

# ============================================================================
# ROUTE DEFINITIONS
# ============================================================================

@riders_bp.route('/')
@riders_bp.route('/dashboard')
@login_required
def dashboard():
    """Modern Rider Dashboard with Enhanced Analytics"""
    try:
        db = get_db()
        
        # Get rider overview statistics
        rider_stats = db.execute('''
            SELECT 
                COUNT(*) as total_riders,
                COUNT(CASE WHEN status = 'active' THEN 1 END) as active_riders,
                COUNT(CASE WHEN is_available = 1 THEN 1 END) as available_riders,
                AVG(rating) as avg_rating,
                SUM(total_deliveries) as total_deliveries,
                SUM(successful_deliveries) as successful_deliveries
            FROM riders
        ''').fetchone()
        
        # Get top performing riders
        top_riders = db.execute('''
            SELECT rider_id, name, rating, total_deliveries, successful_deliveries,
                   performance_stats, status, city, vehicle_type
            FROM riders 
            WHERE status = 'active'
            ORDER BY rating DESC, successful_deliveries DESC
            LIMIT 10
        ''').fetchall()
        
        # Parse performance stats for top riders
        enhanced_riders = []
        for rider in top_riders:
            rider_dict = dict(rider)
            if rider_dict['performance_stats']:
                try:
                    rider_dict['performance_data'] = json.loads(rider_dict['performance_stats'])
                except:
                    rider_dict['performance_data'] = {}
            else:
                rider_dict['performance_data'] = {}
            enhanced_riders.append(rider_dict)
        
        # Get recent performance logs
        recent_performance = db.execute('''
            SELECT rpl.*, r.name as rider_name
            FROM rider_performance_logs rpl
            JOIN riders r ON rpl.rider_id = r.rider_id
            ORDER BY rpl.date DESC
            LIMIT 20
        ''').fetchall()
        
        # Get city-wise distribution
        city_distribution = db.execute('''
            SELECT city, COUNT(*) as rider_count,
                   AVG(rating) as avg_city_rating,
                   SUM(total_deliveries) as city_deliveries
            FROM riders
            WHERE status = 'active'
            GROUP BY city
            ORDER BY rider_count DESC
        ''').fetchall()
        
        return render_template('riders/modern_dashboard.html',
                             rider_stats=rider_stats,
                             top_riders=enhanced_riders,
                             recent_performance=recent_performance,
                             city_distribution=city_distribution,
                             current_date=datetime.now())
        
    except Exception as e:
        print(f"Rider dashboard error: {e}")
        flash(f'Error loading rider dashboard: {str(e)}', 'danger')
        return redirect(url_for('dashboard'))

@riders_bp.route('/tracking')
@login_required
def tracking():
    """Advanced Rider Tracking with Real-time Updates"""
    try:
        db = get_db()
        
        # Get all riders with their current status and location
        riders_status = db.execute('''
            SELECT r.*,
                   COUNT(o.order_id) as active_orders,
                   GROUP_CONCAT(o.order_id) as order_ids,
                   o.customer_address as current_delivery_address
            FROM riders r
            LEFT JOIN orders o ON r.rider_id = o.rider_id
                AND o.status IN ('Dispatched', 'Out for Delivery')
            GROUP BY r.rider_id
            ORDER BY r.is_available DESC, r.status, r.name
        ''').fetchall()
        
        # Enhanced riders with performance stats
        enhanced_riders = []
        for rider in riders_status:
            rider_dict = dict(rider)
            
            # Parse performance stats
            if rider_dict['performance_stats']:
                try:
                    performance_data = json.loads(rider_dict['performance_stats'])
                    rider_dict['performance_data'] = performance_data
                except:
                    rider_dict['performance_data'] = {
                        'avg_delivery_time': 0,
                        'on_time_percentage': 0,
                        'customer_satisfaction': 0,
                        'fuel_efficiency': 0,
                        'monthly_earnings': 0
                    }
            else:
                rider_dict['performance_data'] = {
                    'avg_delivery_time': 25,
                    'on_time_percentage': 95.0,
                    'customer_satisfaction': rider_dict.get('rating', 0),
                    'fuel_efficiency': 35,
                    'monthly_earnings': 40000
                }
            
            enhanced_riders.append(rider_dict)
        
        # Get delivery statistics for today
        today_stats = db.execute('''
            SELECT
                COUNT(*) as total_deliveries_today,
                COUNT(CASE WHEN status = 'Delivered' THEN 1 END) as completed_today,
                COUNT(CASE WHEN status IN ('Dispatched', 'Out for Delivery') THEN 1 END) as pending_today,
                COUNT(CASE WHEN status = 'Failed' THEN 1 END) as failed_today
            FROM orders
            WHERE DATE(order_date) = DATE('now')
            AND rider_id IS NOT NULL
        ''').fetchone()
        
        # Get performance summary
        performance_summary = {
            'total_active_riders': len([r for r in enhanced_riders if r['status'] == 'active']),
            'available_riders': len([r for r in enhanced_riders if r['is_available']]),
            'riders_on_delivery': len([r for r in enhanced_riders if r['active_orders'] > 0]),
            'avg_rating': sum(r['rating'] for r in enhanced_riders) / len(enhanced_riders) if enhanced_riders else 0
        }
        
        return render_template('riders/modern_tracking.html',
                             riders_status=enhanced_riders,
                             today_stats=today_stats,
                             performance_summary=performance_summary,
                             now=datetime.now())
        
    except Exception as e:
        print(f"Rider tracking error: {e}")
        flash(f'Error loading rider tracking: {str(e)}', 'danger')
        return redirect(url_for('dashboard'))

# Duplicate route removed - using more comprehensive version at line 760

@riders_bp.route('/api/update_location', methods=['POST'])
@login_required
def api_update_location():
    """API endpoint to update rider location"""
    try:
        data = request.get_json()
        rider_id = data.get('rider_id')
        location = data.get('location')
        is_available = data.get('is_available', True)
        
        if not rider_id or not location:
            return jsonify({'error': 'Rider ID and location required'}), 400
        
        db = get_db()
        
        # Update rider location and availability
        db.execute('''
            UPDATE riders 
            SET current_location = ?, 
                is_available = ?,
                last_location_update = CURRENT_TIMESTAMP,
                updated_at = CURRENT_TIMESTAMP
            WHERE rider_id = ?
        ''', (location, is_available, rider_id))
        
        db.commit()
        
        return jsonify({
            'success': True,
            'message': 'Location updated successfully',
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@riders_bp.route('/performance')
@login_required
def performance():
    """Rider Performance Dashboard"""
    try:
        db = get_db()

        # Get summary statistics
        summary_stats = {}

        # Total riders count
        total_riders = db.execute('SELECT COUNT(*) as count FROM riders').fetchone()
        summary_stats['total_riders'] = total_riders['count'] if total_riders else 0

        # Active riders count
        active_riders = db.execute('SELECT COUNT(*) as count FROM riders WHERE status = "active"').fetchone()
        summary_stats['active_riders'] = active_riders['count'] if active_riders else 0

        # Average performance score
        avg_performance = db.execute('SELECT AVG(rating) as avg_rating FROM riders WHERE rating IS NOT NULL').fetchone()
        summary_stats['avg_performance_score'] = float(avg_performance['avg_rating']) if avg_performance and avg_performance['avg_rating'] else 0.0

        # Total revenue (estimated from deliveries)
        total_revenue = db.execute('SELECT SUM(total_deliveries * 50) as revenue FROM riders').fetchone()  # Assuming 50 Rs per delivery
        summary_stats['total_revenue'] = float(total_revenue['revenue']) if total_revenue and total_revenue['revenue'] else 0.0

        # Get top performer
        top_performer = db.execute('''
            SELECT rider_id, name, rating,
                   CASE WHEN total_deliveries > 0
                        THEN (successful_deliveries * 100.0 / total_deliveries)
                        ELSE 0 END as success_rate
            FROM riders
            WHERE status = "active" AND rating IS NOT NULL
            ORDER BY rating DESC, success_rate DESC
            LIMIT 1
        ''').fetchone()

        if top_performer:
            summary_stats['top_performer'] = {
                'rider_id': top_performer['rider_id'],
                'name': top_performer['name'],
                'rating': float(top_performer['rating']) if top_performer['rating'] else 0.0,
                'success_rate': float(top_performer['success_rate']) if top_performer['success_rate'] else 0.0
            }
        else:
            summary_stats['top_performer'] = None

        # Get performance data for all riders
        performance_data = db.execute('''
            SELECT rider_id, name, phone, email, city, status, rating,
                   total_deliveries, successful_deliveries,
                   CASE WHEN total_deliveries > 0
                        THEN (successful_deliveries * 100.0 / total_deliveries)
                        ELSE 0 END as success_rate,
                   (total_deliveries * 50) as total_revenue,
                   rating as performance_score
            FROM riders
            ORDER BY rating DESC, success_rate DESC
        ''').fetchall()

        # Convert to list of dictionaries for template
        performance_data_list = []
        for rider in performance_data:
            rider_dict = dict(rider)
            # Ensure numeric fields are properly typed
            rider_dict['rating'] = float(rider_dict['rating']) if rider_dict['rating'] else 0.0
            rider_dict['success_rate'] = float(rider_dict['success_rate']) if rider_dict['success_rate'] else 0.0
            rider_dict['total_revenue'] = float(rider_dict['total_revenue']) if rider_dict['total_revenue'] else 0.0
            rider_dict['performance_score'] = float(rider_dict['performance_score']) if rider_dict['performance_score'] else 0.0
            performance_data_list.append(rider_dict)

        # Get monthly trends data for chart
        monthly_trends_results = db.execute('''
            SELECT
                strftime('%Y-%m', o.order_date) as month,
                COUNT(*) as total_deliveries,
                COALESCE(SUM(o.order_amount), 0) as revenue
            FROM orders o
            JOIN riders r ON o.rider_id = r.rider_id
            WHERE o.status = 'Delivered'
            GROUP BY strftime('%Y-%m', o.order_date)
            ORDER BY month DESC
            LIMIT 12
        ''').fetchall()
        monthly_trends = [dict(row) for row in monthly_trends_results]

        return render_template('riders/performance.html',
                             title='Rider Performance Dashboard',
                             summary_stats=summary_stats,
                             performance_data=performance_data_list,
                             monthly_trends=monthly_trends)

    except Exception as e:
        print(f"Rider performance error: {e}")
        flash(f'Error loading rider performance dashboard: {str(e)}', 'danger')
        return redirect(url_for('riders.dashboard'))

@riders_bp.route('/analytics')
@login_required
def analytics():
    """Advanced Rider Analytics Dashboard"""
    try:
        db = get_db()
        
        # Get comprehensive analytics data
        analytics_data = {
            'performance_trends': [],
            'city_comparison': [],
            'rating_distribution': [],
            'delivery_efficiency': []
        }
        
        # Performance trends (last 30 days)
        trends = db.execute('''
            SELECT date, 
                   SUM(deliveries_completed) as total_deliveries,
                   AVG(customer_ratings_avg) as avg_rating,
                   AVG(average_delivery_time_minutes) as avg_time,
                   SUM(earnings) as total_earnings
            FROM rider_performance_logs
            WHERE date >= date('now', '-30 days')
            GROUP BY date
            ORDER BY date
        ''').fetchall()
        
        analytics_data['performance_trends'] = [dict(trend) for trend in trends]
        
        # City comparison
        city_stats = db.execute('''
            SELECT r.city,
                   COUNT(r.rider_id) as rider_count,
                   AVG(r.rating) as avg_rating,
                   SUM(r.total_deliveries) as total_deliveries,
                   AVG(rpl.earnings) as avg_earnings
            FROM riders r
            LEFT JOIN rider_performance_logs rpl ON r.rider_id = rpl.rider_id
            WHERE r.status = 'active'
            GROUP BY r.city
            ORDER BY rider_count DESC
        ''').fetchall()
        
        analytics_data['city_comparison'] = [dict(city) for city in city_stats]
        
        # Rating distribution
        rating_dist = db.execute('''
            SELECT 
                CASE 
                    WHEN rating >= 4.5 THEN '4.5-5.0'
                    WHEN rating >= 4.0 THEN '4.0-4.4'
                    WHEN rating >= 3.5 THEN '3.5-3.9'
                    WHEN rating >= 3.0 THEN '3.0-3.4'
                    ELSE 'Below 3.0'
                END as rating_range,
                COUNT(*) as rider_count
            FROM riders
            WHERE status = 'active'
            GROUP BY rating_range
            ORDER BY rating_range DESC
        ''').fetchall()
        
        analytics_data['rating_distribution'] = [dict(rating) for rating in rating_dist]
        
        return render_template('riders/modern_analytics.html',
                             analytics_data=analytics_data,
                             current_date=datetime.now())
        
    except Exception as e:
        print(f"Rider analytics error: {e}")
        flash(f'Error loading rider analytics: {str(e)}', 'danger')
        return redirect(url_for('riders.dashboard'))

@riders_bp.route('/api/rider/<rider_id>/performance')
@login_required
def rider_performance_api(rider_id):
    """API endpoint for rider performance data (used by tracking page)"""
    try:
        db = get_db()

        # Get rider basic information
        rider = db.execute('''
            SELECT rider_id, name, email, phone, city, status, rating,
                   total_deliveries, successful_deliveries, vehicle_type,
                   license_number, current_location, created_at
            FROM riders
            WHERE rider_id = ?
        ''', (rider_id,)).fetchone()

        if not rider:
            return jsonify({
                'error': 'Rider not found',
                'rider': {},
                'summary': {}
            }), 404

        # Convert to dict for safe access
        rider_dict = dict(rider)

        # Get performance statistics
        performance_stats = db.execute('''
            SELECT
                COUNT(*) as total_orders,
                SUM(CASE WHEN status = 'Delivered' THEN 1 ELSE 0 END) as delivered_orders,
                AVG(CASE WHEN delivery_rating IS NOT NULL THEN delivery_rating ELSE 0 END) as avg_rating,
                SUM(order_amount) as total_revenue,
                COUNT(CASE WHEN DATE(order_date) = DATE('now') THEN 1 END) as today_deliveries
            FROM orders
            WHERE rider_id = ?
        ''', (rider_id,)).fetchone()

        if performance_stats:
            stats_dict = dict(performance_stats)
            total_orders = stats_dict.get('total_orders', 0) or 0
            delivered_orders = stats_dict.get('delivered_orders', 0) or 0
            success_rate = (delivered_orders / total_orders * 100) if total_orders > 0 else 0
            avg_daily_deliveries = delivered_orders / 30 if delivered_orders > 0 else 0  # Rough estimate
        else:
            success_rate = 0
            avg_daily_deliveries = 0
            total_orders = 0

        # Get recent performance logs count
        logs_count = db.execute('''
            SELECT COUNT(*) as log_count
            FROM orders
            WHERE rider_id = ? AND order_date >= date('now', '-30 days')
        ''', (rider_id,)).fetchone()

        total_logs = logs_count['log_count'] if logs_count else 0

        # Prepare response data
        response_data = {
            'rider': {
                'rider_id': rider_dict.get('rider_id', ''),
                'name': rider_dict.get('name', ''),
                'city': rider_dict.get('city', ''),
                'rating': rider_dict.get('rating', 0.0) or 0.0,
                'total_deliveries': rider_dict.get('total_deliveries', 0) or 0,
                'status': rider_dict.get('status', ''),
                'vehicle_type': rider_dict.get('vehicle_type', ''),
                'current_location': rider_dict.get('current_location', '')
            },
            'summary': {
                'success_rate': success_rate,
                'avg_daily_deliveries': avg_daily_deliveries,
                'total_logs': total_logs,
                'total_orders': total_orders,
                'delivered_orders': delivered_orders
            }
        }

        return jsonify(response_data)

    except Exception as e:
        print(f"Rider performance API error: {e}")
        return jsonify({'error': f'Error loading rider performance: {str(e)}'}), 500

@riders_bp.route('/assignment-dashboard')
@login_required
def assignment_dashboard():
    """Rider Assignment Dashboard - Orders Ready for Pickup"""
    try:
        db = get_db()

        # Get orders ready for rider assignment (packed and ready for pickup)
        ready_orders = db.execute('''
            SELECT o.*, c.name as customer_name, c.address as customer_address,
                   c.phone as customer_phone, c.city as customer_city
            FROM orders o
            LEFT JOIN customers c ON o.customer_id = c.customer_id
            WHERE o.status = 'Ready for Pickup' AND o.warehouse_status = 'packed'
            AND (o.rider_id IS NULL OR o.rider_id = '')
            ORDER BY o.priority_level DESC, o.packed_at ASC
        ''').fetchall()

        # Get available riders
        available_riders = db.execute('''
            SELECT rider_id, name, phone, vehicle_type, current_location, rating
            FROM riders
            WHERE status = 'active' AND is_available = 1
            ORDER BY rating DESC
        ''').fetchall()

        # Process orders - keep datetime fields as strings for template filter processing
        processed_orders = []
        for order in ready_orders:
            order_dict = dict(order)

            # Ensure all datetime fields are properly handled as strings
            # The template filter will handle the conversion and formatting
            datetime_fields = ['packed_at', 'order_date', 'last_updated', 'dispatch_date',
                             'delivery_date', 'approval_date', 'pickup_scheduled_at',
                             'picked_up_at', 'out_for_delivery_at', 'delivered_at']

            for field in datetime_fields:
                if field in order_dict and order_dict[field] is not None:
                    # Ensure the field is a string for consistent template processing
                    if not isinstance(order_dict[field], str):
                        order_dict[field] = str(order_dict[field])

            processed_orders.append(order_dict)

        return render_template('riders/assignment_dashboard.html',
                             ready_orders=processed_orders,
                             available_riders=[dict(rider) for rider in available_riders],
                             title='Rider Assignment Dashboard')

    except Exception as e:
        print(f"Assignment dashboard error: {e}")
        flash(f'Error loading assignment dashboard: {str(e)}', 'danger')
        return redirect(url_for('riders.dashboard'))

@riders_bp.route('/auto-assign/<order_id>')
@login_required
def auto_assign_order(order_id):
    """Automatically assign order to best available rider"""
    try:
        db = get_db()

        # Check if order is available for assignment
        order = db.execute('''
            SELECT * FROM orders
            WHERE order_id = ? AND status = 'Ready for Pickup'
            AND warehouse_status = 'packed'
            AND (rider_id IS NULL OR rider_id = '')
        ''', (order_id,)).fetchone()

        if not order:
            flash('Order not available for assignment', 'warning')
            return redirect(url_for('riders.assignment_dashboard'))

        # Get available riders with scoring algorithm
        available_riders = db.execute('''
            SELECT r.*,
                   COUNT(o.order_id) as current_orders,
                   COALESCE(r.rating, 4.0) as rider_rating
            FROM riders r
            LEFT JOIN orders o ON r.rider_id = o.rider_id
                AND o.status IN ('Dispatched', 'Out for Delivery')
            WHERE r.status = 'active' AND r.is_available = 1
            GROUP BY r.rider_id
            ORDER BY
                (COALESCE(r.rating, 4.0) * 0.4) +  -- 40% weight on rating
                ((10 - COUNT(o.order_id)) * 0.6)   -- 60% weight on availability (inverse of workload)
            DESC
        ''').fetchall()

        if not available_riders:
            flash('No available riders for assignment', 'warning')
            return redirect(url_for('riders.assignment_dashboard'))

        # Select best rider (first in sorted list)
        best_rider = available_riders[0]

        # Assign order to rider
        db.execute('''
            UPDATE orders
            SET rider_id = ?, status = 'Dispatched', dispatch_date = CURRENT_TIMESTAMP,
                updated_by = ?, last_updated = CURRENT_TIMESTAMP
            WHERE order_id = ?
        ''', (best_rider['rider_id'], current_user.username, order_id))

        # Log assignment
        db.execute('''
            INSERT INTO order_status_history
            (order_id, status, previous_status, changed_by, notes)
            VALUES (?, 'Dispatched', 'Ready for Pickup', ?, ?)
        ''', (order_id, current_user.username, f'Auto-assigned to rider: {best_rider["name"]}'))

        db.commit()

        flash(f'Order {order_id} automatically assigned to {best_rider["name"]}!', 'success')
        return redirect(url_for('riders.assignment_dashboard'))

    except Exception as e:
        print(f"Auto-assignment error: {e}")
        flash(f'Error auto-assigning order: {str(e)}', 'danger')
        return redirect(url_for('riders.assignment_dashboard'))

@riders_bp.route('/manual-assign/<order_id>')
@login_required
def manual_assign_order(order_id):
    """Show manual assignment form for specific order"""
    try:
        db = get_db()

        # Get order details with more comprehensive check
        order = db.execute('''
            SELECT o.*, c.name as customer_name, c.address as customer_address,
                   c.phone as customer_phone, c.city as customer_city
            FROM orders o
            LEFT JOIN customers c ON o.customer_id = c.customer_id
            WHERE o.order_id = ?
        ''', (order_id,)).fetchone()

        if not order:
            flash(f'Order {order_id} not found in the system', 'danger')
            return redirect(url_for('riders.assignment_dashboard'))

        # Check if order is available for assignment
        if order['status'] != 'Ready for Pickup':
            flash(f'Order {order_id} cannot be assigned. Current status: {order["status"]}. Only orders with status "Ready for Pickup" can be assigned.', 'warning')
            return redirect(url_for('riders.assignment_dashboard'))

        if order['warehouse_status'] != 'packed':
            flash(f'Order {order_id} is not packed yet. Current warehouse status: {order["warehouse_status"] or "Not set"}. Order must be packed before assignment.', 'warning')
            return redirect(url_for('riders.assignment_dashboard'))

        if order['rider_id'] and order['rider_id'] != '':
            flash(f'Order {order_id} is already assigned to a rider. Please check the assignment dashboard.', 'info')
            return redirect(url_for('riders.assignment_dashboard'))

        # Get available riders
        available_riders = db.execute('''
            SELECT r.*, COUNT(o.order_id) as current_orders
            FROM riders r
            LEFT JOIN orders o ON r.rider_id = o.rider_id
                AND o.status IN ('Dispatched', 'Out for Delivery')
            WHERE r.status = 'active' AND r.is_available = 1
            GROUP BY r.rider_id
            ORDER BY r.rating DESC, current_orders ASC
        ''').fetchall()

        return render_template('riders/manual_assignment.html',
                             order=dict(order),
                             available_riders=[dict(rider) for rider in available_riders],
                             title=f'Assign Order {order_id}')

    except Exception as e:
        print(f"Manual assignment error: {e}")
        flash(f'Error loading assignment form: {str(e)}', 'danger')
        return redirect(url_for('riders.assignment_dashboard'))

@riders_bp.route('/assign-order', methods=['POST'])
@login_required
def assign_order():
    """Process manual order assignment"""
    try:
        order_id = request.form.get('order_id')
        rider_id = request.form.get('rider_id')

        if not order_id or not rider_id:
            flash('Missing order or rider information', 'danger')
            return redirect(url_for('riders.assignment_dashboard'))

        db = get_db()

        # Verify order and rider are valid
        order = db.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
        rider = db.execute('SELECT * FROM riders WHERE rider_id = ?', (rider_id,)).fetchone()

        if not order or not rider:
            flash('Invalid order or rider', 'danger')
            return redirect(url_for('riders.assignment_dashboard'))

        # Assign order to rider
        db.execute('''
            UPDATE orders
            SET rider_id = ?, status = 'Dispatched', dispatch_date = CURRENT_TIMESTAMP,
                updated_by = ?, last_updated = CURRENT_TIMESTAMP
            WHERE order_id = ?
        ''', (rider_id, current_user.username, order_id))

        # Log assignment
        db.execute('''
            INSERT INTO order_status_history
            (order_id, status, previous_status, changed_by, notes)
            VALUES (?, 'Dispatched', 'Ready for Pickup', ?, ?)
        ''', (order_id, current_user.username, f'Manually assigned to rider: {rider["name"]}'))

        db.commit()

        flash(f'Order {order_id} assigned to {rider["name"]}!', 'success')
        return redirect(url_for('riders.assignment_dashboard'))

    except Exception as e:
        print(f"Assignment error: {e}")
        flash(f'Error assigning order: {str(e)}', 'danger')
        return redirect(url_for('riders.assignment_dashboard'))

@riders_bp.route('/self-pickup')
@login_required
def self_pickup_dashboard():
    """Rider Self-Pickup Dashboard - Available Orders for Claiming"""
    try:
        db = get_db()

        # Get orders available for self-pickup (packed and ready)
        available_orders = db.execute('''
            SELECT o.*, c.name as customer_name, c.address as customer_address,
                   c.phone as customer_phone, c.city as customer_city,
                   w.name as warehouse_name, w.address as warehouse_address
            FROM orders o
            LEFT JOIN customers c ON o.customer_id = c.customer_id
            LEFT JOIN warehouses w ON o.warehouse_id = w.warehouse_id
            WHERE o.status = 'Ready for Pickup' AND o.warehouse_status = 'packed'
            AND (o.rider_id IS NULL OR o.rider_id = '')
            ORDER BY o.priority_level DESC, o.order_amount DESC, o.packed_at ASC
        ''').fetchall()

        # Get current user's active orders (if they are a rider)
        current_rider_orders = []
        if hasattr(current_user, 'rider_id') and current_user.rider_id:
            current_rider_orders = db.execute('''
                SELECT o.*, c.name as customer_name, c.address as customer_address
                FROM orders o
                LEFT JOIN customers c ON o.customer_id = c.customer_id
                WHERE o.rider_id = ? AND o.status IN ('Dispatched', 'Out for Delivery')
                ORDER BY o.dispatch_date DESC
            ''', (current_user.rider_id,)).fetchall()

        return render_template('riders/self_pickup_dashboard.html',
                             available_orders=[dict(order) for order in available_orders],
                             current_orders=[dict(order) for order in current_rider_orders],
                             title='Self-Pickup Dashboard')

    except Exception as e:
        print(f"Self-pickup dashboard error: {e}")
        flash(f'Error loading self-pickup dashboard: {str(e)}', 'danger')
        return redirect(url_for('riders.dashboard'))

@riders_bp.route('/claim-order/<order_id>')
@login_required
def claim_order(order_id):
    """Allow rider to claim an available order"""
    try:
        db = get_db()

        # Check if order is available for claiming
        order = db.execute('''
            SELECT * FROM orders
            WHERE order_id = ? AND status = 'Ready for Pickup'
            AND warehouse_status = 'packed'
            AND (rider_id IS NULL OR rider_id = '')
        ''', (order_id,)).fetchone()

        if not order:
            flash('Order not available for claiming', 'warning')
            return redirect(url_for('riders.self_pickup_dashboard'))

        # Check if current user is a rider and available
        # Look up rider_id based on current user's username or email
        rider = db.execute('''
            SELECT * FROM riders
            WHERE (name = ? OR email = ? OR phone = ?)
            AND status = 'active' AND is_available = 1
        ''', (current_user.full_name, current_user.email, current_user.username)).fetchone()

        if not rider:
            # Try alternative lookup by username pattern
            rider = db.execute('''
                SELECT * FROM riders
                WHERE name LIKE ? AND status = 'active' AND is_available = 1
            ''', (f'%{current_user.username}%',)).fetchone()

        if not rider:
            flash('You are not registered as an available rider', 'danger')
            return redirect(url_for('riders.self_pickup_dashboard'))

        rider_id = rider['rider_id']



        # Assign order to current rider
        db.execute('''
            UPDATE orders
            SET rider_id = ?, status = 'Dispatched', dispatch_date = CURRENT_TIMESTAMP,
                updated_by = ?, last_updated = CURRENT_TIMESTAMP
            WHERE order_id = ?
        ''', (rider_id, current_user.username, order_id))

        # Log the self-pickup claim
        db.execute('''
            INSERT INTO order_status_history
            (order_id, status, previous_status, changed_by, notes)
            VALUES (?, 'Dispatched', 'Ready for Pickup', ?, ?)
        ''', (order_id, current_user.username, f'Self-claimed by rider: {rider["name"]}'))

        db.commit()

        flash(f'Order {order_id} successfully claimed! You can now pick it up.', 'success')
        return redirect(url_for('riders.self_pickup_dashboard'))

    except Exception as e:
        print(f"Order claim error: {e}")
        flash(f'Error claiming order: {str(e)}', 'danger')
        return redirect(url_for('riders.self_pickup_dashboard'))

@riders_bp.route('/claim_pickup', methods=['POST'])
@login_required
def claim_pickup():
    """Handle rider pickup claim from orders page"""
    try:
        order_id = request.form.get('order_id')
        rider_name = request.form.get('rider_name')
        rider_id_input = request.form.get('rider_id')
        vehicle_type = request.form.get('vehicle_type', '')

        if not order_id or not rider_name or not rider_id_input:
            return jsonify({'success': False, 'message': 'Missing required information'})

        db = get_db()

        # Check if order is available for claiming
        order = db.execute('''
            SELECT * FROM orders
            WHERE order_id = ? AND status = 'Ready for Pickup'
            AND warehouse_status = 'packed'
            AND (rider_id IS NULL OR rider_id = '')
        ''', (order_id,)).fetchone()

        if not order:
            return jsonify({'success': False, 'message': 'Order not available for claiming'})

        # Find or create rider record
        rider = db.execute('''
            SELECT * FROM riders
            WHERE rider_id = ? OR name = ? OR phone = ?
        ''', (rider_id_input, rider_name, rider_id_input)).fetchone()

        if not rider:
            # Create new rider record
            new_rider_id = f"R{int(time.time())}"
            db.execute('''
                INSERT INTO riders (rider_id, name, phone, vehicle_type, status, is_available)
                VALUES (?, ?, ?, ?, 'active', 1)
            ''', (new_rider_id, rider_name, rider_id_input, vehicle_type))
            rider_id_to_use = new_rider_id
        else:
            rider_id_to_use = rider['rider_id']

        # Assign order to rider
        db.execute('''
            UPDATE orders
            SET rider_id = ?, status = 'Dispatched', dispatch_date = CURRENT_TIMESTAMP,
                updated_by = ?, last_updated = CURRENT_TIMESTAMP
            WHERE order_id = ?
        ''', (rider_id_to_use, current_user.username, order_id))

        # Log the pickup claim
        db.execute('''
            INSERT INTO order_status_history
            (order_id, status, previous_status, changed_by, notes)
            VALUES (?, 'Dispatched', 'Ready for Pickup', ?, ?)
        ''', (order_id, current_user.username, f'Claimed by rider: {rider_name}'))

        db.commit()

        return jsonify({'success': True, 'message': f'Order {order_id} successfully claimed by {rider_name}!'})

    except Exception as e:
        print(f"Pickup claim error: {e}")
        return jsonify({'success': False, 'message': f'Error claiming order: {str(e)}'})

@riders_bp.route('/api/rider-status-data')
@login_required
def rider_status_data():
    """API endpoint for enhanced rider status data"""
    try:
        db = get_db()

        # Get all riders with their current status and assignments
        riders = db.execute('''
            SELECT r.rider_id, r.name, r.phone, r.vehicle_type, r.status,
                   r.current_location, r.rating,
                   o.order_id as current_order,
                   c.name as customer_name,
                   c.address as delivery_address
            FROM riders r
            LEFT JOIN orders o ON r.rider_id = o.rider_id
                AND o.status IN ('Dispatched', 'Out for Delivery')
            LEFT JOIN customers c ON o.customer_id = c.customer_id
            WHERE r.status IN ('active', 'available', 'busy')
            ORDER BY r.status DESC, r.name ASC
        ''').fetchall()

        # Format rider data with enhanced information
        rider_data = []
        for rider in riders:
            # Determine rider status based on current assignment
            if rider['current_order']:
                status = 'Delivering'
                location = f"En route to {rider['customer_name'] or 'customer'}"
                eta = "15-30 min"  # This could be calculated based on real data
                distance = "2.5 km"  # This could be calculated from GPS data
            elif rider['status'] == 'active':
                status = 'Available'
                location = rider['current_location'] or 'Warehouse'
                eta = None
                distance = "At base"
            else:
                status = 'Offline'
                location = 'Unknown'
                eta = None
                distance = 'N/A'

            rider_data.append({
                'rider_id': rider['rider_id'],
                'name': rider['name'],
                'status': status,
                'current_order': rider['current_order'],
                'location': location,
                'eta': eta,
                'distance': distance,
                'phone': rider['phone'],
                'vehicle_type': rider['vehicle_type'],
                'rating': rider['rating']
            })

        return jsonify({
            'riders': rider_data,
            'last_updated': datetime.now().strftime('%H:%M:%S')
        })

    except Exception as e:
        print(f"Rider status data error: {e}")
        return jsonify({'error': str(e)}), 500

@riders_bp.route('/api/live-tracking-data')
@login_required
def live_tracking_data():
    """API endpoint for live tracking sidebar data"""
    try:
        db = get_db()

        # Get active orders (dispatched and out for delivery)
        active_orders = db.execute('''
            SELECT o.order_id, o.status, o.dispatch_date,
                   c.name as customer_name,
                   r.name as rider_name
            FROM orders o
            LEFT JOIN customers c ON o.customer_id = c.customer_id
            LEFT JOIN riders r ON o.rider_id = r.rider_id
            WHERE o.status IN ('Dispatched', 'Out for Delivery')
            ORDER BY o.dispatch_date DESC
            LIMIT 10
        ''').fetchall()

        # Get recent status updates (last 2 hours)
        recent_updates = db.execute('''
            SELECT osh.order_id, osh.status, osh.changed_at, osh.notes,
                   c.name as customer_name
            FROM order_status_history osh
            LEFT JOIN orders o ON osh.order_id = o.order_id
            LEFT JOIN customers c ON o.customer_id = c.customer_id
            WHERE osh.changed_at >= datetime('now', '-2 hours')
            ORDER BY osh.changed_at DESC
            LIMIT 15
        ''').fetchall()

        # Get today's statistics
        today_stats = db.execute('''
            SELECT
                COUNT(CASE WHEN status = 'Delivered' AND DATE(delivery_date) = DATE('now') THEN 1 END) as delivered_today,
                COUNT(CASE WHEN status IN ('Dispatched', 'Out for Delivery') THEN 1 END) as pending_today,
                COALESCE(SUM(CASE WHEN status = 'Delivered' AND DATE(delivery_date) = DATE('now') THEN order_amount END), 0) as revenue_today
            FROM orders
        ''').fetchone()

        # Format the data for JSON response
        response_data = {
            'active_orders': [
                {
                    'order_id': order['order_id'],
                    'customer_name': order['customer_name'],
                    'status': order['status'],
                    'rider_name': order['rider_name'],
                    'dispatch_time': safe_datetime_format(order['dispatch_date'], '%H:%M') if order['dispatch_date'] else 'N/A'
                }
                for order in active_orders
            ],
            'recent_updates': [
                {
                    'order_id': update['order_id'],
                    'customer_name': update['customer_name'],
                    'status': update['status'],
                    'time_ago': get_time_ago(update['changed_at']),
                    'message': f"{update['order_id']} {update['status'].lower()}"
                }
                for update in recent_updates
            ],
            'today_stats': {
                'delivered': today_stats['delivered_today'],
                'pending': today_stats['pending_today'],
                'revenue': f"Rs.{today_stats['revenue_today']:,.0f}"
            },
            'last_updated': datetime.now().strftime('%H:%M:%S')
        }

        return jsonify(response_data)

    except Exception as e:
        print(f"Live tracking data error: {e}")
        return jsonify({'error': str(e)}), 500

def get_time_ago(timestamp):
    """Calculate time ago from timestamp"""
    try:
        if isinstance(timestamp, str):
            timestamp = datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S')

        now = datetime.now()
        diff = now - timestamp

        if diff.days > 0:
            return f"{diff.days}d ago"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours}h ago"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"{minutes}m ago"
        else:
            return "Just now"
    except:
        return "Unknown"

@riders_bp.route('/reports')
@login_required
def reports():
    """Comprehensive Rider Reports Dashboard"""
    try:
        db = get_db()

        # Get report type from query parameter
        report_type = request.args.get('type', 'overview')

        # Get date range filters
        date_from = request.args.get('date_from', '')
        date_to = request.args.get('date_to', '')
        rider_filter = request.args.get('rider', '')

        # Set default date range (last 30 days)
        if not date_from:
            date_from = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        if not date_to:
            date_to = datetime.now().strftime('%Y-%m-%d')

        # Get all riders for filter dropdown
        all_riders = db.execute('''
            SELECT rider_id, name FROM riders
            WHERE status = 'active'
            ORDER BY name
        ''').fetchall()

        # Generate report data based on type
        report_data = generate_report_data(db, report_type, date_from, date_to, rider_filter)

        return render_template('riders/comprehensive_reports.html',
                             report_type=report_type,
                             report_data=report_data,
                             all_riders=[dict(rider) for rider in all_riders],
                             date_from=date_from,
                             date_to=date_to,
                             rider_filter=rider_filter,
                             title='Comprehensive Rider Reports')

    except Exception as e:
        print(f"Reports error: {e}")
        flash(f'Error loading reports: {str(e)}', 'danger')
        return redirect(url_for('riders.dashboard'))

def generate_report_data(db, report_type, date_from, date_to, rider_filter):
    """Generate data for different report types"""

    # Base WHERE clause for date filtering
    date_filter = f"AND DATE(o.order_date) BETWEEN '{date_from}' AND '{date_to}'"
    rider_filter_clause = f"AND r.rider_id = '{rider_filter}'" if rider_filter else ""

    if report_type == 'performance':
        return get_performance_report(db, date_filter, rider_filter_clause)
    elif report_type == 'delivery':
        return get_delivery_report(db, date_filter, rider_filter_clause)
    elif report_type == 'financial':
        return get_financial_report(db, date_filter, rider_filter_clause)
    elif report_type == 'customer':
        return get_customer_report(db, date_filter, rider_filter_clause)
    elif report_type == 'geographic':
        return get_geographic_report(db, date_filter, rider_filter_clause)
    elif report_type == 'time_analysis':
        return get_time_analysis_report(db, date_filter, rider_filter_clause)
    elif report_type == 'efficiency':
        return get_efficiency_report(db, date_filter, rider_filter_clause)
    else:  # overview
        return get_overview_report(db, date_filter, rider_filter_clause)

@riders_bp.route('/reports/export/<report_type>')
@login_required
def export_report(report_type):
    """Export report data to Excel"""
    try:
        db = get_db()

        # Get filter parameters
        date_from = request.args.get('date_from', (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
        date_to = request.args.get('date_to', datetime.now().strftime('%Y-%m-%d'))
        rider_filter = request.args.get('rider', '')

        # Generate report data
        report_data = generate_report_data(db, report_type, date_from, date_to, rider_filter)

        # Create Excel file
        output = BytesIO()
        workbook = xlsxwriter.Workbook(output)
        worksheet = workbook.add_worksheet(f'{report_type.title()} Report')

        # Write headers and data based on report type
        write_excel_report(worksheet, workbook, report_type, report_data)

        workbook.close()
        output.seek(0)

        # Return Excel file
        filename = f'rider_{report_type}_report_{date_from}_to_{date_to}.xlsx'
        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )

    except Exception as e:
        print(f"Export error: {e}")
        flash(f'Error exporting report: {str(e)}', 'danger')
        return redirect(url_for('riders.reports'))

def get_overview_report(db, date_filter, rider_filter_clause):
    """Generate overview report data"""
    data = {}

    # Total deliveries and revenue
    stats = db.execute(f'''
        SELECT
            COUNT(*) as total_orders,
            COUNT(CASE WHEN o.status = 'Delivered' THEN 1 END) as delivered_orders,
            COALESCE(SUM(CASE WHEN o.status = 'Delivered' THEN o.order_amount END), 0) as total_revenue,
            COUNT(DISTINCT r.rider_id) as active_riders
        FROM orders o
        LEFT JOIN riders r ON o.rider_id = r.rider_id
        WHERE 1=1 {date_filter} {rider_filter_clause}
    ''').fetchone()

    data['summary'] = dict(stats)

    # Top performing riders
    top_riders_results = db.execute(f'''
        SELECT
            r.name,
            COUNT(*) as deliveries,
            COALESCE(SUM(o.order_amount), 0) as revenue,
            ROUND(AVG(CASE WHEN o.rating THEN o.rating ELSE 4.0 END), 2) as avg_rating
        FROM orders o
        JOIN riders r ON o.rider_id = r.rider_id
        WHERE o.status = 'Delivered' {date_filter} {rider_filter_clause}
        GROUP BY r.rider_id, r.name
        ORDER BY deliveries DESC
        LIMIT 10
    ''').fetchall()
    data['top_riders'] = [dict(row) for row in top_riders_results]

    return data

def get_performance_report(db, date_filter, rider_filter_clause):
    """Generate performance report data"""
    data = {}

    # Rider performance metrics
    data['rider_performance'] = db.execute(f'''
        SELECT
            r.name,
            r.phone,
            COUNT(*) as total_orders,
            COUNT(CASE WHEN o.status = 'Delivered' THEN 1 END) as delivered,
            COUNT(CASE WHEN o.status = 'Cancelled' THEN 1 END) as cancelled,
            ROUND(COUNT(CASE WHEN o.status = 'Delivered' THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate,
            COALESCE(SUM(CASE WHEN o.status = 'Delivered' THEN o.order_amount END), 0) as revenue,
            ROUND(AVG(CASE WHEN o.rating THEN o.rating ELSE 4.0 END), 2) as avg_rating
        FROM riders r
        LEFT JOIN orders o ON r.rider_id = o.rider_id {date_filter.replace('AND', 'AND o.order_date IS NOT NULL AND')}
        WHERE r.status = 'active' {rider_filter_clause}
        GROUP BY r.rider_id, r.name, r.phone
        ORDER BY success_rate DESC, delivered DESC
    ''').fetchall()

    return data

def get_delivery_report(db, date_filter, rider_filter_clause):
    """Generate delivery report data"""
    data = {}

    # Daily delivery trends
    daily_trends_results = db.execute(f'''
        SELECT
            DATE(o.delivery_date) as delivery_date,
            COUNT(*) as deliveries,
            COALESCE(SUM(o.order_amount), 0) as revenue
        FROM orders o
        JOIN riders r ON o.rider_id = r.rider_id
        WHERE o.status = 'Delivered' {date_filter.replace('o.order_date', 'o.delivery_date')} {rider_filter_clause}
        GROUP BY DATE(o.delivery_date)
        ORDER BY delivery_date DESC
    ''').fetchall()
    data['daily_trends'] = [dict(row) for row in daily_trends_results]

    # Delivery status breakdown
    data['status_breakdown'] = db.execute(f'''
        SELECT
            o.status,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM orders WHERE 1=1 {date_filter}), 2) as percentage
        FROM orders o
        LEFT JOIN riders r ON o.rider_id = r.rider_id
        WHERE 1=1 {date_filter} {rider_filter_clause}
        GROUP BY o.status
        ORDER BY count DESC
    ''').fetchall()

    return data

def write_excel_report(worksheet, workbook, report_type, report_data):
    """Write report data to Excel worksheet"""
    # Define formats
    header_format = workbook.add_format({'bold': True, 'bg_color': '#4472C4', 'font_color': 'white'})

    row = 0

    if report_type == 'overview':
        # Write summary
        worksheet.write(row, 0, 'Summary Statistics', header_format)
        row += 1
        for key, value in report_data['summary'].items():
            worksheet.write(row, 0, key.replace('_', ' ').title())
            worksheet.write(row, 1, value)
            row += 1

        row += 2
        # Write top riders
        worksheet.write(row, 0, 'Top Performing Riders', header_format)
        row += 1
        headers = ['Name', 'Deliveries', 'Revenue', 'Avg Rating']
        for col, header in enumerate(headers):
            worksheet.write(row, col, header, header_format)
        row += 1

        for rider in report_data['top_riders']:
            worksheet.write(row, 0, rider['name'])
            worksheet.write(row, 1, rider['deliveries'])
            worksheet.write(row, 2, rider['revenue'])
            worksheet.write(row, 3, rider['avg_rating'])
            row += 1

@riders_bp.route('/old_reports')
@login_required
def old_reports():
    """Advanced Rider Reports with Filtering"""
    try:
        db = get_db()

        # Get filter parameters
        rider_filter = request.args.get('rider', '')
        date_from = request.args.get('date_from', '')
        date_to = request.args.get('date_to', '')
        customer_filter = request.args.get('customer', '')
        customer_type = request.args.get('customer_type', '')

        # Build base query
        base_query = '''
            SELECT
                r.rider_id,
                r.name as rider_name,
                r.phone as rider_phone,
                r.city as rider_city,
                o.order_id,
                o.customer_name,
                o.customer_phone,
                o.order_date,
                o.delivery_date,
                o.order_amount,
                o.status,
                o.payment_status,
                CASE
                    WHEN o.customer_name LIKE '%Distributor%' OR o.customer_name LIKE '%Distribution%' THEN 'Distributor'
                    WHEN o.customer_name LIKE '%Pharmacy%' OR o.customer_name LIKE '%Medical%' THEN 'Pharmacy'
                    WHEN o.customer_name LIKE '%Institute%' OR o.customer_name LIKE '%Hospital%' OR o.customer_name LIKE '%Clinic%' THEN 'Institute'
                    WHEN o.customer_name LIKE '%Dr.%' OR o.customer_name LIKE '%Doctor%' THEN 'Doctor'
                    ELSE 'Walking Customer'
                END as customer_type_derived
            FROM riders r
            LEFT JOIN orders o ON r.rider_id = o.rider_id
            WHERE 1=1
        '''

        params = []

        # Apply filters
        if rider_filter:
            base_query += ' AND r.rider_id = ?'
            params.append(rider_filter)

        if date_from:
            base_query += ' AND DATE(o.order_date) >= ?'
            params.append(date_from)

        if date_to:
            base_query += ' AND DATE(o.order_date) <= ?'
            params.append(date_to)

        if customer_filter:
            base_query += ' AND o.customer_name LIKE ?'
            params.append(f'%{customer_filter}%')

        if customer_type:
            if customer_type == 'distributor':
                base_query += " AND (o.customer_name LIKE '%Distributor%' OR o.customer_name LIKE '%Distribution%')"
            elif customer_type == 'pharmacy':
                base_query += " AND (o.customer_name LIKE '%Pharmacy%' OR o.customer_name LIKE '%Medical%')"
            elif customer_type == 'institute':
                base_query += " AND (o.customer_name LIKE '%Institute%' OR o.customer_name LIKE '%Hospital%' OR o.customer_name LIKE '%Clinic%')"
            elif customer_type == 'doctor':
                base_query += " AND (o.customer_name LIKE '%Dr.%' OR o.customer_name LIKE '%Doctor%')"
            elif customer_type == 'walking':
                base_query += " AND NOT (o.customer_name LIKE '%Distributor%' OR o.customer_name LIKE '%Distribution%' OR o.customer_name LIKE '%Pharmacy%' OR o.customer_name LIKE '%Medical%' OR o.customer_name LIKE '%Institute%' OR o.customer_name LIKE '%Hospital%' OR o.customer_name LIKE '%Clinic%' OR o.customer_name LIKE '%Dr.%' OR o.customer_name LIKE '%Doctor%')"

        base_query += ' ORDER BY o.order_date DESC, r.name'

        # Execute query
        report_data = db.execute(base_query, params).fetchall()

        # Get all riders for filter dropdown
        all_riders = db.execute('SELECT rider_id, name FROM riders ORDER BY name').fetchall()

        # Calculate summary statistics
        total_orders = len([r for r in report_data if r['order_id']])
        total_revenue = sum(float(r['order_amount'] or 0) for r in report_data if r['order_id'])
        unique_customers = len(set(r['customer_name'] for r in report_data if r['customer_name']))

        # Group by customer type
        customer_type_stats = {}
        for row in report_data:
            if row['order_id']:  # Only count actual orders
                ctype = row['customer_type_derived']
                if ctype not in customer_type_stats:
                    customer_type_stats[ctype] = {'count': 0, 'revenue': 0}
                customer_type_stats[ctype]['count'] += 1
                customer_type_stats[ctype]['revenue'] += float(row['order_amount'] or 0)

        return render_template('riders/reports.html',
                             report_data=report_data,
                             all_riders=all_riders,
                             total_orders=total_orders,
                             total_revenue=total_revenue,
                             unique_customers=unique_customers,
                             customer_type_stats=customer_type_stats,
                             filters={
                                 'rider': rider_filter,
                                 'date_from': date_from,
                                 'date_to': date_to,
                                 'customer': customer_filter,
                                 'customer_type': customer_type
                             },
                             current_user=current_user)

    except Exception as e:
        print(f"Rider reports error: {e}")
        flash(f'Error loading rider reports: {str(e)}', 'danger')
        return redirect(url_for('riders.dashboard'))

@riders_bp.route('/reports/export')
@login_required
def export_reports():
    """Export rider reports to Excel"""
    try:
        # Get same data as reports route
        db = get_db()

        # Get filter parameters
        rider_filter = request.args.get('rider', '')
        date_from = request.args.get('date_from', '')
        date_to = request.args.get('date_to', '')
        customer_filter = request.args.get('customer', '')
        customer_type = request.args.get('customer_type', '')

        # Use same query logic as reports route
        base_query = '''
            SELECT
                r.rider_id,
                r.name as rider_name,
                r.phone as rider_phone,
                r.city as rider_city,
                o.order_id,
                o.customer_name,
                o.customer_phone,
                o.order_date,
                o.delivery_date,
                o.order_amount,
                o.status,
                o.payment_status
            FROM riders r
            LEFT JOIN orders o ON r.rider_id = o.rider_id
            WHERE o.order_id IS NOT NULL
        '''

        params = []

        # Apply same filters
        if rider_filter:
            base_query += ' AND r.rider_id = ?'
            params.append(rider_filter)

        if date_from:
            base_query += ' AND DATE(o.order_date) >= ?'
            params.append(date_from)

        if date_to:
            base_query += ' AND DATE(o.order_date) <= ?'
            params.append(date_to)

        if customer_filter:
            base_query += ' AND o.customer_name LIKE ?'
            params.append(f'%{customer_filter}%')

        base_query += ' ORDER BY o.order_date DESC, r.name'

        report_data = db.execute(base_query, params).fetchall()

        # Create Excel file
        output = BytesIO()
        workbook = xlsxwriter.Workbook(output)
        worksheet = workbook.add_worksheet('Rider Reports')

        # Headers
        headers = ['Rider ID', 'Rider Name', 'Phone', 'City', 'Order ID', 'Customer', 'Customer Phone',
                  'Order Date', 'Delivery Date', 'Amount', 'Status', 'Payment Status']

        for col, header in enumerate(headers):
            worksheet.write(0, col, header)

        # Data
        for row, data in enumerate(report_data, 1):
            worksheet.write(row, 0, data['rider_id'])
            worksheet.write(row, 1, data['rider_name'])
            worksheet.write(row, 2, data['rider_phone'])
            worksheet.write(row, 3, data['rider_city'])
            worksheet.write(row, 4, data['order_id'])
            worksheet.write(row, 5, data['customer_name'])
            worksheet.write(row, 6, data['customer_phone'])
            worksheet.write(row, 7, data['order_date'])
            worksheet.write(row, 8, data['delivery_date'])
            worksheet.write(row, 9, data['order_amount'])
            worksheet.write(row, 10, data['status'])
            worksheet.write(row, 11, data['payment_status'])

        workbook.close()
        output.seek(0)

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'rider_reports_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        )

    except Exception as e:
        flash(f'Error exporting reports: {str(e)}', 'danger')
        return redirect(url_for('riders.reports'))

# Error handlers for the blueprint

@riders_bp.errorhandler(404)
def not_found_error(error):
    return render_template('errors/404.html'), 404

@riders_bp.errorhandler(500)
def internal_error(error):
    return render_template('errors/500.html'), 500
