#!/usr/bin/env python3
"""
Notification Routes Blueprint
API endpoints for the advanced notification system
"""

from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for
from flask_login import login_required, current_user
from datetime import datetime
import json

from notification_system import NotificationManager, NotificationType, NotificationPriority

notifications_bp = Blueprint('notifications', __name__, url_prefix='/notifications')
notification_manager = NotificationManager()

@notifications_bp.route('/')
@login_required
def notification_center():
    """Notification center page"""
    
    # Get filter parameters
    filter_type = request.args.get('type', 'all')
    filter_status = request.args.get('status', 'all')
    page = int(request.args.get('page', 1))
    per_page = 20
    
    # Get user notifications
    unread_only = filter_status == 'unread'
    notification_type = filter_type if filter_type != 'all' else None
    
    notifications = notification_manager.get_user_notifications(
        user_id=current_user.id,
        limit=per_page * page,
        unread_only=unread_only,
        notification_type=notification_type
    )
    
    # Get notification statistics
    stats = notification_manager.get_notification_stats(user_id=current_user.id)
    
    # Get notification types for filter dropdown
    notification_types = [
        {'value': 'all', 'label': 'All Notifications'},
        {'value': 'order_placed', 'label': 'Orders Placed'},
        {'value': 'order_approved', 'label': 'Orders Approved'},
        {'value': 'order_dispatched', 'label': 'Orders Dispatched'},
        {'value': 'order_delivered', 'label': 'Orders Delivered'},
        {'value': 'inventory_low', 'label': 'Low Inventory'},
        {'value': 'user_action', 'label': 'User Actions'},
        {'value': 'system_alert', 'label': 'System Alerts'},
        {'value': 'payment_received', 'label': 'Payments'},
        {'value': 'rider_assigned', 'label': 'Rider Assignments'},
        {'value': 'delivery_delayed', 'label': 'Delivery Delays'}
    ]
    
    return render_template('notifications/notification_center.html',
                         notifications=notifications,
                         stats=stats,
                         notification_types=notification_types,
                         current_filter_type=filter_type,
                         current_filter_status=filter_status,
                         page=page)

@notifications_bp.route('/api/notifications')
@login_required
def api_get_notifications():
    """API endpoint to get notifications"""
    
    limit = int(request.args.get('limit', 20))
    unread_only = request.args.get('unread_only', 'false').lower() == 'true'
    notification_type = request.args.get('type')
    
    notifications = notification_manager.get_user_notifications(
        user_id=current_user.id,
        limit=limit,
        unread_only=unread_only,
        notification_type=notification_type
    )
    
    return jsonify({
        'success': True,
        'notifications': notifications,
        'count': len(notifications)
    })

@notifications_bp.route('/api/unread-count')
@login_required
def api_unread_count():
    """API endpoint to get unread notification count"""
    
    count = notification_manager.get_unread_count(current_user.id)
    
    return jsonify({
        'success': True,
        'unread_count': count
    })

@notifications_bp.route('/api/mark-read/<int:notification_id>', methods=['POST'])
@login_required
def api_mark_read(notification_id):
    """API endpoint to mark notification as read"""
    
    success = notification_manager.mark_as_read(notification_id, current_user.id)
    
    return jsonify({
        'success': success,
        'message': 'Notification marked as read' if success else 'Failed to mark as read'
    })

@notifications_bp.route('/api/mark-all-read', methods=['POST'])
@login_required
def api_mark_all_read():
    """API endpoint to mark all notifications as read"""
    
    count = notification_manager.mark_all_as_read(current_user.id)
    
    return jsonify({
        'success': True,
        'marked_count': count,
        'message': f'Marked {count} notifications as read'
    })

@notifications_bp.route('/api/delete/<int:notification_id>', methods=['DELETE'])
@login_required
def api_delete_notification(notification_id):
    """API endpoint to delete notification"""
    
    success = notification_manager.delete_notification(notification_id, current_user.id)
    
    return jsonify({
        'success': success,
        'message': 'Notification deleted' if success else 'Failed to delete notification'
    })

@notifications_bp.route('/api/archive/<int:notification_id>', methods=['POST'])
@login_required
def api_archive_notification(notification_id):
    """API endpoint to archive notification"""
    
    success = notification_manager.archive_notification(notification_id, current_user.id)
    
    return jsonify({
        'success': success,
        'message': 'Notification archived' if success else 'Failed to archive notification'
    })

@notifications_bp.route('/api/create', methods=['POST'])
@login_required
def api_create_notification():
    """API endpoint to create notification (for testing)"""
    
    data = request.get_json()
    
    if not data or not all(k in data for k in ['title', 'message', 'type']):
        return jsonify({
            'success': False,
            'message': 'Missing required fields: title, message, type'
        }), 400
    
    try:
        priority_map = {
            'low': NotificationPriority.LOW,
            'medium': NotificationPriority.MEDIUM,
            'high': NotificationPriority.HIGH,
            'urgent': NotificationPriority.URGENT
        }
        
        priority = priority_map.get(data.get('priority', 'low'), NotificationPriority.LOW)
        
        notification_id = notification_manager.create_notification(
            user_id=current_user.id,
            notification_type=data['type'],
            title=data['title'],
            message=data['message'],
            data=data.get('data', {}),
            priority=priority
        )
        
        return jsonify({
            'success': True,
            'notification_id': notification_id,
            'message': 'Notification created successfully'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to create notification: {str(e)}'
        }), 500

@notifications_bp.route('/api/stats')
@login_required
def api_notification_stats():
    """API endpoint to get notification statistics"""
    
    days = int(request.args.get('days', 30))
    stats = notification_manager.get_notification_stats(user_id=current_user.id, days=days)
    
    return jsonify({
        'success': True,
        'stats': stats
    })

@notifications_bp.route('/test')
@login_required
def test_notifications():
    """Test page for creating sample notifications"""
    
    return render_template('notifications/test_notifications.html')

@notifications_bp.route('/api/create-test-notifications', methods=['POST'])
@login_required
def api_create_test_notifications():
    """Create test notifications for demonstration"""
    
    test_notifications = [
        {
            'type': 'order_placed',
            'title': 'New Order #ORD-2025-001',
            'message': 'Order placed by Ahmed Khan for Rs. 15,500',
            'priority': 'medium',
            'data': {'order_id': 'ORD-2025-001', 'customer_name': 'Ahmed Khan', 'amount': 15500}
        },
        {
            'type': 'inventory_low',
            'title': 'Low Stock Alert',
            'message': 'Panadol 500mg is running low (Stock: 8)',
            'priority': 'high',
            'data': {'product_name': 'Panadol 500mg', 'current_stock': 8}
        },
        {
            'type': 'order_approved',
            'title': 'Order Approved #ORD-2025-002',
            'message': 'Order #ORD-2025-002 has been approved and is ready for processing',
            'priority': 'medium',
            'data': {'order_id': 'ORD-2025-002'}
        },
        {
            'type': 'rider_assigned',
            'title': 'Rider Assigned',
            'message': 'Muhammad Ali has been assigned to order #ORD-2025-001',
            'priority': 'low',
            'data': {'order_id': 'ORD-2025-001', 'rider_name': 'Muhammad Ali'}
        },
        {
            'type': 'system_alert',
            'title': 'System Maintenance',
            'message': 'Scheduled maintenance will begin at 2:00 AM tonight',
            'priority': 'urgent',
            'data': {'maintenance_time': '2:00 AM'}
        }
    ]
    
    created_count = 0
    
    for notification_data in test_notifications:
        try:
            priority_map = {
                'low': NotificationPriority.LOW,
                'medium': NotificationPriority.MEDIUM,
                'high': NotificationPriority.HIGH,
                'urgent': NotificationPriority.URGENT
            }
            
            priority = priority_map.get(notification_data.get('priority', 'low'), NotificationPriority.LOW)
            
            notification_manager.create_notification(
                user_id=current_user.id,
                notification_type=notification_data['type'],
                title=notification_data['title'],
                message=notification_data['message'],
                data=notification_data.get('data', {}),
                priority=priority
            )
            
            created_count += 1
            
        except Exception as e:
            print(f"Failed to create test notification: {e}")
    
    return jsonify({
        'success': True,
        'created_count': created_count,
        'message': f'Created {created_count} test notifications'
    })

# Helper function to create notifications from other parts of the application
def create_notification_helper(user_id, notification_type, title, message, data=None, priority='medium'):
    """Helper function to create notifications from other routes"""
    
    priority_map = {
        'low': NotificationPriority.LOW,
        'medium': NotificationPriority.MEDIUM,
        'high': NotificationPriority.HIGH,
        'urgent': NotificationPriority.URGENT
    }
    
    priority_enum = priority_map.get(priority, NotificationPriority.MEDIUM)
    
    return notification_manager.create_notification(
        user_id=user_id,
        notification_type=notification_type,
        title=title,
        message=message,
        data=data or {},
        priority=priority_enum
    )
