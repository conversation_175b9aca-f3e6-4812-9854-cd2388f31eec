"""
Order management routes for Medivent Pharmaceuticals Web Portal
"""

from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app, g
from flask_login import login_required, current_user
from datetime import datetime, timedelta
import os
import uuid
import sqlite3
import time
from werkzeug.utils import secure_filename
import json
# Removed SQLAlchemy import - using raw SQLite queries only

# Import PDF generation utilities
from utils.invoice_generator import generate_pdf_invoice
from source_medivent_challan_generator import generate_pdf_challan

# Import validation utilities
from utils.inventory_validator import get_inventory_validator, get_products_with_inventory_for_forms
from utils.product_validator import get_product_validator
from utils.division_validator import get_division_validator

orders_bp = Blueprint('orders', __name__, url_prefix='/orders')

# Import centralized database function
from utils.db import get_db

def generate_order_id():
    """Generate unique order ID using database auto-increment sequence"""
    try:
        db = get_db()

        # Create sequence table if not exists
        db.execute('''
            CREATE TABLE IF NOT EXISTS order_sequence (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Insert and get auto-increment ID
        cursor = db.execute('INSERT INTO order_sequence DEFAULT VALUES')
        sequence_id = cursor.lastrowid

        # Generate order ID with zero-padded sequence
        order_id = f"ORD{sequence_id:08d}"

        db.commit()
        return order_id

    except Exception as e:
        print(f"Error in generate_order_id: {e}")
        # Fallback to UUID-based generation
        import uuid
        uuid_str = str(uuid.uuid4()).replace('-', '').upper()[:12]
        return f"ORD{uuid_str}"

def generate_order_item_id():
    """Generate unique order item ID"""
    timestamp = str(int(time.time()))
    random_part = uuid.uuid4().hex[:6].upper()
    return f"OI{timestamp}{random_part}"

# Order status constants
ORDER_STATUSES = [
    "Placed",              # Initial status when order is placed
    "Approved",            # Order has been approved (skipping Pending Approval)
    "Rejected",            # Order has been rejected by approver
    "Processing",          # Order is being processed
    "Finance Pending",     # DC generated, waiting for invoice generation
    "Ready for Pickup",    # Order is ready for rider pickup
    "Dispatched",          # Order has been dispatched with rider
    "Delivered",           # Order has been delivered to customer
    "Cancelled",           # Order has been cancelled
    "Pending"              # Products not available but order is approved
]

def log_order_activity(order_id, action, details, username):
    """Log order activity to the database"""
    try:
        db = get_db()
        db.execute('''
            INSERT INTO activity_logs (username, action, entity_id, details, module, timestamp)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (username, action, order_id, details, "orders", datetime.now().isoformat()))
        db.commit()
    except Exception as e:
        # If activity_logs table doesn't exist, just skip logging
        print(f"Error logging activity: {e}")
        pass

def generate_invoice_number():
    """Generate a sequential invoice number"""
    db = get_db()
    last_invoice = db.execute('SELECT invoice_number FROM invoices ORDER BY id DESC LIMIT 1').fetchone()
    if last_invoice:
        try:
            # Extract number part after 'INV'
            num = int(last_invoice['invoice_number'][3:])
            next_num = num + 1
        except ValueError:
            next_num = 1
    else:
        next_num = 1

    # Format with leading zeros (e.g., INV00001)
    return f"INV{next_num:05d}"

def generate_dc_number():
    """Generate a sequential delivery challan number"""
    db = get_db()
    last_challan = db.execute('SELECT dc_number FROM challans ORDER BY id DESC LIMIT 1').fetchone()
    if last_challan:
        try:
            # Extract number part after 'DC-'
            num = int(last_challan['dc_number'][3:])
            next_num = num + 1
        except ValueError:
            next_num = 1
    else:
        next_num = 1

    # Format with leading zeros (e.g., DC-001)
    return f"DC-{next_num:03d}"

@orders_bp.route('/')
@login_required
def index():
    """Order management main page"""
    db = get_db()
    orders = db.execute('SELECT * FROM orders ORDER BY order_date DESC').fetchall()
    return render_template('orders/index.html', orders=orders, statuses=ORDER_STATUSES)

@orders_bp.route('/new', methods=['GET', 'POST'])
@login_required
def new_order():
    """Create a new order"""
    if request.method == 'POST':
        try:
            db = get_db()

            # Get form data
            customer_name = request.form.get('customer_name', '').title()
            customer_address = request.form.get('customer_address', '').title()
            customer_phone = request.form.get('customer_phone', '')
            payment_method = request.form.get('payment_mode', '').lower()
            delivery_date = request.form.get('delivery_date', '')  # Capture Expected Delivery Date

            # Generate unique order ID (auto-increment ensures uniqueness)
            order_id = generate_order_id()

            if not order_id:
                flash('Error generating order ID. Please try again.', 'danger')
                return redirect(url_for('orders.new_order'))

            # Begin transaction with proper isolation
            db.execute('BEGIN IMMEDIATE TRANSACTION')  # Use IMMEDIATE for better concurrency control

            # Insert order with explicit error handling for UNIQUE constraint
            try:
                db.execute('''
                    INSERT INTO orders (
                        order_id, customer_name, customer_address, customer_phone,
                        payment_method, status, sales_agent, updated_by, order_date, last_updated,
                        estimated_delivery_date
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    order_id, customer_name, customer_address, customer_phone,
                    payment_method, ORDER_STATUSES[0], current_user.username,
                    current_user.username, datetime.now(), datetime.now(),
                    delivery_date if delivery_date else None
                ))
            except sqlite3.IntegrityError as e:
                db.execute('ROLLBACK')
                if 'UNIQUE constraint failed: orders.order_id' in str(e):
                    flash('Order ID conflict detected. Please try placing the order again.', 'warning')
                else:
                    flash(f'Database integrity error: {str(e)}', 'danger')
                return redirect(url_for('orders.new_order'))

            # Process order items with real-time inventory deduction
            product_ids = request.form.getlist('product_id[]')
            quantities = request.form.getlist('quantity[]')
            foc_quantities = request.form.getlist('foc_quantity[]')

            total_amount = 0
            inventory_validator = get_inventory_validator(db)
            product_validator = get_product_validator(db)

            for i in range(len(product_ids)):
                if i >= len(quantities):
                    continue

                product_id = product_ids[i]
                try:
                    quantity = int(quantities[i])
                    foc_quantity = int(foc_quantities[i]) if i < len(foc_quantities) else 0
                except (ValueError, IndexError):
                    continue

                # Validate product exists and has valid division
                is_valid, product_info = product_validator.validate_product_exists(product_id)
                if not is_valid or not product_info.get('division_valid', False):
                    flash(f'Product {product_id} is not valid for order placement', 'danger')
                    raise Exception(f'Invalid product: {product_id}')

                # Get product details
                product = db.execute('SELECT * FROM products WHERE product_id = ?', (product_id,)).fetchone()
                if not product:
                    continue

                # Validate and execute real-time inventory deduction (within existing transaction)
                success, message = inventory_validator.execute_stock_deduction(
                    product_id, quantity, foc_quantity, order_id, current_user.username, use_transaction=False
                )

                if not success:
                    flash(f'Inventory deduction failed for {product["name"]}: {message}', 'danger')
                    raise Exception(f'Inventory deduction failed: {message}')

                # Calculate line total (only for paid quantity, not FOC)
                unit_price = product['unit_price'] or 0.0
                line_total = unit_price * quantity
                total_amount += line_total

                # Generate unique order item ID
                order_item_id = generate_order_item_id()

                # Insert order item with FOC quantity
                db.execute('''
                    INSERT INTO order_items (
                        order_item_id, order_id, product_id, product_name, strength,
                        quantity, foc_quantity, unit_price, line_total, status
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    order_item_id, order_id, product_id, product['name'],
                    product['strength'], quantity, foc_quantity, unit_price, line_total, ORDER_STATUSES[0]
                ))

            # Update order total
            db.execute('UPDATE orders SET order_amount = ? WHERE order_id = ?', (total_amount, order_id))

            # Commit transaction
            db.execute('COMMIT')

            # Create notification for new order
            try:
                from app import create_notification
                create_notification(
                    title=f'New Order Placed - {order_id}',
                    message=f'Order {order_id} has been placed by {current_user.username} for customer {customer_name}. Total amount: Rs.{total_amount:,.2f}',
                    type_='order',
                    entity_type='order',
                    entity_id=order_id,
                    action_url=f'/orders/{order_id}',
                    user_id=None  # Broadcast to all users
                )
            except Exception as e:
                print(f"Error creating notification: {e}")

            flash(f'Order {order_id} placed successfully with real-time inventory deduction.', 'success')
            return redirect(url_for('orders.view_order', order_id=order_id))

        except Exception as e:
            # Rollback on error
            try:
                db.execute('ROLLBACK')
            except:
                pass
            flash(f'Error creating order: {str(e)}', 'danger')
            return redirect(url_for('orders.new_order'))

    # GET request - show order form
    db = get_db()

    # Get products with available inventory using real-time service
    try:
        from utils.product_realtime_service import get_products_with_inventory_realtime
        products = get_products_with_inventory_realtime(db)

        if not products:
            flash('No products with available inventory found. Please add inventory first.', 'warning')
            return redirect(url_for('inventory.index'))
    except Exception as e:
        flash(f'Error loading products: {str(e)}', 'danger')
        # Fallback to product validator
        try:
            product_validator = get_product_validator(db)
            products = product_validator.get_products_for_order_placement()
        except:
            products = []

    # Get divisions for filtering using unified manager
    try:
        from utils.unified_division_manager import get_unified_division_manager
        unified_manager = get_unified_division_manager(db)
        divisions = unified_manager.get_active_divisions()
    except Exception as e:
        divisions = []

    return render_template('orders/new.html',
                         products=products,
                         divisions=divisions)

@orders_bp.route('/<order_id>')
@login_required
def view_order(order_id):
    """View order details with comprehensive history and comments"""
    db = get_db()

    # Get order details
    order = db.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
    if not order:
        flash(f'Order {order_id} not found.', 'danger')
        return redirect(url_for('orders.index'))

    # Get order items
    order_items = db.execute('''
        SELECT oi.*, p.name as product_name
        FROM order_items oi
        LEFT JOIN products p ON oi.product_id = p.product_id
        WHERE oi.order_id = ?
    ''', (order_id,)).fetchall()

    # Get activity logs for this order (if table exists)
    try:
        logs = db.execute('''
            SELECT * FROM activity_logs
            WHERE entity_id = ?
            ORDER BY timestamp DESC
        ''', (order_id,)).fetchall()
    except:
        logs = []

    # Get hold/release history
    try:
        hold_history = db.execute('''
            SELECT
                hold_id, hold_reason, hold_comments, hold_date, hold_by,
                release_comments, release_date, release_by, status, priority_level
            FROM invoice_holds
            WHERE order_id = ?
            ORDER BY hold_date DESC
        ''', (order_id,)).fetchall()
    except:
        hold_history = []

    # Get finance comments
    try:
        finance_comments = db.execute('''
            SELECT
                comment_id, comment_text, comment_type, created_by, created_at, is_internal
            FROM finance_comments
            WHERE entity_id = ? AND entity_type = 'order'
            ORDER BY created_at DESC
        ''', (order_id,)).fetchall()
    except:
        finance_comments = []

    # Get all workflow comments (hold, release, payment, etc.)
    try:
        workflow_comments = db.execute('''
            SELECT
                comment_id, comment_text, comment_type, created_by, created_at, entity_type
            FROM finance_comments
            WHERE entity_id = ?
            ORDER BY created_at DESC
        ''', (order_id,)).fetchall()
    except:
        workflow_comments = []

    # Get invoice and challan if they exist
    try:
        invoice = db.execute('SELECT * FROM invoices WHERE order_id = ?', (order_id,)).fetchone()
    except:
        invoice = None

    try:
        challan = db.execute('SELECT * FROM delivery_challans WHERE order_id = ?', (order_id,)).fetchone()
    except:
        challan = None

    # Get payment history
    try:
        payments = db.execute('''
            SELECT * FROM payments_enhanced
            WHERE order_id = ?
            ORDER BY payment_date DESC
        ''', (order_id,)).fetchall()
    except:
        payments = []

    return render_template('orders/view.html',
                          order=order,
                          order_items=order_items,
                          logs=logs,
                          hold_history=hold_history,
                          finance_comments=finance_comments,
                          workflow_comments=workflow_comments,
                          invoice=invoice,
                          challan=challan,
                          payments=payments,
                          statuses=ORDER_STATUSES)

@orders_bp.route('/<order_id>/update', methods=['GET', 'POST'])
@login_required
def update_order(order_id):
    """Update an existing order"""
    db = get_db()

    # Get order details
    order = db.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
    if not order:
        flash(f'Order {order_id} not found', 'danger')
        return redirect(url_for('orders.index'))

    if request.method == 'POST':
        # Update order details
        customer_name = request.form.get('customer_name', '').title()
        customer_address = request.form.get('customer_address', '').title()
        customer_phone = request.form.get('customer_phone', '')
        payment_method = request.form.get('payment_mode', '').lower()
        notes = request.form.get('notes', '')

        # Handle status changes
        old_status = order['status']
        new_status = request.form.get('status')

        # Special handling for rejected orders - automatically resubmit as "Placed"
        if old_status == 'Rejected':
            new_status = 'Placed'
            # Clear rejection fields when resubmitting
            rejection_date = None
            rejected_by = None
            rejection_notes = None
        else:
            rejection_date = order['rejection_date'] if 'rejection_date' in order.keys() else None
            rejected_by = order['rejected_by'] if 'rejected_by' in order.keys() else None
            rejection_notes = order['rejection_notes'] if 'rejection_notes' in order.keys() else None

        if new_status and new_status in ORDER_STATUSES:
            # Update specific date fields based on status
            approval_date = order['approval_date']
            approved_by = order['approved_by']
            dispatch_date = order['dispatch_date']

            if new_status == "Approved" and old_status != "Approved":
                approval_date = datetime.utcnow().isoformat()
                approved_by = current_user.username
            elif new_status == "Dispatched" and old_status != "Dispatched":
                dispatch_date = datetime.utcnow().isoformat()
        else:
            new_status = order['status']
            approval_date = order['approval_date']
            approved_by = order['approved_by']
            dispatch_date = order['dispatch_date']

        try:
            # Update order in database
            db.execute('''
                UPDATE orders
                SET customer_name = ?, customer_address = ?, customer_phone = ?,
                    payment_method = ?, notes = ?, status = ?,
                    approval_date = ?, approved_by = ?, dispatch_date = ?,
                    rejection_date = ?, rejected_by = ?, rejection_notes = ?,
                    updated_by = ?
                WHERE order_id = ?
            ''', (customer_name, customer_address, customer_phone, payment_method,
                  notes, new_status, approval_date, approved_by, dispatch_date,
                  rejection_date, rejected_by, rejection_notes,
                  current_user.username, order_id))

            # Log the activity
            activity_message = f"Order details updated by {current_user.username}"
            if old_status == 'Rejected' and new_status == 'Placed':
                activity_message = f"Rejected order resubmitted for approval by {current_user.username}"

            db.execute('''
                INSERT INTO activity_logs (user_id, action, details, timestamp)
                VALUES (?, ?, ?, ?)
            ''', (current_user.id, f"Updated order {order_id}", activity_message,
                  datetime.utcnow().isoformat()))

            db.commit()

            # Create notification for order resubmission
            if old_status == 'Rejected' and new_status == 'Placed':
                try:
                    from app import create_notification
                    create_notification(
                        title=f'Order Resubmitted - {order_id}',
                        message=f'Previously rejected order {order_id} has been updated and resubmitted for approval by {current_user.username}.',
                        type_='order_placed',
                        entity_type='order',
                        entity_id=order_id,
                        action_url=f'/orders/{order_id}',
                        user_id=None  # Broadcast to all users
                    )
                except Exception as e:
                    print(f"Error creating resubmission notification: {e}")

                flash('Order updated and resubmitted for approval successfully!', 'success')
            else:
                flash('Order updated successfully!', 'success')

            return redirect(url_for('orders.view_order', order_id=order_id))

        except Exception as e:
            db.rollback()
            flash(f'Error updating order: {str(e)}', 'danger')
            return redirect(url_for('orders.view_order', order_id=order_id))

    # GET request - show update form
    products = db.execute('SELECT * FROM products ORDER BY name').fetchall()
    return render_template('orders/update.html',
                          order=order,
                          products=products,
                          statuses=ORDER_STATUSES)

# TEMPORARILY DISABLED - NEEDS CONVERSION TO RAW SQLITE
# @orders_bp.route('/workflow')
# @login_required
# def workflow():
#     """Order workflow management - DISABLED UNTIL CONVERTED TO RAW SQLITE"""
#     flash('Workflow functionality is temporarily disabled.', 'warning')
#     return redirect(url_for('orders.index'))

@orders_bp.route('/<order_id>/approve', methods=['POST'])
@login_required
def approve_order(order_id):
    """Approve an order"""
    db = get_db()

    # Get order details
    order = db.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
    if not order:
        flash(f'Order {order_id} not found', 'danger')
        return redirect(url_for('orders.index'))

    if order['status'] not in ["Placed", "Pending"]:
        flash('Only orders with status "Placed" or "Pending" can be approved.', 'warning')
        return redirect(url_for('orders.view_order', order_id=order_id))

    # Get approval notes from form
    approval_notes = request.form.get('approval_notes', '').strip()

    # Generate invoice number
    invoice_number = generate_invoice_number()

    # Calculate estimated delivery date (3 business days from approval)
    approval_datetime = datetime.now()
    estimated_delivery = approval_datetime + timedelta(days=3)

    # Update order status and set invoice number using proper SQL UPDATE
    try:
        db.execute('''
            UPDATE orders
            SET status = ?, invoice_number = ?, approval_date = ?, approved_by = ?,
                updated_by = ?, approval_notes = ?, last_updated = ?, estimated_delivery_date = ?
            WHERE order_id = ?
        ''', ("Approved", invoice_number, approval_datetime.isoformat(), current_user.username,
              current_user.username, approval_notes, approval_datetime.isoformat(),
              estimated_delivery.isoformat(), order_id))

        db.commit()
    except Exception as e:
        db.rollback()
        flash(f'Error approving order: {str(e)}', 'danger')
        return redirect(url_for('orders.view_order', order_id=order_id))

    # Get all order items using raw SQL
    order_items = db.execute('SELECT * FROM order_items WHERE order_id = ?', (order_id,)).fetchall()

    # Check inventory for each item and update status
    available_items = []
    unavailable_items = []

    for item in order_items:
        # Check if product is available in inventory using raw SQL
        inventory = db.execute('''
            SELECT * FROM inventory
            WHERE product_id = ? AND stock_quantity >= ?
        ''', (item['product_id'], item['quantity'])).fetchone()

        if inventory:
            # Update item status to approved using raw SQL
            db.execute('''
                UPDATE order_items SET status = ? WHERE order_item_id = ?
            ''', ("Approved", item['order_item_id']))
            available_items.append(item)
        else:
            # Update item status to pending using raw SQL
            db.execute('''
                UPDATE order_items SET status = ? WHERE order_item_id = ?
            ''', ("Pending", item['order_item_id']))
            unavailable_items.append(item)

    # Get tax rate from settings using raw SQL
    tax_rate_setting = db.execute('''
        SELECT value FROM settings WHERE setting_name = 'tax_rate'
    ''').fetchone()
    tax_rate = float(tax_rate_setting['value']) / 100 if tax_rate_setting else 0.17  # Default 17% if not found

    # Get updated order data for PDF generation
    updated_order = db.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()

    # Generate PDF invoice
    invoice_path = generate_pdf_invoice(updated_order, invoice_number, current_user.username, tax_rate)

    # Save invoice record using raw SQL
    db.execute('''
        INSERT INTO invoices (invoice_number, order_id, generated_by, pdf_path, date_generated)
        VALUES (?, ?, ?, ?, ?)
    ''', (invoice_number, order_id, current_user.username, invoice_path, datetime.now().isoformat()))

    # Generate delivery challan if there are available items
    if available_items:
        dc_number = generate_dc_number()

        # Prepare customer data for challan
        customer_data = {
            'name': updated_order['customer_name'],
            'phone': updated_order['customer_phone'] if 'customer_phone' in updated_order.keys() else '',
            'address': updated_order['customer_address'] if 'customer_address' in updated_order.keys() else ''
        }

        challan_path = generate_pdf_challan(updated_order, customer_data, available_items, dc_number)

        # Save challan record using raw SQL
        db.execute('''
            INSERT INTO challans (dc_number, invoice_number, order_id, customer_name, generated_by, pdf_path, date_generated)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (dc_number, invoice_number, order_id, updated_order['customer_name'], current_user.username, challan_path, datetime.now().isoformat()))

    # Commit all changes
    db.commit()

    # Log activity
    log_order_activity(
        order_id,
        "Order Approved",
        f"Order approved by {current_user.username}. Invoice: {invoice_number}",
        current_user.username
    )

    # Create notification for order approval
    try:
        from app import create_notification
        notification_message = f'Order {order_id} has been approved by {current_user.username}. Invoice #{invoice_number} generated.'
        if approval_notes:
            notification_message += f' Notes: {approval_notes}'

        create_notification(
            title=f'Order Approved - {order_id}',
            message=notification_message,
            type_='success',
            entity_type='order',
            entity_id=order_id,
            action_url=f'/orders/{order_id}',
            user_id=None  # Broadcast to all users
        )
    except Exception as e:
        print(f"Error creating approval notification: {e}")

    flash(f'Order approved successfully. Invoice #{invoice_number} generated.', 'success')
    return redirect(url_for('orders.view_order', order_id=order_id))

@orders_bp.route('/<order_id>/reject', methods=['POST'])
@login_required
def reject_order(order_id):
    """Reject an order with optional notes"""
    db = get_db()

    # Get order details
    order = db.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
    if not order:
        flash(f'Order {order_id} not found', 'danger')
        return redirect(url_for('orders.index'))

    if order['status'] not in ["Placed", "Pending"]:
        flash('Only orders with status "Placed" or "Pending" can be rejected.', 'warning')
        return redirect(url_for('orders.view_order', order_id=order_id))

    # Get rejection notes from form
    rejection_notes = request.form.get('rejection_notes', '').strip()

    try:
        # Update order status to rejected
        db.execute('''
            UPDATE orders
            SET status = ?, rejection_date = ?, rejected_by = ?, rejection_notes = ?, last_updated = ?
            WHERE order_id = ?
        ''', ("Rejected", datetime.now().isoformat(), current_user.username,
              rejection_notes, datetime.now().isoformat(), order_id))

        # Update all order items status to rejected
        db.execute('''
            UPDATE order_items
            SET status = ?
            WHERE order_id = ?
        ''', ("Rejected", order_id))

        db.commit()

        # Log activity
        log_order_activity(
            order_id,
            "Order Rejected",
            f"Order rejected by {current_user.username}. Notes: {rejection_notes}",
            current_user.username
        )

        # Create notification for order rejection - target the salesperson
        try:
            from app import create_notification

            # Get salesperson from order and find their user ID
            sales_agent = order['sales_agent']

            # Get the salesperson's user ID
            salesperson_user = db.execute(
                'SELECT id FROM users WHERE username = ?', (sales_agent,)
            ).fetchone()

            notification_message = f'Your order {order_id} has been rejected by {current_user.username}.'
            if rejection_notes:
                notification_message += f' Reason: {rejection_notes}'
            notification_message += ' You can edit and resubmit the order.'

            # Send targeted notification to salesperson
            if salesperson_user:
                create_notification(
                    title=f'Order Rejected - {order_id}',
                    message=notification_message,
                    type_='order_rejected',
                    entity_type='order',
                    entity_id=order_id,
                    action_url=f'/orders/{order_id}',
                    user_id=salesperson_user['id']
                )

            # Also send a general notification for managers/admins
            create_notification(
                title=f'Order Rejected - {order_id}',
                message=f'Order {order_id} has been rejected by {current_user.username}. Salesperson: {sales_agent}',
                type_='order_rejected',
                entity_type='order',
                entity_id=order_id,
                action_url=f'/orders/{order_id}',
                user_id=None  # Broadcast to all users
            )
        except Exception as e:
            print(f"Error creating rejection notification: {e}")

        flash('Order rejected successfully!', 'warning')
        return redirect(url_for('orders.view_order', order_id=order_id))

    except Exception as e:
        db.rollback()
        flash(f'Error rejecting order: {str(e)}', 'danger')
        return redirect(url_for('orders.view_order', order_id=order_id))

@orders_bp.route('/<order_id>/dispatch', methods=['POST'])
@login_required
def dispatch_order(order_id):
    """Dispatch an order"""
    db = get_db()

    # Get order details using raw SQL
    order = db.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
    if not order:
        flash(f'Order {order_id} not found', 'danger')
        return redirect(url_for('orders.index'))

    if order['status'] not in ["Approved", "Processing", "Ready for Pickup"]:
        flash('Only approved orders can be dispatched.', 'warning')
        return redirect(url_for('orders.view_order', order_id=order_id))

    # Assign rider if provided
    rider = request.form.get('rider')

    try:
        # Update order status using raw SQL
        db.execute('''
            UPDATE orders
            SET status = ?, dispatch_date = ?, updated_by = ?, rider_id = ?, last_updated = ?
            WHERE order_id = ?
        ''', ("Dispatched", datetime.now().isoformat(), current_user.username,
              rider, datetime.now().isoformat(), order_id))

        # Get approved order items using raw SQL
        order_items = db.execute('''
            SELECT * FROM order_items WHERE order_id = ? AND status = ?
        ''', (order_id, "Approved")).fetchall()

        # Update inventory for each item
        for item in order_items:
            # Get current inventory using raw SQL
            inventory = db.execute('''
                SELECT * FROM inventory WHERE product_id = ? AND stock_quantity >= ?
            ''', (item['product_id'], item['quantity'])).fetchone()

            if inventory:
                # Deduct from inventory using raw SQL
                db.execute('''
                    UPDATE inventory
                    SET stock_quantity = stock_quantity - ?, last_updated = ?, updated_by = ?
                    WHERE product_id = ?
                ''', (item['quantity'], datetime.now().isoformat(), current_user.username, item['product_id']))

                # Update item status to dispatched using raw SQL
                db.execute('''
                    UPDATE order_items SET status = ? WHERE order_item_id = ?
                ''', ("Dispatched", item['order_item_id']))

        # Commit all changes
        db.commit()

        flash('Order dispatched successfully!', 'success')

    except Exception as e:
        db.rollback()
        flash(f'Error dispatching order: {str(e)}', 'danger')
        return redirect(url_for('orders.view_order', order_id=order_id))

        # Log activity
        log_order_activity(
            order_id,
            "Order Dispatched",
            f"Order dispatched by {current_user.username}. Rider: {rider if rider else 'Not assigned'}",
            current_user.username
        )

        # Create notification for order dispatch
        try:
            from app import create_notification
            rider_info = f" Assigned to rider: {rider}" if rider else ""
            create_notification(
                title=f'Order Dispatched - {order_id}',
                message=f'Order {order_id} has been dispatched by {current_user.username}.{rider_info}',
                type_='info',
                entity_type='order',
                entity_id=order_id,
                action_url=f'/orders/{order_id}',
                user_id=None  # Broadcast to all users
            )
        except Exception as e:
            print(f"Error creating dispatch notification: {e}")

    flash('Order dispatched successfully.', 'success')
    return redirect(url_for('orders.view_order', order_id=order_id))

@orders_bp.route('/<order_id>/deliver', methods=['POST'])
@login_required
def deliver_order(order_id):
    """Mark an order as delivered"""
    db = get_db()

    # Get order details using raw SQL
    order = db.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
    if not order:
        flash(f'Order {order_id} not found', 'danger')
        return redirect(url_for('orders.index'))

    if order['status'] != "Dispatched":
        flash('Only dispatched orders can be marked as delivered.', 'warning')
        return redirect(url_for('orders.view_order', order_id=order_id))

    try:
        # Update order status using raw SQL
        db.execute('''
            UPDATE orders
            SET status = ?, delivery_date = ?, updated_by = ?, last_updated = ?
            WHERE order_id = ?
        ''', ("Delivered", datetime.now().isoformat(), current_user.username,
              datetime.now().isoformat(), order_id))

        # Update all dispatched items using raw SQL
        db.execute('''
            UPDATE order_items SET status = ? WHERE order_id = ? AND status = ?
        ''', ("Delivered", order_id, "Dispatched"))

        # Commit changes
        db.commit()

        flash('Order marked as delivered successfully!', 'success')

    except Exception as e:
        db.rollback()
        flash(f'Error marking order as delivered: {str(e)}', 'danger')
        return redirect(url_for('orders.view_order', order_id=order_id))

    # Log activity
    log_order_activity(
        order_id,
        "Order Delivered",
        f"Order marked as delivered by {current_user.username}",
        current_user.username
    )

    # Create notification for order delivery
    try:
        from app import create_notification
        create_notification(
            title=f'Order Delivered - {order_id}',
            message=f'Order {order_id} has been successfully delivered. Marked by {current_user.username}.',
            type_='success',
            entity_type='order',
            entity_id=order_id,
            action_url=f'/orders/{order_id}',
            user_id=None  # Broadcast to all users
        )
    except Exception as e:
        print(f"Error creating delivery notification: {e}")

    flash('Order marked as delivered successfully.', 'success')
    return redirect(url_for('orders.view_order', order_id=order_id))

@orders_bp.route('/<order_id>/cancel', methods=['POST'])
@login_required
def cancel_order(order_id):
    """Cancel an order"""
    db = get_db()

    # Get order details using raw SQL
    order = db.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
    if not order:
        flash(f'Order {order_id} not found', 'danger')
        return redirect(url_for('orders.index'))

    if order['status'] in ["Delivered", "Cancelled"]:
        flash('This order cannot be cancelled.', 'warning')
        return redirect(url_for('orders.view_order', order_id=order_id))

    try:
        # Update order status using raw SQL
        db.execute('''
            UPDATE orders
            SET status = ?, updated_by = ?, last_updated = ?
            WHERE order_id = ?
        ''', ("Cancelled", current_user.username, datetime.now().isoformat(), order_id))

        # Update all order items using raw SQL
        db.execute('''
            UPDATE order_items SET status = ? WHERE order_id = ?
        ''', ("Cancelled", order_id))

        # Commit changes
        db.commit()

        flash('Order cancelled successfully!', 'warning')

    except Exception as e:
        db.rollback()
        flash(f'Error cancelling order: {str(e)}', 'danger')
        return redirect(url_for('orders.view_order', order_id=order_id))

    # Log activity
    log_order_activity(
        order_id,
        "Order Cancelled",
        f"Order cancelled by {current_user.username}",
        current_user.username
    )

    flash('Order cancelled successfully.', 'success')
    return redirect(url_for('orders.view_order', order_id=order_id))

@orders_bp.route('/search')
@login_required
def search():
    """Search orders"""
    db = get_db()
    query = request.args.get('q', '').strip()

    if not query:
        return redirect(url_for('orders.index'))

    # Search by order ID, customer name, phone, or invoice number using raw SQL
    search_pattern = f'%{query}%'
    orders = db.execute('''
        SELECT * FROM orders
        WHERE order_id LIKE ? OR customer_name LIKE ? OR customer_phone LIKE ? OR invoice_number LIKE ?
        ORDER BY order_date DESC
    ''', (search_pattern, search_pattern, search_pattern, search_pattern)).fetchall()

    return render_template('orders/search_results.html', orders=orders, query=query, statuses=ORDER_STATUSES)



@orders_bp.route('/<order_id>/history')
@login_required
def view_history(order_id):
    """View order history/activity logs"""
    db = get_db()

    # Get order details using raw SQL
    order = db.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
    if not order:
        flash(f'Order {order_id} not found', 'danger')
        return redirect(url_for('orders.index'))

    # Get order items using the same pattern as view_order function
    order_items = db.execute('''
        SELECT oi.*, p.name as product_name_from_products
        FROM order_items oi
        LEFT JOIN products p ON oi.product_id = p.product_id
        WHERE oi.order_id = ?
        ORDER BY oi.product_name
    ''', (order_id,)).fetchall()

    # Get activity logs using raw SQL
    activity_logs = db.execute('''
        SELECT * FROM activity_logs
        WHERE entity_id = ?
        ORDER BY timestamp ASC
    ''', (order_id,)).fetchall()

    # Get customer details for order placed by information
    customer = db.execute('SELECT * FROM customers WHERE customer_id = ?', (order['customer_id'],)).fetchone() if order['customer_id'] else None

    # Check if delivery challan exists for this order
    try:
        challan = db.execute('SELECT * FROM delivery_challans WHERE order_id = ?', (order_id,)).fetchone()
    except:
        challan = None

    # Convert to list to ensure template compatibility
    order_items_list = list(order_items) if order_items else []

    return render_template('orders/history.html',
                         order=order,
                         order_items=order_items_list,
                         activity_logs=activity_logs,
                         customer=customer,
                         challan=challan)



@orders_bp.route('/<order_id>/invoice')
@login_required
def view_invoice(order_id):
    """View order invoice"""
    db = get_db()

    # Get invoice details using raw SQL
    invoice = db.execute('SELECT * FROM invoices WHERE order_id = ?', (order_id,)).fetchone()
    if not invoice:
        flash(f'Invoice for order {order_id} not found', 'danger')
        return redirect(url_for('orders.view_order', order_id=order_id))

    # If PDF exists, serve it
    if invoice['pdf_path'] and os.path.exists(invoice['pdf_path']):
        return redirect(url_for('static', filename=f'documents/invoices/{os.path.basename(invoice["pdf_path"])}'))

    # Otherwise, show invoice details
    order = db.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
    order_items = db.execute('SELECT * FROM order_items WHERE order_id = ?', (order_id,)).fetchall()

    return render_template('orders/invoice.html', order=order, order_items=order_items, invoice=invoice)

@orders_bp.route('/<order_id>/challan')
@login_required
def view_challan(order_id):
    """View delivery challan"""
    db = get_db()

    # Get challan details using raw SQL
    challan = db.execute('SELECT * FROM challans WHERE order_id = ?', (order_id,)).fetchone()
    if not challan:
        flash(f'Challan for order {order_id} not found', 'danger')
        return redirect(url_for('orders.view_order', order_id=order_id))

    # If PDF exists, serve it
    if challan['pdf_path'] and os.path.exists(challan['pdf_path']):
        return redirect(url_for('static', filename=f'documents/challans/{os.path.basename(challan["pdf_path"])}'))

    # Otherwise, show challan details
    order = db.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
    order_items = db.execute('SELECT * FROM order_items WHERE order_id = ?', (order_id,)).fetchall()

    return render_template('orders/challan.html', order=order, order_items=order_items, challan=challan)

@orders_bp.route('/<order_id>/print-address')
def print_address_label(order_id):
    """Print address label for order - No login required for printing"""
    db = get_db()

    # Get order details
    order = db.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
    if not order:
        return render_template('404.html'), 404

    # Render the address label template
    return render_template('warehouse/address_label.html', order=order)

@orders_bp.route('/<order_id>/details')
@login_required
def view_order_details(order_id):
    """View order details page (HTML version)"""
    db = get_db()

    # Get order details
    order = db.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
    if not order:
        flash(f'Order {order_id} not found.', 'danger')
        return redirect(url_for('orders.index'))

    # Get order items
    order_items = db.execute('''
        SELECT oi.*, p.name as product_name
        FROM order_items oi
        LEFT JOIN products p ON oi.product_id = p.product_id
        WHERE oi.order_id = ?
    ''', (order_id,)).fetchall()

    # Get order logs/history
    try:
        logs = db.execute('''
            SELECT * FROM order_logs
            WHERE order_id = ?
            ORDER BY timestamp DESC
        ''', (order_id,)).fetchall()
    except:
        logs = []

    # Get invoice if exists
    try:
        invoice = db.execute('SELECT * FROM invoices WHERE order_id = ?', (order_id,)).fetchone()
    except:
        invoice = None

    # Get delivery challan if exists
    try:
        challan = db.execute('SELECT * FROM delivery_challans WHERE order_id = ?', (order_id,)).fetchone()
    except:
        challan = None

    return render_template('orders/order_details.html',
                          order=order,
                          order_items=order_items,
                          logs=logs,
                          invoice=invoice,
                          challan=challan)

@orders_bp.route('/<order_id>/details/json')
def get_order_details_json(order_id):
    """Get order details as JSON for API/modal fallback"""
    db = get_db()

    # Get order details
    order = db.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
    if not order:
        return jsonify({
            'success': False,
            'error': f'Order {order_id} not found'
        }), 404

    # Get order items
    order_items = db.execute('''
        SELECT oi.*, p.name as product_name
        FROM order_items oi
        LEFT JOIN products p ON oi.product_id = p.product_id
        WHERE oi.order_id = ?
    ''', (order_id,)).fetchall()

    return jsonify({
        'success': True,
        'order': dict(order),
        'order_items': [dict(item) for item in order_items]
    })
