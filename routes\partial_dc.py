"""
Partial DC Management Routes - Incremental Build
"""

from flask import Blueprint, render_template, g, current_app
import sqlite3

# Database helper function
def get_db():
    if 'db' not in g:
        g.db = sqlite3.connect(current_app.config['DATABASE'])
        g.db.row_factory = sqlite3.Row
        g.db.execute("PRAGMA foreign_keys = ON")
    return g.db

# Create blueprint
partial_dc_bp = Blueprint('partial_dc', __name__, url_prefix='/partial-dc')

@partial_dc_bp.route('/test')
def test_route():
    """Simple test route"""
    return "<h1>Partial DC Blueprint is Working!</h1><p>Minimal version test successful.</p>"

@partial_dc_bp.route('/')
def index():
    """Main route with template"""
    try:
        db = get_db()

        # Get statistics
        stats = {}
        stats['total_orders'] = db.execute('SELECT COUNT(*) as count FROM orders').fetchone()['count']
        stats['pending_orders'] = db.execute("SELECT COUNT(*) as count FROM orders WHERE status IN ('Approved', 'Processing')").fetchone()['count']
        stats['completed_dcs'] = db.execute('SELECT COUNT(*) as count FROM delivery_challans').fetchone()['count']
        stats['pending_items'] = db.execute("SELECT COUNT(*) as count FROM order_items WHERE status != 'Delivered'").fetchone()['count']

        # Get recent orders
        recent_orders = db.execute('''
            SELECT order_id, status, order_date
            FROM orders
            ORDER BY order_date DESC
            LIMIT 5
        ''').fetchall()

        return render_template('partial_dc/index.html',
                             stats=stats,
                             recent_orders=recent_orders)
    except Exception as e:
        return f"<h1>Partial DC Dashboard</h1><p>Error: {str(e)}</p>"

@partial_dc_bp.route('/generate')
def generate():
    """Generate route with template"""
    try:
        db = get_db()

        # Get orders ready for DC generation
        ready_orders = db.execute('''
            SELECT order_id, customer_name, order_date, status, order_amount
            FROM orders
            WHERE status IN ('Approved', 'Processing')
            ORDER BY order_date ASC
            LIMIT 20
        ''').fetchall()

        # Calculate statistics
        total_value = sum(order['order_amount'] for order in ready_orders if order['order_amount'])
        avg_value = total_value / len(ready_orders) if ready_orders else 0

        return render_template('partial_dc/generate.html',
                             ready_orders=ready_orders,
                             total_value=total_value,
                             avg_value=avg_value)
    except Exception as e:
        return f"<h1>DC Generation</h1><p>Error: {str(e)}</p>"

@partial_dc_bp.route('/status')
def status():
    """Status route with template"""
    try:
        db = get_db()

        # Get all delivery challans
        delivery_challans = db.execute('''
            SELECT dc_number, order_id, customer_name, created_date, status, total_amount
            FROM delivery_challans
            ORDER BY created_date DESC
            LIMIT 50
        ''').fetchall()

        # Get status statistics
        status_stats = {}
        status_stats['created'] = db.execute("SELECT COUNT(*) as count FROM delivery_challans WHERE status = 'created'").fetchone()['count']
        status_stats['dispatched'] = db.execute("SELECT COUNT(*) as count FROM delivery_challans WHERE status = 'dispatched'").fetchone()['count']
        status_stats['delivered'] = db.execute("SELECT COUNT(*) as count FROM delivery_challans WHERE status = 'delivered'").fetchone()['count']

        return render_template('partial_dc/status.html',
                             delivery_challans=delivery_challans,
                             status_stats=status_stats)
    except Exception as e:
        return f"<h1>Status Tracking</h1><p>Error: {str(e)}</p>"

@partial_dc_bp.route('/reports')
def reports():
    """Reports route with template"""
    try:
        db = get_db()

        # Get analytics data
        analytics = {}
        analytics['total_orders'] = db.execute('SELECT COUNT(*) as count FROM orders').fetchone()['count']
        analytics['partial_orders'] = db.execute("SELECT COUNT(*) as count FROM orders WHERE status = 'Partially Delivered'").fetchone()['count']
        analytics['completed_dcs'] = db.execute('SELECT COUNT(*) as count FROM delivery_challans').fetchone()['count']
        analytics['pending_value'] = db.execute("SELECT COALESCE(SUM(order_amount), 0) as total FROM orders WHERE status IN ('Approved', 'Processing')").fetchone()['total']
        analytics['fulfillment_rate'] = 85.5  # Placeholder
        analytics['avg_processing_time'] = 3.2  # Placeholder

        return render_template('partial_dc/reports.html',
                             analytics=analytics)
    except Exception as e:
        return f"<h1>Reports & Analytics</h1><p>Error: {str(e)}</p>"
