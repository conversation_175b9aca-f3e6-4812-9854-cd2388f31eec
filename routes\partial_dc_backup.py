"""
Partial DC Management Routes
Comprehensive partial delivery challan management with focused sub-components
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for, g, current_app
from flask_login import login_required, current_user
from datetime import datetime, timedelta
import json
import sqlite3

# Database helper function
def get_db():
    if 'db' not in g:
        g.db = sqlite3.connect(current_app.config['DATABASE'])
        g.db.row_factory = sqlite3.Row
        g.db.execute("PRAGMA foreign_keys = ON")
    return g.db

# Create blueprint
partial_dc_bp = Blueprint('partial_dc', __name__, url_prefix='/partial-dc')

# Test route to verify blueprint registration
@partial_dc_bp.route('/test')
def test_route():
    """Simple test route to verify blueprint is working"""
    return "<h1>Partial DC Blueprint is Working!</h1><p>This confirms the blueprint is properly registered.</p>"

# ============================================================================
# 1. PENDING ORDERS OVERVIEW
# ============================================================================

@partial_dc_bp.route('/')
@partial_dc_bp.route('/pending-orders')
@login_required
def pending_orders():
    """Main dashboard showing orders with pending items"""
    try:
        db = get_db()
        
        # Get orders with partial deliveries or pending items
        pending_orders = db.execute('''
            SELECT DISTINCT o.order_id, o.customer_name, o.order_date, o.status,
                   o.order_amount, o.sales_agent,
                   COUNT(oi.id) as total_items,
                   SUM(CASE WHEN oi.status != 'Delivered' THEN 1 ELSE 0 END) as pending_items,
                   SUM(CASE WHEN oi.status != 'Delivered' THEN oi.quantity ELSE 0 END) as pending_quantity,
                   SUM(CASE WHEN oi.status != 'Delivered' THEN oi.line_total ELSE 0 END) as pending_value
            FROM orders o
            JOIN order_items oi ON o.order_id = oi.order_id
            WHERE o.status IN ('Approved', 'Partially Delivered', 'Processing')
            GROUP BY o.order_id
            HAVING pending_items > 0
            ORDER BY o.order_date DESC
        ''').fetchall()
        
        # Get summary statistics
        stats = {
            'total_orders': len(pending_orders),
            'total_pending_items': sum(row['pending_items'] for row in pending_orders),
            'total_pending_quantity': sum(row['pending_quantity'] for row in pending_orders),
            'total_pending_value': sum(row['pending_value'] for row in pending_orders)
        }
        
        return render_template('partial_dc/pending_orders.html', 
                             orders=pending_orders, 
                             stats=stats)
        
    except Exception as e:
        flash(f'Error loading pending orders: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@partial_dc_bp.route('/pending-orders/<order_id>')
@login_required
def order_details(order_id):
    """Detailed view of specific order with pending items"""
    try:
        db = get_db()
        
        # Get order details
        order = db.execute('''
            SELECT * FROM orders WHERE order_id = ?
        ''', (order_id,)).fetchone()
        
        if not order:
            flash('Order not found', 'error')
            return redirect(url_for('partial_dc.pending_orders'))
        
        # Get order items with inventory status
        items = db.execute('''
            SELECT oi.*, p.product_name, p.strength,
                   COALESCE(inv.current_stock, 0) as available_stock,
                   CASE 
                       WHEN oi.status = 'Delivered' THEN 'Delivered'
                       WHEN COALESCE(inv.current_stock, 0) >= oi.quantity THEN 'Available'
                       WHEN COALESCE(inv.current_stock, 0) > 0 THEN 'Partial'
                       ELSE 'Out of Stock'
                   END as stock_status
            FROM order_items oi
            LEFT JOIN products p ON oi.product_id = p.product_id
            LEFT JOIN inventory inv ON oi.product_id = inv.product_id
            WHERE oi.order_id = ?
            ORDER BY oi.id
        ''', (order_id,)).fetchall()
        
        return render_template('partial_dc/order_details.html', 
                             order=order, 
                             items=items)
        
    except Exception as e:
        flash(f'Error loading order details: {str(e)}', 'error')
        return redirect(url_for('partial_dc.pending_orders'))

# ============================================================================
# 2. DC GENERATION
# ============================================================================

@partial_dc_bp.route('/generate')
@login_required
def dc_generation():
    """DC generation dashboard"""
    try:
        db = get_db()
        
        # Get orders ready for DC generation
        ready_orders = db.execute('''
            SELECT o.order_id, o.customer_name, o.order_date, o.order_amount,
                   COUNT(oi.id) as total_items,
                   SUM(CASE WHEN COALESCE(inv.current_stock, 0) > 0 THEN 1 ELSE 0 END) as available_items
            FROM orders o
            JOIN order_items oi ON o.order_id = oi.order_id
            LEFT JOIN inventory inv ON oi.product_id = inv.product_id
            WHERE o.status IN ('Approved', 'Processing')
              AND oi.status != 'Delivered'
            GROUP BY o.order_id
            HAVING available_items > 0
            ORDER BY o.order_date ASC
        ''').fetchall()
        
        return render_template('partial_dc/dc_generation.html', 
                             orders=ready_orders)
        
    except Exception as e:
        flash(f'Error loading DC generation data: {str(e)}', 'error')
        return redirect(url_for('partial_dc.pending_orders'))

@partial_dc_bp.route('/generate/<order_id>')
@login_required
def generate_dc_form(order_id):
    """Form to generate DC for specific order"""
    try:
        db = get_db()
        
        # Get order details
        order = db.execute('''
            SELECT * FROM orders WHERE order_id = ?
        ''', (order_id,)).fetchone()
        
        if not order:
            flash('Order not found', 'error')
            return redirect(url_for('partial_dc.dc_generation'))
        
        # Get available items for DC generation
        items = db.execute('''
            SELECT oi.*, p.product_name, p.strength,
                   COALESCE(inv.current_stock, 0) as available_stock,
                   LEAST(oi.quantity, COALESCE(inv.current_stock, 0)) as deliverable_qty
            FROM order_items oi
            LEFT JOIN products p ON oi.product_id = p.product_id
            LEFT JOIN inventory inv ON oi.product_id = inv.product_id
            WHERE oi.order_id = ? AND oi.status != 'Delivered'
              AND COALESCE(inv.current_stock, 0) > 0
            ORDER BY oi.id
        ''', (order_id,)).fetchall()
        
        return render_template('partial_dc/generate_form.html', 
                             order=order, 
                             items=items)
        
    except Exception as e:
        flash(f'Error loading DC generation form: {str(e)}', 'error')
        return redirect(url_for('partial_dc.dc_generation'))

@partial_dc_bp.route('/generate/<order_id>', methods=['POST'])
@login_required
def process_dc_generation(order_id):
    """Process DC generation for order"""
    try:
        db = get_db()
        
        # Get selected items and quantities
        selected_items = []
        for key, value in request.form.items():
            if key.startswith('item_') and value:
                item_id = key.replace('item_', '')
                quantity = int(value)
                if quantity > 0:
                    selected_items.append({
                        'item_id': item_id,
                        'quantity': quantity
                    })
        
        if not selected_items:
            flash('Please select at least one item for DC generation', 'warning')
            return redirect(url_for('partial_dc.generate_dc_form', order_id=order_id))
        
        # Generate DC number
        dc_number = generate_dc_number()
        
        # Create delivery challan record
        db.execute('''
            INSERT INTO delivery_challans 
            (dc_number, order_id, customer_name, status, created_by, created_date)
            SELECT ?, ?, customer_name, 'created', ?, CURRENT_TIMESTAMP
            FROM orders WHERE order_id = ?
        ''', (dc_number, order_id, current_user.username, order_id))
        
        # Update order items status and inventory
        total_items = 0
        total_amount = 0
        
        for item in selected_items:
            # Update order item
            db.execute('''
                UPDATE order_items 
                SET status = 'Delivered', 
                    quantity = ?,
                    last_updated = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (item['quantity'], item['item_id']))
            
            # Update inventory
            db.execute('''
                UPDATE inventory 
                SET current_stock = current_stock - ?
                WHERE product_id = (
                    SELECT product_id FROM order_items WHERE id = ?
                )
            ''', (item['quantity'], item['item_id']))
            
            total_items += 1
            # Get item total for amount calculation
            item_total = db.execute('''
                SELECT line_total FROM order_items WHERE id = ?
            ''', (item['item_id'],)).fetchone()
            if item_total:
                total_amount += item_total['line_total']
        
        # Update delivery challan totals
        db.execute('''
            UPDATE delivery_challans 
            SET total_items = ?, total_amount = ?
            WHERE dc_number = ?
        ''', (total_items, total_amount, dc_number))
        
        # Check if order is fully delivered
        remaining_items = db.execute('''
            SELECT COUNT(*) as count FROM order_items 
            WHERE order_id = ? AND status != 'Delivered'
        ''', (order_id,)).fetchone()
        
        if remaining_items['count'] == 0:
            # Order fully delivered
            db.execute('''
                UPDATE orders SET status = 'Delivered' WHERE order_id = ?
            ''', (order_id,))
        else:
            # Order partially delivered
            db.execute('''
                UPDATE orders SET status = 'Partially Delivered' WHERE order_id = ?
            ''', (order_id,))
        
        db.commit()
        
        flash(f'Delivery Challan {dc_number} generated successfully!', 'success')
        return redirect(url_for('partial_dc.status_tracking'))
        
    except Exception as e:
        db.rollback()
        flash(f'Error generating DC: {str(e)}', 'error')
        return redirect(url_for('partial_dc.generate_dc_form', order_id=order_id))

# ============================================================================
# 3. STATUS TRACKING
# ============================================================================

@partial_dc_bp.route('/status')
@login_required
def status_tracking():
    """Track status of all delivery challans"""
    try:
        db = get_db()
        
        # Get all delivery challans with order details
        challans = db.execute('''
            SELECT dc.*, o.customer_name, o.order_date, o.sales_agent
            FROM delivery_challans dc
            JOIN orders o ON dc.order_id = o.order_id
            ORDER BY dc.created_date DESC
        ''').fetchall()
        
        # Get status summary
        status_summary = db.execute('''
            SELECT status, COUNT(*) as count
            FROM delivery_challans
            GROUP BY status
        ''').fetchall()
        
        return render_template('partial_dc/status_tracking.html', 
                             challans=challans,
                             status_summary=status_summary)
        
    except Exception as e:
        flash(f'Error loading status tracking: {str(e)}', 'error')
        return redirect(url_for('partial_dc.pending_orders'))

@partial_dc_bp.route('/status/<dc_number>')
@login_required
def dc_details(dc_number):
    """Detailed view of specific delivery challan"""
    try:
        db = get_db()
        
        # Get DC details
        dc = db.execute('''
            SELECT dc.*, o.customer_name, o.customer_address, o.customer_phone
            FROM delivery_challans dc
            JOIN orders o ON dc.order_id = o.order_id
            WHERE dc.dc_number = ?
        ''', (dc_number,)).fetchone()
        
        if not dc:
            flash('Delivery Challan not found', 'error')
            return redirect(url_for('partial_dc.status_tracking'))
        
        # Get delivered items for this DC
        items = db.execute('''
            SELECT oi.*, p.product_name, p.strength
            FROM order_items oi
            LEFT JOIN products p ON oi.product_id = p.product_id
            WHERE oi.order_id = ? AND oi.status = 'Delivered'
            ORDER BY oi.id
        ''', (dc['order_id'],)).fetchall()
        
        return render_template('partial_dc/dc_details.html', 
                             dc=dc, 
                             items=items)
        
    except Exception as e:
        flash(f'Error loading DC details: {str(e)}', 'error')
        return redirect(url_for('partial_dc.status_tracking'))

# ============================================================================
# 4. REPORTS & ANALYTICS
# ============================================================================

@partial_dc_bp.route('/reports')
@login_required
def reports():
    """Reports and analytics dashboard"""
    try:
        db = get_db()
        
        # Get analytics data
        analytics = {
            'total_partial_orders': db.execute('''
                SELECT COUNT(DISTINCT order_id) FROM orders 
                WHERE status = 'Partially Delivered'
            ''').fetchone()[0],
            
            'pending_value': db.execute('''
                SELECT COALESCE(SUM(oi.line_total), 0) FROM order_items oi
                JOIN orders o ON oi.order_id = o.order_id
                WHERE oi.status != 'Delivered' AND o.status IN ('Approved', 'Processing', 'Partially Delivered')
            ''').fetchone()[0],
            
            'avg_fulfillment_time': 7,  # Placeholder - calculate from actual data
            
            'top_pending_products': db.execute('''
                SELECT p.product_name, p.strength, 
                       SUM(oi.quantity) as total_pending,
                       COUNT(DISTINCT oi.order_id) as orders_count
                FROM order_items oi
                JOIN products p ON oi.product_id = p.product_id
                JOIN orders o ON oi.order_id = o.order_id
                WHERE oi.status != 'Delivered' 
                  AND o.status IN ('Approved', 'Processing', 'Partially Delivered')
                GROUP BY oi.product_id
                ORDER BY total_pending DESC
                LIMIT 10
            ''').fetchall()
        }
        
        return render_template('partial_dc/reports.html', 
                             analytics=analytics)
        
    except Exception as e:
        flash(f'Error loading reports: {str(e)}', 'error')
        return redirect(url_for('partial_dc.pending_orders'))

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

def generate_dc_number():
    """Generate sequential DC number"""
    try:
        db = get_db()
        
        # Get last DC number
        last_dc = db.execute(
            'SELECT dc_number FROM delivery_challans ORDER BY id DESC LIMIT 1'
        ).fetchone()
        
        if last_dc and last_dc['dc_number']:
            try:
                # Extract number from DC-001 format
                last_num = int(last_dc['dc_number'].split('-')[1])
                next_num = last_num + 1
            except (IndexError, ValueError):
                next_num = 1
        else:
            next_num = 1
        
        return f"DC-{next_num:03d}"
        
    except Exception:
        # Fallback to timestamp-based number
        return f"DC-{int(datetime.now().timestamp())}"

# ============================================================================
# API ENDPOINTS
# ============================================================================

@partial_dc_bp.route('/api/order-status/<order_id>')
@login_required
def api_order_status(order_id):
    """API endpoint to get order status"""
    try:
        db = get_db()
        
        status = db.execute('''
            SELECT o.status, 
                   COUNT(oi.id) as total_items,
                   SUM(CASE WHEN oi.status = 'Delivered' THEN 1 ELSE 0 END) as delivered_items,
                   SUM(CASE WHEN oi.status != 'Delivered' THEN 1 ELSE 0 END) as pending_items
            FROM orders o
            JOIN order_items oi ON o.order_id = oi.order_id
            WHERE o.order_id = ?
            GROUP BY o.order_id
        ''', (order_id,)).fetchone()
        
        return jsonify({
            'success': True,
            'status': dict(status) if status else None
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
