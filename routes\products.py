from flask import Blueprint, render_template, request, g, flash, redirect, url_for, jsonify
from flask_login import login_required, current_user
from utils.permissions import permission_required
from utils.division_validator import get_division_validator, get_active_divisions_for_forms
from utils.unified_division_manager import get_divisions_for_forms_unified
from utils.product_validator import get_product_validator
from utils.db import get_db
import sqlite3
import logging
import traceback
from datetime import datetime

products_bp = Blueprint('products', __name__, template_folder='../templates')

@products_bp.route('/')
@permission_required('product_view')
def index():
    """Products index page - redirect to product management"""
    return redirect(url_for('products.product_management'))

def get_db():
    db = getattr(g, '_database', None)
    if db is None:
        db = g._database = sqlite3.connect('instance/medivent.db')
        db.row_factory = sqlite3.Row
    return db

@products_bp.route('/product_management/')
@login_required
@permission_required('product_view')
def product_management():
    """Product management with status filtering and activation/deactivation"""
    try:
        db = get_db()
        cursor = db.cursor()

        # Get filter parameters
        status_filter = request.args.get('status', '').strip()
        search_query = request.args.get('q', '').strip()
        category_filter = request.args.get('category', '').strip()

        # Build query with filters
        where_conditions = []
        params = []

        # Status filter
        if status_filter == 'active':
            where_conditions.append("(LOWER(status) = 'active' AND is_active = 1)")
        elif status_filter == 'inactive':
            where_conditions.append("(LOWER(status) != 'active' OR is_active = 0)")

        # Search filter
        if search_query:
            where_conditions.append("(name LIKE ? OR generic_name LIKE ? OR manufacturer LIKE ? OR category LIKE ?)")
            search_param = f'%{search_query}%'
            params.extend([search_param, search_param, search_param, search_param])

        # Category filter
        if category_filter:
            where_conditions.append("category = ?")
            params.append(category_filter)

        # Build final query
        base_query = "SELECT * FROM products"
        if where_conditions:
            base_query += " WHERE " + " AND ".join(where_conditions)
        base_query += " ORDER BY name"

        cursor.execute(base_query, params)
        raw_products = cursor.fetchall()

        # Convert to enhanced product dictionaries
        products = []
        for row in raw_products:
            try:
                products.append({
                    'id': row['id'] if 'id' in row.keys() else 0,  # Database primary key
                    'product_id': row['product_id'] if 'product_id' in row.keys() else (row['id'] if 'id' in row.keys() else 0),  # Product code
                    'name': row['name'] if 'name' in row.keys() else 'Unnamed Product',
                    'description': row['description'] if 'description' in row.keys() else '',
                    'unit_price': float(row['unit_price']) if 'unit_price' in row.keys() and row['unit_price'] else 0,
                    'category': row['category'] if 'category' in row.keys() else 'Uncategorized',
                    'manufacturer': row['manufacturer'] if 'manufacturer' in row.keys() else 'N/A',
                    'generic_name': row['generic_name'] if 'generic_name' in row.keys() else 'N/A',
                    'strength': row['strength'] if 'strength' in row.keys() else 'N/A',
                    'status': row['status'] if 'status' in row.keys() else 'active',
                    'is_active': bool(row['is_active']) if 'is_active' in row.keys() and row['is_active'] is not None else True,
                    'image_url': row['image_url'] if 'image_url' in row.keys() else '',
                    'created_at': row['created_at'] if 'created_at' in row.keys() else None,
                    'updated_at': row['updated_at'] if 'updated_at' in row.keys() else None
                })
            except Exception as e:
                print(f"Error processing product row: {e}")
                continue

        # Calculate enhanced stats
        total_products = len(products)
        active_products = len([p for p in products if p['is_active']])
        inactive_products = total_products - active_products
        categories = len({p['category'] for p in products if p['category'] != 'Uncategorized'})
        manufacturers = len({p['manufacturer'] for p in products if p['manufacturer'] != 'N/A'})

        stats = {
            'total_products': total_products,
            'active_products': active_products,
            'inactive_products': inactive_products,
            'categories': categories,
            'manufacturers': manufacturers
        }

        return render_template('products/product_management.html',
                             products=products,
                             stats=stats,
                             search_query=search_query,
                             status_filter=status_filter,
                             category_filter=category_filter)

    except Exception as e:
        flash(f'Error loading products: {str(e)}', 'danger')
        return render_template('products/product_management.html',
                             products=[],
                             stats={'total_products': 0, 'active_products': 0, 'inactive_products': 0, 'categories': 0, 'manufacturers': 0},
                             search_query='',
                             status_filter='',
                             category_filter='')

@products_bp.route('/product_test/')
def product_test():
    """Isolated test endpoint"""

    # Test 1: Basic response
    try:
        return "<h1>Basic HTML Test - THIS SHOULD DISPLAY</h1>"
    except Exception as e:
        return f"<h1>Basic Test FAILED: {str(e)}</h1>"


@products_bp.route('/new', methods=['GET', 'POST'])
@login_required
def new_product():
    """Create a new product with comprehensive division validation"""
    if request.method == 'POST':
        try:
            db = get_db()

            # Get form data
            product_data = {
                'name': request.form.get('name', '').strip(),
                'strength': request.form.get('strength', '').strip(),
                'manufacturer': request.form.get('manufacturer', '').strip(),
                'generic_name': request.form.get('generic_name', '').strip(),
                'unit_of_measure': request.form.get('unit_of_measure', '').strip(),
                'unit_price': request.form.get('unit_price'),
                'min_stock_level': request.form.get('min_stock_level'),
                'description': request.form.get('description', '').strip(),
                'division_id': request.form.get('division_id'),
                'mrp': request.form.get('mrp'),
                'tp_rate': request.form.get('tp_rate'),
                'asp': request.form.get('asp')
            }

            # Validate product data
            product_validator = get_product_validator(db)
            is_valid, errors = product_validator.validate_product_registration_data(product_data)

            if not is_valid:
                for error in errors:
                    flash(error, 'danger')
                return redirect(url_for('products.new_product'))

            # Generate product ID
            division_id = int(product_data['division_id'])
            product_id = product_validator.generate_product_id(division_id)

            # Begin transaction
            db.execute('BEGIN TRANSACTION')

            # Insert product with ASP pricing fields
            db.execute('''
                INSERT INTO products (
                    product_id, name, strength, manufacturer, generic_name,
                    unit_of_measure, unit_price, min_stock_level, description,
                    division_id, mrp, tp_rate, asp, created_at, created_by, updated_at, updated_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                product_id,
                product_data['name'],
                product_data['strength'],
                product_data['manufacturer'],
                product_data['generic_name'],
                product_data['unit_of_measure'],
                float(product_data['unit_price']) if product_data['unit_price'] else 0.0,
                int(product_data['min_stock_level']) if product_data['min_stock_level'] else 10,
                product_data['description'],
                division_id,
                float(product_data['mrp']) if product_data['mrp'] else 0.0,
                float(product_data['tp_rate']) if product_data['tp_rate'] else 0.0,
                float(product_data['asp']) if product_data['asp'] else 0.0,
                datetime.now(),
                current_user.username,
                datetime.now(),
                current_user.username
            ))

            # Commit transaction
            db.execute('COMMIT')

            # Invalidate product caches for real-time updates
            try:
                from utils.product_realtime_service import invalidate_product_caches
                invalidate_product_caches(db)
            except Exception as e:
                print(f"Failed to invalidate product caches: {e}")

            flash(f'Product {product_id} created successfully!', 'success')
            return redirect(url_for('products.view_product', product_id=product_id))

        except Exception as e:
            # Rollback on error
            try:
                db.execute('ROLLBACK')
            except:
                pass
            flash(f'Error creating product: {str(e)}', 'danger')
            return redirect(url_for('products.new_product'))

    # GET request - show product form
    try:
        db = get_db()

        # Get active divisions for form using unified manager
        divisions = get_divisions_for_forms_unified(db)

        if not divisions:
            flash('No active divisions found. Please create a division first.', 'warning')
            return redirect(url_for('divisions.index'))

        return render_template('products/new.html', divisions=divisions)

    except Exception as e:
        flash(f'Error loading product form: {str(e)}', 'danger')
        return redirect(url_for('dashboard'))


@products_bp.route('/<product_id>')
@login_required
def view_product(product_id):
    """View product details with division information"""
    try:
        db = get_db()
        product_validator = get_product_validator(db)

        # Validate product exists
        is_valid, _ = product_validator.validate_product_exists(product_id)

        if not is_valid:
            flash(f'Product {product_id} not found', 'danger')
            return redirect(url_for('products.product_management'))

        # Get full product details
        cursor = db.execute('''
            SELECT p.*, d.name as division_name, d.category as division_category,
                   d.manager_id as division_manager_id
            FROM products p
            LEFT JOIN divisions d ON p.division_id = d.division_id
            WHERE p.product_id = ?
        ''', (product_id,))

        product = cursor.fetchone()

        if not product:
            flash(f'Product {product_id} not found', 'danger')
            return redirect(url_for('products.product_management'))

        # Convert product to dict and handle datetime fields
        product_dict = dict(product)

        # Convert datetime strings to datetime objects for template formatting
        from datetime import datetime
        for field in ['created_at', 'updated_at']:
            if product_dict.get(field):
                try:
                    # Handle different datetime formats
                    dt_str = str(product_dict[field])
                    if 'T' in dt_str:
                        # ISO format: 2024-01-01T12:00:00
                        product_dict[field] = datetime.fromisoformat(dt_str.replace('T', ' ').split('.')[0])
                    else:
                        # Standard format: 2024-01-01 12:00:00
                        product_dict[field] = datetime.strptime(dt_str.split('.')[0], '%Y-%m-%d %H:%M:%S')
                except (ValueError, AttributeError):
                    # If parsing fails, set to None
                    product_dict[field] = None

        # Get inventory information
        cursor = db.execute('''
            SELECT COUNT(*) as inventory_entries,
                   COALESCE(SUM(stock_quantity), 0) as total_stock,
                   COALESCE(SUM(allocated_quantity), 0) as allocated_stock,
                   COALESCE(SUM(stock_quantity - allocated_quantity), 0) as available_stock
            FROM inventory
            WHERE product_id = ? AND status = 'active'
        ''', (product_id,))

        inventory_summary = cursor.fetchone()

        return render_template('products/view.html',
                             product=product_dict,
                             inventory_summary=inventory_summary)

    except Exception as e:
        flash(f'Error viewing product: {str(e)}', 'danger')
        return redirect(url_for('products.product_management'))

    # (We'll add more diagnostic steps if this works)

@products_bp.route('/update/<product_id>', methods=['GET', 'POST'])
@login_required
def update_product(product_id):
    """Update an existing product"""
    try:
        db = get_db()

        if request.method == 'POST':
            # Handle product update
            name = request.form.get('name', '').strip()
            description = request.form.get('description', '').strip()
            unit_price = request.form.get('unit_price', '0')
            category = request.form.get('category', '').strip()
            manufacturer = request.form.get('manufacturer', '').strip()

            # Validate inputs
            if not name:
                flash('Product name is required', 'danger')
                return redirect(url_for('products.update_product', product_id=product_id))

            try:
                unit_price = float(unit_price)
            except ValueError:
                unit_price = 0.0

            # Update product in database
            cursor = db.cursor()
            cursor.execute("""
                UPDATE products
                SET name = ?, description = ?, unit_price = ?, category = ?, manufacturer = ?
                WHERE product_id = ?
            """, (name, description, unit_price, category, manufacturer, product_id))

            db.commit()
            flash(f'Product {name} updated successfully!', 'success')
            return redirect(url_for('products.view_product', product_id=product_id))

        # GET request - show update form
        cursor = db.cursor()
        cursor.execute("SELECT * FROM products WHERE product_id = ?", (product_id,))
        product = cursor.fetchone()

        if not product:
            flash(f'Product {product_id} not found', 'danger')
            return redirect(url_for('products.product_management'))

        # Convert to dict for template
        product_dict = {
            'product_id': product['product_id'] if 'product_id' in product.keys() else product_id,
            'name': product['name'] if 'name' in product.keys() else '',
            'description': product['description'] if 'description' in product.keys() else '',
            'unit_price': product['unit_price'] if 'unit_price' in product.keys() else 0,
            'category': product['category'] if 'category' in product.keys() else '',
            'manufacturer': product['manufacturer'] if 'manufacturer' in product.keys() else ''
        }

        return render_template('products/update.html', product=product_dict)

    except Exception as e:
        flash(f'Error updating product: {str(e)}', 'danger')
        return redirect(url_for('products.product_management'))

@products_bp.route('/update_selection')
@login_required
def update_product_selection():
    """Show product selection page for updates"""
    try:
        db = get_db()
        cursor = db.cursor()

        # Get search parameter
        search = request.args.get('search', '').strip()

        # Build query with search
        if search:
            cursor.execute("""
                SELECT * FROM products
                WHERE name LIKE ? OR product_id LIKE ? OR manufacturer LIKE ?
                ORDER BY name
            """, (f'%{search}%', f'%{search}%', f'%{search}%'))
        else:
            cursor.execute("SELECT * FROM products ORDER BY name")

        products = cursor.fetchall()

        # Convert to list of dicts
        products_list = []
        for product in products:
            products_list.append({
                'product_id': product['product_id'] if 'product_id' in product.keys() else (product['id'] if 'id' in product.keys() else 0),
                'name': product['name'] if 'name' in product.keys() else 'Unnamed Product',
                'category': product['category'] if 'category' in product.keys() else '',
                'unit_price': product['unit_price'] if 'unit_price' in product.keys() else 0,
                'manufacturer': product['manufacturer'] if 'manufacturer' in product.keys() else '',
                'strength': product['strength'] if 'strength' in product.keys() else ''
            })

        # Add pagination
        from utils.pagination import paginate_query_results
        paginated_products, pagination = paginate_query_results(products_list, per_page=12)

        # Calculate stats for the template
        stats = {
            'total_products': len(products_list),
            'in_stock': len([p for p in products_list if p.get('unit_price', 0) > 0]),  # Simple approximation
            'low_stock': 0,  # Placeholder
            'out_of_stock': 0  # Placeholder
        }

        return render_template('products/update_selection.html',
                             products=paginated_products,
                             stats=stats,
                             pagination=pagination)

    except Exception as e:
        flash(f'Error loading products: {str(e)}', 'danger')
        return redirect(url_for('products.product_management'))


@products_bp.route('/view_all/')
@login_required
def view_all_products():
    """View all products with comprehensive details and stats"""
    try:
        db = get_db()
        cursor = db.cursor()

        # Get search and filter parameters
        search_query = request.args.get('q', '').strip()
        generic_filter = request.args.get('generic', '').strip()
        sort_by = request.args.get('sort', 'name_asc')

        # Build the query with filters
        base_query = """
            SELECT p.id, p.product_id, p.name, p.strength, p.manufacturer,
                   p.category, p.unit_price, p.status, p.is_active,
                   p.unit_of_measure, p.description, p.created_at
            FROM products p
            WHERE 1=1
        """

        params = []

        # Add search filter
        if search_query:
            base_query += " AND (p.name LIKE ? OR p.manufacturer LIKE ? OR p.category LIKE ?)"
            search_param = f"%{search_query}%"
            params.extend([search_param, search_param, search_param])

        # Add generic filter
        if generic_filter:
            base_query += " AND p.category = ?"
            params.append(generic_filter)

        # Add sorting
        if sort_by == 'name_asc':
            base_query += " ORDER BY p.name ASC"
        elif sort_by == 'name_desc':
            base_query += " ORDER BY p.name DESC"
        elif sort_by == 'price_low':
            base_query += " ORDER BY p.unit_price ASC"
        elif sort_by == 'price_high':
            base_query += " ORDER BY p.unit_price DESC"
        elif sort_by == 'manufacturer':
            base_query += " ORDER BY p.manufacturer ASC"
        else:
            base_query += " ORDER BY p.name ASC"

        cursor.execute(base_query, params)
        products = cursor.fetchall()

        # Get unique categories for filter dropdown
        cursor.execute("SELECT DISTINCT category FROM products WHERE category IS NOT NULL AND category != '' ORDER BY category")
        generic_categories = [{'category': row['category']} for row in cursor.fetchall()]

        # Convert to list of dicts with proper data handling
        products_list = []
        for product in products:
            product_dict = dict(product)
            # Ensure all required fields exist
            product_dict.setdefault('division_name', 'General')
            product_dict.setdefault('image_url', '')
            product_dict.setdefault('generic_name', product_dict.get('category', 'N/A'))
            products_list.append(product_dict)

        # Calculate comprehensive stats
        total_products = len(products_list)
        active_products = len([p for p in products_list if p.get('is_active', 1) == 1])
        inactive_products = total_products - active_products

        stats = {
            'total': total_products,
            'available': active_products,
            'low_stock': 0,  # Placeholder - would need inventory data
            'out_of_stock': inactive_products
        }

        filters = {
            'search': search_query,
            'generic': generic_filter,
            'sort': sort_by
        }

        return render_template('products/view_all_products.html',
                             products=products_list,
                             stats=stats,
                             filters=filters,
                             generic_categories=generic_categories)

    except Exception as e:
        flash(f'Error loading products: {str(e)}', 'danger')
        return redirect(url_for('products.product_management'))

@products_bp.route('/delete/<product_id>', methods=['POST'])
@login_required
def delete_product(product_id):
    """Delete a product"""
    try:
        db = get_db()
        cursor = db.cursor()

        # Get product info first using database ID
        cursor.execute("SELECT * FROM products WHERE id = ?", (product_id,))
        product = cursor.fetchone()

        if not product:
            if request.headers.get('Content-Type') == 'application/json' or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'message': 'Product not found!'})
            flash(f'Product {product_id} not found', 'danger')
            return redirect(url_for('products.product_management'))

        # Delete the product using database ID
        cursor.execute("DELETE FROM products WHERE id = ?", (product_id,))
        db.commit()

        if cursor.rowcount > 0:
            message = f'Product "{product["name"]}" deleted successfully!'
            if request.headers.get('Content-Type') == 'application/json' or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': True, 'message': message})
            flash(message, 'success')
        else:
            message = 'Failed to delete product!'
            if request.headers.get('Content-Type') == 'application/json' or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'message': message})
            flash(message, 'error')

        return redirect(url_for('products.product_management'))

    except Exception as e:
        message = f'Error deleting product: {str(e)}'
        if request.headers.get('Content-Type') == 'application/json' or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'message': message})
        flash(message, 'danger')
        return redirect(url_for('products.product_management'))


@products_bp.route('/activate/<product_id>', methods=['POST'])
@login_required
@permission_required('product_edit')
def activate_product(product_id):
    """Activate a product"""
    try:
        db = get_db()
        cursor = db.cursor()

        # Get product info first
        cursor.execute("SELECT * FROM products WHERE id = ?", (product_id,))
        product = cursor.fetchone()

        if not product:
            if request.headers.get('Content-Type') == 'application/json' or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'message': 'Product not found!'})
            flash('Product not found!', 'error')
            return redirect(url_for('products.product_management'))

        # Update product status using database ID
        cursor.execute("""
            UPDATE products
            SET status = 'active', is_active = 1, updated_at = ?
            WHERE id = ?
        """, (datetime.now(), product_id))

        db.commit()

        if cursor.rowcount > 0:
            message = f'Product "{product["name"]}" activated successfully!'
            if request.headers.get('Content-Type') == 'application/json' or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': True, 'message': message})
            flash(message, 'success')
        else:
            message = 'Failed to activate product!'
            if request.headers.get('Content-Type') == 'application/json' or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'message': message})
            flash(message, 'error')

    except Exception as e:
        message = f'Error activating product: {str(e)}'
        if request.headers.get('Content-Type') == 'application/json' or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'message': message})
        flash(message, 'error')

    return redirect(url_for('products.product_management'))

@products_bp.route('/deactivate/<product_id>', methods=['POST'])
@login_required
@permission_required('product_edit')
def deactivate_product(product_id):
    """Deactivate a product"""
    try:
        db = get_db()
        cursor = db.cursor()

        # Get product info first
        cursor.execute("SELECT * FROM products WHERE id = ?", (product_id,))
        product = cursor.fetchone()

        if not product:
            if request.headers.get('Content-Type') == 'application/json' or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'message': 'Product not found!'})
            flash('Product not found!', 'error')
            return redirect(url_for('products.product_management'))

        # Update product status using database ID
        cursor.execute("""
            UPDATE products
            SET status = 'inactive', is_active = 0, updated_at = ?
            WHERE id = ?
        """, (datetime.now(), product_id))

        db.commit()

        if cursor.rowcount > 0:
            message = f'Product "{product["name"]}" deactivated successfully!'
            if request.headers.get('Content-Type') == 'application/json' or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': True, 'message': message})
            flash(message, 'success')
        else:
            message = 'Failed to deactivate product!'
            if request.headers.get('Content-Type') == 'application/json' or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'message': message})
            flash(message, 'error')

    except Exception as e:
        message = f'Error deactivating product: {str(e)}'
        if request.headers.get('Content-Type') == 'application/json' or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'message': message})
        flash(message, 'error')

    return redirect(url_for('products.product_management'))
