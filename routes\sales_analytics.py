"""
Sales and Division Analytics Routes
Comprehensive analytics for salesperson performance, team metrics, and division analysis
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from datetime import datetime, timedelta
import sqlite3
import json

sales_analytics_bp = Blueprint('sales_analytics', __name__, url_prefix='/sales_analytics')

def get_db():
    """Get database connection"""
    conn = sqlite3.connect('instance/medivent.db')
    conn.row_factory = sqlite3.Row
    return conn

@sales_analytics_bp.route('/')
@login_required
def dashboard():
    """Sales Analytics Dashboard"""
    try:
        db = get_db()
        
        # Get sales overview metrics
        today = datetime.now().date()
        month_start = today.replace(day=1)
        
        # Total sales this month
        monthly_sales = db.execute(
            "SELECT COALESCE(SUM(total_amount), 0) as total FROM orders WHERE DATE(order_date) >= ? AND status != 'Cancelled'",
            (month_start,)
        ).fetchone()['total']
        
        # Sales count this month
        monthly_orders = db.execute(
            "SELECT COUNT(*) as count FROM orders WHERE DATE(order_date) >= ? AND status != 'Cancelled'",
            (month_start,)
        ).fetchone()['count']
        
        # Top performing salesperson
        top_salesperson = db.execute(
            """SELECT sales_agent, COUNT(*) as order_count, SUM(total_amount) as total_sales
               FROM orders
               WHERE DATE(order_date) >= ? AND status != 'Cancelled' AND sales_agent IS NOT NULL
               GROUP BY sales_agent
               ORDER BY total_sales DESC
               LIMIT 1""",
            (month_start,)
        ).fetchone()
        
        # Division performance
        division_performance = db.execute(
            """SELECT d.name as division_name, COUNT(o.order_id) as order_count,
                      SUM(o.total_amount) as total_sales
               FROM divisions d
               LEFT JOIN orders o ON d.division_id = o.division_id
               WHERE DATE(o.order_date) >= ? AND o.status != 'Cancelled'
               GROUP BY d.division_id
               ORDER BY total_sales DESC
               LIMIT 5""",
            (month_start,)
        ).fetchall()
        
        context = {
            'title': 'Sales & Division Analytics',
            'monthly_sales': monthly_sales,
            'monthly_orders': monthly_orders,
            'top_salesperson': top_salesperson,
            'division_performance': division_performance,
            'current_user': current_user,
            'now': datetime.now()
        }
        
        return render_template('sales_analytics/dashboard.html', **context)
        
    except Exception as e:
        flash(f'Error loading sales analytics dashboard: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@sales_analytics_bp.route('/salesperson_ledger')
@login_required
def salesperson_ledger():
    """Comprehensive Salesperson Ledger"""
    try:
        db = get_db()
        
        # Get all salespersons with their performance
        salesperson_data = db.execute(
            """SELECT sales_agent,
                      COUNT(*) as total_orders,
                      SUM(total_amount) as total_sales,
                      AVG(total_amount) as avg_order_value,
                      COUNT(CASE WHEN status = 'Delivered' THEN 1 END) as completed_orders,
                      MIN(order_date) as first_sale,
                      MAX(order_date) as last_sale
               FROM orders
               WHERE sales_agent IS NOT NULL AND status != 'Cancelled'
               GROUP BY sales_agent
               ORDER BY total_sales DESC"""
        ).fetchall()
        
        # Get monthly performance for each salesperson
        monthly_performance = db.execute(
            """SELECT sales_agent, strftime('%Y-%m', order_date) as month,
                      COUNT(*) as monthly_orders,
                      SUM(total_amount) as monthly_sales
               FROM orders
               WHERE sales_agent IS NOT NULL AND status != 'Cancelled'
               AND DATE(order_date) >= DATE('now', '-12 months')
               GROUP BY sales_agent, strftime('%Y-%m', order_date)
               ORDER BY sales_agent, month"""
        ).fetchall()
        
        context = {
            'title': 'Salesperson Ledger',
            'salesperson_data': salesperson_data,
            'monthly_performance': monthly_performance,
            'current_user': current_user,
            'now': datetime.now()
        }
        
        return render_template('sales_analytics/salesperson_ledger.html', **context)
        
    except Exception as e:
        flash(f'Error loading salesperson ledger: {str(e)}', 'error')
        return redirect(url_for('sales_analytics.dashboard'))

@sales_analytics_bp.route('/team_performance')
@login_required
def team_performance():
    """Sales Team Performance Dashboard"""
    try:
        db = get_db()
        
        # Get team performance metrics
        today = datetime.now().date()
        week_ago = today - timedelta(days=7)
        month_ago = today - timedelta(days=30)
        
        # Weekly team performance
        weekly_performance = db.execute(
            """SELECT sales_agent,
                      COUNT(*) as weekly_orders,
                      SUM(total_amount) as weekly_sales,
                      AVG(total_amount) as avg_order_value
               FROM orders
               WHERE sales_agent IS NOT NULL AND status != 'Cancelled'
               AND DATE(order_date) >= ?
               GROUP BY sales_agent
               ORDER BY weekly_sales DESC""",
            (week_ago,)
        ).fetchall()
        
        # Monthly team performance
        monthly_performance = db.execute(
            """SELECT sales_agent,
                      COUNT(*) as monthly_orders,
                      SUM(total_amount) as monthly_sales,
                      AVG(total_amount) as avg_order_value
               FROM orders
               WHERE sales_agent IS NOT NULL AND status != 'Cancelled'
               AND DATE(order_date) >= ?
               GROUP BY sales_agent
               ORDER BY monthly_sales DESC""",
            (month_ago,)
        ).fetchall()
        
        # Team comparison metrics
        team_metrics = db.execute(
            """SELECT 
                COUNT(DISTINCT sales_agent) as total_agents,
                COUNT(*) as total_orders,
                SUM(total_amount) as total_sales,
                AVG(total_amount) as avg_order_value,
                MAX(total_amount) as highest_order,
                MIN(total_amount) as lowest_order
               FROM orders
               WHERE sales_agent IS NOT NULL AND status != 'Cancelled'
               AND DATE(order_date) >= ?""",
            (month_ago,)
        ).fetchone()
        
        context = {
            'title': 'Sales Team Performance',
            'weekly_performance': weekly_performance,
            'monthly_performance': monthly_performance,
            'team_metrics': team_metrics,
            'current_user': current_user,
            'now': datetime.now()
        }
        
        return render_template('sales_analytics/team_performance.html', **context)
        
    except Exception as e:
        flash(f'Error loading team performance: {str(e)}', 'error')
        return redirect(url_for('sales_analytics.dashboard'))

@sales_analytics_bp.route('/division_ledger')
@login_required
def division_ledger():
    """Division Ledger with Financial Tracking"""
    try:
        db = get_db()
        
        # Get division financial data (active divisions only)
        division_ledger = db.execute(
            """SELECT d.division_id, d.name as division_name, d.manager_id,
                      COUNT(o.order_id) as total_orders,
                      SUM(o.total_amount) as total_revenue,
                      AVG(o.total_amount) as avg_order_value,
                      COUNT(CASE WHEN o.status = 'Delivered' THEN 1 END) as completed_orders,
                      SUM(CASE WHEN o.status = 'Delivered' THEN o.total_amount ELSE 0 END) as delivered_revenue,
                      COUNT(CASE WHEN o.status = 'Cancelled' THEN 1 END) as cancelled_orders
               FROM divisions d
               LEFT JOIN orders o ON d.division_id = o.division_id
               WHERE d.is_active = 1 AND d.status = 'active'
               GROUP BY d.division_id
               ORDER BY total_revenue DESC"""
        ).fetchall()
        
        # Get monthly division performance
        monthly_division_performance = db.execute(
            """SELECT d.name as division_name, strftime('%Y-%m', o.order_date) as month,
                      COUNT(o.order_id) as monthly_orders,
                      SUM(o.total_amount) as monthly_revenue
               FROM divisions d
               LEFT JOIN orders o ON d.division_id = o.division_id
               WHERE o.status != 'Cancelled'
               AND DATE(o.order_date) >= DATE('now', '-12 months')
               GROUP BY d.division_id, strftime('%Y-%m', o.order_date)
               ORDER BY d.name, month"""
        ).fetchall()
        
        # Division expense tracking (if available)
        division_expenses = db.execute(
            """SELECT d.name as division_name,
                      COALESCE(SUM(CASE WHEN expense_type = 'operational' THEN amount ELSE 0 END), 0) as operational_expenses,
                      COALESCE(SUM(CASE WHEN expense_type = 'marketing' THEN amount ELSE 0 END), 0) as marketing_expenses,
                      COALESCE(SUM(amount), 0) as total_expenses
               FROM divisions d
               LEFT JOIN division_expenses de ON d.division_id = de.division_id
               WHERE DATE(de.expense_date) >= DATE('now', '-30 days')
               GROUP BY d.division_id
               ORDER BY total_expenses DESC"""
        ).fetchall()
        
        context = {
            'title': 'Division Ledger',
            'division_ledger': division_ledger,
            'monthly_division_performance': monthly_division_performance,
            'division_expenses': division_expenses,
            'current_user': current_user,
            'now': datetime.now()
        }
        
        return render_template('sales_analytics/division_ledger.html', **context)
        
    except Exception as e:
        flash(f'Error loading division ledger: {str(e)}', 'error')
        return redirect(url_for('sales_analytics.dashboard'))

@sales_analytics_bp.route('/division_analysis')
@login_required
def division_analysis():
    """Division-wise Comparative Analysis"""
    try:
        db = get_db()
        
        # Comparative division analysis
        division_comparison = db.execute(
            """SELECT d.division_id, d.name as division_name, d.location,
                      COUNT(o.order_id) as total_orders,
                      SUM(o.total_amount) as total_revenue,
                      AVG(o.total_amount) as avg_order_value,
                      COUNT(DISTINCT o.customer_id) as unique_customers,
                      COUNT(CASE WHEN o.status = 'Delivered' THEN 1 END) as delivered_orders,
                      (COUNT(CASE WHEN o.status = 'Delivered' THEN 1 END) * 100.0 / COUNT(o.order_id)) as delivery_rate,
                      COUNT(CASE WHEN DATE(o.order_date) >= DATE('now', '-30 days') THEN 1 END) as recent_orders
               FROM divisions d
               LEFT JOIN orders o ON d.division_id = o.division_id
               WHERE o.status != 'Cancelled'
               GROUP BY d.division_id
               ORDER BY total_revenue DESC"""
        ).fetchall()
        
        # Division growth analysis
        division_growth = db.execute(
            """SELECT d.name as division_name,
                      SUM(CASE WHEN DATE(o.order_date) >= DATE('now', '-30 days') THEN o.total_amount ELSE 0 END) as current_month,
                      SUM(CASE WHEN DATE(o.order_date) >= DATE('now', '-60 days') AND DATE(o.order_date) < DATE('now', '-30 days') THEN o.total_amount ELSE 0 END) as previous_month,
                      SUM(CASE WHEN DATE(o.order_date) >= DATE('now', '-90 days') AND DATE(o.order_date) < DATE('now', '-60 days') THEN o.total_amount ELSE 0 END) as two_months_ago
               FROM divisions d
               LEFT JOIN orders o ON d.division_id = o.division_id
               WHERE o.status != 'Cancelled'
               GROUP BY d.division_id
               ORDER BY current_month DESC"""
        ).fetchall()
        
        # Product performance by division
        division_products = db.execute(
            """SELECT d.name as division_name, p.name as product_name,
                      COUNT(oi.order_item_id) as times_ordered,
                      SUM(oi.quantity) as total_quantity,
                      SUM(oi.quantity * oi.price) as product_revenue
               FROM divisions d
               LEFT JOIN orders o ON d.division_id = o.division_id
               LEFT JOIN order_items oi ON o.order_id = oi.order_id
               LEFT JOIN products p ON oi.product_id = p.product_id
               WHERE o.status != 'Cancelled'
               AND DATE(o.order_date) >= DATE('now', '-90 days')
               GROUP BY d.division_id, p.product_id
               HAVING times_ordered > 0
               ORDER BY d.name, product_revenue DESC"""
        ).fetchall()
        
        context = {
            'title': 'Division-wise Analysis',
            'division_comparison': division_comparison,
            'division_growth': division_growth,
            'division_products': division_products,
            'current_user': current_user,
            'now': datetime.now()
        }
        
        return render_template('sales_analytics/division_analysis.html', **context)
        
    except Exception as e:
        flash(f'Error loading division analysis: {str(e)}', 'error')
        return redirect(url_for('sales_analytics.dashboard'))

@sales_analytics_bp.route('/api/sales_trends')
@login_required
def api_sales_trends():
    """API endpoint for sales trend data"""
    try:
        db = get_db()
        
        # Get daily sales trends for the last 30 days
        sales_trends = db.execute(
            """SELECT DATE(order_date) as date,
                      COUNT(*) as order_count,
                      SUM(total_amount) as daily_sales
               FROM orders
               WHERE DATE(order_date) >= DATE('now', '-30 days')
               AND status != 'Cancelled'
               GROUP BY DATE(order_date)
               ORDER BY date"""
        ).fetchall()
        
        return jsonify({
            'success': True,
            'data': [dict(row) for row in sales_trends]
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@sales_analytics_bp.route('/api/division_performance')
@login_required
def api_division_performance():
    """API endpoint for division performance data"""
    try:
        db = get_db()
        
        # Get division performance data
        division_data = db.execute(
            """SELECT d.name as division_name,
                      COUNT(o.order_id) as order_count,
                      SUM(o.total_amount) as total_sales
               FROM divisions d
               LEFT JOIN orders o ON d.division_id = o.division_id
               WHERE DATE(o.order_date) >= DATE('now', '-30 days')
               AND o.status != 'Cancelled'
               GROUP BY d.division_id
               ORDER BY total_sales DESC"""
        ).fetchall()
        
        return jsonify({
            'success': True,
            'data': [dict(row) for row in division_data]
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})
