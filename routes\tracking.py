from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for, make_response
from flask_login import login_required
from datetime import datetime
import json
import time
import logging
from io import BytesIO
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.enums import TA_CENTER, TA_LEFT

# Import Playwright for web scraping
try:
    from playwright.sync_api import sync_playwright
    from playwright.async_api import async_playwright
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False

import asyncio
import concurrent.futures
from functools import partial

tracking_bp = Blueprint('tracking', __name__, template_folder='../templates')

def get_text(element, selector, default=""):
    """Helper function to safely extract text"""
    try:
        found = element.query_selector(selector)
        text = found.inner_text().strip() if found else default
        # Return empty string if text is just whitespace or common empty indicators
        if text.lower() in ['n/a', 'na', '-', '--', '---', 'null', 'none', '']:
            return ""
        return text
    except Exception:
        return default

def is_valid_tracking_data(booking_details):
    """Check if booking details contain valid data (not all empty)"""
    if not booking_details:
        return False

    # Check if at least one field has meaningful data
    fields_to_check = ['agent_reference', 'booking_date', 'destination', 'origin', 'tracking_number']

    for field in fields_to_check:
        value = booking_details.get(field, "").strip()
        if value and value.lower() not in ['n/a', 'na', '-', '--', '---', 'null', 'none', 'not available']:
            return True

    return False

def has_meaningful_tracking_data(result_data):
    """Enhanced check for meaningful tracking data from TCS website"""
    if not result_data:
        return False

    # Check booking details
    booking_details = result_data.get('booking_details', {})
    if booking_details:
        for key, value in booking_details.items():
            if value and str(value).strip() and str(value).lower() not in ['n/a', 'na', '-', '--', '---', 'null', 'none', 'not available', '']:
                return True

    # Check track summary
    track_summary = result_data.get('track_summary', {})
    if track_summary:
        for key, value in track_summary.items():
            if value and str(value).strip() and str(value).lower() not in ['n/a', 'na', '-', '--', '---', 'null', 'none', 'not available', '']:
                return True

    # Check status history
    status_history = result_data.get('status_history', [])
    if status_history and len(status_history) > 0:
        return True

    return False

def scrape_tcs_tracking(tracking_number):
    """
    Scrape TCS Express tracking information for a given tracking number
    Returns structured data with tracking details
    """
    if not PLAYWRIGHT_AVAILABLE:
        return {
            "status": "error",
            "error": "Playwright is not installed. Please install it using: pip install playwright && playwright install",
            "tracking_number": tracking_number,
            "timestamp": datetime.now().isoformat()
        }
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=True)
        context = browser.new_context()
        page = context.new_page()
        
        try:
            # Start timer
            start_time = datetime.now()
            
            # Navigate to tracking page
            tracking_url = f"https://www.tcsexpress.com/track/{tracking_number}"
            page.goto(tracking_url, timeout=60000)
            
            # Wait for content to load
            page.wait_for_selector('div.bg-\\[\\#f8f8f8\\]', state="attached", timeout=15000)
            
            # Check for invalid tracking number
            if page.query_selector('text=No record found'):
                return {"status": "not_found", "tracking_number": tracking_number}
            
            # Extract all data
            result = {
                "status": "success",
                "tracking_number": tracking_number,
                "scrape_timestamp": datetime.now().isoformat(),
                "scrape_duration": None,
                "data": {}
            }
            
            # 1. Shipment Booking Details
            booking_section = page.query_selector('div.bg-\\[\\#f8f8f8\\]:has-text("Shipment Booking Details")')
            booking_details = {}

            if booking_section:
                # Try to get tracking number from the red text span
                tracking_span = booking_section.query_selector('span.text-\\[\\#f0575d\\]')
                tracking_from_span = tracking_span.inner_text().strip() if tracking_span else ""

                booking_details = {
                    "tracking_number": tracking_from_span or tracking_number,
                    "agent_reference": get_text(booking_section, 'div.flex:has-text("Agent Reference Number") p.font-bold'),
                    "origin": get_text(booking_section, 'div.flex:has-text("Origin") p.font-bold'),
                    "destination": get_text(booking_section, 'div.flex:has-text("Destination") p.font-bold'),
                    "booking_date": get_text(booking_section, 'div.flex:has-text("Booking Date") p.font-bold')
                }

            result["data"]["booking_details"] = booking_details
            
            # 2. Shipment Track Summary
            summary_section = page.query_selector('div.bg-\\[\\#f8f8f8\\]:has-text("Shipment Track Summary")')
            if summary_section:
                result["data"]["track_summary"] = {
                    "current_status": get_text(summary_section, 'span.text-\\[\\#f0575d\\]'),
                    "delivered_on": get_text(summary_section, 'div.flex:has-text("Delivered On") p.font-bold'),
                    "received_by": get_text(summary_section, 'div.flex:has-text("Received by") p.font-bold')
                }
            
            # 3. Status History
            history_table = page.query_selector('table:has-text("Date Time")')
            if history_table:
                status_history = []
                rows = history_table.query_selector_all('tbody tr')
                for row in rows:
                    cells = row.query_selector_all('td')
                    if len(cells) >= 2:
                        status_history.append({
                            "date_time": cells[0].inner_text().replace('\n', ' ').strip(),
                            "status": cells[1].inner_text().replace('\n', ' ').strip(),
                            "timestamp": datetime.now().isoformat()
                        })
                result["data"]["status_history"] = status_history
            
            # Calculate duration
            result["scrape_duration"] = str(datetime.now() - start_time)

            # Final validation - check if we actually have meaningful data
            if not has_meaningful_tracking_data(result["data"]):
                return {"status": "not_found", "tracking_number": tracking_number}

            return result
            
        except Exception as e:
            logging.error(f"Error scraping TCS tracking for {tracking_number}: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "tracking_number": tracking_number,
                "timestamp": datetime.now().isoformat()
            }
        finally:
            context.close()
            browser.close()

async def scrape_tcs_tracking_async(tracking_number):
    """
    Async version of TCS Express tracking scraper for better performance
    """
    if not PLAYWRIGHT_AVAILABLE:
        return {
            "status": "error",
            "error": "Playwright is not installed. Please install it using: pip install playwright && playwright install",
            "tracking_number": tracking_number,
            "timestamp": datetime.now().isoformat()
        }

    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        context = await browser.new_context()
        page = await context.new_page()

        try:
            # Start timer
            start_time = datetime.now()

            # Navigate to tracking page
            tracking_url = f"https://www.tcsexpress.com/track/{tracking_number}"
            await page.goto(tracking_url, timeout=60000)

            # Wait for content to load
            await page.wait_for_selector('div.bg-\\[\\#f8f8f8\\]', state="attached", timeout=15000)

            # Check for invalid tracking number
            if await page.query_selector('text=No record found'):
                return {"status": "not_found", "tracking_number": tracking_number}

            # Extract all data
            result = {
                "status": "success",
                "tracking_number": tracking_number,
                "scrape_timestamp": datetime.now().isoformat(),
                "scrape_duration": None,
                "data": {}
            }

            # 1. Shipment Booking Details
            booking_section = await page.query_selector('div.bg-\\[\\#f8f8f8\\]:has-text("Shipment Booking Details")')
            booking_details = {}

            if booking_section:
                # Try to get tracking number from the red text span
                tracking_span = await booking_section.query_selector('span.text-\\[\\#f0575d\\]')
                tracking_from_span = await tracking_span.inner_text() if tracking_span else ""
                tracking_from_span = tracking_from_span.strip()

                booking_details = {
                    "tracking_number": tracking_from_span or tracking_number,
                    "agent_reference": await get_text_async(booking_section, 'div.flex:has-text("Agent Reference Number") p.font-bold'),
                    "origin": await get_text_async(booking_section, 'div.flex:has-text("Origin") p.font-bold'),
                    "destination": await get_text_async(booking_section, 'div.flex:has-text("Destination") p.font-bold'),
                    "booking_date": await get_text_async(booking_section, 'div.flex:has-text("Booking Date") p.font-bold')
                }

            # Validate if we have meaningful booking data
            if not is_valid_tracking_data(booking_details):
                return {"status": "not_found", "tracking_number": tracking_number}

            result["data"]["booking_details"] = booking_details

            # 2. Shipment Track Summary
            summary_section = await page.query_selector('div.bg-\\[\\#f8f8f8\\]:has-text("Shipment Track Summary")')
            if summary_section:
                result["data"]["track_summary"] = {
                    "current_status": await get_text_async(summary_section, 'span.text-\\[\\#f0575d\\]'),
                    "delivered_on": await get_text_async(summary_section, 'div.flex:has-text("Delivered On") p.font-bold'),
                    "received_by": await get_text_async(summary_section, 'div.flex:has-text("Received by") p.font-bold')
                }

            # 3. Status History
            history_table = await page.query_selector('table:has-text("Date Time")')
            if history_table:
                status_history = []
                rows = await history_table.query_selector_all('tbody tr')
                for row in rows:
                    cells = await row.query_selector_all('td')
                    if len(cells) >= 2:
                        date_time = await cells[0].inner_text()
                        status = await cells[1].inner_text()
                        status_history.append({
                            "date_time": date_time.replace('\n', ' ').strip(),
                            "status": status.replace('\n', ' ').strip(),
                            "timestamp": datetime.now().isoformat()
                        })
                result["data"]["status_history"] = status_history

            # Calculate duration
            result["scrape_duration"] = str(datetime.now() - start_time)

            return result

        except Exception as e:
            logging.error(f"Error scraping TCS tracking for {tracking_number}: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "tracking_number": tracking_number,
                "timestamp": datetime.now().isoformat()
            }
        finally:
            await context.close()
            await browser.close()

async def get_text_async(element, selector, default=""):
    """Async helper function to safely extract text"""
    try:
        found = await element.query_selector(selector)
        text = await found.inner_text() if found else default
        text = text.strip()
        # Return empty string if text is just whitespace or common empty indicators
        if text.lower() in ['n/a', 'na', '-', '--', '---', 'null', 'none', '']:
            return ""
        return text
    except Exception:
        return default

@tracking_bp.route('/track')
def track_form():
    """Display the tracking form"""
    return render_template('tracking/track_form.html')

@tracking_bp.route('/track/<tracking_number>')
def track_number(tracking_number):
    """Track a specific tracking number via URL parameter"""
    if not tracking_number or not tracking_number.strip():
        return jsonify({
            "status": "error",
            "error": "Invalid tracking number provided",
            "timestamp": datetime.now().isoformat()
        }), 400

    try:
        result = scrape_tcs_tracking(tracking_number.strip())
        return jsonify(result)
    except Exception as e:
        logging.error(f"Error in track_number route: {str(e)}")
        return jsonify({
            "status": "error",
            "error": "Internal server error occurred while tracking",
            "timestamp": datetime.now().isoformat()
        }), 500

@tracking_bp.route('/track', methods=['POST'])
def track_post():
    """Handle POST requests for tracking"""
    try:
        # Handle both form data and JSON
        if request.is_json:
            data = request.get_json()
            tracking_number = data.get('tracking_number', '').strip()
        else:
            tracking_number = request.form.get('tracking_number', '').strip()
        
        if not tracking_number:
            return jsonify({
                "status": "error",
                "error": "Tracking number is required",
                "timestamp": datetime.now().isoformat()
            }), 400

        result = scrape_tcs_tracking(tracking_number)

        # If it's a form submission, render the result page
        if not request.is_json:
            return render_template('tracking/track_result.html', result=result)

        # Otherwise return JSON
        return jsonify(result)

    except Exception as e:
        logging.error(f"Error in track_post route: {str(e)}")
        error_response = {
            "status": "error",
            "error": "Internal server error occurred while tracking",
            "timestamp": datetime.now().isoformat()
        }

        if request.is_json:
            return jsonify(error_response), 500
        else:
            return render_template('tracking/track_result.html', result=error_response)

@tracking_bp.route('/api/track/bulk', methods=['POST'])
def bulk_track():
    """Handle bulk tracking requests"""
    try:
        if not request.is_json:
            return jsonify({
                "status": "error",
                "error": "Content-Type must be application/json",
                "timestamp": datetime.now().isoformat()
            }), 400
        
        data = request.get_json()
        tracking_numbers = data.get('tracking_numbers', [])
        
        if not tracking_numbers or not isinstance(tracking_numbers, list):
            return jsonify({
                "status": "error",
                "error": "tracking_numbers array is required",
                "timestamp": datetime.now().isoformat()
            }), 400
        
        if len(tracking_numbers) > 10:  # Limit bulk requests
            return jsonify({
                "status": "error",
                "error": "Maximum 10 tracking numbers allowed per bulk request",
                "timestamp": datetime.now().isoformat()
            }), 400
        
        results = []
        for tracking_number in tracking_numbers:
            if tracking_number and tracking_number.strip():
                result = scrape_tcs_tracking(tracking_number.strip())
                results.append(result)
                time.sleep(1)  # Add delay to avoid rate limiting
        
        # Enhanced summary statistics with proper classification
        found_count = 0
        not_found_count = 0
        error_count = 0

        for result in results:
            if result.get("status") == "error":
                error_count += 1
            elif result.get("status") == "success":
                # Check if we have meaningful data
                if has_meaningful_tracking_data(result.get("data", {})):
                    found_count += 1
                else:
                    not_found_count += 1
            else:  # status == "not_found"
                not_found_count += 1

        return jsonify({
            "status": "completed",
            "summary": {
                "total_processed": len(results),
                "found": found_count,
                "successful": found_count,  # Legacy compatibility
                "not_found": not_found_count,
                "errors": error_count
            },
            "results": results,
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        logging.error(f"Error in bulk_track route: {str(e)}")
        return jsonify({
            "status": "error",
            "error": "Internal server error occurred during bulk tracking",
            "timestamp": datetime.now().isoformat()
        }), 500

@tracking_bp.route('/api/track/bulk/async', methods=['POST'])
def bulk_track_async():
    """Handle async bulk tracking requests for better performance"""
    try:
        if not request.is_json:
            return jsonify({
                "status": "error",
                "error": "Content-Type must be application/json",
                "timestamp": datetime.now().isoformat()
            }), 400

        data = request.get_json()
        tracking_numbers = data.get('tracking_numbers', [])

        if not tracking_numbers or not isinstance(tracking_numbers, list):
            return jsonify({
                "status": "error",
                "error": "tracking_numbers array is required",
                "timestamp": datetime.now().isoformat()
            }), 400

        if len(tracking_numbers) > 10:  # Limit bulk requests
            return jsonify({
                "status": "error",
                "error": "Maximum 10 tracking numbers allowed per bulk request",
                "timestamp": datetime.now().isoformat()
            }), 400

        # Run async bulk tracking
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            results = loop.run_until_complete(process_bulk_tracking_async(tracking_numbers))
        finally:
            loop.close()

        # Enhanced summary statistics with proper classification
        found_count = 0
        not_found_count = 0
        error_count = 0

        for result in results:
            if result.get("status") == "error":
                error_count += 1
            elif result.get("status") == "success":
                # Check if we have meaningful data
                if has_meaningful_tracking_data(result.get("data", {})):
                    found_count += 1
                else:
                    not_found_count += 1
            else:  # status == "not_found"
                not_found_count += 1

        return jsonify({
            "status": "completed",
            "summary": {
                "total_processed": len(results),
                "found": found_count,
                "successful": found_count,  # Legacy compatibility
                "not_found": not_found_count,
                "errors": error_count
            },
            "results": results,
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        logging.error(f"Error in async bulk_track route: {str(e)}")
        return jsonify({
            "status": "error",
            "error": "Internal server error occurred during async bulk tracking",
            "timestamp": datetime.now().isoformat()
        }), 500

async def process_bulk_tracking_async(tracking_numbers):
    """Process multiple tracking numbers concurrently using asyncio"""
    # Create semaphore to limit concurrent requests (avoid overwhelming the server)
    semaphore = asyncio.Semaphore(3)  # Max 3 concurrent requests

    async def track_with_semaphore(tracking_number):
        async with semaphore:
            return await scrape_tcs_tracking_async(tracking_number)

    # Create tasks for all tracking numbers
    tasks = [track_with_semaphore(num.strip()) for num in tracking_numbers if num.strip()]

    # Execute all tasks concurrently
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # Handle any exceptions
    processed_results = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            processed_results.append({
                "status": "error",
                "error": str(result),
                "tracking_number": tracking_numbers[i],
                "timestamp": datetime.now().isoformat()
            })
        else:
            processed_results.append(result)

    return processed_results

def generate_tracking_pdf(tracking_data, is_bulk=False):
    """Generate PDF report for tracking data"""
    buffer = BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=18)

    # Container for the 'Flowable' objects
    elements = []
    styles = getSampleStyleSheet()

    # Custom styles
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=24,
        spaceAfter=30,
        alignment=TA_CENTER,
        textColor=colors.HexColor('#2c3e50')
    )

    company_style = ParagraphStyle(
        'CompanyStyle',
        parent=styles['Normal'],
        fontSize=14,
        spaceAfter=20,
        alignment=TA_CENTER,
        textColor=colors.HexColor('#34495e')
    )

    # Header
    elements.append(Paragraph("Medivent Pharmaceuticals", company_style))
    elements.append(Paragraph("TCS Express Tracking Report", title_style))
    elements.append(Spacer(1, 20))

    if is_bulk:
        # Bulk tracking report
        elements.append(Paragraph(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", styles['Normal']))
        elements.append(Spacer(1, 20))

        # Summary table
        if 'summary' in tracking_data:
            summary_data = [
                ['Summary', 'Count'],
                ['Total Processed', str(tracking_data['summary']['total_processed'])],
                ['Successful', str(tracking_data['summary']['successful'])],
                ['Not Found', str(tracking_data['summary']['not_found'])],
                ['Errors', str(tracking_data['summary']['errors'])]
            ]

            summary_table = Table(summary_data, colWidths=[3*inch, 1*inch])
            summary_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#3498db')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 14),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            elements.append(summary_table)
            elements.append(Spacer(1, 30))

        # Individual results
        if 'results' in tracking_data:
            elements.append(Paragraph("Detailed Results", styles['Heading2']))
            elements.append(Spacer(1, 12))

            for i, result in enumerate(tracking_data['results'], 1):
                elements.append(Paragraph(f"{i}. Tracking Number: {result['tracking_number']}", styles['Heading3']))

                if result['status'] == 'success':
                    # Create table for successful tracking
                    data = [
                        ['Field', 'Value'],
                        ['Status', 'SUCCESS'],
                        ['Origin', result['data']['booking_details'].get('origin', 'Not Available')],
                        ['Destination', result['data']['booking_details'].get('destination', 'Not Available')],
                        ['Current Status', result['data']['track_summary'].get('current_status', 'Not Available')],
                        ['Booking Date', result['data']['booking_details'].get('booking_date', 'Not Available')]
                    ]
                elif result['status'] == 'not_found':
                    data = [
                        ['Field', 'Value'],
                        ['Status', 'NOT FOUND'],
                        ['Message', 'No record found for this tracking number']
                    ]
                else:
                    data = [
                        ['Field', 'Value'],
                        ['Status', 'ERROR'],
                        ['Error', result.get('error', 'Unknown error')]
                    ]

                table = Table(data, colWidths=[2*inch, 4*inch])
                table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#34495e')),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))

                elements.append(table)
                elements.append(Spacer(1, 20))

    else:
        # Single tracking report
        result = tracking_data
        elements.append(Paragraph(f"Tracking Number: {result['tracking_number']}", styles['Heading2']))
        elements.append(Paragraph(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", styles['Normal']))
        elements.append(Spacer(1, 20))

        if result['status'] == 'success':
            # Booking details
            booking_details = result.get('data', {}).get('booking_details', {})
            booking_data = [
                ['Booking Details', ''],
                ['Tracking Number', booking_details.get('tracking_number', 'Not Available')],
                ['Agent Reference', booking_details.get('agent_reference', 'Not Available') if booking_details.get('agent_reference') else 'Not Available'],
                ['Origin', booking_details.get('origin', 'Not Available') if booking_details.get('origin') else 'Not Available'],
                ['Destination', booking_details.get('destination', 'Not Available') if booking_details.get('destination') else 'Not Available'],
                ['Booking Date', booking_details.get('booking_date', 'Not Available') if booking_details.get('booking_date') else 'Not Available']
            ]

            booking_table = Table(booking_data, colWidths=[2*inch, 4*inch])
            booking_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#3498db')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 14),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            elements.append(booking_table)
            elements.append(Spacer(1, 20))

            # Current status
            track_summary = result.get('data', {}).get('track_summary', {})
            status_data = [
                ['Current Status', ''],
                ['Status', track_summary.get('current_status', 'Not Available') if track_summary.get('current_status') else 'Not Available'],
                ['Delivered On', track_summary.get('delivered_on', 'Not Delivered Yet') if track_summary.get('delivered_on') and track_summary.get('delivered_on') != 'N/A' else 'Not Delivered Yet'],
                ['Received By', track_summary.get('received_by', 'Not Available') if track_summary.get('received_by') and track_summary.get('received_by') != 'N/A' else 'Not Available']
            ]

            status_table = Table(status_data, colWidths=[2*inch, 4*inch])
            status_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#27ae60')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 14),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            elements.append(status_table)
            elements.append(Spacer(1, 20))

            # Status history
            if 'status_history' in result['data'] and result['data']['status_history']:
                elements.append(Paragraph("Status History", styles['Heading2']))
                elements.append(Spacer(1, 12))

                history_data = [['#', 'Date & Time', 'Status']]
                for i, item in enumerate(result['data']['status_history'], 1):
                    history_data.append([str(i), item['date_time'], item['status']])

                history_table = Table(history_data, colWidths=[0.5*inch, 2*inch, 3.5*inch])
                history_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#34495e')),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))

                elements.append(history_table)

        elif result['status'] == 'not_found':
            elements.append(Paragraph("Status: NOT FOUND", styles['Heading2']))
            elements.append(Paragraph("No record found for this tracking number.", styles['Normal']))

        else:
            elements.append(Paragraph("Status: ERROR", styles['Heading2']))
            elements.append(Paragraph(f"Error: {result.get('error', 'Unknown error')}", styles['Normal']))

    # Build PDF
    doc.build(elements)
    buffer.seek(0)
    return buffer

@tracking_bp.route('/api/track/pdf', methods=['GET', 'POST'])
def generate_single_pdf():
    """Generate PDF for single tracking result"""
    # Handle GET requests - redirect to tracking form
    if request.method == 'GET':
        return redirect(url_for('tracking.track_form'))

    try:
        tracking_data_str = request.form.get('tracking_data', '{}')
        logging.info(f"Received tracking data for PDF: {tracking_data_str[:100]}...")

        tracking_data = json.loads(tracking_data_str)

        if not tracking_data:
            logging.error("No tracking data provided for PDF generation")
            return jsonify({"error": "No tracking data provided"}), 400

        logging.info(f"Generating PDF for tracking number: {tracking_data.get('tracking_number', 'unknown')}")
        pdf_buffer = generate_tracking_pdf(tracking_data, is_bulk=False)

        response = make_response(pdf_buffer.getvalue())
        response.headers['Content-Type'] = 'application/pdf'
        response.headers['Content-Disposition'] = f'attachment; filename=tcs_tracking_{tracking_data.get("tracking_number", "unknown")}_{datetime.now().strftime("%Y%m%d")}.pdf'

        logging.info("PDF generated successfully")
        return response

    except json.JSONDecodeError as e:
        logging.error(f"JSON decode error in PDF generation: {str(e)}")
        return jsonify({"error": "Invalid JSON data provided"}), 400
    except Exception as e:
        logging.error(f"Error generating single PDF: {str(e)}")
        import traceback
        logging.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({"error": f"Failed to generate PDF: {str(e)}"}), 500

@tracking_bp.route('/api/track/bulk/pdf', methods=['POST'])
def generate_bulk_pdf():
    """Generate PDF for bulk tracking results"""
    try:
        tracking_data_str = request.form.get('tracking_data', '{}')
        logging.info(f"Received bulk tracking data for PDF: {tracking_data_str[:100]}...")

        tracking_data = json.loads(tracking_data_str)

        if not tracking_data:
            logging.error("No tracking data provided for bulk PDF generation")
            return jsonify({"error": "No tracking data provided"}), 400

        logging.info(f"Generating bulk PDF for {len(tracking_data.get('results', []))} results")
        pdf_buffer = generate_tracking_pdf(tracking_data, is_bulk=True)

        response = make_response(pdf_buffer.getvalue())
        response.headers['Content-Type'] = 'application/pdf'
        response.headers['Content-Disposition'] = f'attachment; filename=tcs_bulk_tracking_{datetime.now().strftime("%Y%m%d")}.pdf'

        logging.info("Bulk PDF generated successfully")
        return response

    except json.JSONDecodeError as e:
        logging.error(f"JSON decode error in bulk PDF generation: {str(e)}")
        return jsonify({"error": "Invalid JSON data provided"}), 400
    except Exception as e:
        logging.error(f"Error generating bulk PDF: {str(e)}")
        import traceback
        logging.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({"error": f"Failed to generate PDF: {str(e)}"}), 500
