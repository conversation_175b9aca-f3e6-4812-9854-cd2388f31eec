"""
User Management Routes for Medivent Pharmaceuticals Web Portal
"""

from flask import Blueprint, render_template, redirect, url_for, flash, request, g, jsonify, current_app
from flask_login import login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import sqlite3
from utils.permissions import get_all_permissions, get_role_permissions, update_role_permissions, sync_default_role_permissions, permission_required, has_permission

# Create blueprint
users_bp = Blueprint('users', __name__)

# Helper function to get database connection
def get_db():
    if 'db' not in g:
        g.db = sqlite3.connect(current_app.config['DATABASE'])
        g.db.row_factory = sqlite3.Row
    return g.db

# Helper function to log user activity
def log_activity(username, action, entity_id=None, details=None):
    db = get_db()
    db.execute(
        """INSERT INTO activity_logs
           (timestamp, username, action, entity_id, details, ip_address)
           VALUES (?, ?, ?, ?, ?, ?)""",
        (datetime.now(), username, action, entity_id, details, request.remote_addr)
    )
    db.commit()

# User Management main page
@users_bp.route('/manage')
@login_required
@permission_required('user_view')
def manage():
    """User management main page"""
    # Get all users
    db = get_db()
    users = db.execute('SELECT * FROM users ORDER BY username').fetchall()

    return render_template('users/manage.html', users=users, now=datetime.now())

# Add new user
@users_bp.route('/add', methods=['GET', 'POST'])
@login_required
@permission_required('user_create')
def add():
    """Add new user"""

    if request.method == 'POST':
        # Get form data
        username = request.form.get('username')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')
        full_name = request.form.get('full_name', '')
        email = request.form.get('email', '')
        role = request.form.get('role', 'user')
        status = request.form.get('status', 'active')

        # Validate form data
        if not username or not password or not confirm_password:
            flash('Username and password are required', 'danger')
            return redirect(url_for('users.add'))

        if password != confirm_password:
            flash('Passwords do not match', 'danger')
            return redirect(url_for('users.add'))

        if len(password) < 8:
            flash('Password must be at least 8 characters long', 'danger')
            return redirect(url_for('users.add'))

        # Check if username already exists
        db = get_db()
        existing_user = db.execute('SELECT * FROM users WHERE username = ?', (username,)).fetchone()
        if existing_user:
            flash('Username already exists', 'danger')
            return redirect(url_for('users.add'))

        # Create new user
        password_hash = generate_password_hash(password)
        db.execute(
            """INSERT INTO users
               (username, password_hash, full_name, email, role, status, created_at, updated_at)
               VALUES (?, ?, ?, ?, ?, ?, ?, ?)""",
            (username, password_hash, full_name, email, role, status, datetime.now(), datetime.now())
        )
        db.commit()

        # Log activity
        log_activity(
            current_user.username,
            'User Created',
            username,
            f"Created new user: {username} with role: {role}"
        )

        # Create notification for new user
        try:
            from app import create_notification
            create_notification(
                title=f'New User Created - {username}',
                message=f'User "{username}" has been created by {current_user.username} with role: {role}',
                type_='success',
                entity_type='user',
                entity_id=str(user_id),
                action_url=f'/users/edit/{user_id}',
                user_id=None  # Broadcast to all users
            )
        except Exception as e:
            print(f"Error creating user notification: {e}")

        flash(f'User {username} created successfully', 'success')
        return redirect(url_for('users.manage'))

    return render_template('users/add.html', now=datetime.now())

# Edit user
@users_bp.route('/edit/<int:user_id>', methods=['GET', 'POST'])
@login_required
@permission_required('user_edit')
def edit(user_id):
    """Edit user"""

    # Get user data
    db = get_db()
    user = db.execute('SELECT * FROM users WHERE id = ?', (user_id,)).fetchone()
    if not user:
        flash('User not found', 'danger')
        return redirect(url_for('users.manage'))

    if request.method == 'POST':
        # Get form data
        full_name = request.form.get('full_name', '')
        email = request.form.get('email', '')
        role = request.form.get('role', 'user')
        status = request.form.get('status', 'active')

        # Prevent changing admin user's role or status
        if user['username'] == 'admin':
            role = 'admin'
            status = 'active'

        # Update user
        db.execute(
            """UPDATE users
               SET full_name = ?, email = ?, role = ?, status = ?, updated_at = ?
               WHERE id = ?""",
            (full_name, email, role, status, datetime.now(), user_id)
        )
        db.commit()

        # Log activity
        log_activity(
            current_user.username,
            'User Updated',
            user['username'],
            f"Updated user: {user['username']} with role: {role}, status: {status}"
        )

        flash(f'User {user["username"]} updated successfully', 'success')
        return redirect(url_for('users.manage'))

    return render_template('users/edit.html', user=user, now=datetime.now())

# Delete user
@users_bp.route('/delete/<int:user_id>')
@login_required
@permission_required('user_delete')
def delete(user_id):
    """Delete user"""

    # Get user data
    db = get_db()
    user = db.execute('SELECT * FROM users WHERE id = ?', (user_id,)).fetchone()
    if not user:
        flash('User not found', 'danger')
        return redirect(url_for('users.manage'))

    # Prevent deleting admin user or current user
    if user['username'] == 'admin' or user['username'] == current_user.username:
        flash('Cannot delete admin user or your own account', 'danger')
        return redirect(url_for('users.manage'))

    # Delete user
    db.execute('DELETE FROM users WHERE id = ?', (user_id,))
    db.commit()

    # Log activity
    log_activity(
        current_user.username,
        'User Deleted',
        user['username'],
        f"Deleted user: {user['username']}"
    )

    flash(f'User {user["username"]} deleted successfully', 'success')
    return redirect(url_for('users.manage'))

# Toggle user status
@users_bp.route('/toggle-status/<int:user_id>')
@login_required
@permission_required('user_activate')
def toggle_status(user_id):
    """Toggle user status (active/inactive)"""

    # Get user data
    db = get_db()
    user = db.execute('SELECT * FROM users WHERE id = ?', (user_id,)).fetchone()
    if not user:
        flash('User not found', 'danger')
        return redirect(url_for('users.manage'))

    # Prevent toggling admin user
    if user['username'] == 'admin':
        flash('Cannot change status of admin user', 'danger')
        return redirect(url_for('users.manage'))

    # Toggle status
    new_status = 'inactive' if user['status'] == 'active' else 'active'
    db.execute(
        """UPDATE users
           SET status = ?, updated_at = ?
           WHERE id = ?""",
        (new_status, datetime.now(), user_id)
    )
    db.commit()

    # Log activity
    log_activity(
        current_user.username,
        'User Status Changed',
        user['username'],
        f"Changed status of user: {user['username']} to {new_status}"
    )

    flash(f'User {user["username"]} status changed to {new_status}', 'success')
    return redirect(url_for('users.manage'))

# Reset user password
@users_bp.route('/reset-password/<int:user_id>', methods=['GET', 'POST'])
@login_required
def reset_password(user_id):
    """Reset user password"""
    # Check if user has permission to reset passwords or is resetting their own password
    if not has_permission('user_reset_password') and current_user.id != user_id:
        flash('Access denied. You can only reset your own password.', 'danger')
        return redirect(url_for('dashboard'))

    # Get user data
    db = get_db()
    user = db.execute('SELECT * FROM users WHERE id = ?', (user_id,)).fetchone()
    if not user:
        flash('User not found', 'danger')
        return redirect(url_for('users.manage'))

    if request.method == 'POST':
        # Get form data
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')
        current_password = request.form.get('current_password')

        # Validate form data
        if not new_password or not confirm_password:
            flash('New password and confirmation are required', 'danger')
            return redirect(url_for('users.reset_password', user_id=user_id))

        if new_password != confirm_password:
            flash('Passwords do not match', 'danger')
            return redirect(url_for('users.reset_password', user_id=user_id))

        if len(new_password) < 8:
            flash('Password must be at least 8 characters long', 'danger')
            return redirect(url_for('users.reset_password', user_id=user_id))

        # If user is resetting their own password, verify current password
        if current_user.id == user_id:
            if not current_password:
                flash('Current password is required', 'danger')
                return redirect(url_for('users.reset_password', user_id=user_id))

            if not check_password_hash(user['password_hash'], current_password):
                flash('Current password is incorrect', 'danger')
                return redirect(url_for('users.reset_password', user_id=user_id))

        # Update password
        password_hash = generate_password_hash(new_password)
        db.execute(
            """UPDATE users
               SET password_hash = ?, updated_at = ?
               WHERE id = ?""",
            (password_hash, datetime.now(), user_id)
        )
        db.commit()

        # Log activity
        log_activity(
            current_user.username,
            'Password Reset',
            user['username'],
            f"Reset password for user: {user['username']}"
        )

        flash(f'Password for {user["username"]} reset successfully', 'success')

        # Redirect to appropriate page
        if current_user.role == 'admin' and current_user.id != user_id:
            return redirect(url_for('users.manage'))
        else:
            return redirect(url_for('dashboard'))

    return render_template('users/reset_password.html', user=user, now=datetime.now())

# User roles management
@users_bp.route('/roles')
@login_required
@permission_required('role_manage')
def roles():
    """User roles management"""
    # Get role counts
    db = get_db()
    cursor = db.cursor()

    # Get all roles
    roles = ['admin', 'manager', 'sales', 'warehouse', 'rider', 'customer', 'user']
    role_counts = {}
    role_permission_counts = {}

    for role in roles:
        # Get user count for this role
        count = cursor.execute('SELECT COUNT(*) as count FROM users WHERE role = ?', (role,)).fetchone()['count']
        role_counts[role] = count

        # Get permission count for this role
        perm_count = cursor.execute('SELECT COUNT(*) as count FROM role_permissions WHERE role = ?', (role,)).fetchone()['count']
        role_permission_counts[role] = perm_count

    # Get total users
    total_users = cursor.execute('SELECT COUNT(*) as count FROM users').fetchone()['count']

    # Get permissions by category
    permission_categories = get_all_permissions()

    return render_template(
        'users/roles.html',
        role_counts=role_counts,
        total_users=total_users,
        role_permission_counts=role_permission_counts,
        permission_categories=permission_categories,
        now=datetime.now()
    )

# Edit role permissions
@users_bp.route('/permissions/<role>', methods=['GET', 'POST'])
@login_required
@permission_required('role_manage')
def permissions(role):
    """Edit permissions for a role"""
    # Get all permissions grouped by category
    permissions = get_all_permissions()

    # Get current permissions for the role
    role_permissions = get_role_permissions(role)

    if request.method == 'POST':
        # Get selected permissions
        selected_permissions = request.form.getlist('permissions')

        # Convert to integers
        permission_ids = [int(p) for p in selected_permissions]

        # Update permissions
        if update_role_permissions(role, permission_ids):
            # Log activity
            log_activity(
                current_user.username,
                'Role Permissions Updated',
                role,
                f"Updated permissions for role: {role}"
            )

            flash(f'Permissions for role {role} updated successfully', 'success')
        else:
            flash(f'Error updating permissions for role {role}', 'danger')

        return redirect(url_for('users.roles'))

    return render_template(
        'users/permissions.html',
        role=role,
        permissions=permissions,
        role_permissions=role_permissions,
        now=datetime.now()
    )

# Sync default permissions
@users_bp.route('/permissions/sync', methods=['POST'])
@login_required
@permission_required('role_manage')
def sync_permissions():
    """Sync default permissions for all roles"""
    if sync_default_role_permissions():
        # Log activity
        log_activity(
            current_user.username,
            'Default Permissions Synced',
            None,
            "Synced default permissions for all roles"
        )

        return jsonify({'success': True})
    else:
        return jsonify({'success': False, 'message': 'Error syncing default permissions'})

# Restore default permissions for a specific role
@users_bp.route('/roles/<role>/default-permissions', methods=['POST'])
@login_required
@permission_required('role_manage')
def restore_default_permissions(role):
    """Restore default permissions for a specific role"""
    from utils.permissions import get_default_role_permissions, update_role_permissions

    # Get default permissions for the role
    default_permissions = get_default_role_permissions(role)

    if not default_permissions:
        return jsonify({'success': False, 'message': f'No default permissions defined for role: {role}'})

    # Update the role permissions
    if update_role_permissions(role, default_permissions):
        # Log activity
        log_activity(
            current_user.username,
            'Default Permissions Restored',
            role,
            f"Restored default permissions for role: {role}"
        )

        return jsonify({'success': True})
    else:
        return jsonify({'success': False, 'message': f'Error restoring default permissions for role: {role}'})

# View user activity logs
@users_bp.route('/logs')
@login_required
@permission_required('logs_view')
def logs():
    """View comprehensive user activity logs with session tracking"""

    db = get_db()

    # Get traditional activity logs
    activity_logs = db.execute(
        """SELECT * FROM activity_logs
           ORDER BY timestamp DESC
           LIMIT 1000"""
    ).fetchall()

    # Initialize default values for new features
    user_sessions = []
    user_activities = []

    # Get user sessions with activity tracking (if table exists)
    try:
        user_sessions = db.execute(
            """SELECT us.*, u.full_name
               FROM user_sessions us
               LEFT JOIN users u ON us.user_id = u.id
               ORDER BY us.login_time DESC
               LIMIT 100"""
        ).fetchall()
        print(f"Found {len(user_sessions)} user sessions")
    except Exception as e:
        print(f"Error fetching user sessions: {e}")
        user_sessions = []

    # Get recent user activity tracking (if table exists)
    try:
        user_activities = db.execute(
            """SELECT uat.*, u.full_name
               FROM user_activity_tracking uat
               LEFT JOIN users u ON uat.user_id = u.id
               ORDER BY uat.timestamp DESC
               LIMIT 500"""
        ).fetchall()
        print(f"Found {len(user_activities)} user activities")
    except Exception as e:
        print(f"Error fetching user activities: {e}")
        user_activities = []

    # Get unique usernames for filtering
    usernames = set()
    for log in activity_logs:
        if log['username']:
            usernames.add(log['username'])
    for session in user_sessions:
        if session['username']:
            usernames.add(session['username'])

    # Get unique actions for filtering
    actions = set()
    for log in activity_logs:
        if log['action']:
            actions.add(log['action'])

    # Calculate session statistics with error handling
    try:
        # Initialize default session stats
        session_stats = {
            'total_sessions': 0,
            'active_sessions': 0,
            'total_screen_time': 0,
            'total_active_time': 0,
            'total_idle_time': 0,
            'average_session_duration': 0
        }

        if user_sessions:
            session_stats['total_sessions'] = len(user_sessions)

            # Count active sessions safely
            active_count = 0
            total_screen_time = 0
            total_active_time = 0
            total_idle_time = 0
            total_duration = 0

            for session in user_sessions:
                try:
                    # Check session status
                    if session.get('status') == 'active':
                        active_count += 1

                    # Sum time values safely
                    total_screen_time += session.get('total_screen_time') or 0
                    total_active_time += session.get('total_active_time') or 0
                    total_idle_time += session.get('total_idle_time') or 0
                    total_duration += session.get('session_duration') or 0

                except Exception as e:
                    print(f"Error processing session {session.get('id', 'unknown')}: {e}")
                    continue

            session_stats.update({
                'active_sessions': active_count,
                'total_screen_time': total_screen_time,
                'total_active_time': total_active_time,
                'total_idle_time': total_idle_time,
                'average_session_duration': total_duration / len(user_sessions) if len(user_sessions) > 0 else 0
            })

        print(f"Session stats calculated: {session_stats}")

    except Exception as e:
        print(f"Error calculating session statistics: {e}")
        # Fallback to safe defaults
        session_stats = {
            'total_sessions': 0,
            'active_sessions': 0,
            'total_screen_time': 0,
            'total_active_time': 0,
            'total_idle_time': 0,
            'average_session_duration': 0
        }

    return render_template('users/logs.html',
                         logs=activity_logs,
                         user_sessions=user_sessions,
                         user_activities=user_activities,
                         usernames=sorted(usernames),
                         actions=sorted(actions),
                         session_stats=session_stats,
                         now=datetime.now())
