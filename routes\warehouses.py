"""
Warehouse Management Routes
Handles CRUD operations for warehouses with image upload and real-time updates
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, g
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
import os
import uuid
import sqlite3
from datetime import datetime

# Create blueprint
warehouses_bp = Blueprint('warehouses', __name__)

def get_db():
    """Get database connection"""
    db = getattr(g, '_database', None)
    if db is None:
        db = g._database = sqlite3.connect('instance/medivent.db')
        db.row_factory = sqlite3.Row
    return db

# Allowed image extensions
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def generate_warehouse_id():
    """Generate unique warehouse ID"""
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    return f"WH{timestamp}"

@warehouses_bp.route('/add', methods=['GET', 'POST'])
@login_required
def add_warehouse():
    """Add new warehouse with image upload"""
    if request.method == 'POST':
        try:
            db = get_db()
            
            # Get form data
            warehouse_data = {
                'name': request.form.get('name', '').strip(),
                'address': request.form.get('address', '').strip(),
                'city': request.form.get('city', '').strip(),
                'country': request.form.get('country', '').strip(),
                'capacity': request.form.get('capacity'),
                'manager': request.form.get('manager', '').strip(),
                'phone': request.form.get('phone', '').strip(),
                'email': request.form.get('email', '').strip(),
                'description': request.form.get('description', '').strip()
            }
            
            # Validate required fields
            if not warehouse_data['name']:
                flash('Warehouse name is required', 'danger')
                return redirect('/warehouse-management/add')

            if not warehouse_data['city']:
                flash('City is required', 'danger')
                return redirect('/warehouse-management/add')
            
            # Handle image upload
            image_path = None
            if 'warehouse_image' in request.files:
                file = request.files['warehouse_image']
                if file and file.filename and allowed_file(file.filename):
                    # Create uploads directory if it doesn't exist
                    upload_dir = os.path.join('static', 'uploads', 'warehouses')
                    os.makedirs(upload_dir, exist_ok=True)
                    
                    # Generate unique filename
                    filename = secure_filename(file.filename)
                    unique_filename = f"{uuid.uuid4().hex}_{filename}"
                    file_path = os.path.join(upload_dir, unique_filename)
                    
                    # Save file
                    file.save(file_path)
                    image_path = f"uploads/warehouses/{unique_filename}"
            
            # Generate warehouse ID
            warehouse_id = generate_warehouse_id()
            
            # Begin transaction
            db.execute('BEGIN TRANSACTION')
            
            # Insert warehouse
            db.execute('''
                INSERT INTO warehouses (
                    warehouse_id, name, address, city, country, capacity,
                    manager, phone, email, description, image_path, status,
                    created_at, created_by, updated_at, updated_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                warehouse_id,
                warehouse_data['name'],
                warehouse_data['address'],
                warehouse_data['city'],
                warehouse_data['country'],
                int(warehouse_data['capacity']) if warehouse_data['capacity'] else None,
                warehouse_data['manager'],
                warehouse_data['phone'],
                warehouse_data['email'],
                warehouse_data['description'],
                image_path,
                'active',
                datetime.now(),
                current_user.username,
                datetime.now(),
                current_user.username
            ))
            
            # Commit transaction
            db.execute('COMMIT')
            
            flash(f'Warehouse "{warehouse_data["name"]}" created successfully!', 'success')
            return redirect('/warehouse-management/manage')
            
        except Exception as e:
            # Rollback on error
            try:
                db.execute('ROLLBACK')
            except:
                pass
            flash(f'Error creating warehouse: {str(e)}', 'danger')
            return redirect('/warehouse-management/add')
    
    # GET request - show form
    return render_template('warehouses/add.html')

@warehouses_bp.route('/manage')
@login_required
def manage_warehouses():
    """Manage warehouses - list all warehouses"""
    try:
        db = get_db()
        
        # Get all warehouses
        cursor = db.execute('''
            SELECT * FROM warehouses 
            ORDER BY created_at DESC
        ''')
        warehouses = cursor.fetchall()
        
        return render_template('warehouses/manage.html', warehouses=warehouses)
        
    except Exception as e:
        flash(f'Error loading warehouses: {str(e)}', 'danger')
        return redirect('/dashboard')

@warehouses_bp.route('/edit/<warehouse_id>', methods=['GET', 'POST'])
@login_required
def edit_warehouse(warehouse_id):
    """Edit warehouse"""
    try:
        db = get_db()

        if request.method == 'POST':
            # Get form data
            warehouse_data = {
                'name': request.form.get('name', '').strip(),
                'address': request.form.get('address', '').strip(),
                'city': request.form.get('city', '').strip(),
                'country': request.form.get('country', '').strip(),
                'capacity': request.form.get('capacity'),
                'manager': request.form.get('manager', '').strip(),
                'phone': request.form.get('phone', '').strip(),
                'email': request.form.get('email', '').strip(),
                'description': request.form.get('description', '').strip(),
                'status': request.form.get('status', 'active')
            }

            # Validate required fields
            if not warehouse_data['name']:
                flash('Warehouse name is required', 'danger')
                return redirect(f'/warehouse-management/edit/{warehouse_id}')

            if not warehouse_data['city']:
                flash('City is required', 'danger')
                return redirect(f'/warehouse-management/edit/{warehouse_id}')

            # Handle image upload
            image_path = None
            if 'warehouse_image' in request.files:
                file = request.files['warehouse_image']
                if file and file.filename and allowed_file(file.filename):
                    # Create uploads directory if it doesn't exist
                    upload_dir = os.path.join('static', 'uploads', 'warehouses')
                    os.makedirs(upload_dir, exist_ok=True)

                    # Generate unique filename
                    filename = secure_filename(file.filename)
                    unique_filename = f"{uuid.uuid4().hex}_{filename}"
                    file_path = os.path.join(upload_dir, unique_filename)

                    # Save file
                    file.save(file_path)
                    image_path = f"uploads/warehouses/{unique_filename}"

            # Begin transaction
            db.execute('BEGIN TRANSACTION')

            # Update warehouse
            if image_path:
                db.execute('''
                    UPDATE warehouses SET
                        name = ?, address = ?, city = ?, country = ?, capacity = ?,
                        manager = ?, phone = ?, email = ?, description = ?, status = ?,
                        image_path = ?, updated_at = ?, updated_by = ?
                    WHERE warehouse_id = ?
                ''', (
                    warehouse_data['name'], warehouse_data['address'], warehouse_data['city'],
                    warehouse_data['country'], int(warehouse_data['capacity']) if warehouse_data['capacity'] else None,
                    warehouse_data['manager'], warehouse_data['phone'], warehouse_data['email'],
                    warehouse_data['description'], warehouse_data['status'], image_path,
                    datetime.now(), current_user.username, warehouse_id
                ))
            else:
                db.execute('''
                    UPDATE warehouses SET
                        name = ?, address = ?, city = ?, country = ?, capacity = ?,
                        manager = ?, phone = ?, email = ?, description = ?, status = ?,
                        updated_at = ?, updated_by = ?
                    WHERE warehouse_id = ?
                ''', (
                    warehouse_data['name'], warehouse_data['address'], warehouse_data['city'],
                    warehouse_data['country'], int(warehouse_data['capacity']) if warehouse_data['capacity'] else None,
                    warehouse_data['manager'], warehouse_data['phone'], warehouse_data['email'],
                    warehouse_data['description'], warehouse_data['status'],
                    datetime.now(), current_user.username, warehouse_id
                ))

            # Commit transaction
            db.execute('COMMIT')

            flash(f'Warehouse "{warehouse_data["name"]}" updated successfully!', 'success')
            return redirect('/warehouse-management/manage')

        # GET request - show edit form
        cursor = db.execute('SELECT * FROM warehouses WHERE warehouse_id = ?', (warehouse_id,))
        warehouse = cursor.fetchone()

        if not warehouse:
            flash('Warehouse not found', 'danger')
            return redirect('/warehouse-management/manage')

        return render_template('warehouses/edit.html', warehouse=warehouse)

    except Exception as e:
        # Rollback on error
        try:
            db.execute('ROLLBACK')
        except:
            pass
        flash(f'Error updating warehouse: {str(e)}', 'danger')
        return redirect('/warehouse-management/manage')

@warehouses_bp.route('/delete/<warehouse_id>', methods=['POST'])
@login_required
def delete_warehouse(warehouse_id):
    """Delete warehouse"""
    try:
        db = get_db()
        
        # Check if warehouse is being used
        cursor = db.execute('SELECT COUNT(*) as count FROM inventory WHERE warehouse_id = ?', (warehouse_id,))
        usage_count = cursor.fetchone()['count']
        
        if usage_count > 0:
            flash(f'Cannot delete warehouse. It is being used by {usage_count} inventory items.', 'danger')
            return redirect('/warehouse-management/manage')

        # Delete warehouse
        db.execute('DELETE FROM warehouses WHERE warehouse_id = ?', (warehouse_id,))
        db.commit()

        flash('Warehouse deleted successfully!', 'success')
        return redirect('/warehouse-management/manage')
        
    except Exception as e:
        flash(f'Error deleting warehouse: {str(e)}', 'danger')
        return redirect('/warehouse-management/manage')

@warehouses_bp.route('/api/list')
@login_required
def api_list_warehouses():
    """API endpoint to get warehouses for forms"""
    try:
        db = get_db()
        cursor = db.execute('''
            SELECT warehouse_id, name, city, status 
            FROM warehouses 
            WHERE status = 'active'
            ORDER BY name
        ''')
        warehouses = [dict(row) for row in cursor.fetchall()]
        
        return jsonify({
            'success': True,
            'warehouses': warehouses
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
