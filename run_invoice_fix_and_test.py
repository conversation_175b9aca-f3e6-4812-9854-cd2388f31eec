#!/usr/bin/env python3
"""
Run the invoice generation fix and test it
"""

import subprocess
import sys
import os
import time

def run_command(command, description):
    """Run a command and return success status"""
    print(f"\n🔧 {description}")
    print("-" * 50)
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ SUCCESS")
            if result.stdout:
                print(result.stdout)
            return True
        else:
            print("❌ FAILED")
            if result.stderr:
                print(f"Error: {result.stderr}")
            if result.stdout:
                print(f"Output: {result.stdout}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def main():
    """Main execution function"""
    
    print("🚀 INVOICE GENERATION FIX AND TEST")
    print("=" * 60)
    
    # Check if database exists
    if not os.path.exists('instance/medivent.db'):
        print("❌ Database not found at instance/medivent.db")
        return
    
    # Step 1: Run the database fix
    success = run_command(
        "python fix_invoice_generation_error.py",
        "RUNNING DATABASE FIX"
    )
    
    if not success:
        print("❌ Database fix failed. Cannot proceed with testing.")
        return
    
    # Step 2: Check if Flask app is running
    print("\n🌐 CHECKING FLASK APPLICATION")
    print("-" * 50)
    
    try:
        import requests
        response = requests.get("http://127.0.0.1:5001", timeout=5)
        if response.status_code == 200:
            print("✅ Flask app is running")
            
            # Step 3: Run comprehensive test
            success = run_command(
                "python test_invoice_generation_comprehensive.py",
                "RUNNING COMPREHENSIVE INVOICE GENERATION TEST"
            )
            
            if success:
                print("\n🎉 ALL TESTS COMPLETED SUCCESSFULLY!")
            else:
                print("\n❌ Some tests failed. Check the output above.")
                
        else:
            print(f"❌ Flask app returned status {response.status_code}")
            print("Please start the Flask application first:")
            print("  python app.py")
            
    except requests.exceptions.ConnectionError:
        print("❌ Flask app is not running")
        print("Please start the Flask application first:")
        print("  python app.py")
        
    except Exception as e:
        print(f"❌ Error checking Flask app: {e}")

if __name__ == "__main__":
    main()
