-- Database Setup for Batch Selection System

-- Create batch_selections table
CREATE TABLE IF NOT EXISTS batch_selections (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_id TEXT NOT NULL,
    product_id TEXT NOT NULL,
    batch_number TEXT NOT NULL,
    warehouse_id TEXT NOT NULL,
    allocated_quantity REAL NOT NULL,
    selection_method TEXT DEFAULT 'manual',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    status TEXT DEFAULT 'pending'
);

-- Create dc_generation_sessions table
CREATE TABLE IF NOT EXISTS dc_generation_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id TEXT UNIQUE NOT NULL,
    order_id TEXT NOT NULL,
    status TEXT DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create orders table if not exists
CREATE TABLE IF NOT EXISTS orders (
    order_id TEXT PRIMARY KEY,
    customer_name TEXT NOT NULL,
    customer_phone TEXT,
    customer_address TEXT,
    order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    order_amount REAL DEFAULT 0,
    status TEXT DEFAULT 'Pending',
    approval_date TIMESTAMP
);

-- Create order_items table if not exists
CREATE TABLE IF NOT EXISTS order_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_id TEXT NOT NULL,
    product_id TEXT NOT NULL,
    quantity REAL NOT NULL,
    unit_price REAL NOT NULL,
    status TEXT DEFAULT 'Pending'
);

-- Create inventory table if not exists
CREATE TABLE IF NOT EXISTS inventory (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    product_id TEXT NOT NULL,
    warehouse_id TEXT NOT NULL,
    batch_number TEXT,
    manufacturing_date DATE,
    expiry_date DATE,
    stock_quantity REAL NOT NULL DEFAULT 0,
    allocated_quantity REAL DEFAULT 0,
    status TEXT DEFAULT 'active'
);

-- Create warehouses table if not exists
CREATE TABLE IF NOT EXISTS warehouses (
    warehouse_id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    city TEXT,
    status TEXT DEFAULT 'active'
);

-- Create products table if not exists
CREATE TABLE IF NOT EXISTS products (
    product_id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    strength TEXT,
    unit_of_measure TEXT,
    manufacturer TEXT
);

-- Add sample warehouses
INSERT OR REPLACE INTO warehouses (warehouse_id, name, city, status) VALUES ('WH001', 'Main Warehouse', 'Karachi', 'active');
INSERT OR REPLACE INTO warehouses (warehouse_id, name, city, status) VALUES ('WH002', 'Branch Warehouse', 'Lahore', 'active');

-- Add sample products
INSERT OR REPLACE INTO products (product_id, name, strength, unit_of_measure, manufacturer) VALUES ('PROD001', 'PANADOL 500MG', '500mg', 'Tablet', 'GSK');
INSERT OR REPLACE INTO products (product_id, name, strength, unit_of_measure, manufacturer) VALUES ('PROD002', 'AUGMENTIN 625MG', '625mg', 'Tablet', 'GSK');

-- Add sample approved orders (exact IDs from screenshot)
INSERT OR REPLACE INTO orders (order_id, customer_name, customer_phone, customer_address, order_amount, status, order_date, approval_date) 
VALUES ('ORD175346758878877F04', 'Col Umar', '03001234567', 'Karachi Address', 15000.0, 'Approved', datetime('now'), datetime('now'));

INSERT OR REPLACE INTO orders (order_id, customer_name, customer_phone, customer_address, order_amount, status, order_date, approval_date) 
VALUES ('ORD175355078A5CED085', 'Munir Shah', '03009876543', 'Lahore Address', 25000.0, 'Approved', datetime('now'), datetime('now'));

-- Add sample order items
INSERT OR REPLACE INTO order_items (order_id, product_id, quantity, unit_price, status) VALUES ('ORD175346758878877F04', 'PROD001', 100, 50.0, 'Approved');
INSERT OR REPLACE INTO order_items (order_id, product_id, quantity, unit_price, status) VALUES ('ORD175346758878877F04', 'PROD002', 50, 120.0, 'Approved');
INSERT OR REPLACE INTO order_items (order_id, product_id, quantity, unit_price, status) VALUES ('ORD175355078A5CED085', 'PROD001', 200, 50.0, 'Approved');

-- Add sample inventory with batch data
INSERT OR REPLACE INTO inventory (product_id, warehouse_id, batch_number, manufacturing_date, expiry_date, stock_quantity, allocated_quantity) 
VALUES ('PROD001', 'WH001', 'MED-202401-A001', '2024-01-01', '2026-01-01', 500, 0);

INSERT OR REPLACE INTO inventory (product_id, warehouse_id, batch_number, manufacturing_date, expiry_date, stock_quantity, allocated_quantity) 
VALUES ('PROD001', 'WH001', 'MED-202402-A002', '2024-02-01', '2026-02-01', 300, 0);

INSERT OR REPLACE INTO inventory (product_id, warehouse_id, batch_number, manufacturing_date, expiry_date, stock_quantity, allocated_quantity) 
VALUES ('PROD002', 'WH001', 'MED-202401-B001', '2024-01-10', '2026-01-10', 300, 0);

INSERT OR REPLACE INTO inventory (product_id, warehouse_id, batch_number, manufacturing_date, expiry_date, stock_quantity, allocated_quantity) 
VALUES ('PROD001', 'WH002', 'MED-202401-A003', '2024-01-15', '2026-01-15', 400, 0);

INSERT OR REPLACE INTO inventory (product_id, warehouse_id, batch_number, manufacturing_date, expiry_date, stock_quantity, allocated_quantity) 
VALUES ('PROD002', 'WH002', 'MED-202401-B003', '2024-01-20', '2026-01-20', 350, 0);
