import sqlite3

conn = sqlite3.connect('instance/medivent.db')
cursor = conn.cursor()

print("=== COMMENT SYSTEM CHECK ===")

# Check tables
cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%comment%'")
comment_tables = cursor.fetchall()
print("Comment tables:", [t[0] for t in comment_tables])

# Check invoice_holds
cursor.execute("SELECT order_id, hold_comments, release_comments FROM invoice_holds LIMIT 3")
holds = cursor.fetchall()
print("Sample holds:", holds)

# Check finance_comments
try:
    cursor.execute("SELECT COUNT(*) FROM finance_comments")
    count = cursor.fetchone()[0]
    print("Finance comments count:", count)
except:
    print("finance_comments table not found")

conn.close()
