#!/usr/bin/env python3
"""
Simple database test to check basic connectivity
"""

import sqlite3
import os

def test_database():
    """Test basic database operations"""
    print("🔍 TESTING DATABASE")
    print("=" * 30)
    
    # Check if database file exists
    db_path = 'instance/medivent.db'
    if os.path.exists(db_path):
        print(f'✅ Database file exists: {db_path}')
        print(f'   Size: {os.path.getsize(db_path)} bytes')
    else:
        print(f'❌ Database file not found: {db_path}')
        return False
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        print('✅ Database connection successful')
        
        # Check tables
        tables = conn.execute("SELECT name FROM sqlite_master WHERE type='table'").fetchall()
        print(f'✅ Found {len(tables)} tables')
        
        # Check if orders table exists
        orders_table = conn.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='orders'").fetchone()
        if orders_table:
            print('✅ Orders table exists')
            
            # Check orders table structure
            columns = conn.execute("PRAGMA table_info(orders)").fetchall()
            print(f'   Orders table has {len(columns)} columns')
            
            # Count orders
            order_count = conn.execute("SELECT COUNT(*) as count FROM orders").fetchone()
            print(f'   Total orders: {order_count["count"]}')
            
            # Check for ORD00000155
            test_order = conn.execute("SELECT * FROM orders WHERE order_id = ?", ('ORD00000155',)).fetchone()
            if test_order:
                print('✅ Order ORD00000155 found')
                print(f'   Customer: {test_order["customer_name"]}')
            else:
                print('❌ Order ORD00000155 not found')
                
                # Try to create it
                print('   Creating test order...')
                try:
                    conn.execute('''
                        INSERT INTO orders (order_id, customer_name, customer_phone, customer_address, 
                                          order_amount, order_date, status)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', ('ORD00000155', '3Minur', '00211111', '4', 444.0, '2025-07-20 00:01', 'Normal'))
                    conn.commit()
                    print('   ✅ Test order created')
                except Exception as e:
                    print(f'   ❌ Failed to create order: {e}')
        else:
            print('❌ Orders table not found')
            
        conn.close()
        return True
        
    except Exception as e:
        print(f'❌ Database error: {e}')
        return False

def test_qr_imports():
    """Test QR code imports"""
    print("\n🔍 TESTING QR IMPORTS")
    print("=" * 30)
    
    try:
        import qrcode
        print('✅ qrcode module imported')
        
        from PIL import Image
        print('✅ PIL module imported')
        
        from utils.qr_code_generator import generate_order_qr_code
        print('✅ QR generator imported')
        
        return True
        
    except Exception as e:
        print(f'❌ Import error: {e}')
        return False

if __name__ == "__main__":
    print("🧪 SIMPLE DIAGNOSTIC TEST")
    print("=" * 40)
    
    db_ok = test_database()
    qr_ok = test_qr_imports()
    
    print(f"\n📊 RESULTS")
    print("=" * 20)
    print(f"Database: {'✅ OK' if db_ok else '❌ FAILED'}")
    print(f"QR Code: {'✅ OK' if qr_ok else '❌ FAILED'}")
