import sqlite3

conn = sqlite3.connect('database.db')
conn.row_factory = sqlite3.Row

order_id = 'ORD175398287142AFD661'

print(f"Checking order items for {order_id}")

# Simple query to check order_items
cursor = conn.cursor()
items = cursor.execute('SELECT * FROM order_items WHERE order_id = ?', (order_id,)).fetchall()

print(f"Found {len(items)} items")

if items:
    for item in items:
        print(f"Item: {dict(item)}")
else:
    print("No items found")
    
    # Check if order exists
    order = cursor.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
    if order:
        print(f"Order exists: {order['customer_name']}, Status: {order['status']}")
    else:
        print("Order does not exist")
    
    # Check total items in database
    total = cursor.execute('SELECT COUNT(*) as count FROM order_items').fetchone()
    print(f"Total order_items in database: {total['count']}")
    
    # Show sample items
    samples = cursor.execute('SELECT order_id, product_id, quantity FROM order_items LIMIT 5').fetchall()
    print("Sample order_items:")
    for sample in samples:
        print(f"  {sample['order_id']} - {sample['product_id']} - Qty: {sample['quantity']}")

conn.close()
