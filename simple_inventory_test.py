#!/usr/bin/env python3
"""
Simple test to check inventory route status
"""

import requests
import time

def simple_test():
    """Simple test of inventory route"""
    try:
        print("🔍 SIMPLE INVENTORY ROUTE TEST")
        print("=" * 50)
        
        # Test without authentication first
        print("1️⃣ Testing inventory route without auth...")
        response = requests.get('http://127.0.0.1:5001/inventory/', timeout=5)
        print(f"Status: {response.status_code}")
        print(f"URL: {response.url}")
        
        if response.status_code == 302:
            print("✅ Got redirect (expected for unauthenticated request)")
            if 'login' in response.headers.get('Location', ''):
                print("✅ Redirected to login (correct behavior)")
            else:
                print(f"❌ Redirected to: {response.headers.get('Location', 'unknown')}")
        
        # Test dashboard route for comparison
        print("\n2️⃣ Testing dashboard route...")
        dashboard_response = requests.get('http://127.0.0.1:5001/dashboard', timeout=5)
        print(f"Dashboard Status: {dashboard_response.status_code}")
        print(f"Dashboard URL: {dashboard_response.url}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False

if __name__ == "__main__":
    simple_test()
