#!/usr/bin/env python3
"""
Simple test for order ID generation
"""

print("Testing order ID generation...")

try:
    from app import app
    with app.app_context():
        from routes.orders import generate_order_id
        
        print("Generating test order IDs:")
        for i in range(5):
            order_id = generate_order_id()
            print(f"  {i+1}. {order_id}")
        
        print("✅ Order ID generation is working!")
        
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
