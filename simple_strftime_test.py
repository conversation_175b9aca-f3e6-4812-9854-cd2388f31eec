#!/usr/bin/env python3
"""
Simple test to verify strftime error is resolved
"""

import requests
import sys

def test_strftime_error():
    """Test if strftime error is resolved"""
    print("🧪 TESTING STRFTIME ERROR RESOLUTION")
    print("=" * 50)
    
    try:
        response = requests.get('http://localhost:5000/riders/assignment-dashboard', timeout=10)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            # Check for the specific error that was reported
            if "'str' object has no attribute 'strftime'" in response.text:
                print("❌ STRFTIME ERROR STILL EXISTS!")
                return False
            elif "Error loading assignment form" in response.text:
                print("❌ ASSIGNMENT FORM ERROR STILL EXISTS!")
                return False
            else:
                print("✅ NO STRFTIME ERRORS FOUND!")
                print("✅ Assignment dashboard loads successfully")
                return True
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Request Error: {e}")
        return False

if __name__ == "__main__":
    success = test_strftime_error()
    if success:
        print("\n🎉 SUCCESS: STRFTIME ERROR HAS BEEN RESOLVED!")
        sys.exit(0)
    else:
        print("\n❌ FAILURE: STRFTIME ERROR STILL EXISTS")
        sys.exit(1)
