#!/usr/bin/env python3
import sqlite3
from utils.inventory_validator import InventoryValidator
from datetime import datetime

print('🧪 Testing transaction fix...')
db = sqlite3.connect('instance/medivent.db')
db.row_factory = sqlite3.Row

# Get a product with inventory
product = db.execute('SELECT product_id FROM products WHERE product_id = "P003"').fetchone()
if product:
    print(f'✅ Testing with product: {product["product_id"]}')
    
    validator = InventoryValidator(db)
    
    # Test within transaction
    db.execute('BEGIN TRANSACTION')
    try:
        success, msg = validator.execute_stock_deduction('P003', 1, 0, 'TEST001', 'testuser', use_transaction=False)
        print(f'✅ Result: {success}, Message: {msg}')
        db.execute('ROLLBACK')
        print('🎉 Transaction test completed successfully!')
    except Exception as e:
        print(f'❌ Error: {e}')
        db.execute('ROLLBACK')
else:
    print('❌ No test product found')

db.close()
print('✅ Test completed!')
