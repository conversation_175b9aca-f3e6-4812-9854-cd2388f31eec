{% extends 'base.html' %}

{% block title %}Generate DC - {{ order.order_id }} - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card bg-gradient-success text-white">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h3 class="mb-0">
                                <i class="fas fa-truck-loading"></i> Generate Delivery Challan
                            </h3>
                            <p class="mb-0">Order: <strong>{{ order.order_id }}</strong> | Customer: <strong>{{ order.customer_name }}</strong></p>
                        </div>
                        <div class="col-md-4 text-right">
                            <h4 class="mb-0">{{ order.order_amount|format_currency }}</h4>
                            <small>Total Order Value</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <form method="POST" id="dcGenerationForm">
        <div class="row">
            <!-- Order Items & Allocation -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-boxes"></i> Inventory Allocation
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Allocation Method Selection -->
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6><i class="fas fa-cog"></i> Allocation Method</h6>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="allocation_method" 
                                                   id="fifo" value="fifo">
                                            <label class="form-check-label" for="fifo">
                                                <strong>FIFO</strong> (First In, First Out)
                                            </label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="allocation_method" 
                                                   id="lifo" value="lifo">
                                            <label class="form-check-label" for="lifo">
                                                <strong>LIFO</strong> (Last In, First Out)
                                            </label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="allocation_method" 
                                                   id="manual" value="manual" checked>
                                            <label class="form-check-label" for="manual">
                                                <strong>Manual</strong> Selection
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Product Allocation -->
                        {% for item in order_items %}
                        <div class="card mb-4 product-allocation" data-product-id="{{ item.product_id }}">
                            <div class="card-header bg-info text-white">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <h6 class="mb-0">
                                            {{ item.product_name }} 
                                            {% if item.strength %}({{ item.strength }}){% endif %}
                                        </h6>
                                        <small>Required: <strong>{{ item.quantity }}</strong> units</small>
                                    </div>
                                    <div class="col-md-4 text-right">
                                        <span class="badge badge-light">
                                            <span class="allocated-count">0</span> / {{ item.quantity }} allocated
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    {% for warehouse_id, warehouse_data in inventory_data[item.product_id].items() %}
                                    <div class="col-md-6">
                                        <div class="card border-secondary">
                                            <div class="card-header bg-secondary text-white">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-warehouse"></i> {{ warehouse_data.warehouse_name }}
                                                </h6>
                                                <small>Available: {{ warehouse_data.total_available }} units</small>
                                            </div>
                                            <div class="card-body">
                                                {% if warehouse_data.batches %}
                                                <div class="table-responsive">
                                                    <table class="table table-sm">
                                                        <thead>
                                                            <tr>
                                                                <th>Batch</th>
                                                                <th>Available</th>
                                                                <th>Expiry</th>
                                                                <th>Allocate</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            {% for batch in warehouse_data.batches %}
                                                            <tr>
                                                                <td>
                                                                    <small>{{ batch.batch_number }}</small>
                                                                </td>
                                                                <td>
                                                                    <span class="badge badge-success">{{ batch.available_quantity }}</span>
                                                                </td>
                                                                <td>
                                                                    <small>{{ batch.expiry_date }}</small>
                                                                </td>
                                                                <td>
                                                                    <input type="number" 
                                                                           class="form-control form-control-sm allocation-input"
                                                                           data-product-id="{{ item.product_id }}"
                                                                           data-batch-id="{{ batch.inventory_id }}"
                                                                           data-warehouse-id="{{ warehouse_id }}"
                                                                           data-max="{{ batch.available_quantity }}"
                                                                           min="0" 
                                                                           max="{{ batch.available_quantity }}"
                                                                           value="0">
                                                                </td>
                                                            </tr>
                                                            {% endfor %}
                                                        </tbody>
                                                    </table>
                                                </div>
                                                {% else %}
                                                <div class="text-center text-muted">
                                                    <i class="fas fa-exclamation-triangle"></i>
                                                    <br>No inventory available
                                                </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- Summary & Actions -->
            <div class="col-md-4">
                <!-- Order Summary -->
                <div class="card">
                    <div class="card-header bg-dark text-white">
                        <h5 class="mb-0"><i class="fas fa-clipboard-list"></i> Order Summary</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Order ID:</strong></td>
                                <td>{{ order.order_id }}</td>
                            </tr>
                            <tr>
                                <td><strong>Customer:</strong></td>
                                <td>{{ order.customer_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>Phone:</strong></td>
                                <td>{{ order.customer_phone or 'N/A' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Total Items:</strong></td>
                                <td>{{ order_items|length }}</td>
                            </tr>
                            <tr>
                                <td><strong>Total Amount:</strong></td>
                                <td><strong>{{ order.order_amount|format_currency }}</strong></td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- Allocation Summary -->
                <div class="card mt-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="fas fa-chart-pie"></i> Allocation Status</h5>
                    </div>
                    <div class="card-body">
                        <div id="allocationSummary">
                            <div class="text-center text-muted">
                                <i class="fas fa-info-circle"></i>
                                <br>Start allocating inventory to see summary
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="card mt-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-check"></i> Generate DC</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="dc_notes">DC Notes (Optional):</label>
                            <textarea class="form-control" id="dc_notes" name="dc_notes" rows="3" 
                                      placeholder="Add any special instructions..."></textarea>
                        </div>
                        
                        <button type="submit" class="btn btn-success btn-block" id="generateDcBtn" disabled>
                            <i class="fas fa-truck"></i> Generate Delivery Challan
                        </button>
                        
                        <a href="{{ url_for('dc_pending') }}" class="btn btn-secondary btn-block mt-2">
                            <i class="fas fa-arrow-left"></i> Back to DC Pending
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
$(document).ready(function() {
    // Update allocation summary when inputs change
    $('.allocation-input').on('input', function() {
        updateAllocationSummary();
        validateAllocations();
    });
    
    // Auto-allocation methods
    $('input[name="allocation_method"]').on('change', function() {
        if ($(this).val() !== 'manual') {
            autoAllocate($(this).val());
        }
    });
    
    function updateAllocationSummary() {
        let summary = {};
        let totalAllocated = 0;
        let totalRequired = 0;
        
        $('.product-allocation').each(function() {
            let productId = $(this).data('product-id');
            let allocated = 0;
            
            $(this).find('.allocation-input').each(function() {
                allocated += parseInt($(this).val()) || 0;
            });
            
            $(this).find('.allocated-count').text(allocated);
            summary[productId] = allocated;
            totalAllocated += allocated;
        });
        
        // Update summary display
        let summaryHtml = '<div class="table-responsive"><table class="table table-sm">';
        summaryHtml += '<tr><th>Product</th><th>Allocated</th></tr>';
        
        $('.product-allocation').each(function() {
            let productName = $(this).find('h6').text().trim();
            let allocated = $(this).find('.allocated-count').text();
            summaryHtml += `<tr><td>${productName}</td><td>${allocated}</td></tr>`;
        });
        
        summaryHtml += '</table></div>';
        $('#allocationSummary').html(summaryHtml);
    }
    
    function validateAllocations() {
        let allValid = true;
        
        $('.product-allocation').each(function() {
            let required = parseInt($(this).find('small strong').text());
            let allocated = parseInt($(this).find('.allocated-count').text());
            
            if (allocated !== required) {
                allValid = false;
            }
        });
        
        $('#generateDcBtn').prop('disabled', !allValid);
    }
    
    function autoAllocate(method) {
        // Implementation for FIFO/LIFO auto-allocation
        alert(`${method.toUpperCase()} auto-allocation will be implemented`);
    }
});
</script>
{% endblock %}
