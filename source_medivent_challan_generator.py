"""
Medivent Pharmaceuticals Delivery Challan Generator
This module generates delivery challans in the exact format specified by Medivent Pharmaceuticals
"""

import os
from datetime import datetime
from reportlab.lib import colors
from reportlab.lib.pagesizes import A4, landscape
from reportlab.lib.units import cm, mm, inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.enums import TA_CENTER, TA_LEFT

# Define custom colors
BLACK = colors.black
WHITE = colors.white
LIGHT_GRAY = colors.HexColor('#F5F5F5')
DARK_GRAY = colors.HexColor('#333333')
BLUE = colors.HexColor('#0066cc')

def generate_pdf_challan(order, customer, order_items, dc_number):
    """
    Generate a PDF delivery challan that exactly matches the sample format

    Args:
        order: Order details
        customer: Customer details
        order_items: List of order items
        dc_number: Delivery challan number

    Returns:
        Path to the generated PDF file
    """
    try:
        # Create challans directory if it doesn't exist
        static_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'static')
        challans_dir = os.path.join(static_dir, 'documents', 'challans')
        os.makedirs(challans_dir, exist_ok=True)

        # PDF file path
        pdf_path = os.path.join(challans_dir, f"{dc_number}.pdf")

        # Create the PDF document - using landscape orientation to match sample
        doc = SimpleDocTemplate(
            pdf_path,
            pagesize=landscape(A4),
            rightMargin=0.5*inch,
            leftMargin=0.5*inch,
            topMargin=0.5*inch,
            bottomMargin=0.5*inch
        )

        # Container for the 'Flowable' objects
        elements = []

        # Get styles
        styles = getSampleStyleSheet()

        # Define custom styles
        title_style = ParagraphStyle(
            'Title',
            parent=styles['Heading1'],
            fontSize=16,
            alignment=TA_CENTER,
            spaceAfter=0.1*inch
        )

        header_style = ParagraphStyle(
            'Header',
            parent=styles['Normal'],
            fontSize=10,
            fontName='Helvetica-Bold',
            alignment=TA_LEFT,
            spaceAfter=0
        )

        normal_style = ParagraphStyle(
            'Normal',
            parent=styles['Normal'],
            fontSize=9,
            alignment=TA_LEFT,
            spaceAfter=0
        )

        # Add the title "Delivery Challan"
        elements.append(Paragraph("Delivery Challan", title_style))

        # Create the main information table - exactly matching the sample format
        current_date = datetime.now()
        formatted_date = current_date.strftime('%d-%b-%Y')

        # Customer and challan details in a table format
        header_data = [
            # Row 1
            [Paragraph("<b>Customer Name</b>", normal_style),
             Paragraph(customer.get('name', ''), normal_style),
             Paragraph("<b>DC No</b>", normal_style),
             Paragraph(f"{dc_number}", normal_style)],

            # Row 2
            [Paragraph("<b>Customer Code</b>", normal_style),
             Paragraph(customer.get('code', 'C00000'), normal_style),
             Paragraph("<b>Posting Date</b>", normal_style),
             Paragraph(f"{formatted_date}", normal_style)],

            # Row 3
            [Paragraph("<b>Address</b>", normal_style),
             Paragraph(customer.get('address', ''), normal_style),
             Paragraph("<b>PO No.</b>", normal_style),
             Paragraph(f"{order.get('order_id', '')}", normal_style)],

            # Row 4 - Address continues, PO Date
            ["", "",
             Paragraph("<b>PO Date</b>", normal_style),
             Paragraph(f"{current_date.strftime('%d-%m-%Y')}", normal_style)]
        ]

        # Create the header table with specific column widths
        header_table = Table(header_data, colWidths=[2.5*cm, 12*cm, 2.5*cm, 3*cm])
        header_table.setStyle(TableStyle([
            ('GRID', (0, 0), (1, 2), 0.5, BLACK),  # Grid for customer info
            ('GRID', (2, 0), (3, 3), 0.5, BLACK),  # Grid for challan info
            ('SPAN', (1, 2), (1, 3)),  # Span the address cell across two rows
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ]))
        elements.append(header_table)

        elements.append(Spacer(1, 0.2*inch))

        # Product table headers - updated to include FOC
        product_headers = [
            Paragraph("<b>SR#</b>", normal_style),
            Paragraph("<b>Product Name</b>", normal_style),
            Paragraph("<b>Batch No</b>", normal_style),  # Moved batch number beside product name
            Paragraph("<b>Generic</b>", normal_style),
            Paragraph("<b>Company</b>", normal_style),
            Paragraph("<b>Mfg Date</b>", normal_style),
            Paragraph("<b>Exp Date</b>", normal_style),
            Paragraph("<b>Qty</b>", normal_style),
            Paragraph("<b>FOC</b>", normal_style),
            Paragraph("<b>Total</b>", normal_style)
        ]

        # Create product data rows
        product_data = [product_headers]

        # Add product items
        total_qty = 0
        for i, item in enumerate(order_items, 1):
            product_name = item.get('product_name', '')
            generic = item.get('generic', '')
            company = item.get('company', '')
            batch_no = item.get('batch_number', '')

            # Format dates to match the sample (MM-YYYY)
            mfg_date = item.get('manufacturing_date', '')
            if isinstance(mfg_date, str) and len(mfg_date) > 7:
                try:
                    mfg_date = datetime.strptime(mfg_date, '%Y-%m-%d').strftime('%m-%Y')
                except:
                    pass

            exp_date = item.get('expiry_date', '')
            if isinstance(exp_date, str) and len(exp_date) > 7:
                try:
                    exp_date = datetime.strptime(exp_date, '%Y-%m-%d').strftime('%m-%Y')
                except:
                    pass

            qty = item.get('quantity', 0)
            foc_qty = item.get('foc_quantity', 0)
            total_item_qty = int(qty) + int(foc_qty)
            total_qty += total_item_qty

            product_data.append([
                str(i),
                Paragraph(product_name, normal_style),
                batch_no,  # Moved batch number beside product name
                Paragraph(generic, normal_style),
                Paragraph(company, normal_style),
                mfg_date,
                exp_date,
                str(qty),
                str(foc_qty) if foc_qty > 0 else "-",
                str(total_item_qty)
            ])

        # Create the product table with specific column widths - adjusted for FOC and Total columns
        col_widths = [1*cm, 4.5*cm, 2*cm, 2.5*cm, 3.5*cm, 1.8*cm, 1.8*cm, 1.2*cm, 1.2*cm, 1.5*cm]
        product_table = Table(product_data, colWidths=col_widths)

        # Style the product table
        product_table.setStyle(TableStyle([
            ('GRID', (0, 0), (-1, -1), 0.5, BLACK),
            ('BACKGROUND', (0, 0), (-1, 0), DARK_GRAY),
            ('TEXTCOLOR', (0, 0), (-1, 0), WHITE),
            ('ALIGN', (0, 0), (0, -1), 'CENTER'),  # Center SR#
            ('ALIGN', (5, 0), (9, -1), 'CENTER'),  # Center dates, qty, FOC, and total
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ]))
        elements.append(product_table)

        # Add total items row
        total_row = [["", "", "", "", "", "", "", "", "Total Items", str(total_qty)]]
        total_table = Table(total_row, colWidths=col_widths)
        total_table.setStyle(TableStyle([
            ('GRID', (8, 0), (9, 0), 0.5, BLACK),
            ('ALIGN', (8, 0), (9, 0), 'CENTER'),
            ('FONTNAME', (8, 0), (9, 0), 'Helvetica-Bold'),
        ]))
        elements.append(total_table)

        # Build the PDF
        doc.build(elements)

        print(f"PDF Delivery Challan {dc_number} generated successfully: {pdf_path}")
        return pdf_path

    except Exception as e:
        print(f"Error generating PDF delivery challan: {e}")
        import traceback
        traceback.print_exc()
        return None
