#!/usr/bin/env python3
"""
Standalone API test server
"""

from flask import Flask, jsonify
from utils.db import get_db
import os
import sqlite3

# Create Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'test-key-for-api'

# Simple API endpoints without authentication
@app.route('/api/order-details/<order_id>')
def get_order_details_simple(order_id):
    """Get order details without authentication"""
    try:
        # Ensure database exists
        db_path = 'instance/medivent.db'
        if not os.path.exists(db_path):
            return jsonify({
                'success': False,
                'message': 'Database not found'
            }), 404
        
        # Connect to database
        db = sqlite3.connect(db_path)
        db.row_factory = sqlite3.Row
        
        # Get order details
        cursor = db.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,))
        order = cursor.fetchone()
        
        if not order:
            return jsonify({
                'success': False,
                'message': f'Order {order_id} not found'
            }), 404
        
        # Get order items
        cursor = db.execute('''
            SELECT oi.*, p.name as product_name, p.strength, p.manufacturer
            FROM order_items oi
            LEFT JOIN products p ON oi.product_id = p.product_id
            WHERE oi.order_id = ?
            ORDER BY oi.product_name
        ''', (order_id,))
        order_items = cursor.fetchall()
        
        db.close()
        
        # Prepare response
        response_data = {
            'success': True,
            'order': dict(order),
            'order_items': [dict(item) for item in order_items],
            'summary': {
                'total_items': len(order_items),
                'order_amount': float(order['order_amount'] or 0)
            }
        }
        
        return jsonify(response_data)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error: {str(e)}'
        }), 500

@app.route('/api/order-qr-code/<order_id>')
def get_order_qr_code_simple(order_id):
    """Generate QR code without authentication"""
    try:
        # Get order details directly (not through HTTP call)
        db_path = 'instance/medivent.db'
        if not os.path.exists(db_path):
            return jsonify({
                'success': False,
                'message': 'Database not found'
            }), 404

        # Connect to database
        db = sqlite3.connect(db_path)
        db.row_factory = sqlite3.Row

        # Get order details
        cursor = db.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,))
        order = cursor.fetchone()

        if not order:
            return jsonify({
                'success': False,
                'message': f'Order {order_id} not found'
            }), 404

        # Get order items
        cursor = db.execute('''
            SELECT oi.*, p.name as product_name, p.strength, p.manufacturer
            FROM order_items oi
            LEFT JOIN products p ON oi.product_id = p.product_id
            WHERE oi.order_id = ?
            ORDER BY oi.product_name
        ''', (order_id,))
        order_items = cursor.fetchall()

        db.close()

        # Prepare order data for QR generation
        order_data = {
            'success': True,
            'order': dict(order),
            'order_items': [dict(item) for item in order_items],
            'summary': {
                'total_items': len(order_items),
                'order_amount': float(order['order_amount'] or 0)
            }
        }
        
        # Try to import QR code generator
        try:
            from utils.qr_code_generator import generate_order_qr_code
            
            # Generate QR code
            include_branding = True
            qr_result = generate_order_qr_code(order_data, include_branding)
            
            if qr_result.get('success'):
                return jsonify(qr_result)
            else:
                return jsonify({
                    'success': False,
                    'message': qr_result.get('error', 'Failed to generate QR code')
                }), 500
                
        except ImportError as e:
            return jsonify({
                'success': False,
                'message': f'QR code generator not available: {str(e)}'
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error generating QR code: {str(e)}'
        }), 500

@app.route('/test')
def test_endpoint():
    """Simple test endpoint"""
    return jsonify({
        'success': True,
        'message': 'Standalone API server is working',
        'endpoints': [
            '/api/order-details/<order_id>',
            '/api/order-qr-code/<order_id>',
            '/test'
        ]
    })

if __name__ == '__main__':
    print("🚀 Starting Standalone API Test Server...")
    print("📍 Available endpoints:")
    print("   GET /test")
    print("   GET /api/order-details/<order_id>")
    print("   GET /api/order-qr-code/<order_id>")
    print("🌐 Server URL: http://127.0.0.1:5003")
    
    app.run(host='127.0.0.1', port=5003, debug=True)
