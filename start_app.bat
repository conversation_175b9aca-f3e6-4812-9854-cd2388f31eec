@echo off
echo Starting Medivent ERP Application...
echo.

echo Setting up database...
python -c "import sqlite3; import os; from datetime import datetime; os.makedirs('instance', exist_ok=True); conn = sqlite3.connect('instance/medivent.db'); cursor = conn.cursor(); cursor.execute('CREATE TABLE IF NOT EXISTS batch_selections (id INTEGER PRIMARY KEY AUTOINCREMENT, order_id TEXT NOT NULL, product_id TEXT NOT NULL, batch_number TEXT NOT NULL, warehouse_id TEXT NOT NULL, allocated_quantity REAL NOT NULL, selection_method TEXT DEFAULT \"manual\", created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, created_by TEXT, status TEXT DEFAULT \"pending\")'); cursor.execute('CREATE TABLE IF NOT EXISTS dc_generation_sessions (id INTEGER PRIMARY KEY AUTOINCREMENT, session_id TEXT UNIQUE NOT NULL, order_id TEXT NOT NULL, status TEXT DEFAULT \"active\", created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP)'); cursor.execute('INSERT OR REPLACE INTO orders (order_id, customer_name, customer_phone, customer_address, order_amount, status, order_date, approval_date) VALUES (?, ?, ?, ?, ?, ?, ?, ?)', ('ORD175346758878877F04', 'Col Umar', '03001234567', 'Karachi Address', 15000.0, 'Approved', datetime.now(), datetime.now())); cursor.execute('INSERT OR REPLACE INTO orders (order_id, customer_name, customer_phone, customer_address, order_amount, status, order_date, approval_date) VALUES (?, ?, ?, ?, ?, ?, ?, ?)', ('ORD175355078A5CED085', 'Munir Shah', '03009876543', 'Lahore Address', 25000.0, 'Approved', datetime.now(), datetime.now())); conn.commit(); conn.close(); print('Database setup completed!')"

echo.
echo Starting Flask application...
python app.py

pause
