/**
 * User Activity Tracker for Medivent ERP
 * Tracks user interactions, screen time, and manages session timeouts
 */

class ActivityTracker {
    constructor() {
        this.isActive = true;
        this.lastActivity = Date.now();
        this.heartbeatInterval = 30000; // 30 seconds
        this.idleThreshold = 60000; // 1 minute
        this.isIdle = false;
        this.screenTimeStart = Date.now();
        this.totalActiveTime = 0;
        this.totalIdleTime = 0;
        this.sessionWarningShown = false;
        
        this.init();
    }
    
    init() {
        console.log('🔄 Initializing Activity Tracker...');
        
        // Set up event listeners for user activity
        this.setupActivityListeners();
        
        // Set up page visibility tracking
        this.setupVisibilityTracking();
        
        // Start heartbeat
        this.startHeartbeat();
        
        // Start idle detection
        this.startIdleDetection();
        
        // Start session status monitoring
        this.startSessionMonitoring();
        
        console.log('✅ Activity Tracker initialized');
    }
    
    setupActivityListeners() {
        const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
        
        events.forEach(event => {
            document.addEventListener(event, () => {
                this.recordActivity(event);
            }, true);
        });
        
        // Track form interactions
        document.addEventListener('input', (e) => {
            this.recordActivity('typing', { element: e.target.tagName });
        });
        
        // Track navigation
        window.addEventListener('beforeunload', () => {
            this.recordActivity('page_unload');
        });
        
        // Track page load
        window.addEventListener('load', () => {
            this.recordActivity('page_load');
        });
    }
    
    setupVisibilityTracking() {
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.recordActivity('tab_hidden');
                this.isActive = false;
            } else {
                this.recordActivity('tab_visible');
                this.isActive = true;
                this.lastActivity = Date.now();
            }
        });
        
        // Track window focus/blur
        window.addEventListener('focus', () => {
            this.recordActivity('window_focus');
            this.isActive = true;
            this.lastActivity = Date.now();
        });
        
        window.addEventListener('blur', () => {
            this.recordActivity('window_blur');
            this.isActive = false;
        });
    }
    
    recordActivity(activityType, metadata = {}) {
        this.lastActivity = Date.now();
        
        // If we were idle, record that we're active again
        if (this.isIdle) {
            this.isIdle = false;
            this.recordActivity('idle_end');
        }
        
        // Send activity to server (debounced)
        this.sendActivityToServer(activityType, metadata);
    }
    
    sendActivityToServer(activityType, metadata = {}) {
        // Debounce rapid events
        clearTimeout(this.activityTimeout);
        this.activityTimeout = setTimeout(() => {
            fetch('/api/activity/track', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify({
                    activity_type: activityType,
                    page_url: window.location.pathname,
                    timestamp: new Date().toISOString(),
                    metadata: metadata
                })
            }).catch(error => {
                console.error('Error sending activity:', error);
            });
        }, 1000);
    }
    
    startHeartbeat() {
        setInterval(() => {
            if (this.isActive && !document.hidden) {
                fetch('/api/activity/heartbeat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': this.getCSRFToken()
                    },
                    body: JSON.stringify({
                        timestamp: new Date().toISOString(),
                        page_url: window.location.pathname
                    })
                }).catch(error => {
                    console.error('Heartbeat error:', error);
                });
            }
        }, this.heartbeatInterval);
    }
    
    startIdleDetection() {
        setInterval(() => {
            const timeSinceActivity = Date.now() - this.lastActivity;
            
            if (timeSinceActivity > this.idleThreshold && !this.isIdle) {
                this.isIdle = true;
                this.recordActivity('idle_start');
                console.log('🔄 User is now idle');
            }
        }, 10000); // Check every 10 seconds
    }
    
    startSessionMonitoring() {
        setInterval(() => {
            this.checkSessionStatus();
        }, 30000); // Check every 30 seconds
    }
    
    checkSessionStatus() {
        fetch('/api/session/status', {
            method: 'GET',
            headers: {
                'X-CSRFToken': this.getCSRFToken()
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'warning' && !this.sessionWarningShown) {
                this.showSessionWarning(data.remaining_time);
            } else if (data.status === 'expired') {
                this.handleSessionExpired();
            }
        })
        .catch(error => {
            console.error('Session status check error:', error);
        });
    }
    
    showSessionWarning(remainingTime) {
        this.sessionWarningShown = true;
        
        const minutes = Math.floor(remainingTime / 60);
        const seconds = remainingTime % 60;
        
        const modal = this.createWarningModal(minutes, seconds);
        document.body.appendChild(modal);
        
        // Show the modal
        $(modal).modal('show');
        
        // Start countdown
        this.startWarningCountdown(modal, remainingTime);
    }
    
    createWarningModal(minutes, seconds) {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'sessionWarningModal';
        modal.setAttribute('data-backdrop', 'static');
        modal.setAttribute('data-keyboard', 'false');
        
        modal.innerHTML = `
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-warning text-dark">
                        <h5 class="modal-title">
                            <i class="fas fa-exclamation-triangle"></i> Session Timeout Warning
                        </h5>
                    </div>
                    <div class="modal-body text-center">
                        <p>Your session will expire in:</p>
                        <h2 class="text-danger" id="countdownTimer">${minutes}:${seconds.toString().padStart(2, '0')}</h2>
                        <p>Click "Stay Logged In" to extend your session.</p>
                    </div>
                    <div class="modal-footer justify-content-center">
                        <button type="button" class="btn btn-success" id="extendSession">
                            <i class="fas fa-clock"></i> Stay Logged In
                        </button>
                        <button type="button" class="btn btn-secondary" id="logoutNow">
                            <i class="fas fa-sign-out-alt"></i> Logout Now
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        // Add event listeners
        modal.querySelector('#extendSession').addEventListener('click', () => {
            this.extendSession();
            $(modal).modal('hide');
            modal.remove();
            this.sessionWarningShown = false;
        });
        
        modal.querySelector('#logoutNow').addEventListener('click', () => {
            this.logout();
        });
        
        return modal;
    }
    
    startWarningCountdown(modal, remainingTime) {
        const timer = modal.querySelector('#countdownTimer');
        let timeLeft = remainingTime;
        
        const countdown = setInterval(() => {
            timeLeft--;
            
            if (timeLeft <= 0) {
                clearInterval(countdown);
                this.handleSessionExpired();
                return;
            }
            
            const minutes = Math.floor(timeLeft / 60);
            const seconds = timeLeft % 60;
            timer.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        }, 1000);
    }
    
    extendSession() {
        fetch('/api/session/extend', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.getCSRFToken()
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('✅ Session extended');
                this.lastActivity = Date.now();
            } else {
                console.error('Failed to extend session');
                this.handleSessionExpired();
            }
        })
        .catch(error => {
            console.error('Error extending session:', error);
        });
    }
    
    handleSessionExpired() {
        // Show logout message
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            <strong>Session Expired!</strong> You have been logged out due to inactivity.
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        `;
        
        document.body.appendChild(alertDiv);
        
        // Redirect to login after 3 seconds
        setTimeout(() => {
            window.location.href = '/logout';
        }, 3000);
    }
    
    logout() {
        window.location.href = '/logout';
    }
    
    getCSRFToken() {
        // Try to get CSRF token from meta tag or form
        const metaToken = document.querySelector('meta[name="csrf-token"]');
        if (metaToken) {
            return metaToken.getAttribute('content');
        }
        
        const formToken = document.querySelector('input[name="csrf_token"]');
        if (formToken) {
            return formToken.value;
        }
        
        return '';
    }
}

// Initialize activity tracker when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize if user is logged in
    if (document.body.classList.contains('logged-in') || 
        document.querySelector('.navbar .dropdown-toggle')) {
        window.activityTracker = new ActivityTracker();
    }
});
