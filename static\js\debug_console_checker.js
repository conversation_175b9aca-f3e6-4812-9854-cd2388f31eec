
    // Console <PERSON>rror Checker for Order Details Modal
    console.log('🔍 Starting Order Details Debug...');
    
    // Check if enhanced modal functions exist
    if (typeof showEnhancedOrderDetails === 'function') {
        console.log('✅ showEnhancedOrderDetails function exists');
    } else {
        console.error('❌ showEnhancedOrderDetails function missing');
    }
    
    if (typeof viewOrderDetails === 'function') {
        console.log('✅ viewOrderDetails function exists');
    } else {
        console.error('❌ viewOrderDetails function missing');
    }
    
    // Check if enhanced modal object exists
    if (typeof enhancedOrderModal !== 'undefined') {
        console.log('✅ enhancedOrderModal object exists');
    } else {
        console.error('❌ enhancedOrderModal object missing');
    }
    
    // Test API endpoint directly
    fetch('/api/order-details/ORD00000155')
        .then(response => {
            console.log('📡 API Response Status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('📊 API Response Data:', data);
            if (data.success) {
                console.log('✅ API call successful');
            } else {
                console.error('❌ API call failed:', data.message);
            }
        })
        .catch(error => {
            console.error('❌ API call error:', error);
        });
    
    // Check modal HTML elements
    const modal = document.getElementById('enhancedOrderModal');
    if (modal) {
        console.log('✅ Enhanced modal element found');
    } else {
        console.error('❌ Enhanced modal element not found');
    }
    