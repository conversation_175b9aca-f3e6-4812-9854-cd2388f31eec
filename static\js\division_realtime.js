/**
 * Division Real-time Update System
 * Handles automatic updates of division-related components across the application
 */

class DivisionRealtimeUpdater {
    constructor() {
        this.updateInterval = 30000; // 30 seconds
        this.intervalId = null;
        this.lastUpdate = null;
        this.isUpdating = false;
    }

    /**
     * Start automatic real-time updates
     */
    startAutoRefresh() {
        console.log('🔄 Starting division real-time updates');
        
        // Initial update
        this.updateAll();
        
        // Set up periodic updates
        this.intervalId = setInterval(() => {
            this.updateAll();
        }, this.updateInterval);
    }

    /**
     * Stop automatic updates
     */
    stopAutoRefresh() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
            console.log('⏹️ Stopped division real-time updates');
        }
    }

    /**
     * Update all division-related components
     */
    async updateAll() {
        if (this.isUpdating) {
            return; // Prevent concurrent updates
        }

        this.isUpdating = true;
        
        try {
            console.log('🔄 Updating division components...');
            
            // Update division counters
            await this.updateDivisionCounters();
            
            // Update division dropdowns
            await this.updateDivisionDropdowns();
            
            // Update division charts
            await this.updateDivisionCharts();
            
            this.lastUpdate = new Date();
            console.log('✅ Division components updated successfully');
            
        } catch (error) {
            console.error('❌ Error updating division components:', error);
        } finally {
            this.isUpdating = false;
        }
    }

    /**
     * Update division counters in dashboards
     */
    async updateDivisionCounters() {
        try {
            const response = await fetch('/api/divisions/count');
            const data = await response.json();
            
            if (data.success) {
                // Update KPI counters
                const kpiElements = document.querySelectorAll('#kpiDivisions');
                kpiElements.forEach(element => {
                    if (element) {
                        element.textContent = data.count;
                        this.animateCounterUpdate(element);
                    }
                });
                
                // Update sparklines with new data
                await this.updateSparklines(data.count);
                
                console.log(`📊 Updated division counters: ${data.count}`);
            }
        } catch (error) {
            console.error('Error updating division counters:', error);
        }
    }

    /**
     * Update division dropdowns in forms
     */
    async updateDivisionDropdowns() {
        try {
            const response = await fetch('/api/divisions/dropdown');
            const data = await response.json();
            
            if (data.success) {
                // Find all division dropdowns
                const dropdowns = document.querySelectorAll('select[name="division_id"], .division-select');
                
                dropdowns.forEach(dropdown => {
                    const currentValue = dropdown.value;
                    
                    // Clear existing options (except first placeholder)
                    const firstOption = dropdown.querySelector('option:first-child');
                    dropdown.innerHTML = '';
                    if (firstOption) {
                        dropdown.appendChild(firstOption);
                    }
                    
                    // Add updated divisions
                    data.divisions.forEach(division => {
                        const option = document.createElement('option');
                        option.value = division.division_id;
                        option.textContent = division.name;
                        dropdown.appendChild(option);
                    });
                    
                    // Restore previous selection if still valid
                    if (currentValue) {
                        dropdown.value = currentValue;
                    }
                });
                
                console.log(`📋 Updated ${dropdowns.length} division dropdowns`);
            }
        } catch (error) {
            console.error('Error updating division dropdowns:', error);
        }
    }

    /**
     * Update division charts
     */
    async updateDivisionCharts() {
        try {
            const response = await fetch('/api/divisions/analytics');
            const data = await response.json();
            
            if (data.success) {
                // Update executive division chart
                this.updateExecutiveDivisionChart(data.divisions);
                
                // Update division revenue chart
                this.updateDivisionRevenueChart(data.divisions);
                
                // Update division orders chart
                this.updateDivisionOrdersChart(data.divisions);
                
                console.log('📈 Updated division charts');
            }
        } catch (error) {
            console.error('Error updating division charts:', error);
        }
    }

    /**
     * Update sparklines with real-time data
     */
    async updateSparklines(currentCount) {
        try {
            const response = await fetch('/api/divisions/sparkline');
            const data = await response.json();
            
            if (data.success && window.createSparkline) {
                // Update all division sparklines
                const sparklineElements = document.querySelectorAll('#sparklineDivisions');
                sparklineElements.forEach(element => {
                    if (element) {
                        // Clear existing sparkline
                        const ctx = element.getContext('2d');
                        ctx.clearRect(0, 0, element.width, element.height);
                        
                        // Recreate with new data
                        window.createSparkline('sparklineDivisions', data.data, '#fff');
                    }
                });
            }
        } catch (error) {
            console.error('Error updating sparklines:', error);
        }
    }

    /**
     * Update executive division chart
     */
    updateExecutiveDivisionChart(divisions) {
        const chartElement = document.getElementById('executiveDivisionChart');
        if (chartElement && window.Chart) {
            try {
                const chart = Chart.getChart(chartElement);
                if (chart) {
                    const labels = divisions.slice(0, 8).map(d => d.name || 'Unknown');
                    const data = divisions.slice(0, 8).map(d => (d.total_revenue / 1000000).toFixed(1));
                    
                    chart.data.labels = labels;
                    chart.data.datasets[0].data = data;
                    chart.update('none'); // Update without animation for real-time feel
                }
            } catch (error) {
                console.error('Error updating executive division chart:', error);
            }
        }
    }

    /**
     * Update division revenue chart
     */
    updateDivisionRevenueChart(divisions) {
        const chartElement = document.getElementById('divisionRevenueChart');
        if (chartElement && window.Chart) {
            try {
                const chart = Chart.getChart(chartElement);
                if (chart) {
                    const labels = divisions.slice(0, 6).map(d => d.name || 'Unknown');
                    const data = divisions.slice(0, 6).map(d => (d.total_revenue / 1000000).toFixed(1));
                    
                    chart.data.labels = labels;
                    chart.data.datasets[0].data = data;
                    chart.update('none');
                }
            } catch (error) {
                console.error('Error updating division revenue chart:', error);
            }
        }
    }

    /**
     * Update division orders chart
     */
    updateDivisionOrdersChart(divisions) {
        const chartElement = document.getElementById('divisionOrdersChart');
        if (chartElement && window.Chart) {
            try {
                const chart = Chart.getChart(chartElement);
                if (chart) {
                    const labels = divisions.slice(0, 6).map(d => d.name || 'Unknown');
                    const data = divisions.slice(0, 6).map(d => d.order_count || 0);
                    
                    chart.data.labels = labels;
                    chart.data.datasets[0].data = data;
                    chart.update('none');
                }
            } catch (error) {
                console.error('Error updating division orders chart:', error);
            }
        }
    }

    /**
     * Animate counter update with visual feedback
     */
    animateCounterUpdate(element) {
        element.style.transition = 'all 0.3s ease';
        element.style.transform = 'scale(1.1)';
        element.style.color = '#28a745';
        
        setTimeout(() => {
            element.style.transform = 'scale(1)';
            element.style.color = '';
        }, 300);
    }

    /**
     * Force immediate update of all components
     */
    forceUpdate() {
        console.log('🔄 Forcing immediate division update...');
        this.updateAll();
    }

    /**
     * Get last update timestamp
     */
    getLastUpdate() {
        return this.lastUpdate;
    }
}

// Global instance
window.divisionRealtimeUpdater = new DivisionRealtimeUpdater();

// Auto-start on dashboard pages
document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on a dashboard page
    const isDashboard = window.location.pathname.includes('/dashboard') || 
                       document.getElementById('kpiDivisions') !== null;
    
    if (isDashboard) {
        console.log('🚀 Initializing division real-time updates for dashboard');
        window.divisionRealtimeUpdater.startAutoRefresh();
    }
});

// Clean up on page unload
window.addEventListener('beforeunload', function() {
    if (window.divisionRealtimeUpdater) {
        window.divisionRealtimeUpdater.stopAutoRefresh();
    }
});
