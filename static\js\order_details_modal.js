/**
 * Order Details Modal - Bootstrap 4 Compatible
 * Simple and robust modal system for displaying order details with QR code
 */

// Global variables
let currentOrderId = null;

/**
 * Alias function for backward compatibility
 * @param {string} orderId - The order ID to display
 */
function viewOrderDetails(orderId) {
    showOrderDetails(orderId);
}

/**
 * Show order details modal
 * @param {string} orderId - The order ID to display
 */
function showOrderDetails(orderId) {
    console.log('🔍 showOrderDetails called with orderId:', orderId);
    
    if (!orderId) {
        console.error('❌ Order ID is required');
        return;
    }
    
    currentOrderId = orderId;
    
    // Show modal
    $('#orderDetailsModal').modal('show');
    
    // Reset modal state
    resetModalState();
    
    // Load order data
    loadOrderDetails(orderId);
}

/**
 * Reset modal to loading state
 */
function resetModalState() {
    console.log('🔄 Resetting modal state');
    
    // Show loading, hide content and error
    $('#modalLoadingState').show();
    $('#modalContent').hide();
    $('#modalErrorState').hide();
    
    // Reset title
    $('#modalOrderTitle').text('Order Details');
    
    // Clear all data fields
    $('#orderIdDisplay').text('-');
    $('#customerNameDisplay').text('-');
    $('#customerPhoneDisplay').text('-');
    $('#orderStatusDisplay').text('-');
    $('#orderDateDisplay').text('-');
    $('#orderAmountDisplay').text('-');
    $('#deliveryAddressDisplay').text('-');
    $('#deliveryCityDisplay').text('-');
    $('#deliveryPostalDisplay').text('-');
    $('#orderPriorityDisplay').text('-');
    $('#orderItemsTableBody').empty();
    $('#totalItemsDisplay').text('-');
    $('#totalQuantityDisplay').text('-');
    $('#totalAmountDisplay').text('-');
    $('#qrCodeContainer').hide();
}

/**
 * Extract order data directly from table row
 * @param {string} orderId - The order ID to extract
 */
function extractOrderDataFromTable(orderId) {
    console.log('📊 Extracting order data from table for:', orderId);

    // Find the order row in the table
    const orderRows = document.querySelectorAll('tbody tr');
    let orderRow = null;

    for (const row of orderRows) {
        const orderIdCell = row.querySelector('td:first-child strong');
        if (orderIdCell && orderIdCell.textContent.trim() === orderId) {
            orderRow = row;
            break;
        }
    }

    if (!orderRow) {
        console.log('❌ Order row not found in table, trying alternative search...');
        // Try alternative search
        for (const row of orderRows) {
            if (row.textContent.includes(orderId)) {
                orderRow = row;
                console.log('✅ Found order row with alternative search');
                break;
            }
        }
    }

    if (!orderRow) {
        console.log('❌ Order row still not found, creating basic data');
        // Create basic order data as fallback
        return {
            success: true,
            order: {
                order_id: orderId,
                customer_name: 'Customer information not available',
                order_date: new Date().toISOString().split('T')[0],
                status: 'Pending Packing',
                order_amount: '0.00',
                delivery_address: 'Address not available from table',
                delivery_city: 'City not available',
                delivery_postal_code: 'Postal code not available',
                priority: 'Normal'
            },
            order_items: [
                {
                    product_name: 'Product details will be loaded from database',
                    quantity: '1',
                    unit_price: '0.00',
                    total_price: '0.00'
                }
            ],
            summary: {
                total_items: 1,
                total_quantity: '1',
                subtotal: '0.00'
            }
        };
    }

    // Extract data from table cells
    const cells = orderRow.querySelectorAll('td');
    console.log('📊 Found', cells.length, 'cells in order row');

    // Extract customer name from second cell
    let customerName = 'Unknown Customer';
    if (cells.length > 1) {
        const customerCell = cells[1];
        const strongElement = customerCell.querySelector('strong');
        if (strongElement) {
            customerName = strongElement.textContent.trim();
        } else {
            customerName = customerCell.textContent.trim().split('\n')[0];
        }
    }

    // Extract items info from third cell
    let itemsInfo = '1 items';
    if (cells.length > 3) {
        itemsInfo = cells[3].textContent.trim();
    }

    // Extract priority from fourth cell
    let priority = 'Normal';
    if (cells.length > 4) {
        const priorityCell = cells[4];
        const badge = priorityCell.querySelector('.badge');
        if (badge) {
            priority = badge.textContent.trim();
        }
    }

    // Create order data from extracted information
    const orderData = {
        success: true,
        order: {
            order_id: orderId,
            customer_name: customerName,
            order_date: new Date().toISOString().split('T')[0],
            status: 'Pending Packing',
            order_amount: '0.00',
            delivery_address: 'Address details will be loaded from database',
            delivery_city: 'City not available',
            delivery_postal_code: 'Postal code not available',
            priority: priority
        },
        order_items: [
            {
                product_name: 'Product details will be loaded from database',
                quantity: itemsInfo.split(' ')[0] || '1',
                unit_price: '0.00',
                total_price: '0.00'
            }
        ],
        summary: {
            total_items: 1,
            total_quantity: itemsInfo.split(' ')[0] || '1',
            subtotal: '0.00'
        }
    };

    console.log('✅ Extracted order data:', orderData);
    return orderData;
}

/**
 * Load order details from API
 * @param {string} orderId - The order ID to load
 */
function loadOrderDetails(orderId) {
    console.log('📡 Loading order details for:', orderId);
    console.log('📡 Current URL:', window.location.href);

    // Try API endpoint first, then fallback to direct route
    console.log('📡 Making API call for order details...');

    const apiUrl = `/api/order-details/${orderId}`;
    const fallbackUrl = `/orders/${orderId}/details`;
    console.log('📡 Primary API URL:', apiUrl);
    console.log('📡 Fallback URL:', fallbackUrl);

    // Try primary API endpoint
    fetch(apiUrl)
        .then(response => {
            console.log('📡 Primary API response status:', response.status);

            if (!response.ok) {
                console.log('📡 Primary API failed, trying fallback...');
                throw new Error(`Primary API failed: HTTP ${response.status}`);
            }

            return response.json();
        })
        .then(data => {
            console.log('📊 Primary API response data:', data);
            console.log('📊 API success:', data.success);

            if (data.success) {
                console.log('✅ Primary API call successful, populating modal...');
                populateOrderDetails(data);
                loadQRCode(orderId);
            } else {
                console.error('❌ Primary API returned success=false:', data.message || data.error);
                throw new Error(data.error || data.message || 'Primary API failed');
            }
        })
        .catch(primaryError => {
            console.log('📡 Primary API failed, trying fallback route...');
            console.log('📡 Primary error:', primaryError.message);

            // Try fallback route
            fetch(fallbackUrl)
                .then(response => {
                    console.log('📡 Fallback API response status:', response.status);

                    if (!response.ok) {
                        throw new Error(`Fallback API failed: HTTP ${response.status}`);
                    }

                    return response.json();
                })
                .then(data => {
                    console.log('📊 Fallback API response data:', data);

                    if (data.success) {
                        console.log('✅ Fallback API call successful, populating modal...');
                        populateOrderDetails(data);
                        loadQRCode(orderId);
                    } else {
                        throw new Error(data.error || data.message || 'Fallback API failed');
                    }
                })
                .catch(fallbackError => {
                    console.error('❌ Both API endpoints failed:', fallbackError);

                    // Try table extraction as final fallback
                    console.log('📊 Both APIs failed, trying table extraction as final fallback...');
                    const tableData = extractOrderDataFromTable(orderId);
                    console.log('📊 Table data:', tableData);

                    if (tableData && tableData.order && tableData.order.customer_name !== 'Customer information not available') {
                        console.log('✅ Table extraction successful, populating modal...');
                        populateOrderDetails(tableData);
                        loadQRCode(orderId);
                    } else {
                        console.error('❌ All data loading methods failed');
                        showErrorState('Unable to load order details. Please refresh the page and try again.');
                    }
                });
        });
}

/**
 * Populate order details in the modal
 * @param {Object} data - Order data from API or table
 */
function populateOrderDetails(data) {
    console.log('📋 Displaying order details');
    console.log('📋 Data received:', data);

    const order = data.order;
    // Handle both 'order_items' and 'items' for compatibility
    const items = data.order_items || data.items || [];
    const summary = data.summary || {};

    console.log('📋 Order object:', order);
    console.log('📋 Items array:', items);
    console.log('📋 Summary object:', summary);

    if (!order) {
        console.error('❌ No order data provided');
        showErrorState('Invalid order data received');
        return;
    }
    
    // Update modal title
    $('#modalOrderTitle').text(`Order ${order.order_id}`);
    
    // Populate order information
    $('#orderIdDisplay').text(order.order_id || '-');
    $('#customerNameDisplay').text(order.customer_name || '-');
    $('#customerPhoneDisplay').text(order.customer_phone || '-');
    $('#orderStatusDisplay').html(getStatusBadge(order.status));
    $('#orderDateDisplay').text(formatDate(order.order_date));
    $('#orderAmountDisplay').text(`Rs.${parseFloat(order.order_amount || 0).toFixed(2)}`);
    
    // Populate delivery information - handle multiple address field names
    const address = order.delivery_address || order.customer_address || order.shipping_address || '-';
    const city = order.delivery_city || order.customer_city || '-';
    const postal = order.delivery_postal_code || order.postal_code || '-';

    $('#deliveryAddressDisplay').text(address);
    $('#deliveryCityDisplay').text(city);
    $('#deliveryPostalDisplay').text(postal);
    $('#orderPriorityDisplay').html(getPriorityBadge(order.priority));
    
    // Populate order items
    populateOrderItems(items);
    
    // Populate summary
    $('#totalItemsDisplay').text(summary.total_items || items.length);
    $('#totalQuantityDisplay').text(summary.total_quantity || calculateTotalQuantity(items));
    $('#totalAmountDisplay').text(`Rs.${parseFloat(order.order_amount || 0).toFixed(2)}`);
    
    // Update button actions
    $('#printAddressBtn').attr('onclick', `printOrderAddress('${order.order_id}')`);
    $('#viewFullDetailsBtn').attr('onclick', `viewFullOrderDetails('${order.order_id}')`);
    
    // Hide loading, show content
    $('#modalLoadingState').hide();
    $('#modalContent').show();
    
    console.log('✅ Order details displayed successfully');
}

/**
 * Populate order items table
 * @param {Array} items - Array of order items
 */
function populateOrderItems(items) {
    const tbody = $('#orderItemsTableBody');
    tbody.empty();
    
    if (!items || items.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="4" class="text-center text-muted">No items found</td>
            </tr>
        `);
        return;
    }
    
    items.forEach(item => {
        const quantity = parseInt(item.quantity || 0);
        const unitPrice = parseFloat(item.unit_price || 0);
        const total = quantity * unitPrice;
        
        tbody.append(`
            <tr>
                <td>
                    <strong>${item.product_name || item.name || 'Unknown Product'}</strong>
                    ${item.product_id ? `<br><small class="text-muted">ID: ${item.product_id}</small>` : ''}
                </td>
                <td class="text-center">
                    <span class="badge badge-primary">${quantity}</span>
                </td>
                <td class="text-right">Rs.${unitPrice.toFixed(2)}</td>
                <td class="text-right font-weight-bold">Rs.${total.toFixed(2)}</td>
            </tr>
        `);
    });
}

/**
 * Load QR code for the order
 * @param {string} orderId - The order ID
 */
function loadQRCode(orderId) {
    console.log('📱 Loading QR code for:', orderId);
    
    const qrCodeUrl = `/api/order-qr-code/${orderId}`;
    const qrImage = $('#qrCodeImage');
    
    // Set image source and show container when loaded
    qrImage.on('load', function() {
        console.log('✅ QR code loaded successfully');
        $('#qrCodeContainer').show();
    });
    
    qrImage.on('error', function() {
        console.warn('⚠️ QR code failed to load');
        $('#qrCodeContainer').hide();
    });
    
    qrImage.attr('src', qrCodeUrl);
}

/**
 * Show error state in modal
 * @param {string} message - Error message to display
 */
function showErrorState(message) {
    console.log('❌ Showing error state:', message);
    
    $('#modalLoadingState').hide();
    $('#modalContent').hide();
    $('#modalErrorState').show();
    $('#errorMessage').text(message);
}

/**
 * Retry loading order details
 */
function retryLoadOrder() {
    console.log('🔄 Retrying order load');
    
    if (currentOrderId) {
        resetModalState();
        loadOrderDetails(currentOrderId);
    }
}

/**
 * Get status badge HTML
 * @param {string} status - Order status
 * @returns {string} Badge HTML
 */
function getStatusBadge(status) {
    const statusMap = {
        'pending': 'badge-warning',
        'confirmed': 'badge-info',
        'packing': 'badge-primary',
        'packed': 'badge-success',
        'dispatched': 'badge-dark',
        'delivered': 'badge-success',
        'cancelled': 'badge-danger'
    };
    
    const badgeClass = statusMap[status?.toLowerCase()] || 'badge-secondary';
    return `<span class="badge ${badgeClass}">${status || 'Unknown'}</span>`;
}

/**
 * Get priority badge HTML
 * @param {string} priority - Order priority
 * @returns {string} Badge HTML
 */
function getPriorityBadge(priority) {
    const priorityMap = {
        'high': 'badge-danger',
        'medium': 'badge-warning',
        'normal': 'badge-success',
        'low': 'badge-secondary'
    };
    
    const badgeClass = priorityMap[priority?.toLowerCase()] || 'badge-secondary';
    return `<span class="badge ${badgeClass}">${priority || 'Normal'}</span>`;
}

/**
 * Format date string
 * @param {string} dateString - Date string to format
 * @returns {string} Formatted date
 */
function formatDate(dateString) {
    if (!dateString) return '-';
    
    try {
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    } catch (error) {
        return dateString;
    }
}

/**
 * Calculate total quantity from items
 * @param {Array} items - Array of order items
 * @returns {number} Total quantity
 */
function calculateTotalQuantity(items) {
    if (!items || items.length === 0) return 0;
    
    return items.reduce((total, item) => {
        return total + parseInt(item.quantity || 0);
    }, 0);
}

/**
 * Print order address
 * @param {string} orderId - Order ID
 */
function printOrderAddress(orderId) {
    console.log('🖨️ Printing address for order:', orderId);
    
    if (orderId) {
        window.open(`/orders/${orderId}/print-address`, '_blank');
    }
}

/**
 * View full order details
 * @param {string} orderId - Order ID
 */
function viewFullOrderDetails(orderId) {
    console.log('👁️ Viewing full details for order:', orderId);
    
    if (orderId) {
        window.open(`/orders/${orderId}/details`, '_blank');
    }
}

// Initialize when document is ready
$(document).ready(function() {
    console.log('✅ Order details modal JavaScript loaded');
    
    // Handle modal events
    $('#orderDetailsModal').on('hidden.bs.modal', function() {
        console.log('🔒 Modal closed');
        currentOrderId = null;
    });
});
