/**
 * Real-Time Updates System for Medivent Pharmaceuticals ERP
 * Auto-refreshes data without page reload
 */

class RealTimeUpdater {
    constructor(options = {}) {
        this.updateInterval = options.interval || 30000; // 30 seconds default
        this.endpoints = options.endpoints || [];
        this.isActive = true;
        this.intervalId = null;
        this.lastUpdateTime = new Date();
        
        // Initialize update indicator
        this.createUpdateIndicator();
        
        // Start auto-updates
        this.startAutoUpdate();
        
        // Handle page visibility changes
        this.handleVisibilityChange();
    }
    
    createUpdateIndicator() {
        // Create a small indicator to show when data is being updated
        const indicator = document.createElement('div');
        indicator.id = 'realtime-indicator';
        indicator.innerHTML = `
            <div class="realtime-status">
                <i class="fas fa-circle text-success" id="status-icon"></i>
                <small class="text-muted">Live Updates</small>
                <small class="text-muted" id="last-update">Just now</small>
            </div>
        `;
        indicator.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(255,255,255,0.9);
            padding: 5px 10px;
            border-radius: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            z-index: 1000;
            font-size: 12px;
        `;
        document.body.appendChild(indicator);
    }
    
    updateIndicator(status = 'success') {
        const icon = document.getElementById('status-icon');
        const lastUpdate = document.getElementById('last-update');
        
        if (icon) {
            icon.className = status === 'loading' ? 'fas fa-spinner fa-spin text-warning' : 
                           status === 'error' ? 'fas fa-circle text-danger' : 
                           'fas fa-circle text-success';
        }
        
        if (lastUpdate) {
            const now = new Date();
            lastUpdate.textContent = now.toLocaleTimeString();
        }
    }
    
    async fetchData(endpoint) {
        try {
            this.updateIndicator('loading');
            const response = await fetch(endpoint, {
                method: 'GET',
                credentials: 'same-origin',  // Include cookies for authentication
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                // If unauthorized, don't show error - just return null
                if (response.status === 401 || response.status === 403) {
                    console.log('Authentication required for:', endpoint);
                    this.updateIndicator('error');
                    return null;
                }
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // Check if response is actually JSON
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                console.log('Non-JSON response received for:', endpoint);
                this.updateIndicator('error');
                return null;
            }

            const data = await response.json();
            this.updateIndicator('success');
            return data;
        } catch (error) {
            // Handle JSON parsing errors specifically
            if (error.message.includes('Unexpected token') || error.message.includes('unexpected char')) {
                console.error('JSON parsing error for', endpoint, '- likely authentication issue');
            } else {
                console.error('Error fetching data from', endpoint, ':', error);
            }
            this.updateIndicator('error');
            return null;
        }
    }
    
    async updateDashboardCards() {
        const data = await this.fetchData('/api/dashboard-data');
        if (data) {
            // Update dashboard cards
            this.updateElement('#orders-count', data.orders_count);
            this.updateElement('#pending-approvals', data.pending_approvals);
            this.updateElement('#products-count', data.products_count);
            this.updateElement('#low-stock-count', data.low_stock_count);
            
            // Update status counts
            if (data.status_counts) {
                Object.keys(data.status_counts).forEach(status => {
                    this.updateElement(`#status-${status.toLowerCase().replace(/\s+/g, '-')}`, data.status_counts[status]);
                });
            }
        }
    }
    
    async updateFinanceReports() {
        const data = await this.fetchData('/api/finance-data');
        if (data) {
            // Update finance metrics
            this.updateElement('#total-revenue', this.formatCurrency(data.total_revenue));
            this.updateElement('#monthly-sales', this.formatCurrency(data.monthly_sales));
            this.updateElement('#pending-receivables', this.formatCurrency(data.pending_receivables));
            this.updateElement('#collection-ratio', data.collection_ratio + '%');
            
            // Update charts if they exist
            if (window.updateFinanceCharts && typeof window.updateFinanceCharts === 'function') {
                window.updateFinanceCharts(data);
            }
        }
    }
    
    async updateSalesReports() {
        const data = await this.fetchData('/api/sales-data');
        if (data) {
            // Update sales metrics
            this.updateElement('#daily-sales', this.formatCurrency(data.daily_sales));
            this.updateElement('#weekly-sales', this.formatCurrency(data.weekly_sales));
            this.updateElement('#monthly-sales-report', this.formatCurrency(data.monthly_sales));
            this.updateElement('#total-orders', data.total_orders);
            
            // Update division-wise sales
            if (data.division_sales) {
                Object.keys(data.division_sales).forEach(division => {
                    this.updateElement(`#${division.toLowerCase()}-sales`, this.formatCurrency(data.division_sales[division]));
                });
            }
            
            // Update charts
            if (window.updateSalesCharts && typeof window.updateSalesCharts === 'function') {
                window.updateSalesCharts(data);
            }
        }
    }
    
    async updateCEODashboard() {
        const data = await this.fetchData('/api/ceo-dashboard-data');
        if (data) {
            // Update CEO metrics
            this.updateElement('#total-business-value', this.formatCurrency(data.total_business_value));
            this.updateElement('#growth-rate', data.growth_rate + '%');
            this.updateElement('#market-share', data.market_share + '%');
            this.updateElement('#customer-satisfaction', data.customer_satisfaction + '%');
            
            // Update division performance
            if (data.division_performance) {
                Object.keys(data.division_performance).forEach(division => {
                    const perf = data.division_performance[division];
                    this.updateElement(`#${division.toLowerCase()}-performance`, perf.achievement + '%');
                    this.updateElement(`#${division.toLowerCase()}-revenue`, this.formatCurrency(perf.revenue));
                });
            }
            
            // Update executive charts
            if (window.updateCEOCharts && typeof window.updateCEOCharts === 'function') {
                window.updateCEOCharts(data);
            }
        }
    }
    
    updateElement(selector, value) {
        const element = document.querySelector(selector);
        if (element) {
            // Add smooth transition effect
            element.style.transition = 'all 0.3s ease';
            element.style.opacity = '0.7';
            
            setTimeout(() => {
                element.textContent = value;
                element.style.opacity = '1';
            }, 150);
        }
    }
    
    formatCurrency(value) {
        if (value === null || value === undefined) return 'Rs.0';
        return 'Rs.' + Number(value).toLocaleString();
    }
    
    async performUpdate() {
        if (!this.isActive) return;
        
        const currentPage = window.location.pathname;
        
        try {
            // Update based on current page
            if (currentPage === '/dashboard' || currentPage === '/') {
                await this.updateDashboardCards();
            } else if (currentPage.includes('/finance')) {
                await this.updateFinanceReports();
            } else if (currentPage.includes('/reports')) {
                await this.updateSalesReports();
            } else if (currentPage.includes('/ceo-dashboard')) {
                await this.updateCEODashboard();
            }
            
            this.lastUpdateTime = new Date();
        } catch (error) {
            console.error('Error during update:', error);
        }
    }
    
    startAutoUpdate() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
        }
        
        this.intervalId = setInterval(() => {
            this.performUpdate();
        }, this.updateInterval);
        
        // Perform initial update
        setTimeout(() => this.performUpdate(), 1000);
    }
    
    stopAutoUpdate() {
        this.isActive = false;
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
    }
    
    handleVisibilityChange() {
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.stopAutoUpdate();
            } else {
                this.isActive = true;
                this.startAutoUpdate();
            }
        });
    }
    
    setUpdateInterval(interval) {
        this.updateInterval = interval;
        this.startAutoUpdate();
    }
}

// Initialize real-time updates when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Create global real-time updater
    window.realtimeUpdater = new RealTimeUpdater({
        interval: 30000 // Update every 30 seconds
    });
    
    // Add manual refresh button
    const refreshBtn = document.createElement('button');
    refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i>';
    refreshBtn.className = 'btn btn-sm btn-outline-primary';
    refreshBtn.style.cssText = 'position: fixed; top: 50px; right: 10px; z-index: 1000;';
    refreshBtn.title = 'Manual Refresh';
    refreshBtn.onclick = () => {
        refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        window.realtimeUpdater.performUpdate().then(() => {
            refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i>';
        });
    };
    document.body.appendChild(refreshBtn);
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = RealTimeUpdater;
}
