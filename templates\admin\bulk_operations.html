{% extends 'base.html' %}

{% block title %}Bulk Operations - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-tasks text-primary"></i> Bulk Operations
        </h1>
        <div>
            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary shadow-sm">
                <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Bulk Data Operations</h6>
                </div>
                <div class="card-body">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Bulk Import</h5>
                            <form method="post" enctype="multipart/form-data">
                                <div class="form-group">
                                    <label>Select File</label>
                                    <input type="file" class="form-control" name="bulk_file" accept=".csv,.xlsx">
                                </div>
                                <div class="form-group">
                                    <label>Operation Type</label>
                                    <select class="form-control" name="operation_type">
                                        <option value="products">Import Products</option>
                                        <option value="customers">Import Customers</option>
                                        <option value="inventory">Import Inventory</option>
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-upload"></i> Import Data
                                </button>
                            </form>
                        </div>
                        <div class="col-md-6">
                            <h5>Bulk Export</h5>
                            <div class="list-group">
                                <a href="{{ url_for('export_orders_excel') }}" class="list-group-item list-group-item-action">
                                    <i class="fas fa-file-excel"></i> Export Orders
                                </a>
                                <a href="{{ url_for('export_inventory_excel') }}" class="list-group-item list-group-item-action">
                                    <i class="fas fa-file-excel"></i> Export Inventory
                                </a>
                                <a href="#" class="list-group-item list-group-item-action">
                                    <i class="fas fa-file-excel"></i> Export Customers
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Page-specific JavaScript
document.addEventListener('DOMContentLoaded', function() {
    console.log('Bulk Operations page loaded');
});
</script>
{% endblock %}