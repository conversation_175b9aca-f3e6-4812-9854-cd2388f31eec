{% extends 'base.html' %}

{% block title %}Automated Payment Matching - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-link text-primary"></i> Automated Payment Matching
        </h1>
        <a href="{{ url_for('advanced_payment.dashboard') }}" class="btn btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Payment Management
        </a>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">Unmatched Payments</h6>
                </div>
                <div class="card-body">
                    {% if unmatched_payments %}
                        <div class="table-responsive">
                            <table class="table table-bordered table-sm">
                                <thead>
                                    <tr>
                                        <th>Payment ID</th>
                                        <th>Amount</th>
                                        <th>Date</th>
                                        <th>Method</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for payment in unmatched_payments %}
                                    <tr>
                                        <td>{{ payment.payment_id }}</td>
                                        <td>₹{{ "%.2f"|format(payment.amount) }}</td>
                                        <td>{{ payment.payment_date }}</td>
                                        <td>{{ payment.payment_method }}</td>
                                        <td>
                                            <button class="btn btn-sm btn-primary" onclick="matchPayment({{ payment.payment_id }})">
                                                <i class="fas fa-link"></i> Match
                                            </button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> All payments are matched!
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">Unmatched Orders</h6>
                </div>
                <div class="card-body">
                    {% if unmatched_orders %}
                        <div class="table-responsive">
                            <table class="table table-bordered table-sm">
                                <thead>
                                    <tr>
                                        <th>Order ID</th>
                                        <th>Customer</th>
                                        <th>Outstanding</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for order in unmatched_orders %}
                                    <tr>
                                        <td>{{ order.order_id }}</td>
                                        <td>{{ order.customer_name }}</td>
                                        <td>₹{{ "%.2f"|format(order.outstanding) }}</td>
                                        <td>
                                            <button class="btn btn-sm btn-success" onclick="matchOrder({{ order.order_id }})">
                                                <i class="fas fa-link"></i> Match
                                            </button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> All orders have payments!
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function matchPayment(paymentId) {
    alert('Matching payment ' + paymentId + '...');
    // Implementation for payment matching
}

function matchOrder(orderId) {
    alert('Matching order ' + orderId + '...');
    // Implementation for order matching
}
</script>
{% endblock %}