{% extends 'base.html' %}

{% block title %}Bulk Payment Processing - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-layer-group text-primary"></i> Bulk Payment Processing
        </h1>
        <a href="{{ url_for('advanced_payment.dashboard') }}" class="btn btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Payment Management
        </a>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Customers with Outstanding Balances</h6>
                </div>
                <div class="card-body">
                    <form method="post" action="{{ url_for('advanced_payment.process_bulk_payment') }}">
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label>Payment Amount</label>
                                <input type="number" class="form-control" name="payment_amount" step="0.01" required>
                            </div>
                            <div class="col-md-3">
                                <label>Payment Method</label>
                                <select class="form-control" name="payment_method">
                                    <option value="Cash">Cash</option>
                                    <option value="Bank Transfer">Bank Transfer</option>
                                    <option value="Cheque">Cheque</option>
                                    <option value="Credit Card">Credit Card</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label>Payment Date</label>
                                <input type="date" class="form-control" name="payment_date" value="{{ now.strftime('%Y-%m-%d') }}">
                            </div>
                            <div class="col-md-3">
                                <label>&nbsp;</label>
                                <button type="submit" class="btn btn-primary form-control">
                                    <i class="fas fa-credit-card"></i> Process Bulk Payment
                                </button>
                            </div>
                        </div>
                        
                        {% if customers_with_balance %}
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th><input type="checkbox" id="select-all"></th>
                                            <th>Customer</th>
                                            <th>Email</th>
                                            <th>Outstanding Balance</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for customer in customers_with_balance %}
                                        <tr>
                                            <td><input type="checkbox" name="customer_ids" value="{{ customer.customer_id }}"></td>
                                            <td>{{ customer.name }}</td>
                                            <td>{{ customer.email }}</td>
                                            <td>₹{{ "%.2f"|format(customer.outstanding_balance) }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i> No customers with outstanding balances found.
                            </div>
                        {% endif %}
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('select-all').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('input[name="customer_ids"]');
    checkboxes.forEach(checkbox => checkbox.checked = this.checked);
});
</script>
{% endblock %}