<!-- Enhanced Live Rider Tracking Sidebar Component -->
<div id="liveTrackingSidebar" class="live-tracking-sidebar enhanced-sidebar">
    <div class="sidebar-header">
        <h6 class="mb-0">
            <i class="fas fa-motorcycle text-success"></i> Live Rider Tracking
            <button class="btn btn-sm btn-outline-light float-right" onclick="toggleSidebar()" id="sidebarCloseBtn">
                <i class="fas fa-chevron-right"></i>
            </button>
        </h6>
        <small class="text-muted">Real-time rider status & delivery tracking</small>
    </div>
    
    <div class="sidebar-content">
        <!-- Connection Status -->
        <div class="connection-status mb-3">
            <div id="connectionIndicator" class="d-flex align-items-center">
                <div class="status-dot bg-success"></div>
                <small class="text-muted ml-2">Connected</small>
            </div>
        </div>

        <!-- Rider Status Overview -->
        <div class="tracking-section">
            <h6 class="section-title">
                <i class="fas fa-users"></i> Rider Status Overview
                <button class="btn btn-sm btn-outline-primary ml-2" onclick="refreshRiderStatus()">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </h6>
            <div id="riderStatusGrid" class="rider-status-grid">
                <!-- Rider status cards will be populated here -->
            </div>
        </div>

        <!-- Active Deliveries Section -->
        <div class="tracking-section">
            <h6 class="section-title">
                <i class="fas fa-motorcycle"></i> Active Deliveries
                <span id="activeOrdersCount" class="badge badge-primary">0</span>
            </h6>
            <div id="activeOrdersList" class="orders-list enhanced-orders-list">
                <!-- Active orders will be populated here -->
            </div>
        </div>

        <!-- Recent Updates Section -->
        <div class="tracking-section">
            <h6 class="section-title">
                <i class="fas fa-clock"></i> Recent Updates
            </h6>
            <div id="recentUpdatesList" class="updates-list">
                <!-- Recent updates will be populated here -->
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="tracking-section">
            <h6 class="section-title">
                <i class="fas fa-chart-line"></i> Today's Stats
            </h6>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="todayDelivered">0</div>
                    <div class="stat-label">Delivered</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="todayPending">0</div>
                    <div class="stat-label">Pending</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="todayRevenue">Rs.0</div>
                    <div class="stat-label">Revenue</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Sidebar Toggle Button (when collapsed) -->
    <div id="sidebarToggle" class="sidebar-toggle enhanced-toggle" onclick="toggleSidebar()" style="display: none;">
        <i class="fas fa-motorcycle"></i>
        <span class="toggle-text">Rider Tracking</span>
        <div class="quick-stats">
            <span id="quickActiveCount" class="quick-stat">0</span>
            <small>Active</small>
        </div>
    </div>
</div>

<!-- Live Tracking Styles -->
<style>
.live-tracking-sidebar {
    position: fixed;
    top: 0;
    right: 0;
    width: 380px;
    height: 100vh;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
    color: white;
    z-index: 1050;
    transform: translateX(0);
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    box-shadow: -5px 0 20px rgba(0,0,0,0.3);
    overflow-y: auto;
    border-left: 3px solid #3498db;
}

.enhanced-sidebar {
    backdrop-filter: blur(10px);
}

.live-tracking-sidebar.collapsed {
    transform: translateX(100%);
}

.sidebar-header {
    padding: 1rem;
    background: rgba(0,0,0,0.1);
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.sidebar-content {
    padding: 1rem;
}

.connection-status {
    text-align: center;
    padding: 0.5rem;
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.tracking-section {
    margin-bottom: 1.5rem;
}

.section-title {
    font-size: 0.9rem;
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(255,255,255,0.2);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* Enhanced Rider Status Grid */
.rider-status-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.rider-status-card {
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    padding: 0.75rem;
    border-left: 4px solid;
    transition: all 0.3s ease;
    cursor: pointer;
}

.rider-status-card:hover {
    background: rgba(255,255,255,0.15);
    transform: translateX(-2px);
}

.rider-status-card.available {
    border-left-color: #27ae60;
}

.rider-status-card.delivering {
    border-left-color: #f39c12;
}

.rider-status-card.returning {
    border-left-color: #3498db;
}

.rider-status-card.offline {
    border-left-color: #95a5a6;
}

.rider-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.rider-name {
    font-weight: 600;
    font-size: 0.9rem;
}

.rider-status-badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-weight: 500;
}

.rider-details {
    font-size: 0.75rem;
    opacity: 0.9;
    line-height: 1.4;
}

.rider-details .detail-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.2rem;
}

/* Enhanced Orders List */
.enhanced-orders-list .order-item {
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    padding: 0.75rem;
    margin-bottom: 0.75rem;
    border-left: 4px solid #3498db;
    transition: all 0.3s ease;
}

.enhanced-orders-list .order-item:hover {
    background: rgba(255,255,255,0.15);
    transform: translateX(-2px);
}

.order-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.order-id {
    font-weight: 600;
    color: #3498db;
}

.order-status-badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    background: rgba(52, 152, 219, 0.2);
    color: #3498db;
}

.order-details {
    font-size: 0.75rem;
    opacity: 0.9;
}

.order-progress {
    margin-top: 0.5rem;
}

.progress-bar {
    height: 3px;
    background: rgba(255,255,255,0.2);
    border-radius: 2px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: #27ae60;
    transition: width 0.3s ease;
}

.orders-list, .updates-list {
    max-height: 200px;
    overflow-y: auto;
}

.order-item, .update-item {
    background: rgba(255,255,255,0.1);
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    border-radius: 6px;
    font-size: 0.85rem;
}

.order-item:hover {
    background: rgba(255,255,255,0.2);
    cursor: pointer;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 0.5rem;
}

.stat-item {
    text-align: center;
    background: rgba(255,255,255,0.1);
    padding: 0.75rem 0.5rem;
    border-radius: 6px;
}

.stat-value {
    font-weight: bold;
    font-size: 1.1rem;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.7rem;
    opacity: 0.8;
}

.sidebar-toggle {
    position: fixed;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 1rem 0.75rem;
    border-radius: 12px 0 0 12px;
    cursor: pointer;
    z-index: 1049;
    box-shadow: -5px 0 15px rgba(0,0,0,0.3);
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    border: 2px solid #3498db;
    min-height: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.enhanced-toggle {
    backdrop-filter: blur(10px);
}

.sidebar-toggle:hover {
    background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
    transform: translateY(-50%) translateX(-8px);
    box-shadow: -8px 0 25px rgba(0,0,0,0.4);
}

.toggle-text {
    font-size: 0.8rem;
    margin-top: 0.5rem;
    writing-mode: vertical-rl;
    text-orientation: mixed;
    font-weight: 600;
}

.quick-stats {
    margin-top: 0.5rem;
    text-align: center;
}

.quick-stat {
    display: block;
    font-size: 1.2rem;
    font-weight: bold;
    color: #3498db;
}

.quick-stats small {
    font-size: 0.7rem;
    opacity: 0.8;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .live-tracking-sidebar {
        width: 100%;
    }
    
    .sidebar-toggle {
        display: none !important;
    }
}

/* Custom scrollbar for sidebar */
.live-tracking-sidebar::-webkit-scrollbar,
.orders-list::-webkit-scrollbar,
.updates-list::-webkit-scrollbar {
    width: 4px;
}

.live-tracking-sidebar::-webkit-scrollbar-track,
.orders-list::-webkit-scrollbar-track,
.updates-list::-webkit-scrollbar-track {
    background: rgba(255,255,255,0.1);
}

.live-tracking-sidebar::-webkit-scrollbar-thumb,
.orders-list::-webkit-scrollbar-thumb,
.updates-list::-webkit-scrollbar-thumb {
    background: rgba(255,255,255,0.3);
    border-radius: 2px;
}
</style>

<!-- Live Tracking JavaScript -->
<script>
let sidebarVisible = true;
let trackingInterval;
let lastUpdateTime = new Date();

// Initialize live tracking
document.addEventListener('DOMContentLoaded', function() {
    initializeLiveTracking();
    startLiveUpdates();
});

function initializeLiveTracking() {
    // Load initial data
    updateRiderStatus();
    updateActiveOrders();
    updateRecentUpdates();
    updateTodayStats();

    // Set up periodic updates
    trackingInterval = setInterval(function() {
        updateRiderStatus();
        updateActiveOrders();
        updateRecentUpdates();
        updateTodayStats();
    }, 15000); // Update every 15 seconds for more real-time feel
}

function toggleSidebar() {
    const sidebar = document.getElementById('liveTrackingSidebar');
    const toggle = document.getElementById('sidebarToggle');
    const closeBtn = document.getElementById('sidebarCloseBtn');

    if (sidebarVisible) {
        sidebar.classList.add('collapsed');
        toggle.style.display = 'block';
        sidebarVisible = false;

        // Update close button icon
        if (closeBtn) {
            closeBtn.innerHTML = '<i class="fas fa-chevron-right"></i>';
        }
    } else {
        sidebar.classList.remove('collapsed');
        toggle.style.display = 'none';
        sidebarVisible = true;

        // Update close button icon
        if (closeBtn) {
            closeBtn.innerHTML = '<i class="fas fa-chevron-right"></i>';
        }
    }

    // Save preference to localStorage
    localStorage.setItem('sidebarVisible', sidebarVisible);
}

// New function to refresh rider status
function refreshRiderStatus() {
    updateRiderStatus();

    // Show brief loading indicator
    const refreshBtn = document.querySelector('[onclick="refreshRiderStatus()"] i');
    if (refreshBtn) {
        refreshBtn.classList.add('fa-spin');
        setTimeout(() => {
            refreshBtn.classList.remove('fa-spin');
        }, 1000);
    }
}

// New function to update rider status
function updateRiderStatus() {
    fetch('/riders/api/rider-status-data')
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('riderStatusGrid');

            if (data.riders && container) {
                container.innerHTML = data.riders.map(rider => {
                    const statusClass = rider.status.toLowerCase().replace(' ', '');
                    const statusBadgeClass = getStatusBadgeClass(rider.status);

                    return `
                        <div class="rider-status-card ${statusClass}" onclick="viewRiderDetails('${rider.rider_id}')">
                            <div class="rider-info">
                                <span class="rider-name">${rider.name}</span>
                                <span class="rider-status-badge ${statusBadgeClass}">${rider.status}</span>
                            </div>
                            <div class="rider-details">
                                ${rider.current_order ? `
                                    <div class="detail-item">
                                        <span>Order:</span>
                                        <span class="order-id">${rider.current_order}</span>
                                    </div>
                                ` : ''}
                                <div class="detail-item">
                                    <span>Location:</span>
                                    <span>${rider.location || 'Unknown'}</span>
                                </div>
                                ${rider.eta ? `
                                    <div class="detail-item">
                                        <span>ETA:</span>
                                        <span>${rider.eta}</span>
                                    </div>
                                ` : ''}
                                <div class="detail-item">
                                    <span>Distance:</span>
                                    <span>${rider.distance || 'N/A'}</span>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');
            }
        })
        .catch(error => {
            console.error('Error updating rider status:', error);
            const container = document.getElementById('riderStatusGrid');
            if (container) {
                container.innerHTML = '<div class="text-center text-muted">Unable to load rider status</div>';
            }
        });
}

function updateActiveOrders() {
    // Fetch real data from API
    fetch('/riders/api/live-tracking-data')
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('activeOrdersList');
            const countBadge = document.getElementById('activeOrdersCount');
            const quickCount = document.getElementById('quickActiveCount');

            if (data.active_orders) {
                const orderCount = data.active_orders.length;
                countBadge.textContent = orderCount;
                if (quickCount) quickCount.textContent = orderCount;

                container.innerHTML = data.active_orders.map(order => {
                    const progress = calculateDeliveryProgress(order.status);
                    return `
                        <div class="order-item" onclick="viewOrderDetails('${order.order_id}')">
                            <div class="order-header">
                                <span class="order-id">${order.order_id}</span>
                                <span class="order-status-badge">${order.status}</span>
                            </div>
                            <div class="order-details">
                                <div class="detail-item">
                                    <span>Customer:</span>
                                    <span>${order.customer_name}</span>
                                </div>
                                <div class="detail-item">
                                    <span>Rider:</span>
                                    <span>${order.rider_name || 'Unassigned'}</span>
                                </div>
                                ${order.dispatch_time ? `
                                    <div class="detail-item">
                                        <span>Dispatched:</span>
                                        <span>${order.dispatch_time}</span>
                                    </div>
                                ` : ''}
                            </div>
                            <div class="order-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: ${progress}%"></div>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');

                // Update recent updates
                if (data.recent_updates) {
                    updateRecentUpdatesFromData(data.recent_updates);
                }

                // Update today's stats
                if (data.today_stats) {
                    updateTodayStatsFromData(data.today_stats);
                }

                // Update connection status
                updateConnectionStatus(data.last_updated);
            }
        })
        .catch(error => {
            console.error('Error fetching live tracking data:', error);
            updateConnectionStatus(null, true);
        });
}

function updateRecentUpdates() {
    // This function is now called from updateActiveOrders with real data
}

function updateRecentUpdatesFromData(updates) {
    const container = document.getElementById('recentUpdatesList');

    container.innerHTML = updates.map(update => `
        <div class="update-item">
            <div class="d-flex justify-content-between">
                <small>${update.message}</small>
                <small class="text-muted">${update.time_ago}</small>
            </div>
        </div>
    `).join('');
}

function updateTodayStats() {
    // This function is now called from updateActiveOrders with real data
}

function updateTodayStatsFromData(stats) {
    document.getElementById('todayDelivered').textContent = stats.delivered;
    document.getElementById('todayPending').textContent = stats.pending;
    document.getElementById('todayRevenue').textContent = stats.revenue;
}

function startLiveUpdates() {
    // Initial connection status
    updateConnectionStatus(new Date().toLocaleTimeString());
}

function updateConnectionStatus(lastUpdated, hasError = false) {
    const indicator = document.getElementById('connectionIndicator');

    if (hasError) {
        indicator.innerHTML = `
            <div class="status-dot bg-danger"></div>
            <small class="text-light ml-2">Connection Error</small>
        `;
    } else {
        indicator.innerHTML = `
            <div class="status-dot bg-success"></div>
            <small class="text-light ml-2">Live • Updated ${lastUpdated || new Date().toLocaleTimeString()}</small>
        `;
    }
}

function viewOrderDetails(orderId) {
    // Open order details modal or navigate to order page
    if (typeof showOrderDetails === 'function') {
        showOrderDetails(orderId);
    } else {
        window.open(`/orders/${orderId}/details`, '_blank');
    }
}

function viewRiderDetails(riderId) {
    // Open rider details or tracking page
    window.open(`/riders/tracking?rider=${riderId}`, '_blank');
}

function getStatusBadgeClass(status) {
    const statusMap = {
        'Available': 'bg-success',
        'Delivering': 'bg-warning',
        'Returning': 'bg-info',
        'Offline': 'bg-secondary'
    };
    return statusMap[status] || 'bg-secondary';
}

function calculateDeliveryProgress(status) {
    const progressMap = {
        'Dispatched': 25,
        'Out for Delivery': 75,
        'Delivered': 100
    };
    return progressMap[status] || 0;
}

// Load sidebar state from localStorage
document.addEventListener('DOMContentLoaded', function() {
    const savedState = localStorage.getItem('sidebarVisible');
    if (savedState === 'false') {
        toggleSidebar();
    }
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (trackingInterval) {
        clearInterval(trackingInterval);
    }
});
</script>
