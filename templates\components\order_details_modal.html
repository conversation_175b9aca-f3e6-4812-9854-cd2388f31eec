<!-- Order Details Modal - Bootstrap 4 Compatible -->
<div class="modal fade" id="orderDetailsModal" tabindex="-1" role="dialog" aria-labelledby="orderDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header bg-primary text-white">
                <h4 class="modal-title" id="orderDetailsModalLabel">
                    <i class="fas fa-file-alt mr-2"></i>
                    <span id="modalOrderTitle">Order Details</span>
                </h4>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>

            <!-- Modal Body -->
            <div class="modal-body p-0">
                <!-- Loading State -->
                <div id="modalLoadingState" class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                    <p class="mt-3 text-muted">Loading order details...</p>
                </div>

                <!-- Content Container -->
                <div id="modalContent" style="display: none;">
                    <!-- Order Information Section -->
                    <div class="container-fluid p-4">
                        <div class="row">
                            <!-- Order Summary -->
                            <div class="col-md-6">
                                <div class="card border-primary">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0"><i class="fas fa-info-circle mr-2"></i>Order Summary</h5>
                                    </div>
                                    <div class="card-body">
                                        <table class="table table-sm table-borderless">
                                            <tr>
                                                <td><strong>Order ID:</strong></td>
                                                <td id="orderIdDisplay">-</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Customer:</strong></td>
                                                <td id="customerNameDisplay">-</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Phone:</strong></td>
                                                <td id="customerPhoneDisplay">-</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Status:</strong></td>
                                                <td id="orderStatusDisplay">-</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Order Date:</strong></td>
                                                <td id="orderDateDisplay">-</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Total Amount:</strong></td>
                                                <td id="orderAmountDisplay" class="font-weight-bold text-success">-</td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <!-- Delivery Information -->
                            <div class="col-md-6">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h5 class="mb-0"><i class="fas fa-map-marker-alt mr-2"></i>Delivery Information</h5>
                                    </div>
                                    <div class="card-body">
                                        <table class="table table-sm table-borderless">
                                            <tr>
                                                <td><strong>Address:</strong></td>
                                                <td id="deliveryAddressDisplay">-</td>
                                            </tr>
                                            <tr>
                                                <td><strong>City:</strong></td>
                                                <td id="deliveryCityDisplay">-</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Postal Code:</strong></td>
                                                <td id="deliveryPostalDisplay">-</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Priority:</strong></td>
                                                <td id="orderPriorityDisplay">-</td>
                                            </tr>
                                        </table>
                                        
                                        <!-- QR Code Section -->
                                        <div class="text-center mt-3">
                                            <div id="qrCodeContainer" style="display: none;">
                                                <h6 class="text-muted">QR Code</h6>
                                                <img id="qrCodeImage" src="" alt="Order QR Code" class="img-fluid" style="max-width: 150px;">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Order Items Section -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <h5 class="mb-0"><i class="fas fa-list mr-2"></i>Order Items</h5>
                                    </div>
                                    <div class="card-body p-0">
                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover mb-0">
                                                <thead class="thead-light">
                                                    <tr>
                                                        <th>Product</th>
                                                        <th>Quantity</th>
                                                        <th>Unit Price</th>
                                                        <th>Total</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="orderItemsTableBody">
                                                    <!-- Order items will be populated here -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Summary Section -->
                        <div class="row mt-4">
                            <div class="col-md-6 offset-md-6">
                                <div class="card border-warning">
                                    <div class="card-header bg-warning text-dark">
                                        <h5 class="mb-0"><i class="fas fa-calculator mr-2"></i>Order Summary</h5>
                                    </div>
                                    <div class="card-body">
                                        <table class="table table-sm table-borderless">
                                            <tr>
                                                <td><strong>Total Items:</strong></td>
                                                <td id="totalItemsDisplay" class="text-right">-</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Total Quantity:</strong></td>
                                                <td id="totalQuantityDisplay" class="text-right">-</td>
                                            </tr>
                                            <tr class="border-top">
                                                <td><strong>Total Amount:</strong></td>
                                                <td id="totalAmountDisplay" class="text-right font-weight-bold text-success">-</td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Error State -->
                <div id="modalErrorState" style="display: none;" class="text-center py-5">
                    <i class="fas fa-exclamation-triangle fa-4x text-warning mb-3"></i>
                    <h4>Unable to Load Order Details</h4>
                    <p class="text-muted" id="errorMessage">An error occurred while loading order details.</p>
                    <button class="btn btn-primary" onclick="retryLoadOrder()">
                        <i class="fas fa-redo mr-2"></i>Try Again
                    </button>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times mr-2"></i>Close
                </button>
                <button type="button" class="btn btn-info" id="printAddressBtn" onclick="printOrderAddress()">
                    <i class="fas fa-print mr-2"></i>Print Address
                </button>
                <button type="button" class="btn btn-success" id="viewFullDetailsBtn">
                    <i class="fas fa-external-link-alt mr-2"></i>View Full Details
                </button>
            </div>
        </div>
    </div>
</div>

<style>
/* Custom styles for the order details modal */
#orderDetailsModal .modal-content {
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

#orderDetailsModal .card {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#orderDetailsModal .table td {
    padding: 0.5rem;
    vertical-align: middle;
}

#orderDetailsModal .spinner-border {
    width: 3rem;
    height: 3rem;
}

#orderDetailsModal #qrCodeImage {
    border: 2px solid #dee2e6;
    border-radius: 8px;
    padding: 10px;
    background: white;
}

#orderDetailsModal .badge {
    font-size: 0.8em;
}
</style>
