{% extends "base.html" %}
{% block title %}All Customer Order History - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">All Customer Order History</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <a href="{{ url_for('customers') }}" class="btn btn-secondary">
                                <i class="fas fa-users"></i> View All Customers
                            </a>
                            <a href="{{ url_for('customers', view='by_type') }}" class="btn btn-secondary">
                                <i class="fas fa-user-tag"></i> View Customers by Type
                            </a>
                            <a href="{{ url_for('customers', action='add') }}" class="btn btn-secondary">
                                <i class="fas fa-user-plus"></i> Add New Customer
                            </a>
                            <a href="{{ url_for('customers', view='pricing') }}" class="btn btn-secondary">
                                <i class="fas fa-tags"></i> View Customer Pricing
                            </a>
                            <a href="{{ url_for('customers', action='set_pricing') }}" class="btn btn-secondary">
                                <i class="fas fa-dollar-sign"></i> Set Customer Pricing
                            </a>
                        </div>
                    </div>

                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">Total Orders</h6>
                                            <h4>{{ customer_orders|length }}</h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-shopping-cart fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">Total Value</h6>
                                            <h4>Rs.{{ "{:,.0f}".format(customer_orders|sum(attribute='order_amount')) }}</h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-money-bill-wave fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">Delivered Orders</h6>
                                            <h4>{{ customer_orders|selectattr('status', 'equalto', 'Delivered')|list|length }}</h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-check-circle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">Pending Orders</h6>
                                            <h4>{{ customer_orders|rejectattr('status', 'equalto', 'Delivered')|list|length }}</h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-clock fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Search and Filter -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="search">Search Orders:</label>
                                <input type="text" class="form-control" id="search" placeholder="Search by customer name, order ID, or status...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="status_filter">Filter by Status:</label>
                                <select class="form-control" id="status_filter">
                                    <option value="">All Statuses</option>
                                    <option value="Delivered">Delivered</option>
                                    <option value="Dispatched">Dispatched</option>
                                    <option value="Processing">Processing</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="customer_filter">Filter by Customer:</label>
                                <select class="form-control" id="customer_filter">
                                    <option value="">All Customers</option>
                                    {% for order in customer_orders %}
                                        {% if order.customer_name not in (customer_orders|map(attribute='customer_name')|list)[:loop.index-1] %}
                                        <option value="{{ order.customer_name }}">{{ order.customer_name }}</option>
                                        {% endif %}
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Orders Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="ordersTable">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Order ID</th>
                                    <th>Customer</th>
                                    <th>Order Date</th>
                                    <th>Amount</th>
                                    <th>Items</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in customer_orders %}
                                <tr>
                                    <td>
                                        <strong>{{ order.order_id }}</strong>
                                    </td>
                                    <td>
                                        <a href="{{ url_for('customers', view='orders', customer_id=order.customer_id) }}" class="text-decoration-none">
                                            {{ order.customer_name }}
                                        </a>
                                    </td>
                                    <td>{{ order.order_date }}</td>
                                    <td>
                                        <strong class="text-success">₨{{ "{:,.0f}".format(order.order_amount) }}</strong>
                                    </td>
                                    <td>
                                        <span class="badge badge-info">{{ order.item_count }} items</span>
                                    </td>
                                    <td>
                                        {% if order.status == 'Delivered' %}
                                            <span class="badge badge-success">{{ order.status }}</span>
                                        {% elif order.status == 'Dispatched' %}
                                            <span class="badge badge-info">{{ order.status }}</span>
                                        {% elif order.status == 'Processing' %}
                                            <span class="badge badge-warning">{{ order.status }}</span>
                                        {% else %}
                                            <span class="badge badge-secondary">{{ order.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <a href="{{ url_for('customers', view='orders', customer_id=order.customer_id) }}" class="btn btn-sm btn-outline-info" title="Customer Orders">
                                                <i class="fas fa-user"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-success" title="Generate Invoice">
                                                <i class="fas fa-file-invoice"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Export Options -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-secondary text-white">
                                    <h6 class="mb-0">Export Options</h6>
                                </div>
                                <div class="card-body">
                                    <button class="btn btn-success" onclick="exportToExcel()">
                                        <i class="fas fa-file-excel"></i> Export to Excel
                                    </button>
                                    <button class="btn btn-danger" onclick="exportToPDF()">
                                        <i class="fas fa-file-pdf"></i> Export to PDF
                                    </button>
                                    <button class="btn btn-info" onclick="printReport()">
                                        <i class="fas fa-print"></i> Print Report
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Search functionality
    $('#search').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('#ordersTable tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });

    // Status filter
    $('#status_filter').on('change', function() {
        var status = $(this).val();
        if (status === '') {
            $('#ordersTable tbody tr').show();
        } else {
            $('#ordersTable tbody tr').hide();
            $('#ordersTable tbody tr').filter(function() {
                return $(this).find('td:nth-child(6)').text().indexOf(status) > -1;
            }).show();
        }
    });

    // Customer filter
    $('#customer_filter').on('change', function() {
        var customer = $(this).val();
        if (customer === '') {
            $('#ordersTable tbody tr').show();
        } else {
            $('#ordersTable tbody tr').hide();
            $('#ordersTable tbody tr').filter(function() {
                return $(this).find('td:nth-child(2)').text().indexOf(customer) > -1;
            }).show();
        }
    });
});

function exportToExcel() {
    alert('Excel export functionality will be implemented');
}

function exportToPDF() {
    alert('PDF export functionality will be implemented');
}

function printReport() {
    // Prevent double printing
    const printBtn = event.target;
    if (printBtn.disabled) return;
    
    printBtn.disabled = true;
    printBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Printing...';
    
    setTimeout(function() {
        window.print();
        setTimeout(function() {
            printBtn.disabled = false;
            printBtn.innerHTML = '<i class="fas fa-print"></i> Print';
        }, 1000);
    }, 100);
}
</script>
{% endblock %}
