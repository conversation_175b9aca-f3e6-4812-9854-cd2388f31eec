{% extends "base.html" %}

{% block title %}Batch Selection - {{ order.order_id }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-boxes"></i> Batch Selection for DC Generation
        </h1>
        <a href="{{ url_for('dc_pending') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to DC Pending
        </a>
    </div>

    <!-- Order Information -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 bg-primary text-white">
            <h6 class="m-0 font-weight-bold">Order Details</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <strong>Order ID:</strong><br>
                    <span class="text-primary">{{ order.order_id }}</span>
                </div>
                <div class="col-md-3">
                    <strong>Customer:</strong><br>
                    {{ order.customer_name }}
                </div>
                <div class="col-md-3">
                    <strong>Order Date:</strong><br>
                    {{ order.order_date }}
                </div>
                <div class="col-md-3">
                    <strong>Order Amount:</strong><br>
                    <span class="text-success">₹{{ "%.2f"|format(order.order_amount) }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Batch Selection Form -->
    <form method="POST" action="{{ url_for('dc_generation.allocate_batches', order_id=order.order_id) }}" id="batchSelectionForm">
        
        {% for product_id, product_data in inventory_data.items() %}
        <div class="card shadow mb-4">
            <div class="card-header py-3 bg-info text-white">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-pills"></i> 
                    {{ product_data.product_name }}
                    {% if product_data.strength %}({{ product_data.strength }}){% endif %}
                    - Required: {{ product_data.required_quantity }}
                </h6>
            </div>
            <div class="card-body">
                {% if product_data.warehouses %}
                    {% for warehouse_id, warehouse_data in product_data.warehouses.items() %}
                    <div class="mb-4">
                        <h6 class="text-info">
                            <i class="fas fa-warehouse"></i> {{ warehouse_data.warehouse_name }}
                        </h6>
                        
                        {% if warehouse_data.batches %}
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered">
                                <thead class="thead-light">
                                    <tr>
                                        <th>Batch Number</th>
                                        <th>Mfg Date</th>
                                        <th>Exp Date</th>
                                        <th>Available Qty</th>
                                        <th>Select Qty</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for batch in warehouse_data.batches %}
                                    <tr>
                                        <td>
                                            <small class="text-monospace">{{ batch.batch_number }}</small>
                                        </td>
                                        <td>
                                            {% if batch.manufacturing_date %}
                                                {{ batch.manufacturing_date }}
                                            {% else %}
                                                <span class="text-muted">N/A</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if batch.expiry_date %}
                                                {{ batch.expiry_date }}
                                            {% else %}
                                                <span class="text-muted">N/A</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge badge-success">{{ batch.available_quantity }}</span>
                                        </td>
                                        <td>
                                            <input type="number"
                                                   name="batch_{{ product_id }}_{{ batch.inventory_id }}"
                                                   class="form-control form-control-sm batch-quantity"
                                                   min="0"
                                                   max="{{ batch.available_quantity }}"
                                                   data-product-id="{{ product_id }}"
                                                   data-inventory-id="{{ batch.inventory_id }}"
                                                   data-required="{{ product_data.required_quantity }}"
                                                   style="width: 80px;">
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            No available batches in this warehouse
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                    
                    <!-- Product Summary -->
                    <div class="alert alert-light border">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Required Quantity:</strong> 
                                <span class="text-primary">{{ product_data.required_quantity }}</span>
                            </div>
                            <div class="col-md-6">
                                <strong>Selected Quantity:</strong> 
                                <span class="text-success" id="selected-{{ product_id }}">0</span>
                                <span class="text-muted" id="status-{{ product_id }}"></span>
                            </div>
                        </div>
                    </div>
                    
                {% else %}
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i>
                    <strong>No inventory available</strong> for this product in any warehouse.
                    Please check stock levels or contact inventory management.
                </div>
                {% endif %}
            </div>
        </div>
        {% endfor %}

        <!-- Action Buttons -->
        <div class="card shadow">
            <div class="card-body text-center">
                <button type="button" class="btn btn-warning mr-2" onclick="autoAllocateFIFO()">
                    <i class="fas fa-magic"></i> Auto Allocate (FIFO)
                </button>
                <button type="button" class="btn btn-secondary mr-2" onclick="clearAllSelections()">
                    <i class="fas fa-eraser"></i> Clear All
                </button>
                <button type="submit" class="btn btn-success mr-2" id="generateDCBtn" disabled>
                    <i class="fas fa-file-alt"></i> Generate Full DC
                </button>
                <button type="button" class="btn btn-warning" id="generatePartialDCBtn" onclick="generatePartialDC()" disabled>
                    <i class="fas fa-file-medical"></i> Generate Partial DC
                </button>
            </div>
        </div>
    </form>
</div>

<script>
// Real-time validation and feedback
document.addEventListener('DOMContentLoaded', function() {
    const quantityInputs = document.querySelectorAll('.batch-quantity');
    const generateBtn = document.getElementById('generateDCBtn');
    const generatePartialBtn = document.getElementById('generatePartialDCBtn');

    // Update totals when quantities change
    quantityInputs.forEach(input => {
        input.addEventListener('input', updateProductTotals);
    });

    function updateProductTotals() {
        let allValid = true;
        let hasAnySelection = false;

        // Get all unique product IDs
        const productIds = [...new Set(Array.from(quantityInputs).map(input =>
            input.getAttribute('data-product-id')
        ))];

        productIds.forEach(productId => {
            const productInputs = document.querySelectorAll(`[data-product-id="${productId}"]`);
            const required = parseInt(productInputs[0].getAttribute('data-required'));

            let selected = 0;
            productInputs.forEach(input => {
                const value = parseInt(input.value) || 0;
                selected += value;
            });

            if (selected > 0) {
                hasAnySelection = true;
            }

            // Update display
            const selectedSpan = document.getElementById(`selected-${productId}`);
            const statusSpan = document.getElementById(`status-${productId}`);

            selectedSpan.textContent = selected;

            if (selected === 0) {
                statusSpan.textContent = '(No selection)';
                statusSpan.className = 'text-muted';
                allValid = false;
            } else if (selected < required) {
                statusSpan.textContent = `(Need ${required - selected} more)`;
                statusSpan.className = 'text-warning';
                allValid = false;
            } else if (selected > required) {
                statusSpan.textContent = `(${selected - required} excess)`;
                statusSpan.className = 'text-danger';
                allValid = false;
            } else {
                statusSpan.textContent = '(Complete)';
                statusSpan.className = 'text-success';
            }
        });

        // Enable/disable generate buttons
        generateBtn.disabled = !allValid;
        generatePartialBtn.disabled = !hasAnySelection;
    }
    
    // Initial update
    updateProductTotals();
});

function autoAllocateFIFO() {
    // Clear existing selections
    clearAllSelections();
    
    // Auto-allocate using FIFO logic
    const productIds = [...new Set(Array.from(document.querySelectorAll('.batch-quantity')).map(input => 
        input.getAttribute('data-product-id')
    ))];
    
    productIds.forEach(productId => {
        const productInputs = document.querySelectorAll(`[data-product-id="${productId}"]`);
        const required = parseInt(productInputs[0].getAttribute('data-required'));
        
        let remaining = required;
        
        // Sort inputs by batch order (they should already be in FIFO order)
        Array.from(productInputs).forEach(input => {
            if (remaining <= 0) return;
            
            const maxAvailable = parseInt(input.getAttribute('max'));
            const toAllocate = Math.min(remaining, maxAvailable);
            
            input.value = toAllocate;
            remaining -= toAllocate;
        });
    });
    
    // Update totals
    document.querySelector('.batch-quantity').dispatchEvent(new Event('input'));
}

function clearAllSelections() {
    document.querySelectorAll('.batch-quantity').forEach(input => {
        input.value = '';
    });

    // Update totals
    document.querySelector('.batch-quantity').dispatchEvent(new Event('input'));
}

function generatePartialDC() {
    // Collect batch selections
    const selections = {};
    const quantityInputs = document.querySelectorAll('.batch-quantity');

    quantityInputs.forEach(input => {
        const quantity = parseFloat(input.value) || 0;
        if (quantity > 0) {
            const productId = input.getAttribute('data-product-id');
            const inventoryId = input.getAttribute('data-inventory-id');

            if (!selections[productId]) {
                selections[productId] = [];
            }

            selections[productId].push({
                inventory_id: inventoryId,
                quantity: quantity
            });
        }
    });

    if (Object.keys(selections).length === 0) {
        alert('Please select at least some quantities before generating partial DC.');
        return;
    }

    // Confirm partial DC generation
    if (confirm('Generate Partial DC? This will create a DC for available quantities and add remaining quantities to pending list.')) {
        const selectionsJson = JSON.stringify(selections);
        const orderId = '{{ order.order_id }}';
        window.location.href = `{{ url_for('dc_generation.generate_partial_dc', order_id=order.order_id) }}?selections=${encodeURIComponent(selectionsJson)}`;
    }
}
</script>
{% endblock %}
