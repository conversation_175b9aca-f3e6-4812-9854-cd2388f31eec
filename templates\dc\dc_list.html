{% extends "base.html" %}

{% block title %}Delivery Challans{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-truck"></i> Delivery Challans
        </h1>
        <div>
            <a href="{{ url_for('dc_pending') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Generate New DC
            </a>
            <a href="{{ url_for('warehouse_packing_dashboard') }}" class="btn btn-secondary">
                <i class="fas fa-warehouse"></i> Warehouse Dashboard
            </a>
        </div>
    </div>

    <!-- Delivery Challans Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 bg-primary text-white">
            <h6 class="m-0 font-weight-bold">
                <i class="fas fa-list"></i> All Delivery Challans
            </h6>
        </div>
        <div class="card-body">
            {% if challans %}
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="challansTable">
                    <thead class="thead-light">
                        <tr>
                            <th>DC Number</th>
                            <th>Order ID</th>
                            <th>Customer</th>
                            <th>Generated Date</th>
                            <th>Status</th>
                            <th>Amount</th>
                            <th>Items</th>
                            <th>Created By</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for challan in challans %}
                        <tr>
                            <td>
                                <strong class="text-primary">{{ challan.dc_number }}</strong>
                            </td>
                            <td>
                                <a href="{{ url_for('view_order', order_id=challan.order_id) }}" 
                                   class="text-decoration-none">
                                    {{ challan.order_id }}
                                </a>
                            </td>
                            <td>{{ challan.customer_name }}</td>
                            <td>{{ challan.created_date }}</td>
                            <td>
                                {% if challan.status == 'created' %}
                                    <span class="badge badge-warning">Created</span>
                                {% elif challan.status == 'dispatched' %}
                                    <span class="badge badge-info">Dispatched</span>
                                {% elif challan.status == 'delivered' %}
                                    <span class="badge badge-success">Delivered</span>
                                {% else %}
                                    <span class="badge badge-secondary">{{ challan.status|title }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="text-success">₹{{ "%.2f"|format(challan.total_amount) }}</span>
                            </td>
                            <td>
                                <span class="badge badge-info">{{ challan.total_items }}</span>
                            </td>
                            <td>{{ challan.created_by or 'System' }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('dc_generation.view_dc', dc_number=challan.dc_number) }}" 
                                       class="btn btn-sm btn-outline-primary" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('dc_generation.download_dc_pdf', dc_number=challan.dc_number) }}" 
                                       class="btn btn-sm btn-outline-success" title="Download PDF">
                                        <i class="fas fa-download"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No Delivery Challans Found</h5>
                <p class="text-muted">No delivery challans have been generated yet.</p>
                <a href="{{ url_for('dc_pending') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Generate First DC
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

{% if challans %}
<script>
$(document).ready(function() {
    $('#challansTable').DataTable({
        "order": [[ 3, "desc" ]], // Sort by date descending
        "pageLength": 25,
        "responsive": true,
        "columnDefs": [
            { "orderable": false, "targets": 8 } // Disable sorting on Actions column
        ]
    });
});
</script>
{% endif %}
{% endblock %}
