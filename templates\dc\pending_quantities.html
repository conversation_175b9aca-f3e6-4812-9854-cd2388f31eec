{% extends 'base.html' %}

{% block title %}DC Pending Quantities{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-warning text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-clock"></i> DC Pending Quantities
                    </h4>
                    <small>Orders with partial fulfillment - remaining quantities pending DC generation</small>
                </div>
                
                <div class="card-body">
                    {% if pending_data %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Order</th>
                                    <th>Customer</th>
                                    <th>Product</th>
                                    <th>Pending Qty</th>
                                    <th>Created</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in pending_data %}
                                <tr>
                                    <td>
                                        <strong>{{ item.order_number }}</strong>
                                        <br>
                                        <small class="text-muted">{{ item.order_date }}</small>
                                    </td>
                                    <td>{{ item.customer_name }}</td>
                                    <td>
                                        <strong>{{ item.product_name }}</strong>
                                        {% if item.strength %}
                                        <br><small class="text-muted">{{ item.strength }}</small>
                                        {% endif %}
                                        {% if item.manufacturer %}
                                        <br><small class="text-muted">{{ item.manufacturer }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge badge-warning badge-lg">
                                            {{ item.pending_quantity }} units
                                        </span>
                                    </td>
                                    <td>
                                        <small>{{ item.created_at }}</small>
                                    </td>
                                    <td>
                                        {% if item.status == 'pending' %}
                                        <span class="badge badge-warning">Pending</span>
                                        {% elif item.status == 'fulfilled' %}
                                        <span class="badge badge-success">Fulfilled</span>
                                        {% else %}
                                        <span class="badge badge-secondary">{{ item.status|title }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if item.status == 'pending' %}
                                        <a href="{{ url_for('dc_generation.fulfill_pending_quantity', pending_id=item.id) }}" 
                                           class="btn btn-sm btn-success">
                                            <i class="fas fa-play"></i> Fulfill
                                        </a>
                                        {% endif %}
                                        
                                        {% if item.notes %}
                                        <button class="btn btn-sm btn-info" 
                                                data-toggle="tooltip" 
                                                title="{{ item.notes }}">
                                            <i class="fas fa-info"></i>
                                        </button>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Summary Statistics -->
                    <div class="row mt-4">
                        <div class="col-md-4">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h5>{{ pending_data|length }}</h5>
                                    <small>Pending Items</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h5>{{ pending_data|map(attribute='order_id')|unique|list|length }}</h5>
                                    <small>Affected Orders</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-secondary text-white">
                                <div class="card-body text-center">
                                    <h5>{{ pending_data|sum(attribute='pending_quantity')|round(2) }}</h5>
                                    <small>Total Pending Units</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                        <h4 class="mt-3">No Pending Quantities</h4>
                        <p class="text-muted">All orders have been fully fulfilled!</p>
                        <a href="{{ url_for('dashboard') }}" class="btn btn-primary">
                            <i class="fas fa-home"></i> Back to Dashboard
                        </a>
                    </div>
                    {% endif %}
                </div>
                
                <div class="card-footer">
                    <div class="row">
                        <div class="col-md-6">
                            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Dashboard
                            </a>
                        </div>
                        <div class="col-md-6 text-right">
                            <button class="btn btn-info" onclick="window.location.reload()">
                                <i class="fas fa-sync"></i> Refresh
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Initialize tooltips
$(document).ready(function(){
    $('[data-toggle="tooltip"]').tooltip();
});
</script>
{% endblock %}
