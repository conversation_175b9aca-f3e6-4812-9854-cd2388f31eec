{% extends 'base.html' %}

{% block title %}Comprehensive Delivery Reports - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-file-alt text-primary"></i> Comprehensive Delivery Reports
        </h1>
        <a href="{{ url_for('delivery_analytics.dashboard') }}" class="btn btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Analytics
        </a>
    </div>

    <div class="row">
        {% for report in report_types %}
        <div class="col-md-4 mb-4">
            <div class="card shadow h-100">
                <div class="card-body text-center">
                    <i class="fas fa-{{ report.icon }} fa-3x text-primary mb-3"></i>
                    <h5 class="card-title">{{ report.name }}</h5>
                    <p class="card-text">{{ report.description }}</p>
                    <button class="btn btn-primary" onclick="generateReport('{{ report.id }}')">
                        <i class="fas fa-download"></i> Generate Report
                    </button>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<script>
function generateReport(reportType) {
    alert('Generating ' + reportType + ' report...');
    // Implementation for report generation
}
</script>
{% endblock %}