{% extends 'base.html' %}

{% block title %}Real-time Delivery Tracking - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-map-marker-alt text-primary"></i> Real-time Delivery Tracking
        </h1>
        <a href="{{ url_for('delivery_analytics.dashboard') }}" class="btn btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Analytics
        </a>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Active Deliveries</h6>
                </div>
                <div class="card-body">
                    {% if active_deliveries %}
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Order ID</th>
                                        <th>Customer</th>
                                        <th>Address</th>
                                        <th>Status</th>
                                        <th>Rider</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for delivery in active_deliveries %}
                                    <tr>
                                        <td>{{ delivery.order_id }}</td>
                                        <td>{{ delivery.customer_name }}</td>
                                        <td>{{ delivery.delivery_address }}</td>
                                        <td><span class="badge badge-info">{{ delivery.status }}</span></td>
                                        <td>{{ delivery.rider_name or 'Not Assigned' }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> No active deliveries at the moment.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">Delivery Map</h6>
                </div>
                <div class="card-body">
                    <div id="delivery-map" style="height: 300px; background: #f8f9fc; border: 1px solid #e3e6f0; border-radius: 5px;">
                        <div class="d-flex align-items-center justify-content-center h-100">
                            <div class="text-center">
                                <i class="fas fa-map fa-3x text-gray-300 mb-3"></i>
                                <p class="text-gray-500">Interactive delivery map will be displayed here</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}