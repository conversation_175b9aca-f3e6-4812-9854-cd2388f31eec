{% extends "base.html" %}

{% block title %}Delivery Challan - {{ challan.dc_number }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-file-alt"></i> Delivery Challan - {{ challan.dc_number }}
        </h1>
        <div>
            <button onclick="window.print()" class="btn btn-primary">
                <i class="fas fa-print"></i> Print
            </button>
            <a href="{{ url_for('warehouse_packing_dashboard') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Warehouse
            </a>
        </div>
    </div>

    <!-- Delivery Challan Details -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 bg-success text-white">
            <h6 class="m-0 font-weight-bold">Delivery Challan Information</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>DC Number:</strong></td>
                            <td>{{ challan.dc_number }}</td>
                        </tr>
                        <tr>
                            <td><strong>Order ID:</strong></td>
                            <td>{{ challan.order_id }}</td>
                        </tr>
                        <tr>
                            <td><strong>Status:</strong></td>
                            <td>
                                <span class="badge badge-success">{{ challan.status|title }}</span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Created By:</strong></td>
                            <td>{{ challan.created_by }}</td>
                        </tr>
                        <tr>
                            <td><strong>Created Date:</strong></td>
                            <td>{{ safe_strftime(challan.created_at, '%Y-%m-%d %H:%M') if challan.created_at else 'N/A' }}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>Customer Name:</strong></td>
                            <td>{{ challan.customer_name }}</td>
                        </tr>
                        <tr>
                            <td><strong>Customer Address:</strong></td>
                            <td>{{ challan.customer_address or 'N/A' }}</td>
                        </tr>
                        <tr>
                            <td><strong>Customer Phone:</strong></td>
                            <td>{{ challan.customer_phone or 'N/A' }}</td>
                        </tr>
                        <tr>
                            <td><strong>Total Items:</strong></td>
                            <td>{{ challan.total_items }}</td>
                        </tr>
                        <tr>
                            <td><strong>Total Amount:</strong></td>
                            <td><strong class="text-success">₹{{ "%.2f"|format(challan.total_amount) }}</strong></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Batch Details -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 bg-info text-white">
            <h6 class="m-0 font-weight-bold">Batch Allocation Details</h6>
        </div>
        <div class="card-body">
            {% if batch_details %}
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="thead-light">
                        <tr>
                            <th>Product</th>
                            <th>Batch Number</th>
                            <th>Warehouse</th>
                            <th>Allocated Quantity</th>
                            <th>Unit</th>
                            <th>Selection Method</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for batch in batch_details %}
                        <tr>
                            <td>
                                <strong>{{ batch.product_name }}</strong>
                                {% if batch.strength %}
                                <br><small class="text-muted">{{ batch.strength }}</small>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge badge-secondary">{{ batch.batch_number }}</span>
                            </td>
                            <td>{{ batch.warehouse_name }}</td>
                            <td>
                                <strong class="text-primary">{{ batch.allocated_quantity }}</strong>
                            </td>
                            <td>{{ batch.unit_of_measure or 'Units' }}</td>
                            <td>
                                <span class="badge badge-outline-info">{{ batch.selection_method|upper }}</span>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                No batch details available for this delivery challan.
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Summary -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 bg-warning text-dark">
            <h6 class="m-0 font-weight-bold">Summary</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <div class="text-center">
                        <h4 class="text-primary">{{ batch_details|length }}</h4>
                        <p class="text-muted">Total Batches</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <h4 class="text-info">{{ challan.total_items }}</h4>
                        <p class="text-muted">Total Items</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <h4 class="text-success">₹{{ "%.2f"|format(challan.total_amount) }}</h4>
                        <p class="text-muted">Total Amount</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions -->
    <div class="card shadow mb-4">
        <div class="card-body text-center">
            <h6 class="text-muted mb-3">Next Steps</h6>
            <a href="{{ url_for('finance_payment_collection') }}" class="btn btn-success mr-2">
                <i class="fas fa-money-bill-wave"></i> View in Finance Module
            </a>
            <a href="{{ url_for('warehouse_packing_dashboard') }}" class="btn btn-primary mr-2">
                <i class="fas fa-warehouse"></i> Back to Warehouse
            </a>
            <button onclick="window.print()" class="btn btn-info">
                <i class="fas fa-print"></i> Print Challan
            </button>
        </div>
    </div>
</div>

<style>
@media print {
    .btn, .card-header, .no-print {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .container-fluid {
        padding: 0 !important;
    }
}
</style>
{% endblock %}
