{% extends "base.html" %}

{% block title %}Delivery Challans{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-truck"></i> Delivery Challans
        </h1>
        <div>
            <a href="{{ url_for('dc_pending') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Generate New DC
            </a>
            <a href="{{ url_for('warehouse_packing_dashboard') }}" class="btn btn-secondary">
                <i class="fas fa-warehouse"></i> Packing Dashboard
            </a>
        </div>
    </div>

    <!-- Delivery Challans Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 bg-primary text-white">
            <h6 class="m-0 font-weight-bold">
                <i class="fas fa-list"></i> All Delivery Challans
            </h6>
        </div>
        <div class="card-body">
            {% if challans %}
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="challansTable">
                    <thead class="thead-light">
                        <tr>
                            <th>DC Number</th>
                            <th>Order ID</th>
                            <th>Customer</th>
                            <th>Generated Date</th>
                            <th>Status</th>
                            <th>Amount</th>
                            <th>Batches</th>
                            <th>Created By</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for challan in challans %}
                        <tr>
                            <td>
                                <strong class="text-primary">{{ challan.dc_number }}</strong>
                            </td>
                            <td>
                                <a href="{{ url_for('view_order', order_id=challan.order_id) }}" 
                                   class="text-decoration-none">
                                    {{ challan.order_id }}
                                </a>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ challan.customer_name }}</strong>
                                    <br><small class="text-muted">Order Date: {{ safe_strftime(challan.order_date, '%Y-%m-%d') if challan.order_date else 'N/A' }}</small>
                                </div>
                            </td>
                            <td>
                                {{ safe_strftime(challan.date_generated, '%Y-%m-%d %H:%M') if challan.date_generated else 'N/A' }}
                            </td>
                            <td>
                                {% if challan.status == 'created' %}
                                    <span class="badge badge-primary">Created</span>
                                {% elif challan.status == 'dispatched' %}
                                    <span class="badge badge-warning">Dispatched</span>
                                {% elif challan.status == 'delivered' %}
                                    <span class="badge badge-success">Delivered</span>
                                {% else %}
                                    <span class="badge badge-secondary">{{ challan.status|title }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <strong class="text-success">₹{{ "%.2f"|format(challan.order_amount) }}</strong>
                            </td>
                            <td>
                                {% if challan.batch_count %}
                                    <span class="badge badge-info">{{ challan.batch_count }} batches</span>
                                {% else %}
                                    <span class="badge badge-secondary">No batches</span>
                                {% endif %}
                            </td>
                            <td>
                                <small class="text-muted">{{ challan.created_by or 'System' }}</small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('view_delivery_challan_old', dc_number=challan.dc_number) }}"
                                       class="btn btn-info btn-sm"
                                       title="View DC Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if challan.pdf_path %}
                                    <a href="{{ challan.pdf_path }}" 
                                       class="btn btn-success btn-sm" 
                                       title="Download PDF" 
                                       target="_blank">
                                        <i class="fas fa-file-pdf"></i>
                                    </a>
                                    {% endif %}
                                    <!-- Status update buttons removed for workflow compliance -->
                                    <!-- After DC generation, orders go to finance for invoice generation -->
                                    <!-- Status updates should only be allowed after proper invoice generation -->
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No Delivery Challans Found</h5>
                <p class="text-muted">No delivery challans have been generated yet.</p>
                <a href="{{ url_for('dc_pending') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Generate First DC
                </a>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total DCs
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ challans|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-truck fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Dispatched
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ challans|selectattr('status', 'equalto', 'dispatched')|list|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shipping-fast fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Delivered
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ challans|selectattr('status', 'equalto', 'delivered')|list|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total Value
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ₹{{ "%.2f"|format(challans|sum(attribute='order_amount')) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-rupee-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#challansTable').DataTable({
        "order": [[ 3, "desc" ]], // Sort by generated date descending
        "pageLength": 25,
        "responsive": true,
        "columnDefs": [
            { "orderable": false, "targets": 8 } // Disable sorting on Actions column
        ]
    });
});

// Status update function removed for workflow compliance
// After DC generation, orders go to finance for invoice generation
// Status updates should only be allowed after proper invoice generation
</script>
{% endblock %}
