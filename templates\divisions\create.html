{% extends 'base.html' %}

{% block title %}Create Division - Modern ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-plus text-success"></i> Create New Division
        </h1>
        <a href="{{ url_for('divisions.index') }}" class="btn btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Divisions
        </a>
    </div>

    <!-- Create Division Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Division Information</h6>
                </div>
                <div class="card-body">
                    <form id="createDivisionForm" method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="code">Division Code *</label>
                                    <input type="text" class="form-control" id="code" name="code" required 
                                           placeholder="e.g., SALES, MKT, OPS">
                                    <small class="form-text text-muted">2-10 characters, letters and numbers only</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name">Division Name *</label>
                                    <input type="text" class="form-control" id="name" name="name" required 
                                           placeholder="e.g., Sales Division">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3" 
                                      placeholder="Brief description of the division's purpose and responsibilities"></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="status">Status</label>
                                    <select class="form-control" id="status" name="status">
                                        {% for status in statuses %}
                                        <option value="{{ status }}" {% if status == 'active' %}selected{% endif %}>
                                            {{ status.title() }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="category">Category</label>
                                    <select class="form-control" id="category" name="category">
                                        <option value="">Select Category</option>
                                        {% for category in categories %}
                                        <option value="{{ category }}">{{ category }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="budget">Budget (Rs.)</label>
                                    <input type="number" class="form-control" id="budget" name="budget" 
                                           step="0.01" min="0" placeholder="0.00">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="contact_email">Contact Email</label>
                                    <input type="email" class="form-control" id="contact_email" name="contact_email" 
                                           placeholder="<EMAIL>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="contact_phone">Contact Phone</label>
                                    <input type="text" class="form-control" id="contact_phone" name="contact_phone" 
                                           placeholder="+92-300-1234567">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="location">Location</label>
                                    <input type="text" class="form-control" id="location" name="location" 
                                           placeholder="e.g., Karachi, Lahore">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="city">City</label>
                                    <input type="text" class="form-control" id="city" name="city" 
                                           placeholder="e.g., Karachi">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="address">Address</label>
                            <textarea class="form-control" id="address" name="address" rows="2" 
                                      placeholder="Complete address of the division"></textarea>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" checked>
                                <label class="custom-control-label" for="is_active">
                                    Active Division (can be used in operations)
                                </label>
                            </div>
                        </div>

                        <hr>

                        <div class="form-group mb-0">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i> Create Division
                            </button>
                            <a href="{{ url_for('divisions.index') }}" class="btn btn-secondary ml-2">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Help Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info-circle"></i> Help & Guidelines
                    </h6>
                </div>
                <div class="card-body">
                    <h6 class="font-weight-bold">Division Code Guidelines:</h6>
                    <ul class="small">
                        <li>Use 2-10 characters</li>
                        <li>Letters, numbers, hyphens, underscores only</li>
                        <li>Examples: SALES, MKT, HR, OPS-001</li>
                    </ul>

                    <h6 class="font-weight-bold mt-3">Categories:</h6>
                    <ul class="small">
                        <li><strong>Revenue:</strong> Sales, Marketing</li>
                        <li><strong>Operations:</strong> Production, Logistics</li>
                        <li><strong>Support:</strong> HR, IT, Admin</li>
                        <li><strong>Finance:</strong> Accounting, Treasury</li>
                    </ul>

                    <h6 class="font-weight-bold mt-3">Status Options:</h6>
                    <ul class="small">
                        <li><strong>Active:</strong> Fully operational</li>
                        <li><strong>Inactive:</strong> Temporarily disabled</li>
                        <li><strong>Suspended:</strong> Under review</li>
                        <li><strong>Archived:</strong> Historical record</li>
                    </ul>
                </div>
            </div>

            <!-- Preview Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-eye"></i> Preview
                    </h6>
                </div>
                <div class="card-body">
                    <div id="divisionPreview">
                        <p class="text-muted">Fill out the form to see a preview of your division.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('createDivisionForm');
    const preview = document.getElementById('divisionPreview');
    
    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
        
        // Show loading state
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating...';
        submitBtn.disabled = true;
        
        fetch('{{ url_for("divisions.create") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'success');
                setTimeout(() => {
                    window.location.href = '{{ url_for("divisions.index") }}';
                }, 1500);
            } else {
                showNotification(data.errors ? data.errors.join(', ') : data.error, 'danger');
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }
        })
        .catch(error => {
            showNotification('Error creating division: ' + error.message, 'danger');
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    });
    
    // Live preview
    const previewFields = ['code', 'name', 'description', 'status', 'category'];
    previewFields.forEach(field => {
        const input = document.getElementById(field);
        if (input) {
            input.addEventListener('input', updatePreview);
            input.addEventListener('change', updatePreview);
        }
    });
    
    function updatePreview() {
        const code = document.getElementById('code').value;
        const name = document.getElementById('name').value;
        const description = document.getElementById('description').value;
        const status = document.getElementById('status').value;
        const category = document.getElementById('category').value;
        
        if (code || name) {
            preview.innerHTML = `
                <div class="border-left-primary pl-3">
                    <h6 class="font-weight-bold text-primary">${name || 'Division Name'}</h6>
                    <p class="small mb-1"><strong>Code:</strong> ${code || 'N/A'}</p>
                    <p class="small mb-1"><strong>Category:</strong> ${category || 'Not selected'}</p>
                    <p class="small mb-1"><strong>Status:</strong> 
                        <span class="badge badge-${getStatusBadgeClass(status)}">${status ? status.charAt(0).toUpperCase() + status.slice(1) : 'Active'}</span>
                    </p>
                    ${description ? `<p class="small text-muted">${description}</p>` : ''}
                </div>
            `;
        } else {
            preview.innerHTML = '<p class="text-muted">Fill out the form to see a preview of your division.</p>';
        }
    }
    
    function getStatusBadgeClass(status) {
        switch(status) {
            case 'active': return 'success';
            case 'inactive': return 'secondary';
            case 'suspended': return 'warning';
            default: return 'success';
        }
    }
    
    // Code formatting
    document.getElementById('code').addEventListener('input', function() {
        this.value = this.value.toUpperCase().replace(/[^A-Z0-9_-]/g, '');
    });
});

function showNotification(message, type) {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 5000);
}
</script>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.card {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}

.form-control:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.btn-success {
    background-color: #1cc88a;
    border-color: #1cc88a;
}

.btn-success:hover {
    background-color: #17a673;
    border-color: #169b6b;
}

.custom-control-input:checked ~ .custom-control-label::before {
    background-color: #4e73df;
    border-color: #4e73df;
}

.small {
    font-size: 0.875rem;
}

.badge {
    font-size: 0.75rem;
}
</style>
{% endblock %}
