{% extends 'base.html' %}

{% block title %}Edit {{ division.name }} - Division Management{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-edit text-warning"></i> Edit Division: {{ division.name }}
        </h1>
        <div class="btn-group" role="group">
            <a href="{{ url_for('divisions.view', division_id=division.division_id) }}" class="btn btn-info shadow-sm">
                <i class="fas fa-eye fa-sm text-white-50"></i> View Details
            </a>
            <a href="{{ url_for('divisions.index') }}" class="btn btn-secondary shadow-sm">
                <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to List
            </a>
        </div>
    </div>

    <!-- Edit Division Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Division Information</h6>
                </div>
                <div class="card-body">
                    <form id="editDivisionForm" method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="code">Division Code *</label>
                                    <input type="text" class="form-control" id="code" name="code" 
                                           value="{{ division.code }}" required>
                                    <small class="form-text text-muted">2-10 characters, letters and numbers only</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name">Division Name *</label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="{{ division.name }}" required>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3">{{ division.description or '' }}</textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="status">Status</label>
                                    <select class="form-control" id="status" name="status">
                                        {% for status in statuses %}
                                        <option value="{{ status }}" {% if status == division.status %}selected{% endif %}>
                                            {{ status.title() }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="category">Category</label>
                                    <select class="form-control" id="category" name="category">
                                        <option value="">Select Category</option>
                                        {% for category in categories %}
                                        <option value="{{ category }}" {% if category == division.category %}selected{% endif %}>
                                            {{ category }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="budget">Budget (Rs.)</label>
                                    <input type="number" class="form-control" id="budget" name="budget" 
                                           step="0.01" min="0" value="{{ division.budget or 0 }}">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="contact_email">Contact Email</label>
                                    <input type="email" class="form-control" id="contact_email" name="contact_email" 
                                           value="{{ division.contact_email or '' }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="contact_phone">Contact Phone</label>
                                    <input type="text" class="form-control" id="contact_phone" name="contact_phone" 
                                           value="{{ division.contact_phone or '' }}">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="location">Location</label>
                                    <input type="text" class="form-control" id="location" name="location" 
                                           value="{{ division.location or '' }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="city">City</label>
                                    <input type="text" class="form-control" id="city" name="city" 
                                           value="{{ division.city or '' }}">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="address">Address</label>
                            <textarea class="form-control" id="address" name="address" rows="2">{{ division.address or '' }}</textarea>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" 
                                       {% if division.is_active %}checked{% endif %}>
                                <label class="custom-control-label" for="is_active">
                                    Active Division (can be used in operations)
                                </label>
                            </div>
                        </div>

                        <hr>

                        <div class="form-group mb-0">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save"></i> Update Division
                            </button>
                            <a href="{{ url_for('divisions.view', division_id=division.division_id) }}" class="btn btn-secondary ml-2">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="button" class="btn btn-danger ml-2" onclick="confirmDelete()">
                                <i class="fas fa-trash"></i> Delete Division
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Current Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info-circle"></i> Current Information
                    </h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless table-sm">
                        <tr>
                            <td class="font-weight-bold">Division ID:</td>
                            <td>{{ division.division_id }}</td>
                        </tr>
                        <tr>
                            <td class="font-weight-bold">Created:</td>
                            <td>{{ division.created_at.split(' ')[0] if division.created_at else 'N/A' }}</td>
                        </tr>
                        <tr>
                            <td class="font-weight-bold">Created By:</td>
                            <td>{{ division.created_by or 'System' }}</td>
                        </tr>
                        <tr>
                            <td class="font-weight-bold">Last Updated:</td>
                            <td>{{ division.updated_at.split(' ')[0] if division.updated_at else 'Never' }}</td>
                        </tr>
                        <tr>
                            <td class="font-weight-bold">Updated By:</td>
                            <td>{{ division.updated_by or 'N/A' }}</td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Change History -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-history"></i> Change History
                    </h6>
                </div>
                <div class="card-body">
                    <div id="changeHistory">
                        <p class="text-muted">Changes will be tracked here after updates.</p>
                    </div>
                </div>
            </div>

            <!-- Validation Rules -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-check-circle"></i> Validation Rules
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="small">
                        <li>Division code must be unique</li>
                        <li>Name is required and must be 2-100 characters</li>
                        <li>Budget must be a positive number</li>
                        <li>Email must be valid format if provided</li>
                        <li>Status must be one of: Active, Inactive, Suspended, Archived</li>
                    </ul>
                </div>
            </div>

            <!-- Danger Zone -->
            <div class="card shadow mb-4 border-danger">
                <div class="card-header py-3 bg-danger">
                    <h6 class="m-0 font-weight-bold text-white">
                        <i class="fas fa-exclamation-triangle"></i> Danger Zone
                    </h6>
                </div>
                <div class="card-body">
                    <p class="text-muted small">
                        Deleting this division will permanently remove all associated data. 
                        This action cannot be undone.
                    </p>
                    <button type="button" class="btn btn-danger btn-sm" onclick="confirmDelete()">
                        <i class="fas fa-trash"></i> Delete Division
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('editDivisionForm');
    
    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
        
        // Show loading state
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Updating...';
        submitBtn.disabled = true;
        
        fetch('{{ url_for("divisions.edit", division_id=division.division_id) }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'success');
                setTimeout(() => {
                    window.location.href = '{{ url_for("divisions.view", division_id=division.division_id) }}';
                }, 1500);
            } else {
                showNotification(data.errors ? data.errors.join(', ') : data.error, 'danger');
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }
        })
        .catch(error => {
            showNotification('Error updating division: ' + error.message, 'danger');
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    });
    
    // Code formatting
    document.getElementById('code').addEventListener('input', function() {
        this.value = this.value.toUpperCase().replace(/[^A-Z0-9_-]/g, '');
    });
    
    // Track changes
    const originalValues = {};
    const formElements = form.querySelectorAll('input, select, textarea');
    formElements.forEach(element => {
        originalValues[element.name] = element.value;
        element.addEventListener('change', trackChanges);
    });
    
    function trackChanges() {
        const changes = [];
        formElements.forEach(element => {
            if (originalValues[element.name] !== element.value) {
                changes.push({
                    field: element.name,
                    old: originalValues[element.name],
                    new: element.value
                });
            }
        });
        
        const historyDiv = document.getElementById('changeHistory');
        if (changes.length > 0) {
            historyDiv.innerHTML = '<h6 class="small font-weight-bold">Pending Changes:</h6>';
            changes.forEach(change => {
                historyDiv.innerHTML += `
                    <div class="small mb-1">
                        <strong>${change.field.replace('_', ' ').toUpperCase()}:</strong><br>
                        <span class="text-muted">${change.old || 'Empty'}</span> → 
                        <span class="text-primary">${change.new || 'Empty'}</span>
                    </div>
                `;
            });
        } else {
            historyDiv.innerHTML = '<p class="text-muted">No changes made yet.</p>';
        }
    }
});

function confirmDelete() {
    if (confirm(`Are you sure you want to delete division "{{ division.name }}"?\n\nThis action cannot be undone and will permanently remove all associated data.`)) {
        deleteDivision();
    }
}

function deleteDivision() {
    fetch('{{ url_for("divisions.delete", division_id=division.division_id) }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            setTimeout(() => {
                window.location.href = '{{ url_for("divisions.index") }}';
            }, 1500);
        } else {
            showNotification(data.error, 'danger');
        }
    })
    .catch(error => {
        showNotification('Error deleting division: ' + error.message, 'danger');
    });
}

function showNotification(message, type) {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 5000);
}
</script>

<style>
.card {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}

.form-control:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.btn-warning {
    background-color: #f6c23e;
    border-color: #f6c23e;
}

.btn-warning:hover {
    background-color: #f4b619;
    border-color: #f4b619;
}

.table-borderless td {
    border: none;
    padding: 0.25rem 0;
}

.border-danger {
    border-color: #e74a3b !important;
}

.bg-danger {
    background-color: #e74a3b !important;
}

.custom-control-input:checked ~ .custom-control-label::before {
    background-color: #4e73df;
    border-color: #4e73df;
}

.small {
    font-size: 0.875rem;
}
</style>
{% endblock %}
