{% extends "base.html" %}

{% block title %}Division Management - Dashboard{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="text-primary mb-1">
                        <i class="fas fa-building"></i> Division Management
                    </h2>
                    <p class="text-muted mb-0">Comprehensive division management with analytics and performance tracking</p>
                </div>
                <div>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDivisionModal">
                        <i class="fas fa-plus"></i> Add Division
                    </button>
                    <a href="{{ url_for('divisions.analytics') }}" class="btn btn-outline-info">
                        <i class="fas fa-chart-line"></i> Analytics
                    </a>
                    <a href="{{ url_for('divisions.export') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-download"></i> Export
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Divisions</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_divisions }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-building fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Active Divisions</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ active_divisions }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Overall Progress</div>
                            <div class="row no-gutters align-items-center">
                                <div class="col-auto">
                                    <div class="h5 mb-0 mr-3 font-weight-bold text-gray-800">{{ overall_progress }}%</div>
                                </div>
                                <div class="col">
                                    <div class="progress progress-sm mr-2">
                                        <div class="progress-bar bg-info" role="progressbar" style="width: {{ overall_progress }}%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-pie fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Total Employees</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_employees }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Divisions Table -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-table"></i> All Divisions
                    </h6>
                </div>
                <div class="card-body">
                    {% if divisions and divisions|length > 0 %}
                    <div class="table-responsive">
                        <table class="table table-bordered" id="divisionsTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Code</th>
                                    <th>Division Name</th>
                                    <th>Manager</th>
                                    <th>Target</th>
                                    <th>Achieved</th>
                                    <th>Progress</th>
                                    <th>Status</th>
                                    <th>Employees</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for division in divisions %}
                                <tr>
                                    <td><strong class="text-primary">{{ division.code }}</strong></td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 35px; height: 35px;">
                                                {{ division.name[0] if division.name else 'D' }}
                                            </div>
                                            <div>
                                                <strong>{{ division.name }}</strong>
                                                {% if division.description %}
                                                <br><small class="text-muted">{{ division.description[:50] }}{% if division.description|length > 50 %}...{% endif %}</small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        {% if division.manager_name %}
                                            <strong>{{ division.manager_name }}</strong>
                                            {% if division.manager_email %}
                                            <br><small class="text-muted">{{ division.manager_email }}</small>
                                            {% endif %}
                                        {% else %}
                                            <span class="text-muted">Not assigned</span>
                                        {% endif %}
                                    </td>
                                    <td>Rs.{{ "{:,.0f}".format(division.target_revenue) if division.target_revenue else "0" }}</td>
                                    <td>Rs.{{ "{:,.0f}".format(division.achieved_revenue) if division.achieved_revenue else "0" }}</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar 
                                                {% if division.progress_percentage >= 90 %}bg-success
                                                {% elif division.progress_percentage >= 70 %}bg-warning
                                                {% else %}bg-danger{% endif %}" 
                                                role="progressbar" 
                                                style="width: {{ division.progress_percentage }}%">
                                                {{ division.progress_percentage }}%
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if division.status == 'active' else 'secondary' }}">
                                            {{ division.status.title() }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ division.employee_count or 0 }}</span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button class="btn btn-outline-primary" onclick="viewDivision('{{ division.division_id }}')" title="View">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-success" onclick="editDivision('{{ division.division_id }}')" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="deleteDivision('{{ division.division_id }}', '{{ division.name }}')" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <!-- Empty State -->
                    <div class="text-center py-5">
                        <i class="fas fa-building fa-4x text-muted mb-4"></i>
                        <h4 class="text-muted mb-3">No Divisions Available</h4>
                        <p class="text-muted mb-4">No divisions have been created yet. Create your first division to get started with organizational management.</p>
                        <button class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#addDivisionModal">
                            <i class="fas fa-plus"></i> Create First Division
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Division Modal -->
<div class="modal fade" id="addDivisionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus"></i> Add New Division
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addDivisionForm" onsubmit="addDivision(event)">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="divisionName" class="form-label">Division Name *</label>
                                <input type="text" class="form-control" id="divisionName" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="divisionCode" class="form-label">Division Code *</label>
                                <input type="text" class="form-control" id="divisionCode" name="code" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="divisionDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="divisionDescription" name="description" rows="3"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="targetRevenue" class="form-label">Target Revenue</label>
                                <input type="number" class="form-control" id="targetRevenue" name="target_revenue" step="0.01">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="achievedRevenue" class="form-label">Achieved Revenue</label>
                                <input type="number" class="form-control" id="achievedRevenue" name="achieved_revenue" step="0.01">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="managerName" class="form-label">Manager Name</label>
                                <input type="text" class="form-control" id="managerName" name="manager_name">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="managerEmail" class="form-label">Manager Email</label>
                                <input type="email" class="form-control" id="managerEmail" name="manager_email">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="employeeCount" class="form-label">Employee Count</label>
                                <input type="number" class="form-control" id="employeeCount" name="employee_count" min="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="divisionStatus" class="form-label">Status</label>
                                <select class="form-select" id="divisionStatus" name="status">
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Create Division
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Division management functions
function addDivision(event) {
    event.preventDefault();
    const formData = new FormData(event.target);
    
    fetch('/divisions/create', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Division created successfully!', 'success');
            $('#addDivisionModal').modal('hide');
            location.reload();
        } else {
            showNotification(data.message || 'Error creating division', 'error');
        }
    })
    .catch(error => {
        showNotification('Error creating division', 'error');
        console.error('Error:', error);
    });
}

function viewDivision(divisionId) {
    window.location.href = `/divisions/${divisionId}`;
}

function editDivision(divisionId) {
    // Implement edit functionality
    alert('Edit functionality will be implemented');
}

function deleteDivision(divisionId, divisionName) {
    if (confirm(`Are you sure you want to delete division "${divisionName}"? This action cannot be undone.`)) {
        fetch(`/divisions/${divisionId}/delete`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Division deleted successfully!', 'success');
                location.reload();
            } else {
                showNotification(data.message || 'Error deleting division', 'error');
            }
        })
        .catch(error => {
            showNotification('Error deleting division', 'error');
            console.error('Error:', error);
        });
    }
}

// Notification function
function showNotification(message, type) {
    if (type === 'success') {
        alert('✅ ' + message);
    } else {
        alert('❌ ' + message);
    }
}

// Initialize DataTable when page loads
$(document).ready(function() {
    if ($('#divisionsTable').length) {
        $('#divisionsTable').DataTable({
            responsive: true,
            pageLength: 25,
            order: [[0, "asc"]], // Sort by code
            columnDefs: [
                { orderable: false, targets: [8] } // Disable sorting for actions column
            ]
        });
    }
});
</script>
{% endblock %}
