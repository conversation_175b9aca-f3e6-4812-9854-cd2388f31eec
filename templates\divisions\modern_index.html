{% extends 'base.html' %}

{% block title %}Division Management - Modern ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-sitemap text-primary"></i> Division Management
        </h1>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-success shadow-sm" onclick="showCreateModal()">
                <i class="fas fa-plus fa-sm text-white-50"></i> New Division
            </button>
            <button type="button" class="btn btn-info shadow-sm" onclick="exportData()">
                <i class="fas fa-download fa-sm text-white-50"></i> Export
            </button>
            <a href="{{ url_for('divisions.analytics') }}" class="btn btn-warning shadow-sm">
                <i class="fas fa-chart-line fa-sm text-white-50"></i> Analytics
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Divisions
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ stats.total_divisions or 0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-sitemap fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Active Divisions
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ stats.active_divisions or 0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total Budget
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                Rs. {{ "{:,.2f}".format(stats.total_budget or 0) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Total Revenue
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                Rs. {{ "{:,.2f}".format(stats.total_revenue or 0) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter Section -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Search & Filter</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="searchInput">Search Divisions</label>
                        <input type="text" class="form-control" id="searchInput" placeholder="Search by name, code, or description...">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="statusFilter">Status</label>
                        <select class="form-control" id="statusFilter">
                            <option value="">All Statuses</option>
                            {% for status in statuses %}
                            <option value="{{ status }}">{{ status.title() }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="categoryFilter">Category</label>
                        <select class="form-control" id="categoryFilter">
                            <option value="">All Categories</option>
                            {% for category in categories %}
                            <option value="{{ category }}">{{ category }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <div>
                            <button type="button" class="btn btn-primary btn-block" onclick="applyFilters()">
                                <i class="fas fa-search"></i> Search
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Divisions Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">Divisions List</h6>
            <div class="dropdown no-arrow">
                <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown">
                    <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                </a>
                <div class="dropdown-menu dropdown-menu-right shadow">
                    <div class="dropdown-header">Actions:</div>
                    <a class="dropdown-item" href="#" onclick="bulkDelete()">
                        <i class="fas fa-trash fa-sm fa-fw mr-2 text-gray-400"></i>
                        Bulk Delete
                    </a>
                    <a class="dropdown-item" href="#" onclick="bulkExport()">
                        <i class="fas fa-download fa-sm fa-fw mr-2 text-gray-400"></i>
                        Bulk Export
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="divisionsTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th><input type="checkbox" id="selectAll"></th>
                            <th>Code</th>
                            <th>Name</th>
                            <th>Category</th>
                            <th>Status</th>
                            <th>Budget</th>
                            <th>Revenue</th>
                            <th>Manager</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="divisionsTableBody">
                        {% for division in divisions %}
                        <tr>
                            <td><input type="checkbox" class="division-checkbox" value="{{ division.division_id }}"></td>
                            <td><strong>{{ division.code }}</strong></td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="mr-3">
                                        <div class="icon-circle bg-primary">
                                            <i class="fas fa-sitemap text-white"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <div class="font-weight-bold">{{ division.name }}</div>
                                        <div class="text-muted small">{{ division.description[:50] }}{% if division.description|length > 50 %}...{% endif %}</div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge badge-info">{{ division.category or 'N/A' }}</span>
                            </td>
                            <td>
                                {% if division.status == 'active' %}
                                <span class="badge badge-success">Active</span>
                                {% elif division.status == 'inactive' %}
                                <span class="badge badge-secondary">Inactive</span>
                                {% elif division.status == 'suspended' %}
                                <span class="badge badge-warning">Suspended</span>
                                {% else %}
                                <span class="badge badge-danger">{{ division.status.title() }}</span>
                                {% endif %}
                            </td>
                            <td>Rs. {{ "{:,.2f}".format(division.budget or 0) }}</td>
                            <td>Rs. {{ "{:,.2f}".format(division.revenue or 0) }}</td>
                            <td>{{ division.manager or 'Not Assigned' }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('divisions.view', division_id=division.division_id) }}" 
                                       class="btn btn-sm btn-info" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('divisions.edit', division_id=division.division_id) }}" 
                                       class="btn btn-sm btn-warning" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-danger" 
                                            onclick="confirmDelete('{{ division.division_id }}', '{{ division.name }}')" 
                                            title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="row mt-3">
                <div class="col-sm-12 col-md-5">
                    <div class="dataTables_info">
                        Showing <span id="showingStart">1</span> to <span id="showingEnd">{{ divisions|length }}</span> 
                        of <span id="totalRecords">{{ divisions|length }}</span> entries
                    </div>
                </div>
                <div class="col-sm-12 col-md-7">
                    <div class="dataTables_paginate paging_simple_numbers">
                        <ul class="pagination" id="pagination">
                            <!-- Pagination will be populated by JavaScript -->
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activities -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Recent Activities</h6>
        </div>
        <div class="card-body">
            {% if recent_activities %}
            <div class="list-group">
                {% for activity in recent_activities %}
                <div class="list-group-item">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">
                            <i class="fas fa-{{ 'plus' if activity.action_type == 'CREATE' else 'edit' if activity.action_type == 'UPDATE' else 'trash' }}"></i>
                            {{ activity.action_type.title() }} - {{ activity.division_name }}
                        </h6>
                        <small>{{ activity.changed_at }}</small>
                    </div>
                    <p class="mb-1">Changed by: {{ activity.changed_by }}</p>
                    <small>{{ activity.changed_at }}</small>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <p class="text-muted">No recent activities</p>
            {% endif %}
        </div>
    </div>
</div>

<!-- Create Division Modal -->
<div class="modal fade" id="createDivisionModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Division</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="createDivisionForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="code">Division Code *</label>
                                <input type="text" class="form-control" id="code" name="code" required>
                                <small class="form-text text-muted">2-10 characters, letters and numbers only</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="name">Division Name *</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="status">Status</label>
                                <select class="form-control" id="status" name="status">
                                    {% for status in statuses %}
                                    <option value="{{ status }}" {% if status == 'active' %}selected{% endif %}>{{ status.title() }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="category">Category</label>
                                <select class="form-control" id="category" name="category">
                                    <option value="">Select Category</option>
                                    {% for category in categories %}
                                    <option value="{{ category }}">{{ category }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="budget">Budget</label>
                                <input type="number" class="form-control" id="budget" name="budget" step="0.01" min="0">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="contact_email">Contact Email</label>
                                <input type="email" class="form-control" id="contact_email" name="contact_email">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="contact_phone">Contact Phone</label>
                                <input type="text" class="form-control" id="contact_phone" name="contact_phone">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="location">Location</label>
                                <input type="text" class="form-control" id="location" name="location">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="city">City</label>
                                <input type="text" class="form-control" id="city" name="city">
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="address">Address</label>
                        <textarea class="form-control" id="address" name="address" rows="2"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="createDivision()">Create Division</button>
            </div>
        </div>
    </div>
</div>

<script>
// Modern Division Management JavaScript
let currentPage = 1;
let currentFilters = {};

function showCreateModal() {
    $('#createDivisionModal').modal('show');
}

function createDivision() {
    const form = document.getElementById('createDivisionForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());
    
    fetch('{{ url_for("divisions.create") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            $('#createDivisionModal').modal('hide');
            location.reload();
        } else {
            showNotification(data.errors ? data.errors.join(', ') : data.error, 'danger');
        }
    })
    .catch(error => {
        showNotification('Error creating division: ' + error.message, 'danger');
    });
}

function confirmDelete(divisionId, divisionName) {
    if (confirm(`Are you sure you want to delete division "${divisionName}"?`)) {
        deleteDivision(divisionId);
    }
}

function deleteDivision(divisionId) {
    fetch(`{{ url_for("divisions.delete", division_id="DIVISION_ID") }}`.replace('DIVISION_ID', divisionId), {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            location.reload();
        } else {
            showNotification(data.error, 'danger');
        }
    })
    .catch(error => {
        showNotification('Error deleting division: ' + error.message, 'danger');
    });
}

function applyFilters() {
    currentFilters = {
        search: document.getElementById('searchInput').value,
        status: document.getElementById('statusFilter').value,
        category: document.getElementById('categoryFilter').value
    };
    currentPage = 1;
    loadDivisions();
}

function loadDivisions() {
    const params = new URLSearchParams({
        page: currentPage,
        per_page: 10,
        ...currentFilters
    });
    
    fetch(`{{ url_for("divisions.api_list") }}?${params}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateTable(data.data);
            updatePagination(data.pagination);
        } else {
            showNotification('Error loading divisions: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        showNotification('Error loading divisions: ' + error.message, 'danger');
    });
}

function updateTable(divisions) {
    const tbody = document.getElementById('divisionsTableBody');
    tbody.innerHTML = '';
    
    divisions.forEach(division => {
        const row = createDivisionRow(division);
        tbody.appendChild(row);
    });
}

function createDivisionRow(division) {
    const row = document.createElement('tr');
    row.innerHTML = `
        <td><input type="checkbox" class="division-checkbox" value="${division.division_id}"></td>
        <td><strong>${division.code}</strong></td>
        <td>
            <div class="d-flex align-items-center">
                <div class="mr-3">
                    <div class="icon-circle bg-primary">
                        <i class="fas fa-sitemap text-white"></i>
                    </div>
                </div>
                <div>
                    <div class="font-weight-bold">${division.name}</div>
                    <div class="text-muted small">${(division.description || '').substring(0, 50)}${division.description && division.description.length > 50 ? '...' : ''}</div>
                </div>
            </div>
        </td>
        <td><span class="badge badge-info">${division.category || 'N/A'}</span></td>
        <td><span class="badge badge-${getStatusBadgeClass(division.status)}">${division.status ? division.status.charAt(0).toUpperCase() + division.status.slice(1) : 'N/A'}</span></td>
        <td>${division.budget_formatted}</td>
        <td>${division.revenue_formatted}</td>
        <td>${division.manager || 'Not Assigned'}</td>
        <td>
            <div class="btn-group" role="group">
                <a href="{{ url_for('divisions.view', division_id='DIVISION_ID') }}".replace('DIVISION_ID', division.division_id) 
                   class="btn btn-sm btn-info" title="View Details">
                    <i class="fas fa-eye"></i>
                </a>
                <a href="{{ url_for('divisions.edit', division_id='DIVISION_ID') }}".replace('DIVISION_ID', division.division_id) 
                   class="btn btn-sm btn-warning" title="Edit">
                    <i class="fas fa-edit"></i>
                </a>
                <button type="button" class="btn btn-sm btn-danger" 
                        onclick="confirmDelete('${division.division_id}', '${division.name}')" 
                        title="Delete">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </td>
    `;
    return row;
}

function getStatusBadgeClass(status) {
    switch(status) {
        case 'active': return 'success';
        case 'inactive': return 'secondary';
        case 'suspended': return 'warning';
        default: return 'danger';
    }
}

function updatePagination(pagination) {
    document.getElementById('showingStart').textContent = ((pagination.page - 1) * pagination.per_page) + 1;
    document.getElementById('showingEnd').textContent = Math.min(pagination.page * pagination.per_page, pagination.total);
    document.getElementById('totalRecords').textContent = pagination.total;
    
    // Update pagination controls
    const paginationEl = document.getElementById('pagination');
    paginationEl.innerHTML = '';
    
    // Previous button
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${pagination.page <= 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${pagination.page - 1})">Previous</a>`;
    paginationEl.appendChild(prevLi);
    
    // Page numbers
    for (let i = 1; i <= pagination.pages; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === pagination.page ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
        paginationEl.appendChild(li);
    }
    
    // Next button
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${pagination.page >= pagination.pages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${pagination.page + 1})">Next</a>`;
    paginationEl.appendChild(nextLi);
}

function changePage(page) {
    currentPage = page;
    loadDivisions();
}

function exportData() {
    window.location.href = '{{ url_for("divisions.export") }}?format=csv';
}

function showNotification(message, type) {
    // Create toast notification
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    `;
    
    document.body.appendChild(toast);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 5000);
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Set up search input event listener
    document.getElementById('searchInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            applyFilters();
        }
    });
    
    // Set up select all checkbox
    document.getElementById('selectAll').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.division-checkbox');
        checkboxes.forEach(cb => cb.checked = this.checked);
    });
});
</script>

<style>
.icon-circle {
    height: 2.5rem;
    width: 2.5rem;
    border-radius: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.card {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}

.table th {
    border-top: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.8rem;
    color: #5a5c69;
}

.btn-group .btn {
    margin-right: 2px;
}

.badge {
    font-size: 0.75rem;
}

.list-group-item {
    border-left: none;
    border-right: none;
}

.modal-lg {
    max-width: 800px;
}

.form-text {
    font-size: 0.8rem;
}
</style>
{% endblock %}
