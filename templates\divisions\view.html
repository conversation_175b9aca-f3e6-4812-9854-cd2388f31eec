{% extends 'base.html' %}

{% block title %}{{ division.name }} - Division Details{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-sitemap text-primary"></i> {{ division.name }}
            <span class="badge badge-{{ 'success' if division.status == 'active' else 'secondary' }} ml-2">
                {{ division.status.title() }}
            </span>
        </h1>
        <div class="btn-group" role="group">
            <a href="{{ url_for('divisions.edit', division_id=division.division_id) }}" class="btn btn-warning shadow-sm">
                <i class="fas fa-edit fa-sm text-white-50"></i> Edit
            </a>
            <a href="{{ url_for('divisions.index') }}" class="btn btn-secondary shadow-sm">
                <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back
            </a>
        </div>
    </div>

    <!-- Division Overview Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Division Code
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ division.code }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-tag fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Budget
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                Rs. {{ "{:,.2f}".format(division.budget or 0) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Category
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ division.category or 'N/A' }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-layer-group fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Created
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ division.created_at.split(' ')[0] if division.created_at else 'N/A' }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Row -->
    <div class="row">
        <!-- Division Details -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Division Details</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="font-weight-bold">Division ID:</td>
                                    <td>{{ division.division_id }}</td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">Name:</td>
                                    <td>{{ division.name }}</td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">Code:</td>
                                    <td><span class="badge badge-primary">{{ division.code }}</span></td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">Status:</td>
                                    <td>
                                        {% if division.status == 'active' %}
                                        <span class="badge badge-success">Active</span>
                                        {% elif division.status == 'inactive' %}
                                        <span class="badge badge-secondary">Inactive</span>
                                        {% elif division.status == 'suspended' %}
                                        <span class="badge badge-warning">Suspended</span>
                                        {% else %}
                                        <span class="badge badge-danger">{{ division.status.title() }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">Category:</td>
                                    <td>{{ division.category or 'Not specified' }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="font-weight-bold">Budget:</td>
                                    <td>Rs. {{ "{:,.2f}".format(division.budget or 0) }}</td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">Location:</td>
                                    <td>{{ division.location or 'Not specified' }}</td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">City:</td>
                                    <td>{{ division.city or 'Not specified' }}</td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">Created By:</td>
                                    <td>{{ division.created_by or 'System' }}</td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">Last Updated:</td>
                                    <td>{{ division.updated_at or 'Never' }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    {% if division.description %}
                    <div class="mt-3">
                        <h6 class="font-weight-bold">Description:</h6>
                        <p class="text-muted">{{ division.description }}</p>
                    </div>
                    {% endif %}

                    {% if division.address %}
                    <div class="mt-3">
                        <h6 class="font-weight-bold">Address:</h6>
                        <p class="text-muted">{{ division.address }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Contact Information -->
            {% if division.contact_email or division.contact_phone %}
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Contact Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% if division.contact_email %}
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-envelope text-primary mr-2"></i>
                                <div>
                                    <strong>Email:</strong><br>
                                    <a href="mailto:{{ division.contact_email }}">{{ division.contact_email }}</a>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        {% if division.contact_phone %}
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-phone text-primary mr-2"></i>
                                <div>
                                    <strong>Phone:</strong><br>
                                    <a href="tel:{{ division.contact_phone }}">{{ division.contact_phone }}</a>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Analytics Summary -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Analytics Summary</h6>
                </div>
                <div class="card-body">
                    {% if analytics %}
                    <div class="list-group list-group-flush">
                        {% for metric in analytics %}
                        <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                            <div>
                                <strong>{{ metric.metric_name.replace('_', ' ').title() }}</strong>
                                <br><small class="text-muted">{{ metric.metric_date }}</small>
                            </div>
                            <span class="badge badge-primary badge-pill">
                                {% if 'revenue' in metric.metric_name or 'profit' in metric.metric_name %}
                                Rs. {{ "{:,.0f}".format(metric.metric_value or 0) }}
                                {% else %}
                                {{ "{:,.0f}".format(metric.metric_value or 0) }}
                                {% endif %}
                            </span>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-muted">No analytics data available</p>
                    {% endif %}
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="{{ url_for('divisions.edit', division_id=division.division_id) }}" 
                           class="list-group-item list-group-item-action">
                            <i class="fas fa-edit text-warning mr-2"></i>
                            Edit Division
                        </a>
                        <a href="#" onclick="exportDivision('{{ division.division_id }}')" 
                           class="list-group-item list-group-item-action">
                            <i class="fas fa-download text-info mr-2"></i>
                            Export Data
                        </a>
                        <a href="#" onclick="viewAnalytics('{{ division.division_id }}')" 
                           class="list-group-item list-group-item-action">
                            <i class="fas fa-chart-line text-success mr-2"></i>
                            View Analytics
                        </a>
                        <a href="#" onclick="confirmDelete('{{ division.division_id }}', '{{ division.name }}')" 
                           class="list-group-item list-group-item-action text-danger">
                            <i class="fas fa-trash text-danger mr-2"></i>
                            Delete Division
                        </a>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            {% if audit_log %}
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Activity</h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        {% for activity in audit_log[:5] %}
                        <div class="list-group-item px-0">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <i class="fas fa-{{ 'plus' if activity.action_type == 'CREATE' else 'edit' if activity.action_type == 'UPDATE' else 'trash' }}"></i>
                                    {{ activity.action_type.title() }}
                                </h6>
                                <small>{{ activity.changed_at.split(' ')[0] if activity.changed_at else 'Unknown' }}</small>
                            </div>
                            <p class="mb-1">By: {{ activity.changed_by or 'System' }}</p>
                            <small>{{ activity.changed_at or 'Unknown time' }}</small>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function exportDivision(divisionId) {
    window.location.href = `{{ url_for("divisions.export") }}?division_id=${divisionId}`;
}

function viewAnalytics(divisionId) {
    window.location.href = `{{ url_for("divisions.analytics") }}?division_id=${divisionId}`;
}

function confirmDelete(divisionId, divisionName) {
    if (confirm(`Are you sure you want to delete division "${divisionName}"?\n\nThis action cannot be undone.`)) {
        deleteDivision(divisionId);
    }
}

function deleteDivision(divisionId) {
    fetch(`{{ url_for("divisions.delete", division_id="DIVISION_ID") }}`.replace('DIVISION_ID', divisionId), {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            setTimeout(() => {
                window.location.href = '{{ url_for("divisions.index") }}';
            }, 1500);
        } else {
            showNotification(data.error, 'danger');
        }
    })
    .catch(error => {
        showNotification('Error deleting division: ' + error.message, 'danger');
    });
}

function showNotification(message, type) {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 5000);
}
</script>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.card {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}

.table-borderless td {
    border: none;
    padding: 0.5rem 0;
}

.list-group-item {
    border-left: none;
    border-right: none;
}

.badge {
    font-size: 0.75rem;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}
</style>
{% endblock %}
