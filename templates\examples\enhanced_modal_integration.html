{% extends "base.html" %}

{% block title %}Enhanced Order Modal - Integration Example{% endblock %}

{% block extra_css %}
<!-- Enhanced Modal Styles -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/enhanced_modal.css') }}">

<style>
/* Example page styles */
.integration-demo {
    padding: 2rem 0;
}

.demo-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 2rem;
}

.demo-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0;
    padding: 1.5rem;
}

.demo-content {
    padding: 2rem;
}

.order-list-item {
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.2s ease;
}

.order-list-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.integration-code {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid integration-demo">
    <div class="row">
        <div class="col-12">
            <div class="demo-card">
                <div class="demo-header">
                    <h2 class="mb-0">
                        <i class="fas fa-magic me-2"></i>
                        Enhanced Order Modal - Integration Guide
                    </h2>
                    <p class="mb-0 mt-2 opacity-75">
                        Modern, responsive order details modal with QR code generation
                    </p>
                </div>
                
                <div class="demo-content">
                    <!-- Features Overview -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h4><i class="fas fa-star text-warning me-2"></i>Key Features</h4>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Modern Bootstrap 5 design</li>
                                <li><i class="fas fa-check text-success me-2"></i>Responsive mobile-first layout</li>
                                <li><i class="fas fa-check text-success me-2"></i>Smooth animations and transitions</li>
                                <li><i class="fas fa-check text-success me-2"></i>QR code generation with branding</li>
                                <li><i class="fas fa-check text-success me-2"></i>Print and download functionality</li>
                                <li><i class="fas fa-check text-success me-2"></i>Loading states and error handling</li>
                                <li><i class="fas fa-check text-success me-2"></i>Dark mode support</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h4><i class="fas fa-qrcode text-primary me-2"></i>QR Code Features</h4>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Comprehensive order information</li>
                                <li><i class="fas fa-check text-success me-2"></i>Customer and delivery details</li>
                                <li><i class="fas fa-check text-success me-2"></i>Product list with quantities</li>
                                <li><i class="fas fa-check text-success me-2"></i>FOC (Free of Charge) items</li>
                                <li><i class="fas fa-check text-success me-2"></i>Company branding and contact info</li>
                                <li><i class="fas fa-check text-success me-2"></i>Scannable format with error correction</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Demo Orders -->
                    <h4><i class="fas fa-play text-primary me-2"></i>Try the Enhanced Modal</h4>
                    <p class="text-muted mb-3">Click on any order below to see the enhanced modal in action:</p>
                    
                    <div class="row">
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="order-list-item">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="mb-0">ORD000000246</h6>
                                    <span class="badge bg-success">Completed</span>
                                </div>
                                <p class="text-muted mb-2">Customer: Test Customer</p>
                                <p class="text-muted mb-3">Amount: Rs.76,500</p>
                                <button class="btn btn-primary btn-sm w-100" 
                                        onclick="showEnhancedOrderDetails('ORD000000246')">
                                    <i class="fas fa-eye me-1"></i>View Details
                                </button>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="order-list-item">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="mb-0">ORD000000155</h6>
                                    <span class="badge bg-warning">Processing</span>
                                </div>
                                <p class="text-muted mb-2">Customer: ABC Pharmacy</p>
                                <p class="text-muted mb-3">Amount: Rs.125,000</p>
                                <button class="btn btn-primary btn-sm w-100" 
                                        onclick="showEnhancedOrderDetails('ORD000000155')">
                                    <i class="fas fa-eye me-1"></i>View Details
                                </button>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="order-list-item">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="mb-0">ORD000000157</h6>
                                    <span class="badge bg-info">Ready for Pickup</span>
                                </div>
                                <p class="text-muted mb-2">Customer: XYZ Medical</p>
                                <p class="text-muted mb-3">Amount: Rs.89,750</p>
                                <button class="btn btn-primary btn-sm w-100" 
                                        onclick="showEnhancedOrderDetails('ORD000000157')">
                                    <i class="fas fa-eye me-1"></i>View Details
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Integration Instructions -->
                    <div class="mt-5">
                        <h4><i class="fas fa-code text-info me-2"></i>Integration Instructions</h4>
                        
                        <div class="accordion" id="integrationAccordion">
                            <!-- Step 1 -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="step1Header">
                                    <button class="accordion-button" type="button" data-bs-toggle="collapse" 
                                            data-bs-target="#step1" aria-expanded="true" aria-controls="step1">
                                        Step 1: Include Required Files
                                    </button>
                                </h2>
                                <div id="step1" class="accordion-collapse collapse show" 
                                     aria-labelledby="step1Header" data-bs-parent="#integrationAccordion">
                                    <div class="accordion-body">
                                        <p>Add these files to your template's head section:</p>
                                        <div class="integration-code">
&lt;!-- Enhanced Modal Styles --&gt;<br>
&lt;link rel="stylesheet" href="{{ url_for('static', filename='css/enhanced_modal.css') }}"&gt;<br><br>
&lt;!-- Enhanced Modal JavaScript --&gt;<br>
&lt;script src="{{ url_for('static', filename='js/enhanced_modal.js') }}"&gt;&lt;/script&gt;
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Step 2 -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="step2Header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                            data-bs-target="#step2" aria-expanded="false" aria-controls="step2">
                                        Step 2: Include Modal HTML
                                    </button>
                                </h2>
                                <div id="step2" class="accordion-collapse collapse" 
                                     aria-labelledby="step2Header" data-bs-parent="#integrationAccordion">
                                    <div class="accordion-body">
                                        <p>Include the modal component in your template:</p>
                                        <div class="integration-code">
&lt;!-- Include Enhanced Order Modal --&gt;<br>
{% include 'components/enhanced_order_modal.html' %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Step 3 -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="step3Header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                            data-bs-target="#step3" aria-expanded="false" aria-controls="step3">
                                        Step 3: Add Trigger Buttons
                                    </button>
                                </h2>
                                <div id="step3" class="accordion-collapse collapse" 
                                     aria-labelledby="step3Header" data-bs-parent="#integrationAccordion">
                                    <div class="accordion-body">
                                        <p>Replace existing "View Details" buttons with enhanced modal triggers:</p>
                                        <div class="integration-code">
&lt;!-- Old way --&gt;<br>
&lt;button onclick="viewOrderDetails('ORD123')"&gt;View Details&lt;/button&gt;<br><br>
&lt;!-- New enhanced way --&gt;<br>
&lt;button onclick="showEnhancedOrderDetails('ORD123')" class="btn btn-primary"&gt;<br>
&nbsp;&nbsp;&lt;i class="fas fa-eye me-1"&gt;&lt;/i&gt;View Details<br>
&lt;/button&gt;
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Step 4 -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="step4Header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                            data-bs-target="#step4" aria-expanded="false" aria-controls="step4">
                                        Step 4: Install QR Code Dependencies
                                    </button>
                                </h2>
                                <div id="step4" class="accordion-collapse collapse" 
                                     aria-labelledby="step4Header" data-bs-parent="#integrationAccordion">
                                    <div class="accordion-body">
                                        <p>Install required Python packages for QR code generation:</p>
                                        <div class="integration-code">
pip install qrcode[pil] pillow
                                        </div>
                                        <p class="mt-2">The system will automatically detect if these packages are missing and show appropriate error messages.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- API Endpoints -->
                    <div class="mt-5">
                        <h4><i class="fas fa-plug text-success me-2"></i>API Endpoints</h4>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Endpoint</th>
                                        <th>Method</th>
                                        <th>Description</th>
                                        <th>Response</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><code>/api/order-details/&lt;order_id&gt;</code></td>
                                        <td><span class="badge bg-success">GET</span></td>
                                        <td>Get comprehensive order information</td>
                                        <td>Order details, items, customer info, summary</td>
                                    </tr>
                                    <tr>
                                        <td><code>/api/order-qr-code/&lt;order_id&gt;</code></td>
                                        <td><span class="badge bg-success">GET</span></td>
                                        <td>Generate QR code for order</td>
                                        <td>Base64 encoded QR code image</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include Enhanced Order Modal -->
{% include 'components/enhanced_order_modal.html' %}
{% endblock %}

{% block extra_js %}
<!-- Enhanced Modal JavaScript -->
<script src="{{ url_for('static', filename='js/enhanced_modal.js') }}"></script>

<script>
// Demo functionality
document.addEventListener('DOMContentLoaded', function() {
    console.log('Enhanced Order Modal Integration Demo Ready');
    
    // Add some demo interactions
    const demoCards = document.querySelectorAll('.order-list-item');
    demoCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.borderColor = '#007bff';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.borderColor = '#e9ecef';
        });
    });
});

// Override console.error for demo to show user-friendly messages
const originalConsoleError = console.error;
console.error = function(...args) {
    if (args[0] && args[0].includes('Order not found')) {
        // Show a demo message instead of error
        alert('Demo Mode: This is a sample order ID. In production, this would load real order data.');
        return;
    }
    originalConsoleError.apply(console, args);
};
</script>
{% endblock %}
