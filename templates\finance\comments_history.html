{% extends "base.html" %}

{% block title %}Comments History - Finance{% endblock %}

{% block content %}
<style>
    .comments-page {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .page-title {
        color: white;
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 5px;
    }
    
    .page-subtitle {
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 0;
    }
    
    .comments-container {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }
    
    .entity-info {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 30px;
        border-left: 5px solid #007bff;
    }
    
    .entity-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 10px;
    }
    
    .entity-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }
    
    .detail-item {
        display: flex;
        flex-direction: column;
    }
    
    .detail-label {
        font-size: 0.85rem;
        color: #6c757d;
        margin-bottom: 3px;
        font-weight: 500;
    }
    
    .detail-value {
        font-size: 1rem;
        color: #2c3e50;
        font-weight: 600;
    }
    
    .hold-alert {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        border: 1px solid #ffc107;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .hold-alert .alert-title {
        font-weight: 700;
        color: #856404;
        margin-bottom: 8px;
    }
    
    .comment-item {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 15px;
        transition: all 0.3s ease;
        position: relative;
    }
    
    .comment-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
    
    .comment-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 12px;
    }
    
    .comment-author {
        font-weight: 700;
        color: #2c3e50;
        margin-right: 15px;
    }
    
    .comment-date {
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .comment-type {
        position: absolute;
        top: 15px;
        right: 15px;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .comment-type.general {
        background: #e3f2fd;
        color: #1976d2;
    }
    
    .comment-type.hold {
        background: #fff3e0;
        color: #f57c00;
    }
    
    .comment-type.release {
        background: #e8f5e8;
        color: #388e3c;
    }
    
    .comment-type.approval {
        background: #f3e5f5;
        color: #7b1fa2;
    }
    
    .comment-type.system {
        background: #f5f5f5;
        color: #616161;
    }
    
    .comment-text {
        color: #2c3e50;
        line-height: 1.6;
        margin-bottom: 10px;
    }
    
    .internal-badge {
        background: #dc3545;
        color: white;
        padding: 2px 6px;
        border-radius: 8px;
        font-size: 0.7rem;
        font-weight: 600;
        margin-left: 10px;
    }
    
    .add-comment-form {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 12px;
        padding: 25px;
        margin-top: 30px;
        border: 1px solid #dee2e6;
    }
    
    .form-title {
        font-size: 1.2rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 20px;
    }
    
    .btn-add-comment {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
        color: white;
        padding: 10px 20px;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-add-comment:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        color: white;
    }
    
    .no-comments {
        text-align: center;
        padding: 40px;
        color: #6c757d;
    }
    
    .no-comments i {
        font-size: 3rem;
        margin-bottom: 15px;
        color: #dee2e6;
    }
    
    @media (max-width: 768px) {
        .entity-details {
            grid-template-columns: 1fr;
        }
        
        .comment-header {
            flex-direction: column;
            align-items: flex-start;
        }
        
        .comment-type {
            position: static;
            margin-top: 10px;
        }
    }
</style>

<div class="comments-page">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-comments mr-3"></i>Comments History
            </h1>
            <p class="page-subtitle">View and manage comments for {{ entity_details.type or 'Entity' }}</p>
        </div>
        
        <!-- Comments Container -->
        <div class="comments-container">
            <!-- Entity Information -->
            {% if entity_details %}
            <div class="entity-info">
                <div class="entity-title">{{ entity_details.title }}</div>
                <div class="entity-details">
                    <div class="detail-item">
                        <span class="detail-label">{{ entity_details.type }} ID</span>
                        <span class="detail-value">{{ entity_details.id }}</span>
                    </div>
                    {% if entity_details.amount %}
                    <div class="detail-item">
                        <span class="detail-label">Amount</span>
                        <span class="detail-value">Rs.{{ "{:,.0f}".format(entity_details.amount) }}</span>
                    </div>
                    {% endif %}
                    {% if entity_details.status %}
                    <div class="detail-item">
                        <span class="detail-label">Status</span>
                        <span class="detail-value">{{ entity_details.status }}</span>
                    </div>
                    {% endif %}
                    {% if entity_details.date %}
                    <div class="detail-item">
                        <span class="detail-label">Date</span>
                        <span class="detail-value">{{ entity_details.date.strftime('%Y-%m-%d') if entity_details.date else 'N/A' }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}
            
            <!-- Hold Alert -->
            {% if hold_info %}
            <div class="hold-alert">
                <div class="alert-title">
                    <i class="fas fa-pause mr-2"></i>This order is currently on hold
                </div>
                <div><strong>Reason:</strong> {{ hold_info.hold_reason.replace('_', ' ').title() }}</div>
                <div><strong>Comments:</strong> {{ hold_info.hold_comments }}</div>
                <div><strong>Hold Date:</strong> {{ hold_info.hold_date.strftime('%Y-%m-%d %H:%M') if hold_info.hold_date else 'N/A' }}</div>
                <div><strong>Hold By:</strong> {{ hold_info.hold_by }}</div>
            </div>
            {% endif %}
            
            <!-- Header with Actions -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4>
                    <i class="fas fa-comment-dots mr-2"></i>Comments 
                    <span class="badge badge-primary">{{ comments|length }}</span>
                </h4>
                
                <div class="d-flex gap-2">
                    <a href="{{ url_for('finance_pending_invoices_management') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Invoices
                    </a>
                </div>
            </div>
            
            <!-- Comments List -->
            {% if comments %}
                {% for comment in comments %}
                <div class="comment-item">
                    <div class="comment-type {{ comment.comment_type }}">{{ comment.comment_type }}</div>
                    
                    <div class="comment-header">
                        <div>
                            <span class="comment-author">{{ comment.created_by }}</span>
                            {% if comment.is_internal %}
                            <span class="internal-badge">Internal</span>
                            {% endif %}
                        </div>
                        <span class="comment-date">{{ comment.created_at.strftime('%Y-%m-%d %H:%M') if comment.created_at else 'N/A' }}</span>
                    </div>
                    
                    <div class="comment-text">{{ comment.comment_text }}</div>
                </div>
                {% endfor %}
            {% else %}
                <div class="no-comments">
                    <i class="fas fa-comment-slash"></i>
                    <h5>No Comments Yet</h5>
                    <p>Be the first to add a comment for this {{ entity_details.type.lower() if entity_details else 'entity' }}.</p>
                </div>
            {% endif %}
            
            <!-- Add Comment Form -->
            <div class="add-comment-form">
                <div class="form-title">
                    <i class="fas fa-plus-circle mr-2"></i>Add New Comment
                </div>
                
                <form action="{{ url_for('finance_add_comment') }}" method="POST">
                    <input type="hidden" name="entity_type" value="{{ entity_type }}">
                    <input type="hidden" name="entity_id" value="{{ entity_id }}">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Comment Type</label>
                                <select name="comment_type" class="form-control">
                                    <option value="general">General</option>
                                    <option value="approval">Approval</option>
                                    <option value="hold">Hold</option>
                                    <option value="release">Release</option>
                                    <option value="payment">Payment</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" name="is_internal" id="is_internal">
                                    <label class="form-check-label" for="is_internal">
                                        Internal Comment (Not visible to customer)
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>Comment <span class="text-danger">*</span></label>
                        <textarea name="comment_text" class="form-control" rows="4" 
                                  placeholder="Enter your comment here..." required></textarea>
                    </div>
                    
                    <button type="submit" class="btn btn-add-comment">
                        <i class="fas fa-plus mr-2"></i>Add Comment
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function refreshComments() {
    location.reload();
}

// Auto-refresh every 2 minutes
setInterval(function() {
    location.reload();
}, 120000);
</script>
{% endblock %}
