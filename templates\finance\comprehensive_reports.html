{% extends "base.html" %}

{% block title %}Comprehensive Finance Reports - Medivent ERP{% endblock %}

{% block head %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
{% endblock %}

{% block content %}
<style>
    /* Enhanced Comprehensive Reports Styles - Matching Reference Image */
    .reports-dashboard {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        min-height: 100vh;
        padding: 20px 0;
    }

    .dashboard-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 20px;
    }

    /* Header Section */
    .reports-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .reports-title {
        color: white;
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 8px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .reports-subtitle {
        color: rgba(255, 255, 255, 0.9);
        font-size: 1rem;
        margin-bottom: 0;
    }

    /* Filter Section */
    .filter-section {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .filter-title {
        color: #2c3e50;
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }

    .filter-title i {
        margin-right: 10px;
        color: #3498db;
    }

    .filter-row {
        margin-bottom: 15px;
    }

    .filter-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 5px;
        display: block;
    }

    .form-control, .form-select {
        border-radius: 8px;
        border: 1px solid #ddd;
        padding: 10px 15px;
        font-size: 0.9rem;
    }

    .form-control:focus, .form-select:focus {
        border-color: #3498db;
        box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    }

    .btn-filter {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 10px 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-filter:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        color: white;
    }

    .btn-export {
        background: linear-gradient(135deg, #27ae60, #229954);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: 600;
        margin-left: 10px;
        transition: all 0.3s ease;
    }

    .btn-export:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
        color: white;
    }

    /* 2025 Modern Summary Cards */
    .summary-cards {
        margin-bottom: 40px;
    }

    .summary-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 28px;
        margin-bottom: 20px;
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.1),
            0 2px 16px rgba(0, 0, 0, 0.05),
            inset 0 1px 0 rgba(255, 255, 255, 0.8);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        height: 140px;
        display: flex;
        align-items: center;
        position: relative;
        overflow: hidden;
    }

    .summary-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: var(--card-accent, linear-gradient(135deg, #3498db, #2980b9));
        border-radius: 20px 20px 0 0;
    }

    .summary-card:hover {
        transform: translateY(-6px) scale(1.02);
        box-shadow:
            0 20px 60px rgba(0, 0, 0, 0.15),
            0 8px 32px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.9);
        border-color: rgba(255, 255, 255, 0.5);
    }

    .summary-icon {
        width: 70px;
        height: 70px;
        border-radius: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28px;
        color: white;
        margin-right: 24px;
        box-shadow:
            0 8px 24px rgba(0, 0, 0, 0.15),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        position: relative;
        overflow: hidden;
    }

    .summary-icon::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transform: rotate(45deg);
        transition: all 0.6s ease;
        opacity: 0;
    }

    .summary-card:hover .summary-icon::before {
        opacity: 1;
        animation: shimmer 1.5s ease-in-out;
    }

    .summary-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .summary-content h3 {
        font-size: 2.2rem;
        font-weight: 800;
        background: linear-gradient(135deg, #2c3e50, #34495e);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 8px;
        line-height: 1;
        letter-spacing: -0.02em;
    }

    .summary-content p {
        color: #64748b;
        font-size: 1rem;
        font-weight: 600;
        margin: 0;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .summary-trend {
        position: absolute;
        top: 16px;
        right: 16px;
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 0.8rem;
        font-weight: 600;
        padding: 4px 8px;
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.7);
        backdrop-filter: blur(10px);
    }

    /* Card accent colors */
    .summary-card.revenue { --card-accent: linear-gradient(135deg, #10b981, #059669); }
    .summary-card.invoices { --card-accent: linear-gradient(135deg, #3b82f6, #2563eb); }
    .summary-card.pending { --card-accent: linear-gradient(135deg, #f59e0b, #d97706); }
    .summary-card.customers { --card-accent: linear-gradient(135deg, #8b5cf6, #7c3aed); }

    /* Chart Section */
    .chart-section {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .chart-title {
        color: #2c3e50;
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }

    .chart-title i {
        margin-right: 10px;
        color: #3498db;
    }

    /* Report Cards */
    .report-card {
        background: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        cursor: pointer;
        border: 2px solid transparent;
        position: relative;
        overflow: hidden;
    }

    .report-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        border-color: #3498db;
    }

    .report-card.selected {
        border-color: #2980b9;
        background: #f8f9fa;
        box-shadow: 0 8px 25px rgba(41, 128, 185, 0.2);
    }

    .report-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        background: linear-gradient(135deg, #3498db, #2980b9);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        margin-bottom: 15px;
    }

    .report-name {
        font-size: 1.1rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 8px;
    }

    .report-description {
        color: #7f8c8d;
        font-size: 0.9rem;
        margin-bottom: 15px;
        line-height: 1.4;
    }

    .report-actions {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
    }

    .btn-report {
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 0.8rem;
        font-weight: 500;
        border: none;
        transition: all 0.2s ease;
    }

    .btn-pdf {
        background: #e74c3c;
        color: white;
    }

    .btn-pdf:hover {
        background: #c0392b;
        color: white;
    }

    .btn-excel {
        background: #27ae60;
        color: white;
    }

    .btn-excel:hover {
        background: #229954;
        color: white;
    }

    .btn-view {
        background: #3498db;
        color: white;
    }

    .btn-view:hover {
        background: #2980b9;
        color: white;
    }

    .category-title {
        color: #2c3e50;
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }

    .category-title i {
        margin-right: 10px;
        color: #3498db;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .reports-title {
            font-size: 1.5rem;
        }

        .summary-card {
            height: auto;
            padding: 15px;
        }

        .summary-icon {
            width: 50px;
            height: 50px;
            font-size: 20px;
        }

        .summary-content h3 {
            font-size: 1.4rem;
        }

        .chart-container {
            height: 250px;
        }
    }

    /* Loading Overlay Styles */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: none;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        backdrop-filter: blur(5px);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .loading-overlay.show {
        opacity: 1;
        visibility: visible;
    }

    .loading-content {
        text-align: center;
        color: white;
        background: rgba(255, 255, 255, 0.1);
        padding: 40px;
        border-radius: 15px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    }

    .loading-content h5 {
        margin: 20px 0 10px 0;
        font-size: 1.5rem;
        font-weight: 600;
    }

    .loading-content p {
        margin: 0;
        opacity: 0.8;
        font-size: 1rem;
    }

    .spinner {
        width: 50px;
        height: 50px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>

<div class="reports-dashboard">
    <div class="dashboard-container">
        <!-- Header -->
        <div class="reports-header">
            <h1 class="reports-title">
                <i class="fas fa-chart-line mr-3"></i>Comprehensive Finance Reports
            </h1>
            <p class="reports-subtitle">Generate detailed financial reports and analytics</p>
        </div>

        <!-- Filter Section -->
        <div class="filter-section">
            <div class="filter-title">
                <i class="fas fa-filter"></i>Report Filters
            </div>
            <form id="reportFilters">
                <div class="row">
                    <div class="col-md-3">
                        <div class="filter-row">
                            <label class="filter-label">Date Range</label>
                            <select class="form-select" id="dateRange">
                                <option value="7">Last 7 Days</option>
                                <option value="30" selected>Last 30 Days</option>
                                <option value="90">Last 90 Days</option>
                                <option value="365">Last Year</option>
                                <option value="custom">Custom Range</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="filter-row">
                            <label class="filter-label">Report Type</label>
                            <select class="form-select" id="reportType">
                                <option value="all">All Reports</option>
                                <option value="revenue">Revenue Analysis</option>
                                <option value="payments">Payment Analysis</option>
                                <option value="customers">Customer Analysis</option>
                                <option value="aging">Aging Analysis</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="filter-row">
                            <label class="filter-label">Customer</label>
                            <select class="form-select" id="customerFilter">
                                <option value="all">All Customers</option>
                                <!-- Customer options will be populated dynamically -->
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="filter-row">
                            <label class="filter-label">&nbsp;</label>
                            <div>
                                <button type="button" class="btn btn-filter" onclick="generateReport()">
                                    <i class="fas fa-search"></i> Generate Report
                                </button>
                                <button type="button" class="btn btn-export" onclick="exportReport('pdf')">
                                    <i class="fas fa-file-pdf"></i> PDF
                                </button>
                                <button type="button" class="btn btn-export" onclick="exportReport('excel')">
                                    <i class="fas fa-file-excel"></i> Excel
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Custom Date Range (Hidden by default) -->
                <div class="row" id="customDateRange" style="display: none;">
                    <div class="col-md-3">
                        <div class="filter-row">
                            <label class="filter-label">Start Date</label>
                            <input type="date" class="form-control" id="startDate">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="filter-row">
                            <label class="filter-label">End Date</label>
                            <input type="date" class="form-control" id="endDate">
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- 2025 Modern Summary Cards -->
        <div class="row summary-cards">
            <div class="col-lg-3 col-md-6">
                <div class="summary-card revenue">
                    <div class="summary-trend">
                        <i class="fas fa-arrow-up text-success"></i>
                        <span class="text-success">+12.5%</span>
                    </div>
                    <div class="summary-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                        <i class="fas fa-rupee-sign"></i>
                    </div>
                    <div class="summary-content">
                        <h3 id="totalRevenue">₹0</h3>
                        <p>Total Revenue</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="summary-card invoices">
                    <div class="summary-trend">
                        <i class="fas fa-arrow-up text-success"></i>
                        <span class="text-success">+8.3%</span>
                    </div>
                    <div class="summary-icon" style="background: linear-gradient(135deg, #3b82f6, #2563eb);">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div class="summary-content">
                        <h3 id="totalInvoices">0</h3>
                        <p>Total Invoices</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="summary-card pending">
                    <div class="summary-trend">
                        <i class="fas fa-arrow-down text-warning"></i>
                        <span class="text-warning">-5.2%</span>
                    </div>
                    <div class="summary-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="summary-content">
                        <h3 id="pendingAmount">₹0</h3>
                        <p>Pending Amount</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="summary-card customers">
                    <div class="summary-trend">
                        <i class="fas fa-arrow-up text-success"></i>
                        <span class="text-success">+15.7%</span>
                    </div>
                    <div class="summary-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="summary-content">
                        <h3 id="totalCustomers">0</h3>
                        <p>Active Customers</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="row">
            <div class="col-lg-6">
                <div class="chart-section">
                    <div class="chart-title">
                        <i class="fas fa-chart-line"></i>Revenue Trend
                    </div>
                    <div class="chart-container">
                        <canvas id="revenueChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="chart-section">
                    <div class="chart-title">
                        <i class="fas fa-chart-pie"></i>Payment Status Distribution
                    </div>
                    <div class="chart-container">
                        <canvas id="paymentChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-6">
                <div class="chart-section">
                    <div class="chart-title">
                        <i class="fas fa-chart-bar"></i>Top Customers by Revenue
                    </div>
                    <div class="chart-container">
                        <canvas id="customerChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="chart-section">
                    <div class="chart-title">
                        <i class="fas fa-chart-area"></i>Monthly Comparison
                    </div>
                    <div class="chart-container">
                        <canvas id="monthlyChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>

<script>
// Initialize charts and functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize date range selector
    document.getElementById('dateRange').addEventListener('change', function() {
        const customRange = document.getElementById('customDateRange');
        if (this.value === 'custom') {
            customRange.style.display = 'block';
        } else {
            customRange.style.display = 'none';
        }
    });

    // Load initial data
    loadReportData();
    initializeCharts();
});

function loadReportData() {
    // Load real data from backend
    document.getElementById('totalRevenue').textContent = '₹{{ "{:,.0f}".format(stats.total_sales or 0) }}';
    document.getElementById('totalInvoices').textContent = '{{ stats.total_orders or 0 }}';
    document.getElementById('pendingAmount').textContent = '₹{{ "{:,.0f}".format(stats.pending_amount or 0) }}';
    document.getElementById('totalCustomers').textContent = '{{ stats.total_customers or 0 }}';
}

function initializeCharts() {
    // Load chart data from API
    fetch('/finance/api/chart-data')
        .then(response => response.json())
        .then(data => {
            createRevenueChart(data.monthly_revenue);
            createCustomerChart(data.customer_revenue);
            createPaymentStatusChart(data.payment_status);
            createDivisionChart(data.division_performance);
        })
        .catch(error => {
            console.error('Error loading chart data:', error);
            // Fallback to empty charts
            createRevenueChart({labels: [], data: []});
            createCustomerChart({labels: [], data: []});
            createPaymentStatusChart({labels: [], data: []});
            createDivisionChart({labels: [], data: []});
        });
}

function createRevenueChart(data) {
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');
    new Chart(revenueCtx, {
        type: 'line',
        data: {
            labels: data.labels || [],
            datasets: [{
                label: 'Revenue',
                data: data.data || [],
                borderColor: '#3498db',
                backgroundColor: 'rgba(52, 152, 219, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return 'Revenue: ₹' + context.parsed.y.toLocaleString();
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '₹' + (value / 1000) + 'K';
                        }
                    }
                }
            }
        }
    });

}

function createPaymentStatusChart(data) {
    const paymentCtx = document.getElementById('paymentChart').getContext('2d');
    new Chart(paymentCtx, {
        type: 'doughnut',
        data: {
            labels: data.labels || ['No Data'],
            datasets: [{
                data: data.data || [1],
                backgroundColor: ['#27ae60', '#f39c12', '#e74c3c', '#9b59b6', '#34495e']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.parsed || 0;
                            const amount = data.amounts ? data.amounts[context.dataIndex] : 0;
                            return `${label}: ${value} orders (₹${amount.toLocaleString()})`;
                        }
                    }
                }
            }
        }
    });

}

function createCustomerChart(data) {
    const customerCtx = document.getElementById('customerChart').getContext('2d');
    new Chart(customerCtx, {
        type: 'bar',
        data: {
            labels: data.labels || [],
            datasets: [{
                label: 'Revenue',
                data: data.data || [],
                backgroundColor: '#3498db',
                borderColor: '#2980b9',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return 'Revenue: ₹' + context.parsed.y.toLocaleString();
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '₹' + (value / 1000) + 'K';
                        }
                    }
                }
            }
        }
    });

}

function createDivisionChart(data) {
    const divisionCtx = document.getElementById('monthlyChart').getContext('2d');
    new Chart(divisionCtx, {
        type: 'pie',
        data: {
            labels: data.labels || [],
            datasets: [{
                data: data.data || [],
                backgroundColor: [
                    '#3498db', '#27ae60', '#f39c12', '#e74c3c', '#9b59b6',
                    '#34495e', '#1abc9c', '#e67e22', '#95a5a6', '#2c3e50'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.parsed || 0;
                            return `${label}: ₹${value.toLocaleString()}`;
                        }
                    }
                }
            }
        }
    });
}

function generateReport() {
    const dateRange = document.getElementById('dateRange').value;
    const reportType = document.getElementById('reportType').value;
    const customer = document.getElementById('customerFilter').value;

    console.log('Generating report with filters:', {
        dateRange,
        reportType,
        customer
    });

    // Show loading state
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';
    btn.disabled = true;

    // Simulate report generation
    setTimeout(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
        alert('Report generated successfully!');
    }, 2000);
}

function exportReport(format) {
    console.log('Exporting report as:', format);

    // Show loading state
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    btn.disabled = true;

    // Simulate export
    setTimeout(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
        alert(`Report exported as ${format.toUpperCase()} successfully!`);
    }, 1500);
}
</script>

        <!-- Report Categories -->
        {% set categories = {} %}
        {% for report in report_types %}
            {% if report.category not in categories %}
                {% set _ = categories.update({report.category: []}) %}
            {% endif %}
            {% set _ = categories[report.category].append(report) %}
        {% endfor %}

        {% for category, reports in categories.items() %}
        <div class="report-category">
            <h3 class="category-title">
                <i class="fas fa-folder-open me-2"></i>{{ category }}
            </h3>
            <div class="row">
                {% for report in reports %}
                <div class="col-md-6 col-lg-4">
                    <div class="report-card" onclick="selectReport('{{ report.id }}')">
                        <div class="report-icon">
                            <i class="{{ report.icon }}"></i>
                        </div>
                        <div class="report-name">{{ report.name }}</div>
                        <div class="report-description">{{ report.description }}</div>
                        <div class="report-actions">
                            <button class="btn btn-pdf btn-report" onclick="generateReport('{{ report.id }}', 'pdf'); event.stopPropagation();">
                                <i class="fas fa-file-pdf me-1"></i>PDF
                            </button>
                            <button class="btn btn-excel btn-report" onclick="generateReport('{{ report.id }}', 'excel'); event.stopPropagation();">
                                <i class="fas fa-file-excel me-1"></i>Excel
                            </button>
                            <button class="btn btn-view btn-report" onclick="viewReport('{{ report.id }}'); event.stopPropagation();">
                                <i class="fas fa-eye me-1"></i>View
                            </button>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Loading Overlay -->
<div class="loading-overlay" id="loadingOverlay">
    <div class="loading-content">
        <div class="spinner"></div>
        <h5>Generating Report...</h5>
        <p>Please wait while we prepare your financial report.</p>
    </div>
</div>

<script>
let selectedReport = null;

function selectReport(reportId) {
    selectedReport = reportId;
    // Visual feedback for selection
    document.querySelectorAll('.report-card').forEach(card => {
        card.style.background = '#f8f9fa';
        card.style.borderColor = '#e9ecef';
    });
    event.currentTarget.style.background = '#e3f2fd';
    event.currentTarget.style.borderColor = 'var(--primary)';
}

function generateReport(reportId, format) {
    showLoading();

    const filters = {
        date_from: document.getElementById('global_date_from')?.value || '',
        date_to: document.getElementById('global_date_to')?.value || '',
        division: document.getElementById('global_division')?.value || '',
        format: format
    };

    // Add timeout fallback to prevent infinite loading
    const loadingTimeout = setTimeout(() => {
        hideLoading();
        showNotification('Request timeout. Please try again.', 'error');
    }, 30000); // 30 second timeout

    fetch('/finance/api/generate-comprehensive-report', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            report_type: reportId,
            filters: filters
        })
    })
    .then(response => {
        clearTimeout(loadingTimeout);
        if (response.ok) {
            return response.json();
        }
        throw new Error('Report generation failed');
    })
    .then(data => {
        hideLoading();
        if (data.success) {
            showNotification(`${data.report_type.replace('_', ' ').toUpperCase()} report generated successfully!`, 'success');

            // For now, show the data in console (can be enhanced to show in modal)
            console.log('Report Data:', data.data);

            if (format === 'pdf') {
                showNotification('PDF generation feature coming soon. Showing JSON data in console.', 'info');
            }
        } else {
            showNotification(`Error: ${data.error}`, 'error');
        }
    })
    .catch(error => {
        clearTimeout(loadingTimeout);
        hideLoading();
        console.error('Error:', error);
        showNotification('Error generating report. Please try again.', 'error');
    });
}

function viewReport(reportId) {
    showLoading();

    // Add timeout fallback
    const loadingTimeout = setTimeout(() => {
        hideLoading();
    }, 3000); // 3 second timeout for view operations

    try {
        // Open report in new window
        const reportUrl = `/finance/api/view-report/${reportId}`;
        window.open(reportUrl, '_blank');

        // Clear timeout and hide loading after a short delay
        setTimeout(() => {
            clearTimeout(loadingTimeout);
            hideLoading();
            showNotification(`Opening ${reportId.replace('_', ' ').toUpperCase()} report...`, 'info');
        }, 1000);
    } catch (error) {
        clearTimeout(loadingTimeout);
        hideLoading();
        showNotification('Error opening report. Please try again.', 'error');
    }
}

// Removed duplicate function - using the one above

function showLoading() {
    // Show loading overlay with improved styling and CSS transitions
    let overlay = document.getElementById('loadingOverlay');

    if (overlay) {
        overlay.style.display = 'flex';
        overlay.classList.add('show');
        // Force reflow to ensure transition works
        overlay.offsetHeight;
    } else {
        // Fallback: create overlay if not found
        overlay = document.createElement('div');
        overlay.id = 'loadingOverlay';
        overlay.className = 'loading-overlay show';
        overlay.innerHTML = `
            <div class="loading-content">
                <div class="spinner"></div>
                <h5>Generating Report...</h5>
                <p>Please wait while we prepare your financial report.</p>
            </div>
        `;
        document.body.appendChild(overlay);
        overlay.style.display = 'flex';
        // Force reflow to ensure transition works
        overlay.offsetHeight;
    }
}

function hideLoading() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.classList.remove('show');
        // Hide after transition completes
        setTimeout(() => {
            overlay.style.display = 'none';
        }, 300);
    }

    // Also hide any dynamically created overlays
    const dynamicOverlays = document.querySelectorAll('.loading-overlay');
    dynamicOverlays.forEach(o => {
        o.classList.remove('show');
        setTimeout(() => {
            o.style.display = 'none';
        }, 300);
    });
}

function showNotification(message, type = 'info') {
    // Create notification
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10000;
        min-width: 300px;
    `;
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}

        const extension = format === 'excel' ? 'xlsx' : 'pdf';
        const timestamp = new Date().toISOString().split('T')[0];
        a.download = `${reportId}_report_${timestamp}.${extension}`;

        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        hideLoading();
        showSuccess(`${format.toUpperCase()} report generated successfully!`);
    })
    .catch(error => {
        console.error('Error:', error);
        hideLoading();
        showError('Error generating report. Please try again.');
    });
}

// Duplicate viewReport function removed - using the improved version above

function showSuccess(message) {
    // Create success toast
    const toast = document.createElement('div');
    toast.className = 'alert alert-success position-fixed';
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 10000; min-width: 300px;';
    toast.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
    document.body.appendChild(toast);

    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 5000);
}

function showError(message) {
    // Create error toast
    const toast = document.createElement('div');
    toast.className = 'alert alert-danger position-fixed';
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 10000; min-width: 300px;';
    toast.innerHTML = `
        <i class="fas fa-exclamation-circle me-2"></i>${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
    document.body.appendChild(toast);

    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 5000);
}

// Bulk report generation with improved timeout handling
function generateAllReports() {
    if (confirm('This will generate all 16 reports. This may take several minutes. Continue?')) {
        showLoading();

        const reportIds = [
            'profit_loss', 'cash_flow', 'balance_sheet', 'aging_analysis',
            'customer_performance', 'product_profitability', 'sales_trends',
            'payment_analysis', 'outstanding_receivables', 'agent_performance',
            'division_analysis', 'monthly_summary', 'quarterly_review',
            'tax_summary', 'inventory_valuation', 'credit_analysis'
        ];

        let completed = 0;
        const total = reportIds.length;

        // Add overall timeout for bulk generation
        const bulkTimeout = setTimeout(() => {
            hideLoading();
            showNotification('Bulk generation timeout. Some reports may still be processing.', 'warning');
        }, 300000); // 5 minute overall timeout

        reportIds.forEach((reportId, index) => {
            setTimeout(() => {
                generateReport(reportId, 'pdf');
                completed++;

                if (completed === total) {
                    clearTimeout(bulkTimeout);
                    hideLoading();
                    showNotification('All reports generated successfully!', 'success');
                }
            }, index * 2000); // Stagger requests by 2 seconds
        });
    }
}
</script>

{% endblock %}
