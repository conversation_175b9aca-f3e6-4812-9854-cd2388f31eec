{% extends "base.html" %}

{% block title %}Enhanced Customer Ledger - Finance{% endblock %}

{% block head %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block content %}
<style>
    .finance-dashboard {
        background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .finance-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .finance-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 10px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }
    
    .finance-subtitle {
        color: rgba(255, 255, 255, 0.9);
        font-size: 1.1rem;
        margin-bottom: 0;
    }
    
    .filter-card {
        background: white;
        border-radius: 20px;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: none;
    }
    
    .customer-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        border: 1px solid rgba(0,0,0,0.05);
        position: relative;
        overflow: hidden;
    }
    
    .customer-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary), var(--secondary));
    }
    
    .customer-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }
    
    .customer-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }
    
    .customer-name {
        font-size: 1.3rem;
        font-weight: 700;
        color: var(--dark);
        margin-bottom: 5px;
    }
    
    .customer-code {
        color: var(--muted);
        font-size: 0.9rem;
    }
    
    .balance-info {
        text-align: right;
    }
    
    .balance-amount {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 5px;
    }
    
    .balance-positive {
        color: #28a745;
    }
    
    .balance-negative {
        color: #dc3545;
    }
    
    .balance-zero {
        color: #6c757d;
    }
    
    .aging-indicator {
        display: flex;
        gap: 10px;
        margin-top: 15px;
    }
    
    .aging-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    .aging-current {
        background: rgba(40, 167, 69, 0.2);
        color: #155724;
    }
    
    .aging-30 {
        background: rgba(255, 193, 7, 0.2);
        color: #856404;
    }
    
    .aging-60 {
        background: rgba(255, 152, 0, 0.2);
        color: #e65100;
    }
    
    .aging-90 {
        background: rgba(220, 53, 69, 0.2);
        color: #721c24;
    }
    
    .risk-indicator {
        position: absolute;
        top: 15px;
        right: 15px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
    }
    
    .risk-low {
        background: #28a745;
    }
    
    .risk-medium {
        background: #ffc107;
    }
    
    .risk-high {
        background: #fd7e14;
    }
    
    .risk-critical {
        background: #dc3545;
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }
    
    .transaction-table {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }
    
    .transaction-table th {
        background: linear-gradient(135deg, var(--primary), var(--secondary));
        color: white;
        border: none;
        font-weight: 600;
        padding: 15px;
    }
    
    .transaction-table td {
        padding: 12px 15px;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }
    
    .transaction-table tbody tr:hover {
        background: rgba(var(--primary-rgb), 0.05);
    }
    
    .btn-modern {
        border-radius: 8px;
        font-weight: 600;
        padding: 8px 16px;
        transition: all 0.3s ease;
    }
    
    .btn-modern:hover {
        transform: translateY(-1px);
    }
    
    .stat-card {
        background: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        text-align: center;
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
        margin: 0 auto 15px;
    }
    
    .stat-value {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 5px;
        color: var(--dark);
    }
    
    .stat-label {
        color: var(--muted);
        font-size: 0.9rem;
    }
</style>

<div class="finance-dashboard">
    <div class="container-fluid">
        <!-- Header Section -->
        <div class="finance-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="finance-title">
                        <i class="fas fa-users me-3"></i>Enhanced Customer Ledger
                    </h1>
                    <p class="finance-subtitle">Comprehensive customer financial tracking with aging analysis and risk assessment</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-light btn-modern" onclick="location.reload()">
                        <i class="fas fa-sync-alt me-2"></i>Refresh
                    </button>
                    <button class="btn btn-light btn-modern" onclick="switchToAging()">
                        <i class="fas fa-chart-bar me-2"></i>Aging Analysis
                    </button>
                    <button class="btn btn-light btn-modern" onclick="exportLedger()">
                        <i class="fas fa-download me-2"></i>Export
                    </button>
                    <button class="btn btn-light btn-modern" onclick="generateReport()">
                        <i class="fas fa-file-pdf me-2"></i>Generate Report
                    </button>
                </div>
            </div>
        </div>

        <!-- Advanced Filter Section -->
        <div class="filter-card">
            <h5 class="mb-3">
                <i class="fas fa-filter me-2 text-primary"></i>Advanced Filters & Search
            </h5>
            <form method="GET" class="row g-3">
                <div class="col-md-2">
                    <label class="form-label fw-bold">Customer Name</label>
                    <input type="text" class="form-control" name="customer_name" 
                           value="{{ filters.customer_name if filters else '' }}" placeholder="Search by name">
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">Customer Code</label>
                    <input type="text" class="form-control" name="customer_code" 
                           value="{{ filters.customer_code if filters else '' }}" placeholder="Customer code">
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">Invoice Number</label>
                    <input type="text" class="form-control" name="invoice_number" 
                           value="{{ filters.invoice_number if filters else '' }}" placeholder="Invoice #">
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">Balance Status</label>
                    <select class="form-control" name="balance_status">
                        <option value="all" {{ 'selected' if filters and filters.balance_status == 'all' else '' }}>All Balances</option>
                        <option value="positive" {{ 'selected' if filters and filters.balance_status == 'positive' else '' }}>Credit Balance</option>
                        <option value="negative" {{ 'selected' if filters and filters.balance_status == 'negative' else '' }}>Debit Balance</option>
                        <option value="zero" {{ 'selected' if filters and filters.balance_status == 'zero' else '' }}>Zero Balance</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">Risk Level</label>
                    <select class="form-control" name="risk_level">
                        <option value="all" {{ 'selected' if filters and filters.risk_level == 'all' else '' }}>All Risk Levels</option>
                        <option value="low" {{ 'selected' if filters and filters.risk_level == 'low' else '' }}>Low Risk</option>
                        <option value="medium" {{ 'selected' if filters and filters.risk_level == 'medium' else '' }}>Medium Risk</option>
                        <option value="high" {{ 'selected' if filters and filters.risk_level == 'high' else '' }}>High Risk</option>
                        <option value="critical" {{ 'selected' if filters and filters.risk_level == 'critical' else '' }}>Critical Risk</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary btn-modern d-block w-100">
                        <i class="fas fa-search me-1"></i>Filter
                    </button>
                </div>
            </form>
        </div>

        <!-- Summary Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #007bff, #0056b3);">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-value">{{ summary.total_customers if summary else 0 }}</div>
                    <div class="stat-label">Total Customers</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #28a745, #1e7e34);">
                        <i class="fas fa-rupee-sign"></i>
                    </div>
                    <div class="stat-value">₹{{ "{:,.0f}".format(summary.total_receivables if summary and summary.total_receivables else 0) }}</div>
                    <div class="stat-label">Total Receivables</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #ffc107, #e0a800);">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stat-value">{{ summary.overdue_customers if summary else 0 }}</div>
                    <div class="stat-label">Overdue Customers</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #dc3545, #c82333);">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-value">₹{{ "{:,.0f}".format(summary.overdue_amount if summary and summary.overdue_amount else 0) }}</div>
                    <div class="stat-label">Overdue Amount</div>
                </div>
            </div>
        </div>

        <!-- Customer Analytics -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="stat-card">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-area me-2 text-primary"></i>Customer Balance Trends
                        </h5>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary btn-sm active" onclick="showCustomerChart('receivables')">Receivables</button>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="showCustomerChart('payments')">Payments</button>
                        </div>
                    </div>
                    <div style="position: relative; height: 250px;">
                        <canvas id="customerTrendsChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card h-100">
                    <h5 class="mb-3">
                        <i class="fas fa-chart-pie me-2 text-warning"></i>Risk Distribution
                    </h5>
                    <div style="position: relative; height: 200px;">
                        <canvas id="riskDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Ledger List -->
        <div class="row">
            <div class="col-12">
                <h4 class="mb-4 text-white">
                    <i class="fas fa-list me-2"></i>Customer Ledger
                    <span class="badge bg-light text-dark ms-2">{{ customers|length if customers else 0 }} Customers</span>
                </h4>

                {% if customers %}
                    {% for customer in customers %}
                    <div class="customer-card">
                        <div class="risk-indicator risk-{{ customer.risk_level or 'low' }}"
                             title="Risk Level: {{ (customer.risk_level or 'low').title() }}"></div>

                        <div class="customer-header">
                            <div class="customer-info">
                                <div class="customer-name">{{ customer.customer_name }}</div>
                                <div class="customer-code">
                                    <i class="fas fa-id-card me-1"></i>{{ customer.customer_code or 'N/A' }}
                                    {% if customer.customer_phone %}
                                        <span class="mx-2">•</span>
                                        <i class="fas fa-phone me-1"></i>{{ customer.customer_phone }}
                                    {% endif %}
                                    {% if customer.salesperson %}
                                        <span class="mx-2">•</span>
                                        <i class="fas fa-user-tie me-1"></i>{{ customer.salesperson }}
                                    {% endif %}
                                </div>
                            </div>

                            <div class="balance-info">
                                <div class="balance-amount
                                    {% if customer.balance > 0 %}balance-positive
                                    {% elif customer.balance < 0 %}balance-negative
                                    {% else %}balance-zero{% endif %}">
                                    ₹{{ "{:,.0f}".format(customer.balance or 0) }}
                                </div>
                                <small class="text-muted">Current Balance</small>
                            </div>
                        </div>

                        <!-- Customer Statistics -->
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <small class="text-muted">Total Orders</small>
                                <div class="fw-bold">{{ customer.total_orders or 0 }}</div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">Total Sales</small>
                                <div class="fw-bold text-success">₹{{ "{:,.0f}".format(customer.total_sales or 0) }}</div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">Last Payment</small>
                                <div class="fw-bold">{{ customer.last_payment_date|safe_date('%d %b %Y') if customer.last_payment_date else 'Never' }}</div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">Days Outstanding</small>
                                <div class="fw-bold text-warning">{{ customer.days_outstanding or 0 }} days</div>
                            </div>
                        </div>

                        <!-- Aging Analysis -->
                        {% if customer.aging_data %}
                        <div class="aging-indicator">
                            <span class="aging-badge aging-current">
                                0-30: ₹{{ "{:,.0f}".format(customer.aging_data.current or 0) }}
                            </span>
                            <span class="aging-badge aging-30">
                                31-60: ₹{{ "{:,.0f}".format(customer.aging_data.days_30 or 0) }}
                            </span>
                            <span class="aging-badge aging-60">
                                61-90: ₹{{ "{:,.0f}".format(customer.aging_data.days_60 or 0) }}
                            </span>
                            <span class="aging-badge aging-90">
                                90+: ₹{{ "{:,.0f}".format(customer.aging_data.days_90_plus or 0) }}
                            </span>
                        </div>
                        {% endif %}

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div class="btn-group" role="group">
                                <button class="btn btn-outline-primary btn-modern btn-sm" onclick="viewCustomerHistory('{{ customer.customer_id }}')">
                                    <i class="fas fa-history me-1"></i>History
                                </button>
                                <button class="btn btn-outline-success btn-modern btn-sm" onclick="viewInvoices('{{ customer.customer_id }}')">
                                    <i class="fas fa-file-invoice me-1"></i>Invoices
                                </button>
                                <button class="btn btn-outline-info btn-modern btn-sm" onclick="viewPayments('{{ customer.customer_id }}')">
                                    <i class="fas fa-credit-card me-1"></i>Payments
                                </button>
                                <button class="btn btn-outline-warning btn-modern btn-sm" onclick="sendReminder('{{ customer.customer_id }}')">
                                    <i class="fas fa-bell me-1"></i>Remind
                                </button>
                            </div>

                            {% if customer.balance > 0 %}
                            <button class="btn btn-success btn-modern btn-sm" onclick="collectPayment('{{ customer.customer_id }}', '{{ customer.customer_name }}', {{ customer.balance }})">
                                <i class="fas fa-money-bill-wave me-1"></i>Collect Payment
                            </button>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-5">
                        <div class="stat-card">
                            <i class="fas fa-users fa-4x text-muted mb-3"></i>
                            <h5 class="text-muted">No customers found</h5>
                            <p class="text-muted">No customers match your current filters or no customer data is available.</p>
                            <button class="btn btn-primary btn-modern" onclick="location.href='{{ url_for('finance_customer_ledger') }}'">
                                <i class="fas fa-refresh me-1"></i>Clear Filters
                            </button>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
// Enhanced Customer Ledger Functions
function viewCustomerHistory(customerId) {
    fetch(`/finance/api/customer/${customerId}/history`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Customer history loaded - detailed modal will be implemented');
            } else {
                alert('Error loading customer history: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading customer history');
        });
}

function viewInvoices(customerId) {
    window.open(`/finance/customer/${customerId}/invoices`, '_blank');
}

function viewPayments(customerId) {
    window.open(`/finance/customer/${customerId}/payments`, '_blank');
}

function sendReminder(customerId) {
    if (confirm('Send payment reminder to this customer?')) {
        alert('Payment reminder functionality will be implemented');
    }
}

function collectPayment(customerId, customerName, balance) {
    window.location.href = `/finance/payment-collection?customer=${encodeURIComponent(customerName)}&amount=${balance}`;
}

function switchToAging() {
    const params = new URLSearchParams(window.location.search);
    params.set('view', 'aging');
    window.location.href = '/finance/customer-ledger?' + params.toString();
}

function exportLedger() {
    const params = new URLSearchParams(window.location.search);
    window.location.href = '/finance/export-customer-ledger?' + params.toString();
}

function generateReport() {
    const params = new URLSearchParams(window.location.search);
    window.open('/finance/generate-ledger-report?' + params.toString(), '_blank');
}

// Customer Analytics Charts
let customerChart = null;
let riskChart = null;

function initializeCustomerCharts() {
    // Customer Trends Chart
    const customerCtx = document.getElementById('customerTrendsChart').getContext('2d');

    customerChart = new Chart(customerCtx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'Outstanding Receivables',
                data: [],
                borderColor: 'rgba(40, 167, 69, 1)',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: 'rgba(40, 167, 69, 1)',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '₹' + (value / 1000) + 'K';
                        }
                    }
                }
            }
        }
    });

    // Risk Distribution Chart
    const riskCtx = document.getElementById('riskDistributionChart').getContext('2d');

    riskChart = new Chart(riskCtx, {
        type: 'doughnut',
        data: {
            labels: ['Low Risk', 'Medium Risk', 'High Risk', 'Critical'],
            datasets: [{
                data: [65, 20, 12, 3],
                backgroundColor: [
                    'rgba(40, 167, 69, 0.8)',
                    'rgba(255, 193, 7, 0.8)',
                    'rgba(255, 87, 34, 0.8)',
                    'rgba(220, 53, 69, 0.8)'
                ],
                borderColor: [
                    'rgba(40, 167, 69, 1)',
                    'rgba(255, 193, 7, 1)',
                    'rgba(255, 87, 34, 1)',
                    'rgba(220, 53, 69, 1)'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 10,
                        usePointStyle: true,
                        font: {
                            size: 11
                        }
                    }
                }
            }
        }
    });
}

function showCustomerChart(type) {
    // Update active button
    document.querySelectorAll('.btn-group .btn').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');

    if (customerChart) {
        customerChart.destroy();
    }

    const ctx = document.getElementById('customerTrendsChart').getContext('2d');
    let chartData, chartColor, chartLabel;

    switch(type) {
        case 'receivables':
            chartData = [];
            chartColor = 'rgba(40, 167, 69, 1)';
            chartLabel = 'Outstanding Receivables';
            break;
        case 'payments':
            chartData = [];
            chartColor = 'rgba(0, 123, 255, 1)';
            chartLabel = 'Payments Received';
            break;
    }

    customerChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: chartLabel,
                data: chartData,
                borderColor: chartColor,
                backgroundColor: chartColor.replace('1)', '0.1)'),
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: chartColor,
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '₹' + (value / 1000) + 'K';
                        }
                    }
                }
            }
        }
    });
}

// Initialize charts on page load
document.addEventListener('DOMContentLoaded', function() {
    initializeCustomerCharts();
});
</script>

{% endblock %}
