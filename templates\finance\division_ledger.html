{% extends "base.html" %}

{% block title %}Division Ledger - Medivent ERP{% endblock %}

{% block head %}
<style>
    /* 2025 Modern Division Ledger Styles */
    .division-dashboard {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px 0;
    }

    /* Modern KPI Cards */
    .modern-division-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 24px;
        padding: 32px;
        margin-bottom: 24px;
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.1),
            0 2px 16px rgba(0, 0, 0, 0.05),
            inset 0 1px 0 rgba(255, 255, 255, 0.8);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        height: 160px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        position: relative;
        overflow: hidden;
    }

    .modern-division-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--card-gradient);
        border-radius: 24px 24px 0 0;
    }

    .modern-division-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow:
            0 20px 60px rgba(0, 0, 0, 0.15),
            0 8px 32px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.9);
    }

    .division-kpi-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;
    }

    .division-kpi-icon {
        width: 64px;
        height: 64px;
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28px;
        color: white;
        background: var(--icon-gradient);
        box-shadow:
            0 8px 24px rgba(0, 0, 0, 0.15),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }

    .division-kpi-trend {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 0.85rem;
        font-weight: 600;
        padding: 6px 12px;
        border-radius: 12px;
        background: rgba(255, 255, 255, 0.7);
        backdrop-filter: blur(10px);
    }

    .division-kpi-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .division-kpi-value {
        font-size: 2.5rem;
        font-weight: 800;
        background: linear-gradient(135deg, #2c3e50, #34495e);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 8px;
        line-height: 1;
        letter-spacing: -0.02em;
    }

    .division-kpi-label {
        color: #64748b;
        font-size: 1rem;
        font-weight: 600;
        margin: 0;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    /* Card variants */
    .modern-division-card.divisions {
        --card-gradient: linear-gradient(135deg, #10b981, #059669);
        --icon-gradient: linear-gradient(135deg, #10b981, #059669, #047857);
    }

    .modern-division-card.revenue {
        --card-gradient: linear-gradient(135deg, #3b82f6, #2563eb);
        --icon-gradient: linear-gradient(135deg, #3b82f6, #2563eb, #1d4ed8);
    }

    .modern-division-card.performance {
        --card-gradient: linear-gradient(135deg, #8b5cf6, #7c3aed);
        --icon-gradient: linear-gradient(135deg, #8b5cf6, #7c3aed, #6d28d9);
    }

    .modern-division-card.growth {
        --card-gradient: linear-gradient(135deg, #f59e0b, #d97706);
        --icon-gradient: linear-gradient(135deg, #f59e0b, #d97706, #b45309);
    }
</style>
{% endblock %}

{% block content %}
<div class="division-dashboard">
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-sitemap text-primary me-2"></i>
                        Division Ledger
                    </h1>
                    <p class="text-muted mb-0">Division-wise financial analysis and performance metrics</p>
                </div>
                <div>
                    <a href="{{ url_for('finance_dashboard') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left"></i> Back to Finance
                    </a>
                    <button class="btn btn-primary" onclick="exportDivisionReport()">
                        <i class="fas fa-download"></i> Export Report
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 2025 Modern Summary Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="modern-division-card divisions">
                <div class="division-kpi-header">
                    <div class="division-kpi-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="division-kpi-trend">
                        <i class="fas fa-arrow-up text-success"></i>
                        <span class="text-success">+3.2%</span>
                    </div>
                </div>
                <div class="division-kpi-content">
                    <div class="division-kpi-value">{{ total_divisions or 0 }}</div>
                    <div class="division-kpi-label">Total Divisions</div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="modern-division-card revenue">
                <div class="division-kpi-header">
                    <div class="division-kpi-icon">
                        <i class="fas fa-rupee-sign"></i>
                    </div>
                    <div class="division-kpi-trend">
                        <i class="fas fa-arrow-up text-success"></i>
                        <span class="text-success">+18.7%</span>
                    </div>
                </div>
                <div class="division-kpi-content">
                    <div class="division-kpi-value">₹{{ (total_division_sales or 0)|safe_currency }}</div>
                    <div class="division-kpi-label">Total Sales</div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="modern-division-card performance">
                <div class="division-kpi-header">
                    <div class="division-kpi-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="division-kpi-trend">
                        <i class="fas fa-arrow-up text-success"></i>
                        <span class="text-success">+12.4%</span>
                    </div>
                </div>
                <div class="division-kpi-content">
                    <div class="division-kpi-value">{{ total_division_orders or 0 }}</div>
                    <div class="division-kpi-label">Total Orders</div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="modern-division-card growth">
                <div class="division-kpi-header">
                    <div class="division-kpi-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div class="division-kpi-trend">
                        <i class="fas fa-arrow-up text-success"></i>
                        <span class="text-success">+7.8%</span>
                    </div>
                </div>
                <div class="division-kpi-content">
                    <div class="division-kpi-value">₹{{ ((total_division_sales or 0) / (total_divisions or 1))|safe_currency }}</div>
                    <div class="division-kpi-label">Avg per Division</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Division Card -->
    {% if top_division %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-trophy"></i> Top Performing Division
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <div class="icon-circle bg-success mx-auto mb-3" style="width: 80px; height: 80px;">
                                <i class="fas fa-crown text-white" style="font-size: 32px; line-height: 80px;"></i>
                            </div>
                            <h5 class="font-weight-bold">{{ top_division.division_name }}</h5>
                            <p class="text-muted">{{ top_division.division_code }}</p>
                        </div>
                        <div class="col-md-9">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h4 class="text-success">Rs. {{ (top_division.total_sales or 0)|safe_currency }}</h4>
                                        <small class="text-muted">Total Sales</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h4 class="text-primary">{{ top_division.total_orders or 0 }}</h4>
                                        <small class="text-muted">Total Orders</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h4 class="text-info">{{ top_division.unique_customers or 0 }}</h4>
                                        <small class="text-muted">Customers</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h4 class="text-warning">{{ top_division.sales_agents or 0 }}</h4>
                                        <small class="text-muted">Sales Agents</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Division Performance Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-table"></i> Division Performance Details
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="divisionTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Division</th>
                            <th>Manager</th>
                            <th>Total Sales</th>
                            <th>Orders</th>
                            <th>Avg Order Value</th>
                            <th>Customers</th>
                            <th>Sales Agents</th>
                            <th>Performance</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for division in division_data %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="mr-3">
                                        <div class="icon-circle bg-primary">
                                            <i class="fas fa-building text-white"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <div class="font-weight-bold">{{ division.division_name }}</div>
                                        <div class="text-muted small">{{ division.division_code }}</div>
                                    </div>
                                </div>
                            </td>
                            <td>{{ division.division_manager or 'Not Assigned' }}</td>
                            <td>
                                <div class="font-weight-bold text-success">
                                    Rs. {{ (division.total_sales or 0)|safe_currency }}
                                </div>
                            </td>
                            <td>
                                <div class="text-center">
                                    <div class="font-weight-bold">{{ division.total_orders or 0 }}</div>
                                    <div class="text-muted small">{{ division.completed_orders or 0 }} completed</div>
                                </div>
                            </td>
                            <td>Rs. {{ (division.avg_order_value or 0)|safe_currency }}</td>
                            <td>{{ division.unique_customers or 0 }}</td>
                            <td>{{ division.sales_agents or 0 }}</td>
                            <td>
                                <div class="progress" style="height: 20px;">
                                    {% set performance = ((division.total_sales or 0) / (total_division_sales or 1) * 100) %}
                                    <div class="progress-bar 
                                        {% if performance > 30 %}bg-success
                                        {% elif performance > 15 %}bg-warning
                                        {% else %}bg-danger{% endif %}" 
                                        role="progressbar" 
                                        style="width: {{ performance }}%" 
                                        aria-valuenow="{{ performance }}" 
                                        aria-valuemin="0" aria-valuemax="100">
                                        {{ performance|round(1) }}%
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-outline-primary" onclick="viewDivisionDetails('{{ division.division_id }}')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-info" onclick="viewDivisionTrends('{{ division.division_id }}')">
                                        <i class="fas fa-chart-line"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Division Performance Chart -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-chart-pie"></i> Division Sales Distribution
            </h6>
        </div>
        <div class="card-body">
            <div class="chart-container">
                <canvas id="divisionChart"></canvas>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#divisionTable').DataTable({
        "pageLength": 25,
        "order": [[ 2, "desc" ]],
        "columnDefs": [
            { "orderable": false, "targets": 8 }
        ]
    });

    // Create division performance chart
    createDivisionChart();
});

function createDivisionChart() {
    const ctx = document.getElementById('divisionChart').getContext('2d');
    
    const divisionData = [
        {% for division in division_data %}
        {
            name: '{{ division.division_name }}',
            sales: {{ division.total_sales or 0 }}
        },
        {% endfor %}
    ];

    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: divisionData.map(d => d.name),
            datasets: [{
                data: divisionData.map(d => d.sales),
                backgroundColor: [
                    '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b',
                    '#858796', '#5a5c69', '#6f42c1', '#e83e8c', '#fd7e14'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

function viewDivisionDetails(divisionId) {
    // Implement detailed view
    window.location.href = `/divisions/${divisionId}`;
}

function viewDivisionTrends(divisionId) {
    // Implement trends view
    alert(`Viewing trends for division ${divisionId}`);
}

function exportDivisionReport() {
    // Implement export functionality
    window.location.href = '/finance/division-ledger/export';
}
</script>

<style>
.icon-circle {
    height: 2.5rem;
    width: 2.5rem;
    border-radius: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-container {
    position: relative;
    height: 400px;
}
</style>
{% endblock %}
</div>
</div>
