{% extends "base.html" %}

{% block title %}Held Invoices - Finance{% endblock %}

{% block content %}
<style>
    .held-invoices {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .page-title {
        color: white;
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 5px;
    }
    
    .page-subtitle {
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 0;
    }
    
    .held-container {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }
    
    .hold-card {
        background: white;
        border: 1px solid #e74c3c;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
        transition: all 0.3s ease;
        position: relative;
        border-left: 5px solid #e74c3c;
    }
    
    .hold-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(231, 76, 60, 0.15);
    }
    
    .hold-card.urgent {
        border-left-color: #c0392b;
        background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);
    }
    
    .hold-card.high {
        border-left-color: #e67e22;
        background: linear-gradient(135deg, #fff8f0 0%, #ffffff 100%);
    }
    
    .hold-card.normal {
        border-left-color: #f39c12;
        background: linear-gradient(135deg, #fffbf0 0%, #ffffff 100%);
    }
    
    .priority-badge {
        position: absolute;
        top: 15px;
        right: 15px;
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .priority-badge.urgent {
        background: #c0392b;
        color: white;
    }
    
    .priority-badge.high {
        background: #e67e22;
        color: white;
    }
    
    .priority-badge.normal {
        background: #f39c12;
        color: white;
    }
    
    .hold-info {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
        margin-bottom: 15px;
    }
    
    .info-item {
        display: flex;
        flex-direction: column;
    }
    
    .info-label {
        font-size: 0.85rem;
        color: #7f8c8d;
        margin-bottom: 3px;
        font-weight: 500;
    }
    
    .info-value {
        font-size: 1rem;
        color: #2c3e50;
        font-weight: 600;
    }
    
    .hold-reason {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 12px;
        margin: 15px 0;
        border-left: 4px solid #e74c3c;
    }
    
    .hold-comments {
        background: #fff5f5;
        border-radius: 8px;
        padding: 12px;
        margin: 15px 0;
        font-style: italic;
        color: #2c3e50;
    }
    
    .action-buttons {
        display: flex;
        gap: 10px;
        margin-top: 15px;
    }
    
    .btn-release {
        background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
        border: none;
        color: white;
        padding: 8px 16px;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-release:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(46, 204, 113, 0.3);
        color: white;
    }
    
    .stats-row {
        margin-bottom: 30px;
    }
    
    .stat-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        text-align: center;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }
    
    .stat-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: #e74c3c;
        margin-bottom: 5px;
    }
    
    .stat-label {
        color: #7f8c8d;
        font-weight: 500;
        text-transform: uppercase;
        font-size: 0.9rem;
    }
    
    .days-held {
        background: #e74c3c;
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    @media (max-width: 768px) {
        .hold-info {
            grid-template-columns: 1fr;
        }
        
        .action-buttons {
            flex-direction: column;
        }
    }
</style>

<div class="held-invoices">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-pause mr-3"></i>Held Invoices
            </h1>
            <p class="page-subtitle">Manage invoices that are currently on hold</p>
        </div>
        
        <!-- Statistics Cards -->
        <div class="row stats-row">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-number">{{ hold_stats.total_held }}</div>
                    <div class="stat-label">Total Held</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-number">{{ hold_stats.urgent_count }}</div>
                    <div class="stat-label">Urgent</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-number">Rs.{{ "{:,.0f}".format(hold_stats.total_amount) }}</div>
                    <div class="stat-label">Total Amount</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-number">{{ "{:.1f}".format(hold_stats.avg_days_held) }}</div>
                    <div class="stat-label">Avg Days Held</div>
                </div>
            </div>
        </div>
        
        <!-- Held Invoices List -->
        <div class="held-container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4>
                    <i class="fas fa-pause mr-2"></i>Held Invoices 
                    <span class="badge badge-danger">{{ held_invoices|length }}</span>
                </h4>
                
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary btn-modern" onclick="refreshHeldInvoices()">
                        <i class="fas fa-sync-alt mr-2"></i>Refresh
                    </button>
                    <a href="{{ url_for('finance_pending_invoices_management') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Pending
                    </a>
                </div>
            </div>
            
            {% if held_invoices %}
                {% for hold in held_invoices %}
                <div class="hold-card {{ hold.priority_level }}">
                    <div class="priority-badge {{ hold.priority_level }}">{{ hold.priority_level }}</div>
                    
                    <div class="hold-info">
                        <div class="info-item">
                            <span class="info-label">Order ID</span>
                            <span class="info-value">{{ hold.order_id }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Customer</span>
                            <span class="info-value">{{ hold.customer_name }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Amount</span>
                            <span class="info-value">Rs.{{ "{:,.0f}".format(hold.order_amount) }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Days on Hold</span>
                            <span class="days-held">{{ hold.days_on_hold }} days</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Hold Date</span>
                            <span class="info-value">{{ hold.hold_date|safe_date('%Y-%m-%d %H:%M') if hold.hold_date else 'N/A' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Hold By</span>
                            <span class="info-value">{{ hold.hold_by }}</span>
                        </div>
                    </div>
                    
                    <div class="hold-reason">
                        <strong>Reason:</strong> {{ hold.hold_reason.replace('_', ' ').title() }}
                    </div>
                    
                    <div class="hold-comments">
                        <strong>Comments:</strong> {{ hold.hold_comments }}
                    </div>
                    
                    <div class="action-buttons">
                        <button class="btn btn-release" onclick="releaseHold('{{ hold.hold_id }}', '{{ hold.order_id }}')">
                            <i class="fas fa-play mr-1"></i>Release from Hold
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="viewOrderDetails('{{ hold.order_id }}')">
                            <i class="fas fa-eye mr-1"></i>View Details
                        </button>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <h5 class="text-muted">No Invoices on Hold</h5>
                    <p class="text-muted">All invoices are currently active and not on hold.</p>
                    <a href="{{ url_for('finance_pending_invoices_management') }}" class="btn btn-primary">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Pending Invoices
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Release Hold Modal -->
<div class="modal fade" id="releaseModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-play text-success mr-2"></i>Release from Hold
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ url_for('finance_release_hold') }}" method="POST">
                <div class="modal-body">
                    <input type="hidden" id="release-hold-id" name="hold_id">
                    <input type="hidden" id="release-order-id" name="order_id">
                    
                    <div class="form-group">
                        <label>Order ID</label>
                        <input type="text" class="form-control" id="release-order-display" readonly>
                    </div>
                    
                    <div class="form-group">
                        <label>Release Comments</label>
                        <textarea class="form-control" name="release_comments" rows="4" 
                                  placeholder="Please provide comments explaining why this invoice is being released from hold..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-play mr-2"></i>Release from Hold
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function releaseHold(holdId, orderId) {
    document.getElementById('release-hold-id').value = holdId;
    document.getElementById('release-order-id').value = orderId;
    document.getElementById('release-order-display').value = orderId;
    $('#releaseModal').modal('show');
}

function viewOrderDetails(orderId) {
    // Navigate to order details page with comprehensive history
    if (orderId) {
        window.open(`/orders/${orderId}`, '_blank');
    } else {
        alert('Order ID not found');
    }
}

function refreshHeldInvoices() {
    location.reload();
}

// Auto-refresh every 2 minutes
setInterval(function() {
    if (!$('#releaseModal').hasClass('show')) {
        location.reload();
    }
}, 120000);
</script>
{% endblock %}
