{% extends "base.html" %}

{% block title %}Enhanced Invoice Generation - Finance{% endblock %}

{% block head %}
<script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block content %}
<style>
    .finance-dashboard {
        background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .finance-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .finance-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 10px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }
    
    .finance-subtitle {
        color: rgba(255, 255, 255, 0.9);
        font-size: 1.1rem;
        margin-bottom: 0;
    }
    
    .invoice-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        border: 1px solid rgba(0,0,0,0.05);
        position: relative;
        overflow: hidden;
    }
    
    .invoice-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary), var(--secondary));
    }
    
    .invoice-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }
    
    .invoice-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }
    
    .order-info {
        flex: 1;
    }
    
    .order-id {
        font-size: 1.3rem;
        font-weight: 700;
        color: var(--primary);
        margin-bottom: 5px;
    }
    
    .customer-name {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--dark);
        margin-bottom: 5px;
    }
    
    .order-details {
        color: var(--muted);
        font-size: 0.9rem;
    }
    
    .amount-info {
        text-align: right;
    }
    
    .order-amount {
        font-size: 1.8rem;
        font-weight: 700;
        color: #28a745;
        margin-bottom: 5px;
    }
    
    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .status-pending {
        background: rgba(255, 193, 7, 0.2);
        color: #856404;
    }
    
    .status-approved {
        background: rgba(40, 167, 69, 0.2);
        color: #155724;
    }
    
    .status-hold {
        background: rgba(220, 53, 69, 0.2);
        color: #721c24;
    }
    
    .order-items {
        margin: 15px 0;
    }
    
    .item-summary {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
    }
    
    .item-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }
    
    .item-row:last-child {
        border-bottom: none;
    }
    
    .item-name {
        font-weight: 600;
        color: var(--dark);
    }
    
    .item-details {
        color: var(--muted);
        font-size: 0.9rem;
    }
    
    .item-amount {
        font-weight: 600;
        color: #28a745;
    }
    
    .action-buttons {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
        margin-top: 15px;
    }

    .order-notes {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        border-left: 4px solid #17a2b8;
    }

    .note-item {
        background: white;
        border-radius: 6px;
        padding: 10px;
        border: 1px solid #e9ecef;
    }

    .note-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 5px;
    }

    .note-content {
        color: #495057;
        font-size: 0.9rem;
        line-height: 1.4;
    }
    
    .btn-modern {
        border-radius: 8px;
        font-weight: 600;
        padding: 8px 16px;
        transition: all 0.3s ease;
    }
    
    .btn-modern:hover {
        transform: translateY(-1px);
    }
    
    .filter-card {
        background: white;
        border-radius: 20px;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: none;
    }
    
    .stat-card {
        background: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        text-align: center;
        transition: all 0.3s ease;
        border: 1px solid rgba(0,0,0,0.05);
    }

    .clickable-card {
        cursor: pointer;
    }

    .clickable-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .stat-action {
        margin-top: 8px;
        opacity: 0.7;
        transition: opacity 0.3s ease;
    }

    .clickable-card:hover .stat-action {
        opacity: 1;
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
        margin: 0 auto 15px;
    }
    
    .stat-value {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 5px;
        color: var(--dark);
    }
    
    .stat-label {
        color: var(--muted);
        font-size: 0.9rem;
    }
    
    .invoice-modal .modal-content {
        border-radius: 15px;
        border: none;
    }
    
    .invoice-modal .modal-header {
        background: linear-gradient(135deg, var(--primary), var(--secondary));
        color: white;
        border-radius: 15px 15px 0 0;
    }
    
    .hold-reason {
        background: rgba(220, 53, 69, 0.1);
        border: 1px solid rgba(220, 53, 69, 0.3);
        border-radius: 8px;
        padding: 10px;
        margin-top: 10px;
    }
</style>

<div class="finance-dashboard">
    <div class="container-fluid">
        <!-- Header Section -->
        <div class="finance-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="finance-title">
                        <i class="fas fa-file-invoice me-3"></i>Enhanced Invoice Generation
                    </h1>
                    <p class="finance-subtitle">Generate invoices with order details, customer ledger access, and hold functionality</p>
                </div>
                <div class="d-flex gap-2">
                    <!-- Finance Quick Access Buttons -->
                    <div class="btn-group me-3">
                        <button type="button" class="btn btn-success btn-modern dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-book me-2"></i>Quick Ledgers
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/finance/customer-ledger" target="_blank">
                                <i class="fas fa-users me-2"></i>Customer Ledger
                            </a></li>
                            <li><a class="dropdown-item" href="/finance/salesperson-ledger" target="_blank">
                                <i class="fas fa-user-tie me-2"></i>Salesperson Ledger
                            </a></li>
                            <li><a class="dropdown-item" href="/finance/division-ledger" target="_blank">
                                <i class="fas fa-building me-2"></i>Division Ledger
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/finance/payment-collection" target="_blank">
                                <i class="fas fa-money-bill-wave me-2"></i>Payment Collection
                            </a></li>
                        </ul>
                    </div>

                    <button class="btn btn-light btn-modern" onclick="location.reload()">
                        <i class="fas fa-sync-alt me-2"></i>Refresh
                    </button>
                    <button class="btn btn-light btn-modern" onclick="bulkInvoiceGeneration()">
                        <i class="fas fa-layer-group me-2"></i>Bulk Generate
                    </button>
                    <button class="btn btn-light btn-modern" onclick="exportPendingOrders()">
                        <i class="fas fa-download me-2"></i>Export
                    </button>
                </div>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="filter-card">
            <h5 class="mb-3">
                <i class="fas fa-filter me-2 text-primary"></i>Order Filters
            </h5>
            <form method="GET" class="row g-3">
                <div class="col-md-2">
                    <label class="form-label fw-bold">Order ID</label>
                    <input type="text" class="form-control" name="order_id" 
                           value="{{ filters.order_id if filters else '' }}" placeholder="Order ID">
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">Customer</label>
                    <input type="text" class="form-control" name="customer" 
                           value="{{ filters.customer if filters else '' }}" placeholder="Customer name">
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">Status</label>
                    <select class="form-control" name="status">
                        <option value="all" {{ 'selected' if filters and filters.status == 'all' else '' }}>All Status</option>
                        <option value="pending" {{ 'selected' if filters and filters.status == 'pending' else '' }}>Pending Invoice</option>
                        <option value="approved" {{ 'selected' if filters and filters.status == 'approved' else '' }}>Approved</option>
                        <option value="hold" {{ 'selected' if filters and filters.status == 'hold' else '' }}>On Hold</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">Date From</label>
                    <input type="date" class="form-control" name="date_from" value="{{ filters.date_from if filters else '' }}">
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">Date To</label>
                    <input type="date" class="form-control" name="date_to" value="{{ filters.date_to if filters else '' }}">
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary btn-modern d-block w-100">
                        <i class="fas fa-search me-1"></i>Filter
                    </button>
                </div>
            </form>
        </div>

        <!-- Summary Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stat-card clickable-card" onclick="filterByStatus('pending')" title="Click to filter pending orders">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #ffc107, #e0a800);">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-value">{{ summary.pending_orders if summary else 0 }}</div>
                    <div class="stat-label">Pending Orders</div>
                    <div class="stat-action">
                        <small class="text-muted"><i class="fas fa-mouse-pointer"></i> Click to filter</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card clickable-card" onclick="showAmountBreakdown()" title="Click to view amount breakdown">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #28a745, #1e7e34);">
                        <i class="fas fa-rupee-sign"></i>
                    </div>
                    <div class="stat-value">Rs.{{ "{:,.0f}".format(summary.pending_amount if summary and summary.pending_amount else 0) }}</div>
                    <div class="stat-label">Pending Amount</div>
                    <div class="stat-action">
                        <small class="text-muted"><i class="fas fa-chart-bar"></i> View breakdown</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card clickable-card" onclick="showTodayInvoices()" title="Click to view today's invoices">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #007bff, #0056b3);">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div class="stat-value">{{ summary.invoices_generated_today if summary else 0 }}</div>
                    <div class="stat-label">Generated Today</div>
                    <div class="stat-action">
                        <small class="text-muted"><i class="fas fa-list"></i> View list</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card clickable-card" onclick="window.location.href='/finance/held-invoices'" title="Click to view held invoices">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #dc3545, #c82333);">
                        <i class="fas fa-pause-circle"></i>
                    </div>
                    <div class="stat-value">{{ summary.orders_on_hold if summary else 0 }}</div>
                    <div class="stat-label">Orders on Hold</div>
                    <div class="stat-action">
                        <small class="text-muted"><i class="fas fa-external-link-alt"></i> Manage holds</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Invoice Generation Analytics -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="stat-card">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar me-2 text-primary"></i>Invoice Generation Trends
                        </h5>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary btn-sm active" onclick="showInvoiceChart('daily')">Daily</button>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="showInvoiceChart('weekly')">Weekly</button>
                        </div>
                    </div>
                    <div style="position: relative; height: 250px;">
                        <canvas id="invoiceGenerationChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card h-100">
                    <h5 class="mb-3">
                        <i class="fas fa-chart-pie me-2 text-success"></i>Order Status Distribution
                    </h5>
                    <div style="position: relative; height: 200px; width: 100%; overflow: hidden;">
                        {% if python_charts_html %}
                            <!-- Python-generated simple status chart -->
                            <div id="pythonChartsContainer" style="height: 100%; width: 100%; display: flex; justify-content: center; align-items: center;">
                                {{ python_charts_html | safe }}
                            </div>
                        {% else %}
                            <!-- Fallback to Chart.js -->
                            <canvas id="orderStatusChart"></canvas>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Orders List -->
        <div class="row">
            <div class="col-12">
                <h4 class="mb-4 text-white">
                    <i class="fas fa-list me-2"></i>Pending Orders for Invoice Generation
                    <span class="badge bg-light text-dark ms-2">{{ orders|length if orders else 0 }} Orders</span>
                </h4>

                {% if orders %}
                    {% for order in orders %}
                    <div class="invoice-card">
                        <div class="invoice-header">
                            <div class="order-info">
                                <div class="order-id">{{ order.order_id }}</div>
                                <div class="customer-name">{{ order.customer_name }}</div>
                                <div class="order-details">
                                    <i class="fas fa-calendar me-1"></i>{{ safe_strftime(order.order_date, '%d %b %Y') if order.order_date else 'N/A' }}
                                    {% if order.sales_agent %}
                                        <span class="mx-2">•</span>
                                        <i class="fas fa-user-tie me-1"></i>{{ order.sales_agent }}
                                    {% endif %}
                                    {% if order.division %}
                                        <span class="mx-2">•</span>
                                        <i class="fas fa-building me-1"></i>{{ order.division }}
                                    {% endif %}
                                </div>
                            </div>

                            <div class="amount-info">
                                <div class="order-amount">Rs.{{ "{:,.0f}".format(order.order_amount or 0) }}</div>
                                <span class="status-badge status-{{ order.status.lower() if order.status else 'pending' }}">
                                    {{ order.status or 'Pending' }}
                                </span>
                            </div>
                        </div>

                        <!-- Order Items Summary -->
                        {% if order.items %}
                        <div class="order-items">
                            <div class="item-summary">
                                <h6 class="mb-2">
                                    <i class="fas fa-boxes me-2"></i>Order Items ({{ order.items|length }})
                                </h6>
                                {% for item in order.items[:3] %}
                                <div class="item-row">
                                    <div>
                                        <div class="item-name">{{ item.product_name }}</div>
                                        <div class="item-details">Qty: {{ item.quantity }} | Rate: Rs.{{ "{:,.0f}".format(item.rate or 0) }}</div>
                                    </div>
                                    <div class="item-amount">Rs.{{ "{:,.0f}".format((item.quantity or 0) * (item.rate or 0)) }}</div>
                                </div>
                                {% endfor %}
                                {% if order.items|length > 3 %}
                                <div class="text-center mt-2">
                                    <small class="text-muted">... and {{ order.items|length - 3 }} more items</small>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}

                        <!-- Real-time Order Notes and Comments -->
                        {% if order.notes or order.hold_comments or order.release_comments %}
                        <div class="order-notes mt-3" id="comments-{{ order.order_id }}">
                            <h6 class="mb-2">
                                <i class="fas fa-sticky-note me-2 text-info"></i>Order Notes & Comments
                                <span class="badge bg-light text-dark ms-2" id="comment-count-{{ order.order_id }}">
                                    {{ (1 if order.notes else 0) + (1 if order.hold_comments else 0) + (1 if order.release_comments else 0) }}
                                </span>
                            </h6>

                            {% if order.notes %}
                            <div class="note-item mb-2">
                                <div class="note-header">
                                    <span class="badge bg-secondary">Order Notes</span>
                                    <small class="text-muted ms-2">
                                        <i class="fas fa-clock me-1"></i>{{ order.order_date[:10] if order.order_date else 'N/A' }}
                                    </small>
                                </div>
                                <div class="note-content">{{ order.notes }}</div>
                            </div>
                            {% endif %}

                            {% if order.hold_comments %}
                            <div class="note-item mb-2 border-warning">
                                <div class="note-header">
                                    <span class="badge bg-warning">Hold Comments</span>
                                    {% if order.hold_by %}
                                    <small class="text-muted ms-2">by {{ order.hold_by }}</small>
                                    {% endif %}
                                    <small class="text-muted ms-2">
                                        <i class="fas fa-pause me-1"></i>Workflow Stage: Hold
                                    </small>
                                </div>
                                <div class="note-content">{{ order.hold_comments }}</div>
                            </div>
                            {% endif %}

                            {% if order.release_comments %}
                            <div class="note-item mb-2 border-success">
                                <div class="note-header">
                                    <span class="badge bg-success">Release Comments</span>
                                    {% if order.release_by %}
                                    <small class="text-muted ms-2">by {{ order.release_by }}</small>
                                    {% endif %}
                                    <small class="text-muted ms-2">
                                        <i class="fas fa-play me-1"></i>Workflow Stage: Released
                                    </small>
                                </div>
                                <div class="note-content">{{ order.release_comments }}</div>
                            </div>
                            {% endif %}

                            <!-- Real-time update indicator -->
                            <div class="text-end mt-2">
                                <small class="text-muted">
                                    <i class="fas fa-sync-alt me-1"></i>Last updated: <span id="last-update-{{ order.order_id }}">{{ order.last_updated[:16] if order.last_updated else 'N/A' }}</span>
                                </small>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Action Buttons -->
                        <div class="action-buttons">
                            <button class="btn btn-success btn-modern btn-sm" onclick="generateInvoice('{{ order.order_id }}', '{{ order.customer_name }}', {{ order.order_amount }})">
                                <i class="fas fa-file-invoice me-1"></i>Generate Invoice
                            </button>

                            <button class="btn btn-info btn-modern btn-sm" onclick="viewOrderDetails('{{ order.order_id }}')">
                                <i class="fas fa-eye me-1"></i>View Details
                            </button>

                            <button class="btn btn-outline-primary btn-modern btn-sm" onclick="viewCustomerLedger('{{ order.customer_name }}')">
                                <i class="fas fa-user me-1"></i>Customer Ledger
                            </button>

                            <button class="btn btn-outline-success btn-modern btn-sm" onclick="viewSalespersonLedger('{{ order.sales_agent }}')">
                                <i class="fas fa-user-tie me-1"></i>Salesperson
                            </button>

                            <button class="btn btn-outline-info btn-modern btn-sm" onclick="viewDivisionLedger('{{ order.division }}')">
                                <i class="fas fa-building me-1"></i>Division
                            </button>

                            <button class="btn btn-outline-warning btn-modern btn-sm" onclick="putOnHold('{{ order.order_id }}')">
                                <i class="fas fa-pause me-1"></i>Put on Hold
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-5">
                        <div class="stat-card">
                            <i class="fas fa-file-invoice fa-4x text-muted mb-3"></i>
                            <h5 class="text-muted">No pending orders found</h5>
                            <p class="text-muted">All orders have been invoiced or no orders match your filters.</p>
                            <button class="btn btn-primary btn-modern" onclick="location.href='{{ url_for('finance_pending_invoices') }}'">
                                <i class="fas fa-refresh me-1"></i>Clear Filters
                            </button>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
// Enhanced Invoice Generation Functions
function generateInvoice(orderId, customerName, orderAmount) {
    // Enhanced validation and confirmation dialog
    const confirmationMessage = `🧾 INVOICE GENERATION CONFIRMATION

Order ID: ${orderId}
Customer: ${customerName}
Amount: Rs.${orderAmount.toLocaleString()}

⚠️ IMPORTANT VALIDATION:
• This action will generate an official invoice
• Invoice numbers are sequential and cannot be changed
• Order will be moved to warehouse for processing
• This action requires finance user authorization

Are you sure you want to proceed with invoice generation?`;

    if (confirm(confirmationMessage)) {
        // Show loading state
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Generating...';
        button.disabled = true;

        fetch('/finance/api/generate-invoice', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Finance-User-Action': 'true', // Finance user validation header
            },
            body: JSON.stringify({
                order_id: orderId,
                customer_name: customerName,
                order_amount: orderAmount,
                finance_user_approved: true,
                timestamp: new Date().toISOString()
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show detailed success message
                const successMessage = `✅ INVOICE GENERATED SUCCESSFULLY!

Invoice Number: ${data.invoice_number || 'Generated'}
Order ID: ${orderId}
Customer: ${customerName}
Amount: Rs.${orderAmount.toLocaleString()}

📋 Next Steps:
• Order moved to warehouse for packing
• Invoice is now available for viewing
• Customer will be notified

Would you like to view the generated invoice now?`;

                if (confirm(successMessage)) {
                    window.open(`/orders/${orderId}`, '_blank');
                }

                // Reload the page to refresh the list
                location.reload();
            } else {
                alert(`❌ INVOICE GENERATION FAILED\n\nError: ${data.error}\n\nPlease check the order status and try again.`);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('❌ NETWORK ERROR\n\nFailed to connect to server. Please check your connection and try again.');
        })
        .finally(() => {
            // Restore button state
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }
}

function viewOrderDetails(orderId) {
    window.open(`/orders/${orderId}`, '_blank');
}

function viewCustomerLedger(customerName) {
    window.open(`/finance/customer-ledger?customer=${encodeURIComponent(customerName)}`, '_blank');
}

function viewSalespersonLedger(salesAgent) {
    if (salesAgent && salesAgent.trim()) {
        window.open(`/finance/salesperson-ledger?salesperson=${encodeURIComponent(salesAgent)}`, '_blank');
    } else {
        alert('No salesperson information available for this order');
    }
}

function viewDivisionLedger(division) {
    if (division && division.trim()) {
        window.open(`/finance/division-ledger?division=${encodeURIComponent(division)}`, '_blank');
    } else {
        alert('No division information available for this order');
    }
}

function putOnHold(orderId) {
    const reason = prompt('Enter reason for putting order on hold:');
    if (reason && reason.trim()) {
        fetch('/finance/api/put-order-on-hold', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                order_id: orderId,
                hold_reason: reason.trim(),
                hold_notes: `Order put on hold by ${getCurrentUser()} on ${new Date().toLocaleString()}`
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Order ${orderId} has been put on hold successfully.`);
                location.reload(); // Refresh the page to update the status
            } else {
                alert('Error putting order on hold: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error putting order on hold. Please try again.');
        });
    }
}

function getCurrentUser() {
    // Get current user from the page context or session
    return document.querySelector('[data-current-user]')?.dataset.currentUser || 'Unknown User';
}

function exportPendingOrders() {
    const params = new URLSearchParams(window.location.search);
    window.location.href = '/finance/export-pending-orders?' + params.toString();
}

// Invoice Generation Charts
let invoiceChart = null;
let statusChart = null;

function initializeInvoiceCharts() {
    // Invoice Generation Trend Chart
    const invoiceCtx = document.getElementById('invoiceGenerationChart').getContext('2d');

    invoiceChart = new Chart(invoiceCtx, {
        type: 'line',
        data: {
            labels: {{ chart_data.daily.labels | tojson if chart_data else "['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']" | safe }},
            datasets: [{
                label: 'Invoices Generated',
                data: {{ chart_data.daily.data | tojson if chart_data else "[0, 0, 0, 0, 0, 0, 0]" | safe }},
                borderColor: 'rgba(0, 123, 255, 1)',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: 'rgba(0, 123, 255, 1)',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 5
                    }
                }
            }
        }
    });

    // Order Status Distribution Chart
    const statusCtx = document.getElementById('orderStatusChart').getContext('2d');

    statusChart = new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: {{ chart_data.status.labels | tojson if chart_data else "['Pending', 'Generated', 'On Hold', 'Dispatched']" | safe }},
            datasets: [{
                data: {{ chart_data.status.data | tojson if chart_data else "[0, 0, 0, 0]" | safe }},
                backgroundColor: [
                    'rgba(255, 193, 7, 0.8)',
                    'rgba(40, 167, 69, 0.8)',
                    'rgba(220, 53, 69, 0.8)',
                    'rgba(0, 123, 255, 0.8)'
                ],
                borderColor: [
                    'rgba(255, 193, 7, 1)',
                    'rgba(40, 167, 69, 1)',
                    'rgba(220, 53, 69, 1)',
                    'rgba(0, 123, 255, 1)'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 15,
                        usePointStyle: true
                    }
                }
            }
        }
    });
}

function showInvoiceChart(period) {
    // Update active button
    document.querySelectorAll('.btn-group .btn').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');

    if (invoiceChart) {
        invoiceChart.destroy();
    }

    const ctx = document.getElementById('invoiceGenerationChart').getContext('2d');
    let chartData, chartLabels;

    switch(period) {
        case 'daily':
            chartLabels = {{ chart_data.daily.labels | tojson if chart_data else "['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']" | safe }};
            chartData = {{ chart_data.daily.data | tojson if chart_data else "[0, 0, 0, 0, 0, 0, 0]" | safe }};
            break;
        case 'weekly':
            chartLabels = {{ chart_data.weekly.labels | tojson if chart_data else "['Week 1', 'Week 2', 'Week 3', 'Week 4']" | safe }};
            chartData = {{ chart_data.weekly.data | tojson if chart_data else "[0, 0, 0, 0]" | safe }};
            break;
    }

    invoiceChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: chartLabels,
            datasets: [{
                label: 'Invoices Generated',
                data: chartData,
                borderColor: 'rgba(0, 123, 255, 1)',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: 'rgba(0, 123, 255, 1)',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: period === 'daily' ? 5 : 20
                    }
                }
            }
        }
    });
}

function getCurrentUser() {
    // Get current user from session or template variable
    return '{{ current_user.username if current_user else "system" }}';
}

// Initialize charts on page load
document.addEventListener('DOMContentLoaded', function() {
    initializeInvoiceCharts();
    initializeBreakdownCharts();

    // Add keyboard shortcuts for better UX
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey || e.metaKey) {
            switch(e.key) {
                case 'r':
                    e.preventDefault();
                    refreshAllData();
                    showToast('Data refreshed', 'success');
                    break;
                case 'e':
                    e.preventDefault();
                    if (currentChartType) {
                        exportChart(currentChartType, 'png');
                        showToast('Chart exported', 'success');
                    }
                    break;
                case '1':
                    e.preventDefault();
                    showChart('division');
                    break;
                case '2':
                    e.preventDefault();
                    showChart('aging');
                    break;
                case '3':
                    e.preventDefault();
                    showChart('customer');
                    break;
            }
        }
    });

    // Add tooltips for better UX
    addEnhancedTooltips();
});

function addEnhancedTooltips() {
    // Add tooltips to chart buttons with keyboard shortcuts
    const buttons = document.querySelectorAll('.btn-group .btn');
    const shortcuts = ['Ctrl+1', 'Ctrl+2', 'Ctrl+3'];
    const chartNames = ['Division Analysis', 'Aging Analysis', 'Customer Analysis'];

    buttons.forEach((btn, index) => {
        if (shortcuts[index]) {
            btn.title = `${chartNames[index]} (${shortcuts[index]})`;
            btn.setAttribute('data-bs-toggle', 'tooltip');
            btn.setAttribute('data-bs-placement', 'top');
        }
    });

    // Initialize Bootstrap tooltips
    if (typeof bootstrap !== 'undefined') {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
}

function showToast(message, type = 'info') {
    // Create toast notification
    const toastHtml = `
        <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : 'primary'} border-0" role="alert">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-${type === 'success' ? 'check' : 'info'}-circle me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;

    // Add to toast container or create one
    let toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toastContainer';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }

    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    // Show toast
    const toastElement = toastContainer.lastElementChild;
    const toast = new bootstrap.Toast(toastElement, { delay: 3000 });
    toast.show();

    // Remove after hiding
    toastElement.addEventListener('hidden.bs.toast', function() {
        toastElement.remove();
    });
}

// Enhanced KPI Card Functions
function filterByStatus(status) {
    // Add filter parameter to current URL
    const url = new URL(window.location);
    url.searchParams.set('status_filter', status);
    window.location.href = url.toString();
}

function showAmountBreakdown() {
    // Check if modal already exists and remove it
    const existingModal = document.getElementById('amountBreakdownModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Create and show amount breakdown modal
    const modal = document.createElement('div');
    modal.id = 'amountBreakdownModal';
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-chart-bar me-2"></i>Pending Amount Breakdown
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- Chart Type Selector -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="btn-group w-100" role="group">
                                <button type="button" class="btn btn-outline-primary active" onclick="showChart('division')">
                                    <i class="fas fa-building me-1"></i>Division Analysis
                                </button>
                                <button type="button" class="btn btn-outline-success" onclick="showChart('aging')">
                                    <i class="fas fa-clock me-1"></i>Aging Analysis
                                </button>
                                <button type="button" class="btn btn-outline-info" onclick="showChart('customer')">
                                    <i class="fas fa-users me-1"></i>Customer Analysis
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Chart Container -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <div id="chartContainer" style="height: 500px; width: 100%;">
                                        <div class="text-center py-5">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Loading chart...</span>
                                            </div>
                                            <p class="mt-2 text-muted">Loading advanced visualization...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Chart Information Panel -->
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <div>
                                            <strong>Interactive Charts:</strong>
                                            <span id="chartInfo">Click on chart segments to drill down and explore data in detail.</span>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <small class="text-muted me-3" id="lastRefreshTime">Last updated: --:--:--</small>
                                        <button class="btn btn-outline-primary btn-sm" onclick="refreshAllData()" title="Refresh Data">
                                            <i class="fas fa-sync-alt"></i>
                                        </button>
                                    </div>
                                </div>
                                <div id="chartHoverInfo" style="display: none; margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 5px; border-left: 4px solid #007bff;">
                                    <!-- Hover information will be displayed here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <div class="d-flex justify-content-between w-100">
                        <div>
                            <button type="button" class="btn btn-outline-info" onclick="toggleFullscreen()">
                                <i class="fas fa-expand me-1"></i>Fullscreen
                            </button>
                            <button type="button" class="btn btn-outline-success" onclick="refreshChartData()">
                                <i class="fas fa-sync-alt me-1"></i>Refresh
                            </button>
                        </div>
                        <div>
                            <button type="button" class="btn btn-primary" onclick="exportBreakdown()">
                                <i class="fas fa-download me-1"></i>Export
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="closeBreakdownModal()">
                                <i class="fas fa-times me-1"></i>Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);

    // Show modal
    const bsModal = new bootstrap.Modal(modal);
    window.currentBreakdownModal = bsModal; // Store reference for manual closing
    bsModal.show();

    // Initialize charts (placeholder data)
    setTimeout(() => {
        initializeBreakdownCharts();
    }, 500);

    // Clean up when modal is closed
    modal.addEventListener('hidden.bs.modal', () => {
        if (modal.parentNode) {
            document.body.removeChild(modal);
        }
        window.currentBreakdownModal = null;
    });
}

function closeBreakdownModal() {
    if (window.currentBreakdownModal) {
        window.currentBreakdownModal.hide();
    }
}

function exportBreakdown() {
    // Export current chart data
    const chartType = currentChartType;
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    const filename = `finance_${chartType}_analysis_${timestamp}`;

    // Get chart container
    const container = document.getElementById('chartContainer');

    if (container && container.querySelector('.plotly')) {
        // Export as PNG using Plotly's built-in export
        Plotly.downloadImage(container, {
            format: 'png',
            width: 1200,
            height: 800,
            filename: filename
        });
    } else {
        alert('No chart available for export');
    }
}

function toggleFullscreen() {
    const modal = document.getElementById('amountBreakdownModal');
    const modalDialog = modal.querySelector('.modal-dialog');

    if (modalDialog.classList.contains('modal-fullscreen')) {
        modalDialog.classList.remove('modal-fullscreen');
        modalDialog.classList.add('modal-lg');
    } else {
        modalDialog.classList.remove('modal-lg');
        modalDialog.classList.add('modal-fullscreen');
    }

    // Resize chart after modal size change
    setTimeout(() => {
        const container = document.getElementById('chartContainer');
        if (container && container.querySelector('.plotly')) {
            Plotly.Plots.resize(container);
        }
    }, 300);
}

function refreshChartData() {
    showChart(currentChartType);
}

// Add click event handlers for drill-down functionality
function addChartInteractivity(container) {
    container.on('plotly_click', function(data) {
        if (data.points && data.points.length > 0) {
            const point = data.points[0];
            handleChartClick(point);
        }
    });

    container.on('plotly_hover', function(data) {
        if (data.points && data.points.length > 0) {
            const point = data.points[0];
            showDetailedTooltip(point);
        }
    });
}

function handleChartClick(point) {
    const label = point.label;
    const value = point.value;

    // Show detailed information modal or drill down
    if (currentChartType === 'division' && label !== 'Total') {
        showDivisionDetails(label, value);
    } else if (currentChartType === 'customer' && point.parent !== '') {
        showCustomerDetails(label, value);
    } else if (currentChartType === 'aging') {
        showAgingDetails(label, value);
    }
}

function showDivisionDetails(division, amount) {
    const detailModal = `
        <div class="modal fade" id="divisionDetailModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-building me-2"></i>${division} - Detailed Analysis
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h3 class="text-primary">Rs.${amount.toLocaleString()}</h3>
                                        <p class="text-muted">Total Pending Amount</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h6>Quick Actions:</h6>
                                        <button class="btn btn-sm btn-outline-primary me-2" onclick="viewDivisionLedger('${division}')">
                                            <i class="fas fa-book me-1"></i>View Ledger
                                        </button>
                                        <button class="btn btn-sm btn-outline-success" onclick="generateDivisionReport('${division}')">
                                            <i class="fas fa-file-alt me-1"></i>Generate Report
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('divisionDetailModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add new modal
    document.body.insertAdjacentHTML('beforeend', detailModal);
    const modal = new bootstrap.Modal(document.getElementById('divisionDetailModal'));
    modal.show();
}

function showCustomerDetails(customer, amount) {
    // Similar implementation for customer details
    alert(`Customer Details: ${customer} - Rs.${amount.toLocaleString()}\n\nDetailed customer analysis will be shown here.`);
}

function showAgingDetails(label, amount) {
    // Similar implementation for aging details
    alert(`Aging Details: ${label} - Rs.${amount.toLocaleString()}\n\nDetailed aging analysis will be shown here.`);
}

function generateDivisionReport(division) {
    window.open(`/finance/division-ledger?division=${encodeURIComponent(division)}`, '_blank');
}

function showDetailedTooltip(point) {
    // Enhanced tooltip functionality can be added here
    console.log('Detailed tooltip for:', point);
}

// Real-time comment refresh functionality
function refreshComments() {
    const orderCards = document.querySelectorAll('[id^="comments-"]');
    orderCards.forEach(card => {
        const orderId = card.id.replace('comments-', '');
        fetchOrderComments(orderId);
    });
}

function fetchOrderComments(orderId) {
    fetch(`/api/orders/${orderId}/comments`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateCommentsDisplay(orderId, data.comments);
                updateLastUpdateTime(orderId, data.last_updated);
            }
        })
        .catch(error => {
            console.log(`Error fetching comments for ${orderId}:`, error);
        });
}

function updateCommentsDisplay(orderId, comments) {
    const commentCountElement = document.getElementById(`comment-count-${orderId}`);
    if (commentCountElement) {
        const totalComments = (comments.notes ? 1 : 0) +
                            (comments.hold_comments ? 1 : 0) +
                            (comments.release_comments ? 1 : 0);
        commentCountElement.textContent = totalComments;
    }
}

function updateLastUpdateTime(orderId, lastUpdated) {
    const lastUpdateElement = document.getElementById(`last-update-${orderId}`);
    if (lastUpdateElement && lastUpdated) {
        lastUpdateElement.textContent = lastUpdated.substring(0, 16);
    }
}

// Auto-refresh comments every 30 seconds
setInterval(refreshComments, 30000);

function showTodayInvoices() {
    // Navigate to invoices with today's filter
    const today = new Date().toISOString().split('T')[0];
    window.location.href = `/finance/invoices?date_filter=${today}`;
}

// Chart System
let currentChartType = 'division';
let chartInstances = {};

function initializeBreakdownCharts() {
    // Initialize with placeholder data
    showChart('division');
}

function showChart(chartType) {
    // Check if chart container exists
    const container = document.getElementById('chartContainer');
    if (!container) {
        console.error('Chart container not found');
        return;
    }

    currentChartType = chartType;

    // Update button states with smooth transition
    document.querySelectorAll('.btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
        btn.style.transition = 'all 0.3s ease';
    });

    const activeButton = event?.target || document.querySelector(`[onclick="showChart('${chartType}')"]`);
    if (activeButton) {
        activeButton.classList.add('active');
    }

    // Show enhanced loading state with progress
    showLoadingState(chartType);

    // Load chart data and render
    renderAdvancedChart(chartType, getSampleData(chartType));
}

function getSampleData(chartType) {
    // Return sample data for demonstration
    switch(chartType) {
        case 'division':
            return {
                divisions: ['General', 'Aqvida', 'Pharma', 'Surgical'],
                amounts: [250000, 180000, 120000, 80000],
                order_counts: [15, 12, 8, 5],
                total_amount: 630000,
                total_orders: 40
            };
        case 'aging':
            return [
                {aging_bucket: '0-30 days', order_count: 15, amount: 250000},
                {aging_bucket: '31-60 days', order_count: 10, amount: 180000},
                {aging_bucket: '61-90 days', order_count: 8, amount: 120000},
                {aging_bucket: '90+ days', order_count: 5, amount: 80000}
            ];
        case 'customer':
            return [
                {customer: 'Dr Col Umair', division: 'General', order_count: 5, amount: 125000},
                {customer: 'ABC Hospital', division: 'Pharma', order_count: 3, amount: 95000},
                {customer: 'XYZ Clinic', division: 'Surgical', order_count: 2, amount: 75000}
            ];
        default:
            return {};
    }
}

function showLoadingState(chartType) {
    const container = document.getElementById('chartContainer');
    if (!container) {
        console.error('Chart container not found for loading state');
        return;
    }

    const chartNames = {
        'division': 'Division Analysis',
        'aging': 'Aging Analysis',
        'customer': 'Customer Analysis'
    };

    container.innerHTML = `
        <div class="text-center py-5">
            <div class="position-relative mb-3">
                <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <div class="progress mt-3" style="height: 6px;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                         role="progressbar" style="width: 0%" id="loadingProgress"></div>
                </div>
            </div>
            <h6 class="text-muted mb-2">Loading ${chartNames[chartType] || 'Chart'}</h6>
            <small class="text-muted">Fetching latest data...</small>
        </div>
    `;

    // Animate progress bar
    animateProgressBar();
}

function animateProgressBar() {
    const progressBar = document.getElementById('loadingProgress');
    if (!progressBar) return;

    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 30;
        if (progress > 90) progress = 90;
        progressBar.style.width = progress + '%';

        if (progress >= 90) {
            clearInterval(interval);
        }
    }, 200);
}

async function loadChartData(chartType) {
    try {
        // Use the working comprehensive chart data endpoint
        const response = await fetch('/finance/api/chart-data-test');
        const allData = await response.json();

        // Transform the data based on chart type
        switch(chartType) {
            case 'division':
                return {
                    success: true,
                    data: {
                        divisions: allData.division_performance.labels,
                        amounts: allData.division_performance.data,
                        order_counts: allData.division_performance.order_counts,
                        total_amount: allData.division_performance.data.reduce((a, b) => a + b, 0),
                        total_orders: allData.division_performance.order_counts.reduce((a, b) => a + b, 0)
                    }
                };
            case 'aging':
                // Create aging data from monthly revenue (simplified)
                return {
                    success: true,
                    data: [
                        {aging_bucket: '0-30 days', order_count: 15, amount: 250000, path: ['Total', '0-30 days']},
                        {aging_bucket: '31-60 days', order_count: 10, amount: 180000, path: ['Total', '31-60 days']},
                        {aging_bucket: '61-90 days', order_count: 8, amount: 120000, path: ['Total', '61-90 days']},
                        {aging_bucket: '90+ days', order_count: 5, amount: 80000, path: ['Total', '90+ days']}
                    ]
                };
            case 'customer':
                return {
                    success: true,
                    data: allData.customer_revenue.labels.slice(0, 10).map((customer, index) => ({
                        customer: customer,
                        division: 'General',
                        order_count: allData.customer_revenue.order_counts[index],
                        amount: allData.customer_revenue.data[index],
                        avg_days: 30,
                        path: ['Total', 'General', customer]
                    }))
                };
            default:
                return {success: false, error: 'Unknown chart type'};
        }
    } catch (error) {
        console.error('Error loading chart data:', error);
        return {success: false, error: error.message};
    }
}






function renderAdvancedChart(chartType, data) {
    const container = document.getElementById('chartContainer');

    // Clear any existing chart
    if (chartInstances[chartType]) {
        Plotly.purge(container);
        delete chartInstances[chartType];
    }

    // Display placeholder for chart
    container.innerHTML = `
        <div class="text-center py-5">
            <i class="fas fa-chart-bar fa-3x text-primary mb-3"></i>
            <h5 class="text-muted">Chart: ${chartType.charAt(0).toUpperCase() + chartType.slice(1)} Analysis</h5>
            <p class="text-muted">Chart functionality will be implemented</p>
            <div class="mt-3">
                <small class="text-muted">Sample data: ${JSON.stringify(data).substring(0, 100)}...</small>
            </div>
        </div>
    `;

    // Store chart instance
    chartInstances[chartType] = container;
}

function showChartError(message, chartType) {
    const container = document.getElementById('chartContainer');
    container.innerHTML = `
        <div class="text-center py-5">
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Chart Loading Error</strong>
                <p class="mb-3">${message}</p>
                <button class="btn btn-outline-danger btn-sm" onclick="showChart('${chartType}')">
                    <i class="fas fa-redo me-1"></i>Retry
                </button>
            </div>
        </div>
    `;
}







function showChartError(error) {
    const container = document.getElementById('chartContainer');
    container.innerHTML = `
        <div class="text-center py-5">
            <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
            <h5 class="text-muted">Chart Loading Error</h5>
            <p class="text-muted">${error}</p>
            <button class="btn btn-primary" onclick="showChart(currentChartType)">
                <i class="fas fa-retry me-1"></i>Retry
            </button>
        </div>
    `;
}

function exportBreakdown() {
    // Export breakdown data
    alert('Export functionality will be implemented');
}
</script>

{% endblock %}
