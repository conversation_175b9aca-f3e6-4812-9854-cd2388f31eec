{% extends "base.html" %}

{% block title %}Invoice Generation - Medivent ERP{% endblock %}

{% block head %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
{% endblock %}

{% block content %}
<style>
    /* Enhanced Invoice Generation Styles - Matching Reference Image */
    .invoice-dashboard {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .dashboard-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 20px;
    }

    /* Header Section */
    .invoice-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .invoice-title {
        color: white;
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 8px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .invoice-subtitle {
        color: rgba(255, 255, 255, 0.9);
        font-size: 1rem;
        margin-bottom: 0;
    }

    /* Statistics Cards */
    .stats-cards {
        margin-bottom: 25px;
    }

    .stats-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 15px;
        box-shadow: 0 3px 10px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        height: 120px;
        display: flex;
        align-items: center;
    }

    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.12);
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        margin-right: 20px;
    }

    .stats-content h3 {
        font-size: 1.8rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 5px;
    }

    .stats-content p {
        color: #7f8c8d;
        font-size: 0.9rem;
        margin: 0;
    }

    /* Filter Section */
    .filter-section {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .filter-title {
        color: #2c3e50;
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }

    .filter-title i {
        margin-right: 10px;
        color: #3498db;
    }

    .filter-row {
        margin-bottom: 15px;
    }

    .filter-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 5px;
        display: block;
    }

    .form-control, .form-select {
        border-radius: 8px;
        border: 1px solid #ddd;
        padding: 10px 15px;
        font-size: 0.9rem;
    }

    .form-control:focus, .form-select:focus {
        border-color: #3498db;
        box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    }

    .btn-filter {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 10px 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-filter:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        color: white;
    }

    .btn-generate {
        background: linear-gradient(135deg, #27ae60, #229954);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 10px 25px;
        font-weight: 600;
        margin-left: 10px;
        transition: all 0.3s ease;
    }

    .btn-generate:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
        color: white;
    }
    
    /* Chart Section */
    .chart-section {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .chart-title {
        color: #2c3e50;
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }

    .chart-title i {
        margin-right: 10px;
        color: #3498db;
    }

    .chart-container {
        position: relative;
        height: 300px;
        margin-bottom: 20px;
    }

    /* Invoice Table */
    .invoice-table {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .table-title {
        color: #2c3e50;
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .table-title i {
        margin-right: 10px;
        color: #3498db;
    }

    .table-modern {
        border: none;
        border-radius: 10px;
        overflow: hidden;
    }

    .table-modern thead {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
    }

    .table-modern thead th {
        border: none;
        padding: 15px;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .table-modern tbody td {
        border: none;
        padding: 12px 15px;
        vertical-align: middle;
        border-bottom: 1px solid #ecf0f1;
    }

    .table-modern tbody tr:hover {
        background: #f8f9fa;
    }

    .status-badge {
        padding: 4px 10px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
        text-transform: uppercase;
    }

    .status-pending {
        background: #fef9e7;
        color: #f39c12;
        border: 1px solid #f39c12;
    }

    .status-generated {
        background: #d5f4e6;
        color: #27ae60;
        border: 1px solid #27ae60;
    }

    .status-sent {
        background: #e3f2fd;
        color: #3498db;
        border: 1px solid #3498db;
    }

    .btn-action {
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 0.8rem;
        font-weight: 500;
        margin: 0 2px;
        transition: all 0.3s ease;
        border: none;
    }

    .btn-generate-invoice {
        background: #27ae60;
        color: white;
    }

    .btn-generate-invoice:hover {
        background: #229954;
        transform: translateY(-1px);
        color: white;
    }

    .btn-view {
        background: #3498db;
        color: white;
    }

    .btn-view:hover {
        background: #2980b9;
        transform: translateY(-1px);
        color: white;
    }

    .btn-send {
        background: #9b59b6;
        color: white;
    }

    .btn-send:hover {
        background: #8e44ad;
        transform: translateY(-1px);
        color: white;
    }

    /* Modal Styles */
    .invoice-modal .modal-content {
        border-radius: 15px;
        border: none;
    }
    
    .invoice-modal .modal-header {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    
    /* Responsive Design */
    @media (max-width: 768px) {
        .invoice-title {
            font-size: 1.5rem;
        }
        
        .stats-card {
            height: auto;
            padding: 15px;
        }
        
        .stats-icon {
            width: 50px;
            height: 50px;
            font-size: 20px;
        }
        
        .stats-content h3 {
            font-size: 1.4rem;
        }
        
        .chart-container {
            height: 250px;
        }
        
        .table-modern {
            font-size: 0.85rem;
        }
        
        .btn-action {
            padding: 4px 8px;
            font-size: 0.75rem;
        }
    }
</style>

<div class="invoice-dashboard">
    <div class="dashboard-container">
        <!-- Header -->
        <div class="invoice-header">
            <h1 class="invoice-title">
                <i class="fas fa-file-invoice mr-3"></i>Invoice Generation
            </h1>
            <p class="invoice-subtitle">Generate and manage invoices with advanced analytics</p>
        </div>

        <!-- Statistics Cards -->
        <div class="row stats-cards">
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-icon" style="background: linear-gradient(135deg, #27ae60, #2ecc71);">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div class="stats-content">
                        <h3 id="totalInvoices">0</h3>
                        <p>Total Invoices</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-icon" style="background: linear-gradient(135deg, #3498db, #5dade2);">
                        <i class="fas fa-rupee-sign"></i>
                    </div>
                    <div class="stats-content">
                        <h3 id="totalAmount">Rs.0</h3>
                        <p>Total Amount</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-icon" style="background: linear-gradient(135deg, #f39c12, #f1c40f);">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stats-content">
                        <h3 id="pendingInvoices">0</h3>
                        <p>Pending Invoices</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-icon" style="background: linear-gradient(135deg, #e74c3c, #c0392b);">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stats-content">
                        <h3 id="overdueInvoices">0</h3>
                        <p>Overdue Invoices</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="filter-section">
            <div class="filter-title">
                <i class="fas fa-filter"></i>Invoice Filters
            </div>
            <form id="invoiceFilters">
                <div class="row">
                    <div class="col-md-3">
                        <div class="filter-row">
                            <label class="filter-label">Date Range</label>
                            <select class="form-select" id="dateRange">
                                <option value="7">Last 7 Days</option>
                                <option value="30" selected>Last 30 Days</option>
                                <option value="90">Last 90 Days</option>
                                <option value="custom">Custom Range</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="filter-row">
                            <label class="filter-label">Invoice Status</label>
                            <select class="form-select" id="invoiceStatus">
                                <option value="all">All Status</option>
                                <option value="pending">Pending</option>
                                <option value="generated">Generated</option>
                                <option value="sent">Sent</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="filter-row">
                            <label class="filter-label">Customer</label>
                            <select class="form-select" id="customerFilter">
                                <option value="all">All Customers</option>
                                <!-- Customer options will be populated dynamically -->
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="filter-row">
                            <label class="filter-label">&nbsp;</label>
                            <div>
                                <button type="button" class="btn btn-filter" onclick="filterInvoices()">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <button type="button" class="btn btn-generate" onclick="showGenerateModal()">
                                    <i class="fas fa-plus"></i> Generate
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Charts Section -->
        <div class="row">
            <div class="col-lg-6">
                <div class="chart-section">
                    <div class="chart-title">
                        <i class="fas fa-chart-line"></i>Invoice Trends
                    </div>
                    <div class="chart-container">
                        <canvas id="invoiceTrendChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="chart-section">
                    <div class="chart-title">
                        <i class="fas fa-chart-pie"></i>Invoice Status Distribution
                    </div>
                    <div class="chart-container">
                        <canvas id="invoiceStatusChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Invoice Table -->
        <div class="invoice-table">
            <div class="table-title">
                <div>
                    <i class="fas fa-list"></i>Invoice Management
                </div>
                <div>
                    <button class="btn btn-generate" onclick="showGenerateModal()">
                        <i class="fas fa-plus"></i> New Invoice
                    </button>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table table-modern">
                    <thead>
                        <tr>
                            <th>Invoice #</th>
                            <th>Customer</th>
                            <th>Order ID</th>
                            <th>Amount</th>
                            <th>Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="invoiceTableBody">
                        {% if invoices and invoices|length > 0 %}
                            {% for invoice in invoices %}
                            <tr>
                                <td>
                                    <strong>{{ invoice.invoice_number or 'INV-' + invoice.id|string }}</strong>
                                </td>
                                <td>
                                    <strong>{{ invoice.customer_name }}</strong><br>
                                    {% if invoice.customer_phone %}
                                    <small class="text-muted">Contact: {{ invoice.customer_phone }}</small>
                                    {% endif %}
                                </td>
                                <td>{{ invoice.order_id }}</td>
                                <td>Rs.{{ "{:,.0f}".format(invoice.amount) }}</td>
                                <td>{{ invoice.created_at.strftime('%Y-%m-%d') if invoice.created_at else 'N/A' }}</td>
                                <td>
                                    {% if invoice.status == 'generated' %}
                                    <span class="status-badge status-generated">Generated</span>
                                    {% elif invoice.status == 'sent' %}
                                    <span class="status-badge status-sent">Sent</span>
                                    {% elif invoice.status == 'paid' %}
                                    <span class="status-badge status-paid">Paid</span>
                                    {% else %}
                                    <span class="status-badge status-pending">Pending</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <button class="btn btn-action btn-view" onclick="viewInvoice('{{ invoice.invoice_number or invoice.id }}')">
                                        <i class="fas fa-eye"></i> View
                                    </button>
                                    {% if invoice.status != 'sent' and invoice.status != 'paid' %}
                                    <button class="btn btn-action btn-send" onclick="sendInvoice('{{ invoice.invoice_number or invoice.id }}')">
                                        <i class="fas fa-paper-plane"></i> Send
                                    </button>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="7" class="text-center text-muted py-4">
                                    <i class="fas fa-file-invoice fa-3x mb-3 d-block"></i>
                                    <h5>No Invoices Found</h5>
                                    <p>No invoices have been generated yet. Create your first invoice by clicking "New Invoice" above.</p>
                                </td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Invoice Generation Modal -->
<div class="modal fade invoice-modal" id="invoiceModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-file-invoice me-2"></i>Generate Invoice
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="invoiceForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Order ID</label>
                                <input type="text" class="form-control" id="invoiceOrderId" readonly>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Customer</label>
                                <input type="text" class="form-control" id="invoiceCustomer" readonly>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Order Amount</label>
                                <input type="text" class="form-control" id="orderAmount" readonly>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Invoice Date</label>
                                <input type="date" class="form-control" id="invoiceDate" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Due Date</label>
                                <input type="date" class="form-control" id="dueDate" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Tax Rate (%)</label>
                                <input type="number" class="form-control" id="taxRate" value="18" min="0" max="100">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Invoice Notes</label>
                        <textarea class="form-control" id="invoiceNotes" rows="3" placeholder="Additional notes for the invoice..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-generate" onclick="submitInvoice()">
                    <i class="fas fa-save"></i> Generate Invoice
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Initialize charts and functionality
document.addEventListener('DOMContentLoaded', function() {
    // Set today's date as default
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('invoiceDate').value = today;

    // Set due date to 30 days from today
    const dueDate = new Date();
    dueDate.setDate(dueDate.getDate() + 30);
    document.getElementById('dueDate').value = dueDate.toISOString().split('T')[0];

    // Load initial data
    loadInvoiceData();
    initializeCharts();
});

function loadInvoiceData() {
    // Load data from database
    document.getElementById('totalInvoices').textContent = '0';
    document.getElementById('totalAmount').textContent = 'Rs.0';
    document.getElementById('pendingInvoices').textContent = '0';
    document.getElementById('overdueInvoices').textContent = '0';
}

function initializeCharts() {
    // Invoice Trend Chart
    const trendCtx = document.getElementById('invoiceTrendChart').getContext('2d');
    new Chart(trendCtx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'Generated',
                data: [15, 20, 18, 25, 22, 28],
                borderColor: '#27ae60',
                backgroundColor: 'rgba(39, 174, 96, 0.1)',
                tension: 0.4,
                fill: true
            }, {
                label: 'Pending',
                data: [8, 12, 10, 15, 11, 9],
                borderColor: '#f39c12',
                backgroundColor: 'rgba(243, 156, 18, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top'
                }
            }
        }
    });

    // Invoice Status Chart
    const statusCtx = document.getElementById('invoiceStatusChart').getContext('2d');
    new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: ['Generated', 'Sent', 'Pending', 'Overdue'],
            datasets: [{
                data: [45, 30, 20, 5],
                backgroundColor: [
                    '#27ae60',
                    '#3498db',
                    '#f39c12',
                    '#e74c3c'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

function filterInvoices() {
    const dateRange = document.getElementById('dateRange').value;
    const status = document.getElementById('invoiceStatus').value;
    const customer = document.getElementById('customerFilter').value;

    console.log('Filtering invoices:', { dateRange, status, customer });

    // Show loading state
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Filtering...';
    btn.disabled = true;

    // Simulate filtering
    setTimeout(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
        alert('Invoices filtered successfully!');
    }, 1500);
}

function showGenerateModal() {
    const modal = new bootstrap.Modal(document.getElementById('invoiceModal'));
    modal.show();
}

function generateInvoice(orderId) {
    // Populate modal with order data - fetch from database
    document.getElementById('invoiceOrderId').value = orderId;

    // Fetch order details from database
    fetch(`/api/orders/${orderId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('invoiceCustomer').value = data.customer_name || '';
                document.getElementById('orderAmount').value = `Rs.${data.order_amount || 0}`;
            }
        })
        .catch(error => {
            console.error('Error fetching order details:', error);
            // Set default values if fetch fails
            document.getElementById('invoiceCustomer').value = '';
            document.getElementById('orderAmount').value = 'Rs.0';
        });

    showGenerateModal();
}

function viewInvoice(invoiceId) {
    alert(`Viewing invoice ${invoiceId}`);
}

function viewOrder(orderId) {
    alert(`Viewing order ${orderId}`);
}

function sendInvoice(invoiceId) {
    alert(`Sending invoice ${invoiceId} to customer`);
}

function submitInvoice() {
    const form = document.getElementById('invoiceForm');
    if (form.checkValidity()) {
        // Show loading state
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';
        btn.disabled = true;

        // Simulate invoice generation
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
            alert('Invoice generated successfully!');

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('invoiceModal'));
            modal.hide();

            // Reset form
            form.reset();
        }, 2000);
    } else {
        form.reportValidity();
    }
}
</script>

{% endblock %}
