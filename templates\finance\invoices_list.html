<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Finance Invoices - Medivent ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .finance-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
        }
        
        .finance-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .finance-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: none;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
        }
        
        .stat-label {
            color: #7f8c8d;
            font-weight: 500;
            margin-top: 0.5rem;
        }
        
        .invoice-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #007bff;
        }
        
        .invoice-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .invoice-number {
            font-size: 1.2rem;
            font-weight: 700;
            color: #2c3e50;
        }
        
        .invoice-amount {
            font-size: 1.1rem;
            font-weight: 600;
            color: #27ae60;
        }
        
        .filter-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .btn-modern {
            border-radius: 8px;
            padding: 0.5rem 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .status-generated {
            background-color: #e3f2fd;
            color: #1976d2;
        }
        
        .status-paid {
            background-color: #e8f5e8;
            color: #2e7d32;
        }
    </style>
</head>
<body style="background-color: #f8f9fa;">
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="finance-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="finance-title">
                        <i class="fas fa-file-invoice me-3"></i>Finance Invoices
                    </h1>
                    <p class="finance-subtitle">Comprehensive invoice management and tracking</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-light btn-modern" onclick="location.reload()">
                        <i class="fas fa-sync-alt me-2"></i>Refresh
                    </button>
                    <button class="btn btn-light btn-modern" onclick="window.history.back()">
                        <i class="fas fa-arrow-left me-2"></i>Back
                    </button>
                </div>
            </div>
        </div>

        <!-- Summary Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <div class="stat-value">{{ summary.total_invoices }}</div>
                    <div class="stat-label">Total Invoices</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <div class="stat-value">{{ summary.today_invoices }}</div>
                    <div class="stat-label">Generated Today</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <div class="stat-value">Rs.{{ "{:,.0f}".format(summary.total_amount) }}</div>
                    <div class="stat-label">Total Amount</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <div class="stat-value">Rs.{{ "{:,.0f}".format(summary.pending_amount) }}</div>
                    <div class="stat-label">Pending Amount</div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filter-card">
            <h5 class="mb-3">
                <i class="fas fa-filter me-2 text-primary"></i>Invoice Filters
            </h5>
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label fw-bold">Date</label>
                    <input type="date" class="form-control" name="date_filter" 
                           value="{{ filters.date_filter if filters else '' }}">
                </div>
                <div class="col-md-3">
                    <label class="form-label fw-bold">Customer</label>
                    <input type="text" class="form-control" name="customer_filter" 
                           value="{{ filters.customer_filter if filters else '' }}" placeholder="Customer name">
                </div>
                <div class="col-md-3">
                    <label class="form-label fw-bold">Status</label>
                    <select class="form-control" name="status_filter">
                        <option value="all" {{ 'selected' if filters and filters.status_filter == 'all' else '' }}>All Status</option>
                        <option value="Generated" {{ 'selected' if filters and filters.status_filter == 'Generated' else '' }}>Generated</option>
                        <option value="Paid" {{ 'selected' if filters and filters.status_filter == 'Paid' else '' }}>Paid</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary btn-modern me-2">
                        <i class="fas fa-search me-1"></i>Filter
                    </button>
                    <a href="/finance/invoices" class="btn btn-outline-secondary btn-modern">
                        <i class="fas fa-times me-1"></i>Clear
                    </a>
                </div>
            </form>
        </div>

        <!-- Invoices List -->
        <div class="row">
            {% if invoices %}
                {% for invoice in invoices %}
                <div class="col-12">
                    <div class="invoice-card">
                        <div class="invoice-header">
                            <div class="d-flex justify-content-between align-items-center w-100">
                                <div>
                                    <div class="invoice-number">{{ invoice.invoice_number }}</div>
                                    <div class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>{{ invoice.date_generated[:10] if invoice.date_generated else 'N/A' }}
                                        <span class="ms-3">
                                            <i class="fas fa-user me-1"></i>{{ invoice.customer_name or 'Unknown Customer' }}
                                        </span>
                                        <span class="ms-3">
                                            <i class="fas fa-receipt me-1"></i>{{ invoice.order_id }}
                                        </span>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <div class="invoice-amount">Rs.{{ "{:,.0f}".format(invoice.total_amount or 0) }}</div>
                                    <div class="mt-1">
                                        <span class="status-badge status-{{ 'paid' if invoice.status == 'Paid' else 'generated' }}">
                                            {{ invoice.status or 'Generated' }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button class="btn btn-primary btn-sm btn-modern" onclick="viewInvoice('{{ invoice.order_id }}')">
                                <i class="fas fa-eye me-1"></i>View Invoice
                            </button>
                            <button class="btn btn-info btn-sm btn-modern" onclick="viewOrder('{{ invoice.order_id }}')">
                                <i class="fas fa-file-alt me-1"></i>View Order
                            </button>
                            {% if invoice.customer_name %}
                            <button class="btn btn-success btn-sm btn-modern" onclick="viewCustomerLedger('{{ invoice.customer_name }}')">
                                <i class="fas fa-book me-1"></i>Customer Ledger
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">No invoices found</h4>
                        <p class="text-muted">Try adjusting your filters or check back later.</p>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function viewInvoice(orderId) {
            window.open(`/orders/${orderId}`, '_blank');
        }
        
        function viewOrder(orderId) {
            window.open(`/orders/${orderId}`, '_blank');
        }
        
        function viewCustomerLedger(customerName) {
            window.open(`/finance/customer-ledger?customer=${encodeURIComponent(customerName)}`, '_blank');
        }
    </script>
</body>
</html>
