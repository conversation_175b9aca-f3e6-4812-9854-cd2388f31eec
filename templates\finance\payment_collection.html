{% extends "base.html" %}

{% block title %}Payment Collection - Finance{% endblock %}

{% block content %}
<style>
    .payment-collection {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .page-title {
        color: white;
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 5px;
    }
    
    .collection-container {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }
    
    .payment-card {
        background: white;
        border: 1px solid var(--border);
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
        transition: all 0.3s ease;
        position: relative;
    }
    
    .payment-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        border-color: var(--primary);
    }
    
    .payment-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 15px;
    }
    
    .order-id {
        font-weight: 700;
        color: var(--primary);
        font-size: 1.1rem;
    }
    
    .payment-amount {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--accent);
    }
    
    .customer-info {
        margin-bottom: 15px;
    }
    
    .customer-name {
        font-weight: 600;
        color: var(--text);
        margin-bottom: 5px;
    }
    
    .customer-phone {
        color: var(--text-light);
        font-size: 0.9rem;
    }
    
    .order-date {
        color: var(--text-light);
        font-size: 0.9rem;
    }
    
    .days-pending {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .days-recent {
        background: rgba(76, 175, 80, 0.1);
        color: var(--success);
    }
    
    .days-moderate {
        background: rgba(255, 193, 7, 0.1);
        color: var(--warning);
    }
    
    .days-overdue {
        background: rgba(244, 67, 54, 0.1);
        color: var(--error);
    }
    
    .action-buttons {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid var(--border);
    }
    
    .btn-modern {
        border-radius: 8px;
        padding: 8px 16px;
        font-weight: 500;
        border: none;
        transition: all 0.3s ease;
    }
    
    .btn-success-modern {
        background: linear-gradient(135deg, var(--success), #45a049);
        color: white;
    }
    
    .btn-info-modern {
        background: linear-gradient(135deg, #17a2b8, #138496);
        color: white;
    }
    
    .btn-warning-modern {
        background: linear-gradient(135deg, var(--warning), #e6ac00);
        color: white;
    }
</style>

<div class="payment-collection">
    <div class="container-fluid">
        <!-- Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="page-title">
                        <i class="fas fa-hand-holding-usd mr-3"></i>Payment Collection
                    </h1>
                    <p class="page-subtitle">Collect and process pending payments</p>
                </div>
                <div class="col-md-4 text-right">
                    <a href="{{ url_for('finance_dashboard') }}" class="btn btn-light btn-modern">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Collection Container -->
        <div class="collection-container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4 class="mb-0">
                    <i class="fas fa-list mr-2"></i>Pending Payments 
                    <span class="badge badge-warning">{{ payments|length }}</span>
                </h4>
                
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary btn-modern" onclick="refreshPayments()">
                        <i class="fas fa-sync-alt mr-2"></i>Refresh
                    </button>
                </div>
            </div>
            
            {% if payments %}
                <div class="row" id="payments-list">
                    {% for payment in payments %}
                    <div class="col-lg-6 col-xl-4 payment-item">
                        <div class="payment-card">
                            <div class="payment-header">
                                <div class="order-id">{{ payment.order_id }}</div>
                                <div class="payment-amount">₹{{ payment.order_amount|format_currency }}</div>
                            </div>
                            
                            <div class="customer-info">
                                <div class="customer-name">{{ payment.customer_name }}</div>
                                {% if payment.customer_phone %}
                                <div class="customer-phone">
                                    <i class="fas fa-phone mr-1"></i>{{ payment.customer_phone }}
                                </div>
                                {% endif %}
                            </div>
                            
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div class="order-date">
                                    <i class="fas fa-calendar mr-1"></i>{{ payment.order_date }}
                                </div>
                            </div>
                            
                            <div class="action-buttons">
                                <div class="d-flex justify-content-between">
                                    <button class="btn btn-success-modern btn-sm" 
                                            onclick="collectPayment('{{ payment.order_id }}', {{ payment.order_amount|safe_amount }}, '{{ payment.customer_name }}')">
                                        <i class="fas fa-money-bill-wave mr-1"></i>Collect
                                    </button>
                                    
                                    {% if payment.customer_phone %}
                                    <button class="btn btn-info-modern btn-sm" 
                                            onclick="callCustomer('{{ payment.customer_phone }}', '{{ payment.customer_name }}')">
                                        <i class="fas fa-phone mr-1"></i>Call
                                    </button>
                                    {% endif %}
                                    
                                    <button class="btn btn-warning-modern btn-sm" 
                                            onclick="sendReminder('{{ payment.order_id }}', '{{ payment.customer_name }}')">
                                        <i class="fas fa-bell mr-1"></i>Remind
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                    <h5>No Pending Payments</h5>
                    <p class="text-muted">All payments have been collected successfully!</p>
                    <a href="{{ url_for('finance_dashboard') }}" class="btn btn-primary-modern">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Payment Collection Modal -->
<div class="modal fade" id="collectionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Collect Payment</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ url_for('finance_process_payment') }}" method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" id="collection-order-id" name="order_id">
                    <input type="hidden" id="collection-customer-id" name="customer_id">

                    <div class="form-group">
                        <label>Customer</label>
                        <input type="text" class="form-control" id="collection-customer" readonly>
                    </div>

                    <div class="form-group">
                        <label>Order ID</label>
                        <input type="text" class="form-control" id="collection-order-display" readonly>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Total Amount</label>
                                <input type="text" class="form-control" id="collection-amount-display" readonly>
                                <input type="hidden" id="collection-amount-value" name="total_amount">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Payment Type</label>
                                <select class="form-control" name="payment_type" id="payment-type" onchange="togglePaymentAmount()">
                                    <option value="full">Full Payment</option>
                                    <option value="partial">Partial Payment</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-group" id="payment-amount-group" style="display: none;">
                        <label>Payment Amount <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" name="payment_amount" id="payment-amount"
                               step="0.01" placeholder="Enter payment amount">
                        <small class="form-text text-muted">Enter the amount being collected (less than total amount)</small>
                    </div>

                    <div class="form-group">
                        <label>Payment Method <span class="text-danger">*</span></label>
                        <select class="form-control" name="payment_method" id="payment-method" onchange="togglePaymentFields()" required>
                            <option value="">Select payment method...</option>
                            <option value="cash">Cash</option>
                            <option value="cheque">Cheque</option>
                            <option value="bank_transfer">Bank Transfer</option>
                            <option value="online">Online Payment</option>
                            <option value="card">Credit/Debit Card</option>
                        </select>
                    </div>

                    <!-- Cheque-specific fields -->
                    <div id="cheque-fields" style="display: none;">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Cheque Number</label>
                                    <input type="text" class="form-control" name="cheque_number" placeholder="Cheque number">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Cheque Date</label>
                                    <input type="date" class="form-control" name="cheque_date">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Bank transfer fields -->
                    <div id="bank-fields" style="display: none;">
                        <div class="form-group">
                            <label>Bank Details</label>
                            <input type="text" class="form-control" name="bank_details" placeholder="Bank name and details">
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Reference Number</label>
                        <input type="text" class="form-control" name="reference_number" placeholder="Transaction ID, cheque number, etc.">
                    </div>

                    <div class="form-group">
                        <label>Payment Proof Documents</label>
                        <input type="file" class="form-control-file" name="payment_attachments" multiple
                               accept="image/*,.pdf" id="payment-attachments">
                        <small class="form-text text-muted">
                            Upload cheque images, bank slips, receipts, or other payment proof documents (Max 5 files, 10MB each)
                        </small>
                    </div>

                    <div class="form-group">
                        <label>Payment Notes</label>
                        <textarea class="form-control" name="payment_notes" rows="3"
                                  placeholder="Additional notes about this payment..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success-modern">
                        <i class="fas fa-check mr-2"></i>Collect Payment
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function collectPayment(orderId, amount, customerName, customerId) {
    document.getElementById('collection-order-id').value = orderId;
    document.getElementById('collection-customer-id').value = customerId || '';
    document.getElementById('collection-customer').value = customerName;
    document.getElementById('collection-order-display').value = orderId;
    document.getElementById('collection-amount-display').value = '₹' + amount.toLocaleString();
    document.getElementById('collection-amount-value').value = amount;
    $('#collectionModal').modal('show');
}

function togglePaymentAmount() {
    const paymentType = document.getElementById('payment-type').value;
    const amountGroup = document.getElementById('payment-amount-group');
    const amountInput = document.getElementById('payment-amount');

    if (paymentType === 'partial') {
        amountGroup.style.display = 'block';
        amountInput.required = true;

        // Set max amount
        const totalAmount = parseFloat(document.getElementById('collection-amount-value').value);
        amountInput.max = totalAmount;
        amountInput.placeholder = `Enter amount (Max: ₹${totalAmount.toLocaleString()})`;
    } else {
        amountGroup.style.display = 'none';
        amountInput.required = false;
        amountInput.value = '';
    }
}

function togglePaymentFields() {
    const paymentMethod = document.getElementById('payment-method').value;
    const chequeFields = document.getElementById('cheque-fields');
    const bankFields = document.getElementById('bank-fields');

    // Hide all fields first
    chequeFields.style.display = 'none';
    bankFields.style.display = 'none';

    // Show relevant fields
    if (paymentMethod === 'cheque') {
        chequeFields.style.display = 'block';
    } else if (paymentMethod === 'bank_transfer') {
        bankFields.style.display = 'block';
    }
}

// File upload validation
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('payment-attachments');
    if (fileInput) {
        fileInput.addEventListener('change', function() {
            const files = this.files;
            const maxFiles = 5;
            const maxSize = 10 * 1024 * 1024; // 10MB

            if (files.length > maxFiles) {
                alert(`Maximum ${maxFiles} files allowed`);
                this.value = '';
                return;
            }

            for (let i = 0; i < files.length; i++) {
                if (files[i].size > maxSize) {
                    alert(`File "${files[i].name}" is too large. Maximum size is 10MB.`);
                    this.value = '';
                    return;
                }
            }
        });
    }
});

function callCustomer(phone, customerName) {
    alert('Call customer: ' + customerName + ' at ' + phone);
}

function sendReminder(orderId, customerName) {
    alert('Send reminder to: ' + customerName + ' for order ' + orderId);
}

function refreshPayments() {
    location.reload();
}
</script>
{% endblock %}
