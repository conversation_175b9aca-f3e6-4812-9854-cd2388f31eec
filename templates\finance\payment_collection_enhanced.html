{% extends "base.html" %}

{% block title %}Enhanced Payment Collection - Medivent ERP{% endblock %}

{% block head %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
{% endblock %}

{% block content %}
<style>
    /* Enhanced Payment Collection Styles - Matching Reference Image */
    .payment-dashboard {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        min-height: 100vh;
        padding: 20px 0;
    }

    .dashboard-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 20px;
    }

    /* Header Section */
    .payment-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .payment-title {
        color: white;
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 8px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .payment-subtitle {
        color: rgba(255, 255, 255, 0.9);
        font-size: 1rem;
        margin-bottom: 0;
    }

    /* Analytics Cards */
    .analytics-cards {
        margin-bottom: 25px;
    }

    .analytics-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 15px;
        box-shadow: 0 3px 10px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        height: 120px;
        display: flex;
        align-items: center;
    }

    .analytics-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.12);
    }

    .analytics-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        margin-right: 20px;
    }

    .analytics-content h3 {
        font-size: 1.8rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 5px;
    }

    .analytics-content p {
        color: #7f8c8d;
        font-size: 0.9rem;
        margin: 0;
    }

    /* Stat Cards */
    .stat-card {
        background: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        text-align: center;
        transition: all 0.3s ease;
        border: 1px solid rgba(0,0,0,0.05);
    }

    .clickable-card {
        cursor: pointer;
    }

    .clickable-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
        margin: 0 auto 15px;
    }

    .stat-value {
        font-size: 1.8rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 5px;
    }

    .stat-label {
        color: #7f8c8d;
        font-size: 0.9rem;
        font-weight: 500;
        margin-bottom: 8px;
    }

    .stat-action {
        margin-top: 8px;
        opacity: 0.7;
        transition: opacity 0.3s ease;
    }

    .clickable-card:hover .stat-action {
        opacity: 1;
    }

    /* Filter Section */
    .filter-section {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .filter-title {
        color: #2c3e50;
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }

    .filter-title i {
        margin-right: 10px;
        color: #3498db;
    }

    .filter-row {
        margin-bottom: 15px;
    }

    .filter-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 5px;
        display: block;
    }

    .form-control, .form-select {
        border-radius: 8px;
        border: 1px solid #ddd;
        padding: 10px 15px;
        font-size: 0.9rem;
    }

    .form-control:focus, .form-select:focus {
        border-color: #3498db;
        box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    }

    .btn-filter {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 10px 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-filter:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        color: white;
    }

    .btn-process {
        background: linear-gradient(135deg, #27ae60, #229954);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 8px 20px;
        font-weight: 600;
        margin-left: 10px;
        transition: all 0.3s ease;
    }

    .btn-process:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
        color: white;
    }
    
    .payment-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        border: 1px solid rgba(0,0,0,0.05);
    }
    
    .payment-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }
    
    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .status-pending {
        background: rgba(255, 193, 7, 0.2);
        color: #856404;
    }
    
    .status-partial {
        background: rgba(255, 152, 0, 0.2);
        color: #e65100;
    }
    
    .status-paid {
        background: rgba(76, 175, 80, 0.2);
        color: #2e7d32;
    }
    
    .action-buttons {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
    }
    
    .btn-modern {
        border-radius: 8px;
        font-weight: 600;
        padding: 8px 16px;
        transition: all 0.3s ease;
    }
    
    .btn-modern:hover {
        transform: translateY(-1px);
    }
    
    .payment-modal .modal-content {
        border-radius: 15px;
        border: none;
    }
    
    .payment-modal .modal-header {
        background: linear-gradient(135deg, var(--primary), var(--secondary));
        color: white;
        border-radius: 15px 15px 0 0;
    }
    
    .file-upload-area {
        border: 2px dashed #ddd;
        border-radius: 10px;
        padding: 30px;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .file-upload-area:hover {
        border-color: var(--primary);
        background: rgba(var(--primary-rgb), 0.05);
    }
    
    .file-upload-area.dragover {
        border-color: var(--primary);
        background: rgba(var(--primary-rgb), 0.1);
    }
    
    .uploaded-files {
        margin-top: 15px;
    }
    
    .file-item {
        display: flex;
        align-items: center;
        justify-content: between;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 8px;
        margin-bottom: 8px;
    }
    
    .file-item img {
        width: 40px;
        height: 40px;
        object-fit: cover;
        border-radius: 5px;
        margin-right: 10px;
    }
</style>

<div class="finance-dashboard">
    <div class="container-fluid">
        <!-- Header Section -->
        <div class="finance-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="finance-title">
                        <i class="fas fa-credit-card me-3"></i>Enhanced Payment Collection
                    </h1>
                    <p class="finance-subtitle">Advanced payment processing with image uploads and comprehensive tracking</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-light btn-modern" onclick="location.reload()">
                        <i class="fas fa-sync-alt me-2"></i>Refresh
                    </button>
                    <button class="btn btn-light btn-modern" onclick="exportPayments()">
                        <i class="fas fa-download me-2"></i>Export
                    </button>
                </div>
            </div>
        </div>

        <!-- Enhanced Filter Section -->
        <div class="filter-card">
            <h5 class="mb-3">
                <i class="fas fa-filter me-2 text-primary"></i>Advanced Filters
            </h5>
            <form method="GET" class="row g-3">
                <div class="col-md-2">
                    <label class="form-label fw-bold">Customer</label>
                    <input type="text" class="form-control" name="customer" 
                           value="{{ filters.customer if filters else '' }}" placeholder="Customer name">
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">Status</label>
                    <select class="form-control" name="status">
                        <option value="all" {{ 'selected' if filters and filters.status == 'all' else '' }}>All Status</option>
                        <option value="pending" {{ 'selected' if filters and filters.status == 'pending' else '' }}>Pending</option>
                        <option value="partial" {{ 'selected' if filters and filters.status == 'partial' else '' }}>Partial</option>
                        <option value="paid" {{ 'selected' if filters and filters.status == 'paid' else '' }}>Paid</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">From Date</label>
                    <input type="date" class="form-control" name="date_from" value="{{ filters.date_from if filters else '' }}">
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">To Date</label>
                    <input type="date" class="form-control" name="date_to" value="{{ filters.date_to if filters else '' }}">
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">Min Amount</label>
                    <input type="number" class="form-control" name="amount_min" 
                           value="{{ filters.amount_min if filters else '' }}" placeholder="0">
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary btn-modern d-block w-100">
                        <i class="fas fa-search me-1"></i>Filter
                    </button>
                </div>
            </form>
        </div>

        <!-- Summary Statistics -->
        {% if summary_stats %}
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stat-card clickable-card" onclick="filterPayments('pending')" title="Click to filter pending payments">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #ffc107, #ff8f00);">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-value">{{ summary_stats.pending_count or 0 }}</div>
                    <div class="stat-label">Pending Payments</div>
                    <div class="stat-action">
                        <small class="text-muted"><i class="fas fa-filter"></i> Filter pending</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card clickable-card" onclick="showPendingBreakdown()" title="Click to view pending amount breakdown">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #ff5722, #d32f2f);">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stat-value">₹{{ "{:,.0f}".format((summary_stats.pending_amount or 0)|float) }}</div>
                    <div class="stat-label">Pending Amount</div>
                    <div class="stat-action">
                        <small class="text-muted"><i class="fas fa-chart-pie"></i> View breakdown</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card clickable-card" onclick="showTodayCollections()" title="Click to view today's collections">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #4caf50, #2e7d32);">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-value">{{ summary_stats.collected_today or 0 }}</div>
                    <div class="stat-label">Collected Today</div>
                    <div class="stat-action">
                        <small class="text-muted"><i class="fas fa-list"></i> View details</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card clickable-card" onclick="showCollectionTrends()" title="Click to view collection trends">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #2196f3, #1565c0);">
                        <i class="fas fa-rupee-sign"></i>
                    </div>
                    <div class="stat-value">₹{{ "{:,.0f}".format((summary_stats.collected_amount_today or 0)|float) }}</div>
                    <div class="stat-label">Amount Collected</div>
                    <div class="stat-action">
                        <small class="text-muted"><i class="fas fa-chart-line"></i> View trends</small>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Payment Analytics Chart -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="stat-card">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line me-2 text-primary"></i>Payment Collection Analytics
                        </h5>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary btn-sm active" onclick="showPaymentChart('daily')">Daily</button>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="showPaymentChart('weekly')">Weekly</button>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="showPaymentChart('monthly')">Monthly</button>
                        </div>
                    </div>
                    <div style="position: relative; height: 300px;">
                        <canvas id="paymentAnalyticsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Collection List -->
        <div class="row">
            <div class="col-12">
                <h4 class="mb-4 text-white">
                    <i class="fas fa-list me-2"></i>Payment Collection
                    <span class="badge bg-light text-dark ms-2">{{ payments|length if payments else 0 }} Orders</span>
                </h4>
                
                {% if payments %}
                    {% for payment in payments %}
                    <div class="payment-card">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <div class="payment-info">
                                    <h6 class="mb-1 fw-bold">{{ payment.customer_name }}</h6>
                                    <small class="text-muted">Order: <strong>{{ payment.order_id }}</strong></small>
                                    {% if payment.customer_phone %}
                                        <br><small class="text-muted">
                                            <i class="fas fa-phone me-1"></i>{{ payment.customer_phone }}
                                        </small>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="amount-info">
                                    <span class="fw-bold text-success" style="font-size: 1.2rem;">₹{{ payment.order_amount|format_currency }}</span>
                                    {% if payment.payment_status == 'partial' %}
                                        <br><small class="text-warning">Partial Payment</small>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-2">
                                <span class="status-badge status-{{ payment.payment_status }}">
                                    {{ payment.payment_status.title() }}
                                </span>
                                {% if payment.payment_method %}
                                    <br><small class="text-muted">{{ payment.payment_method.title() }}</small>
                                {% endif %}
                            </div>
                            <div class="col-md-2">
                                <small class="text-muted">
                                    {% if payment.order_date %}
                                        {{ safe_strftime(payment.order_date, '%d %b %Y') }}
                                    {% else %}
                                        N/A
                                    {% endif %}
                                </small>
                                {% if payment.sales_agent %}
                                    <br><small class="text-info">{{ payment.sales_agent }}</small>
                                {% endif %}
                            </div>
                            <div class="col-md-3">
                                <div class="action-buttons">
                                    {% if payment.payment_status in ['pending', 'partial'] %}
                                        <button class="btn btn-success btn-modern btn-sm" onclick="processPaymentEnhanced('{{ payment.order_id }}', '{{ payment.customer_name }}', {{ payment.order_amount|safe_amount }}, '{{ payment.customer_id or '' }}')">
                                            <i class="fas fa-credit-card me-1"></i>Process
                                        </button>
                                    {% endif %}
                                    <button class="btn btn-info btn-modern btn-sm" onclick="viewOrderDetails('{{ payment.order_id }}')">
                                        <i class="fas fa-eye me-1"></i>View
                                    </button>
                                    {% if payment.payment_status == 'paid' %}
                                        <button class="btn btn-outline-primary btn-modern btn-sm" onclick="viewPaymentHistory('{{ payment.order_id }}')">
                                            <i class="fas fa-history me-1"></i>History
                                        </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-5">
                        <div class="stat-card">
                            <i class="fas fa-credit-card fa-4x text-muted mb-3"></i>
                            <h5 class="text-muted">No payments to collect</h5>
                            <p class="text-muted">All orders have been paid or no orders match your filters.</p>
                            <button class="btn btn-primary btn-modern" onclick="location.reload()">
                                <i class="fas fa-refresh me-1"></i>Refresh
                            </button>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Payment Processing Modal -->
<div class="modal fade payment-modal" id="paymentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-credit-card me-2"></i>Process Payment
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="paymentForm" method="POST" action="{{ url_for('finance_process_payment') }}" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="mb-3">Payment Details</h6>

                            <input type="hidden" id="order_id" name="order_id">
                            <input type="hidden" id="customer_id" name="customer_id">

                            <div class="mb-3">
                                <label class="form-label">Customer Name</label>
                                <input type="text" class="form-control" id="customer_name" readonly>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Order Amount</label>
                                <input type="number" class="form-control" id="order_amount" readonly>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Payment Amount *</label>
                                <input type="number" class="form-control" name="payment_amount" id="payment_amount"
                                       step="0.01" required>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Payment Type *</label>
                                <select class="form-control" name="payment_type" id="payment_type" required>
                                    <option value="full">Full Payment</option>
                                    <option value="partial">Partial Payment</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Payment Method *</label>
                                <select class="form-control" name="payment_method" id="payment_method" required onchange="toggleChequeFields()">
                                    <option value="cash">Cash</option>
                                    <option value="cheque">Cheque</option>
                                    <option value="bank_transfer">Bank Transfer</option>
                                    <option value="credit_card">Credit Card</option>
                                    <option value="online">Online Payment</option>
                                </select>
                            </div>

                            <!-- Cheque Details (shown when cheque is selected) -->
                            <div id="chequeDetails" style="display: none;">
                                <div class="mb-3">
                                    <label class="form-label">Cheque Number</label>
                                    <input type="text" class="form-control" name="cheque_number" id="cheque_number">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Cheque Date</label>
                                    <input type="date" class="form-control" name="cheque_date" id="cheque_date">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Bank Name</label>
                                    <input type="text" class="form-control" name="bank_name" id="bank_name">
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Payment Notes</label>
                                <textarea class="form-control" name="payment_notes" rows="3"
                                          placeholder="Additional notes about this payment..."></textarea>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <h6 class="mb-3">Payment Attachments</h6>
                            <p class="text-muted small">Upload cheque images, deposit slips, or other payment documents</p>

                            <div class="file-upload-area" onclick="document.getElementById('payment_attachments').click()">
                                <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-2"></i>
                                <p class="mb-1">Click to upload files or drag and drop</p>
                                <small class="text-muted">Supports: JPG, PNG, PDF (Max 5MB each)</small>
                                <input type="file" id="payment_attachments" name="payment_attachments"
                                       multiple accept="image/*,.pdf" style="display: none;" onchange="handleFileSelect(this)">
                            </div>

                            <div class="uploaded-files" id="uploadedFiles"></div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check me-1"></i>Process Payment
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Enhanced Payment Processing Functions
function processPaymentEnhanced(orderId, customerName, orderAmount, customerId) {
    document.getElementById('order_id').value = orderId;
    document.getElementById('customer_id').value = customerId;
    document.getElementById('customer_name').value = customerName;
    document.getElementById('order_amount').value = orderAmount;
    document.getElementById('payment_amount').value = orderAmount;

    // Show modal
    new bootstrap.Modal(document.getElementById('paymentModal')).show();
}

function toggleChequeFields() {
    const paymentMethod = document.getElementById('payment_method').value;
    const chequeDetails = document.getElementById('chequeDetails');

    if (paymentMethod === 'cheque') {
        chequeDetails.style.display = 'block';
        document.getElementById('cheque_number').required = true;
        document.getElementById('cheque_date').required = true;
    } else {
        chequeDetails.style.display = 'none';
        document.getElementById('cheque_number').required = false;
        document.getElementById('cheque_date').required = false;
    }
}

function handleFileSelect(input) {
    const files = input.files;
    const uploadedFiles = document.getElementById('uploadedFiles');

    uploadedFiles.innerHTML = '';

    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const fileItem = document.createElement('div');
        fileItem.className = 'file-item';

        // Create preview for images
        if (file.type.startsWith('image/')) {
            const img = document.createElement('img');
            img.src = URL.createObjectURL(file);
            fileItem.appendChild(img);
        } else {
            const icon = document.createElement('i');
            icon.className = 'fas fa-file-pdf fa-2x text-danger me-2';
            fileItem.appendChild(icon);
        }

        const fileInfo = document.createElement('div');
        fileInfo.innerHTML = `
            <div class="fw-bold">${file.name}</div>
            <small class="text-muted">${(file.size / 1024 / 1024).toFixed(2)} MB</small>
        `;
        fileItem.appendChild(fileInfo);

        const removeBtn = document.createElement('button');
        removeBtn.type = 'button';
        removeBtn.className = 'btn btn-sm btn-outline-danger';
        removeBtn.innerHTML = '<i class="fas fa-times"></i>';
        removeBtn.onclick = () => removeFile(i);
        fileItem.appendChild(removeBtn);

        uploadedFiles.appendChild(fileItem);
    }
}

function removeFile(index) {
    const input = document.getElementById('payment_attachments');
    const dt = new DataTransfer();

    for (let i = 0; i < input.files.length; i++) {
        if (i !== index) {
            dt.items.add(input.files[i]);
        }
    }

    input.files = dt.files;
    handleFileSelect(input);
}

function viewOrderDetails(orderId) {
    // Redirect to order details page
    window.open(`/orders/${orderId}`, '_blank');
}

function viewPaymentHistory(orderId) {
    // Fetch payment history and show in modal
    fetch(`/finance/api/payment-history/${orderId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const payments = data.payments;
                const modal = document.createElement('div');
                modal.className = 'modal fade';
                modal.innerHTML = `
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Payment History - Order ${orderId}</h5>
                                <button type="button" class="close" data-dismiss="modal">
                                    <span>&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                ${payments.length > 0 ? `
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Date</th>
                                                    <th>Amount</th>
                                                    <th>Method</th>
                                                    <th>Reference</th>
                                                    <th>Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                ${payments.map(p => `
                                                    <tr>
                                                        <td>${p.payment_date}</td>
                                                        <td>₹${p.amount.toLocaleString()}</td>
                                                        <td>${p.payment_method}</td>
                                                        <td>${p.reference_number || 'N/A'}</td>
                                                        <td><span class="badge badge-success">${p.status}</span></td>
                                                    </tr>
                                                `).join('')}
                                            </tbody>
                                        </table>
                                    </div>
                                ` : '<p class="text-muted">No payment history found for this order.</p>'}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                `;
                document.body.appendChild(modal);
                $(modal).modal('show');

                // Clean up modal when closed
                $(modal).on('hidden.bs.modal', function() {
                    document.body.removeChild(modal);
                });
            } else {
                alert('Error loading payment history: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading payment history');
        });
}

function exportPayments() {
    // Export current payment list
    window.location.href = '/finance/export-payments?' + window.location.search.substring(1);
}

// Drag and drop functionality
document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.querySelector('.file-upload-area');

    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');

        const files = e.dataTransfer.files;
        document.getElementById('payment_attachments').files = files;
        handleFileSelect(document.getElementById('payment_attachments'));
    });

    // Initialize payment analytics chart
    initializePaymentChart();
});

// Payment Analytics Chart
let paymentChart = null;

function initializePaymentChart() {
    const ctx = document.getElementById('paymentAnalyticsChart').getContext('2d');

    paymentChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            datasets: [{
                label: 'Payments Collected',
                data: [12000, 19000, 8000, 15000, 22000, 18000, 25000],
                backgroundColor: 'rgba(40, 167, 69, 0.8)',
                borderColor: 'rgba(40, 167, 69, 1)',
                borderWidth: 2,
                borderRadius: 8,
                borderSkipped: false,
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '₹' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });
}

function showPaymentChart(period) {
    // Update active button
    document.querySelectorAll('.btn-group .btn').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');

    if (paymentChart) {
        paymentChart.destroy();
    }

    const ctx = document.getElementById('paymentAnalyticsChart').getContext('2d');
    let chartData, chartLabels;

    switch(period) {
        case 'daily':
            chartLabels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
            chartData = [12000, 19000, 8000, 15000, 22000, 18000, 25000];
            break;
        case 'weekly':
            chartLabels = ['Week 1', 'Week 2', 'Week 3', 'Week 4'];
            chartData = [85000, 92000, 78000, 105000];
            break;
        case 'monthly':
            chartLabels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
            chartData = [320000, 380000, 290000, 450000, 520000, 480000];
            break;
    }

    paymentChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: chartLabels,
            datasets: [{
                label: 'Payments Collected',
                data: chartData,
                backgroundColor: 'rgba(40, 167, 69, 0.8)',
                borderColor: 'rgba(40, 167, 69, 1)',
                borderWidth: 2,
                borderRadius: 8,
                borderSkipped: false,
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '₹' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });
}

// Enhanced KPI Card Functions for Payment Collection
function filterPayments(status) {
    // Add filter parameter to current URL
    const url = new URL(window.location);
    url.searchParams.set('status', status);
    window.location.href = url.toString();
}

function showPendingBreakdown() {
    // Create and show pending amount breakdown modal
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-chart-pie me-2"></i>Pending Amount Breakdown
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h6 class="card-title">By Customer Type</h6>
                                    <canvas id="customerTypeChart"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h6 class="card-title">By Payment Age</h6>
                                    <canvas id="paymentAgeChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Customer</th>
                                            <th>Amount</th>
                                            <th>Days Pending</th>
                                            <th>Priority</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>Customer A</td>
                                            <td>₹25,000</td>
                                            <td>45 days</td>
                                            <td><span class="badge bg-warning">Medium</span></td>
                                        </tr>
                                        <tr>
                                            <td>Customer B</td>
                                            <td>₹15,000</td>
                                            <td>30 days</td>
                                            <td><span class="badge bg-success">Low</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="exportPendingData()">
                        <i class="fas fa-download me-1"></i>Export
                    </button>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);

    // Show modal
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // Initialize charts
    setTimeout(() => {
        initializePendingCharts();
    }, 500);

    // Clean up when modal is closed
    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
}

function showTodayCollections() {
    // Navigate to collections with today's filter
    const today = new Date().toISOString().split('T')[0];
    window.location.href = `/finance/payment-history?date=${today}`;
}

function showCollectionTrends() {
    // Show collection trends modal
    alert('Collection trends analysis will be implemented');
}

function initializePendingCharts() {
    // Customer type chart
    const typeCtx = document.getElementById('customerTypeChart');
    if (typeCtx) {
        new Chart(typeCtx, {
            type: 'doughnut',
            data: {
                labels: ['Retail', 'Wholesale', 'Corporate', 'Government'],
                datasets: [{
                    data: [40, 30, 20, 10],
                    backgroundColor: ['#007bff', '#28a745', '#ffc107', '#dc3545']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    }

    // Payment age chart
    const ageCtx = document.getElementById('paymentAgeChart');
    if (ageCtx) {
        new Chart(ageCtx, {
            type: 'bar',
            data: {
                labels: ['0-30 days', '31-60 days', '61-90 days', '90+ days'],
                datasets: [{
                    label: 'Amount (₹)',
                    data: [50000, 30000, 20000, 15000],
                    backgroundColor: ['#28a745', '#ffc107', '#fd7e14', '#dc3545']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
}

function exportPendingData() {
    // Export pending data functionality
    alert('Export functionality will be implemented');
}
</script>

{% endblock %}
