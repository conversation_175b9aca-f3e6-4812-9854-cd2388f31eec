{% extends "base.html" %}

{% block title %}Pending Invoices - Finance{% endblock %}

{% block content %}
<style>
    .pending-invoices {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .page-title {
        color: white;
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 5px;
    }
    
    .page-subtitle {
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 0;
    }
    
    .invoices-container {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }
    
    .invoice-card {
        background: white;
        border: 1px solid var(--border);
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
        transition: all 0.3s ease;
        position: relative;
    }
    
    .invoice-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        border-color: var(--primary);
    }
    
    .invoice-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 15px;
    }
    
    .invoice-id {
        font-weight: 700;
        color: var(--primary);
        font-size: 1.1rem;
    }
    
    .invoice-amount {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--accent);
    }
    
    .customer-name {
        font-weight: 600;
        color: var(--text);
        margin-bottom: 5px;
    }
    
    .invoice-date {
        color: var(--text-light);
        font-size: 0.9rem;
    }
    
    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .status-pending {
        background: rgba(255, 193, 7, 0.1);
        color: var(--warning);
        border: 1px solid rgba(255, 193, 7, 0.3);
    }
    
    .action-buttons {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid var(--border);
    }
    
    .btn-modern {
        border-radius: 8px;
        padding: 8px 16px;
        font-weight: 500;
        border: none;
        transition: all 0.3s ease;
    }
    
    .btn-primary-modern {
        background: linear-gradient(135deg, var(--primary), var(--secondary));
        color: white;
    }
    
    .btn-primary-modern:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(5, 114, 206, 0.3);
    }
    
    .btn-success-modern {
        background: linear-gradient(135deg, var(--success), #45a049);
        color: white;
    }
    
    .btn-success-modern:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: var(--text-light);
    }
    
    .empty-icon {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }
</style>

<div class="pending-invoices">
    <div class="container-fluid">
        <!-- Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="page-title">
                        <i class="fas fa-file-invoice mr-3"></i>Pending Invoices
                    </h1>
                    <p class="page-subtitle">Manage and track all pending invoices</p>
                </div>
                <div class="col-md-4 text-right">
                    <a href="{{ url_for('finance_dashboard') }}" class="btn btn-light btn-modern">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Invoices Container -->
        <div class="invoices-container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4 class="mb-0">
                    <i class="fas fa-list mr-2"></i>Pending Invoices 
                    <span class="badge badge-warning">{{ invoices|length }}</span>
                </h4>
                
                <div class="d-flex gap-2 flex-wrap">
                    <a href="{{ url_for('salesperson_ledger') }}" class="btn btn-outline-success btn-modern">
                        <i class="fas fa-user-tie mr-2"></i>Salesperson Ledger
                    </a>
                    <a href="{{ url_for('division_ledger') }}" class="btn btn-outline-info btn-modern">
                        <i class="fas fa-building mr-2"></i>Division Ledger
                    </a>
                    <a href="{{ url_for('finance_customer_ledger') }}" class="btn btn-outline-secondary btn-modern">
                        <i class="fas fa-users mr-2"></i>Customer Ledger
                    </a>
                    <button class="btn btn-outline-primary btn-modern" onclick="refreshInvoices()">
                        <i class="fas fa-sync-alt mr-2"></i>Refresh
                    </button>
                </div>
            </div>
            
            {% if invoices %}
                <div class="row">
                    {% for invoice in invoices %}
                    <div class="col-lg-6 col-xl-4">
                        <div class="invoice-card">
                            <div class="invoice-header">
                                <div class="invoice-id">{{ invoice.order_id }}</div>
                                <div class="invoice-amount">Rs.{{ invoice.order_amount|format_currency }}</div>
                            </div>
                            
                            <div class="customer-name">{{ invoice.customer_name }}</div>
                            <div class="invoice-date">
                                <i class="fas fa-calendar mr-1"></i>{{ invoice.order_date }}
                            </div>
                            
                            <div class="mt-3">
                                <span class="status-badge status-pending">
                                    <i class="fas fa-clock mr-1"></i>{{ invoice.payment_status.title() }}
                                </span>
                            </div>
                            
                            <div class="action-buttons">
                                <div class="d-flex justify-content-between flex-wrap gap-1">
                                    <button class="btn btn-primary-modern btn-sm"
                                            onclick="viewInvoice('{{ invoice.order_id }}')">
                                        <i class="fas fa-eye mr-1"></i>View
                                    </button>

                                    <a href="{{ url_for('finance_view_comments', entity_type='order', entity_id=invoice.order_id) }}"
                                       class="btn btn-outline-info btn-sm">
                                        <i class="fas fa-comments mr-1"></i>Comments
                                    </a>

                                    <button class="btn btn-outline-warning btn-modern btn-sm"
                                            onclick="putOnHold('{{ invoice.order_id }}')">
                                        <i class="fas fa-pause me-1"></i>Put on Hold
                                    </button>

                                    <button class="btn btn-success-modern btn-sm"
                                            onclick="processPayment('{{ invoice.order_id }}', {{ invoice.order_amount }})">
                                        <i class="fas fa-money-bill-wave mr-1"></i>Process Payment
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h5>No Pending Invoices</h5>
                    <p>All invoices have been processed successfully!</p>
                    <a href="{{ url_for('finance_dashboard') }}" class="btn btn-primary-modern">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Payment Processing Modal -->
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Process Payment</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ url_for('finance_process_payment') }}" method="POST">
                <div class="modal-body">
                    <input type="hidden" id="payment-order-id" name="order_id">
                    
                    <div class="form-group">
                        <label>Order ID</label>
                        <input type="text" class="form-control" id="payment-order-display" readonly>
                    </div>
                    
                    <div class="form-group">
                        <label>Amount</label>
                        <input type="text" class="form-control" id="payment-amount-display" readonly>
                    </div>
                    
                    <div class="form-group">
                        <label>Payment Method</label>
                        <select class="form-control" name="payment_method" required>
                            <option value="cash">Cash</option>
                            <option value="bank_transfer">Bank Transfer</option>
                            <option value="cheque">Cheque</option>
                            <option value="online">Online Payment</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label>Notes (Optional)</label>
                        <textarea class="form-control" name="notes" rows="3" placeholder="Payment notes..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success-modern">
                        <i class="fas fa-check mr-2"></i>Process Payment
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Hold Invoice Modal -->
<div class="modal fade" id="holdModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-pause text-warning mr-2"></i>Put Invoice on Hold
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ url_for('finance_put_on_hold') }}" method="POST">
                <div class="modal-body">
                    <input type="hidden" id="hold-order-id" name="order_id">

                    <div class="form-group">
                        <label>Order ID</label>
                        <input type="text" class="form-control" id="hold-order-display" readonly>
                    </div>

                    <div class="form-group">
                        <label>Hold Reason <span class="text-danger">*</span></label>
                        <select class="form-control" name="hold_reason" required>
                            <option value="">Select reason...</option>
                            <option value="credit_check">Credit Check Required</option>
                            <option value="documentation">Missing Documentation</option>
                            <option value="customer_request">Customer Request</option>
                            <option value="payment_issue">Payment Issue</option>
                            <option value="quality_concern">Quality Concern</option>
                            <option value="management_review">Management Review</option>
                            <option value="other">Other</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>Priority Level</label>
                        <select class="form-control" name="priority_level">
                            <option value="normal">Normal</option>
                            <option value="high">High</option>
                            <option value="urgent">Urgent</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>Comments <span class="text-danger">*</span></label>
                        <textarea class="form-control" name="hold_comments" rows="4"
                                  placeholder="Please provide detailed comments explaining why this invoice is being put on hold..." required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-pause mr-2"></i>Put on Hold
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function viewInvoice(orderId) {
    // Implement invoice viewing functionality
    alert('View invoice for: ' + orderId);
}

function processPayment(orderId, amount) {
    document.getElementById('payment-order-id').value = orderId;
    document.getElementById('payment-order-display').value = orderId;
    document.getElementById('payment-amount-display').value = 'Rs.' + amount.toLocaleString();
    $('#paymentModal').modal('show');
}

function putOnHold(orderId) {
    document.getElementById('hold-order-id').value = orderId;
    document.getElementById('hold-order-display').value = orderId;
    $('#holdModal').modal('show');
}

function refreshInvoices() {
    location.reload();
}

// Auto-refresh every 60 seconds
setInterval(function() {
    if (!$('#paymentModal').hasClass('show')) {
        location.reload();
    }
}, 60000);
</script>
{% endblock %}
