{% extends "base.html" %}

{% block title %}{{ title }} - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-chart-bar text-primary me-2"></i>
                        {{ title }}
                    </h1>
                    <p class="text-muted mb-0">Generated on {{ now.strftime('%B %d, %Y at %I:%M %p') }}</p>
                </div>
                <div>
                    <a href="{{ url_for('comprehensive_finance_reports') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left"></i> Back to Reports
                    </a>
                    <button class="btn btn-primary" onclick="window.print()">
                        <i class="fas fa-print"></i> Print Report
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Content -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-file-alt"></i> {{ title }}
                    </h6>
                </div>
                <div class="card-body">
                    {% if report_data.error %}
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        Error generating report: {{ report_data.error }}
                    </div>
                    {% else %}
                    
                    <!-- Profit & Loss Report -->
                    {% if report_type == 'profit_loss' %}
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h4>₹{{ report_data.revenue.total_revenue|safe_currency }}</h4>
                                    <p class="mb-0">Total Revenue</p>
                                    <small>{{ report_data.revenue.total_orders }} orders</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h4>₹{{ report_data.costs.total_costs|safe_currency }}</h4>
                                    <p class="mb-0">Total Costs</p>
                                    <small>COGS + Operating</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4>₹{{ report_data.profit.net_profit|safe_currency }}</h4>
                                    <p class="mb-0">Net Profit</p>
                                    <small>{{ report_data.profit.profit_margin|round(1) }}% margin</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="thead-light">
                                <tr>
                                    <th>Item</th>
                                    <th class="text-right">Amount (₹)</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>Revenue</strong></td>
                                    <td class="text-right"><strong>{{ report_data.revenue.total_revenue|safe_currency }}</strong></td>
                                </tr>
                                <tr>
                                    <td>&nbsp;&nbsp;Total Orders</td>
                                    <td class="text-right">{{ report_data.revenue.total_orders }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Costs</strong></td>
                                    <td class="text-right"></td>
                                </tr>
                                <tr>
                                    <td>&nbsp;&nbsp;Cost of Goods Sold</td>
                                    <td class="text-right">{{ report_data.costs.cost_of_goods|safe_currency }}</td>
                                </tr>
                                <tr>
                                    <td>&nbsp;&nbsp;Operating Expenses</td>
                                    <td class="text-right">{{ report_data.costs.operating_expenses|safe_currency }}</td>
                                </tr>
                                <tr class="table-warning">
                                    <td><strong>Gross Profit</strong></td>
                                    <td class="text-right"><strong>{{ report_data.profit.gross_profit|safe_currency }}</strong></td>
                                </tr>
                                <tr class="table-success">
                                    <td><strong>Net Profit</strong></td>
                                    <td class="text-right"><strong>{{ report_data.profit.net_profit|safe_currency }}</strong></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    {% endif %}

                    <!-- Cash Flow Report -->
                    {% if report_type == 'cash_flow' %}
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h4>₹{{ report_data.operating_cash.cash_inflow|safe_currency }}</h4>
                                    <p class="mb-0">Cash Inflow</p>
                                    <small>Received payments</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h4>₹{{ report_data.operating_cash.pending_cash|safe_currency }}</h4>
                                    <p class="mb-0">Pending Cash</p>
                                    <small>Outstanding receivables</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <h5>Monthly Cash Flow Trends</h5>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="thead-light">
                                <tr>
                                    <th>Month</th>
                                    <th class="text-right">Cash Inflow (₹)</th>
                                    <th class="text-right">Orders</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for trend in report_data.monthly_trends %}
                                <tr>
                                    <td>{{ trend.month }}</td>
                                    <td class="text-right">{{ trend.inflow|safe_currency }}</td>
                                    <td class="text-right">{{ trend.orders }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% endif %}

                    <!-- Balance Sheet Report -->
                    {% if report_type == 'balance_sheet' %}
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Assets</h5>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <tbody>
                                        <tr>
                                            <td>Cash</td>
                                            <td class="text-right">₹{{ report_data.assets.cash|safe_currency }}</td>
                                        </tr>
                                        <tr>
                                            <td>Accounts Receivable</td>
                                            <td class="text-right">₹{{ report_data.assets.accounts_receivable|safe_currency }}</td>
                                        </tr>
                                        <tr>
                                            <td>Inventory</td>
                                            <td class="text-right">₹{{ report_data.assets.inventory|safe_currency }}</td>
                                        </tr>
                                        <tr class="table-primary">
                                            <td><strong>Total Assets</strong></td>
                                            <td class="text-right"><strong>₹{{ report_data.assets.total_assets|safe_currency }}</strong></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5>Liabilities & Equity</h5>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <tbody>
                                        <tr>
                                            <td>Accounts Payable</td>
                                            <td class="text-right">₹{{ report_data.liabilities.accounts_payable|safe_currency }}</td>
                                        </tr>
                                        <tr class="table-warning">
                                            <td><strong>Total Liabilities</strong></td>
                                            <td class="text-right"><strong>₹{{ report_data.liabilities.total_liabilities|safe_currency }}</strong></td>
                                        </tr>
                                        <tr>
                                            <td>Retained Earnings</td>
                                            <td class="text-right">₹{{ report_data.equity.retained_earnings|safe_currency }}</td>
                                        </tr>
                                        <tr class="table-success">
                                            <td><strong>Total Equity</strong></td>
                                            <td class="text-right"><strong>₹{{ report_data.equity.total_equity|safe_currency }}</strong></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Customer Performance Report -->
                    {% if report_type == 'customer_performance' %}
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="thead-light">
                                <tr>
                                    <th>Customer Name</th>
                                    <th class="text-right">Orders</th>
                                    <th class="text-right">Total Revenue (₹)</th>
                                    <th class="text-right">Avg Order Value (₹)</th>
                                    <th>Last Order</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for customer in report_data.customers %}
                                <tr>
                                    <td><strong>{{ customer.name }}</strong></td>
                                    <td class="text-right">{{ customer.orders }}</td>
                                    <td class="text-right">{{ customer.revenue|safe_currency }}</td>
                                    <td class="text-right">{{ customer.avg_value|safe_currency }}</td>
                                    <td>{{ customer.last_order or 'N/A' }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% endif %}

                    <!-- Sales Trends Report -->
                    {% if report_type == 'sales_trends' %}
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="thead-light">
                                <tr>
                                    <th>Month</th>
                                    <th class="text-right">Orders</th>
                                    <th class="text-right">Revenue (₹)</th>
                                    <th class="text-right">Avg Order Value (₹)</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for trend in report_data.monthly_trends %}
                                <tr>
                                    <td>{{ trend.month }}</td>
                                    <td class="text-right">{{ trend.orders }}</td>
                                    <td class="text-right">{{ trend.revenue|safe_currency }}</td>
                                    <td class="text-right">{{ trend.avg_value|safe_currency }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% endif %}

                    <!-- Default message for other reports -->
                    {% if report_type not in ['profit_loss', 'cash_flow', 'balance_sheet', 'customer_performance', 'sales_trends'] %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        {{ report_data.message or 'Report data generated successfully. Full implementation coming soon.' }}
                    </div>
                    {% endif %}

                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .btn, .navbar, .sidebar {
        display: none !important;
    }
    .container-fluid {
        margin: 0;
        padding: 0;
    }
}
</style>
{% endblock %}
