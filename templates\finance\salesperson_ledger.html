{% extends "base.html" %}

{% block title %}Salesperson <PERSON><PERSON> - Medivent ERP{% endblock %}

{% block head %}
<style>
    /* 2025 Modern Salesperson Ledger Styles */
    .salesperson-dashboard {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px 0;
    }

    /* Modern KPI Cards */
    .modern-kpi-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 24px;
        padding: 32px;
        margin-bottom: 24px;
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.1),
            0 2px 16px rgba(0, 0, 0, 0.05),
            inset 0 1px 0 rgba(255, 255, 255, 0.8);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        height: 160px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        position: relative;
        overflow: hidden;
    }

    .modern-kpi-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--card-gradient);
        border-radius: 24px 24px 0 0;
    }

    .modern-kpi-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow:
            0 20px 60px rgba(0, 0, 0, 0.15),
            0 8px 32px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.9);
    }

    .kpi-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;
    }

    .kpi-icon {
        width: 64px;
        height: 64px;
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28px;
        color: white;
        background: var(--icon-gradient);
        box-shadow:
            0 8px 24px rgba(0, 0, 0, 0.15),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }

    .kpi-trend {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 0.85rem;
        font-weight: 600;
        padding: 6px 12px;
        border-radius: 12px;
        background: rgba(255, 255, 255, 0.7);
        backdrop-filter: blur(10px);
    }

    .kpi-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .kpi-value {
        font-size: 2.5rem;
        font-weight: 800;
        background: linear-gradient(135deg, #2c3e50, #34495e);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 8px;
        line-height: 1;
        letter-spacing: -0.02em;
    }

    .kpi-label {
        color: #64748b;
        font-size: 1rem;
        font-weight: 600;
        margin: 0;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    /* Card variants */
    .modern-kpi-card.sales {
        --card-gradient: linear-gradient(135deg, #10b981, #059669);
        --icon-gradient: linear-gradient(135deg, #10b981, #059669, #047857);
    }

    .modern-kpi-card.orders {
        --card-gradient: linear-gradient(135deg, #3b82f6, #2563eb);
        --icon-gradient: linear-gradient(135deg, #3b82f6, #2563eb, #1d4ed8);
    }

    .modern-kpi-card.performance {
        --card-gradient: linear-gradient(135deg, #8b5cf6, #7c3aed);
        --icon-gradient: linear-gradient(135deg, #8b5cf6, #7c3aed, #6d28d9);
    }

    .modern-kpi-card.customers {
        --card-gradient: linear-gradient(135deg, #f59e0b, #d97706);
        --icon-gradient: linear-gradient(135deg, #f59e0b, #d97706, #b45309);
    }
</style>
{% endblock %}

{% block content %}
<div class="salesperson-dashboard">
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-user-tie text-primary me-2"></i>
                        Salesperson Ledger
                    </h1>
                    <p class="text-muted mb-0">Sales team performance analysis and metrics</p>
                </div>
                <div>
                    <select id="salespersonSelector" class="form-select me-2" style="width: 200px; display: inline-block;" onchange="loadSalespersonAnalytics()">
                        <option value="">Select Salesperson</option>
                        {% for sp in salesperson_data %}
                        <option value="{{ sp.sales_agent }}">{{ sp.sales_agent }}</option>
                        {% endfor %}
                    </select>
                    <a href="{{ url_for('finance_dashboard') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left"></i> Back to Finance
                    </a>
                    <button class="btn btn-primary" onclick="exportSalespersonReport()">
                        <i class="fas fa-download"></i> Export Report
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 2025 Modern Summary Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="modern-kpi-card sales">
                <div class="kpi-header">
                    <div class="kpi-icon">
                        <i class="fas fa-rupee-sign"></i>
                    </div>
                    <div class="kpi-trend">
                        <i class="fas fa-arrow-up text-success"></i>
                        <span class="text-success">+12.5%</span>
                    </div>
                </div>
                <div class="kpi-content">
                    <div class="kpi-value">₹{{ (total_sales or 0)|safe_currency }}</div>
                    <div class="kpi-label">Total Sales</div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="modern-kpi-card orders">
                <div class="kpi-header">
                    <div class="kpi-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="kpi-trend">
                        <i class="fas fa-arrow-up text-success"></i>
                        <span class="text-success">+8.3%</span>
                    </div>
                </div>
                <div class="kpi-content">
                    <div class="kpi-value">{{ total_orders or 0 }}</div>
                    <div class="kpi-label">Total Orders</div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="modern-kpi-card performance">
                <div class="kpi-header">
                    <div class="kpi-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="kpi-trend">
                        <i class="fas fa-arrow-up text-success"></i>
                        <span class="text-success">+15.7%</span>
                    </div>
                </div>
                <div class="kpi-content">
                    <div class="kpi-value">₹{{ (avg_performance or 0)|safe_currency }}</div>
                    <div class="kpi-label">Avg Performance</div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="modern-kpi-card customers">
                <div class="kpi-header">
                    <div class="kpi-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="kpi-trend">
                        <i class="fas fa-arrow-up text-success"></i>
                        <span class="text-success">+5.1%</span>
                    </div>
                </div>
                <div class="kpi-content">
                    <div class="kpi-value">{{ salesperson_data|length }}</div>
                    <div class="kpi-label">Sales Team</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Performer Card -->
    {% if top_performer %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-trophy"></i> Top Performer
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <div class="icon-circle bg-success mx-auto mb-3" style="width: 80px; height: 80px;">
                                <i class="fas fa-star text-white" style="font-size: 32px; line-height: 80px;"></i>
                            </div>
                            <h5 class="font-weight-bold">{{ top_performer.sales_agent }}</h5>
                        </div>
                        <div class="col-md-9">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h4 class="text-success">Rs. {{ (top_performer.total_sales or 0)|safe_currency }}</h4>
                                        <small class="text-muted">Total Sales</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h4 class="text-primary">{{ top_performer.total_orders or 0 }}</h4>
                                        <small class="text-muted">Total Orders</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h4 class="text-info">{{ top_performer.unique_customers or 0 }}</h4>
                                        <small class="text-muted">Customers</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h4 class="text-warning">{{ ((top_performer.completed_orders or 0) / (top_performer.total_orders or 1) * 100)|round(1) }}%</h4>
                                        <small class="text-muted">Success Rate</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Advanced Analytics Section -->
    <div id="analyticsSection" class="row mb-4" style="display: none;">
        <div class="col-lg-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-pie"></i> Product Revenue Breakdown
                    </h6>
                </div>
                <div class="card-body">
                    <div id="productSunburstChart" style="height: 400px;">
                        <div class="text-center py-5">
                            <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Select a salesperson to view product breakdown</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-users"></i> Customer Type Analysis
                    </h6>
                </div>
                <div class="card-body">
                    <div id="customerTypeSunburstChart" style="height: 400px;">
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Select a salesperson to view customer analysis</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Performance Trends -->
    <div id="trendsSection" class="row mb-4" style="display: none;">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-line"></i> Monthly Performance Trends
                    </h6>
                </div>
                <div class="card-body">
                    <div id="monthlyTrendsChart" style="height: 300px;">
                        <div class="text-center py-5">
                            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Select a salesperson to view monthly trends</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Customers Table -->
    <div id="customersSection" class="row mb-4" style="display: none;">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-handshake"></i> Top Customers
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="topCustomersTable">
                            <thead>
                                <tr>
                                    <th>Customer Name</th>
                                    <th>Total Revenue</th>
                                    <th>Orders</th>
                                    <th>Avg Order Value</th>
                                    <th>Last Order</th>
                                </tr>
                            </thead>
                            <tbody id="topCustomersTableBody">
                                <!-- Dynamic content -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Salesperson Performance Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-table"></i> Salesperson Performance Details
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="salespersonTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Sales Agent</th>
                            <th>Total Sales</th>
                            <th>Orders</th>
                            <th>Avg Order Value</th>
                            <th>Customers</th>
                            <th>Success Rate</th>
                            <th>First Sale</th>
                            <th>Last Sale</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for salesperson in salesperson_data %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="mr-3">
                                        <div class="icon-circle bg-primary">
                                            <i class="fas fa-user text-white"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <div class="font-weight-bold">{{ salesperson.sales_agent }}</div>
                                        <div class="text-muted small">Sales Representative</div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="font-weight-bold text-success">
                                    Rs. {{ (salesperson.total_sales or 0)|safe_currency }}
                                </div>
                            </td>
                            <td>
                                <div class="text-center">
                                    <div class="font-weight-bold">{{ salesperson.total_orders or 0 }}</div>
                                    <div class="text-muted small">{{ salesperson.completed_orders or 0 }} completed</div>
                                </div>
                            </td>
                            <td>Rs. {{ (salesperson.avg_order_value or 0)|safe_currency }}</td>
                            <td>{{ salesperson.unique_customers or 0 }}</td>
                            <td>
                                <div class="progress" style="height: 20px;">
                                    {% set success_rate = ((salesperson.completed_orders or 0) / (salesperson.total_orders or 1) * 100) %}
                                    <div class="progress-bar bg-success" role="progressbar" 
                                         style="width: {{ success_rate }}%" 
                                         aria-valuenow="{{ success_rate }}" 
                                         aria-valuemin="0" aria-valuemax="100">
                                        {{ success_rate|round(1) }}%
                                    </div>
                                </div>
                            </td>
                            <td>{{ (salesperson.first_sale or '')|date_only }}</td>
                            <td>{{ (salesperson.last_sale or '')|date_only }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-outline-primary" onclick="viewSalespersonDetails('{{ salesperson.sales_agent }}')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-info" onclick="viewSalespersonTrends('{{ salesperson.sales_agent }}')">
                                        <i class="fas fa-chart-line"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#salespersonTable').DataTable({
        "pageLength": 25,
        "order": [[ 1, "desc" ]],
        "columnDefs": [
            { "orderable": false, "targets": 8 }
        ]
    });
});

function viewSalespersonDetails(salesAgent) {
    // Implement detailed view
    alert(`Viewing details for ${salesAgent}`);
}

function viewSalespersonTrends(salesAgent) {
    // Implement trends view
    alert(`Viewing trends for ${salesAgent}`);
}

// Advanced Analytics Functions
function loadSalespersonAnalytics() {
    const selectedSalesperson = document.getElementById('salespersonSelector').value;

    if (!selectedSalesperson) {
        hideAnalyticsSections();
        return;
    }

    // Show loading state
    showLoadingState();

    // Fetch analytics data
    fetch(`/finance/api/salesperson-analytics/${encodeURIComponent(selectedSalesperson)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayAnalytics(data.data);
                showAnalyticsSections();
            } else {
                console.error('Error loading analytics:', data.error);
                alert('Error loading analytics data');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading analytics data');
        });
}

function showAnalyticsSections() {
    document.getElementById('analyticsSection').style.display = 'block';
    document.getElementById('trendsSection').style.display = 'block';
    document.getElementById('customersSection').style.display = 'block';
}

function hideAnalyticsSections() {
    document.getElementById('analyticsSection').style.display = 'none';
    document.getElementById('trendsSection').style.display = 'none';
    document.getElementById('customersSection').style.display = 'none';
}

function showLoadingState() {
    const sections = ['productSunburstChart', 'customerTypeSunburstChart', 'monthlyTrendsChart'];
    sections.forEach(id => {
        document.getElementById(id).innerHTML = `
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="text-muted mt-2">Loading analytics...</p>
            </div>
        `;
    });
}

function displayAnalytics(data) {
    // Display Product Sunburst Chart
    displayProductSunburst(data.products);

    // Display Customer Type Sunburst Chart
    displayCustomerTypeSunburst(data.customer_types);

    // Display Monthly Trends
    displayMonthlyTrends(data.monthly_trends);

    // Display Top Customers Table
    displayTopCustomers(data.top_customers);
}

function displayProductSunburst(products) {
    const chartDiv = document.getElementById('productSunburstChart');

    if (!products || products.length === 0) {
        chartDiv.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                <p class="text-muted">No product data available</p>
            </div>
        `;
        return;
    }

    // Create a simple pie chart representation
    let html = '<div class="row">';
    const colors = ['#007bff', '#28a745', '#ffc107', '#dc3545', '#6f42c1', '#fd7e14', '#20c997', '#6c757d'];

    products.slice(0, 8).forEach((product, index) => {
        const percentage = ((product.value / products.reduce((sum, p) => sum + p.value, 0)) * 100).toFixed(1);
        html += `
            <div class="col-md-6 mb-3">
                <div class="d-flex align-items-center">
                    <div style="width: 20px; height: 20px; background-color: ${colors[index % colors.length]}; border-radius: 3px; margin-right: 10px;"></div>
                    <div class="flex-grow-1">
                        <div class="d-flex justify-content-between">
                            <span class="font-weight-bold">${product.name}</span>
                            <span class="text-muted">${percentage}%</span>
                        </div>
                        <small class="text-muted">₹${product.value.toLocaleString()} (${product.count} orders)</small>
                    </div>
                </div>
            </div>
        `;
    });
    html += '</div>';

    chartDiv.innerHTML = html;
}

function displayCustomerTypeSunburst(customerTypes) {
    const chartDiv = document.getElementById('customerTypeSunburstChart');

    if (!customerTypes || customerTypes.length === 0) {
        chartDiv.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <p class="text-muted">No customer data available</p>
            </div>
        `;
        return;
    }

    // Create customer type breakdown
    let html = '<div class="row">';
    const colors = ['#007bff', '#28a745', '#ffc107', '#dc3545', '#6f42c1'];

    customerTypes.forEach((type, index) => {
        const percentage = ((type.value / customerTypes.reduce((sum, t) => sum + t.value, 0)) * 100).toFixed(1);
        html += `
            <div class="col-12 mb-3">
                <div class="d-flex align-items-center">
                    <div style="width: 30px; height: 30px; background-color: ${colors[index % colors.length]}; border-radius: 50%; margin-right: 15px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-user text-white" style="font-size: 12px;"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="d-flex justify-content-between">
                            <span class="font-weight-bold">${type.name}</span>
                            <span class="text-muted">${percentage}%</span>
                        </div>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar" style="width: ${percentage}%; background-color: ${colors[index % colors.length]};"></div>
                        </div>
                        <small class="text-muted">₹${type.value.toLocaleString()} • ${type.count} orders • ${type.customers} customers</small>
                    </div>
                </div>
            </div>
        `;
    });
    html += '</div>';

    chartDiv.innerHTML = html;
}

function displayMonthlyTrends(trends) {
    const chartDiv = document.getElementById('monthlyTrendsChart');

    if (!trends || trends.length === 0) {
        chartDiv.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                <p class="text-muted">No trend data available</p>
            </div>
        `;
        return;
    }

    // Create a simple line chart representation
    let html = '<div class="table-responsive"><table class="table table-sm">';
    html += '<thead><tr><th>Month</th><th>Revenue</th><th>Orders</th><th>Customers</th></tr></thead><tbody>';

    trends.forEach(trend => {
        html += `
            <tr>
                <td><strong>${trend.month}</strong></td>
                <td>₹${trend.revenue.toLocaleString()}</td>
                <td>${trend.orders}</td>
                <td>${trend.customers}</td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    chartDiv.innerHTML = html;
}

function displayTopCustomers(customers) {
    const tableBody = document.getElementById('topCustomersTableBody');

    if (!customers || customers.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">No customer data available</td></tr>';
        return;
    }

    let html = '';
    customers.forEach(customer => {
        html += `
            <tr>
                <td><strong>${customer.name}</strong></td>
                <td>₹${customer.revenue.toLocaleString()}</td>
                <td>${customer.orders}</td>
                <td>₹${customer.avg_value.toLocaleString()}</td>
                <td>${customer.last_order || 'N/A'}</td>
            </tr>
        `;
    });

    tableBody.innerHTML = html;
}

function exportSalespersonReport() {
    // Implement export functionality
    window.location.href = '/finance/salesperson-ledger/export';
}
</script>

<style>
.icon-circle {
    height: 2.5rem;
    width: 2.5rem;
    border-radius: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
{% endblock %}
</div>
</div>
