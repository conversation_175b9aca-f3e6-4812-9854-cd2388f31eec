{% extends "base.html" %}

{% block title %}Finance Settings{% endblock %}

{% block content %}
<style>
    .settings-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .page-title {
        color: white;
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 5px;
    }
    
    .settings-card {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    
    .setting-group {
        margin-bottom: 25px;
        padding: 20px;
        border: 1px solid #e9ecef;
        border-radius: 10px;
        background: #f8f9fa;
    }
    
    .setting-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
    }
    
    .setting-description {
        font-size: 0.9rem;
        color: #6c757d;
        margin-bottom: 10px;
    }
    
    .form-control {
        border-radius: 8px;
        border: 1px solid #ced4da;
        padding: 10px 15px;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
        border-radius: 8px;
        padding: 12px 30px;
        font-weight: 600;
    }
    
    .btn-secondary {
        background: #6c757d;
        border: none;
        border-radius: 8px;
        padding: 12px 30px;
        font-weight: 600;
    }
</style>

<div class="settings-container">
    <div class="container-fluid">
        <!-- Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="page-title">
                        <i class="fas fa-cog mr-3"></i>Finance Settings
                    </h1>
                    <p class="text-white-50 mb-0">Configure finance system parameters</p>
                </div>
                <div class="col-md-4 text-right">
                    <a href="{{ url_for('finance_dashboard') }}" class="btn btn-light">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Settings Form -->
        <div class="settings-card">
            <form method="POST">
                <h4 class="mb-4">
                    <i class="fas fa-calculator mr-2 text-primary"></i>Financial Configuration
                </h4>
                
                <!-- Tax Rate Setting -->
                <div class="setting-group">
                    <label class="setting-label">Tax Rate (%)</label>
                    <div class="setting-description">
                        Default tax rate applied to all invoices and calculations
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <input type="number" 
                                   name="tax_rate" 
                                   class="form-control" 
                                   value="{{ settings.tax_rate or '17.0' }}" 
                                   step="0.1" 
                                   min="0" 
                                   max="100"
                                   placeholder="17.0">
                        </div>
                        <div class="col-md-8">
                            <small class="text-muted">
                                Current rate: {{ settings.tax_rate or '17.0' }}% 
                                (Applied to all new invoices)
                            </small>
                        </div>
                    </div>
                </div>
                
                <!-- TP Rate Multiplier Setting -->
                <div class="setting-group">
                    <label class="setting-label">TP Rate Multiplier</label>
                    <div class="setting-description">
                        Multiplier used to calculate Trade Price from MRP (TP = MRP × multiplier)
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <input type="number" 
                                   name="tp_rate_multiplier" 
                                   class="form-control" 
                                   value="{{ settings.tp_rate_multiplier or '0.85' }}" 
                                   step="0.01" 
                                   min="0" 
                                   max="1"
                                   placeholder="0.85">
                        </div>
                        <div class="col-md-8">
                            <small class="text-muted">
                                Current multiplier: {{ settings.tp_rate_multiplier or '0.85' }} 
                                ({{ ((settings.tp_rate_multiplier|float or 0.85) * 100)|round(1) }}% of MRP)
                            </small>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="text-right mt-4">
                    <button type="button" class="btn btn-secondary mr-3" onclick="resetForm()">
                        <i class="fas fa-undo mr-2"></i>Reset
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save mr-2"></i>Save Settings
                    </button>
                </div>
            </form>
        </div>
        
        <!-- Current Settings Display -->
        <div class="settings-card">
            <h4 class="mb-4">
                <i class="fas fa-info-circle mr-2 text-info"></i>Current Configuration
            </h4>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="card border-primary">
                        <div class="card-body text-center">
                            <h5 class="card-title text-primary">Tax Rate</h5>
                            <h2 class="text-primary">{{ settings.tax_rate or '17.0' }}%</h2>
                            <p class="card-text">Applied to all invoices</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-success">
                        <div class="card-body text-center">
                            <h5 class="card-title text-success">TP Rate Multiplier</h5>
                            <h2 class="text-success">{{ settings.tp_rate_multiplier or '0.85' }}</h2>
                            <p class="card-text">For MRP to TP calculation</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function resetForm() {
    if (confirm('Are you sure you want to reset all settings to their current values?')) {
        location.reload();
    }
}

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const taxRate = parseFloat(document.querySelector('input[name="tax_rate"]').value);
    const tpMultiplier = parseFloat(document.querySelector('input[name="tp_rate_multiplier"]').value);
    
    if (taxRate < 0 || taxRate > 100) {
        e.preventDefault();
        alert('Tax rate must be between 0% and 100%');
        return false;
    }
    
    if (tpMultiplier < 0 || tpMultiplier > 1) {
        e.preventDefault();
        alert('TP Rate multiplier must be between 0 and 1');
        return false;
    }
});
</script>
{% endblock %}
