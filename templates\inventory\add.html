{% extends 'base.html' %}

{% block title %}Add Inventory{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Add Inventory</h1>
        <a href="{{ url_for('inventory.index') }}" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Inventory
        </a>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Inventory Details</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('inventory.new_inventory') }}">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="product_id">Product <span class="text-danger">*</span></label>
                                    <select class="form-control" id="product_id" name="product_id" required>
                                        <option value="">Select Product</option>
                                        {% for product in products %}
                                        <option value="{{ product.product_id }}" data-strength="{{ product.strength }}">
                                            {{ product.name }} - {{ product.strength }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="warehouse_id">Warehouse <span class="text-danger">*</span></label>
                                    <select class="form-control" id="warehouse_id" name="warehouse_id" required>
                                        <option value="">Select Warehouse</option>
                                        {% for warehouse in warehouses %}
                                        <option value="{{ warehouse.warehouse_id }}">
                                            {{ warehouse.name }} - {{ warehouse.location }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="batch_number">Batch Number <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="batch_number" name="batch_number" required>
                                    <small class="form-text text-muted">Unique batch identifier</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="quantity">Quantity <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="quantity" name="quantity" min="1" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="manufacturing_date">Manufacturing Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="manufacturing_date" name="manufacturing_date" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="expiry_date">Expiry Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="expiry_date" name="expiry_date" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="cost_price">Cost Price (per unit)</label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">₨</span>
                                        </div>
                                        <input type="number" class="form-control" id="cost_price" name="cost_price" step="0.01" min="0">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="selling_price">Selling Price (per unit)</label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">₨</span>
                                        </div>
                                        <input type="number" class="form-control" id="selling_price" name="selling_price" step="0.01" min="0">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="supplier">Supplier</label>
                                    <input type="text" class="form-control" id="supplier" name="supplier">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="purchase_order">Purchase Order #</label>
                                    <input type="text" class="form-control" id="purchase_order" name="purchase_order">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="notes">Notes</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="Additional notes about this inventory batch..."></textarea>
                        </div>

                        <hr>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Add Inventory
                            </button>
                            <a href="{{ url_for('inventory.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Inventory Guidelines</h6>
                </div>
                <div class="card-body">
                    <h6>Important Notes:</h6>
                    <ul class="small">
                        <li>Ensure batch numbers are unique</li>
                        <li>Manufacturing date must be before expiry date</li>
                        <li>Cost price should be less than selling price</li>
                        <li>Check existing inventory before adding duplicates</li>
                        <li>Verify product details before submission</li>
                    </ul>
                    
                    <h6 class="mt-3">Batch Naming Convention:</h6>
                    <p class="small text-muted">
                        Use format: PRODUCT-YYYYMM-XXX<br>
                        Example: PARA-202501-001
                    </p>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <a href="{{ url_for('products.product_management') }}" class="btn btn-sm btn-outline-primary btn-block">
                        <i class="fas fa-plus"></i> Add New Product
                    </a>
                    <a href="{{ url_for('warehouses.manage_warehouses') }}" class="btn btn-sm btn-outline-info btn-block">
                        <i class="fas fa-warehouse"></i> Manage Warehouses
                    </a>
                    <a href="{{ url_for('inventory.index') }}" class="btn btn-sm btn-outline-secondary btn-block">
                        <i class="fas fa-list"></i> View All Inventory
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // Auto-calculate total value
    $('#quantity, #cost_price').on('input', function() {
        var quantity = parseFloat($('#quantity').val()) || 0;
        var costPrice = parseFloat($('#cost_price').val()) || 0;
        var totalValue = quantity * costPrice;
        
        if (totalValue > 0) {
            $('#total_value_display').text('Total Value: ₨' + totalValue.toFixed(2));
        }
    });

    // Date validation
    $('#manufacturing_date, #expiry_date').on('change', function() {
        var mfgDate = new Date($('#manufacturing_date').val());
        var expDate = new Date($('#expiry_date').val());
        
        if (mfgDate && expDate && mfgDate >= expDate) {
            alert('Manufacturing date must be before expiry date!');
            $('#expiry_date').focus();
        }
    });

    // Batch number suggestion
    $('#product_id').on('change', function() {
        var productText = $(this).find('option:selected').text();
        if (productText && productText !== 'Select Product') {
            var productCode = productText.substring(0, 4).toUpperCase();
            var currentDate = new Date();
            var yearMonth = currentDate.getFullYear().toString() + 
                           (currentDate.getMonth() + 1).toString().padStart(2, '0');
            var suggestedBatch = productCode + '-' + yearMonth + '-001';
            
            if (!$('#batch_number').val()) {
                $('#batch_number').val(suggestedBatch);
            }
        }
    });

    // Form validation
    $('form').on('submit', function(e) {
        var mfgDate = new Date($('#manufacturing_date').val());
        var expDate = new Date($('#expiry_date').val());
        
        if (mfgDate >= expDate) {
            e.preventDefault();
            alert('Manufacturing date must be before expiry date!');
            return false;
        }
        
        var quantity = parseInt($('#quantity').val());
        if (quantity <= 0) {
            e.preventDefault();
            alert('Quantity must be greater than 0!');
            return false;
        }
    });
});
</script>
{% endblock %}
