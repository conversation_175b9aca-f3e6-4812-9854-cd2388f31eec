{% extends 'base.html' %}

{% block title %}Edit Inventory - {{ inventory.product_name }} - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-edit text-primary"></i> Edit Inventory
        </h1>
        <div>
            <a href="{{ url_for('inventory.view_inventory', inventory_id=inventory.inventory_id) }}" class="btn btn-secondary shadow-sm">
                <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Details
            </a>
            <a href="{{ url_for('inventory.index') }}" class="btn btn-outline-secondary shadow-sm">
                <i class="fas fa-list fa-sm text-white-50"></i> All Inventory
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Edit Form -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-edit"></i> Edit Inventory Details
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <!-- Product Information (Read-only) -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label"><strong>Product Name</strong></label>
                                <div class="form-control-plaintext">{{ inventory.product_name }}</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label"><strong>Strength</strong></label>
                                <div class="form-control-plaintext">{{ inventory.strength }}</div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label"><strong>Division</strong></label>
                                <div class="form-control-plaintext">{{ inventory.division_name }}</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label"><strong>Batch Number</strong></label>
                                <div class="form-control-plaintext">{{ inventory.batch_number }}</div>
                            </div>
                        </div>

                        <hr>

                        <!-- Editable Fields -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="stock_quantity" class="form-label">Stock Quantity <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="stock_quantity" name="stock_quantity" 
                                       value="{{ inventory.stock_quantity }}" min="0" required>
                            </div>
                            <div class="col-md-6">
                                <label for="allocated_quantity" class="form-label">Allocated Quantity</label>
                                <input type="number" class="form-control" id="allocated_quantity" name="allocated_quantity" 
                                       value="{{ inventory.allocated_quantity or 0 }}" min="0">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="location_code" class="form-label">Location Code</label>
                                <input type="text" class="form-control" id="location_code" name="location_code" 
                                       value="{{ inventory.location_code or '' }}" placeholder="e.g., A1-B2-C3">
                            </div>
                            <div class="col-md-6">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-control" id="status" name="status">
                                    <option value="active" {% if inventory.status == 'active' %}selected{% endif %}>Active</option>
                                    <option value="deactive" {% if inventory.status == 'deactive' %}selected{% endif %}>Inactive</option>
                                    <option value="expired" {% if inventory.status == 'expired' %}selected{% endif %}>Expired</option>
                                    <option value="damaged" {% if inventory.status == 'damaged' %}selected{% endif %}>Damaged</option>
                                </select>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Update Inventory
                                </button>
                                <a href="{{ url_for('inventory.view_inventory', inventory_id=inventory.inventory_id) }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Current Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">Current Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>Warehouse:</strong><br>
                        {{ inventory.warehouse_name or 'Not assigned' }}
                    </div>
                    <div class="mb-2">
                        <strong>Manufacturing Date:</strong><br>
                        {{ inventory.manufacturing_date }}
                    </div>
                    <div class="mb-2">
                        <strong>Expiry Date:</strong><br>
                        {{ inventory.expiry_date }}
                    </div>
                    <div class="mb-2">
                        <strong>Country of Origin:</strong><br>
                        {{ inventory.country_of_origin }}
                    </div>
                    <div class="mb-2">
                        <strong>Last Updated:</strong><br>
                        {{ inventory.last_updated }}
                    </div>
                    <div class="mb-2">
                        <strong>Updated By:</strong><br>
                        {{ inventory.updated_by }}
                    </div>
                </div>
            </div>

            <!-- Available Stock Calculation -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">Stock Calculation</h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>Total Stock:</strong>
                        <span class="float-right">{{ inventory.stock_quantity }}</span>
                    </div>
                    <div class="mb-2">
                        <strong>Allocated:</strong>
                        <span class="float-right">{{ inventory.allocated_quantity or 0 }}</span>
                    </div>
                    <hr>
                    <div class="mb-0">
                        <strong>Available:</strong>
                        <span class="float-right text-success">
                            {{ (inventory.stock_quantity or 0) - (inventory.allocated_quantity or 0) }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <a href="{{ url_for('inventory.new_inventory', product_id=inventory.product_id) }}" class="btn btn-sm btn-outline-primary btn-block">
                        <i class="fas fa-plus"></i> Add More Stock
                    </a>
                    <a href="{{ url_for('inventory.product_inventory', product_id=inventory.product_id) }}" class="btn btn-sm btn-outline-info btn-block">
                        <i class="fas fa-warehouse"></i> View All Product Stock
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Calculate available stock in real-time
    const stockInput = document.getElementById('stock_quantity');
    const allocatedInput = document.getElementById('allocated_quantity');
    
    function updateAvailableStock() {
        const stock = parseInt(stockInput.value) || 0;
        const allocated = parseInt(allocatedInput.value) || 0;
        const available = stock - allocated;
        
        // Update the display (if you want real-time updates)
        // You can add a span with id="available-stock" to show this
    }
    
    stockInput.addEventListener('input', updateAvailableStock);
    allocatedInput.addEventListener('input', updateAvailableStock);
});
</script>
{% endblock %}
