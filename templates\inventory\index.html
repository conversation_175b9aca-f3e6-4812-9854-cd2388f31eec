{% extends 'base.html' %}

{% block title %}Inventory - Medivent Pharmaceuticals ERP{% endblock %}

{% block styles %}
<style>
    .status-toggle {
        text-decoration: none !important;
        cursor: pointer;
    }

    .status-toggle .badge {
        transition: all 0.3s ease;
    }

    .status-toggle:hover .badge {
        transform: scale(1.1);
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }

    .status-toggle .badge {
        padding: 6px 12px;
        font-size: 0.9rem;
    }

    /* Make the badge wider for better visibility */
    .status-toggle .badge {
        min-width: 80px;
        text-align: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Inventory Management</h4>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <form action="{{ url_for('inventory.index') }}" method="get" class="form-inline">
                                <div class="input-group w-100">
                                    <input type="text" name="q" class="form-control" placeholder="Search by Product, Batch, Country, Manufacturer, or Division" value="{{ search_query }}">
                                    <div class="input-group-append">
                                        <button class="btn btn-primary" type="submit">Search</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="col-md-6 text-right">
                            {% if has_permission('report_export') %}
                            <a href="{{ url_for('export_inventory_excel') }}" class="btn btn-secondary mr-2">
                                <i class="fas fa-file-excel"></i> Export to Excel
                            </a>
                            {% endif %}
                            {% if has_permission('inventory_add') %}
                            <a href="{{ url_for('inventory.new_inventory') }}" class="btn btn-success">
                                <i class="fas fa-plus-circle"></i> Add New Stock
                            </a>
                            {% endif %}
                            {% if has_permission('inventory_transfer') %}
                            <a href="{{ url_for('inventory.transfer') }}" class="btn btn-info ml-2">
                                <i class="fas fa-exchange-alt"></i> Stock Transfer
                            </a>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Products Summary Section -->
                    {% if products_summary %}
                    <div class="mb-4">
                        <h5 class="text-primary">📦 Products Overview</h5>
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered">
                                <thead class="thead-light">
                                    <tr>
                                        <th>Product</th>
                                        <th>Strength</th>
                                        <th>Division</th>
                                        <th>Total Stock</th>
                                        <th>Available Stock</th>
                                        <th>Batches</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for product in products_summary %}
                                    <tr>
                                        <td><strong>{{ product.product_name }}</strong></td>
                                        <td>{{ product.strength or 'N/A' }}</td>
                                        <td>{{ product.division_name }}</td>
                                        <td>
                                            {% if product.total_stock > 0 %}
                                                <span class="badge badge-success">{{ product.total_stock }} units</span>
                                            {% else %}
                                                <span class="badge badge-warning">No Stock</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if product.available_stock > 0 %}
                                                <span class="badge badge-primary">{{ product.available_stock }} available</span>
                                            {% else %}
                                                <span class="badge badge-secondary">0 available</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if product.batch_count > 0 %}
                                                <span class="badge badge-info">{{ product.batch_count }} batch{{ 'es' if product.batch_count != 1 else '' }}</span>
                                            {% else %}
                                                <span class="badge badge-light">No batches</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if product.total_stock > 0 %}
                                                <span class="badge badge-success">In Stock</span>
                                            {% else %}
                                                <span class="badge badge-info">Ready for Stock</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if has_permission('inventory_add') %}
                                            <a href="{{ url_for('inventory.new_inventory', product_id=product.product_id) }}" class="btn btn-sm btn-success">
                                                <i class="fas fa-plus"></i> Add Stock
                                            </a>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Inventory Records Section -->
                    <h5 class="text-primary">📋 Inventory Records</h5>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Product</th>
                                    <th>Strength</th>
                                    <th>Batch #</th>
                                    <th>Country</th>
                                    <th>Manufacturer</th>
                                    <th>Division</th>
                                    <th>Expiry Date</th>
                                    <th>Warehouse</th>
                                    <th>Quantity</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if recent_inventory %}
                                    {% for item in recent_inventory %}
                                    <tr>
                                        <td>{{ item.product_name }}</td>
                                        <td>{{ item.strength }}</td>
                                        <td><strong>{{ item.batch_number }}</strong></td>
                                        <td>{{ item.country or 'N/A' }}</td>
                                        <td>{{ item.manufacturer or 'Generic' }}</td>
                                        <td>{{ item.division_name or 'Unassigned' }}</td>
                                        <td>{{ item.expiry_date }}</td>
                                        <td>{{ item.warehouse_name }}</td>
                                        <td>
                                            <span class="badge badge-primary">{{ item.available_quantity or (item.stock_quantity - (item.allocated_quantity or 0)) }}</span>
                                            <small class="text-muted d-block">Total: {{ item.stock_quantity }}</small>
                                        </td>
                                    <td>
                                        {% if has_permission('inventory_adjust') %}
                                        <a href="{{ url_for('toggle_inventory_status', inventory_id=item.inventory_id) }}"
                                           class="status-toggle"
                                           data-toggle="tooltip"
                                           title="Click to toggle status between active and deactive">
                                            <span class="badge
                                                {% if item.status == 'active' %}badge-success
                                                {% elif item.status == 'low' %}badge-warning
                                                {% elif item.status == 'expired' %}badge-danger
                                                {% elif item.status == 'deactive' %}badge-secondary
                                                {% else %}badge-secondary
                                                {% endif %}">
                                                {{ item.status }}
                                            </span>
                                        </a>
                                        {% else %}
                                        <span class="badge
                                            {% if item.status == 'active' %}badge-success
                                            {% elif item.status == 'low' %}badge-warning
                                            {% elif item.status == 'expired' %}badge-danger
                                            {% elif item.status == 'deactive' %}badge-secondary
                                            {% else %}badge-secondary
                                            {% endif %}">
                                            {{ item.status }}
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ url_for('inventory.history', inventory_id=item.inventory_id) }}" class="btn btn-sm btn-info">History</a>
                                            {% if has_permission('inventory_add') %}
                                            <a href="{{ url_for('inventory.new_inventory') }}" class="btn btn-sm btn-success">Add Stock</a>
                                            {% endif %}
                                            {% if has_permission('inventory_transfer') %}
                                            <a href="{{ url_for('inventory.transfer') }}" class="btn btn-sm btn-primary">Transfer</a>
                                            {% endif %}
                                        </div>
                                    </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="11" class="text-center text-muted py-4">
                                            <i class="fas fa-box-open fa-3x mb-3"></i><br>
                                            <strong>No inventory records found</strong><br>
                                            <small>Products are available above. Click "Add Stock" to create inventory records.</small>
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function(){
        // Initialize tooltips
        $('[data-toggle="tooltip"]').tooltip();
    });
</script>
{% endblock %}