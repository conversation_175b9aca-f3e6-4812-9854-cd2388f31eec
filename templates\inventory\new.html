{% extends 'base.html' %}

{% block title %}Add New Stock - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Add New Stock</h4>
                </div>
                <div class="card-body">
                    {% if selected_product %}
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        <i class="fas fa-info-circle"></i>
                        <strong>Auto-Selected Product:</strong> {{ selected_product.name }}
                        {% if selected_product.strength %} ({{ selected_product.strength }}){% endif %}
                        - {{ selected_product.division_name }}
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    {% endif %}

                    <form method="post" action="{{ url_for('inventory.new_inventory') }}">
                        {% if selected_product %}
                        <!-- Auto-Selected Product - Show Details as Read-Only -->
                        <div class="card mb-4 border-primary">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0"><i class="fas fa-box"></i> Selected Product Details</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>Product Name:</strong><br>
                                        <span class="text-primary">{{ selected_product.name }}</span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Generic Name:</strong><br>
                                        <span class="text-success">{{ selected_product.generic_name or 'N/A' }}</span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Strength:</strong><br>
                                        <span class="text-muted">{{ selected_product.strength or 'N/A' }}</span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Division:</strong><br>
                                        <span class="text-info">{{ selected_product.division_name }}</span>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-md-3">
                                        <strong>Manufacturer:</strong><br>
                                        <span class="text-muted">{{ selected_product.manufacturer or 'N/A' }}</span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Unit of Measure:</strong><br>
                                        <span class="text-muted">{{ selected_product.unit_of_measure or 'N/A' }}</span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Unit Price:</strong><br>
                                        <span class="text-warning">₹{{ selected_product.unit_price or '0.00' }}</span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Min Stock Level:</strong><br>
                                        <span class="text-secondary">{{ selected_product.min_stock_level or '10' }}</span>
                                    </div>
                                </div>
                                <!-- Hidden input for product_id -->
                                <input type="hidden" name="product_id" value="{{ selected_product.product_id }}">
                            </div>
                        </div>

                        <!-- Simplified Form - Only Essential Fields -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="warehouse_id">Warehouse <span class="text-danger">*</span></label>
                                    <select class="form-control" id="warehouse_id" name="warehouse_id" required>
                                        <option value="">Select Warehouse</option>
                                        {% for warehouse in warehouses %}
                                        <option value="{{ warehouse.warehouse_id }}">{{ warehouse.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="batch_number">Batch Number <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="batch_number" name="batch_number"
                                           placeholder="Enter batch number" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="stock_quantity">Quantity <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="stock_quantity" name="stock_quantity"
                                           placeholder="Enter quantity" min="1" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="manufacturing_date">Manufacturing Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="manufacturing_date" name="manufacturing_date" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="expiry_date">Expiry Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="expiry_date" name="expiry_date" required>
                                </div>
                            </div>
                        </div>

                        {% else %}
                        <!-- Manual Product Selection - Full Form -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="product_id">Product <span class="text-danger">*</span></label>
                                    <select class="form-control" id="product_id" name="product_id" required>
                                        <option value="">Select Product</option>
                                        {% for product in products %}
                                        <option value="{{ product.product_id }}"
                                                data-division="{{ product.division_name }}"
                                                data-strength="{{ product.strength }}">
                                            {{ product.display_name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                    <small class="form-text text-muted">Only products with valid division assignments are shown</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="warehouse_id">Warehouse <span class="text-danger">*</span></label>
                                    <select class="form-control" id="warehouse_id" name="warehouse_id" required>
                                        <option value="">Select Warehouse</option>
                                        {% for warehouse in warehouses %}
                                        <option value="{{ warehouse.warehouse_id }}">{{ warehouse.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="batch_number">Batch Number <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="batch_number" name="batch_number"
                                           placeholder="Enter batch number" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="stock_quantity">Quantity <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="stock_quantity" name="stock_quantity"
                                           placeholder="Enter quantity" min="1" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="manufacturing_date">Manufacturing Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="manufacturing_date" name="manufacturing_date" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="expiry_date">Expiry Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="expiry_date" name="expiry_date" required>
                                </div>
                            </div>
                        </div>


                        {% endif %}

                        <div class="row mt-4">
                            <div class="col-md-12 text-right">
                                <a href="{{ url_for('inventory.index') }}" class="btn btn-secondary">Cancel</a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Add Stock
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    {% if selected_product %}
    // Auto-selected product - focus on first required field
    const warehouseSelect = document.getElementById('warehouse_id');
    if (warehouseSelect) {
        warehouseSelect.focus();
    }
    {% else %}
    // Manual selection - focus on product dropdown
    const productSelect = document.getElementById('product_id');
    if (productSelect) {
        productSelect.focus();
    }
    {% endif %}

    // Auto-calculate expiry date based on manufacturing date (optional enhancement)
    const mfgDate = document.getElementById('manufacturing_date');
    const expDate = document.getElementById('expiry_date');

    if (mfgDate && expDate) {
        mfgDate.addEventListener('change', function() {
            if (this.value && !expDate.value) {
                // Auto-suggest expiry date 2 years from manufacturing date
                const mfgDateObj = new Date(this.value);
                mfgDateObj.setFullYear(mfgDateObj.getFullYear() + 2);
                expDate.value = mfgDateObj.toISOString().split('T')[0];
            }
        });
    }
});
</script>

{% endblock %}
