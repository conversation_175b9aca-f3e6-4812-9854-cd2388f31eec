{% extends 'base.html' %}

{% block title %}Product Inventory - {{ product_info.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="fas fa-boxes"></i> 
                        Product Inventory: {{ product_info.name }}
                        {% if product_info.strength %}
                            <span class="badge badge-info">{{ product_info.strength }}</span>
                        {% endif %}
                    </h4>
                </div>
                <div class="card-body">
                    
                    <!-- Product Information -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5 class="card-title">Product Details</h5>
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>Product ID:</strong></td>
                                            <td>{{ product_info.product_id }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Name:</strong></td>
                                            <td>{{ product_info.name }}</td>
                                        </tr>
                                        {% if product_info.strength %}
                                        <tr>
                                            <td><strong>Strength:</strong></td>
                                            <td>{{ product_info.strength }}</td>
                                        </tr>
                                        {% endif %}
                                        {% if product_info.manufacturer %}
                                        <tr>
                                            <td><strong>Manufacturer:</strong></td>
                                            <td>{{ product_info.manufacturer }}</td>
                                        </tr>
                                        {% endif %}
                                        {% if product_info.division_name %}
                                        <tr>
                                            <td><strong>Division:</strong></td>
                                            <td>{{ product_info.division_name }}</td>
                                        </tr>
                                        {% endif %}
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5 class="card-title">Stock Summary</h5>
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>Available Stock:</strong></td>
                                            <td>
                                                <span class="badge badge-{{ 'success' if available_stock > 0 else 'danger' }} badge-lg">
                                                    {{ available_stock }} units
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Total Batches:</strong></td>
                                            <td>
                                                <span class="badge badge-info badge-lg">
                                                    {{ inventory_entries|length }} batch{{ 'es' if inventory_entries|length != 1 else '' }}
                                                </span>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Inventory Entries -->
                    <div class="row">
                        <div class="col-md-12">
                            <h5>Inventory Batches</h5>
                            
                            {% if inventory_entries %}
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Batch Number</th>
                                            <th>Warehouse</th>
                                            <th>Stock Quantity</th>
                                            <th>Allocated</th>
                                            <th>Available</th>
                                            <th>Manufacturing Date</th>
                                            <th>Expiry Date</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for entry in inventory_entries %}
                                        <tr>
                                            <td>
                                                <strong>{{ entry.batch_number }}</strong>
                                            </td>
                                            <td>
                                                {% if entry.warehouse_name %}
                                                    {{ entry.warehouse_name }}
                                                {% else %}
                                                    <span class="text-muted">N/A</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <span class="badge badge-primary">
                                                    {{ entry.stock_quantity }}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge badge-warning">
                                                    {{ entry.allocated_quantity or 0 }}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge badge-success">
                                                    {{ (entry.stock_quantity or 0) - (entry.allocated_quantity or 0) }}
                                                </span>
                                            </td>
                                            <td>
                                                {% if entry.manufacturing_date %}
                                                    {{ entry.manufacturing_date }}
                                                {% else %}
                                                    <span class="text-muted">N/A</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if entry.expiry_date %}
                                                    {{ entry.expiry_date }}
                                                {% else %}
                                                    <span class="text-muted">N/A</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <span class="badge badge-{{ 'success' if entry.status == 'active' else 'secondary' }}">
                                                    {{ entry.status|title }}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <a href="{{ url_for('inventory.view_inventory', inventory_id=entry.inventory_id) }}" 
                                                       class="btn btn-outline-primary btn-sm" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ url_for('inventory.edit_inventory', inventory_id=entry.inventory_id) }}" 
                                                       class="btn btn-outline-secondary btn-sm" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="{{ url_for('inventory.history', inventory_id=entry.inventory_id) }}" 
                                                       class="btn btn-outline-info btn-sm" title="History">
                                                        <i class="fas fa-history"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                No inventory entries found for this product.
                                <a href="{{ url_for('inventory.new_inventory') }}" class="btn btn-primary btn-sm ml-2">
                                    <i class="fas fa-plus"></i> Add Inventory
                                </a>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row mt-4">
                        <div class="col-md-12 text-right">
                            <a href="{{ url_for('inventory.new_inventory') }}?product_id={{ product_info.product_id }}" 
                               class="btn btn-success">
                                <i class="fas fa-plus"></i> Add New Batch
                            </a>
                            <a href="{{ url_for('inventory.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Inventory
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
