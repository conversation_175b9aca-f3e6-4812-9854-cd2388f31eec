{% extends 'base.html' %}

{% block title %}Inventory Details - {{ inventory.product_name }} - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-warehouse text-primary"></i> Inventory Details
        </h1>
        <div>
            <a href="{{ url_for('inventory.index') }}" class="btn btn-secondary shadow-sm">
                <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Inventory
            </a>
            <a href="{{ url_for('inventory.edit_inventory', inventory_id=inventory.inventory_id) }}" class="btn btn-primary shadow-sm">
                <i class="fas fa-edit fa-sm text-white-50"></i> Edit Stock
            </a>
        </div>
    </div>

    <!-- Inventory Details Card -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 bg-primary text-white">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-info-circle"></i> Stock Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="text-primary">{{ inventory.product_name }}</h5>
                            {% if inventory.strength %}
                            <p class="text-muted mb-2">Strength: {{ inventory.strength }}</p>
                            {% endif %}
                            
                            <div class="mb-3">
                                <strong>Batch Number:</strong> 
                                <span class="badge badge-info">{{ inventory.batch_number }}</span>
                            </div>
                            
                            <div class="mb-3">
                                <strong>Warehouse:</strong> {{ inventory.warehouse_name or 'N/A' }}
                            </div>
                            
                            <div class="mb-3">
                                <strong>Location:</strong> {{ inventory.location_code or 'N/A' }}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <strong>Stock Quantity:</strong>
                                <span class="badge badge-{% if inventory.stock_quantity > 100 %}success{% elif inventory.stock_quantity > 50 %}warning{% else %}danger{% endif %} badge-lg">
                                    {{ inventory.stock_quantity }}
                                </span>
                            </div>
                            
                            <div class="mb-3">
                                <strong>Allocated:</strong>
                                <span class="badge badge-warning">{{ inventory.allocated_quantity or 0 }}</span>
                            </div>
                            
                            <div class="mb-3">
                                <strong>Available:</strong>
                                {% set available = inventory.stock_quantity - (inventory.allocated_quantity or 0) %}
                                <span class="badge badge-{% if available > 0 %}success{% else %}danger{% endif %}">
                                    {{ available }}
                                </span>
                            </div>
                            
                            <div class="mb-3">
                                <strong>Status:</strong>
                                {% if inventory.status == 'active' %}
                                <span class="badge badge-success">Active</span>
                                {% elif inventory.status == 'inactive' %}
                                <span class="badge badge-secondary">Inactive</span>
                                {% elif inventory.status == 'expired' %}
                                <span class="badge badge-danger">Expired</span>
                                {% else %}
                                <span class="badge badge-light">{{ inventory.status|title }}</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Dates Section -->
                    <hr>
                    <div class="row">
                        <div class="col-md-4">
                            {% if inventory.manufacturing_date %}
                            <div class="mb-2">
                                <strong>Manufacturing Date:</strong><br>
                                <span class="text-muted">{{ inventory.manufacturing_date }}</span>
                            </div>
                            {% endif %}
                        </div>
                        <div class="col-md-4">
                            {% if inventory.expiry_date %}
                            <div class="mb-2">
                                <strong>Expiry Date:</strong><br>
                                <span class="text-muted">
                                    {{ inventory.expiry_date | safe_date }}
                                </span>
                            </div>
                            {% endif %}
                        </div>
                        <div class="col-md-4">
                            {% if inventory.date_received %}
                            <div class="mb-2">
                                <strong>Date Received:</strong><br>
                                <span class="text-muted">{{ inventory.date_received }}</span>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Additional Info -->
                    {% if inventory.country_of_origin %}
                    <hr>
                    <div class="mb-2">
                        <strong>Country of Origin:</strong> {{ inventory.country_of_origin }}
                    </div>
                    {% endif %}
                    
                    {% if inventory.received_by %}
                    <div class="mb-2">
                        <strong>Received By:</strong> {{ inventory.received_by }}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Stock Movements -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3 bg-info text-white">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-history"></i> Stock Movements
                    </h6>
                </div>
                <div class="card-body">
                    {% if movements %}
                    <div class="timeline">
                        {% for movement in movements %}
                        <div class="timeline-item mb-3">
                            <div class="timeline-marker bg-{% if movement.movement_type == 'receipt' %}success{% elif movement.movement_type == 'allocation' %}warning{% elif movement.movement_type == 'transfer' %}info{% else %}secondary{% endif %}"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">{{ movement.movement_type|title }}</h6>
                                <p class="mb-1">
                                    <strong>Quantity:</strong> 
                                    <span class="text-{% if movement.quantity > 0 %}success{% else %}danger{% endif %}">
                                        {{ '+' if movement.quantity > 0 else '' }}{{ movement.quantity }}
                                    </span>
                                </p>
                                <small class="text-muted">
                                    {{ movement.movement_date }} by {{ movement.moved_by }}
                                </small>
                                {% if movement.notes %}
                                <p class="mb-0 mt-1"><small>{{ movement.notes }}</small></p>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center text-muted">
                        <i class="fas fa-history fa-2x mb-2"></i>
                        <p>No stock movements recorded</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e3e6f0;
}

.badge-lg {
    font-size: 1rem;
    padding: 8px 12px;
}
</style>
{% endblock %}
