{% extends 'base.html' %}

{% block title %}Notification Center - Medivent ERP{% endblock %}

{% block extra_css %}
<style>
    .notification-center {
        background: #f8f9fa;
        min-height: 100vh;
    }
    
    .notification-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
    }
    
    .notification-card {
        background: white;
        border: none;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        margin-bottom: 1rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .notification-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.12);
    }
    
    .notification-card.unread {
        border-left: 4px solid #007bff;
        background: linear-gradient(90deg, #f8f9ff 0%, #ffffff 100%);
    }
    
    .notification-card.unread::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 2px;
        background: linear-gradient(90deg, #007bff, #0056b3);
    }
    
    .notification-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        color: white;
        margin-right: 1rem;
    }
    
    .notification-content {
        flex: 1;
    }
    
    .notification-title {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.25rem;
        font-size: 1rem;
    }
    
    .notification-message {
        color: #6c757d;
        font-size: 0.9rem;
        line-height: 1.4;
        margin-bottom: 0.5rem;
    }
    
    .notification-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.8rem;
        color: #adb5bd;
    }
    
    .notification-time {
        display: flex;
        align-items: center;
    }
    
    .notification-actions {
        display: flex;
        gap: 0.5rem;
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .notification-card:hover .notification-actions {
        opacity: 1;
    }
    
    .priority-badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.5rem;
        border-radius: 12px;
        font-weight: 500;
        text-transform: uppercase;
    }
    
    .priority-urgent {
        background: #dc3545;
        color: white;
        animation: pulse-urgent 2s infinite;
    }
    
    .priority-high {
        background: #fd7e14;
        color: white;
    }
    
    .priority-medium {
        background: #ffc107;
        color: #212529;
    }
    
    .priority-low {
        background: #e9ecef;
        color: #6c757d;
    }
    
    @keyframes pulse-urgent {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
    }
    
    .filter-tabs {
        background: white;
        border-radius: 12px;
        padding: 0.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    }
    
    .filter-tab {
        border: none;
        background: transparent;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        color: #6c757d;
        font-weight: 500;
        transition: all 0.3s ease;
        margin-right: 0.5rem;
    }
    
    .filter-tab.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    
    .stats-card {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        text-align: center;
        box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        margin-bottom: 1rem;
    }
    
    .stats-number {
        font-size: 2rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }
    
    .stats-label {
        color: #6c757d;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        color: #6c757d;
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.3;
    }
    
    .notification-type-filter {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 0.5rem 1rem;
        margin-bottom: 1rem;
    }
    
    .bulk-actions {
        background: white;
        border-radius: 12px;
        padding: 1rem;
        margin-bottom: 1rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        display: none;
    }
    
    .bulk-actions.show {
        display: block;
    }
    
    @media (max-width: 768px) {
        .notification-card {
            margin-bottom: 0.5rem;
        }
        
        .notification-icon {
            width: 40px;
            height: 40px;
            font-size: 1rem;
        }
        
        .notification-title {
            font-size: 0.9rem;
        }
        
        .notification-message {
            font-size: 0.8rem;
        }
        
        .filter-tab {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="notification-center">
    <!-- Header -->
    <div class="notification-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="fas fa-bell me-3"></i>
                        Notification Center
                    </h1>
                    <p class="mb-0 opacity-75">Stay updated with all your important notifications</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light btn-lg" onclick="markAllAsRead()">
                        <i class="fas fa-check-double me-2"></i>
                        Mark All Read
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="row">
            <!-- Sidebar with Stats -->
            <div class="col-lg-3">
                <!-- Statistics -->
                <div class="stats-card">
                    <div class="stats-number text-primary">{{ stats.total_notifications }}</div>
                    <div class="stats-label">Total Notifications</div>
                </div>
                
                <div class="stats-card">
                    <div class="stats-number text-warning">{{ stats.unread_notifications }}</div>
                    <div class="stats-label">Unread</div>
                </div>
                
                <div class="stats-card">
                    <div class="stats-number text-success">{{ "%.1f"|format(stats.read_rate) }}%</div>
                    <div class="stats-label">Read Rate</div>
                </div>

                <!-- Type Filter -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Filter by Type</h6>
                    </div>
                    <div class="card-body">
                        <select class="form-select notification-type-filter" onchange="filterByType(this.value)">
                            {% for type in notification_types %}
                            <option value="{{ type.value }}" {% if type.value == current_filter_type %}selected{% endif %}>
                                {{ type.label }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9">
                <!-- Filter Tabs -->
                <div class="filter-tabs">
                    <button class="filter-tab {% if current_filter_status == 'all' %}active{% endif %}" 
                            onclick="filterByStatus('all')">
                        All Notifications
                    </button>
                    <button class="filter-tab {% if current_filter_status == 'unread' %}active{% endif %}" 
                            onclick="filterByStatus('unread')">
                        Unread Only
                    </button>
                    <button class="filter-tab" onclick="filterByStatus('read')">
                        Read Only
                    </button>
                </div>

                <!-- Bulk Actions -->
                <div class="bulk-actions" id="bulkActions">
                    <div class="d-flex justify-content-between align-items-center">
                        <span id="selectedCount">0 notifications selected</span>
                        <div>
                            <button class="btn btn-sm btn-outline-primary me-2" onclick="bulkMarkRead()">
                                <i class="fas fa-check me-1"></i> Mark Read
                            </button>
                            <button class="btn btn-sm btn-outline-warning me-2" onclick="bulkArchive()">
                                <i class="fas fa-archive me-1"></i> Archive
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="bulkDelete()">
                                <i class="fas fa-trash me-1"></i> Delete
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Notifications List -->
                <div id="notificationsList">
                    {% if notifications %}
                        {% for notification in notifications %}
                        <div class="notification-card {% if not notification.is_read %}unread{% endif %}" 
                             data-id="{{ notification.id }}" data-type="{{ notification.type_name }}">
                            <div class="card-body">
                                <div class="d-flex align-items-start">
                                    <!-- Selection Checkbox -->
                                    <div class="form-check me-3">
                                        <input class="form-check-input notification-checkbox" type="checkbox" 
                                               value="{{ notification.id }}" onchange="updateBulkActions()">
                                    </div>

                                    <!-- Notification Icon -->
                                    <div class="notification-icon bg-{{ notification.color_class }}">
                                        <i class="{{ notification.icon_class }}"></i>
                                    </div>

                                    <!-- Notification Content -->
                                    <div class="notification-content">
                                        <div class="notification-title">{{ notification.title }}</div>
                                        <div class="notification-message">{{ notification.message }}</div>
                                        
                                        <div class="notification-meta">
                                            <div class="notification-time">
                                                <i class="fas fa-clock me-1"></i>
                                                <span class="time-ago" data-time="{{ notification.created_at }}">
                                                    {{ notification.created_at }}
                                                </span>
                                            </div>
                                            
                                            <div class="d-flex align-items-center">
                                                <span class="priority-badge priority-{{ notification.priority_label.lower() }}">
                                                    {{ notification.priority_label }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Notification Actions -->
                                    <div class="notification-actions">
                                        {% if not notification.is_read %}
                                        <button class="btn btn-sm btn-outline-primary" 
                                                onclick="markAsRead({{ notification.id }})">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        {% endif %}
                                        
                                        <button class="btn btn-sm btn-outline-warning" 
                                                onclick="archiveNotification({{ notification.id }})">
                                            <i class="fas fa-archive"></i>
                                        </button>
                                        
                                        <button class="btn btn-sm btn-outline-danger" 
                                                onclick="deleteNotification({{ notification.id }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Additional Data (if available) -->
                                {% if notification.data %}
                                <div class="mt-2">
                                    {% if notification.data.action_url %}
                                    <a href="{{ notification.data.action_url }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-external-link-alt me-1"></i>
                                        View Details
                                    </a>
                                    {% endif %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-bell-slash"></i>
                            <h4>No Notifications</h4>
                            <p>You're all caught up! No notifications to display.</p>
                        </div>
                    {% endif %}
                </div>

                <!-- Load More Button -->
                {% if notifications|length >= 20 %}
                <div class="text-center mt-4">
                    <button class="btn btn-outline-primary btn-lg" onclick="loadMoreNotifications()">
                        <i class="fas fa-chevron-down me-2"></i>
                        Load More Notifications
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Notification Center JavaScript
let currentPage = {{ page }};
let isLoading = false;

// Initialize notification center
document.addEventListener('DOMContentLoaded', function() {
    updateTimeAgo();
    setInterval(updateTimeAgo, 60000); // Update every minute

    // Auto-refresh notifications every 30 seconds
    setInterval(refreshNotifications, 30000);
});

// Update time ago display
function updateTimeAgo() {
    document.querySelectorAll('.time-ago').forEach(function(element) {
        const time = element.getAttribute('data-time');
        element.textContent = formatTimeAgo(new Date(time));
    });
}

// Format time ago
function formatTimeAgo(date) {
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return Math.floor(diffInSeconds / 60) + ' minutes ago';
    if (diffInSeconds < 86400) return Math.floor(diffInSeconds / 3600) + ' hours ago';
    if (diffInSeconds < 604800) return Math.floor(diffInSeconds / 86400) + ' days ago';

    return date.toLocaleDateString();
}

// Mark notification as read
function markAsRead(notificationId) {
    fetch(`/notifications/api/mark-read/${notificationId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const card = document.querySelector(`[data-id="${notificationId}"]`);
            card.classList.remove('unread');

            // Remove mark as read button
            const readBtn = card.querySelector('.btn-outline-primary');
            if (readBtn && readBtn.innerHTML.includes('fa-check')) {
                readBtn.remove();
            }

            // Update unread count
            updateUnreadCount();

            showToast('Notification marked as read', 'success');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('Failed to mark as read', 'error');
    });
}

// Mark all notifications as read
function markAllAsRead() {
    if (!confirm('Mark all notifications as read?')) return;

    fetch('/notifications/api/mark-all-read', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove unread class from all cards
            document.querySelectorAll('.notification-card.unread').forEach(card => {
                card.classList.remove('unread');
            });

            // Remove all mark as read buttons
            document.querySelectorAll('.btn-outline-primary').forEach(btn => {
                if (btn.innerHTML.includes('fa-check')) {
                    btn.remove();
                }
            });

            updateUnreadCount();
            showToast(`Marked ${data.marked_count} notifications as read`, 'success');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('Failed to mark all as read', 'error');
    });
}

// Archive notification
function archiveNotification(notificationId) {
    fetch(`/notifications/api/archive/${notificationId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const card = document.querySelector(`[data-id="${notificationId}"]`);
            card.style.opacity = '0.5';
            setTimeout(() => card.remove(), 300);

            showToast('Notification archived', 'success');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('Failed to archive notification', 'error');
    });
}

// Delete notification
function deleteNotification(notificationId) {
    if (!confirm('Delete this notification permanently?')) return;

    fetch(`/notifications/api/delete/${notificationId}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const card = document.querySelector(`[data-id="${notificationId}"]`);
            card.style.opacity = '0';
            setTimeout(() => card.remove(), 300);

            showToast('Notification deleted', 'success');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('Failed to delete notification', 'error');
    });
}

// Filter by status
function filterByStatus(status) {
    const url = new URL(window.location);
    url.searchParams.set('status', status);
    url.searchParams.set('page', '1');
    window.location.href = url.toString();
}

// Filter by type
function filterByType(type) {
    const url = new URL(window.location);
    url.searchParams.set('type', type);
    url.searchParams.set('page', '1');
    window.location.href = url.toString();
}

// Update bulk actions visibility
function updateBulkActions() {
    const checkboxes = document.querySelectorAll('.notification-checkbox:checked');
    const bulkActions = document.getElementById('bulkActions');
    const selectedCount = document.getElementById('selectedCount');

    if (checkboxes.length > 0) {
        bulkActions.classList.add('show');
        selectedCount.textContent = `${checkboxes.length} notification${checkboxes.length > 1 ? 's' : ''} selected`;
    } else {
        bulkActions.classList.remove('show');
    }
}

// Bulk mark as read
function bulkMarkRead() {
    const checkboxes = document.querySelectorAll('.notification-checkbox:checked');
    const ids = Array.from(checkboxes).map(cb => cb.value);

    if (ids.length === 0) return;

    Promise.all(ids.map(id =>
        fetch(`/notifications/api/mark-read/${id}`, { method: 'POST' })
    ))
    .then(() => {
        checkboxes.forEach(cb => {
            const card = cb.closest('.notification-card');
            card.classList.remove('unread');
            cb.checked = false;
        });

        updateBulkActions();
        updateUnreadCount();
        showToast(`Marked ${ids.length} notifications as read`, 'success');
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('Failed to mark notifications as read', 'error');
    });
}

// Bulk archive
function bulkArchive() {
    const checkboxes = document.querySelectorAll('.notification-checkbox:checked');
    const ids = Array.from(checkboxes).map(cb => cb.value);

    if (ids.length === 0) return;

    Promise.all(ids.map(id =>
        fetch(`/notifications/api/archive/${id}`, { method: 'POST' })
    ))
    .then(() => {
        checkboxes.forEach(cb => {
            const card = cb.closest('.notification-card');
            card.style.opacity = '0.5';
            setTimeout(() => card.remove(), 300);
        });

        updateBulkActions();
        showToast(`Archived ${ids.length} notifications`, 'success');
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('Failed to archive notifications', 'error');
    });
}

// Bulk delete
function bulkDelete() {
    const checkboxes = document.querySelectorAll('.notification-checkbox:checked');
    const ids = Array.from(checkboxes).map(cb => cb.value);

    if (ids.length === 0) return;
    if (!confirm(`Delete ${ids.length} notifications permanently?`)) return;

    Promise.all(ids.map(id =>
        fetch(`/notifications/api/delete/${id}`, { method: 'DELETE' })
    ))
    .then(() => {
        checkboxes.forEach(cb => {
            const card = cb.closest('.notification-card');
            card.style.opacity = '0';
            setTimeout(() => card.remove(), 300);
        });

        updateBulkActions();
        showToast(`Deleted ${ids.length} notifications`, 'success');
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('Failed to delete notifications', 'error');
    });
}

// Update unread count in header
function updateUnreadCount() {
    fetch('/notifications/api/unread-count')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const badge = document.querySelector('.notification-badge');
            if (badge) {
                if (data.unread_count > 0) {
                    badge.textContent = data.unread_count > 99 ? '99+' : data.unread_count;
                    badge.style.display = 'inline-block';
                } else {
                    badge.style.display = 'none';
                }
            }
        }
    })
    .catch(error => console.error('Error updating unread count:', error));
}

// Refresh notifications
function refreshNotifications() {
    fetch('/notifications/api/notifications?limit=20')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateUnreadCount();
        }
    })
    .catch(error => console.error('Error refreshing notifications:', error));
}

// Show toast notification
function showToast(message, type = 'info') {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'primary'} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    // Add to toast container
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }

    toastContainer.appendChild(toast);

    // Initialize and show toast
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    // Remove toast element after it's hidden
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}
</script>
{% endblock %}
