{% extends 'base.html' %}

{% block title %}Test Notifications - Medivent ERP{% endblock %}

{% block extra_css %}
<style>
    .test-container {
        background: #f8f9fa;
        min-height: 100vh;
        padding: 2rem 0;
    }
    
    .test-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        margin-bottom: 2rem;
    }
    
    .test-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 12px 12px 0 0;
    }
    
    .notification-preview {
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
        background: #f8f9fa;
    }
    
    .notification-type-card {
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .notification-type-card:hover {
        border-color: #007bff;
        background: #f8f9ff;
    }
    
    .notification-type-card.selected {
        border-color: #007bff;
        background: #e7f3ff;
    }
    
    .priority-selector {
        display: flex;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }
    
    .priority-btn {
        padding: 0.5rem 1rem;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        background: white;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .priority-btn.selected {
        background: #007bff;
        color: white;
        border-color: #007bff;
    }
    
    .priority-btn.low { border-color: #6c757d; }
    .priority-btn.medium { border-color: #ffc107; }
    .priority-btn.high { border-color: #fd7e14; }
    .priority-btn.urgent { border-color: #dc3545; }
    
    .priority-btn.low.selected { background: #6c757d; }
    .priority-btn.medium.selected { background: #ffc107; color: #212529; }
    .priority-btn.high.selected { background: #fd7e14; }
    .priority-btn.urgent.selected { background: #dc3545; }
</style>
{% endblock %}

{% block content %}
<div class="test-container">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <!-- Header -->
                <div class="test-card">
                    <div class="test-header">
                        <h1 class="mb-2">
                            <i class="fas fa-flask me-3"></i>
                            Notification System Testing
                        </h1>
                        <p class="mb-0 opacity-75">Create and test different types of notifications</p>
                    </div>
                    
                    <div class="card-body">
                        <!-- Quick Test Buttons -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <button class="btn btn-success btn-lg w-100 mb-2" onclick="createTestNotifications()">
                                    <i class="fas fa-magic me-2"></i>
                                    Create Sample Notifications
                                </button>
                            </div>
                            <div class="col-md-6">
                                <a href="/notifications" class="btn btn-primary btn-lg w-100 mb-2">
                                    <i class="fas fa-bell me-2"></i>
                                    View Notification Center
                                </a>
                            </div>
                        </div>
                        
                        <!-- Custom Notification Creator -->
                        <h4 class="mb-3">Create Custom Notification</h4>
                        
                        <form id="customNotificationForm">
                            <!-- Notification Type Selection -->
                            <div class="mb-3">
                                <label class="form-label">Notification Type</label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="notification-type-card" data-type="order_placed">
                                            <div class="d-flex align-items-center">
                                                <div class="me-3">
                                                    <i class="fas fa-shopping-cart text-success fa-2x"></i>
                                                </div>
                                                <div>
                                                    <h6 class="mb-1">Order Placed</h6>
                                                    <small class="text-muted">New order notification</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="notification-type-card" data-type="inventory_low">
                                            <div class="d-flex align-items-center">
                                                <div class="me-3">
                                                    <i class="fas fa-exclamation-triangle text-warning fa-2x"></i>
                                                </div>
                                                <div>
                                                    <h6 class="mb-1">Low Inventory</h6>
                                                    <small class="text-muted">Stock alert notification</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="notification-type-card" data-type="order_approved">
                                            <div class="d-flex align-items-center">
                                                <div class="me-3">
                                                    <i class="fas fa-check-circle text-info fa-2x"></i>
                                                </div>
                                                <div>
                                                    <h6 class="mb-1">Order Approved</h6>
                                                    <small class="text-muted">Approval notification</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="notification-type-card" data-type="system_alert">
                                            <div class="d-flex align-items-center">
                                                <div class="me-3">
                                                    <i class="fas fa-cog text-secondary fa-2x"></i>
                                                </div>
                                                <div>
                                                    <h6 class="mb-1">System Alert</h6>
                                                    <small class="text-muted">System notification</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Priority Selection -->
                            <div class="mb-3">
                                <label class="form-label">Priority Level</label>
                                <div class="priority-selector">
                                    <div class="priority-btn low" data-priority="low">
                                        <i class="fas fa-circle me-1"></i> Low
                                    </div>
                                    <div class="priority-btn medium selected" data-priority="medium">
                                        <i class="fas fa-circle me-1"></i> Medium
                                    </div>
                                    <div class="priority-btn high" data-priority="high">
                                        <i class="fas fa-circle me-1"></i> High
                                    </div>
                                    <div class="priority-btn urgent" data-priority="urgent">
                                        <i class="fas fa-circle me-1"></i> Urgent
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Title and Message -->
                            <div class="mb-3">
                                <label for="notificationTitle" class="form-label">Title</label>
                                <input type="text" class="form-control" id="notificationTitle" 
                                       placeholder="Enter notification title" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="notificationMessage" class="form-label">Message</label>
                                <textarea class="form-control" id="notificationMessage" rows="3" 
                                          placeholder="Enter notification message" required></textarea>
                            </div>
                            
                            <!-- Additional Data -->
                            <div class="mb-3">
                                <label for="notificationData" class="form-label">Additional Data (JSON)</label>
                                <textarea class="form-control" id="notificationData" rows="3" 
                                          placeholder='{"order_id": "ORD-001", "customer_name": "John Doe"}'></textarea>
                                <small class="form-text text-muted">Optional: Additional data in JSON format</small>
                            </div>
                            
                            <!-- Preview -->
                            <div class="mb-3">
                                <label class="form-label">Preview</label>
                                <div class="notification-preview" id="notificationPreview">
                                    <div class="d-flex align-items-start">
                                        <div class="me-3">
                                            <div class="notification-icon bg-primary text-white" 
                                                 style="width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                                <i class="fas fa-bell"></i>
                                            </div>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1" id="previewTitle">Sample Notification Title</h6>
                                            <p class="mb-1 text-muted" id="previewMessage">Sample notification message will appear here...</p>
                                            <small class="text-muted">
                                                <i class="fas fa-clock me-1"></i>
                                                Just now
                                                <span class="ms-2 badge bg-secondary" id="previewPriority">Medium</span>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Submit Button -->
                            <button type="submit" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-paper-plane me-2"></i>
                                Create Notification
                            </button>
                        </form>
                    </div>
                </div>
                
                <!-- Statistics -->
                <div class="test-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            Notification Statistics
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row" id="notificationStats">
                            <div class="col-md-3 text-center">
                                <h3 class="text-primary mb-1" id="totalCount">0</h3>
                                <small class="text-muted">Total</small>
                            </div>
                            <div class="col-md-3 text-center">
                                <h3 class="text-warning mb-1" id="unreadCount">0</h3>
                                <small class="text-muted">Unread</small>
                            </div>
                            <div class="col-md-3 text-center">
                                <h3 class="text-success mb-1" id="readRate">0%</h3>
                                <small class="text-muted">Read Rate</small>
                            </div>
                            <div class="col-md-3 text-center">
                                <button class="btn btn-outline-primary btn-sm" onclick="refreshStats()">
                                    <i class="fas fa-sync-alt"></i> Refresh
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let selectedType = 'order_placed';
let selectedPriority = 'medium';

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Set up event listeners
    setupEventListeners();
    
    // Load initial stats
    refreshStats();
    
    // Update preview
    updatePreview();
});

function setupEventListeners() {
    // Notification type selection
    document.querySelectorAll('.notification-type-card').forEach(card => {
        card.addEventListener('click', function() {
            document.querySelectorAll('.notification-type-card').forEach(c => c.classList.remove('selected'));
            this.classList.add('selected');
            selectedType = this.getAttribute('data-type');
            updatePreview();
        });
    });
    
    // Priority selection
    document.querySelectorAll('.priority-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            document.querySelectorAll('.priority-btn').forEach(b => b.classList.remove('selected'));
            this.classList.add('selected');
            selectedPriority = this.getAttribute('data-priority');
            updatePreview();
        });
    });
    
    // Form inputs
    document.getElementById('notificationTitle').addEventListener('input', updatePreview);
    document.getElementById('notificationMessage').addEventListener('input', updatePreview);
    
    // Form submission
    document.getElementById('customNotificationForm').addEventListener('submit', function(e) {
        e.preventDefault();
        createCustomNotification();
    });
}

function updatePreview() {
    const title = document.getElementById('notificationTitle').value || 'Sample Notification Title';
    const message = document.getElementById('notificationMessage').value || 'Sample notification message will appear here...';
    
    document.getElementById('previewTitle').textContent = title;
    document.getElementById('previewMessage').textContent = message;
    document.getElementById('previewPriority').textContent = selectedPriority.charAt(0).toUpperCase() + selectedPriority.slice(1);
    
    // Update priority badge color
    const priorityBadge = document.getElementById('previewPriority');
    priorityBadge.className = 'ms-2 badge';
    switch(selectedPriority) {
        case 'low': priorityBadge.classList.add('bg-secondary'); break;
        case 'medium': priorityBadge.classList.add('bg-warning', 'text-dark'); break;
        case 'high': priorityBadge.classList.add('bg-orange'); break;
        case 'urgent': priorityBadge.classList.add('bg-danger'); break;
    }
}

function createTestNotifications() {
    const btn = event.target;
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating...';
    
    fetch('/notifications/api/create-test-notifications', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(`Created ${data.created_count} test notifications!`, 'success');
            refreshStats();
        } else {
            showToast('Failed to create test notifications', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('Error creating test notifications', 'error');
    })
    .finally(() => {
        btn.disabled = false;
        btn.innerHTML = '<i class="fas fa-magic me-2"></i>Create Sample Notifications';
    });
}

function createCustomNotification() {
    const title = document.getElementById('notificationTitle').value;
    const message = document.getElementById('notificationMessage').value;
    const dataText = document.getElementById('notificationData').value;
    
    let additionalData = {};
    if (dataText.trim()) {
        try {
            additionalData = JSON.parse(dataText);
        } catch (e) {
            showToast('Invalid JSON in additional data field', 'error');
            return;
        }
    }
    
    const notificationData = {
        type: selectedType,
        title: title,
        message: message,
        priority: selectedPriority,
        data: additionalData
    };
    
    fetch('/notifications/api/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(notificationData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('Notification created successfully!', 'success');
            
            // Reset form
            document.getElementById('customNotificationForm').reset();
            selectedPriority = 'medium';
            document.querySelectorAll('.priority-btn').forEach(b => b.classList.remove('selected'));
            document.querySelector('[data-priority="medium"]').classList.add('selected');
            updatePreview();
            
            // Refresh stats
            refreshStats();
        } else {
            showToast(data.message || 'Failed to create notification', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('Error creating notification', 'error');
    });
}

function refreshStats() {
    fetch('/notifications/api/stats')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const stats = data.stats;
            document.getElementById('totalCount').textContent = stats.total_notifications;
            document.getElementById('unreadCount').textContent = stats.unread_notifications;
            document.getElementById('readRate').textContent = stats.read_rate.toFixed(1) + '%';
        }
    })
    .catch(error => {
        console.error('Error refreshing stats:', error);
    });
}

function showToast(message, type = 'info') {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'primary'} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    // Add to toast container
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }
    
    toastContainer.appendChild(toast);
    
    // Initialize and show toast
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    // Remove toast element after it's hidden
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}
</script>
{% endblock %}
