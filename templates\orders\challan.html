{% extends "base.html" %}

{% block title %}Delivery Challan - {{ challan.dc_number }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-truck"></i> Delivery Challan - {{ challan.dc_number }}
        </h1>
        <div>
            {% if challan.pdf_path %}
            <a href="{{ challan.pdf_path }}" class="btn btn-success" target="_blank">
                <i class="fas fa-file-pdf"></i> Download PDF
            </a>
            {% endif %}
            <a href="{{ url_for('orders.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Orders
            </a>
        </div>
    </div>

    <!-- <PERSON><PERSON> Details -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Delivery Challan Details</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>DC Number:</strong> {{ challan.dc_number }}</p>
                            <p><strong>Order ID:</strong> {{ order.order_id }}</p>
                            <p><strong>Customer:</strong> {{ order.customer_name }}</p>
                            <p><strong>Address:</strong> {{ order.customer_address }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Created Date:</strong> {{ ultra_safe_date_format(challan.created_at, '%Y-%m-%d %H:%M') }}</p>
                            <p><strong>Status:</strong> 
                                <span class="badge badge-{% if challan.status == 'created' %}warning{% elif challan.status == 'dispatched' %}info{% elif challan.status == 'delivered' %}success{% else %}secondary{% endif %}">
                                    {{ challan.status|title }}
                                </span>
                            </p>
                            <p><strong>Created By:</strong> {{ challan.created_by or 'System' }}</p>
                            <p><strong>Total Amount:</strong> Rs. {{ "{:,.2f}".format(challan.total_amount or 0) }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    {% if challan.status == 'created' %}
                    <button class="btn btn-info btn-block mb-2">
                        <i class="fas fa-shipping-fast"></i> Mark as Dispatched
                    </button>
                    {% endif %}
                    
                    {% if challan.status == 'dispatched' %}
                    <button class="btn btn-success btn-block mb-2">
                        <i class="fas fa-check-circle"></i> Mark as Delivered
                    </button>
                    {% endif %}
                    
                    <button class="btn btn-primary btn-block mb-2">
                        <i class="fas fa-print"></i> Print Challan
                    </button>
                    
                    <button class="btn btn-warning btn-block">
                        <i class="fas fa-edit"></i> Edit Details
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Order Items -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Order Items</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead class="thead-light">
                        <tr>
                            <th>Product</th>
                            <th>Strength</th>
                            <th>Manufacturer</th>
                            <th>Quantity</th>
                            <th>Unit Price</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in order_items %}
                        <tr>
                            <td>{{ item.product_name or item.product_id }}</td>
                            <td>{{ item.strength or 'N/A' }}</td>
                            <td>{{ item.manufacturer or 'N/A' }}</td>
                            <td>{{ item.quantity }}</td>
                            <td>Rs. {{ "{:,.2f}".format(item.unit_price or 0) }}</td>
                            <td>Rs. {{ "{:,.2f}".format(item.total_price or 0) }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Batch Details -->
    {% if challan.batch_details %}
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Batch Allocation Details</h6>
        </div>
        <div class="card-body">
            <pre>{{ challan.batch_details }}</pre>
        </div>
    </div>
    {% endif %}

    <!-- Notes -->
    {% if challan.notes %}
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Notes</h6>
        </div>
        <div class="card-body">
            <p>{{ challan.notes }}</p>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
