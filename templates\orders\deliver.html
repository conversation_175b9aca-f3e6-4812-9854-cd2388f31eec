{% extends 'base.html' %}

{% block title %}Deliver Order - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Deliver Order #{{ order.order_id }}</h4>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>Order Details</h5>
                            <table class="table table-bordered">
                                <tr>
                                    <th>Order ID:</th>
                                    <td>{{ order.order_id }}</td>
                                </tr>
                                <tr>
                                    <th>Customer:</th>
                                    <td>{{ order.customer_name }}</td>
                                </tr>
                                <tr>
                                    <th>Address:</th>
                                    <td>
                                        {{ order.customer_address }}
                                        {% if order.customer_address %}
                                        <br>
                                        <button type="button" class="btn btn-sm btn-outline-primary mt-1" onclick="openDeliveryAddressOnMap('{{ order.customer_address }}', '{{ order.customer_name }}')">
                                            <i class="fas fa-map-marker-alt"></i> View on Map
                                        </button>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>Phone:</th>
                                    <td>{{ order.customer_phone }}</td>
                                </tr>
                                <tr>
                                    <th>Status:</th>
                                    <td>
                                        <span class="badge badge-dark">{{ order.status }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Order Date:</th>
                                    <td>{{ order.order_date }}</td>
                                </tr>
                                <tr>
                                    <th>Dispatch Date:</th>
                                    <td>{{ order.dispatch_date }}</td>
                                </tr>
                                <tr>
                                    <th>Rider:</th>
                                    <td>{{ order.rider_id }}</td>
                                </tr>
                                <tr>
                                    <th>Total Amount:</th>
                                    <td>Rs. {{ order.order_amount }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>Delivery Information</h5>
                            <form method="post" action="{{ url_for('deliver_order', order_id=order.order_id) }}">
                                <div class="form-group">
                                    <label for="feedback">Customer Feedback (Optional)</label>
                                    <textarea class="form-control" id="feedback" name="feedback" rows="5"></textarea>
                                </div>
                                <div class="form-group text-right">
                                    <a href="{{ url_for('workflow') }}" class="btn btn-secondary">Cancel</a>
                                    <button type="submit" class="btn btn-success">Mark as Delivered</button>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <h5>Order Items</h5>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Strength</th>
                                    <th>Quantity</th>
                                    <th>Unit Price</th>
                                    <th>Line Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in order_items %}
                                <tr>
                                    <td>{{ item.product_name }}</td>
                                    <td>{{ item.strength }}</td>
                                    <td>{{ item.quantity }}</td>
                                    <td>Rs. {{ item.unit_price }}</td>
                                    <td>Rs. {{ item.line_total }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="4" class="text-right">Total:</th>
                                    <th>Rs. {{ order.order_amount }}</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Google Maps integration function for delivery addresses
function openDeliveryAddressOnMap(address, customerName) {
    if (!address || address.trim() === '') {
        alert('No delivery address available');
        return;
    }

    // Create Google Maps URL with the address
    const encodedAddress = encodeURIComponent(address.trim());
    const googleMapsUrl = `https://www.google.com/maps/search/?api=1&query=${encodedAddress}`;

    // Open in new tab
    window.open(googleMapsUrl, '_blank');

    console.log(`Opening Google Maps for delivery to ${customerName}: ${address}`);
}
</script>
{% endblock %}
