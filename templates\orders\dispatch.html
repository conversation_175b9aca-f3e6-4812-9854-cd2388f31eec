{% extends 'base.html' %}

{% block title %}Dispatch Order - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Dispatch Order #{{ order.order_id }}</h4>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>Order Details</h5>
                            <table class="table table-bordered">
                                <tr>
                                    <th>Order ID:</th>
                                    <td>{{ order.order_id }}</td>
                                </tr>
                                <tr>
                                    <th>Customer:</th>
                                    <td>{{ order.customer_name }}</td>
                                </tr>
                                <tr>
                                    <th>Address:</th>
                                    <td>{{ order.customer_address }}</td>
                                </tr>
                                <tr>
                                    <th>Phone:</th>
                                    <td>{{ order.customer_phone }}</td>
                                </tr>
                                <tr>
                                    <th>Status:</th>
                                    <td>
                                        <span class="badge
                                            {% if order.status == 'Placed' %}badge-warning
                                            {% elif order.status == 'Approved' %}badge-primary
                                            {% elif order.status == 'Processing' %}badge-info
                                            {% elif order.status == 'Ready for Pickup' %}badge-secondary
                                            {% elif order.status == 'Dispatched' %}badge-dark
                                            {% elif order.status == 'Delivered' %}badge-success
                                            {% elif order.status == 'Cancelled' %}badge-danger
                                            {% elif order.status == 'Pending' %}badge-warning
                                            {% endif %}">
                                            {{ order.status }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Order Date:</th>
                                    <td>{{ order.order_date }}</td>
                                </tr>
                                <tr>
                                    <th>Total Amount:</th>
                                    <td>Rs. {{ order.order_amount }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>Dispatch Information</h5>
                            <form method="post" action="{{ url_for('orders_dispatch_order', order_id=order.order_id) }}">
                                <div class="form-group">
                                    <label for="rider_name">Rider Name</label>
                                    <input type="text" class="form-control" id="rider_name" name="rider_name" required>
                                </div>
                                <div class="form-group">
                                    <label for="rider_contact">Rider Contact</label>
                                    <input type="text" class="form-control" id="rider_contact" name="rider_contact">
                                </div>
                                <div class="form-group">
                                    <label for="dispatch_notes">Dispatch Notes (Optional)</label>
                                    <textarea class="form-control" id="dispatch_notes" name="dispatch_notes" rows="3"></textarea>
                                </div>
                                <div class="form-group text-right">
                                    <a href="{{ url_for('workflow') }}" class="btn btn-secondary">Cancel</a>
                                    <button type="submit" class="btn btn-primary">Dispatch Order</button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <h5>Order Items</h5>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Strength</th>
                                    <th>Quantity</th>
                                    <th>FOC Qty</th>
                                    <th>Total Qty</th>
                                    <th>Unit Price</th>
                                    <th>Line Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in order_items %}
                                <tr>
                                    <td>{{ item.product_name }}</td>
                                    <td>{{ item.strength }}</td>
                                    <td>{{ item.quantity }}</td>
                                    <td>
                                        {% if item.foc_quantity and item.foc_quantity > 0 %}
                                            <span class="badge badge-success">{{ item.foc_quantity }}</span>
                                        {% else %}
                                            <span class="text-muted">0</span>
                                        {% endif %}
                                    </td>
                                    <td><strong>{{ item.quantity + (item.foc_quantity or 0) }}</strong></td>
                                    <td>Rs. {{ item.unit_price }}</td>
                                    <td>Rs. {{ item.line_total }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="6" class="text-right">Total:</th>
                                    <th>Rs. {{ order.order_amount }}</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
