{% extends 'base.html' %}

{% block title %}Advanced Order Search - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-search"></i> Advanced Order Search
                    </h4>
                    <small>Search and filter orders with advanced criteria</small>
                </div>
            </div>

            <!-- Search Filters -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-filter"></i> Search Filters</h5>
                </div>
                <div class="card-body">
                    <form method="GET" id="searchForm">
                        <div class="row">
                            <!-- Basic Search -->
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="q">Search Query</label>
                                    <input type="text" class="form-control" id="q" name="q" 
                                           value="{{ search_query }}" 
                                           placeholder="Order ID, Customer, Phone, Invoice...">
                                </div>
                            </div>

                            <!-- Status Filter -->
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="status">Order Status</label>
                                    <select class="form-control" id="status" name="status">
                                        <option value="">All Statuses</option>
                                        {% for status in statuses %}
                                        <option value="{{ status }}" {% if status == status_filter %}selected{% endif %}>
                                            {{ status }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>

                            <!-- Customer Filter -->
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="customer">Customer Name</label>
                                    <input type="text" class="form-control" id="customer" name="customer" 
                                           value="{{ customer_filter }}" 
                                           placeholder="Filter by customer name">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Date Range -->
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="date_from">Date From</label>
                                    <input type="date" class="form-control" id="date_from" name="date_from" 
                                           value="{{ date_from }}">
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="date_to">Date To</label>
                                    <input type="date" class="form-control" id="date_to" name="date_to" 
                                           value="{{ date_to }}">
                                </div>
                            </div>

                            <!-- Amount Range -->
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="amount_min">Min Amount</label>
                                    <input type="number" class="form-control" id="amount_min" name="amount_min" 
                                           value="{{ amount_min }}" step="0.01" min="0" 
                                           placeholder="0.00">
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="amount_max">Max Amount</label>
                                    <input type="number" class="form-control" id="amount_max" name="amount_max" 
                                           value="{{ amount_max }}" step="0.01" min="0" 
                                           placeholder="999999.99">
                                </div>
                            </div>
                        </div>

                        <!-- Search Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> Search Orders
                                </button>
                                <a href="{{ url_for('orders_enhanced.advanced_search') }}" class="btn btn-secondary ml-2">
                                    <i class="fas fa-times"></i> Clear Filters
                                </a>
                                <button type="button" class="btn btn-info ml-2" onclick="exportResults()">
                                    <i class="fas fa-download"></i> Export Results
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Search Results -->
            <div class="card">
                <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-list"></i> Search Results
                        {% if total_count > 0 %}
                        <span class="badge badge-light ml-2">{{ total_count }} orders found</span>
                        {% endif %}
                    </h5>
                    {% if total_pages > 1 %}
                    <div>
                        Page {{ page }} of {{ total_pages }}
                    </div>
                    {% endif %}
                </div>
                <div class="card-body p-0">
                    {% if orders %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover mb-0">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Order ID</th>
                                    <th>Customer</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                    <th>Amount</th>
                                    <th>Payment</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in orders %}
                                <tr>
                                    <td>
                                        <strong class="text-primary">{{ order.order_id }}</strong>
                                        {% if order.invoice_number %}
                                        <br><small class="text-muted">{{ order.invoice_number }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong>{{ order.customer_name }}</strong>
                                        {% if order.customer_phone %}
                                        <br><small class="text-muted">{{ order.customer_phone }}</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ order.order_date.strftime('%d/%m/%Y') if order.order_date else 'N/A' }}</td>
                                    <td>
                                        <span class="badge badge-{{ 'success' if order.status == 'Delivered' else 'warning' if order.status in ['Placed', 'Processing'] else 'info' }}">
                                            {{ order.status }}
                                        </span>
                                    </td>
                                    <td>Rs.{{ "{:,.2f}".format(order.order_amount or 0) }}</td>
                                    <td>
                                        <span class="badge badge-{{ 'success' if order.payment_status == 'paid' else 'danger' if order.payment_status == 'overdue' else 'warning' }}">
                                            {{ order.payment_status|title }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{{ url_for('orders.view_order', order_id=order.order_id) }}" 
                                               class="btn btn-outline-primary" title="View Order">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('orders_enhanced.update_order', order_id=order.order_id) }}" 
                                               class="btn btn-outline-warning" title="Update Order">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ url_for('orders_enhanced.order_history', order_id=order.order_id) }}" 
                                               class="btn btn-outline-info" title="Order History">
                                                <i class="fas fa-history"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if total_pages > 1 %}
                    <div class="card-footer">
                        <nav aria-label="Search results pagination">
                            <ul class="pagination justify-content-center mb-0">
                                {% if has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('orders_enhanced.advanced_search', page=page-1, q=search_query, status=status_filter, date_from=date_from, date_to=date_to, customer=customer_filter, amount_min=amount_min, amount_max=amount_max) }}">
                                        Previous
                                    </a>
                                </li>
                                {% endif %}

                                {% for p in range(1, total_pages + 1) %}
                                    {% if p == page %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ p }}</span>
                                    </li>
                                    {% elif p <= 3 or p >= total_pages - 2 or (p >= page - 1 and p <= page + 1) %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('orders_enhanced.advanced_search', page=p, q=search_query, status=status_filter, date_from=date_from, date_to=date_to, customer=customer_filter, amount_min=amount_min, amount_max=amount_max) }}">
                                            {{ p }}
                                        </a>
                                    </li>
                                    {% elif p == 4 and page > 5 %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                    {% elif p == total_pages - 3 and page < total_pages - 4 %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                    {% endif %}
                                {% endfor %}

                                {% if has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('orders_enhanced.advanced_search', page=page+1, q=search_query, status=status_filter, date_from=date_from, date_to=date_to, customer=customer_filter, amount_min=amount_min, amount_max=amount_max) }}">
                                        Next
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% endif %}

                    {% else %}
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-search fa-3x mb-3"></i>
                        <h5>No Orders Found</h5>
                        <p>No orders match your search criteria. Try adjusting your filters.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit form on filter change (optional)
    const autoSubmitElements = ['status', 'date_from', 'date_to'];
    
    autoSubmitElements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', function() {
                // Optional: Auto-submit on change
                // document.getElementById('searchForm').submit();
            });
        }
    });
    
    // Set date range shortcuts
    const today = new Date();
    const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const lastMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    // Add quick date range buttons
    const dateFromInput = document.getElementById('date_from');
    const dateToInput = document.getElementById('date_to');
    
    // Quick date range functions
    window.setDateRange = function(days) {
        const endDate = new Date();
        const startDate = new Date(endDate.getTime() - days * 24 * 60 * 60 * 1000);
        
        dateFromInput.value = startDate.toISOString().split('T')[0];
        dateToInput.value = endDate.toISOString().split('T')[0];
    };
});

function exportResults() {
    // Export search results to CSV
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'csv');
    window.location.href = '{{ url_for("orders_enhanced.advanced_search") }}?' + params.toString();
}
</script>
{% endblock %}
