{% extends 'base.html' %}

{% block title %}Update Order {{ order.order_id }} - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="card mb-4">
                <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-0">
                            <i class="fas fa-edit"></i> Update Order {{ order.order_id }}
                        </h4>
                        <small>Modify order details and status</small>
                    </div>
                    <div>
                        <a href="{{ url_for('orders.view_order', order_id=order.order_id) }}" class="btn btn-light">
                            <i class="fas fa-arrow-left"></i> Back to Order
                        </a>
                    </div>
                </div>
            </div>

            <!-- Update Form -->
            <form method="POST" id="updateOrderForm">
                <div class="row">
                    <!-- Customer Information -->
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="fas fa-user"></i> Customer Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label for="customer_name">Customer Name *</label>
                                    <input type="text" class="form-control" id="customer_name" name="customer_name" 
                                           value="{{ order.customer_name }}" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="customer_address">Address</label>
                                    <textarea class="form-control" id="customer_address" name="customer_address" 
                                              rows="3">{{ order.customer_address or '' }}</textarea>
                                </div>
                                
                                <div class="form-group">
                                    <label for="customer_phone">Phone</label>
                                    <input type="text" class="form-control" id="customer_phone" name="customer_phone" 
                                           value="{{ order.customer_phone or '' }}">
                                </div>
                                
                                <div class="form-group">
                                    <label for="customer_email">Email</label>
                                    <input type="email" class="form-control" id="customer_email" name="customer_email" 
                                           value="{{ order.customer_email or '' }}">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Order Status & Details -->
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0"><i class="fas fa-cog"></i> Order Status & Details</h5>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label for="status">Order Status *</label>
                                    <select class="form-control" id="status" name="status" required>
                                        {% for status in statuses %}
                                        <option value="{{ status }}" {% if status == order.status %}selected{% endif %}>
                                            {{ status }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label for="payment_method">Payment Method</label>
                                    <select class="form-control" id="payment_method" name="payment_method">
                                        <option value="">Select Payment Method</option>
                                        <option value="cash" {% if order.payment_method == 'cash' %}selected{% endif %}>Cash</option>
                                        <option value="credit" {% if order.payment_method == 'credit' %}selected{% endif %}>Credit</option>
                                        <option value="bank_transfer" {% if order.payment_method == 'bank_transfer' %}selected{% endif %}>Bank Transfer</option>
                                        <option value="cheque" {% if order.payment_method == 'cheque' %}selected{% endif %}>Cheque</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label for="payment_status">Payment Status</label>
                                    <select class="form-control" id="payment_status" name="payment_status">
                                        <option value="pending" {% if order.payment_status == 'pending' %}selected{% endif %}>Pending</option>
                                        <option value="partial" {% if order.payment_status == 'partial' %}selected{% endif %}>Partial</option>
                                        <option value="paid" {% if order.payment_status == 'paid' %}selected{% endif %}>Paid</option>
                                        <option value="overdue" {% if order.payment_status == 'overdue' %}selected{% endif %}>Overdue</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label for="sales_agent">Sales Agent</label>
                                    <input type="text" class="form-control" id="sales_agent" name="sales_agent" 
                                           value="{{ order.sales_agent or '' }}">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Order Items -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-list"></i> Order Items</h5>
                    </div>
                    <div class="card-body">
                        {% if order_items %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Product</th>
                                        <th>Quantity</th>
                                        <th>Unit Price</th>
                                        <th>Total</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for item in order_items %}
                                    <tr>
                                        <td>
                                            <strong>{{ item.product_name or item.product_id }}</strong>
                                            {% if item.strength %}
                                            <br><small class="text-muted">{{ item.strength }}</small>
                                            {% endif %}
                                        </td>
                                        <td>{{ item.quantity }}</td>
                                        <td>Rs.{{ "{:,.2f}".format(item.unit_price or 0) }}</td>
                                        <td>Rs.{{ "{:,.2f}".format(item.line_total or 0) }}</td>
                                        <td>
                                            <span class="badge badge-{{ 'success' if item.status == 'Delivered' else 'warning' }}">
                                                {{ item.status }}
                                            </span>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-box-open fa-3x mb-3"></i>
                            <h5>No Items Found</h5>
                            <p>No items are associated with this order.</p>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Notes -->
                <div class="card mb-4">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0"><i class="fas fa-sticky-note"></i> Order Notes</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="notes">Additional Notes</label>
                            <textarea class="form-control" id="notes" name="notes" rows="4" 
                                      placeholder="Add any additional notes or comments about this order...">{{ order.notes or '' }}</textarea>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <a href="{{ url_for('orders.view_order', order_id=order.order_id) }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            </div>
                            <div>
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-save"></i> Update Order
                                </button>
                                <a href="{{ url_for('orders_enhanced.order_history', order_id=order.order_id) }}" class="btn btn-info ml-2">
                                    <i class="fas fa-history"></i> View History
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.getElementById('updateOrderForm');
    
    form.addEventListener('submit', function(e) {
        const customerName = document.getElementById('customer_name').value.trim();
        const status = document.getElementById('status').value;
        
        if (!customerName) {
            e.preventDefault();
            alert('Customer name is required.');
            document.getElementById('customer_name').focus();
            return false;
        }
        
        if (!status) {
            e.preventDefault();
            alert('Please select an order status.');
            document.getElementById('status').focus();
            return false;
        }
        
        // Confirm update
        if (!confirm('Are you sure you want to update this order?')) {
            e.preventDefault();
            return false;
        }
    });
    
    // Status change warning
    const statusSelect = document.getElementById('status');
    const originalStatus = '{{ order.status }}';
    
    statusSelect.addEventListener('change', function() {
        if (this.value !== originalStatus) {
            const warning = document.createElement('div');
            warning.className = 'alert alert-warning mt-2';
            warning.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Changing order status will trigger workflow actions.';
            
            // Remove existing warnings
            const existingWarning = statusSelect.parentNode.querySelector('.alert-warning');
            if (existingWarning) {
                existingWarning.remove();
            }
            
            statusSelect.parentNode.appendChild(warning);
        }
    });
});
</script>
{% endblock %}
