{% extends 'base.html' %}

{% block title %}Invoice {{ invoice.invoice_number }} - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1 class="mb-0">
                                <i class="fas fa-file-invoice"></i> Invoice {{ invoice.invoice_number }}
                            </h1>
                            <p class="mb-0">Order: {{ order.order_id }} | Customer: {{ order.customer_name }}</p>
                        </div>
                        <div class="col-md-4 text-right">
                            <a href="{{ url_for('finance_dashboard') }}" class="btn btn-light btn-sm">
                                <i class="fas fa-arrow-left"></i> Back to Finance
                            </a>
                            <button onclick="window.print()" class="btn btn-success btn-sm">
                                <i class="fas fa-print"></i> Print
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Invoice Details -->
            <div class="card">
                <div class="card-body">
                    <!-- Company Header -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h2 class="text-primary">Medivent Pharmaceuticals</h2>
                            <p class="mb-1">123 Medical Plaza, Healthcare District</p>
                            <p class="mb-1">Karachi, Pakistan</p>
                            <p class="mb-1">Phone: +92-21-1234567</p>
                            <p class="mb-0">Email: <EMAIL></p>
                        </div>
                        <div class="col-md-6 text-right">
                            <h3 class="text-primary">INVOICE</h3>
                            <table class="table table-borderless" style="width: auto; margin-left: auto;">
                                <tr>
                                    <td><strong>Invoice #:</strong></td>
                                    <td>{{ invoice.invoice_number }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Date:</strong></td>
                                    <td>{{ invoice.date_generated | format_datetime('%d %b %Y') if invoice.date_generated else 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Order #:</strong></td>
                                    <td>{{ order.order_id }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td><span class="badge badge-success">{{ invoice.status }}</span></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Customer Details -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5 class="text-primary">Bill To:</h5>
                            <div class="border p-3">
                                <h6>{{ order.customer_name }}</h6>
                                {% if order.customer_address %}
                                    <p class="mb-1">{{ order.customer_address }}</p>
                                {% endif %}
                                {% if order.customer_phone %}
                                    <p class="mb-1">Phone: {{ order.customer_phone }}</p>
                                {% endif %}
                                {% if order.customer_email %}
                                    <p class="mb-0">Email: {{ order.customer_email }}</p>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5 class="text-primary">Order Details:</h5>
                            <div class="border p-3">
                                <p class="mb-1"><strong>Order Date:</strong> {{ order.order_date | format_datetime('%d %b %Y') if order.order_date else 'N/A' }}</p>
                                <p class="mb-1"><strong>Sales Agent:</strong> {{ order.sales_agent or 'N/A' }}</p>
                                <p class="mb-1"><strong>Payment Method:</strong> {{ order.payment_method or 'N/A' }}</p>
                                <p class="mb-0"><strong>Payment Status:</strong> 
                                    <span class="badge badge-{% if order.payment_status == 'paid' %}success{% else %}warning{% endif %}">
                                        {{ order.payment_status|title }}
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Order Items -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary">Items:</h5>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead class="thead-light">
                                        <tr>
                                            <th>#</th>
                                            <th>Product</th>
                                            <th>Strength</th>
                                            <th>Batch</th>
                                            <th class="text-center">Qty</th>
                                            <th class="text-center">FOC</th>
                                            <th class="text-right">Unit Price</th>
                                            <th class="text-right">Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in order_items %}
                                        <tr>
                                            <td>{{ loop.index }}</td>
                                            <td>{{ item.product_name or item.product_id }}</td>
                                            <td>{{ item.strength or 'N/A' }}</td>
                                            <td>{{ item.batch_number or 'N/A' }}</td>
                                            <td class="text-center">{{ item.quantity }}</td>
                                            <td class="text-center">{{ item.foc_quantity or 0 }}</td>
                                            <td class="text-right">Rs.{{ "{:,.2f}".format(item.unit_price or 0) }}</td>
                                            <td class="text-right">Rs.{{ "{:,.2f}".format(item.line_total or 0) }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Totals -->
                    <div class="row">
                        <div class="col-md-8"></div>
                        <div class="col-md-4">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Subtotal:</strong></td>
                                    <td class="text-right">Rs.{{ "{:,.2f}".format(invoice.subtotal or 0) }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Tax Amount:</strong></td>
                                    <td class="text-right">Rs.{{ "{:,.2f}".format(invoice.tax_amount or 0) }}</td>
                                </tr>
                                <tr class="border-top">
                                    <td><h5><strong>Total Amount:</strong></h5></td>
                                    <td class="text-right"><h5><strong>Rs.{{ "{:,.2f}".format(invoice.total_amount or 0) }}</strong></h5></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Footer -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <hr>
                            <div class="text-center">
                                <p class="mb-1"><strong>Thank you for your business!</strong></p>
                                <p class="mb-0 text-muted">Generated by {{ invoice.generated_by }} on {{ invoice.date_generated | format_datetime('%d %b %Y at %I:%M %p') if invoice.date_generated else 'N/A' }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .card-header, .btn, .no-print {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    .container-fluid {
        padding: 0 !important;
    }
}
</style>
{% endblock %}
