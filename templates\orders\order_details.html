{% extends "base.html" %}

{% block title %}Order Details - {{ order.order_id }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Order Header -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="mb-0">
                                <i class="fas fa-file-alt"></i> Order Details - {{ order.order_id }}
                            </h4>
                            <small>{{ order.customer_name }}</small>
                        </div>
                        <div class="col-md-4 text-right">
                            <span class="badge badge-{% if order.status == 'Delivered' %}success{% elif order.status == 'Processing' %}primary{% elif order.status == 'Placed' %}warning{% else %}secondary{% endif %} badge-lg">
                                {{ order.status }}
                            </span>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="row">
                        <!-- Order Information -->
                        <div class="col-md-6">
                            <h6><strong>Order Information</strong></h6>
                            <table class="table table-sm table-borderless">
                                <tr>
                                    <td><strong>Order ID:</strong></td>
                                    <td>{{ order.order_id }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Order Date:</strong></td>
                                    <td>{{ order.order_date | format_datetime }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        <span class="badge badge-{% if order.status == 'Delivered' %}success{% elif order.status == 'Processing' %}primary{% elif order.status == 'Placed' %}warning{% else %}secondary{% endif %}">
                                            {{ order.status }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Priority:</strong></td>
                                    <td>
                                        <span class="badge badge-{% if order.priority == 'urgent' %}danger{% elif order.priority == 'high' %}warning{% else %}secondary{% endif %}">
                                            {{ order.priority|title if order.priority else 'Normal' }}
                                        </span>
                                    </td>
                                </tr>
                                {% if order.delivery_date %}
                                <tr>
                                    <td><strong>Delivery Date:</strong></td>
                                    <td>{{ order.delivery_date | format_datetime('%Y-%m-%d') }}</td>
                                </tr>
                                {% endif %}
                                {% if order.special_instructions %}
                                <tr>
                                    <td><strong>Instructions:</strong></td>
                                    <td>{{ order.special_instructions }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                        
                        <!-- Customer Information -->
                        <div class="col-md-6">
                            <h6><strong>Customer Information</strong></h6>
                            <table class="table table-sm table-borderless">
                                <tr>
                                    <td><strong>Customer:</strong></td>
                                    <td>{{ order.customer_name }}</td>
                                </tr>
                                {% if order.customer_id %}
                                <tr>
                                    <td><strong>Customer ID:</strong></td>
                                    <td>{{ order.customer_id }}</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <td><strong>Payment Status:</strong></td>
                                    <td>
                                        <span class="badge badge-{% if order.payment_status == 'paid' %}success{% elif order.payment_status == 'partial' %}warning{% else %}danger{% endif %}">
                                            {{ order.payment_status|title }}
                                        </span>
                                    </td>
                                </tr>
                                {% if order.payment_method %}
                                <tr>
                                    <td><strong>Payment Method:</strong></td>
                                    <td>{{ order.payment_method|title }}</td>
                                </tr>
                                {% endif %}
                                {% if order.invoice_number %}
                                <tr>
                                    <td><strong>Invoice Number:</strong></td>
                                    <td>{{ order.invoice_number }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Order Items -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-list"></i> Order Items</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Product</th>
                                    <th>Quantity</th>
                                    <th>Unit Price</th>
                                    <th>Total Price</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in order_items %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>
                                        <strong>{{ item.product_name }}</strong>
                                        {% if item.product_id %}
                                        <br><small class="text-muted">{{ item.product_id }}</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ item.quantity }}</td>
                                    <td>₹{{ item.unit_price | format_currency }}</td>
                                    <td>₹{{ item.total_price | format_currency }}</td>
                                    <td>
                                        {% if item.status %}
                                        <span class="badge badge-success">{{ item.status }}</span>
                                        {% else %}
                                        <span class="badge badge-secondary">Pending</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr class="table-active">
                                    <th colspan="4" class="text-right">Subtotal:</th>
                                    <th>₹{{ (order.order_amount - (order.tax_amount or 0) - (order.shipping_cost or 0) + (order.discount_amount or 0)) | format_currency }}</th>
                                    <th></th>
                                </tr>
                                {% if order.discount_amount and order.discount_amount > 0 %}
                                <tr class="table-active">
                                    <th colspan="4" class="text-right">Discount:</th>
                                    <th class="text-success">-₹{{ order.discount_amount | format_currency }}</th>
                                    <th></th>
                                </tr>
                                {% endif %}
                                {% if order.tax_amount and order.tax_amount > 0 %}
                                <tr class="table-active">
                                    <th colspan="4" class="text-right">Tax:</th>
                                    <th>₹{{ order.tax_amount | format_currency }}</th>
                                    <th></th>
                                </tr>
                                {% endif %}
                                {% if order.shipping_cost and order.shipping_cost > 0 %}
                                <tr class="table-active">
                                    <th colspan="4" class="text-right">Shipping:</th>
                                    <th>₹{{ order.shipping_cost | format_currency }}</th>
                                    <th></th>
                                </tr>
                                {% endif %}
                                <tr class="table-primary">
                                    <th colspan="4" class="text-right">Total Amount:</th>
                                    <th>₹{{ order.order_amount | format_currency }}</th>
                                    <th></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Inventory Allocations -->
            {% if allocations %}
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-boxes"></i> Inventory Allocations</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Batch Number</th>
                                    <th>Allocated Qty</th>
                                    <th>Warehouse</th>
                                    <th>Expiry Date</th>
                                    <th>Allocation Type</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for allocation in allocations %}
                                <tr>
                                    <td>{{ allocation.product_name }}</td>
                                    <td>{{ allocation.batch_number }}</td>
                                    <td>{{ allocation.allocated_quantity }}</td>
                                    <td>{{ allocation.warehouse_name }}</td>
                                    <td>{{ allocation.expiry_date | format_datetime('%Y-%m-%d') if allocation.expiry_date else 'N/A' }}</td>
                                    <td>
                                        <span class="badge badge-{% if allocation.allocation_type == 'automatic' %}primary{% else %}info{% endif %}">
                                            {{ allocation.allocation_type|title }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Action Buttons -->
            <div class="card">
                <div class="card-body text-center">
                    <div class="btn-group" role="group">
                        <a href="{{ url_for('orders') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Orders
                        </a>
                        <a href="{{ url_for('update_order', order_id=order.order_id) }}" class="btn btn-primary">
                            <i class="fas fa-edit"></i> Edit Order
                        </a>
                        <a href="{{ url_for('allocate_inventory_page', order_id=order.order_id) }}" class="btn btn-info">
                            <i class="fas fa-boxes"></i> Manage Inventory
                        </a>
                        {% if order.status in ['Approved', 'Processing'] %}
                        <a href="{{ url_for('dc_generation.batch_selection', order_id=order.order_id) }}" class="btn btn-warning">
                            <i class="fas fa-boxes"></i> Generate DC
                        </a>
                        {% endif %}
                        <a href="{{ url_for('view_invoice', order_id=order.order_id) }}" class="btn btn-success">
                            <i class="fas fa-file-invoice"></i> View Invoice
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.badge-lg {
    font-size: 1rem;
    padding: 0.5rem 1rem;
}
</style>
{% endblock %}
