{% extends "base.html" %}

{% block title %}Order History & Analytics - Medivent ERP{% endblock %}

{% block head %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
    .history-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .history-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    
    .history-header {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 20px;
        border-radius: 15px 15px 0 0;
    }
    
    .stats-card {
        background: white;
        border-radius: 10px;
        padding: 20px;
        text-align: center;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }
    
    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }
    
    .stats-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 10px;
    }
    
    .stats-label {
        color: #6c757d;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    
    .chart-container {
        background: white;
        border-radius: 10px;
        padding: 20px;
        height: 400px;
    }
    
    .timeline-item {
        border-left: 3px solid #667eea;
        padding-left: 20px;
        margin-bottom: 20px;
        position: relative;
    }
    
    .timeline-item::before {
        content: '';
        position: absolute;
        left: -8px;
        top: 0;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #667eea;
    }
    
    .timeline-date {
        color: #6c757d;
        font-size: 0.85rem;
        margin-bottom: 5px;
    }
    
    .timeline-content {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
    }
</style>
{% endblock %}

{% block content %}
<div class="history-container">
    <div class="container">
        <!-- Header -->
        <div class="history-card">
            <div class="history-header">
                <h1 class="mb-2">
                    <i class="fas fa-history mr-3"></i>Order History & Analytics
                </h1>
                <p class="mb-0">Comprehensive order analytics and historical data</p>
            </div>
        </div>
        
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number text-primary" id="totalOrders">0</div>
                    <div class="stats-label">Total Orders</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number text-success" id="totalRevenue">₹0</div>
                    <div class="stats-label">Total Revenue</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number text-warning" id="avgOrderValue">₹0</div>
                    <div class="stats-label">Avg Order Value</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number text-info" id="pendingOrders">0</div>
                    <div class="stats-label">Pending Orders</div>
                </div>
            </div>
        </div>
        
        <!-- Charts Section -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="history-card">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line mr-2"></i>Orders Over Time
                        </h5>
                    </div>
                    <div class="chart-container">
                        <canvas id="ordersChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="history-card">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-pie mr-2"></i>Order Status Distribution
                        </h5>
                    </div>
                    <div class="chart-container">
                        <canvas id="statusChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Recent Activity Timeline -->
        <div class="history-card">
            <div class="card-header bg-light">
                <h5 class="mb-0">
                    <i class="fas fa-clock mr-2"></i>Recent Order Activity
                </h5>
            </div>
            <div class="card-body">
                <div id="recentActivity">
                    <!-- Timeline will be loaded here -->
                </div>
            </div>
        </div>
        
        <!-- Quick Filters -->
        <div class="history-card">
            <div class="card-header bg-light">
                <h5 class="mb-0">
                    <i class="fas fa-filter mr-2"></i>Quick Filters
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-2">
                        <button class="btn btn-outline-primary btn-block" onclick="filterByPeriod('today')">
                            Today
                        </button>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-primary btn-block" onclick="filterByPeriod('week')">
                            This Week
                        </button>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-primary btn-block" onclick="filterByPeriod('month')">
                            This Month
                        </button>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-primary btn-block" onclick="filterByPeriod('quarter')">
                            This Quarter
                        </button>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-primary btn-block" onclick="filterByPeriod('year')">
                            This Year
                        </button>
                    </div>
                    <div class="col-md-2">
                        <a href="/orders" class="btn btn-outline-secondary btn-block">
                            View All Orders
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let ordersChart, statusChart;

document.addEventListener('DOMContentLoaded', function() {
    loadOrderHistory();
    initializeCharts();
});

async function loadOrderHistory() {
    try {
        const response = await fetch('/api/orders/history');
        const data = await response.json();
        
        if (data.success) {
            updateStatistics(data.statistics);
            updateCharts(data.charts);
            updateRecentActivity(data.recent_activity);
        } else {
            console.error('Error loading order history:', data.error);
        }
    } catch (error) {
        console.error('Error fetching order history:', error);
    }
}

function updateStatistics(stats) {
    document.getElementById('totalOrders').textContent = stats.total_orders || 0;
    document.getElementById('totalRevenue').textContent = '₹' + (stats.total_revenue || 0).toLocaleString();
    document.getElementById('avgOrderValue').textContent = '₹' + (stats.avg_order_value || 0).toLocaleString();
    document.getElementById('pendingOrders').textContent = stats.pending_orders || 0;
}

function initializeCharts() {
    // Orders Over Time Chart
    const ordersCtx = document.getElementById('ordersChart').getContext('2d');
    ordersChart = new Chart(ordersCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'Orders',
                data: [],
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // Status Distribution Chart
    const statusCtx = document.getElementById('statusChart').getContext('2d');
    statusChart = new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: [],
            datasets: [{
                data: [],
                backgroundColor: [
                    '#667eea', '#764ba2', '#f093fb', '#f5576c',
                    '#4facfe', '#00f2fe', '#43e97b', '#38f9d7'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

function updateCharts(chartData) {
    // Update orders chart
    if (chartData.orders_over_time) {
        ordersChart.data.labels = chartData.orders_over_time.labels;
        ordersChart.data.datasets[0].data = chartData.orders_over_time.data;
        ordersChart.update();
    }
    
    // Update status chart
    if (chartData.status_distribution) {
        statusChart.data.labels = chartData.status_distribution.labels;
        statusChart.data.datasets[0].data = chartData.status_distribution.data;
        statusChart.update();
    }
}

function updateRecentActivity(activities) {
    const container = document.getElementById('recentActivity');
    
    if (!activities || activities.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">No recent activity</p>';
        return;
    }
    
    let html = '';
    activities.forEach(activity => {
        html += `
            <div class="timeline-item">
                <div class="timeline-date">${new Date(activity.date).toLocaleString()}</div>
                <div class="timeline-content">
                    <strong>${activity.title}</strong><br>
                    <span class="text-muted">${activity.description}</span>
                    ${activity.order_id ? `<br><a href="/orders/${activity.order_id}/view" class="btn btn-sm btn-outline-primary mt-2">View Order</a>` : ''}
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

function filterByPeriod(period) {
    // Reload data with period filter
    fetch(`/api/orders/history?period=${period}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStatistics(data.statistics);
                updateCharts(data.charts);
                updateRecentActivity(data.recent_activity);
            }
        })
        .catch(error => {
            console.error('Error filtering by period:', error);
        });
}
</script>
{% endblock %}
