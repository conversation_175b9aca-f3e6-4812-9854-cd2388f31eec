{% extends "base.html" %}

{% block title %}Batch Selection - {{ order.order_id }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-boxes"></i> Batch Selection for DC Generation
        </h1>
        <a href="{{ url_for('dc_pending') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to DC Pending
        </a>
    </div>

    <!-- Order Information -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 bg-primary text-white">
            <h6 class="m-0 font-weight-bold">Order Details</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <strong>Order ID:</strong><br>
                    <span class="text-primary">{{ order.order_id }}</span>
                </div>
                <div class="col-md-3">
                    <strong>Customer:</strong><br>
                    {{ order.customer_name }}
                </div>
                <div class="col-md-3">
                    <strong>Order Date:</strong><br>
                    {{ safe_strftime(order.order_date, '%Y-%m-%d %H:%M') if order.order_date else 'N/A' }}
                </div>
                <div class="col-md-3">
                    <strong>Order Amount:</strong><br>
                    <span class="text-success">₹{{ "%.2f"|format(order.order_amount) }}</span>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-6">
                    <strong>Address:</strong><br>
                    {{ order.customer_address or 'N/A' }}
                </div>
                <div class="col-md-3">
                    <strong>Phone:</strong><br>
                    {{ order.customer_phone or 'N/A' }}
                </div>
                <div class="col-md-3">
                    <strong>Status:</strong><br>
                    <span class="badge badge-success">{{ order.status }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Allocation Method Selection -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 bg-info text-white">
            <h6 class="m-0 font-weight-bold">Allocation Method</h6>
        </div>
        <div class="card-body">
            <form method="POST" id="methodForm">
                <input type="hidden" name="action" value="apply_method">
                <div class="row">
                    <div class="col-md-4">
                        <label for="selection_method"><strong>Batch Selection Method:</strong></label>
                        <select class="form-control" id="selection_method" name="selection_method">
                            <option value="fifo">FIFO (First In, First Out)</option>
                            <option value="lifo">LIFO (Last In, First Out)</option>
                            <option value="random">Random/Autopilot</option>
                            <option value="manual">Manual Selection</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="warehouse_filter"><strong>Warehouse Filter:</strong></label>
                        <select class="form-control" id="warehouse_filter" name="warehouse_filter">
                            <option value="all">All Warehouses</option>
                            {% for warehouse in warehouses %}
                            <option value="{{ warehouse.warehouse_id }}">{{ warehouse.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label>&nbsp;</label><br>
                        <button type="submit" class="btn btn-info">
                            <i class="fas fa-magic"></i> Apply Method
                        </button>
                        <button type="button" class="btn btn-warning" onclick="resetAllocations()">
                            <i class="fas fa-undo"></i> Reset
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Product Allocation -->
    <form method="POST" id="allocationForm">
        <input type="hidden" name="action" value="save_allocations">
        
        {% for item in order_items %}
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h6 class="m-0 font-weight-bold text-primary">
                            {{ item.product_name }}
                            {% if item.strength %} - {{ item.strength }}{% endif %}
                        </h6>
                        <small class="text-muted">Product ID: {{ item.product_id }}</small>
                    </div>
                    <div class="col-md-4 text-right">
                        <span class="badge badge-primary">Required: {{ item.quantity }}</span>
                        <span class="badge badge-secondary" id="allocated_{{ item.product_id }}">Allocated: 0</span>
                    </div>
                </div>
            </div>
            <div class="card-body">
                {% if item.product_id in inventory_data and inventory_data[item.product_id] %}
                    {% for warehouse_id, warehouse_data in inventory_data[item.product_id].items() %}
                        {% if warehouse_data and warehouse_data.get('batches') and warehouse_data.get('batches')|length > 0 %}
                        <h6 class="text-info">{{ warehouse_data.warehouse_name }}</h6>
                        <div class="table-responsive mb-3">
                            <table class="table table-sm table-bordered">
                                <thead class="thead-light">
                                    <tr>
                                        <th>Batch Number</th>
                                        <th>Mfg Date</th>
                                        <th>Exp Date</th>
                                        <th>Available</th>
                                        <th>Quantity to Use</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for batch in warehouse_data.get('batches', []) %}
                                    <tr>
                                        <td>
                                            <small class="text-monospace">{{ batch.batch_number }}</small>
                                        </td>
                                        <td>
                                            {{ batch.manufacturing_date_formatted if batch.manufacturing_date_formatted else 'N/A' }}
                                        </td>
                                        <td>
                                            {% if batch.expiry_date_formatted %}
                                                <span class="{{ batch.expiry_date_class }}">
                                                    {{ batch.expiry_date_formatted }}
                                                </span>
                                            {% else %}
                                                <span class="text-muted">No Expiry</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge badge-success">{{ batch.available_quantity }}</span>
                                        </td>
                                        <td>
                                            <input type="number" 
                                                   class="form-control form-control-sm allocation-input" 
                                                   name="allocation_{{ item.product_id }}_{{ batch.batch_number }}_{{ warehouse_id }}"
                                                   min="0" 
                                                   max="{{ batch.available_quantity }}" 
                                                   step="0.01"
                                                   data-product="{{ item.product_id }}"
                                                   data-available="{{ batch.available_quantity }}"
                                                   value="{{ existing_selections.get(item.product_id + '_' + batch.batch_number, {}).get('allocated_quantity', 0) }}"
                                                   onchange="updateAllocationStatus('{{ item.product_id }}', {{ item.quantity }})">
                                        </td>
                                        <td>
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-primary"
                                                    onclick="useMaxQuantity('{{ item.product_id }}', '{{ batch.batch_number }}', '{{ warehouse_id }}', {{ batch.available_quantity }}, {{ item.quantity }})">
                                                Use Max
                                            </button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% endif %}
                    {% endfor %}
                {% else %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>No available inventory for {{ item.product_name }} ({{ item.product_id }})</strong><br>
                        <small>Please add inventory or check if all stock is already allocated.</small>
                        <a href="{{ url_for('inventory.inventory_management') }}" class="btn btn-sm btn-primary mt-2">
                            <i class="fas fa-plus"></i> Add Inventory
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
        {% endfor %}

        <!-- Action Buttons -->
        <div class="card shadow mb-4">
            <div class="card-header py-3 bg-success text-white">
                <h6 class="m-0 font-weight-bold">Generate Delivery Challan</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <div id="allocation-summary">
                            <!-- Allocation summary will be populated by JavaScript -->
                        </div>
                    </div>
                    <div class="col-md-4 text-right">
                        <div class="alert alert-info mb-2" id="workflow-guide">
                            <small><strong>Workflow:</strong><br>
                            1. Use "Apply Method" (FIFO) OR manually enter quantities<br>
                            2. Click "Save Allocations"<br>
                            3. Then "Generate DC"</small>
                        </div>
                        <button type="submit" class="btn btn-primary mb-2" onclick="handleSaveAllocations(event)">
                            <i class="fas fa-save"></i> Save Allocations
                        </button><br>
                        <button type="button" class="btn btn-success" id="generateDcBtn" onclick="generateDC()" disabled>
                            <i class="fas fa-truck"></i> Generate DC
                        </button>
                        <div id="dc-status" class="mt-2" style="display: none;">
                            <small class="text-muted">Allocations must be saved first</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
// JavaScript for batch selection functionality
function updateAllocationStatus(productId, requiredQuantity) {
    let totalAllocated = 0;
    
    // Sum all allocations for this product
    document.querySelectorAll(`input[data-product="${productId}"]`).forEach(input => {
        totalAllocated += parseFloat(input.value) || 0;
    });
    
    // Update the allocated badge
    const allocatedBadge = document.getElementById(`allocated_${productId}`);
    allocatedBadge.textContent = `Allocated: ${totalAllocated}`;
    
    // Update badge color based on allocation status
    allocatedBadge.className = 'badge ' + (totalAllocated >= requiredQuantity ? 'badge-success' : 'badge-warning');
    
    // Check if all products are fully allocated
    checkAllAllocations();
}

function checkAllAllocations() {
    let allAllocated = true;
    const summary = [];
    
    {% for item in order_items %}
    const required{{ loop.index }} = {{ item.quantity }};
    let allocated{{ loop.index }} = 0;
    
    document.querySelectorAll(`input[data-product="{{ item.product_id }}"]`).forEach(input => {
        allocated{{ loop.index }} += parseFloat(input.value) || 0;
    });
    
    const isFullyAllocated{{ loop.index }} = allocated{{ loop.index }} >= required{{ loop.index }};
    if (!isFullyAllocated{{ loop.index }}) {
        allAllocated = false;
    }
    
    summary.push({
        name: '{{ item.product_name }}',
        required: required{{ loop.index }},
        allocated: allocated{{ loop.index }},
        isFullyAllocated: isFullyAllocated{{ loop.index }}
    });
    {% endfor %}
    
    // Update summary display
    updateAllocationSummary(summary);
    
    // Enable/disable generate DC button
    document.getElementById('generateDcBtn').disabled = !allAllocated;
}

function updateAllocationSummary(summary) {
    const summaryDiv = document.getElementById('allocation-summary');
    let html = '<h6>Allocation Summary:</h6>';
    
    summary.forEach(item => {
        const statusIcon = item.isFullyAllocated ? 
            '<i class="fas fa-check-circle text-success"></i>' : 
            '<i class="fas fa-exclamation-circle text-warning"></i>';
        
        html += `<div class="mb-1">
            ${statusIcon} ${item.name}: ${item.allocated}/${item.required}
        </div>`;
    });
    
    summaryDiv.innerHTML = html;
}

function useMaxQuantity(productId, batchNumber, warehouseId, availableQuantity, requiredQuantity) {
    const inputName = `allocation_${productId}_${batchNumber}_${warehouseId}`;
    const input = document.querySelector(`input[name="${inputName}"]`);
    
    if (input) {
        // Calculate how much more is needed for this product
        let currentAllocated = 0;
        document.querySelectorAll(`input[data-product="${productId}"]`).forEach(inp => {
            if (inp !== input) {
                currentAllocated += parseFloat(inp.value) || 0;
            }
        });
        
        const stillNeeded = Math.max(0, requiredQuantity - currentAllocated);
        const toAllocate = Math.min(availableQuantity, stillNeeded);
        
        input.value = toAllocate;
        updateAllocationStatus(productId, requiredQuantity);
    }
}

function resetAllocations() {
    if (confirm('Are you sure you want to reset all allocations?')) {
        document.querySelectorAll('.allocation-input').forEach(input => {
            input.value = 0;
        });
        
        {% for item in order_items %}
        updateAllocationStatus('{{ item.product_id }}', {{ item.quantity }});
        {% endfor %}
    }
}

function generateDC() {
    // Add debugging popup
    showDebugPopup('Generate DC button clicked!');

    if (confirm('Generate Delivery Challan? This action cannot be undone.')) {
        // Show loading state
        const generateBtn = document.getElementById('generateDcBtn');
        const originalText = generateBtn.innerHTML;
        generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';
        generateBtn.disabled = true;

        // Show progress popup
        showProgressPopup('Starting DC generation...');

        // Submit form via AJAX to handle response
        const formData = new FormData();
        formData.append('action', 'generate_dc');

        console.log('🚀 Starting DC generation for order:', window.location.pathname);

        fetch(window.location.href, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            console.log('📡 DC Generation Response:', response);
            console.log('📊 Response Status:', response.status);
            console.log('📋 Response Headers:', response.headers);

            updateProgressPopup('Processing server response...');

            if (response.ok) {
                const contentType = response.headers.get('content-type');
                console.log('📄 Content Type:', contentType);

                if (contentType && contentType.includes('application/json')) {
                    return response.json();
                } else {
                    // If not JSON, it might be a redirect response
                    console.log('🔄 Non-JSON response, redirecting to:', response.url);
                    showErrorPopup('Unexpected response format. Redirecting...');
                    setTimeout(() => {
                        window.location.href = response.url;
                    }, 2000);
                    return null;
                }
            } else {
                // Try to get error details from response
                return response.json().then(errorData => {
                    throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
                }).catch(() => {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                });
            }
        })
        .then(data => {
            console.log('📦 DC Generation Data:', data);

            if (data && data.success) {
                console.log('✅ DC generated successfully:', data.dc_number);
                updateProgressPopup('DC generated successfully! Opening PDF...');

                // Build success message
                let successMessage = data.message;
                if (data.invoice_number) {
                    successMessage += `\nInvoice ${data.invoice_number} created and forwarded to finance module.`;
                }

                // Try to open DC PDF in new tab
                if (data.pdf_url) {
                    console.log('📄 Opening PDF URL:', data.pdf_url);
                    const newTab = window.open(data.pdf_url, '_blank');

                    if (newTab) {
                        console.log('✅ PDF opened in new tab successfully');
                        showSuccessPopup(`${successMessage}\nDelivery Challan PDF opened in new tab.`);
                    } else {
                        console.warn('⚠️ Failed to open new tab - popup blocked?');
                        showSuccessPopup(`${successMessage}\nNote: PDF popup may have been blocked. Click OK to view PDF.`);
                        // Fallback: redirect to PDF directly
                        setTimeout(() => {
                            window.location.href = data.pdf_url;
                        }, 1000);
                    }
                } else {
                    showSuccessPopup(successMessage);
                }

                // Redirect to warehouse page after a delay
                setTimeout(() => {
                    if (data.redirect_url) {
                        window.location.href = data.redirect_url;
                    } else {
                        window.location.href = '/warehouses';
                    }
                }, 3000);

            } else if (data) {
                console.error('❌ DC generation failed:', data);
                let errorMessage = data.message || 'Failed to generate Delivery Challan';

                // Handle specific error types with better user guidance
                if (data.error === 'no_allocations') {
                    showAllocationGuidancePopup(data.message, data.next_step);
                } else if (data.error === 'incomplete_allocations') {
                    showAllocationGuidancePopup(data.message, data.next_step);
                } else {
                    // Generic error handling
                    if (data.error) {
                        errorMessage += `\nError Type: ${data.error}`;
                    }
                    if (data.details) {
                        console.error('Error details:', data.details);
                    }
                    showErrorPopup(errorMessage);
                }
            }
        })
        .catch(error => {
            console.error('💥 Error generating DC:', error);
            showErrorPopup(`Error generating Delivery Challan: ${error.message}\nPlease check the console for details and try again.`);
        })
        .finally(() => {
            // Restore button state
            generateBtn.innerHTML = originalText;
            generateBtn.disabled = false;
            hideProgressPopup();
        });
    }
}

// Enhanced debugging and user feedback functions
function showDebugPopup(message) {
    console.log('🐛 DEBUG:', message);
    // Uncomment the line below to show debug popups (can be annoying)
    // alert('DEBUG: ' + message);
}

function showProgressPopup(message) {
    console.log('⏳ PROGRESS:', message);

    // Create or update progress popup
    let popup = document.getElementById('progressPopup');
    if (!popup) {
        popup = document.createElement('div');
        popup.id = 'progressPopup';
        popup.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #007bff;
            color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            z-index: 10000;
            text-align: center;
            min-width: 300px;
        `;
        document.body.appendChild(popup);
    }

    popup.innerHTML = `
        <div style="margin-bottom: 10px;">
            <i class="fas fa-spinner fa-spin"></i>
        </div>
        <div>${message}</div>
    `;
    popup.style.display = 'block';
}

function updateProgressPopup(message) {
    const popup = document.getElementById('progressPopup');
    if (popup) {
        popup.innerHTML = `
            <div style="margin-bottom: 10px;">
                <i class="fas fa-spinner fa-spin"></i>
            </div>
            <div>${message}</div>
        `;
    }
}

function hideProgressPopup() {
    const popup = document.getElementById('progressPopup');
    if (popup) {
        popup.style.display = 'none';
    }
}

function showSuccessPopup(message) {
    console.log('✅ SUCCESS:', message);
    hideProgressPopup();

    // Create success popup
    const popup = document.createElement('div');
    popup.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #28a745;
        color: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        z-index: 10001;
        text-align: center;
        min-width: 300px;
        max-width: 500px;
    `;

    popup.innerHTML = `
        <div style="margin-bottom: 10px;">
            <i class="fas fa-check-circle" style="font-size: 24px;"></i>
        </div>
        <div style="white-space: pre-line;">${message}</div>
        <div style="margin-top: 15px;">
            <button onclick="this.parentElement.parentElement.remove()"
                    style="background: white; color: #28a745; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                OK
            </button>
        </div>
    `;

    document.body.appendChild(popup);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (popup.parentElement) {
            popup.remove();
        }
    }, 5000);
}

function showErrorPopup(message) {
    console.error('❌ ERROR:', message);
    hideProgressPopup();

    // Create error popup
    const popup = document.createElement('div');
    popup.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #dc3545;
        color: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        z-index: 10001;
        text-align: center;
        min-width: 300px;
        max-width: 500px;
    `;

    popup.innerHTML = `
        <div style="margin-bottom: 10px;">
            <i class="fas fa-exclamation-triangle" style="font-size: 24px;"></i>
        </div>
        <div style="white-space: pre-line;">${message}</div>
        <div style="margin-top: 15px;">
            <button onclick="this.parentElement.parentElement.remove()"
                    style="background: white; color: #dc3545; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                OK
            </button>
        </div>
    `;

    document.body.appendChild(popup);
}

function showAllocationGuidancePopup(message, nextStep) {
    console.error('❌ ALLOCATION ERROR:', message);
    hideProgressPopup();

    // Create guidance popup with action buttons
    const popup = document.createElement('div');
    popup.className = 'allocation-guidance-popup';
    popup.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: linear-gradient(135deg, #fff3cd, #ffeaa7);
        border: 2px solid #ffc107;
        border-radius: 12px;
        padding: 25px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        z-index: 10000;
        text-align: center;
        font-family: Arial, sans-serif;
        color: #856404;
        min-width: 400px;
        max-width: 600px;
    `;

    popup.innerHTML = `
        <div style="margin-bottom: 15px;">
            <i class="fas fa-exclamation-triangle" style="font-size: 32px; color: #ffc107;"></i>
        </div>
        <h4 style="margin: 0 0 15px 0; color: #856404;">Batch Allocation Required</h4>
        <div style="white-space: pre-line; margin-bottom: 15px; line-height: 1.5;">${message}</div>
        ${nextStep ? `<div style="background: rgba(255,255,255,0.7); padding: 10px; border-radius: 6px; margin-bottom: 20px; font-style: italic;">${nextStep}</div>` : ''}
        <div style="display: flex; gap: 10px; justify-content: center;">
            <button onclick="autoAllocateFIFO()"
                    style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer; font-weight: bold;">
                <i class="fas fa-magic"></i> Auto-Allocate (FIFO)
            </button>
            <button onclick="autoAllocateLIFO()"
                    style="background: #17a2b8; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer; font-weight: bold;">
                <i class="fas fa-magic"></i> Auto-Allocate (LIFO)
            </button>
            <button onclick="this.parentElement.parentElement.remove()"
                    style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer;">
                Manual Allocation
            </button>
        </div>
    `;

    document.body.appendChild(popup);
}

// Auto-allocation functions
function autoAllocateFIFO() {
    document.querySelector('.allocation-guidance-popup')?.remove();
    showProgressPopup('Auto-allocating batches using FIFO method...');

    // Submit form with FIFO method
    const form = document.querySelector('form[action*="select-batch"]');
    if (form) {
        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = 'action';
        methodInput.value = 'apply_fifo';
        form.appendChild(methodInput);
        form.submit();
    }
}

function autoAllocateLIFO() {
    document.querySelector('.allocation-guidance-popup')?.remove();
    showProgressPopup('Auto-allocating batches using LIFO method...');

    // Submit form with LIFO method
    const form = document.querySelector('form[action*="select-batch"]');
    if (form) {
        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = 'action';
        methodInput.value = 'apply_lifo';
        form.appendChild(methodInput);
        form.submit();
    }
}

// Handle Save Allocations button click
function handleSaveAllocations(event) {
    showDebugPopup('Save Allocations button clicked!');

    // Show confirmation
    if (!confirm('Save current batch allocations?')) {
        event.preventDefault();
        return false;
    }

    // Show loading state
    const saveBtn = event.target;
    const originalText = saveBtn.innerHTML;
    saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
    saveBtn.disabled = true;

    showProgressPopup('Saving batch allocations...');

    // Let the form submit naturally, but add a hidden field to identify the action
    const form = saveBtn.closest('form');

    // Add hidden field for action if it doesn't exist
    let actionField = form.querySelector('input[name="action"]');
    if (!actionField) {
        actionField = document.createElement('input');
        actionField.type = 'hidden';
        actionField.name = 'action';
        form.appendChild(actionField);
    }
    actionField.value = 'save_allocations';

    // Restore button after a delay (in case of redirect)
    setTimeout(() => {
        saveBtn.innerHTML = originalText;
        saveBtn.disabled = false;
        hideProgressPopup();
    }, 3000);

    return true;
}

// Initialize allocation status on page load
document.addEventListener('DOMContentLoaded', function() {
    {% for item in order_items %}
    updateAllocationStatus('{{ item.product_id }}', {{ item.quantity }});
    {% endfor %}

    // Add form submission handler for better feedback
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(event) {
            const action = form.querySelector('input[name="action"]')?.value;
            if (action === 'save_allocations') {
                console.log('📝 Form submitting with save_allocations action');
            }
        });
    }
});
</script>
{% endblock %}
