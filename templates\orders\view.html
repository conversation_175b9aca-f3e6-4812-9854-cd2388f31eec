{% extends 'base.html' %}

{% block title %}Order Details - {{ order.order_id }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Order Details - {{ order.order_id }}</h1>
        <div class="btn-group">
            <a href="{{ url_for('orders.index') }}" class="btn btn-sm btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Orders
            </a>
            <!-- Invoice viewing temporarily disabled -->
            <button class="btn btn-sm btn-primary" onclick="alert('Invoice viewing temporarily disabled during maintenance')">
                <i class="fas fa-file-invoice"></i> View Invoice
            </button>
        </div>
    </div>

    <!-- Order Information -->
    <div class="row">
        <div class="col-md-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Order Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <th width="40%">Order ID:</th>
                                    <td><strong>{{ order.order_id }}</strong></td>
                                </tr>
                                <tr>
                                    <th>Customer:</th>
                                    <td>{{ order.customer_name }}</td>
                                </tr>
                                <tr>
                                    <th>Order Date:</th>
                                    <td>{{ order.order_date|format_datetime }}</td>
                                </tr>
                                <tr>
                                    <th>Status:</th>
                                    <td>
                                        <span class="badge 
                                            {% if order.status == 'Placed' %}badge-warning
                                            {% elif order.status == 'Approved' %}badge-primary
                                            {% elif order.status == 'Processing' %}badge-info
                                            {% elif order.status == 'Ready for Pickup' %}badge-secondary
                                            {% elif order.status == 'Dispatched' %}badge-dark
                                            {% elif order.status == 'Delivered' %}badge-success
                                            {% elif order.status == 'Cancelled' %}badge-danger
                                            {% endif %}">
                                            {{ order.status }}
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <th width="40%">Order Amount:</th>
                                    <td><strong>{{ order.order_amount|format_currency }}</strong></td>
                                </tr>
                                <tr>
                                    <th>Payment Method:</th>
                                    <td>{{ order.payment_method or 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <th>Sales Agent:</th>
                                    <td>{{ order.sales_agent or 'N/A' }}</td>
                                </tr>
                                {% if order.invoice_number %}
                                <tr>
                                    <th>Invoice Number:</th>
                                    <td><strong>{{ order.invoice_number }}</strong></td>
                                </tr>
                                {% endif %}
                                {% if order.estimated_delivery_date %}
                                <tr>
                                    <th>Expected Delivery:</th>
                                    <td><span class="badge badge-info">{{ order.estimated_delivery_date|format_datetime('%Y-%m-%d %H:%M:%S') }}</span></td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Order Items -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Order Items</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Strength</th>
                                    <th>Quantity</th>
                                    <th>Unit Price</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in order_items %}
                                <tr>
                                    <td>{{ item.product_name or 'Unknown Product' }}</td>
                                    <td>{{ item.strength or 'N/A' }}</td>
                                    <td>{{ item.quantity }}</td>
                                    <td>{{ item.unit_price|format_currency }}</td>
                                    <td>{{ (item.quantity * item.unit_price)|format_currency }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Customer Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Customer Information</h6>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tr>
                            <th>Name:</th>
                            <td>{{ order.customer_name }}</td>
                        </tr>
                        <tr>
                            <th>Address:</th>
                            <td>
                                {{ order.customer_address or 'N/A' }}
                                {% if order.customer_address %}
                                <br>
                                <button type="button" class="btn btn-sm btn-outline-primary mt-1" onclick="openCustomerAddressOnMap('{{ order.customer_address }}', '{{ order.customer_name }}')">
                                    <i class="fas fa-map-marker-alt"></i> View on Map
                                </button>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>Phone:</th>
                            <td>{{ order.customer_phone or 'N/A' }}</td>
                        </tr>
                        <tr>
                            <th>Email:</th>
                            <td>{{ order.customer_email or 'N/A' }}</td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Order Actions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Actions</h6>
                </div>
                <div class="card-body">
                    <!-- Approval/Rejection Actions - Only for Placed orders -->
                    {% if order.status == 'Placed' %}
                    <div class="row">
                        <div class="col-md-6">
                            <form action="{{ url_for('orders.approve_order', order_id=order.order_id) }}" method="post" class="approval-form">
                                <input type="hidden" name="approval_notes" id="approval_notes_hidden">
                                <button type="submit" class="btn btn-success btn-block mb-2" onclick="return handleApproval(event)">
                                    <i class="fas fa-check"></i> Approve Order
                                </button>
                            </form>
                        </div>
                        <div class="col-md-6">
                            <form action="{{ url_for('orders.reject_order', order_id=order.order_id) }}" method="post" class="rejection-form">
                                <input type="hidden" name="rejection_notes" id="rejection_notes_hidden">
                                <button type="submit" class="btn btn-danger btn-block mb-2" onclick="return handleRejection(event)">
                                    <i class="fas fa-times"></i> Reject Order
                                </button>
                            </form>
                        </div>
                    </div>
                    {% endif %}

                    <!-- DC Actions - Generate or View based on status -->
                    {% if order.status == 'Approved' and not challan %}
                    <!-- Generate DC Button - Only for Approved orders without DC -->
                    <a href="{{ url_for('dc_generation.batch_selection', order_id=order.order_id) }}"
                       class="btn btn-warning btn-block mb-2">
                        <i class="fas fa-boxes"></i> Generate DC
                    </a>
                    {% elif challan %}
                    <!-- View DC Button - Only when DC exists -->
                    <a href="{{ url_for('dc_generation.view_dc', dc_number=challan.dc_number) }}"
                       class="btn btn-success btn-block mb-2">
                        <i class="fas fa-eye"></i> View DC
                    </a>
                    {% endif %}

                    <!-- Generate Invoice Button - Only AFTER DC is generated -->
                    {% if challan %}
                    <button onclick="generateInvoice('{{ order.order_id }}')"
                            class="btn btn-primary btn-block mb-2">
                        <i class="fas fa-file-invoice"></i> Generate Invoice
                    </button>
                    {% endif %}

                    <!-- Edit Order Button - Only for Placed orders -->
                    {% if order.status == 'Placed' %}
                    <a href="{{ url_for('orders.update_order', order_id=order.order_id) }}"
                       class="btn btn-info btn-block mb-2">
                        <i class="fas fa-edit"></i> Edit Order
                    </a>
                    {% endif %}

                    <!-- View Invoice Button - Only if invoice exists -->
                    {% if order.status in ['Invoiced', 'Completed'] %}
                    <a href="{{ url_for('finance.view_invoice', order_id=order.order_id) }}"
                       class="btn btn-outline-primary btn-block mb-2">
                        <i class="fas fa-file-pdf"></i> View Invoice
                    </a>
                    {% endif %}

                    <!-- Back to Orders Button -->
                    <a href="{{ url_for('orders.index') }}"
                       class="btn btn-secondary btn-block">
                        <i class="fas fa-list"></i> Back to Orders
                    </a>
                </div>
            </div>

            <!-- Approval/Rejection Notes Section - Only for Placed orders -->
            {% if order.status == 'Placed' %}
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Approval Notes/Comments (Optional)</h6>
                </div>
                <div class="card-body">
                    <textarea id="action_notes" class="form-control" rows="3"
                              placeholder="Enter any notes or comments for approval/rejection..."></textarea>
                    <small class="form-text text-muted">
                        These notes will be saved with the approval/rejection action and visible to the salesperson.
                    </small>
                </div>
            </div>
            {% endif %}

            <!-- Order Status Information -->
            {% if order.status == 'Rejected' and order.rejection_notes %}
            <div class="card shadow mb-4 border-danger">
                <div class="card-header py-3 bg-danger text-white">
                    <h6 class="m-0 font-weight-bold">Rejection Information</h6>
                </div>
                <div class="card-body">
                    <p><strong>Rejected by:</strong> {{ order.rejected_by }}</p>
                    <p><strong>Rejection Date:</strong> {{ order.rejection_date }}</p>
                    <p><strong>Rejection Notes:</strong></p>
                    <div class="alert alert-warning">{{ order.rejection_notes }}</div>
                    {% if order.sales_agent == current_user.username %}
                    <a href="{{ url_for('orders.update_order', order_id=order.order_id) }}"
                       class="btn btn-primary">
                        <i class="fas fa-edit"></i> Edit and Resubmit Order
                    </a>
                    {% endif %}
                </div>
            </div>
            {% endif %}

            {% if order.status == 'Approved' and order.approval_notes %}
            <div class="card shadow mb-4 border-success">
                <div class="card-header py-3 bg-success text-white">
                    <h6 class="m-0 font-weight-bold">Approval Information</h6>
                </div>
                <div class="card-body">
                    <p><strong>Approved by:</strong> {{ order.approved_by }}</p>
                    <p><strong>Approval Date:</strong> {{ order.approval_date }}</p>
                    <p><strong>Approval Notes:</strong></p>
                    <div class="alert alert-success">{{ order.approval_notes }}</div>
                </div>
            </div>
            {% endif %}

            <!-- Hold/Release History -->
            {% if hold_history %}
            <div class="card shadow mb-4">
                <div class="card-header py-3 bg-warning text-white">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-pause mr-2"></i>Hold/Release History
                    </h6>
                </div>
                <div class="card-body">
                    {% for hold in hold_history %}
                    <div class="border-left border-warning pl-3 mb-3">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="text-warning mb-1">
                                    {% if hold.status == 'active' %}
                                        <i class="fas fa-pause"></i> Currently On Hold
                                    {% else %}
                                        <i class="fas fa-play"></i> Released from Hold
                                    {% endif %}
                                </h6>
                                <small class="text-muted">{{ hold.hold_date }}</small>
                            </div>
                            <span class="badge badge-{{ 'warning' if hold.status == 'active' else 'success' }}">
                                {{ hold.priority_level|title }}
                            </span>
                        </div>

                        <div class="mt-2">
                            <strong>Reason:</strong> {{ hold.hold_reason }}<br>
                            {% if hold.hold_comments %}
                            <strong>Hold Comments:</strong> {{ hold.hold_comments }}<br>
                            {% endif %}
                            <small class="text-muted">Put on hold by: {{ hold.hold_by }}</small>
                        </div>

                        {% if hold.status == 'released' and hold.release_comments %}
                        <div class="mt-2 pt-2 border-top">
                            <strong class="text-success">Release Comments:</strong> {{ hold.release_comments }}<br>
                            <small class="text-muted">Released by: {{ hold.release_by }} on {{ hold.release_date }}</small>
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Workflow Comments -->
            {% if workflow_comments %}
            <div class="card shadow mb-4">
                <div class="card-header py-3 bg-info text-white">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-comments mr-2"></i>Workflow Comments
                    </h6>
                </div>
                <div class="card-body">
                    {% for comment in workflow_comments %}
                    <div class="border-left border-info pl-3 mb-3">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="mb-1">
                                    <span class="badge badge-{{
                                        'warning' if comment.comment_type == 'hold' else
                                        'success' if comment.comment_type == 'release' else
                                        'primary' if comment.comment_type == 'payment' else
                                        'secondary'
                                    }}">
                                        {{ comment.comment_type|title }}
                                    </span>
                                    {{ comment.entity_type|title }}
                                </h6>
                                <small class="text-muted">{{ comment.created_at }} by {{ comment.created_by }}</small>
                            </div>
                        </div>
                        <div class="mt-2">
                            {{ comment.comment_text }}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
// Google Maps integration function for customer addresses
function openCustomerAddressOnMap(address, customerName) {
    if (!address || address.trim() === '') {
        alert('No address available for this customer');
        return;
    }

    // Create Google Maps URL with the address
    const encodedAddress = encodeURIComponent(address.trim());
    const googleMapsUrl = `https://www.google.com/maps/search/?api=1&query=${encodedAddress}`;

    // Open in new tab
    window.open(googleMapsUrl, '_blank');

    console.log(`Opening Google Maps for ${customerName}: ${address}`);
}

// Handle approval with notes
function handleApproval(event) {
    event.preventDefault();

    const notes = document.getElementById('action_notes').value.trim();
    const confirmMessage = notes
        ? `Are you sure you want to approve this order?\n\nNotes: ${notes}`
        : 'Are you sure you want to approve this order?';

    if (confirm(confirmMessage)) {
        // Set the notes in the hidden field
        document.getElementById('approval_notes_hidden').value = notes;
        // Submit the form
        event.target.closest('form').submit();
    }

    return false;
}

// Handle rejection with notes
function handleRejection(event) {
    event.preventDefault();

    const notes = document.getElementById('action_notes').value.trim();

    if (!notes) {
        if (!confirm('Are you sure you want to reject this order without providing any notes?')) {
            return false;
        }
    }

    const confirmMessage = notes
        ? `Are you sure you want to reject this order?\n\nReason: ${notes}`
        : 'Are you sure you want to reject this order?';

    if (confirm(confirmMessage)) {
        // Set the notes in the hidden field
        document.getElementById('rejection_notes_hidden').value = notes;
        // Submit the form
        event.target.closest('form').submit();
    }

    return false;
}

// Generate Invoice function
function generateInvoice(orderId) {
    if (!orderId) {
        alert('Order ID is required');
        return;
    }

    // Show loading state
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';
    button.disabled = true;

    // Make AJAX request to generate invoice
    fetch('/finance/api/generate-invoice', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            order_id: orderId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Invoice generated successfully!');
            // Reload the page to show updated status
            window.location.reload();
        } else {
            alert('Error: ' + (data.error || 'Failed to generate invoice'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error generating invoice. Please try again.');
    })
    .finally(() => {
        // Restore button state
        button.innerHTML = originalText;
        button.disabled = false;
    });
}
</script>
{% endblock %}
