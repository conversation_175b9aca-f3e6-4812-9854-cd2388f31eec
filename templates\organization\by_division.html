{% extends "base.html" %}
{% block title %}Team by Division - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Team by Division</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <a href="{{ url_for('organization', view='chart') }}" class="btn btn-secondary">
                                <i class="fas fa-project-diagram"></i> View Organization Chart
                            </a>
                            <a href="{{ url_for('organization', view='divisions') }}" class="btn btn-secondary">
                                <i class="fas fa-building"></i> View Divisions
                            </a>
                            <a href="{{ url_for('organization', view='team_members') }}" class="btn btn-secondary">
                                <i class="fas fa-user-friends"></i> View Team Members
                            </a>
                            <a href="{{ url_for('organization', view='team_by_division') }}" class="btn btn-primary">
                                <i class="fas fa-users"></i> View Team by Division
                            </a>
                        </div>
                    </div>

                    <!-- Division Selection -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="division_select">Select Division:</label>
                                <select class="form-control" id="division_select" onchange="filterByDivision()">
                                    <option value="">-- All Divisions --</option>
                                    {% for division in divisions %}
                                    <option value="{{ division }}" {% if selected_division == division %}selected{% endif %}>{{ division }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="search_members">Search Members:</label>
                                <input type="text" class="form-control" id="search_members" placeholder="Search by name or designation...">
                            </div>
                        </div>
                    </div>

                    <!-- Division Summary -->
                    {% if selected_division %}
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <h5><i class="fas fa-building"></i> {{ selected_division }} Division</h5>
                                <p><strong>Total Members:</strong> {{ team_members|length }} |
                                   <strong>Designations:</strong> {{ team_members|map(attribute='designation')|unique|list|length }}</p>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Team Members Display -->
                    {% if team_members %}
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-secondary text-white">
                                    <h6 class="mb-0">
                                        {% if selected_division %}
                                            {{ selected_division }} Team Members
                                        {% else %}
                                            All Team Members
                                        {% endif %}
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover" id="teamTable">
                                            <thead class="thead-dark">
                                                <tr>
                                                    <th>S.No</th>
                                                    <th>Name</th>
                                                    <th>Designation</th>
                                                    <th>Division</th>
                                                    <th>Level</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for member in team_members %}
                                                <tr>
                                                    <td>{{ member.sno }}</td>
                                                    <td><strong>{{ member.name }}</strong></td>
                                                    <td>{{ member.designation }}</td>
                                                    <td>
                                                        <span class="badge badge-primary">{{ member.division }}</span>
                                                    </td>
                                                    <td>
                                                        {% if 'GENERAL MANAGER' in member.designation %}
                                                            <span class="badge badge-danger">Level 1</span>
                                                        {% elif 'DEPUTY GENERAL MANAGER' in member.designation %}
                                                            <span class="badge badge-warning">Level 2</span>
                                                        {% elif 'BUSINESS UNIT HEAD' in member.designation %}
                                                            <span class="badge badge-info">Level 3</span>
                                                        {% elif 'BUSINESS MANAGER' in member.designation %}
                                                            <span class="badge badge-success">Level 4</span>
                                                        {% elif 'MANAGER' in member.designation %}
                                                            <span class="badge badge-primary">Level 5</span>
                                                        {% else %}
                                                            <span class="badge badge-secondary">Level 6</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-outline-info" title="View Details" data-toggle="modal" data-target="#memberDetailsModal{{ loop.index }}">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <a href="{{ url_for('organization', view='team_by_division', division=member.division) }}" class="btn btn-sm btn-outline-primary" title="View Division Team">
                                                            <i class="fas fa-users"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Division Statistics -->
                    {% if selected_division %}
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">{{ selected_division }} Division Statistics</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        {% set designations = team_members|map(attribute='designation')|unique|list %}
                                        {% for designation in designations %}
                                        {% set count = team_members|selectattr('designation', 'equalto', designation)|list|length %}
                                        <div class="col-md-4 mb-3">
                                            <div class="card bg-light">
                                                <div class="card-body text-center py-2">
                                                    <h5 class="mb-0">{{ count }}</h5>
                                                    <small>{{ designation }}</small>
                                                </div>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    {% else %}
                    <div class="row">
                        <div class="col-md-12">
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                {% if selected_division %}
                                    No team members found for {{ selected_division }} division.
                                {% else %}
                                    Please select a division to view team members.
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Member Details Modals -->
{% for member in team_members %}
<div class="modal fade" id="memberDetailsModal{{ loop.index }}" tabindex="-1" role="dialog" aria-labelledby="memberDetailsModalLabel{{ loop.index }}" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="memberDetailsModalLabel{{ loop.index }}">
                    <i class="fas fa-user"></i> {{ member.name }} - Details
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-id-card"></i> Personal Information</h6>
                            </div>
                            <div class="card-body">
                                <table class="table table-borderless table-sm">
                                    <tr>
                                        <td><strong>Full Name:</strong></td>
                                        <td>{{ member.name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Employee ID:</strong></td>
                                        <td>EMP{{ "%04d"|format(member.sno) }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Email:</strong></td>
                                        <td>{{ member.name.lower().replace(' ', '.') }}@medivent.com</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Phone:</strong></td>
                                        <td>+92-300-{{ "%07d"|format((member.sno * 1234567) % 10000000) }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Join Date:</strong></td>
                                        <td>Jan 2020</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-briefcase"></i> Professional Information</h6>
                            </div>
                            <div class="card-body">
                                <table class="table table-borderless table-sm">
                                    <tr>
                                        <td><strong>Designation:</strong></td>
                                        <td>{{ member.designation }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Division:</strong></td>
                                        <td><span class="badge badge-primary">{{ member.division }}</span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Level:</strong></td>
                                        <td>{{ member.level }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Department:</strong></td>
                                        <td>Sales & Marketing</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Location:</strong></td>
                                        <td>
                                            {% if member.sno % 3 == 0 %}
                                                Karachi Office
                                            {% elif member.sno % 3 == 1 %}
                                                Lahore Office
                                            {% else %}
                                                Islamabad Office
                                            {% endif %}
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-chart-line"></i> Performance Summary (Mock Data)</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h4 class="text-success">{{ (member.sno * 15 + 85) }}%</h4>
                                            <small class="text-muted">Target Achievement</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h4 class="text-info">₨{{ "{:,}".format(member.sno * 125000 + 500000) }}</h4>
                                            <small class="text-muted">Monthly Sales</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h4 class="text-warning">{{ member.sno * 2 + 15 }}</h4>
                                            <small class="text-muted">Active Clients</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h4 class="text-primary">{{ member.sno + 5 }}</h4>
                                            <small class="text-muted">Orders This Month</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary">
                    <i class="fas fa-edit"></i> Edit Profile
                </button>
                <button type="button" class="btn btn-info">
                    <i class="fas fa-envelope"></i> Send Message
                </button>
            </div>
        </div>
    </div>
</div>
{% endfor %}

<script>
function filterByDivision() {
    var division = document.getElementById('division_select').value;
    var url = new URL(window.location.href);
    url.searchParams.set('view', 'team_by_division');
    if (division) {
        url.searchParams.set('division', division);
    } else {
        url.searchParams.delete('division');
    }
    window.location.href = url.toString();
}

$(document).ready(function() {
    // Search functionality
    $('#search_members').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('#teamTable tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });
});
</script>
{% endblock %}
