{% extends "base.html" %}
{% block title %}Organization Chart - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Medivent Pharmaceuticals - Organization Chart</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <a href="{{ url_for('organization', view='chart') }}" class="btn btn-primary">
                                <i class="fas fa-project-diagram"></i> Organization Chart
                            </a>
                            <a href="{{ url_for('organization', view='divisions') }}" class="btn btn-secondary">
                                <i class="fas fa-building"></i> View Divisions
                            </a>
                            <a href="{{ url_for('organization', view='team_members') }}" class="btn btn-secondary">
                                <i class="fas fa-user-friends"></i> View Team Members
                            </a>
                            <a href="{{ url_for('organization', view='team_by_division') }}" class="btn btn-secondary">
                                <i class="fas fa-users"></i> View Team by Division
                            </a>
                        </div>
                    </div>

                    <!-- Organization Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">Total Team Members</h6>
                                            <h4>{{ team_data|length }}</h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-users fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">Total Divisions</h6>
                                            <h4>{{ team_data|map(attribute='division')|unique|list|length }}</h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-building fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">Management Levels</h6>
                                            <h4>{{ team_data|map(attribute='designation')|unique|list|length }}</h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-layer-group fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">Business Unit Heads</h6>
                                            <h4>{{ team_data|selectattr('designation', 'equalto', 'BUSINESS UNIT HEAD')|list|length }}</h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-user-tie fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Organization Chart by Division -->
                    <div class="row">
                        {% set divisions = team_data|map(attribute='division')|unique|list %}
                        {% for division in divisions %}
                        <div class="col-md-6 mb-4">
                            <div class="card">
                                <div class="card-header bg-dark text-white">
                                    <h6 class="mb-0">{{ division }} Division</h6>
                                </div>
                                <div class="card-body">
                                    {% set division_members = team_data|selectattr('division', 'equalto', division)|list %}

                                    <!-- Division Head -->
                                    {% set division_head = [] %}
                                    {% for member in division_members %}
                                        {% if 'GENERAL MANAGER' in member.designation or 'BUSINESS UNIT HEAD' in member.designation %}
                                            {% set _ = division_head.append(member) %}
                                        {% endif %}
                                    {% endfor %}
                                    {% if division_head %}
                                    <div class="text-center mb-3">
                                        <div class="card bg-primary text-white">
                                            <div class="card-body py-2">
                                                <h6 class="mb-0">{{ division_head[0].name }}</h6>
                                                <small>{{ division_head[0].designation }}</small>
                                            </div>
                                        </div>
                                    </div>
                                    {% endif %}

                                    <!-- Management Level -->
                                    {% set managers = [] %}
                                    {% for member in division_members %}
                                        {% if ('MANAGER' in member.designation and 'GENERAL MANAGER' not in member.designation and 'BUSINESS UNIT HEAD' not in member.designation) or 'DEPUTY' in member.designation %}
                                            {% set _ = managers.append(member) %}
                                        {% endif %}
                                    {% endfor %}
                                    {% if managers %}
                                    <div class="row mb-2">
                                        {% for manager in managers %}
                                        <div class="col-md-6 mb-2">
                                            <div class="card bg-success text-white">
                                                <div class="card-body py-1">
                                                    <h6 class="mb-0" style="font-size: 0.8rem;">{{ manager.name }}</h6>
                                                    <small style="font-size: 0.7rem;">{{ manager.designation }}</small>
                                                </div>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}

                                    <!-- Sales Team -->
                                    {% set sales_team = [] %}
                                    {% for member in division_members %}
                                        {% if 'GENERAL MANAGER' not in member.designation and 'BUSINESS UNIT HEAD' not in member.designation and 'MANAGER' not in member.designation and 'DEPUTY' not in member.designation %}
                                            {% set _ = sales_team.append(member) %}
                                        {% endif %}
                                    {% endfor %}
                                    {% if sales_team %}
                                    <div class="row">
                                        {% for member in sales_team %}
                                        <div class="col-md-12 mb-1">
                                            <div class="card bg-light">
                                                <div class="card-body py-1">
                                                    <h6 class="mb-0" style="font-size: 0.75rem;">{{ member.name }}</h6>
                                                    <small style="font-size: 0.65rem;" class="text-muted">{{ member.designation }}</small>
                                                </div>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}

                                    <!-- Division Stats -->
                                    <div class="mt-3 pt-2 border-top">
                                        <small class="text-muted">
                                            <strong>Total Members:</strong> {{ division_members|length }} |
                                            <strong>Designations:</strong> {{ division_members|map(attribute='designation')|unique|list|length }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- Hierarchical Structure -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-secondary text-white">
                                    <h6 class="mb-0">Hierarchical Structure Overview</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped">
                                            <thead class="thead-dark">
                                                <tr>
                                                    <th>Level</th>
                                                    <th>Designation</th>
                                                    <th>Count</th>
                                                    <th>Names</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% set designations = team_data|map(attribute='designation')|unique|list %}
                                                {% for designation in designations %}
                                                {% set members = team_data|selectattr('designation', 'equalto', designation)|list %}
                                                <tr>
                                                    <td>
                                                        {% if 'GENERAL MANAGER' in designation %}
                                                            <span class="badge badge-danger">Level 1</span>
                                                        {% elif 'DEPUTY GENERAL MANAGER' in designation %}
                                                            <span class="badge badge-warning">Level 2</span>
                                                        {% elif 'BUSINESS UNIT HEAD' in designation %}
                                                            <span class="badge badge-info">Level 3</span>
                                                        {% elif 'BUSINESS MANAGER' in designation %}
                                                            <span class="badge badge-success">Level 4</span>
                                                        {% elif 'MANAGER' in designation %}
                                                            <span class="badge badge-primary">Level 5</span>
                                                        {% else %}
                                                            <span class="badge badge-secondary">Level 6</span>
                                                        {% endif %}
                                                    </td>
                                                    <td><strong>{{ designation }}</strong></td>
                                                    <td><span class="badge badge-dark">{{ members|length }}</span></td>
                                                    <td>
                                                        {% for member in members %}
                                                            <small class="text-muted">{{ member.name }}{% if not loop.last %}, {% endif %}</small>
                                                        {% endfor %}
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
