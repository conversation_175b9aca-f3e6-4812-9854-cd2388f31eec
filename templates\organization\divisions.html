{% extends 'base.html' %}

{% block title %}Divisions{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Divisions</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <a href="{{ url_for('organization', view='chart') }}" class="btn btn-secondary">
                                <i class="fas fa-project-diagram"></i> View Organization Chart
                            </a>
                            <a href="{{ url_for('organization', view='team_members') }}" class="btn btn-secondary">
                                <i class="fas fa-user-friends"></i> View Team Members
                            </a>
                            <a href="{{ url_for('organization', view='team_by_division') }}" class="btn btn-secondary">
                                <i class="fas fa-users"></i> View Team by Division
                            </a>

                        </div>
                    </div>

                    <!-- Division Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <h5><i class="fas fa-info-circle"></i> Medivent Pharmaceuticals - Division Overview</h5>
                                <p>Total Divisions: <strong>{{ divisions|length }}</strong> | Total Team Members: <strong>{{ divisions.values()|sum(attribute='total_count') }}</strong></p>
                            </div>
                        </div>
                    </div>

                    <!-- Divisions Grid -->
                    <div class="row">
                        {% for division_name, division_data in divisions.items() %}
                        <div class="col-md-6 mb-4">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">{{ division_name }}</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <div class="card bg-success text-white">
                                                <div class="card-body text-center py-2">
                                                    <h4 class="mb-0">{{ division_data.total_count }}</h4>
                                                    <small>Total Members</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="card bg-info text-white">
                                                <div class="card-body text-center py-2">
                                                    <h4 class="mb-0">{{ division_data.designations|length }}</h4>
                                                    <small>Designations</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Division Head -->
                                    {% set division_head = None %}
                                    {% for member in division_data.members %}
                                        {% if ('GENERAL MANAGER' in member.designation or 'BUSINESS UNIT HEAD' in member.designation) and not division_head %}
                                            {% set division_head = member %}
                                        {% endif %}
                                    {% endfor %}
                                    {% if division_head %}
                                    <div class="mb-3">
                                        <h6 class="text-muted">Division Head:</h6>
                                        <div class="card bg-warning text-dark">
                                            <div class="card-body py-2">
                                                <strong>{{ division_head.name }}</strong><br>
                                                <small>{{ division_head.designation }}</small>
                                            </div>
                                        </div>
                                    </div>
                                    {% endif %}

                                    <!-- Designations Breakdown -->
                                    <div class="mb-3">
                                        <h6 class="text-muted">Team Structure:</h6>
                                        {% for designation, count in division_data.designations.items() %}
                                        <span class="badge badge-secondary mr-1 mb-1">{{ designation }}: {{ count }}</span>
                                        {% endfor %}
                                    </div>

                                    <!-- Actions -->
                                    <div class="text-center">
                                        <a href="{{ url_for('organization', view='team_by_division', division=division_name) }}" class="btn btn-primary">
                                            <i class="fas fa-users"></i> View Team Members
                                        </a>
                                        <a href="{{ url_for('organization', view='chart') }}" class="btn btn-secondary">
                                            <i class="fas fa-project-diagram"></i> View in Chart
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- Detailed Table -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-secondary text-white">
                                    <h6 class="mb-0">Detailed Division Information</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead class="thead-dark">
                                                <tr>
                                                    <th>Division</th>
                                                    <th>Head</th>
                                                    <th>Total Members</th>
                                                    <th>Key Designations</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for division_name, division_data in divisions.items() %}
                                                <tr>
                                                    <td><strong>{{ division_name }}</strong></td>
                                                    <td>
                                                        {% set division_head = None %}
                                                        {% for member in division_data.members %}
                                                            {% if ('GENERAL MANAGER' in member.designation or 'BUSINESS UNIT HEAD' in member.designation) and not division_head %}
                                                                {% set division_head = member %}
                                                            {% endif %}
                                                        {% endfor %}
                                                        {% if division_head %}
                                                            {{ division_head.name }}<br>
                                                            <small class="text-muted">{{ division_head.designation }}</small>
                                                        {% else %}
                                                            <span class="text-muted">No head assigned</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        <span class="badge badge-primary">{{ division_data.total_count }} members</span>
                                                    </td>
                                                    <td>
                                                        <small>
                                                            {% for designation, count in division_data.designations.items() %}
                                                                {{ designation }} ({{ count }}){% if not loop.last %}, {% endif %}
                                                            {% endfor %}
                                                        </small>
                                                    </td>
                                                    <td>
                                                        <a href="{{ url_for('organization', view='team_by_division', division=division_name) }}" class="btn btn-sm btn-info">
                                                            <i class="fas fa-users"></i> View Team
                                                        </a>
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


{% endblock %}
