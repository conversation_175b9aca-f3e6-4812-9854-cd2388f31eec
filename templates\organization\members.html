{% extends 'base.html' %}

{% block title %}Team Members{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Team Members</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <a href="{{ url_for('organization', view='chart') }}" class="btn btn-secondary">
                                <i class="fas fa-project-diagram"></i> View Organization Chart
                            </a>
                            <a href="{{ url_for('organization', view='divisions') }}" class="btn btn-secondary">
                                <i class="fas fa-building"></i> View Divisions
                            </a>
                            <a href="{{ url_for('organization', view='team_by_division') }}" class="btn btn-secondary">
                                <i class="fas fa-users"></i> View Team by Division
                            </a>
                        </div>
                    </div>

                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4>{{ team_members|length }}</h4>
                                    <small>Total Team Members</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    {% set unique_divisions = [] %}
                                    {% for member in team_members %}
                                        {% if member.division not in unique_divisions %}
                                            {% set _ = unique_divisions.append(member.division) %}
                                        {% endif %}
                                    {% endfor %}
                                    <h4>{{ unique_divisions|length }}</h4>
                                    <small>Divisions</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    {% set unique_designations = [] %}
                                    {% for member in team_members %}
                                        {% if member.designation not in unique_designations %}
                                            {% set _ = unique_designations.append(member.designation) %}
                                        {% endif %}
                                    {% endfor %}
                                    <h4>{{ unique_designations|length }}</h4>
                                    <small>Designations</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    {% set manager_count = 0 %}
                                    {% for member in team_members %}
                                        {% if 'MANAGER' in member.designation %}
                                            {% set manager_count = manager_count + 1 %}
                                        {% endif %}
                                    {% endfor %}
                                    <h4>{{ manager_count }}</h4>
                                    <small>Managers</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Search and Filter -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <input type="text" class="form-control" id="searchMembers" placeholder="Search team members...">
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="filterDivision">
                                <option value="">All Divisions</option>
                                {% for division in unique_divisions %}
                                <option value="{{ division }}">{{ division }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="filterDesignation">
                                <option value="">All Designations</option>
                                {% for designation in unique_designations %}
                                <option value="{{ designation }}">{{ designation }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="teamMembersTable">
                            <thead class="thead-dark">
                                <tr>
                                    <th>S.No</th>
                                    <th>Name</th>
                                    <th>Designation</th>
                                    <th>Division</th>
                                    <th>Level</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if team_members %}
                                    {% for member in team_members %}
                                    <tr>
                                        <td>{{ member.sno }}</td>
                                        <td><strong>{{ member.name }}</strong></td>
                                        <td>{{ member.designation }}</td>
                                        <td>
                                            <span class="badge badge-primary">{{ member.division }}</span>
                                        </td>
                                        <td>
                                            {% if 'GENERAL MANAGER' in member.designation %}
                                                <span class="badge badge-danger">Level 1</span>
                                            {% elif 'DEPUTY GENERAL MANAGER' in member.designation %}
                                                <span class="badge badge-warning">Level 2</span>
                                            {% elif 'BUSINESS UNIT HEAD' in member.designation %}
                                                <span class="badge badge-info">Level 3</span>
                                            {% elif 'BUSINESS MANAGER' in member.designation %}
                                                <span class="badge badge-success">Level 4</span>
                                            {% elif 'MANAGER' in member.designation %}
                                                <span class="badge badge-primary">Level 5</span>
                                            {% else %}
                                                <span class="badge badge-secondary">Level 6</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{{ url_for('organization', view='team_by_division', division=member.division) }}" class="btn btn-sm btn-outline-primary" title="View Division Team">
                                                <i class="fas fa-users"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-info" title="View Details" data-toggle="modal" data-target="#memberDetailsModal{{ loop.index }}">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="6" class="text-center">No team members found</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Search functionality
    $('#searchMembers').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('#teamMembersTable tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });

    // Division filter
    $('#filterDivision').on('change', function() {
        var division = $(this).val();
        if (division === '') {
            $('#teamMembersTable tbody tr').show();
        } else {
            $('#teamMembersTable tbody tr').hide();
            $('#teamMembersTable tbody tr').filter(function() {
                return $(this).find('td:nth-child(4)').text().indexOf(division) > -1;
            }).show();
        }
    });

    // Designation filter
    $('#filterDesignation').on('change', function() {
        var designation = $(this).val();
        if (designation === '') {
            $('#teamMembersTable tbody tr').show();
        } else {
            $('#teamMembersTable tbody tr').hide();
            $('#teamMembersTable tbody tr').filter(function() {
                return $(this).find('td:nth-child(3)').text().indexOf(designation) > -1;
            }).show();
        }
    });
});
</script>

<!-- Member Details Modals -->
{% for member in team_members %}
<div class="modal fade" id="memberDetailsModal{{ loop.index }}" tabindex="-1" role="dialog" aria-labelledby="memberDetailsModalLabel{{ loop.index }}" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="memberDetailsModalLabel{{ loop.index }}">
                    <i class="fas fa-user"></i> {{ member.name }} - Details
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-id-card"></i> Personal Information</h6>
                            </div>
                            <div class="card-body">
                                <table class="table table-borderless table-sm">
                                    <tr>
                                        <td><strong>Full Name:</strong></td>
                                        <td>{{ member.name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Employee ID:</strong></td>
                                        <td>{{ member.employee_id or ('EMP' + (member.sno|string).zfill(3)) }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Email:</strong></td>
                                        <td>{{ member.email or (member.name.lower().replace(' ', '.') + '@mediventpharma.pk') }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Phone:</strong></td>
                                        <td>{{ member.phone or ('0300-' + ((member.sno * 1111111)|string).zfill(7)) }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Join Date:</strong></td>
                                        <td>{{ member.join_date or '2020-01-01' }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-briefcase"></i> Professional Information</h6>
                            </div>
                            <div class="card-body">
                                <table class="table table-borderless table-sm">
                                    <tr>
                                        <td><strong>Designation:</strong></td>
                                        <td>{{ member.designation }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Division:</strong></td>
                                        <td><span class="badge badge-primary">{{ member.division }}</span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Level:</strong></td>
                                        <td>{{ member.level }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Department:</strong></td>
                                        <td>Sales & Marketing</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Location:</strong></td>
                                        <td>{{ member.location or 'Karachi' }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-info-circle"></i> Additional Information</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>Reports To:</strong> {{ member.reports_to or 'Not specified' }}</p>
                                        <p><strong>Experience:</strong> {{ member.experience or 'Not specified' }}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>Level:</strong> {{ member.level or 'Not specified' }}</p>
                                        <p><strong>Status:</strong> <span class="badge badge-success">Active</span></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary">
                    <i class="fas fa-edit"></i> Edit Profile
                </button>
                <button type="button" class="btn btn-info">
                    <i class="fas fa-envelope"></i> Send Message
                </button>
            </div>
        </div>
    </div>
</div>
{% endfor %}

{% endblock %}
