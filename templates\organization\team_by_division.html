{% extends 'base.html' %}

{% block title %}Team by Division - Organization Structure{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-users"></i>
                        {% if division_name %}
                            {{ division_name }} Division Team
                        {% else %}
                            Team by Division
                        {% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Navigation -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <a href="{{ url_for('organization', view='chart') }}" class="btn btn-secondary">
                                <i class="fas fa-project-diagram"></i> Organization Chart
                            </a>
                            <a href="{{ url_for('organization', view='divisions') }}" class="btn btn-secondary">
                                <i class="fas fa-building"></i> View Divisions
                            </a>
                            <a href="{{ url_for('organization', view='team_members') }}" class="btn btn-secondary">
                                <i class="fas fa-user-friends"></i> All Team Members
                            </a>
                            <a href="{{ url_for('organization', view='team_by_division') }}" class="btn btn-primary">
                                <i class="fas fa-users"></i> Team by Division
                            </a>
                        </div>
                    </div>

                    {% if division_name and division_members %}
                    <!-- Specific Division Team -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">{{ division_name }} Division - {{ division_members|length }} Team Members</h6>
                                </div>
                                <div class="card-body">
                                    <!-- Division Summary -->
                                    <div class="row mb-4">
                                        <div class="col-md-4">
                                            <div class="card bg-light">
                                                <div class="card-body text-center">
                                                    <h3 class="text-primary">{{ division_members|length }}</h3>
                                                    <p class="mb-0">Total Members</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="card bg-light">
                                                <div class="card-body text-center">
                                                    {% set unique_designations = [] %}
                                                    {% for member in division_members %}
                                                        {% if member.designation not in unique_designations %}
                                                            {% set _ = unique_designations.append(member.designation) %}
                                                        {% endif %}
                                                    {% endfor %}
                                                    <h3 class="text-success">{{ unique_designations|length }}</h3>
                                                    <p class="mb-0">Designations</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="card bg-light">
                                                <div class="card-body text-center">
                                                    {% set division_head = None %}
                                                    {% for member in division_members %}
                                                        {% if ('GENERAL MANAGER' in member.designation or 'BUSINESS UNIT HEAD' in member.designation) and not division_head %}
                                                            {% set division_head = member %}
                                                        {% endif %}
                                                    {% endfor %}
                                                    <h6 class="text-warning">Division Head</h6>
                                                    <p class="mb-0">
                                                        {% if division_head %}
                                                            {{ division_head.name }}
                                                        {% else %}
                                                            Not Assigned
                                                        {% endif %}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Team Members Table -->
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead class="thead-dark">
                                                <tr>
                                                    <th>Employee ID</th>
                                                    <th>Name</th>
                                                    <th>Designation</th>
                                                    <th>Reports To</th>
                                                    <th>Contact</th>
                                                    <th>Level</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for member in division_members %}
                                                <tr>
                                                    <td><strong>{{ member.employee_id }}</strong></td>
                                                    <td>
                                                        <strong>{{ member.name }}</strong>
                                                        {% if 'GENERAL MANAGER' in member.designation or 'BUSINESS UNIT HEAD' in member.designation %}
                                                            <span class="badge badge-warning ml-2">Head</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        <span class="badge
                                                            {% if 'GENERAL MANAGER' in member.designation or 'BUSINESS UNIT HEAD' in member.designation %}badge-warning
                                                            {% elif 'BUSINESS MANAGER' in member.designation %}badge-primary
                                                            {% elif 'REGIONAL SALES MANAGER' in member.designation %}badge-info
                                                            {% elif 'AREA SALES MANAGER' in member.designation %}badge-secondary
                                                            {% else %}badge-light
                                                            {% endif %}">
                                                            {{ member.designation }}
                                                        </span>
                                                    </td>
                                                    <td>{{ member.reports_to }}</td>
                                                    <td>
                                                        <small>
                                                            <i class="fas fa-phone"></i> {{ member.phone }}<br>
                                                            <i class="fas fa-envelope"></i> {{ member.email }}
                                                        </small>
                                                    </td>
                                                    <td>
                                                        <span class="badge badge-outline-dark">Level {{ member.level }}</span>
                                                    </td>
                                                    <td>
                                                        <button class="btn btn-sm btn-info" data-toggle="modal" data-target="#memberDetailsModal{{ loop.index }}">
                                                            <i class="fas fa-eye"></i> View
                                                        </button>
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>

                                    <!-- Back Button -->
                                    <div class="text-center mt-4">
                                        <a href="{{ url_for('organization', view='divisions') }}" class="btn btn-secondary">
                                            <i class="fas fa-arrow-left"></i> Back to Divisions
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {% else %}
                    <!-- Division Selection -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-secondary text-white">
                                    <h6 class="mb-0">Select a Division to View Team Members</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        {% for division_name, division_data in divisions_summary.items() %}
                                        {% if division_name != 'EXECUTIVE' %}
                                        <div class="col-md-4 mb-3">
                                            <div class="card h-100 border-primary">
                                                <div class="card-header bg-primary text-white text-center">
                                                    <h6 class="mb-0">{{ division_name }}</h6>
                                                </div>
                                                <div class="card-body text-center">
                                                    <h3 class="text-primary">{{ division_data.total_count }}</h3>
                                                    <p class="text-muted">Team Members</p>

                                                    <!-- Division Head -->
                                                    {% set division_head = None %}
                                                    {% for member in division_data.members %}
                                                        {% if ('GENERAL MANAGER' in member.designation or 'BUSINESS UNIT HEAD' in member.designation) and not division_head %}
                                                            {% set division_head = member %}
                                                        {% endif %}
                                                    {% endfor %}

                                                    {% if division_head %}
                                                    <div class="mb-3">
                                                        <small class="text-muted">Division Head:</small><br>
                                                        <strong>{{ division_head.name }}</strong><br>
                                                        <small>{{ division_head.designation }}</small>
                                                    </div>
                                                    {% endif %}

                                                    <a href="{{ url_for('organization', view='team_by_division', division=division_name) }}" class="btn btn-primary">
                                                        <i class="fas fa-users"></i> View Team
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                        {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Member Details Modals -->
{% for member in division_members %}
<div class="modal fade" id="memberDetailsModal{{ loop.index }}" tabindex="-1" role="dialog" aria-labelledby="memberDetailsModalLabel{{ loop.index }}" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="memberDetailsModalLabel{{ loop.index }}">
                    <i class="fas fa-user"></i> {{ member.name }} - Details
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-id-card"></i> Personal Information</h6>
                            </div>
                            <div class="card-body">
                                <table class="table table-borderless table-sm">
                                    <tr>
                                        <td><strong>Full Name:</strong></td>
                                        <td>{{ member.name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Employee ID:</strong></td>
                                        <td>{{ member.employee_id }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Email:</strong></td>
                                        <td>{{ member.email }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Phone:</strong></td>
                                        <td>{{ member.phone }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Join Date:</strong></td>
                                        <td>Jan 2020</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-briefcase"></i> Professional Information</h6>
                            </div>
                            <div class="card-body">
                                <table class="table table-borderless table-sm">
                                    <tr>
                                        <td><strong>Designation:</strong></td>
                                        <td>{{ member.designation }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Division:</strong></td>
                                        <td><span class="badge badge-primary">{{ member.division }}</span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Level:</strong></td>
                                        <td>{{ member.level }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Reports To:</strong></td>
                                        <td>{{ member.reports_to }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Department:</strong></td>
                                        <td>Sales & Marketing</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-chart-line"></i> Performance Summary (Mock Data)</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h4 class="text-success">{{ (loop.index * 15 + 85) }}%</h4>
                                            <small class="text-muted">Target Achievement</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h4 class="text-info">₨{{ "{:,}".format(loop.index * 125000 + 500000) }}</h4>
                                            <small class="text-muted">Monthly Sales</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h4 class="text-warning">{{ loop.index * 2 + 15 }}</h4>
                                            <small class="text-muted">Active Clients</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h4 class="text-primary">{{ loop.index + 5 }}</h4>
                                            <small class="text-muted">Orders This Month</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary">
                    <i class="fas fa-edit"></i> Edit Profile
                </button>
                <button type="button" class="btn btn-info">
                    <i class="fas fa-envelope"></i> Send Message
                </button>
            </div>
        </div>
    </div>
</div>
{% endfor %}


{% endblock %}
