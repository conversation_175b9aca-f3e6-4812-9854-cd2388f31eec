{% extends 'base.html' %}

{% block title %}DC Generation - Partial DC Management{% endblock %}

{% block extra_css %}
<style>
    .generation-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
    }
    
    .ready-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 15px;
        transition: all 0.2s ease;
        border-left: 4px solid #28a745;
    }
    
    .ready-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    
    .availability-badge {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .availability-full {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
    }
    
    .availability-partial {
        background: linear-gradient(135deg, #ffc107, #fd7e14);
        color: #212529;
    }
    
    .quick-stats {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .stat-item {
        text-align: center;
        padding: 10px;
    }
    
    .stat-number {
        font-size: 1.5rem;
        font-weight: bold;
        color: #28a745;
    }
    
    .stat-label {
        font-size: 0.9rem;
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row">
        <div class="col-12">
            <div class="generation-header">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2><i class="fas fa-file-export"></i> DC Generation Dashboard</h2>
                        <p class="mb-0">Generate delivery challans for orders with available inventory</p>
                    </div>
                    <div class="col-md-4 text-md-right">
                        <button class="btn btn-light" onclick="location.reload()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                        <a href="{{ url_for('partial_dc.pending_orders') }}" class="btn btn-outline-light ml-2">
                            <i class="fas fa-list"></i> View All Orders
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Statistics -->
    <div class="row">
        <div class="col-12">
            <div class="quick-stats">
                <div class="row">
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-number">{{ orders|length }}</div>
                            <div class="stat-label">Orders Ready</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-number">{{ orders|sum(attribute='available_items') }}</div>
                            <div class="stat-label">Available Items</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-number">{{ orders|sum(attribute='total_items') }}</div>
                            <div class="stat-label">Total Items</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-number">₹{{ "{:,.0f}".format(orders|sum(attribute='order_amount')) }}</div>
                            <div class="stat-label">Total Value</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Orders Ready for DC Generation -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-check-circle text-success"></i> Orders Ready for DC Generation
                    </h5>
                </div>
                <div class="card-body">
                    {% if orders %}
                    <div class="row">
                        {% for order in orders %}
                        <div class="col-lg-6 col-xl-4">
                            <div class="ready-card card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <div>
                                            <h6 class="card-title mb-1">{{ order.order_id }}</h6>
                                            <p class="text-muted mb-0">{{ order.customer_name }}</p>
                                        </div>
                                        <div class="text-right">
                                            {% if order.available_items == order.total_items %}
                                            <span class="availability-badge availability-full">
                                                <i class="fas fa-check"></i> Full
                                            </span>
                                            {% else %}
                                            <span class="availability-badge availability-partial">
                                                <i class="fas fa-exclamation"></i> Partial
                                            </span>
                                            {% endif %}
                                        </div>
                                    </div>
                                    
                                    <div class="row text-center mb-3">
                                        <div class="col-4">
                                            <small class="text-muted">Available</small>
                                            <div class="font-weight-bold text-success">{{ order.available_items }}</div>
                                        </div>
                                        <div class="col-4">
                                            <small class="text-muted">Total</small>
                                            <div class="font-weight-bold">{{ order.total_items }}</div>
                                        </div>
                                        <div class="col-4">
                                            <small class="text-muted">Value</small>
                                            <div class="font-weight-bold">₹{{ "{:,.0f}".format(order.order_amount) }}</div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <small class="text-muted">Order Date:</small>
                                        <div>{{ order.order_date.strftime('%Y-%m-%d') if order.order_date else 'N/A' }}</div>
                                    </div>
                                    
                                    <div class="d-flex justify-content-between">
                                        <a href="{{ url_for('partial_dc.order_details', order_id=order.order_id) }}" 
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye"></i> View Details
                                        </a>
                                        <a href="{{ url_for('partial_dc.generate_dc_form', order_id=order.order_id) }}" 
                                           class="btn btn-success btn-sm">
                                            <i class="fas fa-file-export"></i> Generate DC
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    
                    <!-- Bulk Actions -->
                    {% if orders|length > 1 %}
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> Bulk Actions</h6>
                                <p class="mb-2">Generate DCs for multiple orders at once:</p>
                                <button class="btn btn-primary" onclick="generateBulkDCs()">
                                    <i class="fas fa-layer-group"></i> Generate All Available DCs
                                </button>
                                <button class="btn btn-warning ml-2" onclick="generatePartialDCs()">
                                    <i class="fas fa-exclamation-triangle"></i> Generate Partial DCs Only
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-clipboard-check fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">No Orders Ready</h4>
                        <p class="text-muted">No orders have available inventory for DC generation at this time.</p>
                        <div class="mt-3">
                            <a href="{{ url_for('partial_dc.pending_orders') }}" class="btn btn-primary">
                                <i class="fas fa-list"></i> View Pending Orders
                            </a>
                            <a href="{{ url_for('inventory.index') }}" class="btn btn-info ml-2">
                                <i class="fas fa-warehouse"></i> Check Inventory
                            </a>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Tips -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="fas fa-lightbulb"></i> Quick Tips</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h6><i class="fas fa-check-circle text-success"></i> Full DC</h6>
                            <p class="small">All ordered items are available in inventory and can be delivered completely.</p>
                        </div>
                        <div class="col-md-4">
                            <h6><i class="fas fa-exclamation-triangle text-warning"></i> Partial DC</h6>
                            <p class="small">Some items are available. Generate partial DC and track remaining items.</p>
                        </div>
                        <div class="col-md-4">
                            <h6><i class="fas fa-sync-alt text-info"></i> Auto-Refresh</h6>
                            <p class="small">Inventory levels update in real-time. Refresh to see latest availability.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function generateBulkDCs() {
        if (confirm('Generate DCs for all orders with available inventory?')) {
            // Implementation for bulk DC generation
            alert('Bulk DC generation feature coming soon!');
        }
    }
    
    function generatePartialDCs() {
        if (confirm('Generate partial DCs for orders with partial inventory?')) {
            // Implementation for partial DC generation
            alert('Partial DC generation feature coming soon!');
        }
    }
    
    // Auto-refresh every 2 minutes
    setTimeout(function() {
        location.reload();
    }, 120000);
    
    // Add loading states to buttons
    $('.btn').click(function() {
        if ($(this).attr('href')) {
            $(this).html('<i class="fas fa-spinner fa-spin"></i> Loading...');
        }
    });
</script>
{% endblock %}
