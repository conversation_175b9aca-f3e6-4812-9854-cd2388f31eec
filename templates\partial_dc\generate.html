{% extends 'base.html' %}

{% block title %}DC Generation - Partial DC Management{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-file-export"></i> DC Generation
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h5>Orders Ready for DC Generation</h5>
                            {% if ready_orders %}
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Order ID</th>
                                            <th>Customer</th>
                                            <th>Order Date</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for order in ready_orders %}
                                        <tr>
                                            <td>{{ order.order_id }}</td>
                                            <td>{{ order.customer_name }}</td>
                                            <td>{{ order.order_date.strftime('%Y-%m-%d') if order.order_date else 'N/A' }}</td>
                                            <td>
                                                <span class="badge badge-warning">{{ order.status }}</span>
                                            </td>
                                            <td>
                                                <button class="btn btn-primary btn-sm" onclick="generateDC('{{ order.order_id }}')">
                                                    <i class="fas fa-file-export"></i> Generate DC
                                                </button>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="alert alert-info">
                                <h5>No Orders Ready</h5>
                                <p>No orders are currently ready for DC generation.</p>
                                <a href="/partial-dc/" class="btn btn-primary">Back to Dashboard</a>
                            </div>
                            {% endif %}
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Generation Statistics</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <strong>Ready Orders:</strong> {{ ready_orders|length }}
                                    </div>
                                    <div class="mb-3">
                                        <strong>Total Value:</strong> ₹{{ total_value|round(2) }}
                                    </div>
                                    <div class="mb-3">
                                        <strong>Average Order:</strong> ₹{{ avg_value|round(2) }}
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5>Quick Actions</h5>
                                </div>
                                <div class="card-body">
                                    <a href="/partial-dc/" class="btn btn-secondary btn-block mb-2">
                                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                                    </a>
                                    <a href="/partial-dc/status" class="btn btn-info btn-block">
                                        <i class="fas fa-truck"></i> Track Status
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function generateDC(orderId) {
    if (confirm('Generate DC for order ' + orderId + '?')) {
        // Implementation for DC generation
        alert('DC generation for ' + orderId + ' - Feature coming soon!');
    }
}
</script>
{% endblock %}
