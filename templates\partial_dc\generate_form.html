{% extends 'base.html' %}

{% block title %}Generate DC - {{ order.order_id }}{% endblock %}

{% block extra_css %}
<style>
    .form-header {
        background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
        color: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
    }
    
    .item-selection-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 15px;
        transition: all 0.2s ease;
    }
    
    .item-selection-card:hover {
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    
    .item-checkbox {
        transform: scale(1.2);
        margin-right: 10px;
    }
    
    .quantity-input {
        width: 80px;
        text-align: center;
    }
    
    .stock-indicator {
        padding: 4px 8px;
        border-radius: 10px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .stock-available {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .stock-partial {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }
    
    .stock-insufficient {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    
    .summary-card {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        position: sticky;
        top: 20px;
    }
    
    .summary-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        padding-bottom: 10px;
        border-bottom: 1px solid #dee2e6;
    }
    
    .summary-item:last-child {
        border-bottom: none;
        margin-bottom: 0;
        font-weight: bold;
        font-size: 1.1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Back Button -->
    <div class="row mb-3">
        <div class="col-12">
            <a href="{{ url_for('partial_dc.dc_generation') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to DC Generation
            </a>
        </div>
    </div>

    <!-- Form Header -->
    <div class="row">
        <div class="col-12">
            <div class="form-header">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2><i class="fas fa-file-export"></i> Generate Delivery Challan</h2>
                        <p class="mb-0">Order: {{ order.order_id }} | Customer: {{ order.customer_name }}</p>
                    </div>
                    <div class="col-md-4 text-md-right">
                        <h4 class="mb-0">₹{{ "{:,.2f}".format(order.order_amount) }}</h4>
                        <small>Total Order Value</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <form method="POST" id="dcGenerationForm">
        <div class="row">
            <!-- Items Selection -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list-check"></i> Select Items for Delivery
                        </h5>
                    </div>
                    <div class="card-body">
                        {% if items %}
                        <div class="mb-3">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="selectAll()">
                                <i class="fas fa-check-square"></i> Select All Available
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm ml-2" onclick="clearAll()">
                                <i class="fas fa-square"></i> Clear All
                            </button>
                            <button type="button" class="btn btn-outline-success btn-sm ml-2" onclick="selectMaxQuantities()">
                                <i class="fas fa-arrow-up"></i> Max Quantities
                            </button>
                        </div>
                        
                        {% for item in items %}
                        <div class="item-selection-card card">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-1">
                                        <input type="checkbox" class="item-checkbox" 
                                               id="check_{{ item.id }}" 
                                               onchange="toggleItem({{ item.id }})">
                                    </div>
                                    <div class="col-md-4">
                                        <h6 class="mb-1">{{ item.product_name or 'Unknown Product' }}</h6>
                                        <small class="text-muted">{{ item.strength or 'N/A' }}</small>
                                        <br><small class="text-muted">ID: {{ item.product_id }}</small>
                                    </div>
                                    <div class="col-md-2 text-center">
                                        <strong>{{ item.quantity }}</strong>
                                        <br><small class="text-muted">Ordered</small>
                                    </div>
                                    <div class="col-md-2 text-center">
                                        <strong class="{% if item.available_stock >= item.quantity %}text-success{% elif item.available_stock > 0 %}text-warning{% else %}text-danger{% endif %}">
                                            {{ item.available_stock }}
                                        </strong>
                                        <br><small class="text-muted">Available</small>
                                    </div>
                                    <div class="col-md-2 text-center">
                                        <input type="number" 
                                               class="form-control quantity-input" 
                                               name="item_{{ item.id }}"
                                               id="qty_{{ item.id }}"
                                               min="0" 
                                               max="{{ item.deliverable_qty }}"
                                               value="{{ item.deliverable_qty }}"
                                               disabled
                                               onchange="updateSummary()">
                                        <small class="text-muted">Deliver</small>
                                    </div>
                                    <div class="col-md-1 text-center">
                                        {% if item.available_stock >= item.quantity %}
                                        <span class="stock-indicator stock-available">Full</span>
                                        {% elif item.available_stock > 0 %}
                                        <span class="stock-indicator stock-partial">Partial</span>
                                        {% else %}
                                        <span class="stock-indicator stock-insufficient">None</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                        
                        {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                            <h5 class="text-muted">No Items Available</h5>
                            <p class="text-muted">No items in this order have available inventory for delivery.</p>
                            <a href="{{ url_for('partial_dc.pending_orders') }}" class="btn btn-primary">
                                <i class="fas fa-list"></i> Back to Orders
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Summary Sidebar -->
            <div class="col-lg-4">
                <div class="summary-card">
                    <h6 class="mb-3"><i class="fas fa-calculator"></i> DC Summary</h6>
                    
                    <div class="summary-item">
                        <span>Selected Items:</span>
                        <span id="selectedCount">0</span>
                    </div>
                    
                    <div class="summary-item">
                        <span>Total Quantity:</span>
                        <span id="totalQuantity">0</span>
                    </div>
                    
                    <div class="summary-item">
                        <span>Estimated Value:</span>
                        <span id="estimatedValue">₹0.00</span>
                    </div>
                    
                    <div class="summary-item">
                        <span><strong>DC Total:</strong></span>
                        <span id="dcTotal"><strong>₹0.00</strong></span>
                    </div>
                    
                    <hr>
                    
                    <div class="mb-3">
                        <label for="dcNotes" class="form-label">Notes (Optional):</label>
                        <textarea class="form-control" id="dcNotes" name="notes" rows="3" 
                                  placeholder="Add any special instructions or notes..."></textarea>
                    </div>
                    
                    {% if items %}
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-success btn-lg" id="generateBtn" disabled>
                            <i class="fas fa-file-export"></i> Generate DC
                        </button>
                        <button type="button" class="btn btn-outline-info" onclick="previewDC()">
                            <i class="fas fa-eye"></i> Preview
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function toggleItem(itemId) {
        const checkbox = document.getElementById(`check_${itemId}`);
        const quantityInput = document.getElementById(`qty_${itemId}`);
        
        if (checkbox.checked) {
            quantityInput.disabled = false;
            quantityInput.focus();
        } else {
            quantityInput.disabled = true;
            quantityInput.value = quantityInput.max;
        }
        
        updateSummary();
    }
    
    function selectAll() {
        document.querySelectorAll('.item-checkbox').forEach(checkbox => {
            checkbox.checked = true;
            toggleItem(checkbox.id.replace('check_', ''));
        });
    }
    
    function clearAll() {
        document.querySelectorAll('.item-checkbox').forEach(checkbox => {
            checkbox.checked = false;
            toggleItem(checkbox.id.replace('check_', ''));
        });
    }
    
    function selectMaxQuantities() {
        document.querySelectorAll('.quantity-input').forEach(input => {
            if (!input.disabled) {
                input.value = input.max;
            }
        });
        updateSummary();
    }
    
    function updateSummary() {
        let selectedCount = 0;
        let totalQuantity = 0;
        let estimatedValue = 0;
        
        document.querySelectorAll('.item-checkbox:checked').forEach(checkbox => {
            const itemId = checkbox.id.replace('check_', '');
            const quantityInput = document.getElementById(`qty_${itemId}`);
            const quantity = parseInt(quantityInput.value) || 0;
            
            if (quantity > 0) {
                selectedCount++;
                totalQuantity += quantity;
                // Estimate value based on unit price (would need to pass this data)
                estimatedValue += quantity * 100; // Placeholder calculation
            }
        });
        
        document.getElementById('selectedCount').textContent = selectedCount;
        document.getElementById('totalQuantity').textContent = totalQuantity;
        document.getElementById('estimatedValue').textContent = `₹${estimatedValue.toLocaleString()}.00`;
        document.getElementById('dcTotal').innerHTML = `<strong>₹${estimatedValue.toLocaleString()}.00</strong>`;
        
        // Enable/disable generate button
        const generateBtn = document.getElementById('generateBtn');
        if (generateBtn) {
            generateBtn.disabled = selectedCount === 0;
        }
    }
    
    function previewDC() {
        const selectedItems = [];
        document.querySelectorAll('.item-checkbox:checked').forEach(checkbox => {
            const itemId = checkbox.id.replace('check_', '');
            const quantityInput = document.getElementById(`qty_${itemId}`);
            const quantity = parseInt(quantityInput.value) || 0;
            
            if (quantity > 0) {
                selectedItems.push({
                    itemId: itemId,
                    quantity: quantity
                });
            }
        });
        
        if (selectedItems.length === 0) {
            alert('Please select at least one item to preview.');
            return;
        }
        
        // Show preview modal or redirect to preview page
        alert(`Preview: ${selectedItems.length} items selected for delivery.`);
    }
    
    // Form validation
    document.getElementById('dcGenerationForm').addEventListener('submit', function(e) {
        const selectedItems = document.querySelectorAll('.item-checkbox:checked').length;
        
        if (selectedItems === 0) {
            e.preventDefault();
            alert('Please select at least one item for delivery.');
            return false;
        }
        
        if (!confirm(`Generate delivery challan for ${selectedItems} items?`)) {
            e.preventDefault();
            return false;
        }
        
        // Show loading state
        const submitBtn = document.getElementById('generateBtn');
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';
        submitBtn.disabled = true;
    });
    
    // Initialize summary on page load
    updateSummary();
</script>
{% endblock %}
