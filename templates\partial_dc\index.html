{% extends 'base.html' %}

{% block title %}Partial DC Management{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-clock"></i> Partial DC Management Dashboard
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h3>{{ stats.total_orders or 0 }}</h3>
                                    <p>Total Orders</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h3>{{ stats.pending_orders or 0 }}</h3>
                                    <p>Pending Orders</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h3>{{ stats.completed_dcs or 0 }}</h3>
                                    <p>Completed DCs</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h3>{{ stats.pending_items or 0 }}</h3>
                                    <p>Pending Items</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Quick Actions</h5>
                                </div>
                                <div class="card-body">
                                    <a href="/partial-dc/generate" class="btn btn-primary btn-block mb-2">
                                        <i class="fas fa-file-export"></i> Generate DC
                                    </a>
                                    <a href="/partial-dc/status" class="btn btn-info btn-block mb-2">
                                        <i class="fas fa-truck"></i> Track Status
                                    </a>
                                    <a href="/partial-dc/reports" class="btn btn-success btn-block">
                                        <i class="fas fa-chart-line"></i> View Reports
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Recent Activity</h5>
                                </div>
                                <div class="card-body">
                                    {% if recent_orders %}
                                    <ul class="list-group list-group-flush">
                                        {% for order in recent_orders %}
                                        <li class="list-group-item d-flex justify-content-between">
                                            <span>{{ order.order_id }}</span>
                                            <span class="badge badge-primary">{{ order.status }}</span>
                                        </li>
                                        {% endfor %}
                                    </ul>
                                    {% else %}
                                    <p class="text-muted">No recent activity</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
