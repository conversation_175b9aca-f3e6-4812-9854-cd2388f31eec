{% extends 'base.html' %}

{% block title %}Order Details - {{ order.order_id }}{% endblock %}

{% block extra_css %}
<style>
    .order-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
    }
    
    .info-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    
    .stock-status {
        padding: 4px 12px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .stock-available {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
    }
    
    .stock-partial {
        background: linear-gradient(135deg, #ffc107, #fd7e14);
        color: #212529;
    }
    
    .stock-out {
        background: linear-gradient(135deg, #dc3545, #e83e8c);
        color: white;
    }
    
    .stock-delivered {
        background: linear-gradient(135deg, #6c757d, #495057);
        color: white;
    }
    
    .item-row {
        transition: all 0.2s ease;
    }
    
    .item-row:hover {
        background-color: #f8f9fa;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Back Button -->
    <div class="row mb-3">
        <div class="col-12">
            <a href="{{ url_for('partial_dc.pending_orders') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Pending Orders
            </a>
        </div>
    </div>

    <!-- Order Header -->
    <div class="row">
        <div class="col-12">
            <div class="order-header">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2><i class="fas fa-file-alt"></i> Order {{ order.order_id }}</h2>
                        <p class="mb-0">Customer: {{ order.customer_name }}</p>
                        <small>Order Date: {{ order.order_date.strftime('%Y-%m-%d %H:%M') if order.order_date else 'N/A' }}</small>
                    </div>
                    <div class="col-md-4 text-md-right">
                        <div class="mb-2">
                            <span class="badge badge-light badge-lg">{{ order.status }}</span>
                        </div>
                        <h4 class="mb-0">₹{{ "{:,.2f}".format(order.order_amount) }}</h4>
                        <small>Total Order Value</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Order Information -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="info-card card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-user"></i> Customer Information</h6>
                </div>
                <div class="card-body">
                    <table class="table table-sm table-borderless">
                        <tr>
                            <td><strong>Name:</strong></td>
                            <td>{{ order.customer_name }}</td>
                        </tr>
                        <tr>
                            <td><strong>Phone:</strong></td>
                            <td>{{ order.customer_phone or 'N/A' }}</td>
                        </tr>
                        <tr>
                            <td><strong>Address:</strong></td>
                            <td>{{ order.customer_address or 'N/A' }}</td>
                        </tr>
                        <tr>
                            <td><strong>Sales Agent:</strong></td>
                            <td>{{ order.sales_agent or 'N/A' }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="info-card card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-info-circle"></i> Order Status</h6>
                </div>
                <div class="card-body">
                    <table class="table table-sm table-borderless">
                        <tr>
                            <td><strong>Status:</strong></td>
                            <td><span class="badge badge-primary">{{ order.status }}</span></td>
                        </tr>
                        <tr>
                            <td><strong>Payment:</strong></td>
                            <td><span class="badge badge-warning">{{ order.payment_status or 'Pending' }}</span></td>
                        </tr>
                        <tr>
                            <td><strong>Approved:</strong></td>
                            <td>{{ order.approval_date.strftime('%Y-%m-%d') if order.approval_date else 'Not yet' }}</td>
                        </tr>
                        <tr>
                            <td><strong>Last Updated:</strong></td>
                            <td>{{ order.last_updated.strftime('%Y-%m-%d %H:%M') if order.last_updated else 'N/A' }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Order Items -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-list"></i> Order Items</h5>
                    <a href="{{ url_for('partial_dc.generate_dc_form', order_id=order.order_id) }}" 
                       class="btn btn-success btn-sm">
                        <i class="fas fa-file-export"></i> Generate DC
                    </a>
                </div>
                <div class="card-body">
                    {% if items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Strength</th>
                                    <th>Ordered Qty</th>
                                    <th>Available Stock</th>
                                    <th>Unit Price</th>
                                    <th>Line Total</th>
                                    <th>Status</th>
                                    <th>Stock Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in items %}
                                <tr class="item-row">
                                    <td>
                                        <strong>{{ item.product_name or 'Unknown Product' }}</strong>
                                        <br><small class="text-muted">{{ item.product_id }}</small>
                                    </td>
                                    <td>{{ item.strength or 'N/A' }}</td>
                                    <td>
                                        <strong>{{ item.quantity }}</strong>
                                    </td>
                                    <td>
                                        <strong class="{% if item.available_stock >= item.quantity %}text-success{% elif item.available_stock > 0 %}text-warning{% else %}text-danger{% endif %}">
                                            {{ item.available_stock }}
                                        </strong>
                                    </td>
                                    <td>₹{{ "{:,.2f}".format(item.unit_price) }}</td>
                                    <td>₹{{ "{:,.2f}".format(item.line_total) }}</td>
                                    <td>
                                        {% if item.status == 'Delivered' %}
                                        <span class="badge badge-success">{{ item.status }}</span>
                                        {% else %}
                                        <span class="badge badge-warning">{{ item.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if item.stock_status == 'Available' %}
                                        <span class="stock-status stock-available">Available</span>
                                        {% elif item.stock_status == 'Partial' %}
                                        <span class="stock-status stock-partial">Partial</span>
                                        {% elif item.stock_status == 'Delivered' %}
                                        <span class="stock-status stock-delivered">Delivered</span>
                                        {% else %}
                                        <span class="stock-status stock-out">Out of Stock</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Items Found</h5>
                        <p class="text-muted">This order has no items or all items have been delivered.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mt-4">
        <div class="col-12 text-center">
            <a href="{{ url_for('partial_dc.pending_orders') }}" class="btn btn-secondary">
                <i class="fas fa-list"></i> Back to Orders
            </a>
            {% if items %}
            <a href="{{ url_for('partial_dc.generate_dc_form', order_id=order.order_id) }}" 
               class="btn btn-success ml-2">
                <i class="fas fa-file-export"></i> Generate Delivery Challan
            </a>
            {% endif %}
            <a href="{{ url_for('orders.view', order_id=order.order_id) }}" 
               class="btn btn-info ml-2">
                <i class="fas fa-external-link-alt"></i> View in Orders
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Highlight rows based on stock status
    $(document).ready(function() {
        $('.item-row').each(function() {
            var stockStatus = $(this).find('.stock-status').text().trim();
            if (stockStatus === 'Out of Stock') {
                $(this).addClass('table-danger');
            } else if (stockStatus === 'Partial') {
                $(this).addClass('table-warning');
            } else if (stockStatus === 'Available') {
                $(this).addClass('table-success');
            }
        });
    });
</script>
{% endblock %}
