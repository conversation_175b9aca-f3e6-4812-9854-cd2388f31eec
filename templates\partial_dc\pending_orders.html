{% extends 'base.html' %}

{% block title %}Pending Orders - Partial DC Management{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .stats-number {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .stats-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    .order-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 15px;
        transition: transform 0.2s ease;
    }
    
    .order-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    
    .status-badge {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .status-pending {
        background: linear-gradient(135deg, #ffc107, #fd7e14);
        color: #212529;
    }
    
    .status-partial {
        background: linear-gradient(135deg, #17a2b8, #007bff);
        color: white;
    }
    
    .priority-high {
        border-left: 4px solid #dc3545;
    }
    
    .priority-medium {
        border-left: 4px solid #ffc107;
    }
    
    .priority-low {
        border-left: 4px solid #28a745;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fas fa-clock text-primary"></i> Pending Orders Overview</h2>
                    <p class="text-muted">Orders with pending items awaiting delivery</p>
                </div>
                <div>
                    <button class="btn btn-primary" onclick="location.reload()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                    <a href="{{ url_for('partial_dc.dc_generation') }}" class="btn btn-success">
                        <i class="fas fa-plus"></i> Generate DC
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="stats-card">
                <div class="stats-number">{{ stats.total_orders }}</div>
                <div class="stats-label">Pending Orders</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stats-card">
                <div class="stats-number">{{ stats.total_pending_items }}</div>
                <div class="stats-label">Pending Items</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stats-card">
                <div class="stats-number">{{ stats.total_pending_quantity }}</div>
                <div class="stats-label">Total Quantity</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stats-card">
                <div class="stats-number">₹{{ "{:,.2f}".format(stats.total_pending_value) }}</div>
                <div class="stats-label">Pending Value</div>
            </div>
        </div>
    </div>

    <!-- Orders List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list"></i> Orders with Pending Items
                    </h5>
                </div>
                <div class="card-body">
                    {% if orders %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Order ID</th>
                                    <th>Customer</th>
                                    <th>Order Date</th>
                                    <th>Status</th>
                                    <th>Items</th>
                                    <th>Pending</th>
                                    <th>Value</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in orders %}
                                <tr class="order-row">
                                    <td>
                                        <strong>{{ order.order_id }}</strong>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ order.customer_name }}</strong>
                                            {% if order.sales_agent %}
                                            <br><small class="text-muted">Agent: {{ order.sales_agent }}</small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        {{ order.order_date.strftime('%Y-%m-%d') if order.order_date else 'N/A' }}
                                    </td>
                                    <td>
                                        {% if order.status == 'Partially Delivered' %}
                                        <span class="status-badge status-partial">{{ order.status }}</span>
                                        {% else %}
                                        <span class="status-badge status-pending">{{ order.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="text-center">
                                            <strong>{{ order.total_items }}</strong>
                                            <br><small class="text-muted">total</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="text-center">
                                            <strong class="text-warning">{{ order.pending_items }}</strong>
                                            <br><small class="text-muted">{{ order.pending_quantity }} qty</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="text-center">
                                            <strong>₹{{ "{:,.2f}".format(order.order_amount) }}</strong>
                                            <br><small class="text-warning">₹{{ "{:,.2f}".format(order.pending_value) }} pending</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="btn-group-vertical btn-group-sm">
                                            <a href="{{ url_for('partial_dc.order_details', order_id=order.order_id) }}" 
                                               class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                            <a href="{{ url_for('partial_dc.generate_dc_form', order_id=order.order_id) }}" 
                                               class="btn btn-outline-success btn-sm">
                                                <i class="fas fa-file-export"></i> Generate DC
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">No Pending Orders</h4>
                        <p class="text-muted">All orders are either fully delivered or not yet approved.</p>
                        <a href="{{ url_for('orders.index') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> View All Orders
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-refresh every 5 minutes
    setTimeout(function() {
        location.reload();
    }, 300000);
    
    // Add hover effects
    $('.order-row').hover(
        function() {
            $(this).addClass('table-active');
        },
        function() {
            $(this).removeClass('table-active');
        }
    );
</script>
{% endblock %}
