{% extends 'base.html' %}

{% block title %}Reports & Analytics - Partial DC Management{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-chart-line"></i> Reports & Analytics
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h3>{{ analytics.total_orders }}</h3>
                                    <p>Total Orders</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h3>{{ analytics.partial_orders }}</h3>
                                    <p>Partial Orders</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h3>{{ analytics.completed_dcs }}</h3>
                                    <p>Completed DCs</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h3>₹{{ analytics.pending_value|round(2) }}</h3>
                                    <p>Pending Value</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <a href="/partial-dc/" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Dashboard
                            </a>
                            <button class="btn btn-primary ml-2" onclick="refreshData()">
                                <i class="fas fa-sync-alt"></i> Refresh Data
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function refreshData() {
    location.reload();
}
</script>
{% endblock %}
<style>
    .reports-header {
        background: linear-gradient(135deg, #fd7e14 0%, #ffc107 100%);
        color: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
    }
    
    .analytics-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        overflow: hidden;
    }
    
    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-align: center;
        padding: 25px;
        border-radius: 15px;
        margin-bottom: 20px;
    }
    
    .metric-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 10px;
    }
    
    .metric-label {
        font-size: 1rem;
        opacity: 0.9;
    }
    
    .chart-container {
        position: relative;
        height: 300px;
        margin-bottom: 20px;
    }
    
    .product-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f8f9fa;
    }
    
    .product-item:last-child {
        border-bottom: none;
    }
    
    .product-rank {
        background: linear-gradient(135deg, #ffc107, #fd7e14);
        color: #212529;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 0.9rem;
    }
    
    .trend-up {
        color: #28a745;
    }
    
    .trend-down {
        color: #dc3545;
    }
    
    .trend-stable {
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row">
        <div class="col-12">
            <div class="reports-header">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2><i class="fas fa-chart-line"></i> Reports & Analytics</h2>
                        <p class="mb-0">Comprehensive insights into partial DC performance and trends</p>
                    </div>
                    <div class="col-md-4 text-md-right">
                        <button class="btn btn-light" onclick="exportReport()">
                            <i class="fas fa-download"></i> Export Report
                        </button>
                        <button class="btn btn-outline-light ml-2" onclick="location.reload()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="metric-card">
                <div class="metric-number">{{ analytics.total_partial_orders }}</div>
                <div class="metric-label">Partial Orders</div>
                <small class="d-block mt-2">
                    <i class="fas fa-arrow-up trend-up"></i> +12% from last month
                </small>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="metric-card" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                <div class="metric-number">₹{{ "{:,.0f}".format(analytics.pending_value) }}</div>
                <div class="metric-label">Pending Value</div>
                <small class="d-block mt-2">
                    <i class="fas fa-arrow-down trend-down"></i> -5% from last week
                </small>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="metric-card" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); color: #212529;">
                <div class="metric-number">{{ analytics.avg_fulfillment_time }}</div>
                <div class="metric-label">Avg. Fulfillment (Days)</div>
                <small class="d-block mt-2">
                    <i class="fas fa-minus trend-stable"></i> Same as last month
                </small>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="metric-card" style="background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);">
                <div class="metric-number">87%</div>
                <div class="metric-label">Fulfillment Rate</div>
                <small class="d-block mt-2">
                    <i class="fas fa-arrow-up trend-up"></i> +3% from last month
                </small>
            </div>
        </div>
    </div>

    <!-- Charts and Analytics -->
    <div class="row">
        <!-- Fulfillment Trends -->
        <div class="col-lg-8">
            <div class="analytics-card card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-chart-area"></i> Fulfillment Trends</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="fulfillmentChart"></canvas>
                    </div>
                    <div class="row text-center mt-3">
                        <div class="col-4">
                            <h6 class="text-success">Full Deliveries</h6>
                            <h4>65%</h4>
                        </div>
                        <div class="col-4">
                            <h6 class="text-warning">Partial Deliveries</h6>
                            <h4>28%</h4>
                        </div>
                        <div class="col-4">
                            <h6 class="text-danger">Pending</h6>
                            <h4>7%</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Pending Products -->
        <div class="col-lg-4">
            <div class="analytics-card card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-trophy"></i> Top Pending Products</h6>
                </div>
                <div class="card-body">
                    {% if analytics.top_pending_products %}
                    {% for product in analytics.top_pending_products %}
                    <div class="product-item">
                        <div class="d-flex align-items-center">
                            <div class="product-rank">{{ loop.index }}</div>
                            <div class="ml-3">
                                <strong>{{ product.product_name }}</strong>
                                {% if product.strength %}
                                <br><small class="text-muted">{{ product.strength }}</small>
                                {% endif %}
                            </div>
                        </div>
                        <div class="text-right">
                            <strong class="text-warning">{{ product.total_pending }}</strong>
                            <br><small class="text-muted">{{ product.orders_count }} orders</small>
                        </div>
                    </div>
                    {% endfor %}
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                        <p class="text-muted">No pending products!</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Metrics -->
    <div class="row mt-4">
        <div class="col-lg-6">
            <div class="analytics-card card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-clock"></i> Delivery Performance</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="performanceChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="analytics-card card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-calendar-alt"></i> Monthly Comparison</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="monthlyChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Reports Table -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="analytics-card card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0"><i class="fas fa-table"></i> Detailed Performance Report</h6>
                    <div>
                        <select class="form-select form-select-sm" id="reportPeriod">
                            <option value="7">Last 7 Days</option>
                            <option value="30" selected>Last 30 Days</option>
                            <option value="90">Last 90 Days</option>
                        </select>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Orders Processed</th>
                                    <th>Full Deliveries</th>
                                    <th>Partial Deliveries</th>
                                    <th>Pending Items</th>
                                    <th>Success Rate</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Sample data - replace with actual data -->
                                <tr>
                                    <td>2025-08-04</td>
                                    <td>15</td>
                                    <td>10</td>
                                    <td>4</td>
                                    <td>1</td>
                                    <td><span class="badge badge-success">93%</span></td>
                                </tr>
                                <tr>
                                    <td>2025-08-03</td>
                                    <td>12</td>
                                    <td>8</td>
                                    <td>3</td>
                                    <td>1</td>
                                    <td><span class="badge badge-success">92%</span></td>
                                </tr>
                                <tr>
                                    <td>2025-08-02</td>
                                    <td>18</td>
                                    <td>11</td>
                                    <td>5</td>
                                    <td>2</td>
                                    <td><span class="badge badge-warning">89%</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Fulfillment Trends Chart
    const fulfillmentCtx = document.getElementById('fulfillmentChart').getContext('2d');
    new Chart(fulfillmentCtx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
            datasets: [{
                label: 'Full Deliveries',
                data: [65, 68, 70, 72, 69, 71, 75],
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                tension: 0.4
            }, {
                label: 'Partial Deliveries',
                data: [25, 28, 22, 20, 25, 23, 20],
                borderColor: '#ffc107',
                backgroundColor: 'rgba(255, 193, 7, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });

    // Performance Chart
    const performanceCtx = document.getElementById('performanceChart').getContext('2d');
    new Chart(performanceCtx, {
        type: 'doughnut',
        data: {
            labels: ['On Time', 'Delayed', 'Pending'],
            datasets: [{
                data: [70, 20, 10],
                backgroundColor: ['#28a745', '#ffc107', '#dc3545']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Monthly Comparison Chart
    const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
    new Chart(monthlyCtx, {
        type: 'bar',
        data: {
            labels: ['May', 'Jun', 'Jul', 'Aug'],
            datasets: [{
                label: 'Orders',
                data: [120, 135, 148, 95],
                backgroundColor: '#667eea'
            }, {
                label: 'Fulfilled',
                data: [110, 128, 140, 87],
                backgroundColor: '#28a745'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    function exportReport() {
        // Implementation for report export
        alert('Report export feature coming soon!');
    }

    // Update charts based on period selection
    document.getElementById('reportPeriod').addEventListener('change', function() {
        // Implementation to update charts based on selected period
        console.log('Period changed to:', this.value);
    });
</script>
{% endblock %}
