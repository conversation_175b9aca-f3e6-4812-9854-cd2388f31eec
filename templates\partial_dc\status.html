{% extends 'base.html' %}

{% block title %}Status Tracking - Partial DC Management{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-truck"></i> Status Tracking
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h3>{{ status_stats.created or 0 }}</h3>
                                    <p>Created</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h3>{{ status_stats.dispatched or 0 }}</h3>
                                    <p>Dispatched</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h3>{{ status_stats.delivered or 0 }}</h3>
                                    <p>Delivered</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-secondary text-white">
                                <div class="card-body text-center">
                                    <h3>{{ delivery_challans|length }}</h3>
                                    <p>Total DCs</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12">
                            <h5>Delivery Challans</h5>
                            {% if delivery_challans %}
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>DC Number</th>
                                            <th>Order ID</th>
                                            <th>Customer</th>
                                            <th>Created Date</th>
                                            <th>Status</th>
                                            <th>Amount</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for dc in delivery_challans %}
                                        <tr>
                                            <td><strong>{{ dc.dc_number }}</strong></td>
                                            <td>{{ dc.order_id }}</td>
                                            <td>{{ dc.customer_name }}</td>
                                            <td>{{ dc.created_date.strftime('%Y-%m-%d %H:%M') if dc.created_date else 'N/A' }}</td>
                                            <td>
                                                {% if dc.status == 'created' %}
                                                <span class="badge badge-primary">Created</span>
                                                {% elif dc.status == 'dispatched' %}
                                                <span class="badge badge-warning">Dispatched</span>
                                                {% elif dc.status == 'delivered' %}
                                                <span class="badge badge-success">Delivered</span>
                                                {% else %}
                                                <span class="badge badge-secondary">{{ dc.status|title }}</span>
                                                {% endif %}
                                            </td>
                                            <td>₹{{ dc.total_amount|round(2) if dc.total_amount else '0.00' }}</td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" onclick="viewDC('{{ dc.dc_number }}')">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    {% if dc.status == 'created' %}
                                                    <button class="btn btn-outline-warning" onclick="updateStatus('{{ dc.dc_number }}', 'dispatched')">
                                                        <i class="fas fa-truck"></i>
                                                    </button>
                                                    {% elif dc.status == 'dispatched' %}
                                                    <button class="btn btn-outline-success" onclick="updateStatus('{{ dc.dc_number }}', 'delivered')">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                    {% endif %}
                                                    <button class="btn btn-outline-info" onclick="downloadPDF('{{ dc.dc_number }}')">
                                                        <i class="fas fa-download"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="alert alert-info">
                                <h5>No Delivery Challans</h5>
                                <p>No delivery challans have been generated yet.</p>
                                <a href="/partial-dc/generate" class="btn btn-success">Generate First DC</a>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-12">
                            <a href="/partial-dc/" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Dashboard
                            </a>
                            <a href="/partial-dc/generate" class="btn btn-success ml-2">
                                <i class="fas fa-plus"></i> Generate New DC
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function viewDC(dcNumber) {
    alert('View DC ' + dcNumber + ' - Feature coming soon!');
}

function updateStatus(dcNumber, newStatus) {
    if (confirm('Update status of DC ' + dcNumber + ' to ' + newStatus + '?')) {
        alert('Status update for ' + dcNumber + ' - Feature coming soon!');
    }
}

function downloadPDF(dcNumber) {
    alert('Download PDF for ' + dcNumber + ' - Feature coming soon!');
}
</script>
{% endblock %}
