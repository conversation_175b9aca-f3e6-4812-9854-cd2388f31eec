{% extends 'base.html' %}

{% block title %}Status Tracking - Partial DC Management{% endblock %}

{% block extra_css %}
<style>
    .tracking-header {
        background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
        color: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
    }
    
    .status-summary {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 25px;
    }
    
    .status-item {
        text-align: center;
        padding: 15px;
        border-radius: 10px;
        margin-bottom: 10px;
        transition: all 0.2s ease;
    }
    
    .status-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .status-created {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
    }
    
    .status-dispatched {
        background: linear-gradient(135deg, #ffc107, #fd7e14);
        color: #212529;
    }
    
    .status-delivered {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
    }
    
    .dc-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 15px;
        transition: all 0.2s ease;
    }
    
    .dc-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    
    .dc-status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .timeline-item {
        position: relative;
        padding-left: 30px;
        margin-bottom: 15px;
    }
    
    .timeline-item::before {
        content: '';
        position: absolute;
        left: 8px;
        top: 8px;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #007bff;
    }
    
    .timeline-item::after {
        content: '';
        position: absolute;
        left: 11px;
        top: 16px;
        width: 2px;
        height: calc(100% - 8px);
        background: #dee2e6;
    }
    
    .timeline-item:last-child::after {
        display: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row">
        <div class="col-12">
            <div class="tracking-header">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2><i class="fas fa-truck"></i> Delivery Challan Status Tracking</h2>
                        <p class="mb-0">Monitor and track all delivery challans in real-time</p>
                    </div>
                    <div class="col-md-4 text-md-right">
                        <button class="btn btn-light" onclick="location.reload()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                        <a href="{{ url_for('partial_dc.dc_generation') }}" class="btn btn-outline-light ml-2">
                            <i class="fas fa-plus"></i> Generate New DC
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Summary -->
    <div class="row">
        <div class="col-12">
            <div class="status-summary">
                <h6 class="mb-3"><i class="fas fa-chart-pie"></i> Status Overview</h6>
                <div class="row">
                    {% for status in status_summary %}
                    <div class="col-md-4">
                        <div class="status-item status-{{ status.status }}">
                            <h4 class="mb-1">{{ status.count }}</h4>
                            <div>{{ status.status.title() }}</div>
                        </div>
                    </div>
                    {% endfor %}
                    {% if not status_summary %}
                    <div class="col-12 text-center">
                        <p class="text-muted">No delivery challans found</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Delivery Challans List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-list"></i> All Delivery Challans
                    </h5>
                    <div>
                        <select class="form-select form-select-sm" id="statusFilter" onchange="filterByStatus()">
                            <option value="">All Status</option>
                            <option value="created">Created</option>
                            <option value="dispatched">Dispatched</option>
                            <option value="delivered">Delivered</option>
                        </select>
                    </div>
                </div>
                <div class="card-body">
                    {% if challans %}
                    <div class="table-responsive">
                        <table class="table table-hover" id="challansTable">
                            <thead>
                                <tr>
                                    <th>DC Number</th>
                                    <th>Order ID</th>
                                    <th>Customer</th>
                                    <th>Created Date</th>
                                    <th>Status</th>
                                    <th>Items</th>
                                    <th>Amount</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for dc in challans %}
                                <tr class="dc-row" data-status="{{ dc.status }}">
                                    <td>
                                        <strong class="text-primary">{{ dc.dc_number }}</strong>
                                    </td>
                                    <td>
                                        <a href="{{ url_for('partial_dc.order_details', order_id=dc.order_id) }}" 
                                           class="text-decoration-none">
                                            {{ dc.order_id }}
                                        </a>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ dc.customer_name }}</strong>
                                            {% if dc.sales_agent %}
                                            <br><small class="text-muted">Agent: {{ dc.sales_agent }}</small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        {{ dc.created_date.strftime('%Y-%m-%d %H:%M') if dc.created_date else 'N/A' }}
                                    </td>
                                    <td>
                                        {% if dc.status == 'created' %}
                                        <span class="dc-status-badge" style="background: #007bff; color: white;">
                                            <i class="fas fa-file"></i> Created
                                        </span>
                                        {% elif dc.status == 'dispatched' %}
                                        <span class="dc-status-badge" style="background: #ffc107; color: #212529;">
                                            <i class="fas fa-truck"></i> Dispatched
                                        </span>
                                        {% elif dc.status == 'delivered' %}
                                        <span class="dc-status-badge" style="background: #28a745; color: white;">
                                            <i class="fas fa-check"></i> Delivered
                                        </span>
                                        {% else %}
                                        <span class="dc-status-badge" style="background: #6c757d; color: white;">
                                            {{ dc.status.title() }}
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">
                                        <strong>{{ dc.total_items or 0 }}</strong>
                                    </td>
                                    <td class="text-center">
                                        <strong>₹{{ "{:,.2f}".format(dc.total_amount or 0) }}</strong>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('partial_dc.dc_details', dc_number=dc.dc_number) }}" 
                                               class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if dc.status == 'created' %}
                                            <button class="btn btn-outline-warning btn-sm" 
                                                    onclick="updateStatus('{{ dc.dc_number }}', 'dispatched')">
                                                <i class="fas fa-truck"></i>
                                            </button>
                                            {% elif dc.status == 'dispatched' %}
                                            <button class="btn btn-outline-success btn-sm" 
                                                    onclick="updateStatus('{{ dc.dc_number }}', 'delivered')">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            {% endif %}
                                            <button class="btn btn-outline-info btn-sm" 
                                                    onclick="downloadPDF('{{ dc.dc_number }}')">
                                                <i class="fas fa-download"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">No Delivery Challans</h4>
                        <p class="text-muted">No delivery challans have been generated yet.</p>
                        <a href="{{ url_for('partial_dc.dc_generation') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Generate First DC
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity Timeline -->
    {% if challans %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-history"></i> Recent Activity</h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        {% for dc in challans[:5] %}
                        <div class="timeline-item">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <strong>{{ dc.dc_number }}</strong> - {{ dc.status.title() }}
                                    <br><small class="text-muted">{{ dc.customer_name }}</small>
                                </div>
                                <small class="text-muted">
                                    {{ dc.created_date.strftime('%m/%d %H:%M') if dc.created_date else 'N/A' }}
                                </small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    function filterByStatus() {
        const filter = document.getElementById('statusFilter').value;
        const rows = document.querySelectorAll('.dc-row');
        
        rows.forEach(row => {
            if (filter === '' || row.dataset.status === filter) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }
    
    function updateStatus(dcNumber, newStatus) {
        const statusText = newStatus === 'dispatched' ? 'dispatch' : 'mark as delivered';
        
        if (confirm(`Are you sure you want to ${statusText} DC ${dcNumber}?`)) {
            // Implementation for status update
            fetch(`/partial-dc/api/update-status/${dcNumber}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ status: newStatus })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Error updating status: ' + data.error);
                }
            })
            .catch(error => {
                alert('Error updating status: ' + error);
            });
        }
    }
    
    function downloadPDF(dcNumber) {
        // Implementation for PDF download
        window.open(`/partial-dc/api/download-pdf/${dcNumber}`, '_blank');
    }
    
    // Auto-refresh every 3 minutes
    setTimeout(function() {
        location.reload();
    }, 180000);
    
    // Add search functionality
    function searchChallans() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const rows = document.querySelectorAll('.dc-row');
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }
</script>
{% endblock %}
