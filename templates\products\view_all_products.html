{% extends "base.html" %}

{% block title %}View All Products - Medivent ERP{% endblock %}

{% block extra_css %}
<style>
    .main-content {
        margin: 20px;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 10px;
        min-height: calc(100vh - 140px);
    }

    .filter-card {
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        border: 1px solid #e9ecef;
    }

    .search-box {
        border-radius: 8px;
        border: 1px solid #ced4da;
        padding: 10px 15px;
        transition: all 0.3s ease;
    }

    .search-box:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        outline: none;
    }

    .table-container {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        overflow: hidden;
        border: 1px solid #e9ecef;
    }

    .table thead th {
        background: #007bff;
        color: white;
        border: none;
        padding: 12px 8px;
        font-weight: 600;
        font-size: 0.9rem;
        text-align: center;
        vertical-align: middle;
    }

    .table tbody tr {
        transition: background-color 0.2s ease;
    }

    .table tbody tr:hover {
        background-color: #f8f9ff;
    }

    .table td {
        padding: 10px 8px;
        vertical-align: middle;
        border-color: #e9ecef;
        font-size: 0.85rem;
        text-align: center;
    }

    .product-image {
        width: 50px;
        height: 50px;
        object-fit: cover;
        border-radius: 8px;
        border: 2px solid #e9ecef;
    }

    .badge-custom {
        padding: 6px 10px;
        border-radius: 15px;
        font-size: 0.7rem;
        font-weight: 600;
    }

    .pricing-info {
        font-size: 0.8rem;
        line-height: 1.3;
    }

    .stock-info {
        font-size: 0.8rem;
        line-height: 1.3;
    }

    .btn-primary {
        background: #007bff;
        border: 1px solid #007bff;
        border-radius: 6px;
        padding: 8px 16px;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .btn-primary:hover {
        background: #0056b3;
        border-color: #0056b3;
        transform: translateY(-1px);
    }

    .action-btn {
        width: 32px;
        height: 32px;
        border-radius: 6px;
        border: 1px solid;
        margin: 0 2px;
        padding: 0;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
    }

    .action-btn:hover {
        transform: scale(1.05);
    }

    .stats-card {
        border-radius: 10px;
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.2s ease;
    }

    .stats-card:hover {
        transform: translateY(-2px);
    }

    .product-name {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 2px;
    }

    .product-id {
        color: #6c757d;
        font-size: 0.75rem;
    }

    .responsive-table {
        overflow-x: auto;
    }

    @media (max-width: 768px) {
        .main-content {
            margin: 10px;
            padding: 15px;
        }

        .table td, .table th {
            padding: 8px 4px;
            font-size: 0.8rem;
        }

        .product-image {
            width: 40px;
            height: 40px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="main-content">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-1"><i class="fas fa-list-alt text-primary"></i> View All Products</h2>
            <p class="text-muted">Browse and view all products in your inventory catalog</p>
        </div>
        <div>
            <button class="btn btn-outline-success me-2" onclick="exportToExcel()">
                <i class="fas fa-file-excel"></i> Export to Excel
            </button>
            <button class="btn btn-outline-primary me-2" onclick="window.print()">
                <i class="fas fa-print"></i> Print Catalog
            </button>
            <a href="{{ url_for('products.product_management') }}" class="btn btn-primary">
                <i class="fas fa-cog"></i> Manage Products
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card bg-primary text-white stats-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-1">{{ stats.total or 0 }}</h3>
                            <p class="mb-0">Total Products</p>
                        </div>
                        <div>
                            <i class="fas fa-boxes fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-success text-white stats-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-1">{{ stats.available or 0 }}</h3>
                            <p class="mb-0">Active Products</p>
                        </div>
                        <div>
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-warning text-white stats-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-1">{{ stats.low_stock or 0 }}</h3>
                            <p class="mb-0">Low Stock</p>
                        </div>
                        <div>
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-danger text-white stats-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-1">{{ stats.out_of_stock or 0 }}</h3>
                            <p class="mb-0">Inactive Products</p>
                        </div>
                        <div>
                            <i class="fas fa-times-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search & Filter Bar -->
    <div class="filter-card">
        <form method="GET" action="{{ url_for('products.view_all_products') }}" id="filterForm">
            <div class="row align-items-end">
                <div class="col-md-4 mb-3">
                    <label class="form-label"><strong>Search Product Catalog</strong></label>
                    <div class="input-group">
                        <input type="text" name="q" class="form-control search-box"
                               placeholder="Search by product name, manufacturer..."
                               value="{{ filters.search or '' }}">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <label class="form-label"><strong>Filter by Category</strong></label>
                    <select name="generic" class="form-control" onchange="document.getElementById('filterForm').submit()">
                        <option value="">All Categories</option>
                        {% if generic_categories %}
                            {% for category in generic_categories %}
                            <option value="{{ category.category }}"
                                    {% if filters.generic == category.category %}selected{% endif %}>
                                {{ category.category }}
                            </option>
                            {% endfor %}
                        {% endif %}
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label class="form-label"><strong>Sort by</strong></label>
                    <select name="sort" class="form-control" onchange="document.getElementById('filterForm').submit()">
                        <option value="name_asc" {% if filters.sort == 'name_asc' %}selected{% endif %}>Product Name A-Z</option>
                        <option value="name_desc" {% if filters.sort == 'name_desc' %}selected{% endif %}>Product Name Z-A</option>
                        <option value="price_low" {% if filters.sort == 'price_low' %}selected{% endif %}>Price Low to High</option>
                        <option value="price_high" {% if filters.sort == 'price_high' %}selected{% endif %}>Price High to Low</option>
                        <option value="manufacturer" {% if filters.sort == 'manufacturer' %}selected{% endif %}>Manufacturer</option>
                    </select>
                </div>
                <div class="col-md-2 mb-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('products.view_all_products') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> Clear Filters
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- Product Catalog -->
    <div class="table-container">
        <div class="d-flex justify-content-between align-items-center p-3 border-bottom">
            <h5 class="mb-0"><i class="fas fa-list text-primary"></i> Product Catalog</h5>
            <small class="text-muted">{{ products|length }} products found</small>
        </div>

        <div class="responsive-table">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th width="5%">#</th>
                        <th width="8%">Image</th>
                        <th width="25%">Product Information</th>
                        <th width="12%">Strength</th>
                        <th width="15%">Category</th>
                        <th width="15%">Manufacturer</th>
                        <th width="10%">Price</th>
                        <th width="8%">Status</th>
                        <th width="7%">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% if products %}
                        {% for product in products %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>
                                {% if product.image_url %}
                                    <img src="{{ product.image_url }}" alt="{{ product.name }}" class="product-image">
                                {% else %}
                                    <img src="https://via.placeholder.com/50x50/007bff/ffffff?text={{ product.name[:2]|upper }}"
                                         alt="{{ product.name }}" class="product-image">
                                {% endif %}
                            </td>
                            <td class="text-start">
                                <div class="product-name">{{ product.name or 'N/A' }}</div>
                                <div class="product-id">ID: {{ product.product_id or 'N/A' }}</div>
                                {% if product.description %}
                                <small class="text-muted">{{ product.description[:50] }}{% if product.description|length > 50 %}...{% endif %}</small>
                                {% endif %}
                            </td>
                            <td>
                                {% if product.strength %}
                                    <span class="badge badge-custom bg-primary">{{ product.strength }}</span>
                                {% else %}
                                    <span class="badge badge-custom bg-secondary">N/A</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge badge-custom bg-info">{{ product.category or 'General' }}</span>
                            </td>
                            <td class="text-start">
                                <strong>{{ product.manufacturer or 'Unknown' }}</strong>
                            </td>
                            <td>
                                <div class="pricing-info">
                                    <strong>₹{{ "%.2f"|format(product.unit_price or 0) }}</strong><br>
                                    <small class="text-muted">{{ product.unit_of_measure or 'Unit' }}</small>
                                </div>
                            </td>
                            <td>
                                {% if product.is_active == 1 %}
                                    <span class="badge badge-custom bg-success">Active</span>
                                {% else %}
                                    <span class="badge badge-custom bg-danger">Inactive</span>
                                {% endif %}
                            </td>
                            <td>
                                <button class="action-btn btn btn-outline-primary btn-sm"
                                        onclick="viewProduct('{{ product.product_id or product.id }}')"
                                        title="View Details">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <a href="{{ url_for('products.update_product', product_id=product.product_id or product.id) }}"
                                   class="action-btn btn btn-outline-warning btn-sm"
                                   title="Edit Product">
                                    <i class="fas fa-edit"></i>
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="9" class="text-center py-4">
                                <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No products found</h5>
                                <p class="text-muted">Try adjusting your search criteria or add some products.</p>
                                <a href="{{ url_for('products.new_product') }}" class="btn btn-primary mt-2">
                                    <i class="fas fa-plus"></i> Add First Product
                                </a>
                            </td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination & Summary -->
    <div class="d-flex justify-content-between align-items-center mt-4">
        <div>
            <p class="mb-0 text-muted">
                <i class="fas fa-info-circle"></i>
                <strong>Catalog Summary:</strong> Displaying {{ products|length }} of {{ stats.total }} total products •
                {{ stats.available }} Available • {{ stats.low_stock }} Low Stock • {{ stats.out_of_stock }} Out of Stock
            </p>
        </div>
        <nav aria-label="Product pagination">
            <ul class="pagination mb-0">
                <li class="page-item">
                    <a class="page-link" href="#" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                <li class="page-item"><a class="page-link" href="#">2</a></li>
                <li class="page-item"><a class="page-link" href="#">3</a></li>
                <li class="page-item"><a class="page-link" href="#">...</a></li>
                <li class="page-item"><a class="page-link" href="#">{{ (stats.total / 50)|round|int }}</a></li>
                <li class="page-item">
                    <a class="page-link" href="#" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
            </ul>
        </nav>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // View toggle functionality
    document.addEventListener('DOMContentLoaded', function() {
        const listViewBtn = document.getElementById('listView');
        const gridViewBtn = document.getElementById('gridView');

        listViewBtn.addEventListener('click', function() {
            listViewBtn.classList.add('active');
            gridViewBtn.classList.remove('active');
            // Add list view logic here
        });

        gridViewBtn.addEventListener('click', function() {
            gridViewBtn.classList.add('active');
            listViewBtn.classList.remove('active');
            // Add grid view logic here
        });
    });

    // Export to Excel functionality
    function exportToExcel() {
        // Create a form to submit export request
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ url_for("products.view_all_products") }}';

        const exportField = document.createElement('input');
        exportField.type = 'hidden';
        exportField.name = 'export';
        exportField.value = 'excel';
        form.appendChild(exportField);

        // Add current filters to export
        const currentUrl = new URL(window.location);
        currentUrl.searchParams.forEach((value, key) => {
            const field = document.createElement('input');
            field.type = 'hidden';
            field.name = key;
            field.value = value;
            form.appendChild(field);
        });

        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
    }

    // Advanced search toggle
    function toggleAdvancedSearch() {
        // Toggle advanced search panel
        const filterCard = document.querySelector('.filter-card');
        if (filterCard.style.display === 'none') {
            filterCard.style.display = 'block';
        } else {
            filterCard.style.display = 'none';
        }
    }

    // View product details
    function viewProduct(productId) {
        // Navigate to product details page
        window.location.href = `/products/${productId}`;
    }

    // Auto-submit search on Enter key
    document.querySelector('input[name="q"]').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            document.getElementById('filterForm').submit();
        }
    });

    // Add loading state to buttons
    document.getElementById('filterForm').addEventListener('submit', function() {
        const submitBtn = this.querySelector('button[type="submit"]');
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Filtering...';
        submitBtn.disabled = true;
    });
</script>
{% endblock %}
