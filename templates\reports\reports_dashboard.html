{% extends "base.html" %}

{% block title %}Reports Dashboard{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-chart-pie"></i> Reports Dashboard
                    </h4>
                    <small>Comprehensive business intelligence and analytics</small>
                </div>
            </div>

            <!-- Report Categories -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-chart-line fa-3x mb-3"></i>
                            <h5>Sales Reports</h5>
                            <p class="mb-0">{{ report_stats.sales_reports_count }} reports available</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-boxes fa-3x mb-3"></i>
                            <h5>Inventory Reports</h5>
                            <p class="mb-0">{{ report_stats.inventory_reports_count }} reports available</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-money-bill-wave fa-3x mb-3"></i>
                            <h5>Financial Reports</h5>
                            <p class="mb-0">{{ report_stats.financial_reports_count }} reports available</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-users fa-3x mb-3"></i>
                            <h5>Customer Reports</h5>
                            <p class="mb-0">{{ report_stats.customer_reports_count }} reports available</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Report Generation -->
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-bolt"></i> Quick Report Generation</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2">
                            <a href="{{ url_for('daily_sales_report_new') }}" class="btn btn-outline-primary btn-block">
                                <i class="fas fa-calendar-day"></i><br>
                                Daily Sales
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{{ url_for('weekly_sales_report_new') }}" class="btn btn-outline-success btn-block">
                                <i class="fas fa-calendar-week"></i><br>
                                Weekly Sales
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{{ url_for('monthly_sales_report_new') }}" class="btn btn-outline-info btn-block">
                                <i class="fas fa-calendar-alt"></i><br>
                                Monthly Sales
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{{ url_for('inventory_report') }}" class="btn btn-outline-warning btn-block">
                                <i class="fas fa-boxes"></i><br>
                                Inventory Status
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{{ url_for('product_performance_report') }}" class="btn btn-outline-secondary btn-block">
                                <i class="fas fa-chart-line"></i><br>
                                Product Performance
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{{ url_for('sales_by_agent_report') }}" class="btn btn-outline-dark btn-block">
                                <i class="fas fa-user-tie"></i><br>
                                Agent Performance
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sales Reports Section -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0"><i class="fas fa-chart-line"></i> Sales Reports</h5>
                        </div>
                        <div class="card-body">
                            <div class="list-group">
                                <a href="{{ url_for('daily_sales_report_new') }}" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1"><i class="fas fa-calendar-day"></i> Daily Sales Report</h6>
                                        <small>Updated today</small>
                                    </div>
                                    <p class="mb-1">Detailed daily sales analysis with trends and comparisons</p>
                                </a>
                                
                                <a href="{{ url_for('weekly_sales_report_new') }}" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1"><i class="fas fa-calendar-week"></i> Weekly Sales Report</h6>
                                        <small>Updated weekly</small>
                                    </div>
                                    <p class="mb-1">Weekly performance metrics and growth analysis</p>
                                </a>
                                
                                <a href="{{ url_for('monthly_sales_report_new') }}" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1"><i class="fas fa-calendar-alt"></i> Monthly Sales Report</h6>
                                        <small>Updated monthly</small>
                                    </div>
                                    <p class="mb-1">Comprehensive monthly business overview</p>
                                </a>
                                
                                <a href="{{ url_for('custom_date_range_report') }}" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1"><i class="fas fa-calendar"></i> Custom Date Range</h6>
                                        <small>On demand</small>
                                    </div>
                                    <p class="mb-1">Flexible date range analysis and reporting</p>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0"><i class="fas fa-boxes"></i> Inventory Reports</h5>
                        </div>
                        <div class="card-body">
                            <div class="list-group">
                                <a href="{{ url_for('inventory_report') }}" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1"><i class="fas fa-warehouse"></i> Inventory Status</h6>
                                        <small>Real-time</small>
                                    </div>
                                    <p class="mb-1">Current stock levels and availability</p>
                                </a>
                                
                                <a href="{{ url_for('inventory.index') }}?view=movements" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1"><i class="fas fa-exchange-alt"></i> Stock Movements</h6>
                                        <small>Updated hourly</small>
                                    </div>
                                    <p class="mb-1">Track inventory movements and transfers</p>
                                </a>
                                
                                <a href="{{ url_for('reports') }}?type=expiry" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1"><i class="fas fa-exclamation-triangle"></i> Expiring Products</h6>
                                        <small>Updated daily</small>
                                    </div>
                                    <p class="mb-1">Products approaching expiry dates</p>
                                </a>
                                
                                <a href="{{ url_for('inventory.index') }}?view=low_stock" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1"><i class="fas fa-arrow-down"></i> Low Stock Alert</h6>
                                        <small>Real-time</small>
                                    </div>
                                    <p class="mb-1">Products below reorder levels</p>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Financial & Customer Reports -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-warning text-white">
                            <h5 class="mb-0"><i class="fas fa-money-bill-wave"></i> Financial Reports</h5>
                        </div>
                        <div class="card-body">
                            <div class="list-group">
                                <a href="{{ url_for('financial_reports') }}" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1"><i class="fas fa-chart-pie"></i> Financial Dashboard</h6>
                                        <small>Real-time</small>
                                    </div>
                                    <p class="mb-1">Comprehensive financial overview</p>
                                </a>
                                
                                <a href="{{ url_for('finance_pending_invoices') }}" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1"><i class="fas fa-file-invoice-dollar"></i> Pending Invoices</h6>
                                        <small>Updated hourly</small>
                                    </div>
                                    <p class="mb-1">Outstanding invoice management</p>
                                </a>
                                
                                <a href="{{ url_for('finance_customer_ledger') }}" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1"><i class="fas fa-book"></i> Customer Ledger</h6>
                                        <small>Real-time</small>
                                    </div>
                                    <p class="mb-1">Customer account statements</p>
                                </a>
                                
                                <a href="{{ url_for('finance_payment_collection') }}" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1"><i class="fas fa-hand-holding-usd"></i> Payment Collection</h6>
                                        <small>Updated daily</small>
                                    </div>
                                    <p class="mb-1">Payment tracking and collection</p>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-danger text-white">
                            <h5 class="mb-0"><i class="fas fa-users"></i> Customer Reports</h5>
                        </div>
                        <div class="card-body">
                            <div class="list-group">
                                <a href="{{ url_for('sales_by_agent_report') }}" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1"><i class="fas fa-user-tie"></i> Sales by Agent</h6>
                                        <small>Updated daily</small>
                                    </div>
                                    <p class="mb-1">Agent performance and sales metrics</p>
                                </a>
                                
                                <a href="{{ url_for('customers', view='by_type') }}" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1"><i class="fas fa-user-tag"></i> Customer Analysis</h6>
                                        <small>Updated weekly</small>
                                    </div>
                                    <p class="mb-1">Customer segmentation and analysis</p>
                                </a>
                                
                                <a href="{{ url_for('customers', view='orders') }}" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1"><i class="fas fa-history"></i> Order History</h6>
                                        <small>Real-time</small>
                                    </div>
                                    <p class="mb-1">Customer order patterns and history</p>
                                </a>
                                
                                <a href="{{ url_for('product_performance_report') }}" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1"><i class="fas fa-chart-line"></i> Product Performance</h6>
                                        <small>Updated daily</small>
                                    </div>
                                    <p class="mb-1">Product sales and performance metrics</p>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Custom Report Builder -->
            <div class="card mb-4">
                <div class="card-header bg-dark text-white">
                    <h5 class="mb-0"><i class="fas fa-tools"></i> Custom Report Builder</h5>
                </div>
                <div class="card-body">
                    <form id="customReportForm">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="reportType">Report Type</label>
                                    <select id="reportType" class="form-control">
                                        <option value="">Select Type</option>
                                        <option value="sales">Sales Analysis</option>
                                        <option value="inventory">Inventory Analysis</option>
                                        <option value="financial">Financial Analysis</option>
                                        <option value="customer">Customer Analysis</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="dateRange">Date Range</label>
                                    <select id="dateRange" class="form-control">
                                        <option value="today">Today</option>
                                        <option value="yesterday">Yesterday</option>
                                        <option value="this_week">This Week</option>
                                        <option value="last_week">Last Week</option>
                                        <option value="this_month">This Month</option>
                                        <option value="last_month">Last Month</option>
                                        <option value="custom">Custom Range</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="startDate">Start Date</label>
                                    <input type="date" id="startDate" class="form-control" style="display: none;">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="endDate">End Date</label>
                                    <input type="date" id="endDate" class="form-control" style="display: none;">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="button" class="btn btn-primary btn-block" onclick="generateCustomReport()">
                                        <i class="fas fa-chart-bar"></i> Generate
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Recent Reports -->
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0"><i class="fas fa-history"></i> Recent Reports</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Report Name</th>
                                    <th>Type</th>
                                    <th>Generated</th>
                                    <th>Generated By</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for report in recent_reports %}
                                <tr>
                                    <td><strong>{{ report.report_name }}</strong></td>
                                    <td>
                                        <span class="badge badge-{% if report.report_type == 'sales' %}success{% elif report.report_type == 'inventory' %}info{% elif report.report_type == 'financial' %}warning{% else %}primary{% endif %}">
                                            {{ report.report_type|title }}
                                        </span>
                                    </td>
                                    <td>{{ report.generated_date | format_datetime('%Y-%m-%d %H:%M') }}</td>
                                    <td>{{ report.generated_by }}</td>
                                    <td>
                                        <span class="badge badge-{% if report.status == 'completed' %}success{% elif report.status == 'processing' %}warning{% else %}danger{% endif %}">
                                            {{ report.status|title }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-sm btn-primary" onclick="viewReport('{{ report.report_id }}')" title="View">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-success" onclick="downloadReport('{{ report.report_id }}')" title="Download">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-info" onclick="shareReport('{{ report.report_id }}')" title="Share">
                                                <i class="fas fa-share"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Custom report builder
document.getElementById('dateRange').addEventListener('change', function() {
    const customDates = document.getElementById('startDate').parentElement.parentElement;
    const endDateContainer = document.getElementById('endDate').parentElement;
    
    if (this.value === 'custom') {
        document.getElementById('startDate').style.display = 'block';
        document.getElementById('endDate').style.display = 'block';
    } else {
        document.getElementById('startDate').style.display = 'none';
        document.getElementById('endDate').style.display = 'none';
    }
});

function generateCustomReport() {
    const reportType = document.getElementById('reportType').value;
    const dateRange = document.getElementById('dateRange').value;
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    
    if (!reportType) {
        alert('Please select a report type');
        return;
    }
    
    let url = `/reports/custom?type=${reportType}&range=${dateRange}`;
    
    if (dateRange === 'custom' && startDate && endDate) {
        url += `&start_date=${startDate}&end_date=${endDate}`;
    }
    
    window.location.href = url;
}

// Report actions
function viewReport(reportId) {
    window.location.href = `/reports/${reportId}/view`;
}

function downloadReport(reportId) {
    window.location.href = `/reports/${reportId}/download`;
}

function shareReport(reportId) {
    const shareUrl = `${window.location.origin}/reports/${reportId}/share`;
    navigator.clipboard.writeText(shareUrl).then(() => {
        alert('Report share link copied to clipboard!');
    });
}

// Auto-refresh recent reports every 30 seconds
setInterval(function() {
    // Only refresh the recent reports section
    fetch('/reports/recent-reports-data')
        .then(response => response.json())
        .then(data => {
            // Update recent reports table
            console.log('Recent reports updated');
        });
}, 30000);
</script>
{% endblock %}
