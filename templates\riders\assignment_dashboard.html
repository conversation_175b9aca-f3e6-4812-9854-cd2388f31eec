{% extends 'base.html' %}

{% block title %}Rider Assignment Dashboard{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-motorcycle"></i> Rider Assignment Dashboard
        </h1>
        <div class="btn-group">
            <a href="{{ url_for('riders.dashboard') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Back to Rider Dashboard
            </a>
            <button onclick="refreshDashboard()" class="btn btn-primary btn-sm">
                <i class="fas fa-sync-alt"></i> Refresh
            </button>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Orders Ready for Assignment
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ ready_orders|length }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-box-open fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Available Riders
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ available_riders|length }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total Value
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                Rs.{{ "{:,.0f}".format(ready_orders|sum(attribute='order_amount') or 0) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Orders Ready for Assignment -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 bg-warning text-white">
            <h6 class="m-0 font-weight-bold">
                <i class="fas fa-motorcycle"></i> Orders Ready for Rider Assignment ({{ ready_orders|length }})
            </h6>
        </div>
        <div class="card-body">
            {% if ready_orders %}
            <div class="table-responsive">
                <table class="table table-bordered" id="readyOrdersTable">
                    <thead>
                        <tr>
                            <th>Order ID</th>
                            <th>Customer</th>
                            <th>Address</th>
                            <th>Amount</th>
                            <th>Packed At</th>
                            <th>Priority</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for order in ready_orders %}
                        <tr>
                            <td>
                                <strong class="text-primary">{{ order.order_id }}</strong>
                            </td>
                            <td>
                                <strong>{{ order.customer_name }}</strong><br>
                                <small class="text-muted">{{ order.customer_phone }}</small>
                            </td>
                            <td>
                                <small>{{ order.customer_address }}<br>
                                {{ order.customer_city }}</small>
                            </td>
                            <td>
                                <strong>Rs.{{ "{:,.0f}".format(order.order_amount or 0) }}</strong>
                            </td>
                            <td>
                                <small>{{ order.packed_at | format_datetime('%Y-%m-%d %H:%M') if order.packed_at else 'N/A' }}</small>
                            </td>
                            <td>
                                {% if order.priority_level and order.priority_level > 1 %}
                                <span class="badge badge-danger">High</span>
                                {% else %}
                                <span class="badge badge-secondary">Normal</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-primary btn-sm" onclick="assignOrder('{{ order.order_id }}')">
                                        <i class="fas fa-user-plus"></i> Assign
                                    </button>
                                    <button class="btn btn-success btn-sm" onclick="autoAssign('{{ order.order_id }}')">
                                        <i class="fas fa-magic"></i> Auto
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                <h4>No Orders Pending Assignment</h4>
                <p class="text-muted">All packed orders have been assigned to riders!</p>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Available Riders -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 bg-success text-white">
            <h6 class="m-0 font-weight-bold">
                <i class="fas fa-users"></i> Available Riders ({{ available_riders|length }})
            </h6>
        </div>
        <div class="card-body">
            {% if available_riders %}
            <div class="row">
                {% for rider in available_riders %}
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="card border-left-success">
                        <div class="card-body">
                            <h6 class="card-title">{{ rider.name }}</h6>
                            <p class="card-text">
                                <small class="text-muted">
                                    <i class="fas fa-phone"></i> {{ rider.phone }}<br>
                                    <i class="fas fa-motorcycle"></i> {{ rider.vehicle_type }}<br>
                                    <i class="fas fa-map-marker-alt"></i> {{ rider.current_location or 'Unknown' }}<br>
                                    <i class="fas fa-star"></i> {{ rider.rating or 'N/A' }}/5.0
                                </small>
                            </p>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-user-slash fa-4x text-muted mb-3"></i>
                <h4>No Available Riders</h4>
                <p class="text-muted">All riders are currently busy or unavailable.</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function refreshDashboard() {
    location.reload();
}

function assignOrder(orderId) {
    window.location.href = `/riders/manual-assign/${orderId}`;
}

function autoAssign(orderId) {
    if (confirm('Auto-assign this order to the best available rider?')) {
        window.location.href = `/riders/auto-assign/${orderId}`;
    }
}
</script>
{% endblock %}

<!-- Include Live Tracking Sidebar -->
{% include 'components/live_tracking_sidebar.html' %}
