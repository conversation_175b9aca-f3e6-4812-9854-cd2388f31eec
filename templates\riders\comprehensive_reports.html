{% extends 'base.html' %}

{% block title %}Comprehensive Rider Reports{% endblock %}

{% block extra_css %}
<style>
.report-nav {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 2rem;
}

.report-nav .nav-link {
    color: white;
    border-radius: 8px;
    margin: 0 0.25rem;
    transition: all 0.3s ease;
}

.report-nav .nav-link:hover,
.report-nav .nav-link.active {
    background: rgba(255,255,255,0.2);
    color: white;
}

.report-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.2s ease;
}

.report-card:hover {
    transform: translateY(-2px);
}

.metric-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 1.5rem;
    text-align: center;
}

.metric-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.metric-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.chart-container {
    height: 400px;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-chart-bar"></i> Comprehensive Rider Reports
        </h1>
        <div class="btn-group">
            <a href="{{ url_for('riders.dashboard') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
            <a href="{{ url_for('riders.export_report', report_type=report_type) }}?date_from={{ date_from }}&date_to={{ date_to }}&rider={{ rider_filter }}" 
               class="btn btn-success btn-sm">
                <i class="fas fa-download"></i> Export Excel
            </a>
        </div>
    </div>

    <!-- Report Navigation -->
    <div class="report-nav">
        <ul class="nav nav-pills justify-content-center">
            <li class="nav-item">
                <a class="nav-link {{ 'active' if report_type == 'overview' else '' }}" 
                   href="?type=overview&date_from={{ date_from }}&date_to={{ date_to }}&rider={{ rider_filter }}">
                    <i class="fas fa-tachometer-alt"></i> Overview
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {{ 'active' if report_type == 'performance' else '' }}" 
                   href="?type=performance&date_from={{ date_from }}&date_to={{ date_to }}&rider={{ rider_filter }}">
                    <i class="fas fa-trophy"></i> Performance
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {{ 'active' if report_type == 'delivery' else '' }}" 
                   href="?type=delivery&date_from={{ date_from }}&date_to={{ date_to }}&rider={{ rider_filter }}">
                    <i class="fas fa-truck"></i> Delivery
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {{ 'active' if report_type == 'financial' else '' }}" 
                   href="?type=financial&date_from={{ date_from }}&date_to={{ date_to }}&rider={{ rider_filter }}">
                    <i class="fas fa-dollar-sign"></i> Financial
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {{ 'active' if report_type == 'customer' else '' }}" 
                   href="?type=customer&date_from={{ date_from }}&date_to={{ date_to }}&rider={{ rider_filter }}">
                    <i class="fas fa-users"></i> Customer
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {{ 'active' if report_type == 'geographic' else '' }}" 
                   href="?type=geographic&date_from={{ date_from }}&date_to={{ date_to }}&rider={{ rider_filter }}">
                    <i class="fas fa-map"></i> Geographic
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {{ 'active' if report_type == 'time_analysis' else '' }}" 
                   href="?type=time_analysis&date_from={{ date_from }}&date_to={{ date_to }}&rider={{ rider_filter }}">
                    <i class="fas fa-clock"></i> Time Analysis
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {{ 'active' if report_type == 'efficiency' else '' }}" 
                   href="?type=efficiency&date_from={{ date_from }}&date_to={{ date_to }}&rider={{ rider_filter }}">
                    <i class="fas fa-gauge-high"></i> Efficiency
                </a>
            </li>
        </ul>
    </div>

    <!-- Filters -->
    <div class="card report-card mb-4">
        <div class="card-header bg-primary text-white">
            <h6 class="m-0 font-weight-bold">
                <i class="fas fa-filter"></i> Report Filters
            </h6>
        </div>
        <div class="card-body">
            <form method="GET" class="row">
                <input type="hidden" name="type" value="{{ report_type }}">
                
                <div class="col-md-3">
                    <label for="date_from">From Date:</label>
                    <input type="date" name="date_from" id="date_from" class="form-control" value="{{ date_from }}">
                </div>
                
                <div class="col-md-3">
                    <label for="date_to">To Date:</label>
                    <input type="date" name="date_to" id="date_to" class="form-control" value="{{ date_to }}">
                </div>
                
                <div class="col-md-3">
                    <label for="rider">Rider:</label>
                    <select name="rider" id="rider" class="form-control">
                        <option value="">All Riders</option>
                        {% for rider in all_riders %}
                        <option value="{{ rider.rider_id }}" {{ 'selected' if rider.rider_id == rider_filter else '' }}>
                            {{ rider.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary btn-block">
                        <i class="fas fa-search"></i> Apply Filters
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Report Content -->
    {% if report_type == 'overview' %}
        {% include 'riders/reports/overview_report.html' %}
    {% elif report_type == 'performance' %}
        {% include 'riders/reports/performance_report.html' %}
    {% elif report_type == 'delivery' %}
        {% include 'riders/reports/delivery_report.html' %}
    {% elif report_type == 'financial' %}
        {% include 'riders/reports/financial_report.html' %}
    {% elif report_type == 'customer' %}
        {% include 'riders/reports/customer_report.html' %}
    {% elif report_type == 'geographic' %}
        {% include 'riders/reports/geographic_report.html' %}
    {% elif report_type == 'time_analysis' %}
        {% include 'riders/reports/time_analysis_report.html' %}
    {% elif report_type == 'efficiency' %}
        {% include 'riders/reports/efficiency_report.html' %}
    {% endif %}

    <!-- Report Instructions -->
    <div class="card report-card">
        <div class="card-header bg-info text-white">
            <h6 class="m-0 font-weight-bold">
                <i class="fas fa-info-circle"></i> Report Instructions
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6><i class="fas fa-1"></i> Report Types:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success"></i> <strong>Overview:</strong> General performance summary</li>
                        <li><i class="fas fa-check text-success"></i> <strong>Performance:</strong> Rider success rates and ratings</li>
                        <li><i class="fas fa-check text-success"></i> <strong>Delivery:</strong> Delivery trends and status analysis</li>
                        <li><i class="fas fa-check text-success"></i> <strong>Financial:</strong> Revenue and commission analysis</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6><i class="fas fa-2"></i> Advanced Reports:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success"></i> <strong>Customer:</strong> Customer satisfaction metrics</li>
                        <li><i class="fas fa-check text-success"></i> <strong>Geographic:</strong> Area-wise delivery analysis</li>
                        <li><i class="fas fa-check text-success"></i> <strong>Time Analysis:</strong> Peak hours and efficiency</li>
                        <li><i class="fas fa-check text-success"></i> <strong>Efficiency:</strong> Route optimization insights</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-submit form when filters change
document.getElementById('date_from').addEventListener('change', function() {
    this.form.submit();
});

document.getElementById('date_to').addEventListener('change', function() {
    this.form.submit();
});

document.getElementById('rider').addEventListener('change', function() {
    this.form.submit();
});
</script>
{% endblock %}
