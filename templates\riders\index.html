{% extends 'base.html' %}

{% block title %}Rider Management - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<style>
/* Modern 2025 Rider Management UI */
.rider-management {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px 0;
}

.rider-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 25px;
    padding: 40px;
    margin-bottom: 30px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.rider-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #4e73df, #224abe, #36b9cc);
}

.rider-title {
    background: linear-gradient(45deg, #4e73df, #224abe);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    font-size: 2.5rem;
    margin: 0;
}

.modern-rider-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.modern-rider-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
}

.modern-btn-rider {
    background: linear-gradient(135deg, #4e73df, #224abe);
    border: none;
    border-radius: 15px;
    padding: 12px 25px;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 10px 20px rgba(78, 115, 223, 0.3);
    margin: 0 5px;
}

.modern-btn-rider:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(78, 115, 223, 0.4);
    color: white;
}

.modern-btn-success {
    background: linear-gradient(135deg, #1cc88a, #13855c);
    box-shadow: 0 10px 20px rgba(28, 200, 138, 0.3);
}

.modern-btn-success:hover {
    box-shadow: 0 15px 30px rgba(28, 200, 138, 0.4);
}

.modern-btn-warning {
    background: linear-gradient(135deg, #f6c23e, #dda20a);
    box-shadow: 0 10px 20px rgba(246, 194, 62, 0.3);
}

.modern-btn-warning:hover {
    box-shadow: 0 15px 30px rgba(246, 194, 62, 0.4);
}

.modern-btn-danger {
    background: linear-gradient(135deg, #e74a3b, #c0392b);
    box-shadow: 0 10px 20px rgba(231, 74, 59, 0.3);
}

.modern-btn-danger:hover {
    box-shadow: 0 15px 30px rgba(231, 74, 59, 0.4);
}

.stat-card-modern {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card-modern:hover {
    transform: translateY(-10px);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
}

.stat-card-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #4e73df, #224abe);
}

.stat-number-modern {
    font-size: 3rem;
    font-weight: 700;
    background: linear-gradient(45deg, #4e73df, #224abe);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 10px;
}

.stat-label-modern {
    font-size: 1rem;
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 0;
}

@media (max-width: 768px) {
    .rider-title {
        font-size: 2rem;
    }
    .stat-number-modern {
        font-size: 2.5rem;
    }
}
</style>

<div class="rider-management">
    <div class="container-fluid">
        <!-- Modern Header -->
        <div class="rider-header">
            <div class="d-flex align-items-center justify-content-between">
                <div>
                    <h1 class="rider-title">
                        <i class="fas fa-motorcycle mr-3"></i>Rider Management
                    </h1>
                    <p class="text-muted mb-0 mt-2">Professional rider registration and management system</p>
                </div>
                <div>
                    <button class="modern-btn-rider" onclick="exportRiders()">
                        <i class="fas fa-download mr-2"></i>Export
                    </button>
                    <a href="{{ url_for('register_rider') }}" class="modern-btn-rider modern-btn-success">
                        <i class="fas fa-plus mr-2"></i>Register New Rider
                    </a>
                </div>
            </div>
        </div>

        <!-- Modern Performance Stats -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stat-card-modern">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="stat-number-modern">{{ riders|length }}</div>
                            <div class="stat-label-modern">
                                <i class="fas fa-motorcycle mr-2"></i>Total Riders
                            </div>
                        </div>
                        <div class="text-primary">
                            <i class="fas fa-users fa-3x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stat-card-modern">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="stat-number-modern text-success">
                                {% set active_count = 0 %}
                                {% for rider in riders %}
                                    {% if rider.status == 'Active' %}
                                        {% set active_count = active_count + 1 %}
                                    {% endif %}
                                {% endfor %}
                                {{ active_count }}
                            </div>
                            <div class="stat-label-modern">
                                <i class="fas fa-check-circle mr-2 text-success"></i>Active Riders
                            </div>
                        </div>
                        <div class="text-success">
                            <i class="fas fa-motorcycle fa-3x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stat-card-modern">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="stat-number-modern text-warning">
                                {% set pending_count = 0 %}
                                {% for rider in riders %}
                                    {% if rider.status == 'pending' %}
                                        {% set pending_count = pending_count + 1 %}
                                    {% endif %}
                                {% endfor %}
                                {{ pending_count }}
                            </div>
                            <div class="stat-label-modern">
                                <i class="fas fa-clock mr-2 text-warning"></i>Pending Approval
                            </div>
                        </div>
                        <div class="text-warning">
                            <i class="fas fa-hourglass-half fa-3x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stat-card-modern">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="stat-number-modern text-info">
                                {% set total_deliveries = 0 %}
                                {% for rider in riders %}
                                    {% set total_deliveries = total_deliveries + (rider.total_deliveries or 0) %}
                                {% endfor %}
                                {{ total_deliveries }}
                            </div>
                            <div class="stat-label-modern">
                                <i class="fas fa-shipping-fast mr-2 text-info"></i>Total Deliveries
                            </div>
                        </div>
                        <div class="text-info">
                            <i class="fas fa-box fa-3x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modern Riders Table -->
        <div class="modern-rider-card">
            <div class="d-flex align-items-center justify-content-between mb-4">
                <div>
                    <h4 class="font-weight-bold text-primary mb-1">
                        <i class="fas fa-list mr-2"></i>Registered Riders
                    </h4>
                    <p class="text-muted mb-0">Manage and monitor all registered riders</p>
                </div>
                <div>
                    <button class="modern-btn-rider modern-btn-warning" onclick="bulkApprove()">
                        <i class="fas fa-check mr-2"></i>Bulk Approve
                    </button>
                    <button class="modern-btn-rider" onclick="exportRiders()">
                        <i class="fas fa-download mr-2"></i>Export
                    </button>
                </div>
            </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="ridersTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAll">
                            </th>
                            <th>Rider ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>City</th>
                            <th>Bike</th>
                            <th>License</th>
                            <th>Status</th>
                            <th>Rating</th>
                            <th>Deliveries</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for rider in riders %}
                        <tr>
                            <td>
                                <input type="checkbox" class="rider-checkbox" value="{{ rider.rider_id }}">
                            </td>
                            <td>{{ rider.rider_id }}</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-circle bg-primary text-white mr-2">
                                        {{ rider.name[0] if rider.name else 'R' }}{{ rider.name.split(' ')[1][0] if rider.name and ' ' in rider.name else 'R' }}
                                    </div>
                                    <div>
                                        <strong>{{ rider.name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ rider.license_number }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>{{ rider.email }}</td>
                            <td>{{ rider.phone }}</td>
                            <td>{{ rider.current_location or 'N/A' }}</td>
                            <td>
                                {% if rider.make and rider.model %}
                                <span class="badge badge-info">{{ rider.make }} {{ rider.model }}</span>
                                <br>
                                <small class="text-muted">{{ rider.license_plate }}</small>
                                {% else %}
                                <span class="text-muted">No bike registered</span>
                                {% endif %}
                            </td>
                            <td>
                                <small>{{ rider.license_number }}</small>
                                <br>
                                <small class="text-muted">Vehicle: {{ rider.vehicle_number or 'N/A' }}</small>
                            </td>
                            <td>
                                {% if rider.status == 'active' %}
                                <span class="badge badge-success">Active</span>
                                {% elif rider.status == 'pending' %}
                                <span class="badge badge-warning">Pending</span>
                                {% elif rider.status == 'suspended' %}
                                <span class="badge badge-danger">Suspended</span>
                                {% else %}
                                <span class="badge badge-secondary">{{ rider.status|title }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <span class="mr-2">{{ rider.rating|round(1) }}</span>
                                    <div class="rating-stars">
                                        {% for i in range(1, 6) %}
                                            {% if i <= rider.rating %}
                                                <i class="fas fa-star text-warning"></i>
                                            {% else %}
                                                <i class="far fa-star text-muted"></i>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                            </td>
                            <td>{{ rider.total_deliveries }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm modern-btn-rider" onclick="viewRider('{{ rider.rider_id }}')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm modern-btn-rider modern-btn-success" onclick="editRider('{{ rider.rider_id }}')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm modern-btn-rider modern-btn-info" onclick="openRiderLocationOnMap('{{ rider.current_location or rider.city or '' }}', '{{ rider.name }}')" title="View location on Google Maps">
                                        <i class="fas fa-map-marker-alt"></i>
                                    </button>
                                    {% if rider.status == 'pending' %}
                                    <button type="button" class="btn btn-sm modern-btn-rider modern-btn-success" onclick="approveRider('{{ rider.rider_id }}')">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    {% endif %}
                                    {% if rider.status == 'Active' %}
                                    <button type="button" class="btn btn-sm modern-btn-rider modern-btn-warning" onclick="suspendRider('{{ rider.rider_id }}')">
                                        <i class="fas fa-ban"></i>
                                    </button>
                                    {% endif %}
                                    <button type="button" class="btn btn-sm modern-btn-rider modern-btn-danger" onclick="deleteRider('{{ rider.rider_id }}')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    .avatar-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 14px;
    }
    
    .rating-stars {
        font-size: 12px;
    }
    
    .rating-stars i {
        margin-right: 1px;
    }
</style>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        $('#ridersTable').DataTable({
            order: [[1, 'desc']],
            pageLength: 25,
            responsive: true
        });
        
        // Select all checkbox functionality
        $('#selectAll').change(function() {
            $('.rider-checkbox').prop('checked', this.checked);
        });
    });
    
    function viewRider(riderId) {
        // Implement view rider details
        window.location.href = `/riders/${riderId}/view`;
    }
    
    function editRider(riderId) {
        // Implement edit rider
        window.location.href = `/riders/${riderId}/edit`;
    }
    
    function approveRider(riderId) {
        if (confirm('Are you sure you want to approve this rider?')) {
            fetch(`/riders/${riderId}/approve`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(data.message, 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showNotification(data.message, 'error');
                }
            })
            .catch(error => {
                showNotification('Error approving rider', 'error');
            });
        }
    }
    
    function suspendRider(riderId) {
        if (confirm('Are you sure you want to suspend this rider?')) {
            fetch(`/riders/${riderId}/suspend`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(data.message, 'warning');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showNotification(data.message, 'error');
                }
            })
            .catch(error => {
                showNotification('Error suspending rider', 'error');
            });
        }
    }

    function deleteRider(riderId) {
        if (confirm('Are you sure you want to delete this rider? This action cannot be undone.')) {
            fetch(`/riders/${riderId}/delete`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(data.message, 'success');
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showNotification(data.message, 'error');
                }
            })
            .catch(error => {
                showNotification('Error deleting rider', 'error');
            });
        }
    }

    function exportRiders() {
        window.location.href = '/riders/export';
    }

    function bulkApprove() {
        const selectedRiders = $('.rider-checkbox:checked').map(function() {
            return this.value;
        }).get();

        if (selectedRiders.length === 0) {
            showNotification('Please select riders to approve', 'warning');
            return;
        }

        if (confirm(`Are you sure you want to approve ${selectedRiders.length} riders?`)) {
            fetch('/riders/bulk-approve', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({rider_ids: selectedRiders})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(data.message, 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showNotification(data.message, 'error');
                }
            })
            .catch(error => {
                showNotification('Error approving riders', 'error');
            });
        }
    }

    function showNotification(message, type) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        `;

        document.body.appendChild(notification);

        // Auto remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    // Note: Google Maps integration functions are defined in base.html
    // openRiderLocationOnMap, openGoogleMaps, etc. are available globally
</script>
{% endblock %}
