{% extends 'base.html' %}

{% block title %}Manual Assignment - Order {{ order.order_id }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-user-plus"></i> Manual Assignment - Order {{ order.order_id }}
        </h1>
        <a href="{{ url_for('riders.assignment_dashboard') }}" class="btn btn-secondary btn-sm">
            <i class="fas fa-arrow-left"></i> Back to Assignment Dashboard
        </a>
    </div>

    <div class="row">
        <!-- Order Details -->
        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 bg-primary text-white">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-box"></i> Order Details
                    </h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>Order ID:</strong></td>
                            <td>{{ order.order_id }}</td>
                        </tr>
                        <tr>
                            <td><strong>Customer:</strong></td>
                            <td>{{ order.customer_name }}</td>
                        </tr>
                        <tr>
                            <td><strong>Phone:</strong></td>
                            <td>{{ order.customer_phone }}</td>
                        </tr>
                        <tr>
                            <td><strong>Address:</strong></td>
                            <td>{{ order.customer_address }}<br>{{ order.customer_city }}</td>
                        </tr>
                        <tr>
                            <td><strong>Amount:</strong></td>
                            <td><strong>Rs.{{ "{:,.0f}".format(order.order_amount or 0) }}</strong></td>
                        </tr>
                        <tr>
                            <td><strong>Priority:</strong></td>
                            <td>
                                {% if order.priority_level and order.priority_level > 1 %}
                                <span class="badge badge-danger">High Priority</span>
                                {% else %}
                                <span class="badge badge-secondary">Normal</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Packed At:</strong></td>
                            <td>{{ order.packed_at.strftime('%Y-%m-%d %H:%M') if order.packed_at else 'N/A' }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- Rider Selection -->
        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 bg-success text-white">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-users"></i> Select Rider
                    </h6>
                </div>
                <div class="card-body">
                    {% if available_riders %}
                    <form method="POST" action="{{ url_for('riders.assign_order') }}">
                        <input type="hidden" name="order_id" value="{{ order.order_id }}">
                        
                        <div class="form-group">
                            <label for="rider_id"><strong>Available Riders:</strong></label>
                            <select name="rider_id" id="rider_id" class="form-control" required>
                                <option value="">-- Select a Rider --</option>
                                {% for rider in available_riders %}
                                <option value="{{ rider.rider_id }}" 
                                        data-rating="{{ rider.rating or 'N/A' }}"
                                        data-orders="{{ rider.current_orders }}"
                                        data-vehicle="{{ rider.vehicle_type }}"
                                        data-location="{{ rider.current_location or 'Unknown' }}">
                                    {{ rider.name }} - {{ rider.vehicle_type }} 
                                    (Rating: {{ rider.rating or 'N/A' }}/5.0, 
                                     Current Orders: {{ rider.current_orders }})
                                </option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- Rider Details Display -->
                        <div id="riderDetails" class="mt-3" style="display: none;">
                            <div class="card border-left-info">
                                <div class="card-body">
                                    <h6 class="card-title">Selected Rider Details</h6>
                                    <p class="card-text">
                                        <small class="text-muted">
                                            <i class="fas fa-star"></i> <span id="riderRating"></span><br>
                                            <i class="fas fa-motorcycle"></i> <span id="riderVehicle"></span><br>
                                            <i class="fas fa-map-marker-alt"></i> <span id="riderLocation"></span><br>
                                            <i class="fas fa-tasks"></i> <span id="riderOrders"></span> current orders
                                        </small>
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="form-group mt-3">
                            <button type="submit" class="btn btn-success btn-block">
                                <i class="fas fa-check"></i> Assign Order to Selected Rider
                            </button>
                        </div>
                    </form>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-user-slash fa-3x text-muted mb-3"></i>
                        <h5>No Available Riders</h5>
                        <p class="text-muted">All riders are currently busy or unavailable.</p>
                        <a href="{{ url_for('riders.assignment_dashboard') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Dashboard
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3 bg-warning text-white">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-bolt"></i> Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="btn-group">
                        <a href="{{ url_for('riders.auto_assign_order', order_id=order.order_id) }}" 
                           class="btn btn-primary"
                           onclick="return confirm('Auto-assign this order to the best available rider?')">
                            <i class="fas fa-magic"></i> Auto-Assign to Best Rider
                        </a>
                        <a href="{{ url_for('riders.assignment_dashboard') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel Assignment
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('rider_id').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    const detailsDiv = document.getElementById('riderDetails');
    
    if (selectedOption.value) {
        document.getElementById('riderRating').textContent = selectedOption.dataset.rating + '/5.0';
        document.getElementById('riderVehicle').textContent = selectedOption.dataset.vehicle;
        document.getElementById('riderLocation').textContent = selectedOption.dataset.location;
        document.getElementById('riderOrders').textContent = selectedOption.dataset.orders;
        detailsDiv.style.display = 'block';
    } else {
        detailsDiv.style.display = 'none';
    }
});
</script>
{% endblock %}
