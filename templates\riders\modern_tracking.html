{% extends 'base.html' %}

{% block title %}Modern Rider Tracking 2025{% endblock %}

{% block extra_css %}
<style>
    .tracking-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }
    .tracking-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 8px;
    }
    .status-active { background-color: #28a745; }
    .status-busy { background-color: #ffc107; }
    .status-offline { background-color: #dc3545; }
    .status-inactive { background-color: #6c757d; }
    
    .performance-meter {
        height: 8px;
        background-color: #e9ecef;
        border-radius: 4px;
        overflow: hidden;
    }
    .performance-fill {
        height: 100%;
        transition: width 0.3s ease;
    }
    .location-badge {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        border-radius: 20px;
        padding: 0.25rem 0.75rem;
        font-size: 0.8rem;
    }
    .real-time-indicator {
        animation: pulse 2s infinite;
    }
    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-satellite-dish text-primary me-2"></i>
                        Live Rider Tracking 2025
                    </h1>
                    <p class="text-muted mb-0">Real-time rider monitoring and performance tracking</p>
                </div>
                <div>
                    <span class="real-time-indicator badge bg-success me-2">
                        <i class="fas fa-circle"></i> LIVE
                    </span>
                    <button class="btn btn-primary" onclick="refreshTracking()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Stats -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ performance_summary.total_active_riders }}</h4>
                            <p class="mb-0">Active Riders</p>
                        </div>
                        <i class="fas fa-users fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ performance_summary.available_riders }}</h4>
                            <p class="mb-0">Available</p>
                        </div>
                        <i class="fas fa-check-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ performance_summary.riders_on_delivery }}</h4>
                            <p class="mb-0">On Delivery</p>
                        </div>
                        <i class="fas fa-shipping-fast fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ (performance_summary.avg_rating or 0)|round(1) }}</h4>
                            <p class="mb-0">Avg Rating</p>
                        </div>
                        <i class="fas fa-star fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Today's Delivery Stats -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar text-info me-2"></i>
                        Today's Delivery Statistics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <h3 class="text-primary">{{ today_stats.total_deliveries_today }}</h3>
                            <p class="text-muted">Total Orders</p>
                        </div>
                        <div class="col-md-3">
                            <h3 class="text-success">{{ today_stats.completed_today }}</h3>
                            <p class="text-muted">Completed</p>
                        </div>
                        <div class="col-md-3">
                            <h3 class="text-warning">{{ today_stats.pending_today }}</h3>
                            <p class="text-muted">In Progress</p>
                        </div>
                        <div class="col-md-3">
                            <h3 class="text-danger">{{ today_stats.failed_today }}</h3>
                            <p class="text-muted">Failed</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Rider Tracking Grid -->
    <div class="row">
        {% for rider in riders_status %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="tracking-card card h-100">
                <div class="card-body">
                    <!-- Rider Header -->
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div>
                            <h6 class="card-title mb-1">{{ rider.name }}</h6>
                            <small class="text-muted">{{ rider.rider_id }}</small>
                        </div>
                        <div class="text-end">
                            {% if rider.is_available and rider.status == 'active' %}
                                <span class="status-indicator status-active"></span>
                                <small class="text-success">Available</small>
                            {% elif rider.active_orders > 0 %}
                                <span class="status-indicator status-busy"></span>
                                <small class="text-warning">On Delivery</small>
                            {% elif rider.status == 'active' %}
                                <span class="status-indicator status-offline"></span>
                                <small class="text-secondary">Offline</small>
                            {% else %}
                                <span class="status-indicator status-inactive"></span>
                                <small class="text-muted">Inactive</small>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Location -->
                    <div class="mb-3">
                        <span class="location-badge">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            {{ rider.current_location or rider.city }}
                        </span>
                    </div>

                    <!-- Performance Metrics -->
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                            <small>Rating</small>
                            <small>{{ (rider.rating or 0)|round(1) }}/5.0</small>
                        </div>
                        <div class="performance-meter">
                            <div class="performance-fill bg-warning" style="width: {{ (rider.rating / 5 * 100)|round }}%"></div>
                        </div>
                    </div>

                    {% if rider.performance_data %}
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                            <small>On-Time Performance</small>
                            <small>{{ rider.performance_data.get('on_time_percentage', 0) }}%</small>
                        </div>
                        <div class="performance-meter">
                            <div class="performance-fill bg-success" style="width: {{ rider.performance_data.get('on_time_percentage', 0) }}%"></div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                            <small>Customer Satisfaction</small>
                            <small>{{ (rider.performance_data.get('customer_satisfaction', 0) or 0)|round(1) }}/5.0</small>
                        </div>
                        <div class="performance-meter">
                            <div class="performance-fill bg-info" style="width: {{ (rider.performance_data.get('customer_satisfaction', 0) / 5 * 100)|round }}%"></div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Statistics -->
                    <div class="row text-center mb-3">
                        <div class="col-4">
                            <small class="text-muted d-block">Active Orders</small>
                            <strong class="text-primary">{{ rider.active_orders }}</strong>
                        </div>
                        <div class="col-4">
                            <small class="text-muted d-block">Total</small>
                            <strong>{{ rider.total_deliveries }}</strong>
                        </div>
                        <div class="col-4">
                            <small class="text-muted d-block">Success</small>
                            <strong class="text-success">{{ rider.successful_deliveries }}</strong>
                        </div>
                    </div>

                    <!-- Performance Details -->
                    {% if rider.performance_data %}
                    <div class="row text-center">
                        <div class="col-6">
                            <small class="text-muted d-block">Avg Time</small>
                            <strong>{{ rider.performance_data.get('avg_delivery_time', 'N/A') }}min</strong>
                        </div>
                        <div class="col-6">
                            <small class="text-muted d-block">Earnings</small>
                            <strong class="text-success">Rs. {{ (rider.performance_data.get('monthly_earnings', 0) or 0)|int|format_number }}</strong>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Action Buttons -->
                    <div class="mt-3">
                        <div class="btn-group w-100" role="group">
                            <button class="btn btn-sm btn-outline-primary" onclick="viewRiderDetails('{{ rider.rider_id }}')">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-info" onclick="trackRider('{{ rider.rider_id }}')">
                                <i class="fas fa-route"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-success" onclick="contactRider('{{ rider.rider_id }}')">
                                <i class="fas fa-phone"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Rider Details Modal -->
<div class="modal fade" id="riderDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Rider Performance Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="riderDetailsContent">
                <!-- Content loaded via AJAX -->
            </div>
        </div>
    </div>
</div>

<script>
function refreshTracking() {
    location.reload();
}

// Notification function for user feedback
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'info' ? 'primary' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'danger'} alert-dismissible fade show`;
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.zIndex = '9999';
    notification.style.minWidth = '300px';
    notification.innerHTML = `
        ${message}
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto-remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}

function viewRiderDetails(riderId) {
    // Load rider performance data via AJAX
    fetch(`/riders/api/rider/${riderId}/performance`)
        .then(response => response.json())
        .then(data => {
            const content = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>Basic Information</h6>
                        <p><strong>Name:</strong> ${data.rider.name}</p>
                        <p><strong>City:</strong> ${data.rider.city}</p>
                        <p><strong>Rating:</strong> ${data.rider.rating}/5.0</p>
                        <p><strong>Total Deliveries:</strong> ${data.rider.total_deliveries}</p>
                    </div>
                    <div class="col-md-6">
                        <h6>Performance Summary</h6>
                        <p><strong>Success Rate:</strong> ${data.summary.success_rate.toFixed(1)}%</p>
                        <p><strong>Avg Daily Deliveries:</strong> ${data.summary.avg_daily_deliveries.toFixed(1)}</p>
                        <p><strong>Performance Logs:</strong> ${data.summary.total_logs}</p>
                    </div>
                </div>
            `;
            document.getElementById('riderDetailsContent').innerHTML = content;
            new bootstrap.Modal(document.getElementById('riderDetailsModal')).show();
        })
        .catch(error => {
            console.error('Error loading rider details:', error);
            alert('Error loading rider details');
        });
}

function trackRider(riderId) {
    showNotification(`📡 Starting live tracking for rider ${riderId}...`, 'info');

    // Simulate tracking initialization
    setTimeout(() => {
        showNotification(`✅ Live tracking activated for rider ${riderId}`, 'success');

        // Open tracking in new window/tab or redirect to detailed tracking
        setTimeout(() => {
            window.location.href = `/admin/rider_tracking`;
        }, 1000);
    }, 1500);
}

function contactRider(riderId) {
    showNotification(`📞 Initiating contact with rider ${riderId}...`, 'info');

    // Get rider phone number from the page or make API call
    fetch(`/riders/api/rider/${riderId}/performance`)
        .then(response => response.json())
        .then(data => {
            const riderName = data.rider.name;
            const phoneNumber = data.rider.phone || '+92-300-1234567'; // Fallback number

            if (confirm(`📞 Call ${riderName} at ${phoneNumber}?`)) {
                showNotification(`Calling ${riderName}...`, 'success');
                window.open(`tel:${phoneNumber}`);
            }
        })
        .catch(error => {
            console.error('Error getting rider contact:', error);
            // Fallback to generic contact
            if (confirm(`📞 Call rider ${riderId}? (Contact details not available)`)) {
                showNotification(`Attempting to contact rider ${riderId}...`, 'info');
            }
        });
}

// Auto-refresh every 30 seconds
setInterval(function() {
    refreshTracking();
}, 30000);
</script>
{% endblock %}
