{% extends 'base.html' %}

{% block title %}Rider Performance Analytics - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-chart-line text-primary"></i> Rider Performance Analytics
        </h1>
        <div>
            <button class="btn btn-primary shadow-sm" onclick="exportPerformanceReport()">
                <i class="fas fa-download fa-sm text-white-50"></i> Export Report
            </button>
            <button class="btn btn-success shadow-sm" onclick="refreshData()">
                <i class="fas fa-sync fa-sm text-white-50"></i> Refresh
            </button>
        </div>
    </div>

    <!-- Summary Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Riders</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ summary_stats.total_riders }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-motorcycle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Active Riders</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ summary_stats.active_riders }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Avg Performance Score</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ (summary_stats.avg_performance_score or 0)|round(2) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-star fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Total Revenue</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">Rs. {{ (summary_stats.total_revenue or 0)|safe_currency }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Trends Chart -->
    <div class="row">
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Monthly Performance Trends</h6>
                </div>
                <div class="card-body">
                    <canvas id="performanceTrendsChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Detailed Rider Performance</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="performanceTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Rider</th>
                            <th>Contact</th>
                            <th>Status</th>
                            <th>Rating</th>
                            <th>Success Rate</th>
                            <th>Total Deliveries</th>
                            <th>Revenue</th>
                            <th>Performance Score</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for rider in performance_data %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="mr-3">
                                        <div class="icon-circle bg-primary">
                                            <i class="fas fa-motorcycle text-white"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <div class="font-weight-bold">{{ rider.name }}</div>
                                        <div class="text-muted small">{{ rider.rider_id }}</div>
                                        <div class="text-muted small">{{ rider.city }}</div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="small">
                                    <div><i class="fas fa-phone"></i> {{ rider.phone or 'N/A' }}</div>
                                    <div><i class="fas fa-envelope"></i> {{ rider.email or 'N/A' }}</div>
                                </div>
                            </td>
                            <td>
                                <span class="badge badge-{{ 'success' if rider.status == 'Active' else 'warning' if rider.status == 'Pending' else 'danger' }}">
                                    {{ rider.status }}
                                </span>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <span class="mr-2">{{ (rider.rating or 0)|safe_rating|round(1) }}</span>
                                    <div class="star-rating">
                                        {% set rating_value = (rider.rating or 0)|int %}
                                        {% for i in range(5) %}
                                            {% if i < rating_value %}
                                                <i class="fas fa-star text-warning"></i>
                                            {% else %}
                                                <i class="far fa-star text-muted"></i>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar bg-success" role="progressbar" 
                                         style="width: {{ rider.success_rate }}%" 
                                         aria-valuenow="{{ rider.success_rate }}" 
                                         aria-valuemin="0" aria-valuemax="100">
                                        {{ (rider.success_rate or 0)|round(1) }}%
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="text-center">
                                    <div class="font-weight-bold">{{ rider.total_deliveries or 0 }}</div>
                                    <div class="text-muted small">{{ rider.successful_deliveries or 0 }} successful</div>
                                </div>
                            </td>
                            <td>
                                <div class="font-weight-bold text-success">
                                    Rs. {{ (rider.total_revenue or 0)|safe_currency }}
                                </div>
                            </td>
                            <td>
                                <div class="text-center">
                                    <div class="font-weight-bold text-primary">{{ (rider.performance_score or 0)|round(2) }}</div>
                                    <div class="progress mt-1" style="height: 5px;">
                                        <div class="progress-bar bg-primary" role="progressbar" 
                                             style="width: {{ (rider.performance_score or 0) * 20 }}%"></div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-outline-primary" onclick="viewRiderDetails('{{ rider.rider_id }}')" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-info" onclick="viewRiderHistory('{{ rider.rider_id }}')" title="View History">
                                        <i class="fas fa-history"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-success" onclick="contactRider('{{ rider.phone or '+92-300-1234567' }}')" title="Contact">
                                        <i class="fas fa-phone"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Top Performer</h6>
                </div>
                <div class="card-body">
                    {% if summary_stats.top_performer %}
                    <div class="text-center">
                        <div class="icon-circle bg-success mx-auto mb-3" style="width: 60px; height: 60px;">
                            <i class="fas fa-trophy text-white" style="font-size: 24px; line-height: 60px;"></i>
                        </div>
                        <h5 class="font-weight-bold">{{ summary_stats.top_performer.name }}</h5>
                        <p class="text-muted">{{ summary_stats.top_performer.rider_id }}</p>
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="font-weight-bold text-primary">{{ (summary_stats.top_performer.rating or 0)|safe_rating|round(1) }}</div>
                                <div class="text-muted small">Rating</div>
                            </div>
                            <div class="col-6">
                                <div class="font-weight-bold text-success">{{ (summary_stats.top_performer.success_rate or 0)|round(1) }}%</div>
                                <div class="text-muted small">Success Rate</div>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="text-center text-muted">
                        <i class="fas fa-info-circle fa-2x mb-3"></i>
                        <p>No performance data available</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.icon-circle {
    height: 2.5rem;
    width: 2.5rem;
    border-radius: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.star-rating {
    font-size: 0.8rem;
}

.progress {
    background-color: #e9ecef;
}

.btn-group .btn {
    margin-right: 2px;
}
</style>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Performance Trends Chart
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('performanceTrendsChart').getContext('2d');

    const monthlyData = {{ monthly_trends|tojson }};
    const labels = monthlyData.map(item => item.month).reverse();
    const deliveries = monthlyData.map(item => item.total_deliveries).reverse();
    const revenue = monthlyData.map(item => item.revenue).reverse();

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Total Deliveries',
                data: deliveries,
                borderColor: '#4e73df',
                backgroundColor: 'rgba(78, 115, 223, 0.1)',
                tension: 0.3,
                fill: true
            }, {
                label: 'Revenue (Rs.)',
                data: revenue,
                borderColor: '#1cc88a',
                backgroundColor: 'rgba(28, 200, 138, 0.1)',
                tension: 0.3,
                fill: true,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    beginAtZero: true
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    beginAtZero: true,
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            }
        }
    });
});

function viewRiderDetails(riderId) {
    window.location.href = `/riders/${riderId}/view`;
}

function viewRiderHistory(riderId) {
    showNotification(`Loading delivery history for rider ${riderId}...`, 'info');
    setTimeout(() => {
        window.location.href = `/riders/${riderId}/history`;
    }, 1000);
}

function contactRider(phoneNumber) {
    if (phoneNumber && phoneNumber !== 'N/A') {
        if (confirm(`📞 Call rider at ${phoneNumber}?`)) {
            showNotification(`Calling ${phoneNumber}...`, 'info');

            // Log the contact attempt via API
            fetch('/api/riders/contact', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    phone: phoneNumber,
                    action: 'call'
                })
            }).then(response => response.json())
              .then(data => {
                  if (data.success) {
                      showNotification('Contact logged successfully', 'success');
                  }
              }).catch(error => {
                  console.log('Contact logging failed:', error);
              });

            // Open phone dialer
            window.open(`tel:${phoneNumber}`);
        }
    } else {
        showNotification('Phone number not available', 'warning');
    }
}

function exportPerformanceReport() {
    showNotification('📊 Exporting performance report...', 'info');
    setTimeout(() => {
        window.location.href = '/riders/export';
    }, 1500);
}

function refreshData() {
    showNotification('🔄 Refreshing performance data...', 'info');
    setTimeout(() => {
        location.reload();
    }, 1000);
}

// Initialize DataTable
$(document).ready(function() {
    $('#performanceTable').DataTable({
        "pageLength": 10,
        "order": [[ 7, "desc" ]], // Sort by performance score
        "columnDefs": [
            { "orderable": false, "targets": 8 } // Disable sorting on Actions column
        ]
    });
});
</script>
{% endblock %}
