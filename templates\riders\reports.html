{% extends "base.html" %}

{% block title %}Rider Reports - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-chart-bar text-primary me-2"></i>
                        Rider Reports
                    </h1>
                    <p class="text-muted mb-0">Advanced filtering and analytics for rider performance</p>
                </div>
                <div>
                    <a href="{{ url_for('riders.dashboard') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left"></i> Back to Riders
                    </a>
                    <button class="btn btn-success" onclick="exportReports()">
                        <i class="fas fa-file-excel"></i> Export Excel
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-filter"></i> Advanced Filters
            </h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ url_for('riders.reports') }}" id="filterForm">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="rider" class="form-label">Rider</label>
                        <select class="form-control" id="rider" name="rider">
                            <option value="">All Riders</option>
                            {% for rider in all_riders %}
                            <option value="{{ rider.rider_id }}" 
                                {% if filters.rider == rider.rider_id|string %}selected{% endif %}>
                                {{ rider.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="date_from" class="form-label">Date From</label>
                        <input type="date" class="form-control" id="date_from" name="date_from" 
                               value="{{ filters.date_from }}">
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="date_to" class="form-label">Date To</label>
                        <input type="date" class="form-control" id="date_to" name="date_to" 
                               value="{{ filters.date_to }}">
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="customer" class="form-label">Customer</label>
                        <input type="text" class="form-control" id="customer" name="customer" 
                               placeholder="Search customer..." value="{{ filters.customer }}">
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="customer_type" class="form-label">Customer Type</label>
                        <select class="form-control" id="customer_type" name="customer_type">
                            <option value="">All Types</option>
                            <option value="distributor" {% if filters.customer_type == 'distributor' %}selected{% endif %}>Distributors</option>
                            <option value="pharmacy" {% if filters.customer_type == 'pharmacy' %}selected{% endif %}>Pharmacies</option>
                            <option value="institute" {% if filters.customer_type == 'institute' %}selected{% endif %}>Institutes</option>
                            <option value="doctor" {% if filters.customer_type == 'doctor' %}selected{% endif %}>Doctors</option>
                            <option value="walking" {% if filters.customer_type == 'walking' %}selected{% endif %}>Walking Customers</option>
                        </select>
                    </div>
                    <div class="col-md-8 mb-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search"></i> Apply Filters
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                            <i class="fas fa-times"></i> Clear
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Orders</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_orders or 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Revenue</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">Rs. {{ (total_revenue or 0)|safe_currency }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Unique Customers</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ unique_customers or 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Avg Order Value</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">Rs. {{ ((total_revenue or 0) / (total_orders or 1))|safe_currency }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Customer Type Distribution -->
    {% if customer_type_stats %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-pie-chart"></i> Customer Type Distribution
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for ctype, stats in customer_type_stats.items() %}
                        <div class="col-md-2 text-center mb-3">
                            <div class="card border-0">
                                <div class="card-body">
                                    <h5 class="card-title text-primary">{{ stats.count }}</h5>
                                    <p class="card-text">{{ ctype }}</p>
                                    <small class="text-muted">Rs. {{ stats.revenue|safe_currency }}</small>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Reports Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-table"></i> Detailed Reports
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="reportsTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Rider</th>
                            <th>Customer</th>
                            <th>Customer Type</th>
                            <th>Order Date</th>
                            <th>Delivery Date</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Payment</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for row in report_data %}
                        {% if row.order_id %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="mr-3">
                                        <div class="icon-circle bg-primary">
                                            <i class="fas fa-motorcycle text-white"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <div class="font-weight-bold">{{ row.rider_name }}</div>
                                        <div class="text-muted small">{{ row.rider_phone }}</div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <div class="font-weight-bold">{{ row.customer_name }}</div>
                                    <div class="text-muted small">{{ row.customer_phone }}</div>
                                </div>
                            </td>
                            <td>
                                <span class="badge 
                                    {% if row.customer_type_derived == 'Distributor' %}badge-primary
                                    {% elif row.customer_type_derived == 'Pharmacy' %}badge-success
                                    {% elif row.customer_type_derived == 'Institute' %}badge-info
                                    {% elif row.customer_type_derived == 'Doctor' %}badge-warning
                                    {% else %}badge-secondary{% endif %}">
                                    {{ row.customer_type_derived }}
                                </span>
                            </td>
                            <td>{{ (row.order_date or '')|date_only }}</td>
                            <td>{{ (row.delivery_date or '')|date_only }}</td>
                            <td>
                                <div class="font-weight-bold text-success">
                                    Rs. {{ (row.order_amount or 0)|safe_currency }}
                                </div>
                            </td>
                            <td>
                                <span class="badge 
                                    {% if row.status == 'Delivered' %}badge-success
                                    {% elif row.status == 'Pending' %}badge-warning
                                    {% elif row.status == 'Cancelled' %}badge-danger
                                    {% else %}badge-info{% endif %}">
                                    {{ row.status }}
                                </span>
                            </td>
                            <td>
                                <span class="badge 
                                    {% if row.payment_status == 'Paid' %}badge-success
                                    {% elif row.payment_status == 'Pending' %}badge-warning
                                    {% else %}badge-secondary{% endif %}">
                                    {{ row.payment_status or 'Unknown' }}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-outline-primary" onclick="viewOrderDetails('{{ row.order_id }}')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endif %}
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#reportsTable').DataTable({
        "pageLength": 25,
        "order": [[ 3, "desc" ]],
        "columnDefs": [
            { "orderable": false, "targets": 8 }
        ]
    });
});

function clearFilters() {
    document.getElementById('filterForm').reset();
    window.location.href = '{{ url_for("riders.reports") }}';
}

function exportReports() {
    const params = new URLSearchParams(window.location.search);
    window.location.href = '{{ url_for("riders.export_reports") }}?' + params.toString();
}

function viewOrderDetails(orderId) {
    // Implement order details view
    window.location.href = `/orders/${orderId}`;
}
</script>

<style>
.icon-circle {
    height: 2.5rem;
    width: 2.5rem;
    border-radius: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
{% endblock %}
