<!-- Customer Report Template -->
<div class="customer-report">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-users"></i> Customer Analysis Report
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Customer Statistics -->
                    {% if report_data.customer_stats %}
                    <div class="mb-4">
                        <h6 class="text-primary">Top Customers by Value</h6>
                        <div class="table-responsive">
                            <table class="table table-striped table-sm">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Customer</th>
                                        <th>Contact</th>
                                        <th>Orders</th>
                                        <th>Total Spent</th>
                                        <th>Avg Order</th>
                                        <th>Delivered</th>
                                        <th>Last Order</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for customer in report_data.customer_stats %}
                                    <tr>
                                        <td>
                                            <strong>{{ customer.customer_name }}</strong>
                                            {% if customer.customer_address %}
                                            <br><small class="text-muted">{{ customer.customer_address[:50] }}{% if customer.customer_address|length > 50 %}...{% endif %}</small>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if customer.customer_phone %}
                                            <i class="fas fa-phone text-muted"></i> {{ customer.customer_phone }}
                                            {% else %}
                                            <span class="text-muted">N/A</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">{{ customer.total_orders }}</span>
                                        </td>
                                        <td>Rs. {{ "{:,.2f}".format(customer.total_spent or 0) }}</td>
                                        <td>Rs. {{ "{:,.2f}".format(customer.avg_order_value or 0) }}</td>
                                        <td>
                                            <span class="badge bg-success">{{ customer.delivered_orders }}</span>
                                        </td>
                                        <td>
                                            {% if customer.last_order_date %}
                                            {{ customer.last_order_date[:10] }}
                                            {% else %}
                                            <span class="text-muted">N/A</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Customer Success Rate -->
                    {% if report_data.delivery_success_rate %}
                    <div class="mb-4">
                        <h6 class="text-primary">Customer Delivery Success Rate</h6>
                        <div class="table-responsive">
                            <table class="table table-striped table-sm">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Customer</th>
                                        <th>Total Orders</th>
                                        <th>Delivered</th>
                                        <th>Success Rate</th>
                                        <th>Performance</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for customer in report_data.delivery_success_rate %}
                                    <tr>
                                        <td><strong>{{ customer.customer_name }}</strong></td>
                                        <td>{{ customer.total_orders }}</td>
                                        <td>{{ customer.delivered }}</td>
                                        <td>
                                            <span class="badge {% if customer.success_rate >= 90 %}bg-success{% elif customer.success_rate >= 70 %}bg-warning{% else %}bg-danger{% endif %}">
                                                {{ customer.success_rate }}%
                                            </span>
                                        </td>
                                        <td>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar {% if customer.success_rate >= 90 %}bg-success{% elif customer.success_rate >= 70 %}bg-warning{% else %}bg-danger{% endif %}" 
                                                     role="progressbar" 
                                                     style="width: {{ customer.success_rate }}%"
                                                     aria-valuenow="{{ customer.success_rate }}" 
                                                     aria-valuemin="0" 
                                                     aria-valuemax="100">
                                                    {{ customer.success_rate }}%
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Customer Insights -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title text-primary">
                                        <i class="fas fa-chart-bar"></i> Customer Insights
                                    </h6>
                                    {% if report_data.customer_stats %}
                                    {% set total_customers = report_data.customer_stats | length %}
                                    {% set total_orders = report_data.customer_stats | sum(attribute='total_orders') %}
                                    {% set total_revenue = report_data.customer_stats | sum(attribute='total_spent') %}
                                    {% set total_delivered = report_data.customer_stats | sum(attribute='delivered_orders') %}
                                    <ul class="list-unstyled">
                                        <li><strong>Total Customers:</strong> {{ total_customers }}</li>
                                        <li><strong>Total Orders:</strong> {{ total_orders }}</li>
                                        <li><strong>Total Revenue:</strong> Rs. {{ "{:,.2f}".format(total_revenue) }}</li>
                                        <li><strong>Avg per Customer:</strong> Rs. {{ "{:,.2f}".format(total_revenue / (total_customers or 1)) }}</li>
                                        <li><strong>Overall Delivery Rate:</strong> {{ "{:.1f}".format((total_delivered / (total_orders or 1)) * 100) }}%</li>
                                    </ul>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title text-success">
                                        <i class="fas fa-star"></i> Top Performers
                                    </h6>
                                    {% if report_data.customer_stats %}
                                    {% set top_customer = report_data.customer_stats | first %}
                                    {% set best_avg = report_data.customer_stats | max(attribute='avg_order_value') %}
                                    {% set most_orders = report_data.customer_stats | max(attribute='total_orders') %}
                                    <ul class="list-unstyled">
                                        <li><strong>Highest Spender:</strong> {{ top_customer.customer_name }} (Rs. {{ "{:,.2f}".format(top_customer.total_spent) }})</li>
                                        <li><strong>Best Avg Order:</strong> {{ best_avg.customer_name }} (Rs. {{ "{:,.2f}".format(best_avg.avg_order_value) }})</li>
                                        <li><strong>Most Orders:</strong> {{ most_orders.customer_name }} ({{ most_orders.total_orders }} orders)</li>
                                    </ul>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Customer Segmentation -->
                    {% if report_data.customer_stats %}
                    <div class="mt-4">
                        <h6 class="text-primary">Customer Segmentation</h6>
                        <div class="row">
                            {% set high_value = report_data.customer_stats | selectattr('total_spent', 'gt', 50000) | list %}
                            {% set medium_value = report_data.customer_stats | selectattr('total_spent', 'gt', 20000) | selectattr('total_spent', 'le', 50000) | list %}
                            {% set low_value = report_data.customer_stats | selectattr('total_spent', 'le', 20000) | list %}
                            
                            <div class="col-md-4">
                                <div class="card border-left-success">
                                    <div class="card-body">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            High Value (>50K)
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            {{ high_value | length }} customers
                                        </div>
                                        <div class="text-xs text-muted">
                                            Rs. {{ "{:,.0f}".format(high_value | sum(attribute='total_spent')) }} total
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card border-left-warning">
                                    <div class="card-body">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            Medium Value (20K-50K)
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            {{ medium_value | length }} customers
                                        </div>
                                        <div class="text-xs text-muted">
                                            Rs. {{ "{:,.0f}".format(medium_value | sum(attribute='total_spent')) }} total
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card border-left-info">
                                    <div class="card-body">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            Low Value (<20K)
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            {{ low_value | length }} customers
                                        </div>
                                        <div class="text-xs text-muted">
                                            Rs. {{ "{:,.0f}".format(low_value | sum(attribute='total_spent')) }} total
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- No Data Message -->
                    {% if not report_data.customer_stats and not report_data.delivery_success_rate %}
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No customer data available</h5>
                        <p class="text-muted">Try adjusting your date range or rider filters.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.customer-report .card {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border: 1px solid #e3e6f0;
}

.customer-report .table th {
    border-top: none;
    font-weight: 600;
    font-size: 0.875rem;
}

.customer-report .badge {
    font-size: 0.875rem;
}

.customer-report .text-xs {
    font-size: 0.75rem;
}

.customer-report .font-weight-bold {
    font-weight: 700;
}

.customer-report .text-gray-800 {
    color: #5a5c69;
}

.customer-report .progress {
    background-color: #f8f9fc;
}
</style>
