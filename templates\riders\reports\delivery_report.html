<!-- Delivery Report Template -->
<div class="delivery-report">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-truck"></i> Delivery Performance Report
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Daily Delivery Trends -->
                    {% if report_data.daily_trends %}
                    <div class="mb-4">
                        <h6 class="text-primary">Daily Delivery Trends</h6>
                        <div class="table-responsive">
                            <table class="table table-striped table-sm">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Date</th>
                                        <th>Deliveries</th>
                                        <th>Revenue</th>
                                        <th>Avg per Delivery</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for trend in report_data.daily_trends %}
                                    <tr>
                                        <td>{{ trend.delivery_date or 'N/A' }}</td>
                                        <td>
                                            <span class="badge bg-primary">{{ trend.deliveries }}</span>
                                        </td>
                                        <td>Rs. {{ "{:,.2f}".format(trend.revenue or 0) }}</td>
                                        <td>Rs. {{ "{:,.2f}".format((trend.revenue or 0) / (trend.deliveries or 1)) }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Delivery Status Breakdown -->
                    {% if report_data.status_breakdown %}
                    <div class="mb-4">
                        <h6 class="text-primary">Delivery Status Breakdown</h6>
                        <div class="row">
                            {% for status in report_data.status_breakdown %}
                            <div class="col-md-3 mb-3">
                                <div class="card border-left-primary">
                                    <div class="card-body">
                                        <div class="row no-gutters align-items-center">
                                            <div class="col mr-2">
                                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                    {{ status.status }}
                                                </div>
                                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                    {{ status.count }}
                                                </div>
                                                <div class="text-xs text-muted">
                                                    {{ status.percentage }}% of total
                                                </div>
                                            </div>
                                            <div class="col-auto">
                                                {% if status.status == 'Delivered' %}
                                                    <i class="fas fa-check-circle fa-2x text-success"></i>
                                                {% elif status.status == 'Pending' %}
                                                    <i class="fas fa-clock fa-2x text-warning"></i>
                                                {% elif status.status == 'Cancelled' %}
                                                    <i class="fas fa-times-circle fa-2x text-danger"></i>
                                                {% else %}
                                                    <i class="fas fa-box fa-2x text-info"></i>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Summary Statistics -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title text-primary">
                                        <i class="fas fa-chart-line"></i> Delivery Insights
                                    </h6>
                                    {% if report_data.daily_trends %}
                                    {% set total_deliveries = report_data.daily_trends | sum(attribute='deliveries') %}
                                    {% set total_revenue = report_data.daily_trends | sum(attribute='revenue') %}
                                    <ul class="list-unstyled">
                                        <li><strong>Total Deliveries:</strong> {{ total_deliveries }}</li>
                                        <li><strong>Total Revenue:</strong> Rs. {{ "{:,.2f}".format(total_revenue) }}</li>
                                        <li><strong>Average per Day:</strong> {{ (total_deliveries / (report_data.daily_trends | length)) | round(1) }}</li>
                                        <li><strong>Revenue per Delivery:</strong> Rs. {{ "{:,.2f}".format(total_revenue / (total_deliveries or 1)) }}</li>
                                    </ul>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title text-success">
                                        <i class="fas fa-trophy"></i> Performance Highlights
                                    </h6>
                                    {% if report_data.daily_trends and report_data.daily_trends|length > 0 %}
                                        {% set best_day = report_data.daily_trends | max(attribute='deliveries') %}
                                        {% set best_revenue_day = report_data.daily_trends | max(attribute='revenue') %}
                                        <ul class="list-unstyled">
                                            <li><strong>Best Delivery Day:</strong> {{ best_day.delivery_date }} ({{ best_day.deliveries }} deliveries)</li>
                                            <li><strong>Best Revenue Day:</strong> {{ best_revenue_day.delivery_date }} (Rs. {{ "{:,.2f}".format(best_revenue_day.revenue) }})</li>
                                        {% if report_data.status_breakdown %}
                                        {% set delivered_status = report_data.status_breakdown | selectattr('status', 'equalto', 'Delivered') | first %}
                                        {% if delivered_status %}
                                        <li><strong>Success Rate:</strong> {{ delivered_status.percentage }}%</li>
                                        {% endif %}
                                        {% endif %}
                                    </ul>
                                    {% else %}
                                    <p class="text-muted">No performance data available for the selected period.</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- No Data Message -->
                    {% if not report_data.daily_trends and not report_data.status_breakdown %}
                    <div class="text-center py-5">
                        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No delivery data available</h5>
                        <p class="text-muted">Try adjusting your date range or rider filters.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.delivery-report .card {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border: 1px solid #e3e6f0;
}

.delivery-report .table th {
    border-top: none;
    font-weight: 600;
    font-size: 0.875rem;
}

.delivery-report .badge {
    font-size: 0.875rem;
}

.delivery-report .text-xs {
    font-size: 0.75rem;
}

.delivery-report .font-weight-bold {
    font-weight: 700;
}

.delivery-report .text-gray-800 {
    color: #5a5c69;
}
</style>
