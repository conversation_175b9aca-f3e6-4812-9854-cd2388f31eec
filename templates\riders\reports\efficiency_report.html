<!-- Efficiency Report Template -->
<div class="efficiency-report">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-tachometer-alt"></i> Efficiency Analysis Report
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Rider Efficiency Metrics -->
                    {% if report_data.rider_efficiency %}
                    <div class="mb-4">
                        <h6 class="text-dark">Rider Efficiency Metrics</h6>
                        <div class="table-responsive">
                            <table class="table table-striped table-sm">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Rider</th>
                                        <th>Rating</th>
                                        <th>Orders</th>
                                        <th>Success Rate</th>
                                        <th>Revenue/Order</th>
                                        <th>Avg Delivery Time</th>
                                        <th>Efficiency Score</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for rider in report_data.rider_efficiency %}
                                    <tr>
                                        <td>
                                            <strong>{{ rider.name }}</strong>
                                            <br><small class="text-muted">{{ rider.rider_id }}</small>
                                        </td>
                                        <td>
                                            {% if rider.rating %}
                                            <div class="d-flex align-items-center">
                                                <span class="me-1">{{ rider.rating }}</span>
                                                {% for i in range(1, 6) %}
                                                    {% if i <= (rider.rating or 0) %}
                                                        <i class="fas fa-star text-warning"></i>
                                                    {% else %}
                                                        <i class="far fa-star text-muted"></i>
                                                    {% endif %}
                                                {% endfor %}
                                            </div>
                                            {% else %}
                                            <span class="text-muted">No rating</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">{{ rider.total_orders }}</span>
                                            <br><small class="text-success">{{ rider.delivered_orders }} delivered</small>
                                        </td>
                                        <td>
                                            <span class="badge {% if rider.success_rate >= 90 %}bg-success{% elif rider.success_rate >= 75 %}bg-warning{% else %}bg-danger{% endif %}">
                                                {{ rider.success_rate }}%
                                            </span>
                                            <div class="progress mt-1" style="height: 5px;">
                                                <div class="progress-bar {% if rider.success_rate >= 90 %}bg-success{% elif rider.success_rate >= 75 %}bg-warning{% else %}bg-danger{% endif %}" 
                                                     style="width: {{ rider.success_rate }}%"></div>
                                            </div>
                                        </td>
                                        <td>Rs. {{ "{:,.2f}".format(rider.revenue_per_order or 0) }}</td>
                                        <td>
                                            {% if rider.avg_delivery_time_hours > 0 %}
                                                {{ "{:.1f}".format(rider.avg_delivery_time_hours) }}h
                                                {% if rider.avg_delivery_time_hours <= 2 %}
                                                    <i class="fas fa-bolt text-success"></i>
                                                {% elif rider.avg_delivery_time_hours <= 6 %}
                                                    <i class="fas fa-clock text-warning"></i>
                                                {% else %}
                                                    <i class="fas fa-hourglass text-danger"></i>
                                                {% endif %}
                                            {% else %}
                                                <span class="text-muted">N/A</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% set efficiency_score = (rider.success_rate * 0.4) + ((rider.revenue_per_order or 0) / 1000 * 0.3) + (max(0, 100 - (rider.avg_delivery_time_hours or 0) * 10) * 0.3) %}
                                            {% if efficiency_score >= 80 %}
                                                <span class="badge bg-success">{{ "{:.0f}".format(efficiency_score) }}</span>
                                                <i class="fas fa-trophy text-warning"></i>
                                            {% elif efficiency_score >= 60 %}
                                                <span class="badge bg-warning">{{ "{:.0f}".format(efficiency_score) }}</span>
                                                <i class="fas fa-medal text-info"></i>
                                            {% else %}
                                                <span class="badge bg-danger">{{ "{:.0f}".format(efficiency_score) }}</span>
                                                <i class="fas fa-arrow-up text-primary"></i>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Performance Comparison -->
                    {% if report_data.performance_comparison %}
                    <div class="mb-4">
                        <h6 class="text-dark">Performance Benchmarks</h6>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Metric</th>
                                        <th>Top Performer</th>
                                        <th>Average</th>
                                        <th>Benchmark</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for comparison in report_data.performance_comparison %}
                                    {% if comparison.category == 'Top Performer' %}
                                    {% set top_performer = comparison %}
                                    {% elif comparison.category == 'Average' %}
                                    {% set average = comparison %}
                                    {% endif %}
                                    {% endfor %}
                                    
                                    {% if top_performer and average %}
                                    <tr>
                                        <td><strong>Success Rate</strong></td>
                                        <td>
                                            <span class="badge bg-success">{{ "{:.1f}".format(top_performer.max_success_rate or 0) }}%</span>
                                        </td>
                                        <td>{{ "{:.1f}".format(average.avg_success_rate or 0) }}%</td>
                                        <td>
                                            {% if (average.avg_success_rate or 0) >= 85 %}
                                                <span class="badge bg-success">Excellent</span>
                                            {% elif (average.avg_success_rate or 0) >= 75 %}
                                                <span class="badge bg-warning">Good</span>
                                            {% else %}
                                                <span class="badge bg-danger">Needs Improvement</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Revenue per Order</strong></td>
                                        <td>
                                            <span class="badge bg-success">Rs. {{ "{:,.0f}".format(top_performer.max_revenue_per_order or 0) }}</span>
                                        </td>
                                        <td>Rs. {{ "{:,.0f}".format(average.avg_revenue_per_order or 0) }}</td>
                                        <td>
                                            {% if (average.avg_revenue_per_order or 0) >= 3000 %}
                                                <span class="badge bg-success">High Value</span>
                                            {% elif (average.avg_revenue_per_order or 0) >= 2000 %}
                                                <span class="badge bg-warning">Medium Value</span>
                                            {% else %}
                                                <span class="badge bg-info">Standard</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Delivery Time</strong></td>
                                        <td>
                                            <span class="badge bg-success">{{ "{:.1f}".format(top_performer.min_delivery_time or 0) }}h</span>
                                        </td>
                                        <td>{{ "{:.1f}".format(average.avg_delivery_time or 0) }}h</td>
                                        <td>
                                            {% if (average.avg_delivery_time or 0) <= 3 %}
                                                <span class="badge bg-success">Fast</span>
                                            {% elif (average.avg_delivery_time or 0) <= 6 %}
                                                <span class="badge bg-warning">Standard</span>
                                            {% else %}
                                                <span class="badge bg-danger">Slow</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Efficiency Categories -->
                    {% if report_data.rider_efficiency %}
                    <div class="mb-4">
                        <h6 class="text-dark">Efficiency Distribution</h6>
                        <div class="row">
                            {% set high_performers = report_data.rider_efficiency | selectattr('success_rate', 'ge', 90) | list %}
                            {% set good_performers = report_data.rider_efficiency | selectattr('success_rate', 'ge', 75) | selectattr('success_rate', 'lt', 90) | list %}
                            {% set average_performers = report_data.rider_efficiency | selectattr('success_rate', 'lt', 75) | list %}
                            
                            <div class="col-md-4">
                                <div class="card border-left-success">
                                    <div class="card-body">
                                        <div class="row no-gutters align-items-center">
                                            <div class="col mr-2">
                                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                    High Performers (≥90%)
                                                </div>
                                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                    {{ high_performers | length }} riders
                                                </div>
                                                <div class="text-xs text-muted">
                                                    {{ "{:.1f}".format((high_performers | length) / (report_data.rider_efficiency | length) * 100) }}% of team
                                                </div>
                                            </div>
                                            <div class="col-auto">
                                                <i class="fas fa-trophy fa-2x text-success"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card border-left-warning">
                                    <div class="card-body">
                                        <div class="row no-gutters align-items-center">
                                            <div class="col mr-2">
                                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                                    Good Performers (75-89%)
                                                </div>
                                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                    {{ good_performers | length }} riders
                                                </div>
                                                <div class="text-xs text-muted">
                                                    {{ "{:.1f}".format((good_performers | length) / (report_data.rider_efficiency | length) * 100) }}% of team
                                                </div>
                                            </div>
                                            <div class="col-auto">
                                                <i class="fas fa-medal fa-2x text-warning"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card border-left-danger">
                                    <div class="card-body">
                                        <div class="row no-gutters align-items-center">
                                            <div class="col mr-2">
                                                <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                                    Needs Improvement (<75%)
                                                </div>
                                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                    {{ average_performers | length }} riders
                                                </div>
                                                <div class="text-xs text-muted">
                                                    {{ "{:.1f}".format((average_performers | length) / (report_data.rider_efficiency | length) * 100) }}% of team
                                                </div>
                                            </div>
                                            <div class="col-auto">
                                                <i class="fas fa-arrow-up fa-2x text-danger"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Efficiency Summary -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title text-dark">
                                        <i class="fas fa-chart-pie"></i> Team Efficiency Overview
                                    </h6>
                                    {% if report_data.rider_efficiency %}
                                    {% set total_riders = report_data.rider_efficiency | length %}
                                    {% set avg_success = (report_data.rider_efficiency | sum(attribute='success_rate')) / total_riders %}
                                    {% set avg_revenue = (report_data.rider_efficiency | sum(attribute='revenue_per_order')) / total_riders %}
                                    <ul class="list-unstyled">
                                        <li><strong>Total Active Riders:</strong> {{ total_riders }}</li>
                                        <li><strong>Team Avg Success Rate:</strong> {{ "{:.1f}".format(avg_success) }}%</li>
                                        <li><strong>Avg Revenue per Order:</strong> Rs. {{ "{:,.0f}".format(avg_revenue) }}</li>
                                        <li><strong>Total Orders Handled:</strong> {{ report_data.rider_efficiency | sum(attribute='total_orders') }}</li>
                                    </ul>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title text-primary">
                                        <i class="fas fa-crown"></i> Top Efficiency Leaders
                                    </h6>
                                    {% if report_data.rider_efficiency and report_data.rider_efficiency|length > 0 %}
                                        {% set top_success = report_data.rider_efficiency | max(attribute='success_rate') %}
                                        {% set top_revenue = report_data.rider_efficiency | max(attribute='revenue_per_order') %}
                                        {% set most_orders_rider = report_data.rider_efficiency | max(attribute='total_orders') %}
                                        <ul class="list-unstyled">
                                            <li><strong>Best Success Rate:</strong> {{ top_success.name }} ({{ top_success.success_rate }}%)</li>
                                            <li><strong>Best Revenue/Order:</strong> {{ top_revenue.name }} (Rs. {{ "{:,.0f}".format(top_revenue.revenue_per_order) }})</li>
                                            <li><strong>Most Orders:</strong> {{ most_orders_rider.name }}</li>
                                        </ul>
                                    {% else %}
                                        <p class="text-muted">No efficiency data available for the selected period.</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- No Data Message -->
                    {% if not report_data.rider_efficiency and not report_data.performance_comparison %}
                    <div class="text-center py-5">
                        <i class="fas fa-tachometer-alt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No efficiency data available</h5>
                        <p class="text-muted">Try adjusting your date range or rider filters.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.border-left-danger {
    border-left: 0.25rem solid #e74a3b !important;
}

.efficiency-report .card {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border: 1px solid #e3e6f0;
}

.efficiency-report .table th {
    border-top: none;
    font-weight: 600;
    font-size: 0.875rem;
}

.efficiency-report .badge {
    font-size: 0.875rem;
}

.efficiency-report .text-xs {
    font-size: 0.75rem;
}

.efficiency-report .font-weight-bold {
    font-weight: 700;
}

.efficiency-report .text-gray-800 {
    color: #5a5c69;
}

.efficiency-report .progress {
    background-color: #f8f9fc;
}
</style>
