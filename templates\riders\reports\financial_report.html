<!-- Financial Report Template -->
<div class="financial-report">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-dollar-sign"></i> Financial Performance Report
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Revenue by Rider -->
                    {% if report_data.rider_revenue %}
                    <div class="mb-4">
                        <h6 class="text-success">Revenue by Rider</h6>
                        <div class="table-responsive">
                            <table class="table table-striped table-sm">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Rider</th>
                                        <th>Total Orders</th>
                                        <th>Total Revenue</th>
                                        <th>Delivered Revenue</th>
                                        <th>Avg Order Value</th>
                                        <th>Success Rate</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for rider in report_data.rider_revenue %}
                                    <tr>
                                        <td>
                                            <strong>{{ rider.name }}</strong>
                                            <br><small class="text-muted">{{ rider.rider_id }}</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">{{ rider.total_orders }}</span>
                                        </td>
                                        <td>Rs. {{ "{:,.2f}".format(rider.total_revenue or 0) }}</td>
                                        <td>Rs. {{ "{:,.2f}".format(rider.delivered_revenue or 0) }}</td>
                                        <td>Rs. {{ "{:,.2f}".format(rider.avg_order_value or 0) }}</td>
                                        <td>
                                            {% set success_rate = ((rider.delivered_revenue or 0) / (rider.total_revenue or 1)) * 100 %}
                                            <span class="badge {% if success_rate >= 80 %}bg-success{% elif success_rate >= 60 %}bg-warning{% else %}bg-danger{% endif %}">
                                                {{ "{:.1f}".format(success_rate) }}%
                                            </span>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Monthly Revenue Trends -->
                    {% if report_data.monthly_trends %}
                    <div class="mb-4">
                        <h6 class="text-success">Monthly Revenue Trends</h6>
                        <div class="table-responsive">
                            <table class="table table-striped table-sm">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Month</th>
                                        <th>Orders</th>
                                        <th>Total Revenue</th>
                                        <th>Delivered Revenue</th>
                                        <th>Conversion Rate</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for trend in report_data.monthly_trends %}
                                    <tr>
                                        <td>{{ trend.month }}</td>
                                        <td>{{ trend.orders }}</td>
                                        <td>Rs. {{ "{:,.2f}".format(trend.revenue or 0) }}</td>
                                        <td>Rs. {{ "{:,.2f}".format(trend.delivered_revenue or 0) }}</td>
                                        <td>
                                            {% set conversion = ((trend.delivered_revenue or 0) / (trend.revenue or 1)) * 100 %}
                                            {{ "{:.1f}".format(conversion) }}%
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Payment Status Breakdown -->
                    {% if report_data.payment_status %}
                    <div class="mb-4">
                        <h6 class="text-success">Payment Status Analysis</h6>
                        <div class="row">
                            {% for status in report_data.payment_status %}
                            <div class="col-md-4 mb-3">
                                <div class="card border-left-success">
                                    <div class="card-body">
                                        <div class="row no-gutters align-items-center">
                                            <div class="col mr-2">
                                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                    {{ status.payment_status }}
                                                </div>
                                                <div class="h6 mb-0 font-weight-bold text-gray-800">
                                                    {{ status.count }} orders
                                                </div>
                                                <div class="text-sm text-success">
                                                    Rs. {{ "{:,.2f}".format(status.total_amount or 0) }}
                                                </div>
                                            </div>
                                            <div class="col-auto">
                                                {% if status.payment_status == 'paid' %}
                                                    <i class="fas fa-check-circle fa-2x text-success"></i>
                                                {% elif status.payment_status == 'pending' %}
                                                    <i class="fas fa-clock fa-2x text-warning"></i>
                                                {% else %}
                                                    <i class="fas fa-exclamation-triangle fa-2x text-danger"></i>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Financial Summary -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title text-success">
                                        <i class="fas fa-chart-pie"></i> Revenue Summary
                                    </h6>
                                    {% if report_data.rider_revenue %}
                                    {% set total_revenue = report_data.rider_revenue | sum(attribute='total_revenue') %}
                                    {% set total_delivered = report_data.rider_revenue | sum(attribute='delivered_revenue') %}
                                    {% set total_orders = report_data.rider_revenue | sum(attribute='total_orders') %}
                                    <ul class="list-unstyled">
                                        <li><strong>Total Revenue:</strong> Rs. {{ "{:,.2f}".format(total_revenue) }}</li>
                                        <li><strong>Delivered Revenue:</strong> Rs. {{ "{:,.2f}".format(total_delivered) }}</li>
                                        <li><strong>Total Orders:</strong> {{ total_orders }}</li>
                                        <li><strong>Average Order Value:</strong> Rs. {{ "{:,.2f}".format(total_revenue / (total_orders or 1)) }}</li>
                                        <li><strong>Revenue Conversion:</strong> {{ "{:.1f}".format((total_delivered / (total_revenue or 1)) * 100) }}%</li>
                                    </ul>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title text-primary">
                                        <i class="fas fa-trophy"></i> Top Performers
                                    </h6>
                                    {% if report_data.rider_revenue %}
                                    {% set top_rider = report_data.rider_revenue | first %}
                                    {% set best_avg = report_data.rider_revenue | max(attribute='avg_order_value') %}
                                    <ul class="list-unstyled">
                                        <li><strong>Top Revenue:</strong> {{ top_rider.name }} (Rs. {{ "{:,.2f}".format(top_rider.total_revenue) }})</li>
                                        <li><strong>Best Avg Order:</strong> {{ best_avg.name }} (Rs. {{ "{:,.2f}".format(best_avg.avg_order_value) }})</li>
                                        <li><strong>Active Riders:</strong> {{ report_data.rider_revenue | length }}</li>
                                    </ul>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- No Data Message -->
                    {% if not report_data.rider_revenue and not report_data.monthly_trends %}
                    <div class="text-center py-5">
                        <i class="fas fa-dollar-sign fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No financial data available</h5>
                        <p class="text-muted">Try adjusting your date range or rider filters.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.financial-report .card {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border: 1px solid #e3e6f0;
}

.financial-report .table th {
    border-top: none;
    font-weight: 600;
    font-size: 0.875rem;
}

.financial-report .badge {
    font-size: 0.875rem;
}

.financial-report .text-xs {
    font-size: 0.75rem;
}

.financial-report .text-sm {
    font-size: 0.875rem;
}

.financial-report .font-weight-bold {
    font-weight: 700;
}

.financial-report .text-gray-800 {
    color: #5a5c69;
}
</style>
