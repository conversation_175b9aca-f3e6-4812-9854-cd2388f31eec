<!-- Geographic Report Template -->
<div class="geographic-report">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-map-marker-alt"></i> Geographic Analysis Report
                    </h5>
                </div>
                <div class="card-body">
                    <!-- City Statistics -->
                    {% if report_data.city_stats %}
                    <div class="mb-4">
                        <h6 class="text-warning">Delivery Performance by City</h6>
                        <div class="table-responsive">
                            <table class="table table-striped table-sm">
                                <thead class="table-dark">
                                    <tr>
                                        <th>City</th>
                                        <th>Total Deliveries</th>
                                        <th>Total Revenue</th>
                                        <th>Active Riders</th>
                                        <th>Avg Order Value</th>
                                        <th>Market Share</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% set total_deliveries = report_data.city_stats | sum(attribute='total_deliveries') %}
                                    {% for city in report_data.city_stats %}
                                    <tr>
                                        <td>
                                            <strong>{{ city.city }}</strong>
                                            <i class="fas fa-map-pin text-warning ml-2"></i>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">{{ city.total_deliveries }}</span>
                                        </td>
                                        <td>Rs. {{ "{:,.2f}".format(city.total_revenue or 0) }}</td>
                                        <td>
                                            <span class="badge bg-info">{{ city.active_riders }}</span>
                                        </td>
                                        <td>Rs. {{ "{:,.2f}".format(city.avg_order_value or 0) }}</td>
                                        <td>
                                            {% set market_share = (city.total_deliveries / (total_deliveries or 1)) * 100 %}
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar bg-warning" 
                                                     role="progressbar" 
                                                     style="width: {{ market_share }}%"
                                                     aria-valuenow="{{ market_share }}" 
                                                     aria-valuemin="0" 
                                                     aria-valuemax="100">
                                                    {{ "{:.1f}".format(market_share) }}%
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Address Analysis -->
                    {% if report_data.address_stats %}
                    <div class="mb-4">
                        <h6 class="text-warning">Delivery Areas Distribution</h6>
                        <div class="row">
                            {% for area in report_data.address_stats %}
                            <div class="col-md-4 mb-3">
                                <div class="card border-left-warning">
                                    <div class="card-body">
                                        <div class="row no-gutters align-items-center">
                                            <div class="col mr-2">
                                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                                    {{ area.delivery_area }}
                                                </div>
                                                <div class="h6 mb-0 font-weight-bold text-gray-800">
                                                    {{ area.deliveries }} deliveries
                                                </div>
                                                <div class="text-sm text-success">
                                                    Rs. {{ "{:,.2f}".format(area.revenue or 0) }}
                                                </div>
                                            </div>
                                            <div class="col-auto">
                                                <i class="fas fa-building fa-2x text-warning"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Rider Coverage -->
                    {% if report_data.rider_coverage %}
                    <div class="mb-4">
                        <h6 class="text-warning">Rider Coverage by Area</h6>
                        <div class="accordion" id="riderCoverageAccordion">
                            {% set cities = report_data.rider_coverage | groupby('city') %}
                            {% for city, riders in cities %}
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="heading{{ loop.index }}">
                                    <button class="accordion-button {% if not loop.first %}collapsed{% endif %}" 
                                            type="button" 
                                            data-bs-toggle="collapse" 
                                            data-bs-target="#collapse{{ loop.index }}" 
                                            aria-expanded="{% if loop.first %}true{% else %}false{% endif %}" 
                                            aria-controls="collapse{{ loop.index }}">
                                        <i class="fas fa-city text-warning me-2"></i>
                                        <strong>{{ city or 'Unknown City' }}</strong>
                                        <span class="badge bg-warning ms-2">{{ riders | list | length }} riders</span>
                                    </button>
                                </h2>
                                <div id="collapse{{ loop.index }}" 
                                     class="accordion-collapse collapse {% if loop.first %}show{% endif %}" 
                                     aria-labelledby="heading{{ loop.index }}" 
                                     data-bs-parent="#riderCoverageAccordion">
                                    <div class="accordion-body">
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>Rider</th>
                                                        <th>Orders Handled</th>
                                                        <th>Revenue Generated</th>
                                                        <th>Performance</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {% for rider in riders %}
                                                    <tr>
                                                        <td>{{ rider.rider_name }}</td>
                                                        <td>
                                                            <span class="badge bg-primary">{{ rider.orders_handled }}</span>
                                                        </td>
                                                        <td>Rs. {{ "{:,.2f}".format(rider.revenue_generated or 0) }}</td>
                                                        <td>
                                                            {% if rider.orders_handled > 10 %}
                                                                <span class="badge bg-success">High</span>
                                                            {% elif rider.orders_handled > 5 %}
                                                                <span class="badge bg-warning">Medium</span>
                                                            {% else %}
                                                                <span class="badge bg-secondary">Low</span>
                                                            {% endif %}
                                                        </td>
                                                    </tr>
                                                    {% endfor %}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Geographic Summary -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title text-warning">
                                        <i class="fas fa-globe"></i> Coverage Summary
                                    </h6>
                                    {% if report_data.city_stats %}
                                    {% set total_cities = report_data.city_stats | length %}
                                    {% set total_deliveries = report_data.city_stats | sum(attribute='total_deliveries') %}
                                    {% set total_revenue = report_data.city_stats | sum(attribute='total_revenue') %}
                                    {% set total_riders = report_data.city_stats | sum(attribute='active_riders') %}
                                    <ul class="list-unstyled">
                                        <li><strong>Cities Covered:</strong> {{ total_cities }}</li>
                                        <li><strong>Total Deliveries:</strong> {{ total_deliveries }}</li>
                                        <li><strong>Total Revenue:</strong> Rs. {{ "{:,.2f}".format(total_revenue) }}</li>
                                        <li><strong>Active Riders:</strong> {{ total_riders }}</li>
                                        <li><strong>Avg per City:</strong> {{ (total_deliveries / (total_cities or 1)) | round(1) }} deliveries</li>
                                    </ul>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title text-primary">
                                        <i class="fas fa-trophy"></i> Top Markets
                                    </h6>
                                    {% if report_data.city_stats and report_data.city_stats|length > 0 %}
                                        {% set top_city = report_data.city_stats | first %}
                                        {% set best_revenue = report_data.city_stats | max(attribute='total_revenue') %}
                                        {% set best_avg = report_data.city_stats | max(attribute='avg_order_value') %}
                                    <ul class="list-unstyled">
                                        <li><strong>Most Deliveries:</strong> {{ top_city.city }} ({{ top_city.total_deliveries }})</li>
                                        <li><strong>Highest Revenue:</strong> {{ best_revenue.city }} (Rs. {{ "{:,.0f}".format(best_revenue.total_revenue) }})</li>
                                        <li><strong>Best Avg Order:</strong> {{ best_avg.city }} (Rs. {{ "{:,.0f}".format(best_avg.avg_order_value) }})</li>
                                    </ul>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- No Data Message -->
                    {% if not report_data.city_stats and not report_data.address_stats %}
                    <div class="text-center py-5">
                        <i class="fas fa-map-marker-alt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No geographic data available</h5>
                        <p class="text-muted">Try adjusting your date range or rider filters.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.geographic-report .card {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border: 1px solid #e3e6f0;
}

.geographic-report .table th {
    border-top: none;
    font-weight: 600;
    font-size: 0.875rem;
}

.geographic-report .badge {
    font-size: 0.875rem;
}

.geographic-report .text-xs {
    font-size: 0.75rem;
}

.geographic-report .text-sm {
    font-size: 0.875rem;
}

.geographic-report .font-weight-bold {
    font-weight: 700;
}

.geographic-report .text-gray-800 {
    color: #5a5c69;
}

.geographic-report .progress {
    background-color: #f8f9fc;
}

.geographic-report .accordion-button {
    background-color: #f8f9fc;
    border: none;
}

.geographic-report .accordion-button:not(.collapsed) {
    background-color: #fff3cd;
    color: #856404;
}
</style>
