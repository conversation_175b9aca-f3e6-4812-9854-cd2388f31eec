<!-- Overview Report Content -->

<!-- Summary Metrics -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="metric-card">
            <div class="metric-value">{{ report_data.summary.total_orders or 0 }}</div>
            <div class="metric-label">Total Orders</div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="metric-card">
            <div class="metric-value">{{ report_data.summary.delivered_orders or 0 }}</div>
            <div class="metric-label">Delivered Orders</div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="metric-card">
            <div class="metric-value">Rs.{{ "{:,.0f}".format(report_data.summary.total_revenue or 0) }}</div>
            <div class="metric-label">Total Revenue</div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="metric-card">
            <div class="metric-value">{{ report_data.summary.active_riders or 0 }}</div>
            <div class="metric-label">Active Riders</div>
        </div>
    </div>
</div>

<!-- Success Rate Card -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card report-card">
            <div class="card-header bg-success text-white">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-percentage"></i> Overall Success Rate
                </h6>
            </div>
            <div class="card-body text-center">
                {% set success_rate = (report_data.summary.delivered_orders / report_data.summary.total_orders * 100) if report_data.summary.total_orders > 0 else 0 %}
                <div class="display-4 text-success font-weight-bold">{{ "{:.1f}%".format(success_rate) }}</div>
                <p class="text-muted">Orders successfully delivered out of total orders</p>
                
                <div class="progress" style="height: 20px;">
                    <div class="progress-bar bg-success" role="progressbar" 
                         style="width: {{ success_rate }}%" 
                         aria-valuenow="{{ success_rate }}" 
                         aria-valuemin="0" 
                         aria-valuemax="100">
                        {{ "{:.1f}%".format(success_rate) }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Top Performing Riders -->
<div class="card report-card mb-4">
    <div class="card-header bg-warning text-white">
        <h6 class="m-0 font-weight-bold">
            <i class="fas fa-trophy"></i> Top Performing Riders
        </h6>
    </div>
    <div class="card-body">
        {% if report_data.top_riders %}
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead class="thead-light">
                    <tr>
                        <th>Rank</th>
                        <th>Rider Name</th>
                        <th>Deliveries</th>
                        <th>Revenue</th>
                        <th>Avg Rating</th>
                        <th>Performance</th>
                    </tr>
                </thead>
                <tbody>
                    {% for rider in report_data.top_riders %}
                    <tr>
                        <td>
                            {% if loop.index == 1 %}
                            <span class="badge badge-warning"><i class="fas fa-crown"></i> #{{ loop.index }}</span>
                            {% elif loop.index <= 3 %}
                            <span class="badge badge-secondary"><i class="fas fa-medal"></i> #{{ loop.index }}</span>
                            {% else %}
                            <span class="badge badge-light">#{{ loop.index }}</span>
                            {% endif %}
                        </td>
                        <td><strong>{{ rider.name }}</strong></td>
                        <td>
                            <span class="badge badge-primary">{{ rider.deliveries }}</span>
                        </td>
                        <td>
                            <strong>Rs.{{ "{:,.0f}".format(rider.revenue or 0) }}</strong>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <span class="mr-2">{{ rider.avg_rating }}</span>
                                <div class="rating-stars">
                                    {% for i in range(5) %}
                                        {% if i < (rider.avg_rating | round) %}
                                        <i class="fas fa-star text-warning"></i>
                                        {% else %}
                                        <i class="far fa-star text-muted"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                        </td>
                        <td>
                            {% set performance_score = (rider.deliveries * 0.4 + rider.avg_rating * 20) %}
                            {% if performance_score >= 80 %}
                            <span class="badge badge-success">Excellent</span>
                            {% elif performance_score >= 60 %}
                            <span class="badge badge-warning">Good</span>
                            {% else %}
                            <span class="badge badge-secondary">Average</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-4">
            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
            <h5>No Performance Data</h5>
            <p class="text-muted">No rider performance data available for the selected period.</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Quick Insights -->
<div class="row">
    <div class="col-md-6">
        <div class="card report-card">
            <div class="card-header bg-info text-white">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-lightbulb"></i> Key Insights
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    {% if report_data.summary.total_orders > 0 %}
                    <li class="mb-2">
                        <i class="fas fa-check-circle text-success"></i>
                        <strong>{{ "{:.1f}%".format((report_data.summary.delivered_orders / report_data.summary.total_orders * 100)) }}</strong> 
                        delivery success rate
                    </li>
                    {% endif %}
                    
                    {% if report_data.summary.delivered_orders > 0 %}
                    <li class="mb-2">
                        <i class="fas fa-dollar-sign text-success"></i>
                        <strong>Rs.{{ "{:,.0f}".format((report_data.summary.total_revenue / report_data.summary.delivered_orders) if report_data.summary.delivered_orders > 0 else 0) }}</strong> 
                        average order value
                    </li>
                    {% endif %}
                    
                    {% if report_data.top_riders %}
                    <li class="mb-2">
                        <i class="fas fa-trophy text-warning"></i>
                        <strong>{{ report_data.top_riders[0].name }}</strong> 
                        is the top performer
                    </li>
                    {% endif %}
                    
                    <li class="mb-2">
                        <i class="fas fa-users text-info"></i>
                        <strong>{{ report_data.summary.active_riders }}</strong> 
                        riders are currently active
                    </li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card report-card">
            <div class="card-header bg-secondary text-white">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-chart-pie"></i> Performance Distribution
                </h6>
            </div>
            <div class="card-body">
                {% if report_data.top_riders %}
                <div class="chart-container">
                    <div class="text-center">
                        <h5>Top 5 Riders by Deliveries</h5>
                        {% for rider in report_data.top_riders[:5] %}
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>{{ rider.name }}</span>
                            <div class="progress flex-grow-1 mx-3" style="height: 20px;">
                                {% set max_deliveries = report_data.top_riders[0].deliveries %}
                                {% set percentage = (rider.deliveries / max_deliveries * 100) if max_deliveries > 0 else 0 %}
                                <div class="progress-bar bg-primary" 
                                     style="width: {{ percentage }}%">
                                    {{ rider.deliveries }}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% else %}
                <div class="chart-container">
                    <div class="text-center text-muted">
                        <i class="fas fa-chart-pie fa-3x mb-3"></i>
                        <p>No data available for chart</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
