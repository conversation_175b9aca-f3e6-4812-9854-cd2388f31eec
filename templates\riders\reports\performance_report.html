<!-- Performance Report Content -->

<!-- Performance Summary -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card report-card">
            <div class="card-header bg-primary text-white">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-tachometer-alt"></i> Rider Performance Analysis
                </h6>
            </div>
            <div class="card-body">
                {% if report_data.rider_performance %}
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="thead-dark">
                            <tr>
                                <th>Rider Name</th>
                                <th>Phone</th>
                                <th>Total Orders</th>
                                <th>Delivered</th>
                                <th>Cancelled</th>
                                <th>Success Rate</th>
                                <th>Revenue</th>
                                <th>Avg Rating</th>
                                <th>Performance Grade</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for rider in report_data.rider_performance %}
                            <tr>
                                <td><strong>{{ rider.name }}</strong></td>
                                <td>{{ rider.phone }}</td>
                                <td>
                                    <span class="badge badge-info">{{ rider.total_orders or 0 }}</span>
                                </td>
                                <td>
                                    <span class="badge badge-success">{{ rider.delivered or 0 }}</span>
                                </td>
                                <td>
                                    <span class="badge badge-danger">{{ rider.cancelled or 0 }}</span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="progress flex-grow-1 mr-2" style="height: 20px;">
                                            {% set success_rate = rider.success_rate or 0 %}
                                            <div class="progress-bar 
                                                {% if success_rate >= 90 %}bg-success
                                                {% elif success_rate >= 75 %}bg-warning
                                                {% else %}bg-danger{% endif %}" 
                                                 style="width: {{ success_rate }}%">
                                                {{ "{:.1f}%".format(success_rate) }}
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <strong>Rs.{{ "{:,.0f}".format(rider.revenue or 0) }}</strong>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="mr-2">{{ rider.avg_rating or 0 }}</span>
                                        <div class="rating-stars">
                                            {% for i in range(5) %}
                                                {% if i < ((rider.avg_rating or 0) | round) %}
                                                <i class="fas fa-star text-warning"></i>
                                                {% else %}
                                                <i class="far fa-star text-muted"></i>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    {% set performance_score = ((rider.success_rate or 0) + (rider.avg_rating or 0) * 20) / 2 %}
                                    {% if performance_score >= 90 %}
                                    <span class="badge badge-success">A+</span>
                                    {% elif performance_score >= 80 %}
                                    <span class="badge badge-success">A</span>
                                    {% elif performance_score >= 70 %}
                                    <span class="badge badge-warning">B</span>
                                    {% elif performance_score >= 60 %}
                                    <span class="badge badge-warning">C</span>
                                    {% else %}
                                    <span class="badge badge-danger">D</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                    <h5>No Performance Data</h5>
                    <p class="text-muted">No rider performance data available for the selected period.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Performance Insights -->
<div class="row">
    <div class="col-md-4">
        <div class="card report-card">
            <div class="card-header bg-success text-white">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-trophy"></i> Top Performers
                </h6>
            </div>
            <div class="card-body">
                {% if report_data.rider_performance %}
                {% set top_performers = report_data.rider_performance[:3] %}
                {% for rider in top_performers %}
                <div class="d-flex justify-content-between align-items-center mb-3 p-2 bg-light rounded">
                    <div>
                        <strong>{{ rider.name }}</strong><br>
                        <small class="text-muted">{{ "{:.1f}%".format(rider.success_rate or 0) }} success rate</small>
                    </div>
                    <div class="text-right">
                        {% if loop.index == 1 %}
                        <i class="fas fa-crown text-warning fa-2x"></i>
                        {% elif loop.index == 2 %}
                        <i class="fas fa-medal text-secondary fa-2x"></i>
                        {% else %}
                        <i class="fas fa-award text-warning fa-2x"></i>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
                {% else %}
                <p class="text-muted">No top performers data available.</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card report-card">
            <div class="card-header bg-warning text-white">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-exclamation-triangle"></i> Needs Improvement
                </h6>
            </div>
            <div class="card-body">
                {% if report_data.rider_performance %}
                {% set low_performers = report_data.rider_performance | selectattr('success_rate', 'lt', 75) | list %}
                {% if low_performers %}
                {% for rider in low_performers[:3] %}
                <div class="d-flex justify-content-between align-items-center mb-3 p-2 bg-light rounded">
                    <div>
                        <strong>{{ rider.name }}</strong><br>
                        <small class="text-muted">{{ "{:.1f}%".format(rider.success_rate or 0) }} success rate</small>
                    </div>
                    <div>
                        <span class="badge badge-warning">Focus Area</span>
                    </div>
                </div>
                {% endfor %}
                {% else %}
                <div class="text-center text-success">
                    <i class="fas fa-check-circle fa-3x mb-2"></i>
                    <p>All riders performing well!</p>
                </div>
                {% endif %}
                {% else %}
                <p class="text-muted">No performance data available.</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card report-card">
            <div class="card-header bg-info text-white">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-chart-bar"></i> Performance Stats
                </h6>
            </div>
            <div class="card-body">
                {% if report_data.rider_performance %}
                {% set total_riders = report_data.rider_performance | length %}
                {% set high_performers = report_data.rider_performance | selectattr('success_rate', 'ge', 90) | list | length %}
                {% set avg_performers = report_data.rider_performance | selectattr('success_rate', 'ge', 75) | selectattr('success_rate', 'lt', 90) | list | length %}
                {% set low_performers = report_data.rider_performance | selectattr('success_rate', 'lt', 75) | list | length %}
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Excellent (90%+)</span>
                        <strong class="text-success">{{ high_performers }}</strong>
                    </div>
                    <div class="progress mb-2" style="height: 10px;">
                        <div class="progress-bar bg-success" 
                             style="width: {{ (high_performers / total_riders * 100) if total_riders > 0 else 0 }}%"></div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Good (75-89%)</span>
                        <strong class="text-warning">{{ avg_performers }}</strong>
                    </div>
                    <div class="progress mb-2" style="height: 10px;">
                        <div class="progress-bar bg-warning" 
                             style="width: {{ (avg_performers / total_riders * 100) if total_riders > 0 else 0 }}%"></div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Needs Work (<75%)</span>
                        <strong class="text-danger">{{ low_performers }}</strong>
                    </div>
                    <div class="progress mb-2" style="height: 10px;">
                        <div class="progress-bar bg-danger" 
                             style="width: {{ (low_performers / total_riders * 100) if total_riders > 0 else 0 }}%"></div>
                    </div>
                </div>
                
                <hr>
                <div class="text-center">
                    <h5 class="text-primary">{{ total_riders }}</h5>
                    <small class="text-muted">Total Active Riders</small>
                </div>
                {% else %}
                <p class="text-muted">No statistics available.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
