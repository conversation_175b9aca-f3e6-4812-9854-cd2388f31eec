<!-- Time Analysis Report Template -->
<div class="time-analysis-report">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-clock"></i> Time Analysis Report
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Delivery Time Analysis -->
                    {% if report_data.delivery_times %}
                    <div class="mb-4">
                        <h6 class="text-info">Delivery Time Performance by Rider</h6>
                        <div class="table-responsive">
                            <table class="table table-striped table-sm">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Rider</th>
                                        <th>Total Orders</th>
                                        <th>Avg Delivery Time</th>
                                        <th>Fastest Delivery</th>
                                        <th>Slowest Delivery</th>
                                        <th>Performance Rating</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for rider in report_data.delivery_times %}
                                    <tr>
                                        <td><strong>{{ rider.name }}</strong></td>
                                        <td>
                                            <span class="badge bg-primary">{{ rider.total_orders }}</span>
                                        </td>
                                        <td>
                                            {% if rider.avg_delivery_hours > 0 %}
                                                {{ "{:.1f}".format(rider.avg_delivery_hours) }} hours
                                            {% else %}
                                                <span class="text-muted">N/A</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if rider.min_delivery_hours > 0 %}
                                                {{ "{:.1f}".format(rider.min_delivery_hours) }} hours
                                            {% else %}
                                                <span class="text-muted">N/A</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if rider.max_delivery_hours > 0 %}
                                                {{ "{:.1f}".format(rider.max_delivery_hours) }} hours
                                            {% else %}
                                                <span class="text-muted">N/A</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if rider.avg_delivery_hours > 0 %}
                                                {% if rider.avg_delivery_hours <= 2 %}
                                                    <span class="badge bg-success">Excellent</span>
                                                {% elif rider.avg_delivery_hours <= 6 %}
                                                    <span class="badge bg-warning">Good</span>
                                                {% elif rider.avg_delivery_hours <= 12 %}
                                                    <span class="badge bg-orange">Average</span>
                                                {% else %}
                                                    <span class="badge bg-danger">Needs Improvement</span>
                                                {% endif %}
                                            {% else %}
                                                <span class="badge bg-secondary">No Data</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Hourly Delivery Patterns -->
                    {% if report_data.hourly_patterns %}
                    <div class="mb-4">
                        <h6 class="text-info">Hourly Delivery Patterns</h6>
                        <div class="table-responsive">
                            <table class="table table-striped table-sm">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Hour</th>
                                        <th>Deliveries</th>
                                        <th>Avg Order Value</th>
                                        <th>Activity Level</th>
                                        <th>Performance Chart</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if report_data.hourly_patterns and report_data.hourly_patterns|length > 0 %}
                                        {% set max_deliveries = report_data.hourly_patterns | max(attribute='deliveries') %}
                                    {% else %}
                                        {% set max_deliveries = {'deliveries': 1} %}
                                    {% endif %}
                                    {% for hour in report_data.hourly_patterns %}
                                    <tr>
                                        <td>
                                            <strong>{{ hour.hour }}:00</strong>
                                            {% if hour.hour|int >= 6 and hour.hour|int < 12 %}
                                                <i class="fas fa-sun text-warning"></i>
                                            {% elif hour.hour|int >= 12 and hour.hour|int < 18 %}
                                                <i class="fas fa-sun text-orange"></i>
                                            {% elif hour.hour|int >= 18 and hour.hour|int < 22 %}
                                                <i class="fas fa-moon text-info"></i>
                                            {% else %}
                                                <i class="fas fa-moon text-dark"></i>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ hour.deliveries }}</span>
                                        </td>
                                        <td>Rs. {{ "{:,.2f}".format(hour.avg_order_value or 0) }}</td>
                                        <td>
                                            {% set activity_level = (hour.deliveries / (max_deliveries.deliveries or 1)) * 100 %}
                                            {% if activity_level >= 80 %}
                                                <span class="badge bg-success">Peak</span>
                                            {% elif activity_level >= 50 %}
                                                <span class="badge bg-warning">High</span>
                                            {% elif activity_level >= 25 %}
                                                <span class="badge bg-info">Medium</span>
                                            {% else %}
                                                <span class="badge bg-secondary">Low</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar bg-info" 
                                                     role="progressbar" 
                                                     style="width: {{ activity_level }}%"
                                                     aria-valuenow="{{ activity_level }}" 
                                                     aria-valuemin="0" 
                                                     aria-valuemax="100">
                                                    {{ "{:.0f}".format(activity_level) }}%
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Weekly Delivery Patterns -->
                    {% if report_data.weekly_patterns %}
                    <div class="mb-4">
                        <h6 class="text-info">Weekly Delivery Patterns</h6>
                        <div class="row">
                            {% for day in report_data.weekly_patterns %}
                            <div class="col-md-3 mb-3">
                                <div class="card border-left-info">
                                    <div class="card-body">
                                        <div class="row no-gutters align-items-center">
                                            <div class="col mr-2">
                                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                                    {{ day.day_of_week }}
                                                </div>
                                                <div class="h6 mb-0 font-weight-bold text-gray-800">
                                                    {{ day.deliveries }} deliveries
                                                </div>
                                                <div class="text-sm text-success">
                                                    Rs. {{ "{:,.2f}".format(day.avg_order_value or 0) }}
                                                </div>
                                            </div>
                                            <div class="col-auto">
                                                {% if day.day_of_week in ['Saturday', 'Sunday'] %}
                                                    <i class="fas fa-calendar-week fa-2x text-warning"></i>
                                                {% else %}
                                                    <i class="fas fa-calendar-day fa-2x text-info"></i>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Time Analysis Summary -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title text-info">
                                        <i class="fas fa-stopwatch"></i> Delivery Time Insights
                                    </h6>
                                    {% if report_data.delivery_times and report_data.delivery_times|length > 0 %}
                                        {% set avg_times = report_data.delivery_times | selectattr('avg_delivery_hours', 'gt', 0) | list %}
                                        {% if avg_times and avg_times|length > 0 %}
                                            {% set overall_avg = (avg_times | sum(attribute='avg_delivery_hours')) / (avg_times | length) %}
                                            {% set fastest_rider = avg_times | min(attribute='avg_delivery_hours') %}
                                            {% set slowest_rider = avg_times | max(attribute='avg_delivery_hours') %}
                                            <ul class="list-unstyled">
                                                <li><strong>Overall Avg Time:</strong> {{ "{:.1f}".format(overall_avg) }} hours</li>
                                                <li><strong>Fastest Rider:</strong> {{ fastest_rider.name }} ({{ "{:.1f}".format(fastest_rider.avg_delivery_hours) }}h)</li>
                                                <li><strong>Slowest Rider:</strong> {{ slowest_rider.name }} ({{ "{:.1f}".format(slowest_rider.avg_delivery_hours) }}h)</li>
                                                <li><strong>Active Riders:</strong> {{ avg_times | length }}</li>
                                            </ul>
                                        {% else %}
                                            <p class="text-muted">No delivery time data available for the selected period.</p>
                                        {% endif %}
                                    {% else %}
                                        <p class="text-muted">No delivery time data available.</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title text-primary">
                                        <i class="fas fa-chart-line"></i> Peak Activity Times
                                    </h6>
                                    {% if report_data.hourly_patterns and report_data.hourly_patterns|length > 0 %}
                                        {% set peak_hour = report_data.hourly_patterns | max(attribute='deliveries') %}
                                        {% set peak_revenue_hour = report_data.hourly_patterns | max(attribute='avg_order_value') %}
                                        {% set total_hourly = report_data.hourly_patterns | sum(attribute='deliveries') %}
                                        <ul class="list-unstyled">
                                            <li><strong>Peak Hour:</strong> {{ peak_hour.hour }}:00 ({{ peak_hour.deliveries }} deliveries)</li>
                                            <li><strong>Peak Revenue Hour:</strong> {{ peak_revenue_hour.hour }}:00</li>
                                            <li><strong>Total Hourly Data:</strong> {{ total_hourly }} deliveries</li>
                                        </ul>
                                    {% else %}
                                        <p class="text-muted">No hourly pattern data available.</p>
                                    {% endif %}
                                    {% if report_data.weekly_patterns and report_data.weekly_patterns|length > 0 %}
                                        {% set best_day = report_data.weekly_patterns | max(attribute='deliveries') %}
                                        <ul class="list-unstyled">
                                            <li><strong>Best Day:</strong> {{ best_day.day_of_week }} ({{ best_day.deliveries }} deliveries)</li>
                                        </ul>
                                    {% else %}
                                        <p class="text-muted">No weekly pattern data available.</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- No Data Message -->
                    {% if not report_data.delivery_times and not report_data.hourly_patterns and not report_data.weekly_patterns %}
                    <div class="text-center py-5">
                        <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No time analysis data available</h5>
                        <p class="text-muted">Try adjusting your date range or rider filters.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.time-analysis-report .card {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border: 1px solid #e3e6f0;
}

.time-analysis-report .table th {
    border-top: none;
    font-weight: 600;
    font-size: 0.875rem;
}

.time-analysis-report .badge {
    font-size: 0.875rem;
}

.time-analysis-report .text-xs {
    font-size: 0.75rem;
}

.time-analysis-report .text-sm {
    font-size: 0.875rem;
}

.time-analysis-report .font-weight-bold {
    font-weight: 700;
}

.time-analysis-report .text-gray-800 {
    color: #5a5c69;
}

.time-analysis-report .progress {
    background-color: #f8f9fc;
}

.time-analysis-report .bg-orange {
    background-color: #fd7e14 !important;
}

.time-analysis-report .text-orange {
    color: #fd7e14 !important;
}
</style>
