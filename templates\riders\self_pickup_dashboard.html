{% extends 'base.html' %}

{% block title %}Rider Self-Pickup Dashboard{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-hand-paper"></i> Self-Pickup Dashboard
        </h1>
        <div class="btn-group">
            <a href="{{ url_for('riders.dashboard') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Back to Rider Dashboard
            </a>
            <button onclick="refreshDashboard()" class="btn btn-primary btn-sm">
                <i class="fas fa-sync-alt"></i> Refresh
            </button>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Available for Pickup
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ available_orders|length }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-box-open fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                My Active Orders
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ current_orders|length }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-motorcycle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total Value Available
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                Rs.{{ "{:,.0f}".format(available_orders|sum(attribute='order_amount') or 0) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Available Orders for Pickup -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 bg-success text-white">
            <h6 class="m-0 font-weight-bold">
                <i class="fas fa-hand-paper"></i> Orders Available for Self-Pickup ({{ available_orders|length }})
            </h6>
        </div>
        <div class="card-body">
            {% if available_orders %}
            <div class="table-responsive">
                <table class="table table-bordered" id="availableOrdersTable">
                    <thead>
                        <tr>
                            <th>Order ID</th>
                            <th>Customer</th>
                            <th>Delivery Address</th>
                            <th>Amount</th>
                            <th>Priority</th>
                            <th>Warehouse</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for order in available_orders %}
                        <tr>
                            <td>
                                <strong class="text-primary">{{ order.order_id }}</strong><br>
                                <small class="text-muted">{{ order.packed_at.strftime('%m/%d %H:%M') if order.packed_at else 'N/A' }}</small>
                            </td>
                            <td>
                                <strong>{{ order.customer_name }}</strong><br>
                                <small class="text-muted">{{ order.customer_phone }}</small>
                            </td>
                            <td>
                                <small>{{ order.customer_address }}<br>
                                {{ order.customer_city }}</small>
                            </td>
                            <td>
                                <strong>Rs.{{ "{:,.0f}".format(order.order_amount or 0) }}</strong>
                            </td>
                            <td>
                                {% if order.priority_level and order.priority_level > 1 %}
                                <span class="badge badge-danger">High</span>
                                {% else %}
                                <span class="badge badge-secondary">Normal</span>
                                {% endif %}
                            </td>
                            <td>
                                <small>{{ order.warehouse_name or 'Main Warehouse' }}<br>
                                {{ order.warehouse_address or 'N/A' }}</small>
                            </td>
                            <td>
                                <button class="btn btn-success btn-sm" onclick="claimOrder('{{ order.order_id }}')">
                                    <i class="fas fa-hand-paper"></i> Claim Order
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                <h4>No Orders Available</h4>
                <p class="text-muted">All orders have been assigned or there are no orders ready for pickup.</p>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- My Current Orders -->
    {% if current_orders %}
    <div class="card shadow mb-4">
        <div class="card-header py-3 bg-warning text-white">
            <h6 class="m-0 font-weight-bold">
                <i class="fas fa-motorcycle"></i> My Current Orders ({{ current_orders|length }})
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Order ID</th>
                            <th>Customer</th>
                            <th>Address</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Dispatched</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for order in current_orders %}
                        <tr>
                            <td><strong class="text-primary">{{ order.order_id }}</strong></td>
                            <td>{{ order.customer_name }}</td>
                            <td><small>{{ order.customer_address }}</small></td>
                            <td><strong>Rs.{{ "{:,.0f}".format(order.order_amount or 0) }}</strong></td>
                            <td>
                                {% if order.status == 'Dispatched' %}
                                <span class="badge badge-warning">{{ order.status }}</span>
                                {% elif order.status == 'Out for Delivery' %}
                                <span class="badge badge-info">{{ order.status }}</span>
                                {% else %}
                                <span class="badge badge-secondary">{{ order.status }}</span>
                                {% endif %}
                            </td>
                            <td><small>{{ order.dispatch_date.strftime('%Y-%m-%d %H:%M') if order.dispatch_date else 'N/A' }}</small></td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Instructions -->
    <div class="card shadow">
        <div class="card-header py-3 bg-info text-white">
            <h6 class="m-0 font-weight-bold">
                <i class="fas fa-info-circle"></i> Self-Pickup Instructions
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6><i class="fas fa-1"></i> How to Claim Orders:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success"></i> Browse available orders above</li>
                        <li><i class="fas fa-check text-success"></i> Click "Claim Order" for orders you want to deliver</li>
                        <li><i class="fas fa-check text-success"></i> Go to the warehouse to pick up the order</li>
                        <li><i class="fas fa-check text-success"></i> Deliver to customer address</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6><i class="fas fa-2"></i> Important Notes:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-exclamation-triangle text-warning"></i> High priority orders should be delivered first</li>
                        <li><i class="fas fa-clock text-info"></i> Orders are sorted by priority and value</li>
                        <li><i class="fas fa-phone text-primary"></i> Contact customer before delivery</li>
                        <li><i class="fas fa-map-marker-alt text-danger"></i> Verify delivery address carefully</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function refreshDashboard() {
    location.reload();
}

function claimOrder(orderId) {
    if (confirm(`Are you sure you want to claim order ${orderId}? You will be responsible for picking it up and delivering it.`)) {
        window.location.href = `/riders/claim-order/${orderId}`;
    }
}
</script>
{% endblock %}
