<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Code Modal Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>QR Code Modal Test</h1>
        <p>This page tests the QR code generation for order ORD00000165</p>
        
        <button class="btn btn-primary" onclick="testQRCode()">Test QR Code Generation</button>
        <button class="btn btn-success" onclick="openModal()">Open Enhanced Modal</button>
        
        <div class="mt-4">
            <h3>Test Results:</h3>
            <div id="testResults" class="alert alert-info">
                Click "Test QR Code Generation" to start testing...
            </div>
        </div>
        
        <div class="mt-4">
            <h3>QR Code Display:</h3>
            <div id="qrDisplay" class="border p-3" style="min-height: 200px;">
                QR code will appear here...
            </div>
        </div>
    </div>

    <!-- Include the enhanced modal -->
    {% include 'components/enhanced_order_modal.html' %}

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/enhanced_modal.js') }}"></script>
    
    <script>
        async function testQRCode() {
            const resultsDiv = document.getElementById('testResults');
            const qrDisplay = document.getElementById('qrDisplay');
            
            resultsDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing QR code generation...';
            qrDisplay.innerHTML = 'Loading...';
            
            try {
                console.log('Starting QR code test...');
                
                // Test the API endpoint directly
                const response = await fetch('/api/order-qr-code/ORD00000165?branding=true');
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('API Response:', data);
                
                if (data.success) {
                    resultsDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h5><i class="fas fa-check-circle"></i> QR Code Generated Successfully!</h5>
                            <p><strong>Order ID:</strong> ${data.order_id}</p>
                            <p><strong>File Path:</strong> ${data.qr_code.file_path}</p>
                            <p><strong>Base64 Length:</strong> ${data.qr_code.base64.length} characters</p>
                            <p><strong>File Size:</strong> ${data.qr_code.size} bytes</p>
                        </div>
                    `;
                    
                    // Display the QR code
                    qrDisplay.innerHTML = `
                        <div class="text-center">
                            <img src="data:image/png;base64,${data.qr_code.base64}" 
                                 alt="QR Code" 
                                 class="img-fluid border" 
                                 style="max-width: 300px;">
                            <p class="mt-2 text-muted">QR Code for Order ORD00000165</p>
                        </div>
                    `;
                    
                } else {
                    resultsDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h5><i class="fas fa-exclamation-triangle"></i> QR Code Generation Failed</h5>
                            <p><strong>Error:</strong> ${data.error}</p>
                        </div>
                    `;
                    qrDisplay.innerHTML = '<div class="text-center text-muted">QR code generation failed</div>';
                }
                
            } catch (error) {
                console.error('Test error:', error);
                resultsDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h5><i class="fas fa-times-circle"></i> Test Failed</h5>
                        <p><strong>Error:</strong> ${error.message}</p>
                    </div>
                `;
                qrDisplay.innerHTML = '<div class="text-center text-muted">Test failed</div>';
            }
        }
        
        function openModal() {
            if (typeof enhancedOrderModal !== 'undefined') {
                enhancedOrderModal.show('ORD00000165');
            } else {
                alert('Enhanced modal not available');
            }
        }
    </script>
</body>
</html>
