{% extends "base.html" %}

{% block title %}Validation Testing{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-vial"></i> Comprehensive Validation Testing
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Test Results -->
                        <div class="col-md-6">
                            <h5>Test Results</h5>
                            <div id="test-results">
                                <div class="alert alert-info">
                                    Click "Run Tests" to start validation testing
                                </div>
                            </div>
                        </div>
                        
                        <!-- Test Controls -->
                        <div class="col-md-6">
                            <h5>Test Controls</h5>
                            <div class="btn-group-vertical w-100" role="group">
                                <button type="button" class="btn btn-primary mb-2" onclick="runAllTests()">
                                    <i class="fas fa-play"></i> Run All Tests
                                </button>
                                <button type="button" class="btn btn-info mb-2" onclick="testProductValidation()">
                                    <i class="fas fa-pills"></i> Test Product Validation
                                </button>
                                <button type="button" class="btn btn-success mb-2" onclick="testInventoryCreation()">
                                    <i class="fas fa-warehouse"></i> Test Inventory Creation
                                </button>
                                <button type="button" class="btn btn-warning mb-2" onclick="testDivisionValidation()">
                                    <i class="fas fa-building"></i> Test Division Validation
                                </button>
                                <button type="button" class="btn btn-secondary mb-2" onclick="clearResults()">
                                    <i class="fas fa-trash"></i> Clear Results
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function addResult(message, type = 'info') {
    const resultsDiv = document.getElementById('test-results');
    const alertClass = type === 'success' ? 'alert-success' : 
                      type === 'error' ? 'alert-danger' : 
                      type === 'warning' ? 'alert-warning' : 'alert-info';
    
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert ${alertClass}`;
    alertDiv.innerHTML = message;
    
    resultsDiv.appendChild(alertDiv);
    resultsDiv.scrollTop = resultsDiv.scrollHeight;
}

function clearResults() {
    document.getElementById('test-results').innerHTML = '<div class="alert alert-info">Ready for testing</div>';
}

async function testProductValidation() {
    addResult('<i class="fas fa-spinner fa-spin"></i> Testing Product P001 validation...', 'info');
    
    try {
        // Test the inventory creation URL that was failing
        const response = await fetch('/inventory/new?product_id=P001');
        const text = await response.text();
        
        if (response.ok && !text.includes('does not have a valid division assignment')) {
            addResult('<i class="fas fa-check"></i> ✅ Product P001 validation PASSED - No division error found', 'success');
            return true;
        } else if (text.includes('does not have a valid division assignment')) {
            addResult('<i class="fas fa-times"></i> ❌ Product P001 validation FAILED - Division error still present', 'error');
            return false;
        } else {
            addResult('<i class="fas fa-question"></i> ⚠️ Product P001 validation UNCLEAR - Response: ' + response.status, 'warning');
            return false;
        }
    } catch (error) {
        addResult('<i class="fas fa-exclamation-triangle"></i> ❌ Product validation test failed: ' + error.message, 'error');
        return false;
    }
}

async function testInventoryCreation() {
    addResult('<i class="fas fa-spinner fa-spin"></i> Testing inventory creation form...', 'info');
    
    try {
        // Test if the inventory form loads without errors
        const response = await fetch('/inventory/new');
        const text = await response.text();
        
        if (response.ok && text.includes('Create New Inventory')) {
            addResult('<i class="fas fa-check"></i> ✅ Inventory creation form loads successfully', 'success');
            
            // Check if products are available in the form
            if (text.includes('P001') || text.includes('Fulvestutant')) {
                addResult('<i class="fas fa-check"></i> ✅ Products are available in inventory form', 'success');
                return true;
            } else {
                addResult('<i class="fas fa-times"></i> ❌ No products found in inventory form', 'error');
                return false;
            }
        } else {
            addResult('<i class="fas fa-times"></i> ❌ Inventory form failed to load: ' + response.status, 'error');
            return false;
        }
    } catch (error) {
        addResult('<i class="fas fa-exclamation-triangle"></i> ❌ Inventory creation test failed: ' + error.message, 'error');
        return false;
    }
}

async function testDivisionValidation() {
    addResult('<i class="fas fa-spinner fa-spin"></i> Testing division validation...', 'info');
    
    try {
        // Test product detail page to see if division name is displayed
        const response = await fetch('/products/P001');
        const text = await response.text();
        
        if (response.ok) {
            if (text.includes('Aqvida') && !text.includes('DIV8286AC29')) {
                addResult('<i class="fas fa-check"></i> ✅ Division name "Aqvida" displayed correctly (not ID)', 'success');
                return true;
            } else if (text.includes('DIV8286AC29')) {
                addResult('<i class="fas fa-times"></i> ❌ Division ID still displayed instead of name', 'error');
                return false;
            } else {
                addResult('<i class="fas fa-question"></i> ⚠️ Division information not found in product page', 'warning');
                return false;
            }
        } else {
            addResult('<i class="fas fa-times"></i> ❌ Product detail page failed to load: ' + response.status, 'error');
            return false;
        }
    } catch (error) {
        addResult('<i class="fas fa-exclamation-triangle"></i> ❌ Division validation test failed: ' + error.message, 'error');
        return false;
    }
}

async function runAllTests() {
    clearResults();
    addResult('<i class="fas fa-rocket"></i> 🚀 Starting comprehensive validation tests...', 'info');
    
    const tests = [
        { name: 'Product Validation', func: testProductValidation },
        { name: 'Inventory Creation', func: testInventoryCreation },
        { name: 'Division Validation', func: testDivisionValidation }
    ];
    
    let passedTests = 0;
    let totalTests = tests.length;
    
    for (const test of tests) {
        addResult(`<hr><strong>Running ${test.name} Test...</strong>`, 'info');
        const result = await test.func();
        if (result) passedTests++;
    }
    
    addResult('<hr>', 'info');
    if (passedTests === totalTests) {
        addResult(`<strong>🎉 ALL TESTS PASSED! (${passedTests}/${totalTests})</strong>`, 'success');
        addResult('✅ Division validation error should be resolved', 'success');
        addResult('✅ Division display issue should be fixed', 'success');
        addResult('✅ Inventory creation should work correctly', 'success');
    } else {
        addResult(`<strong>⚠️ SOME TESTS FAILED (${passedTests}/${totalTests} passed)</strong>`, 'warning');
        addResult('❌ Additional fixes may be needed', 'error');
    }
}

// Auto-run tests when page loads
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(runAllTests, 1000);
});
</script>
{% endblock %}
