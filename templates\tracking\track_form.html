{% extends "base.html" %}

{% block title %}TCS Express Tracking{% endblock %}

{% block content %}

<style>
/* Progress bar styling */
.progress {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.progress-bar {
    border-radius: 15px;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    transition: width 0.6s ease;
}

#progressContainer {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    animation: slideInFromTop 0.5s ease-out;
}

@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced progress animations */
.progress-bar-animated {
    background-image: linear-gradient(45deg, rgba(255,255,255,.15) 25%, transparent 25%, transparent 50%, rgba(255,255,255,.15) 50%, rgba(255,255,255,.15) 75%, transparent 75%, transparent);
    background-size: 1rem 1rem;
    animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
    0% { background-position: 1rem 0; }
    100% { background-position: 0 0; }
}

/* Current tracking number highlight */
#currentTrackingNumber {
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    padding: 8px;
    border: 1px solid rgba(255,255,255,0.2);
}

/* Summary cards styling */
.alert-info .bg-primary,
.alert-info .bg-success,
.alert-info .bg-warning,
.alert-info .bg-danger {
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    transition: transform 0.2s ease;
}

.alert-info .bg-primary:hover,
.alert-info .bg-success:hover,
.alert-info .bg-warning:hover,
.alert-info .bg-danger:hover {
    transform: translateY(-2px);
}
</style>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-shipping-fast me-2"></i>
                        TCS Express Tracking
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row justify-content-center">
                        <div class="col-md-8">
                            <!-- Single Tracking Form -->
                            <div class="mb-4">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-search me-2"></i>
                                    Track Single Package
                                </h5>
                                <form id="singleTrackForm" method="POST" action="{{ url_for('tracking.track_post') }}">
                                    <div class="input-group mb-3">
                                        <input type="text"
                                               class="form-control form-control-lg"
                                               name="tracking_number"
                                               id="trackingNumber"
                                               placeholder="Enter TCS tracking number (e.g., TCS123456789)"
                                               required>
                                        <button class="btn btn-primary btn-lg" type="submit">
                                            <i class="fas fa-search me-2"></i>Track Package
                                        </button>
                                    </div>
                                </form>
                            </div>

                            <!-- Bulk Tracking Form -->
                            <div class="mb-4">
                                <h5 class="text-success mb-3">
                                    <i class="fas fa-list me-2"></i>
                                    Bulk Tracking (API)
                                </h5>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Enter multiple tracking numbers (one per line) for bulk tracking via API
                                </div>
                                <form id="bulkTrackForm">
                                    <div class="mb-3">
                                        <textarea class="form-control" 
                                                  id="bulkTrackingNumbers" 
                                                  rows="5" 
                                                  placeholder="Enter tracking numbers, one per line:&#10;TCS123456789&#10;TCS987654321&#10;TCS555666777"></textarea>
                                    </div>
                                    <button class="btn btn-success" type="submit">
                                        <i class="fas fa-list-check me-2"></i>Track All (Max 10)
                                    </button>
                                </form>
                            </div>

                            <!-- Progress Tracking -->
                            <div id="progressContainer" class="card bg-info text-white" style="display: none;">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-spinner fa-spin me-2"></i>
                                        Bulk Tracking Progress
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <span class="fw-bold">Progress:</span>
                                                <span id="progressCounter" class="badge bg-light text-dark fs-6">0/0</span>
                                            </div>
                                            <div class="progress" style="height: 25px;">
                                                <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated bg-success"
                                                     role="progressbar" style="width: 0%">
                                                    <span id="progressPercentage" class="fw-bold">0%</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="text-center">
                                                <div class="mb-2">
                                                    <i class="fas fa-search fa-2x text-light mb-2"></i>
                                                </div>
                                                <div id="currentTrackingNumber" class="fw-bold fs-6 mb-1">
                                                    Preparing...
                                                </div>
                                                <div id="remainingCount" class="small">
                                                    Remaining: 0
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="progressDetails" class="text-center">
                                        <small>Initializing bulk tracking process...</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Container -->
    <div id="resultsContainer" class="mt-4" style="display: none;">
        <div class="card shadow-sm">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-check-circle me-2"></i>
                    Tracking Results
                </h5>
            </div>
            <div class="card-body">
                <div id="resultsContent"></div>
            </div>
        </div>
    </div>

    <!-- Loading Spinner -->
    <div id="loadingSpinner" class="text-center mt-4" style="display: none;">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-2">Fetching tracking information...</p>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle bulk tracking form
    document.getElementById('bulkTrackForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const textarea = document.getElementById('bulkTrackingNumbers');
        const trackingNumbers = textarea.value.split('\n')
            .map(num => num.trim())
            .filter(num => num.length > 0);
        
        if (trackingNumbers.length === 0) {
            alert('Please enter at least one tracking number');
            return;
        }
        
        if (trackingNumbers.length > 10) {
            alert('Maximum 10 tracking numbers allowed');
            return;
        }
        
        // Show progress tracking
        showProgressTracking(trackingNumbers.length);
        document.getElementById('resultsContainer').style.display = 'none';

        // Process tracking numbers with real-time progress
        processBulkTrackingWithProgress(trackingNumbers);
    });
    
    function displayBulkResults(data) {
        const resultsContainer = document.getElementById('resultsContainer');
        const resultsContent = document.getElementById('resultsContent');
        
        let html = '';
        
        if (data.summary) {
            html += `
                <div class="alert alert-info">
                    <h6><i class="fas fa-chart-pie me-2"></i>Bulk Tracking Summary</h6>
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="bg-primary text-white rounded p-2 mb-2">
                                <h5 class="mb-0">${data.summary.total_processed}</h5>
                                <small>Total Processed</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="bg-success text-white rounded p-2 mb-2">
                                <h5 class="mb-0">${data.summary.found || data.summary.successful}</h5>
                                <small>Found (With Data)</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="bg-warning text-white rounded p-2 mb-2">
                                <h5 class="mb-0">${data.summary.not_found}</h5>
                                <small>Not Found</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="bg-danger text-white rounded p-2 mb-2">
                                <h5 class="mb-0">${data.summary.errors}</h5>
                                <small>Errors</small>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        if (data.results && data.results.length > 0) {
            data.results.forEach((result, index) => {
                html += formatTrackingResult(result, index + 1);
            });
        }
        
        resultsContent.innerHTML = html;
        resultsContainer.style.display = 'block';
    }
    
    function formatTrackingResult(result, index) {
        // Use enhanced classification if available, otherwise fall back to status
        const classification = result.classification || result.status;

        if (classification === 'found' || (result.status === 'success' && classification !== 'not_found')) {
            return `
                <div class="card mb-3 shadow-sm border-success">
                    <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-check-circle me-2"></i>
                            #${index} -
                            <a href="/track/${result.tracking_number}" target="_blank" class="text-white text-decoration-none fw-bold">
                                ${result.tracking_number}
                            </a>
                        </h6>
                        <span class="badge bg-light text-success">SUCCESSFUL</span>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h6 class="text-primary"><i class="fas fa-info-circle me-1"></i>Booking Details</h6>
                                <p class="mb-1"><strong>Origin:</strong> <span class="text-muted">${result.data.booking_details?.origin || 'Not Available'}</span></p>
                                <p class="mb-1"><strong>Destination:</strong> <span class="text-muted">${result.data.booking_details?.destination || 'Not Available'}</span></p>
                                <p class="mb-1"><strong>Booking Date:</strong> <span class="text-muted">${result.data.booking_details?.booking_date || 'Not Available'}</span></p>
                                <p class="mb-0"><strong>Agent Ref:</strong> <span class="text-muted">${result.data.booking_details?.agent_reference || 'Not Available'}</span></p>
                            </div>
                            <div class="col-md-4">
                                <h6 class="text-success"><i class="fas fa-truck me-1"></i>Current Status</h6>
                                <p class="mb-1"><strong>Status:</strong> <span class="badge bg-success">${result.data.track_summary?.current_status || 'Not Available'}</span></p>
                                <p class="mb-1"><strong>Delivered On:</strong> <span class="text-muted">${result.data.track_summary?.delivered_on !== 'N/A' ? result.data.track_summary?.delivered_on : 'Not Delivered'}</span></p>
                                <p class="mb-0"><strong>Received By:</strong> <span class="text-muted">${result.data.track_summary?.received_by !== 'N/A' ? result.data.track_summary?.received_by : 'Not Available'}</span></p>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="d-grid gap-2">
                                    <a href="/track/${result.tracking_number}" target="_blank" class="btn btn-primary btn-sm">
                                        <i class="fas fa-external-link-alt me-1"></i>View Details
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        } else if (classification === 'not_found' || result.status === 'not_found') {
            return `
                <div class="card mb-3 shadow-sm border-warning">
                    <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            #${index} -
                            <span class="fw-bold">${result.tracking_number}</span>
                        </h6>
                        <span class="badge bg-dark text-warning">NOT FOUND</span>
                    </div>
                    <div class="card-body text-center">
                        <i class="fas fa-search fa-2x text-warning mb-2"></i>
                        <p class="text-warning mb-0">No record found for this tracking number</p>
                        <small class="text-muted">Please verify the tracking number and try again</small>
                    </div>
                </div>
            `;
        } else {
            return `
                <div class="card mb-3 shadow-sm border-danger">
                    <div class="card-header bg-danger text-white d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            #${index} -
                            <span class="fw-bold">${result.tracking_number}</span>
                        </h6>
                        <span class="badge bg-light text-danger">ERROR</span>
                    </div>
                    <div class="card-body text-center">
                        <i class="fas fa-times-circle fa-2x text-danger mb-2"></i>
                        <p class="text-danger mb-1">Error occurred while tracking</p>
                        <small class="text-muted">${result.error || 'Unknown error occurred'}</small>
                    </div>
                </div>
            `;
        }
    }

    function downloadBulkPDF(data) {
        // Create a form to submit PDF generation request
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/api/track/bulk/pdf';
        form.target = '_blank';

        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'tracking_data';
        input.value = JSON.stringify(data);

        form.appendChild(input);
        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
    }

    function showProgressTracking(totalCount) {
        const progressContainer = document.getElementById('progressContainer');
        const progressBar = document.getElementById('progressBar');
        const progressPercentage = document.getElementById('progressPercentage');
        const progressCounter = document.getElementById('progressCounter');
        const currentTrackingNumber = document.getElementById('currentTrackingNumber');
        const remainingCount = document.getElementById('remainingCount');
        const progressDetails = document.getElementById('progressDetails');

        // Show progress container
        progressContainer.style.display = 'block';

        // Initialize progress
        progressBar.style.width = '0%';
        progressPercentage.textContent = '0%';
        progressCounter.textContent = `0/${totalCount}`;
        currentTrackingNumber.textContent = 'Preparing...';
        remainingCount.textContent = `Remaining: ${totalCount}`;
        progressDetails.innerHTML = `<small>Starting bulk tracking for ${totalCount} packages...</small>`;

        // Smooth scroll to progress
        progressContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    function updateProgress(completed, total, currentTracking = '', status = '') {
        const progressBar = document.getElementById('progressBar');
        const progressPercentage = document.getElementById('progressPercentage');
        const progressCounter = document.getElementById('progressCounter');
        const currentTrackingNumber = document.getElementById('currentTrackingNumber');
        const remainingCount = document.getElementById('remainingCount');
        const progressDetails = document.getElementById('progressDetails');

        const percentage = Math.round((completed / total) * 100);
        const remaining = total - completed;

        // Update progress bar
        progressBar.style.width = percentage + '%';
        progressPercentage.textContent = percentage + '%';
        progressCounter.textContent = `${completed}/${total}`;
        remainingCount.textContent = `Remaining: ${remaining}`;

        // Update current tracking info
        if (currentTracking) {
            currentTrackingNumber.innerHTML = `
                <div class="fw-bold">Processing:</div>
                <div class="text-warning">${currentTracking}</div>
            `;

            let statusText = 'Processing tracking request...';
            if (status) {
                statusText = `Status: ${status}`;
            }

            progressDetails.innerHTML = `
                <small>
                    <strong>Current:</strong> ${currentTracking}<br>
                    <strong>Progress:</strong> ${completed}/${total} completed (${remaining} remaining)<br>
                    <em>${statusText}</em>
                </small>
            `;
        } else {
            currentTrackingNumber.textContent = completed === total ? 'Completed!' : 'Processing...';
            progressDetails.innerHTML = `
                <small>
                    <strong>Progress:</strong> ${completed}/${total} packages processed
                </small>
            `;
        }
    }

    function hideProgressTracking() {
        const progressContainer = document.getElementById('progressContainer');
        setTimeout(() => {
            progressContainer.style.display = 'none';
        }, 2000);
    }

    function classifyTrackingResult(result) {
        // Enhanced status classification logic
        if (result.status === 'error') {
            return 'error';
        }

        // Check if we have actual tracking data
        if (result.status === 'success' && result.data) {
            const bookingDetails = result.data.booking_details || {};
            const trackSummary = result.data.track_summary || {};

            // Check if we have meaningful data (not just empty/N/A values)
            const hasValidBookingData = Object.values(bookingDetails).some(value =>
                value && value !== 'N/A' && value !== 'Not Available' && value.trim() !== ''
            );

            const hasValidTrackingData = Object.values(trackSummary).some(value =>
                value && value !== 'N/A' && value !== 'Not Available' && value.trim() !== ''
            );

            // Only classify as "found" if we have actual tracking data
            if (hasValidBookingData || hasValidTrackingData) {
                return 'found';
            }
        }

        // If no valid data found, classify as "not_found"
        return 'not_found';
    }

    async function processBulkTrackingWithProgress(trackingNumbers) {
        const results = [];
        const total = trackingNumbers.length;

        try {
            for (let i = 0; i < trackingNumbers.length; i++) {
                const trackingNumber = trackingNumbers[i];

                // Update progress - show current tracking number being processed
                updateProgress(i, total, trackingNumber, 'Fetching data...');

                try {
                    // Make individual API call
                    const response = await fetch(`/track/${trackingNumber}`, {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json',
                        }
                    });

                    const result = await response.json();

                    // Classify the result based on actual data availability
                    const classification = classifyTrackingResult(result);
                    result.classification = classification;

                    results.push(result);

                    // Update progress with status
                    const statusText = classification === 'found' ? 'Data Found' :
                                     classification === 'not_found' ? 'No Data Found' : 'Error';
                    updateProgress(i + 1, total, trackingNumber, statusText);

                    // Small delay to show progress
                    await new Promise(resolve => setTimeout(resolve, 800));

                } catch (error) {
                    console.error(`Error tracking ${trackingNumber}:`, error);
                    const errorResult = {
                        status: 'error',
                        tracking_number: trackingNumber,
                        error: 'Network error occurred',
                        timestamp: new Date().toISOString(),
                        classification: 'error'
                    };
                    results.push(errorResult);

                    updateProgress(i + 1, total, trackingNumber, 'Network Error');
                }
            }

            // Final progress update
            updateProgress(total, total);

            // Calculate enhanced summary based on classification
            const summary = {
                total_processed: results.length,
                found: results.filter(r => r.classification === 'found').length,
                not_found: results.filter(r => r.classification === 'not_found').length,
                errors: results.filter(r => r.classification === 'error').length
            };

            // Legacy compatibility - map to old field names
            summary.successful = summary.found;

            const bulkData = {
                status: 'completed',
                summary: summary,
                results: results,
                timestamp: new Date().toISOString()
            };

            // Hide progress and show results
            hideProgressTracking();
            displayBulkResults(bulkData);

        } catch (error) {
            console.error('Bulk tracking error:', error);
            hideProgressTracking();
            alert('Error occurred during bulk tracking');
        }
    }

});
</script>
{% endblock %}
