{% extends "base.html" %}

{% block title %}Tracking Result - {{ result.tracking_number }}{% endblock %}

{% block content %}
<style>
.result-card {
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: none;
    overflow: hidden;
    transition: all 0.3s ease;
}

.result-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
    border: none;
    padding: 20px;
}

.bg-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
}

.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}

.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
}

.card-body {
    padding: 25px;
}

.btn {
    border-radius: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.result-card {
    animation: fadeInUp 0.6s ease forwards;
}

.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
}

.table thead th {
    background: linear-gradient(135deg, #343a40 0%, #495057 100%);
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table tbody tr:hover {
    background-color: rgba(0,123,255,0.05);
    transform: scale(1.01);
    transition: all 0.2s ease;
}
</style>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-shipping-fast me-2"></i>
                    Tracking Result
                </h2>
                <a href="{{ url_for('tracking.track_form') }}" class="btn btn-outline-primary">
                    <i class="fas fa-search me-2"></i>Track Another Package
                </a>
            </div>

            {% if result.status == 'success' %}
                <!-- Success Result -->
                <div class="row">
                    <!-- Booking Details -->
                    <div class="col-md-6 mb-4">
                        <div class="result-card h-100">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Shipment Booking Details
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-sm-6">
                                        <p><strong>Tracking Number:</strong></p>
                                        <p class="text-primary fs-5">{{ result.data.booking_details.tracking_number }}</p>
                                    </div>
                                    <div class="col-sm-6">
                                        <p><strong>Agent Reference:</strong></p>
                                        <p>{{ result.data.booking_details.agent_reference if result.data.booking_details.agent_reference != 'N/A' else 'Not Available' }}</p>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-6">
                                        <p><strong>Origin:</strong></p>
                                        <p>{{ result.data.booking_details.origin if result.data.booking_details.origin != 'N/A' else 'Not Available' }}</p>
                                    </div>
                                    <div class="col-sm-6">
                                        <p><strong>Destination:</strong></p>
                                        <p>{{ result.data.booking_details.destination if result.data.booking_details.destination != 'N/A' else 'Not Available' }}</p>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <p><strong>Booking Date:</strong></p>
                                        <p>{{ result.data.booking_details.booking_date if result.data.booking_details.booking_date != 'N/A' else 'Not Available' }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Current Status -->
                    <div class="col-md-6 mb-4">
                        <div class="result-card h-100">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-truck me-2"></i>
                                    Current Status
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="text-center mb-3">
                                    <h4 class="text-success">{{ result.data.track_summary.current_status }}</h4>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <p><strong>Delivered On:</strong></p>
                                        <p class="text-success">{{ result.data.track_summary.delivered_on if result.data.track_summary.delivered_on != 'N/A' else 'Not Delivered Yet' }}</p>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <p><strong>Received By:</strong></p>
                                        <p>{{ result.data.track_summary.received_by if result.data.track_summary.received_by != 'N/A' else 'Not Available' }}</p>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            Scraped in: {{ result.scrape_duration }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Status History -->
                {% if result.data.status_history %}
                <div class="row">
                    <div class="col-12">
                        <div class="result-card">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-history me-2"></i>
                                    Status History
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>#</th>
                                                <th>Date & Time</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for item in result.data.status_history %}
                                            <tr>
                                                <td>{{ loop.index }}</td>
                                                <td>{{ item.date_time }}</td>
                                                <td>{{ item.status }}</td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}

            {% elif result.status == 'not_found' %}
                <!-- Not Found Result -->
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="card shadow-sm">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="mb-0">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    Package Not Found
                                </h5>
                            </div>
                            <div class="card-body text-center">
                                <i class="fas fa-search fa-3x text-warning mb-3"></i>
                                <h4>No Record Found</h4>
                                <p class="text-muted">
                                    The tracking number <strong>{{ result.tracking_number }}</strong> was not found in the TCS Express system.
                                </p>
                                <p class="text-muted">
                                    Please verify the tracking number and try again.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

            {% else %}
                <!-- Error Result -->
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="card shadow-sm">
                            <div class="card-header bg-danger text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-exclamation-circle me-2"></i>
                                    Tracking Error
                                </h5>
                            </div>
                            <div class="card-body text-center">
                                <i class="fas fa-times-circle fa-3x text-danger mb-3"></i>
                                <h4>Error Occurred</h4>
                                <p class="text-muted">
                                    An error occurred while tracking <strong>{{ result.tracking_number }}</strong>:
                                </p>
                                <div class="alert alert-danger">
                                    {{ result.error }}
                                </div>
                                <p class="text-muted">
                                    Please try again later or contact support if the problem persists.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Action Buttons -->
            <div class="row mt-4">
                <div class="col-12 text-center">
                    <a href="{{ url_for('tracking.track_form') }}" class="btn btn-primary me-2">
                        <i class="fas fa-search me-2"></i>Track Another Package
                    </a>
                    {% if result.status == 'success' %}
                    <button class="btn btn-outline-danger" onclick="downloadPDF()">
                        <i class="fas fa-file-pdf me-2"></i>Download PDF Report
                    </button>
                    {% endif %}
                </div>
            </div>

            <!-- Metadata -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card bg-light">
                        <div class="card-body">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Scraped at: {{ result.scrape_timestamp or result.timestamp }}
                                {% if result.scrape_duration %}
                                | Duration: {{ result.scrape_duration }}
                                {% endif %}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% if result.status == 'success' %}
<script>
function downloadPDF() {
    const data = {{ result | tojson }};

    // Create a form to submit PDF generation request
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '/api/track/pdf';
    form.target = '_blank';

    const input = document.createElement('input');
    input.type = 'hidden';
    input.name = 'tracking_data';
    input.value = JSON.stringify(data);

    form.appendChild(input);
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
}
</script>
{% endif %}
{% endblock %}
