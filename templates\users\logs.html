{% extends 'base.html' %}

{% block title %}User Activity Logs{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line"></i> User Activity & Session Tracking
                    </h5>
                    <div>
                        <button type="button" class="btn btn-light btn-sm" id="exportCSV">
                            <i class="fas fa-file-csv"></i> Export CSV
                        </button>
                        <button type="button" class="btn btn-light btn-sm" id="exportPDF">
                            <i class="fas fa-file-pdf"></i> Export PDF
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Session Statistics Dashboard -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="row">
                                <div class="col-md-2">
                                    <div class="card bg-info text-white">
                                        <div class="card-body text-center">
                                            <h4>{{ session_stats.total_sessions if session_stats else 0 }}</h4>
                                            <small>Total Sessions</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="card bg-success text-white">
                                        <div class="card-body text-center">
                                            <h4>{{ session_stats.active_sessions if session_stats else 0 }}</h4>
                                            <small>Active Sessions</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="card bg-warning text-white">
                                        <div class="card-body text-center">
                                            <h4>{{ "%.1f"|format((session_stats.total_screen_time if session_stats else 0) / 3600) }}h</h4>
                                            <small>Total Screen Time</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body text-center">
                                            <h4>{{ "%.1f"|format((session_stats.total_active_time if session_stats else 0) / 3600) }}h</h4>
                                            <small>Total Active Time</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="card bg-secondary text-white">
                                        <div class="card-body text-center">
                                            <h4>{{ "%.0f"|format((session_stats.average_session_duration if session_stats else 0) / 60) }}m</h4>
                                            <small>Avg Session</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <a href="{{ url_for('users.manage') }}" class="btn btn-secondary btn-block h-100 d-flex align-items-center justify-content-center">
                                        <i class="fas fa-arrow-left"></i> Back to Users
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tab Navigation -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <ul class="nav nav-tabs" id="activityTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <a class="nav-link active" id="activity-logs-tab" data-toggle="tab" href="#activity-logs" role="tab">
                                        <i class="fas fa-list"></i> Activity Logs
                                    </a>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <a class="nav-link" id="user-sessions-tab" data-toggle="tab" href="#user-sessions" role="tab">
                                        <i class="fas fa-clock"></i> User Sessions
                                    </a>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <a class="nav-link" id="real-time-activity-tab" data-toggle="tab" href="#real-time-activity" role="tab">
                                        <i class="fas fa-chart-line"></i> Real-time Activity
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-3"></div>
                        <div class="col-md-9">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="userFilter">Filter by User</label>
                                        <select class="form-control" id="userFilter">
                                            <option value="">All Users</option>
                                            {% for username in usernames %}
                                            <option value="{{ username }}">{{ username }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="actionFilter">Filter by Action</label>
                                        <select class="form-control" id="actionFilter">
                                            <option value="">All Actions</option>
                                            {% for action in actions %}
                                            <option value="{{ action }}">{{ action }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="dateFilter">Filter by Date</label>
                                        <select class="form-control" id="dateFilter">
                                            <option value="">All Dates</option>
                                            <option value="today">Today</option>
                                            <option value="yesterday">Yesterday</option>
                                            <option value="last7days">Last 7 Days</option>
                                            <option value="last30days">Last 30 Days</option>
                                            <option value="custom">Custom Range</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row custom-date-range" style="display: none;">
                                <div class="col-md-5">
                                    <div class="form-group">
                                        <label for="startDate">Start Date</label>
                                        <input type="date" class="form-control" id="startDate">
                                    </div>
                                </div>
                                <div class="col-md-5">
                                    <div class="form-group">
                                        <label for="endDate">End Date</label>
                                        <input type="date" class="form-control" id="endDate">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label>&nbsp;</label>
                                        <button type="button" class="btn btn-primary form-control" id="applyDateFilter">Apply</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tab Content -->
                    <div class="tab-content" id="activityTabsContent">
                        <!-- Activity Logs Tab -->
                        <div class="tab-pane fade show active" id="activity-logs" role="tabpanel">
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i> Showing <span id="logCount">{{ logs|length }}</span> activity logs.
                                        <button type="button" class="btn btn-sm btn-outline-primary ml-2" id="clearFilters">Clear All Filters</button>
                                    </div>
                                </div>
                            </div>

                            <div class="table-responsive">
                        <table class="table table-striped table-hover" id="logsTable">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Timestamp <i class="fas fa-sort"></i></th>
                                    <th>User <i class="fas fa-sort"></i></th>
                                    <th>Action <i class="fas fa-sort"></i></th>
                                    <th>Details</th>
                                    <th>IP Address</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if logs %}
                                    {% for log in logs %}
                                    <tr class="log-row"
                                        data-user="{{ log.username }}"
                                        data-action="{{ log.action }}"
                                        data-date="{{ safe_strftime(log.timestamp, '%Y-%m-%d') }}">
                                        <td>{{ safe_strftime(log.timestamp, '%Y-%m-%d %H:%M:%S') if log.timestamp else 'Unknown' }}</td>
                                        <td>{{ log.username }}</td>
                                        <td>
                                            <span class="badge badge-{{
                                                'success' if log.action == 'Login' else
                                                'secondary' if log.action == 'Logout' else
                                                'info' if log.action == 'View' else
                                                'primary' if log.action == 'Create' else
                                                'warning' if log.action == 'Update' else
                                                'danger' if log.action == 'Delete' else
                                                'dark'
                                            }}">
                                                {{ log.action }}
                                            </span>
                                        </td>
                                        <td>{{ log.details }}</td>
                                        <td>{{ log.ip_address }}</td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="5" class="text-center">No logs found</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>

                            <!-- Pagination -->
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="pageSize">Items per page:</label>
                                        <select class="form-control form-control-sm d-inline-block w-auto ml-2" id="pageSize">
                                            <option value="10">10</option>
                                            <option value="25" selected>25</option>
                                            <option value="50">50</option>
                                            <option value="100">100</option>
                                            <option value="all">All</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <nav aria-label="Activity log pagination">
                                        <ul class="pagination justify-content-end" id="pagination">
                                            <!-- Pagination will be generated by JavaScript -->
                                        </ul>
                                    </nav>
                                </div>
                            </div>
                        </div>

                        <!-- User Sessions Tab -->
                        <div class="tab-pane fade" id="user-sessions" role="tabpanel">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Session ID</th>
                                            <th>User</th>
                                            <th>Login Time</th>
                                            <th>Last Activity</th>
                                            <th>Duration</th>
                                            <th>Screen Time</th>
                                            <th>Active Time</th>
                                            <th>Status</th>
                                            <th>IP Address</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if user_sessions %}
                                            {% for session in user_sessions %}
                                            <tr>
                                                <td>
                                                    <small class="text-muted">{{ session.session_id[:8] }}...</small>
                                                </td>
                                                <td>
                                                    <strong>{{ session.username }}</strong>
                                                    {% if session.full_name %}
                                                        <br><small class="text-muted">{{ session.full_name }}</small>
                                                    {% endif %}
                                                </td>
                                                <td>{{ safe_strftime(session.login_time, '%Y-%m-%d %H:%M:%S') if session.login_time else 'Unknown' }}</td>
                                                <td>{{ safe_strftime(session.last_activity, '%Y-%m-%d %H:%M:%S') if session.last_activity else 'Unknown' }}</td>
                                                <td>
                                                    {% if session.session_duration %}
                                                        {{ "%.0f"|format(session.session_duration / 60) }} min
                                                    {% else %}
                                                        -
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {% if session.total_screen_time %}
                                                        {{ "%.0f"|format(session.total_screen_time / 60) }} min
                                                    {% else %}
                                                        -
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {% if session.total_active_time %}
                                                        {{ "%.0f"|format(session.total_active_time / 60) }} min
                                                    {% else %}
                                                        -
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    <span class="badge badge-{{
                                                        'success' if session.status == 'active' else
                                                        'secondary' if session.status == 'logged_out' else
                                                        'warning' if session.status == 'expired' else
                                                        'dark'
                                                    }}">
                                                        {{ session.status|title }}
                                                    </span>
                                                </td>
                                                <td>{{ session.ip_address }}</td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td colspan="9" class="text-center">No sessions found</td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Real-time Activity Tab -->
                        <div class="tab-pane fade" id="real-time-activity" role="tabpanel">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Timestamp</th>
                                            <th>User</th>
                                            <th>Activity Type</th>
                                            <th>Page/URL</th>
                                            <th>Duration</th>
                                            <th>Session</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if user_activities %}
                                            {% for activity in user_activities[:100] %}
                                            <tr>
                                                <td>{{ safe_strftime(activity.timestamp, '%Y-%m-%d %H:%M:%S') if activity.timestamp else 'Unknown' }}</td>
                                                <td>
                                                    <strong>{{ activity.username }}</strong>
                                                    {% if activity.full_name %}
                                                        <br><small class="text-muted">{{ activity.full_name }}</small>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    <span class="badge badge-{{
                                                        'success' if activity.activity_type in ['page_view', 'click'] else
                                                        'info' if activity.activity_type in ['scroll', 'typing'] else
                                                        'warning' if activity.activity_type in ['idle_start', 'idle_end'] else
                                                        'secondary' if activity.activity_type == 'heartbeat' else
                                                        'primary'
                                                    }}">
                                                        {{ activity.activity_type|replace('_', ' ')|title }}
                                                    </span>
                                                </td>
                                                <td>
                                                    {% if activity.page_url %}
                                                        <code>{{ activity.page_url }}</code>
                                                    {% else %}
                                                        -
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {% if activity.duration %}
                                                        {{ activity.duration }}s
                                                    {% else %}
                                                        -
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    <small class="text-muted">{{ activity.session_id[:8] if activity.session_id else '-' }}...</small>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td colspan="6" class="text-center">No real-time activities found</td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Variables for pagination
        var currentPage = 1;
        var pageSize = 25;
        var totalPages = Math.ceil({{ logs|length }} / pageSize);

        // Initialize pagination
        updatePagination();
        showPage(currentPage);

        // Date filter change
        $('#dateFilter').change(function() {
            if ($(this).val() === 'custom') {
                $('.custom-date-range').show();
            } else {
                $('.custom-date-range').hide();
                applyFilters();
            }
        });

        // Apply custom date filter
        $('#applyDateFilter').click(function() {
            applyFilters();
        });

        // User and action filters
        $('#userFilter, #actionFilter').change(function() {
            applyFilters();
        });

        // Clear all filters
        $('#clearFilters').click(function() {
            $('#userFilter').val('');
            $('#actionFilter').val('');
            $('#dateFilter').val('');
            $('.custom-date-range').hide();
            $('#startDate').val('');
            $('#endDate').val('');

            $('.log-row').show();
            updateLogCount();
            resetPagination();
        });

        // Page size change
        $('#pageSize').change(function() {
            pageSize = $(this).val() === 'all' ? 1000000 : parseInt($(this).val());
            resetPagination();
        });

        // Export to CSV
        $('#exportCSV').click(function() {
            exportTableToCSV('activity_logs.csv');
        });

        // Export to PDF
        $('#exportPDF').click(function() {
            try {
                console.log("Starting PDF export...");

                // Create a new jsPDF instance
                const { jsPDF } = window.jspdf;
                const doc = new jsPDF();

                // Add title
                doc.setFontSize(18);
                doc.text('Medivent Pharmaceuticals Pvt. Ltd.', 105, 15, { align: 'center' });
                doc.setFontSize(14);
                doc.text('User Activity Logs', 105, 25, { align: 'center' });

                // Add date
                doc.setFontSize(10);
                doc.text('Generated on: ' + new Date().toLocaleString(), 105, 35, { align: 'center' });

                // Get headers
                const headers = [];
                $('#logsTable thead th').each(function() {
                    // Remove sort icons from header text
                    let headerText = $(this).clone().children().remove().end().text().trim();
                    headers.push(headerText);
                });

                console.log("Headers:", headers);

                // Get all filtered rows data
                const bodyData = [];

                // Get all rows that should be visible based on filters
                var filteredRows = $('.log-row').filter(function() {
                    var user = $(this).data('user');
                    var action = $(this).data('action');
                    var date = $(this).data('date');
                    var showRow = true;

                    var userFilter = $('#userFilter').val();
                    var actionFilter = $('#actionFilter').val();
                    var dateFilter = $('#dateFilter').val();

                    // Apply user filter
                    if (userFilter && user !== userFilter) {
                        showRow = false;
                    }

                    // Apply action filter
                    if (actionFilter && action !== actionFilter) {
                        showRow = false;
                    }

                    // Apply date filter
                    if (dateFilter) {
                        var today = new Date();
                        today.setHours(0, 0, 0, 0);

                        var yesterday = new Date();
                        yesterday.setDate(yesterday.getDate() - 1);
                        yesterday.setHours(0, 0, 0, 0);

                        var last7Days = new Date();
                        last7Days.setDate(last7Days.getDate() - 7);
                        last7Days.setHours(0, 0, 0, 0);

                        var last30Days = new Date();
                        last30Days.setDate(last30Days.getDate() - 30);
                        last30Days.setHours(0, 0, 0, 0);

                        var rowDate = new Date(date);

                        if (dateFilter === 'today' && rowDate < today) {
                            showRow = false;
                        } else if (dateFilter === 'yesterday' && (rowDate < yesterday || rowDate >= today)) {
                            showRow = false;
                        } else if (dateFilter === 'last7days' && rowDate < last7Days) {
                            showRow = false;
                        } else if (dateFilter === 'last30days' && rowDate < last30Days) {
                            showRow = false;
                        } else if (dateFilter === 'custom') {
                            var startDate = $('#startDate').val() ? new Date($('#startDate').val()) : null;
                            var endDate = $('#endDate').val() ? new Date($('#endDate').val()) : null;

                            if (startDate && rowDate < startDate) {
                                showRow = false;
                            }

                            if (endDate) {
                                endDate.setDate(endDate.getDate() + 1); // Include the end date
                                if (rowDate >= endDate) {
                                    showRow = false;
                                }
                            }
                        }
                    }

                    return showRow;
                });

                // Extract data from filtered rows
                filteredRows.each(function() {
                    const rowData = [];
                    $(this).find('td').each(function() {
                        // Get text content and clean it
                        let cellText = $(this).text().replace(/[\n\r]+/g, ' ').trim();
                        rowData.push(cellText);
                    });
                    bodyData.push(rowData);
                });

                console.log("Filtered rows count:", filteredRows.length);
                console.log("Body data rows:", bodyData.length);

                // If no filtered rows, get all rows
                if (bodyData.length === 0) {
                    console.log("No filtered rows found, using all rows");
                    $('#logsTable tbody tr').each(function() {
                        const rowData = [];
                        $(this).find('td').each(function() {
                            let cellText = $(this).text().replace(/[\n\r]+/g, ' ').trim();
                            rowData.push(cellText);
                        });
                        bodyData.push(rowData);
                    });
                }

                // Add table to PDF
                doc.autoTable({
                    head: [headers],
                    body: bodyData,
                    startY: 40,
                    theme: 'grid',
                    headStyles: {
                        fillColor: [0, 123, 255],
                        textColor: 255,
                        fontStyle: 'bold'
                    },
                    alternateRowStyles: {
                        fillColor: [240, 240, 240]
                    },
                    margin: { top: 40 }
                });

                console.log("PDF table created with " + bodyData.length + " rows");

                // Save the PDF
                doc.save('activity_logs.pdf');
                console.log("PDF saved successfully");
            } catch (e) {
                console.error("Error generating PDF:", e);
                alert("Error generating PDF: " + e.message);
            }
        });

        // Apply all filters
        function applyFilters() {
            var userFilter = $('#userFilter').val();
            var actionFilter = $('#actionFilter').val();
            var dateFilter = $('#dateFilter').val();

            $('.log-row').hide();

            $('.log-row').each(function() {
                var user = $(this).data('user');
                var action = $(this).data('action');
                var date = $(this).data('date');
                var showRow = true;

                // Apply user filter
                if (userFilter && user !== userFilter) {
                    showRow = false;
                }

                // Apply action filter
                if (actionFilter && action !== actionFilter) {
                    showRow = false;
                }

                // Apply date filter
                if (dateFilter) {
                    var today = new Date();
                    today.setHours(0, 0, 0, 0);

                    var yesterday = new Date();
                    yesterday.setDate(yesterday.getDate() - 1);
                    yesterday.setHours(0, 0, 0, 0);

                    var last7Days = new Date();
                    last7Days.setDate(last7Days.getDate() - 7);
                    last7Days.setHours(0, 0, 0, 0);

                    var last30Days = new Date();
                    last30Days.setDate(last30Days.getDate() - 30);
                    last30Days.setHours(0, 0, 0, 0);

                    var rowDate = new Date(date);

                    if (dateFilter === 'today' && rowDate < today) {
                        showRow = false;
                    } else if (dateFilter === 'yesterday' && (rowDate < yesterday || rowDate >= today)) {
                        showRow = false;
                    } else if (dateFilter === 'last7days' && rowDate < last7Days) {
                        showRow = false;
                    } else if (dateFilter === 'last30days' && rowDate < last30Days) {
                        showRow = false;
                    } else if (dateFilter === 'custom') {
                        var startDate = $('#startDate').val() ? new Date($('#startDate').val()) : null;
                        var endDate = $('#endDate').val() ? new Date($('#endDate').val()) : null;

                        if (startDate && rowDate < startDate) {
                            showRow = false;
                        }

                        if (endDate) {
                            endDate.setDate(endDate.getDate() + 1); // Include the end date
                            if (rowDate >= endDate) {
                                showRow = false;
                            }
                        }
                    }
                }

                if (showRow) {
                    $(this).show();
                }
            });

            updateLogCount();
            resetPagination();
        }

        // Update log count display
        function updateLogCount() {
            var visibleLogs = $('.log-row:visible').length;
            $('#logCount').text(visibleLogs);
        }

        // Reset pagination after filtering
        function resetPagination() {
            currentPage = 1;
            totalPages = Math.ceil($('.log-row:visible').length / pageSize);
            updatePagination();
            showPage(currentPage);
        }

        // Update pagination controls
        function updatePagination() {
            var paginationHtml = '';

            // Previous button
            paginationHtml += '<li class="page-item ' + (currentPage === 1 ? 'disabled' : '') + '">';
            paginationHtml += '<a class="page-link" href="#" data-page="prev">&laquo;</a></li>';

            // Page numbers
            var startPage = Math.max(1, currentPage - 2);
            var endPage = Math.min(totalPages, startPage + 4);

            if (startPage > 1) {
                paginationHtml += '<li class="page-item"><a class="page-link" href="#" data-page="1">1</a></li>';
                if (startPage > 2) {
                    paginationHtml += '<li class="page-item disabled"><a class="page-link" href="#">...</a></li>';
                }
            }

            for (var i = startPage; i <= endPage; i++) {
                paginationHtml += '<li class="page-item ' + (i === currentPage ? 'active' : '') + '">';
                paginationHtml += '<a class="page-link" href="#" data-page="' + i + '">' + i + '</a></li>';
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    paginationHtml += '<li class="page-item disabled"><a class="page-link" href="#">...</a></li>';
                }
                paginationHtml += '<li class="page-item"><a class="page-link" href="#" data-page="' + totalPages + '">' + totalPages + '</a></li>';
            }

            // Next button
            paginationHtml += '<li class="page-item ' + (currentPage === totalPages ? 'disabled' : '') + '">';
            paginationHtml += '<a class="page-link" href="#" data-page="next">&raquo;</a></li>';

            $('#pagination').html(paginationHtml);

            // Add click handlers to pagination links
            $('.page-link').click(function(e) {
                e.preventDefault();
                var page = $(this).data('page');

                if (page === 'prev') {
                    if (currentPage > 1) {
                        currentPage--;
                    }
                } else if (page === 'next') {
                    if (currentPage < totalPages) {
                        currentPage++;
                    }
                } else {
                    currentPage = page;
                }

                updatePagination();
                showPage(currentPage);
            });
        }

        // Show specific page
        function showPage(page) {
            var start = (page - 1) * pageSize;
            var end = start + pageSize;

            // First, make all filtered rows visible but hidden
            $('.log-row').hide();

            // Then show only the rows for the current page
            // Get all rows that should be visible based on filters
            var filteredRows = $('.log-row').filter(function() {
                var user = $(this).data('user');
                var action = $(this).data('action');
                var date = $(this).data('date');
                var showRow = true;

                var userFilter = $('#userFilter').val();
                var actionFilter = $('#actionFilter').val();
                var dateFilter = $('#dateFilter').val();

                // Apply user filter
                if (userFilter && user !== userFilter) {
                    showRow = false;
                }

                // Apply action filter
                if (actionFilter && action !== actionFilter) {
                    showRow = false;
                }

                // Apply date filter
                if (dateFilter) {
                    var today = new Date();
                    today.setHours(0, 0, 0, 0);

                    var yesterday = new Date();
                    yesterday.setDate(yesterday.getDate() - 1);
                    yesterday.setHours(0, 0, 0, 0);

                    var last7Days = new Date();
                    last7Days.setDate(last7Days.getDate() - 7);
                    last7Days.setHours(0, 0, 0, 0);

                    var last30Days = new Date();
                    last30Days.setDate(last30Days.getDate() - 30);
                    last30Days.setHours(0, 0, 0, 0);

                    var rowDate = new Date(date);

                    if (dateFilter === 'today' && rowDate < today) {
                        showRow = false;
                    } else if (dateFilter === 'yesterday' && (rowDate < yesterday || rowDate >= today)) {
                        showRow = false;
                    } else if (dateFilter === 'last7days' && rowDate < last7Days) {
                        showRow = false;
                    } else if (dateFilter === 'last30days' && rowDate < last30Days) {
                        showRow = false;
                    } else if (dateFilter === 'custom') {
                        var startDate = $('#startDate').val() ? new Date($('#startDate').val()) : null;
                        var endDate = $('#endDate').val() ? new Date($('#endDate').val()) : null;

                        if (startDate && rowDate < startDate) {
                            showRow = false;
                        }

                        if (endDate) {
                            endDate.setDate(endDate.getDate() + 1); // Include the end date
                            if (rowDate >= endDate) {
                                showRow = false;
                            }
                        }
                    }
                }

                return showRow;
            });

            // Show only the rows for the current page
            filteredRows.slice(start, end).show();

            // Update log count
            $('#logCount').text(filteredRows.length);
        }

        // Export table to CSV
        function exportTableToCSV(filename) {
            try {
                console.log("Starting CSV export...");
                var csv = [];

                // Get headers
                var headerRow = [];
                $('#logsTable thead th').each(function() {
                    // Remove sort icons from header text
                    var headerText = $(this).clone().children().remove().end().text().trim();
                    headerRow.push('"' + headerText + '"');
                });
                csv.push(headerRow.join(','));

                console.log("Headers:", headerRow);

                // Get all filtered rows data
                // Get all rows that should be visible based on filters
                var filteredRows = $('.log-row').filter(function() {
                    var user = $(this).data('user');
                    var action = $(this).data('action');
                    var date = $(this).data('date');
                    var showRow = true;

                    var userFilter = $('#userFilter').val();
                    var actionFilter = $('#actionFilter').val();
                    var dateFilter = $('#dateFilter').val();

                    // Apply user filter
                    if (userFilter && user !== userFilter) {
                        showRow = false;
                    }

                    // Apply action filter
                    if (actionFilter && action !== actionFilter) {
                        showRow = false;
                    }

                    // Apply date filter
                    if (dateFilter) {
                        var today = new Date();
                        today.setHours(0, 0, 0, 0);

                        var yesterday = new Date();
                        yesterday.setDate(yesterday.getDate() - 1);
                        yesterday.setHours(0, 0, 0, 0);

                        var last7Days = new Date();
                        last7Days.setDate(last7Days.getDate() - 7);
                        last7Days.setHours(0, 0, 0, 0);

                        var last30Days = new Date();
                        last30Days.setDate(last30Days.getDate() - 30);
                        last30Days.setHours(0, 0, 0, 0);

                        var rowDate = new Date(date);

                        if (dateFilter === 'today' && rowDate < today) {
                            showRow = false;
                        } else if (dateFilter === 'yesterday' && (rowDate < yesterday || rowDate >= today)) {
                            showRow = false;
                        } else if (dateFilter === 'last7days' && rowDate < last7Days) {
                            showRow = false;
                        } else if (dateFilter === 'last30days' && rowDate < last30Days) {
                            showRow = false;
                        } else if (dateFilter === 'custom') {
                            var startDate = $('#startDate').val() ? new Date($('#startDate').val()) : null;
                            var endDate = $('#endDate').val() ? new Date($('#endDate').val()) : null;

                            if (startDate && rowDate < startDate) {
                                showRow = false;
                            }

                            if (endDate) {
                                endDate.setDate(endDate.getDate() + 1); // Include the end date
                                if (rowDate >= endDate) {
                                    showRow = false;
                                }
                            }
                        }
                    }

                    return showRow;
                });

                // Extract data from filtered rows
                filteredRows.each(function() {
                    var row = [];
                    $(this).find('td').each(function() {
                        // Get text content and clean it
                        var text = $(this).text().replace(/[\n\r]+/g, ' ').trim();
                        row.push('"' + text + '"');
                    });
                    csv.push(row.join(','));
                });

                console.log("Filtered rows count:", filteredRows.length);

                // If no filtered rows, get all rows
                if (csv.length === 1) {
                    console.log("No filtered rows found, using all rows");
                    $('#logsTable tbody tr').each(function() {
                        var row = [];
                        $(this).find('td').each(function() {
                            var text = $(this).text().replace(/[\n\r]+/g, ' ').trim();
                            row.push('"' + text + '"');
                        });
                        csv.push(row.join(','));
                    });
                }

                // Download CSV file
                downloadCSV(csv.join('\n'), filename);

                console.log("CSV Export completed with " + (csv.length - 1) + " rows");
            } catch (e) {
                console.error("Error exporting CSV:", e);
                alert("Error exporting CSV: " + e.message);
            }
        }

        function downloadCSV(csv, filename) {
            try {
                var csvFile = new Blob([csv], {type: 'text/csv'});
                var downloadLink = document.createElement('a');

                downloadLink.download = filename;
                downloadLink.href = window.URL.createObjectURL(csvFile);
                downloadLink.style.display = 'none';
                document.body.appendChild(downloadLink);

                downloadLink.click();
                document.body.removeChild(downloadLink);
                console.log("CSV download initiated successfully");
            } catch (e) {
                console.error("Error downloading CSV: ", e);
                alert("Error exporting CSV: " + e.message);
            }
        }
    });
</script>
{% endblock %}
