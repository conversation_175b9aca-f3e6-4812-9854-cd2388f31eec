<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Address Label - {{ order.order_id }}</title>
    <style>
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
        
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: white;
        }
        
        .label-container {
            border: 2px solid #000;
            padding: 20px;
            max-width: 400px;
            margin: 0 auto;
            background: white;
        }
        
        .company-header {
            text-align: center;
            border-bottom: 1px solid #ccc;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        
        .company-name {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .company-details {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }
        
        .order-info {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .order-id {
            font-size: 16px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        
        .order-date {
            font-size: 12px;
            color: #666;
        }
        
        .delivery-section {
            margin-bottom: 20px;
        }
        
        .section-title {
            font-size: 14px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
            text-transform: uppercase;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        
        .customer-name {
            font-size: 16px;
            font-weight: bold;
            color: #000;
            margin-bottom: 10px;
        }
        
        .customer-address {
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            margin-bottom: 10px;
        }
        
        .customer-phone {
            font-size: 14px;
            color: #007bff;
            font-weight: bold;
        }
        
        .barcode-section {
            text-align: center;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #ccc;
        }
        
        .barcode-text {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            letter-spacing: 2px;
            background: #f8f9fa;
            padding: 5px 10px;
            border-radius: 3px;
            display: inline-block;
        }
        
        .print-controls {
            text-align: center;
            margin: 20px 0;
        }
        
        .btn {
            padding: 10px 20px;
            margin: 0 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn:hover {
            opacity: 0.8;
        }
        
        .priority-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .priority-high {
            background: #dc3545;
            color: white;
        }
        
        .priority-medium {
            background: #ffc107;
            color: #000;
        }
        
        .priority-normal {
            background: #6c757d;
            color: white;
        }
    </style>
</head>
<body>
    <div class="print-controls no-print">
        <button class="btn btn-primary" onclick="window.print()">
            <i class="fas fa-print"></i> Print Label
        </button>
        <button class="btn btn-secondary" onclick="window.close()">
            <i class="fas fa-times"></i> Close
        </button>
    </div>

    <div class="label-container">
        <!-- Company Header -->
        <div class="company-header">
            <div class="company-name">MEDIVENT PHARMACEUTICALS</div>
            <div class="company-details">
                123 Medical Plaza, Healthcare District<br>
                Karachi, Pakistan<br>
                Phone: +92-21-1234567
            </div>
        </div>

        <!-- Order Information -->
        <div class="order-info">
            <div class="order-id">ORDER: {{ order.order_id }}</div>
            <div class="order-date">{{ order.order_date|format_datetime }}</div>
            {% if order.priority_level > 2 %}
                <span class="priority-badge priority-high">High Priority</span>
            {% elif order.priority_level > 1 %}
                <span class="priority-badge priority-medium">Medium Priority</span>
            {% else %}
                <span class="priority-badge priority-normal">Normal</span>
            {% endif %}
        </div>

        <!-- Delivery Information -->
        <div class="delivery-section">
            <div class="section-title">Deliver To:</div>
            <div class="customer-name">{{ order.customer_name }}</div>
            <div class="customer-address">
                {% if order.customer_address %}
                    {{ order.customer_address }}
                {% else %}
                    <em>Address not provided</em>
                {% endif %}
            </div>
            {% if order.customer_phone %}
            <div class="customer-phone">📞 {{ order.customer_phone }}</div>
            {% endif %}
        </div>

        <!-- Order Value -->
        <div class="delivery-section">
            <div class="section-title">Order Value:</div>
            <div style="font-size: 16px; font-weight: bold; color: #28a745;">
                Rs.{{ "{:,.0f}".format(order.order_amount) }}
            </div>
        </div>

        <!-- Special Instructions -->
        {% if order.special_instructions %}
        <div class="delivery-section">
            <div class="section-title">Special Instructions:</div>
            <div style="font-size: 12px; color: #dc3545; font-style: italic;">
                {{ order.special_instructions }}
            </div>
        </div>
        {% endif %}

        <!-- QR Code Section -->
        <div class="barcode-section">
            <div id="qr-code-container" style="margin-bottom: 10px;">
                <div id="qr-loading" style="color: #666; font-size: 12px;">
                    <i class="fas fa-spinner fa-spin"></i> Generating QR Code...
                </div>
                <div id="qr-display" style="display: none;">
                    <img id="qr-image" style="max-width: 120px; height: auto;" alt="Order QR Code">
                </div>
                <div id="qr-error" style="display: none; color: #dc3545; font-size: 12px;">
                    QR Code unavailable
                </div>
            </div>
            <div class="barcode-text">{{ order.order_id }}</div>
            <div style="font-size: 10px; color: #666; margin-top: 5px;">
                Scan for complete order details
            </div>
        </div>
    </div>

    <script>
        // Load QR code when page loads
        window.onload = function() {
            loadQRCode();
            // Auto-print when page loads (optional)
            // window.print();
        }

        // Close window after printing
        window.onafterprint = function() {
            // Uncomment to auto-close after printing
            // window.close();
        }

        async function loadQRCode() {
            const orderId = '{{ order.order_id }}';
            const qrLoading = document.getElementById('qr-loading');
            const qrDisplay = document.getElementById('qr-display');
            const qrError = document.getElementById('qr-error');
            const qrImage = document.getElementById('qr-image');

            try {
                console.log('Loading QR code for order:', orderId);

                const response = await fetch(`/api/order-qr-code/${orderId}?branding=true`);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const data = await response.json();

                if (data.success && data.qr_code && data.qr_code.base64) {
                    // Show QR code
                    qrImage.src = `data:image/png;base64,${data.qr_code.base64}`;
                    qrLoading.style.display = 'none';
                    qrDisplay.style.display = 'block';
                    console.log('QR code loaded successfully');
                } else {
                    throw new Error(data.message || 'Failed to generate QR code');
                }

            } catch (error) {
                console.error('Error loading QR code:', error);
                qrLoading.style.display = 'none';
                qrError.style.display = 'block';
            }
        }
    </script>
</body>
</html>
