{% extends 'base.html' %}

{% block title %}Warehouse Management{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Warehouse Management</h1>
        <div>
            <a href="#" class="d-none d-sm-inline-block btn btn-sm btn-success shadow-sm" data-toggle="modal" data-target="#addWarehouseModal">
                <i class="fas fa-plus fa-sm text-white-50"></i> Add Warehouse
            </a>
            <a href="{{ url_for('inventory.index') }}" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
                <i class="fas fa-boxes fa-sm text-white-50"></i> View Inventory
            </a>
        </div>
    </div>

    <!-- Warehouse Summary Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Warehouses</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ warehouses|length }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-warehouse fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Active Warehouses</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ warehouses|selectattr('is_active', 'equalto', True)|list|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Warehouses Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Warehouse List</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Warehouse ID</th>
                            <th>Name</th>
                            <th>Location</th>
                            <th>Manager</th>
                            <th>Capacity</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for warehouse in warehouses %}
                        <tr>
                            <td>{{ warehouse.warehouse_id }}</td>
                            <td>{{ warehouse.name }}</td>
                            <td>{{ warehouse.location }}</td>
                            <td>{{ warehouse.manager|default('Not Assigned') }}</td>
                            <td>{{ warehouse.capacity|default('N/A') }}</td>
                            <td>
                                <button class="btn btn-sm btn-{% if warehouse.status == 'active' %}success{% else %}secondary{% endif %}"
                                        onclick="toggleWarehouseStatus('{{ warehouse.warehouse_id }}', '{{ warehouse.name }}', '{{ warehouse.status }}')"
                                        title="Click to toggle status">
                                    {% if warehouse.status == 'active' %}
                                        <i class="fas fa-check-circle"></i> Active
                                    {% else %}
                                        <i class="fas fa-times-circle"></i> Inactive
                                    {% endif %}
                                </button>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('inventory.index') }}?warehouse={{ warehouse.warehouse_id }}" class="btn btn-sm btn-primary" title="View Inventory">
                                        <i class="fas fa-boxes"></i>
                                    </a>
                                    <a href="#" class="btn btn-sm btn-info" onclick="viewWarehouse('{{ warehouse.warehouse_id }}')" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="#" class="btn btn-sm btn-warning" onclick="editWarehouse('{{ warehouse.warehouse_id }}')" title="Edit Warehouse">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button class="btn btn-sm btn-danger" onclick="deleteWarehouse('{{ warehouse.warehouse_id }}', '{{ warehouse.name }}')" title="Delete Warehouse">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="7" class="text-center">No warehouses found</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Warehouse Performance Summary -->
    <div class="row">
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Warehouse Utilization</h6>
                </div>
                <div class="card-body">
                    <canvas id="warehouseUtilizationChart"></canvas>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Activities</h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">Stock Added - Karachi Main</h6>
                                <small>2 hours ago</small>
                            </div>
                            <p class="mb-1">Added 500 units of Paracetamol 500mg</p>
                        </div>
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">Order Dispatched - Lahore Branch</h6>
                                <small>4 hours ago</small>
                            </div>
                            <p class="mb-1">Order ORD00058 dispatched to customer</p>
                        </div>
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">Low Stock Alert - Karachi Main</h6>
                                <small>6 hours ago</small>
                            </div>
                            <p class="mb-1">Amoxicillin 250mg below minimum threshold</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Warehouse Modal -->
<div class="modal fade" id="addWarehouseModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Warehouse</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form method="POST" action="{{ url_for('warehouses.manage_warehouses') }}">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="warehouse_name">Warehouse Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="warehouse_name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="warehouse_location">Location <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="warehouse_location" name="location" required>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="warehouse_address">Street Address</label>
                        <textarea class="form-control" id="warehouse_address" name="address" rows="2" placeholder="Enter full street address"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="warehouse_city">City</label>
                                <input type="text" class="form-control" id="warehouse_city" name="city">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="warehouse_state">State/Province</label>
                                <input type="text" class="form-control" id="warehouse_state" name="state">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="warehouse_postal_code">Postal Code</label>
                                <input type="text" class="form-control" id="warehouse_postal_code" name="postal_code">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="warehouse_country">Country</label>
                        <input type="text" class="form-control" id="warehouse_country" name="country" value="Pakistan">
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="warehouse_manager">Manager</label>
                                <input type="text" class="form-control" id="warehouse_manager" name="manager">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="warehouse_capacity">Capacity (sq ft)</label>
                                <input type="text" class="form-control" id="warehouse_capacity" name="capacity" placeholder="e.g., 10,000 sq ft">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="warehouse_phone">Phone Number</label>
                                <input type="tel" class="form-control" id="warehouse_phone" name="phone" placeholder="+92-XXX-XXXXXXX">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="warehouse_email">Email Address</label>
                                <input type="email" class="form-control" id="warehouse_email" name="email" placeholder="<EMAIL>">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="warehouse_active" name="is_active" checked>
                            <label class="form-check-label" for="warehouse_active">
                                Active Warehouse
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Warehouse</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    $('#dataTable').DataTable({
        "order": [[ 0, "asc" ]],
        "pageLength": 25
    });

    // Warehouse Utilization Chart
    var ctx = document.getElementById('warehouseUtilizationChart').getContext('2d');
    var chart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: [{% for warehouse in warehouses %}'{{ warehouse.name }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                data: [{% for warehouse in warehouses %}{{ loop.index * 20 }}{% if not loop.last %},{% endif %}{% endfor %}],
                backgroundColor: [
                    '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b', '#858796'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});

function viewWarehouse(warehouseId) {
    // Implement view warehouse functionality
    alert('View warehouse: ' + warehouseId);
}

function editWarehouse(warehouseId) {
    // Redirect to edit warehouse page
    window.location.href = `/warehouse/${warehouseId}/edit`;
}

function deleteWarehouse(warehouseId, warehouseName) {
    if (confirm(`Are you sure you want to delete warehouse "${warehouseName}"?\n\nThis action cannot be undone.`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/warehouse/${warehouseId}/delete`;

        // Add CSRF token if available
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = csrfToken.getAttribute('content');
            form.appendChild(csrfInput);
        }

        document.body.appendChild(form);
        form.submit();
    }
}

function toggleWarehouseStatus(warehouseId, warehouseName, currentStatus) {
    const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
    const action = newStatus === 'active' ? 'activate' : 'deactivate';

    if (confirm(`Are you sure you want to ${action} warehouse "${warehouseName}"?`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/warehouse/${warehouseId}/toggle-status`;

        // Add CSRF token if available
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = csrfToken.getAttribute('content');
            form.appendChild(csrfInput);
        }

        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}
