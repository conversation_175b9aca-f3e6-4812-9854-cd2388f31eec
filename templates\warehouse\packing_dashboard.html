{% extends 'base.html' %}

{% block title %}Warehouse Packing Dashboard{% endblock %}

{% block extra_css %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-boxes"></i> Warehouse Packing Dashboard
        </h1>
        <div class="btn-group">
            <a href="/warehouse-management/manage" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Back to Warehouse Management
            </a>
            <button onclick="refreshDashboard()" class="btn btn-primary btn-sm">
                <i class="fas fa-sync-alt"></i> Refresh
            </button>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Pending Packing
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ pending_orders|length }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-box-open fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2" style="cursor: pointer;" onclick="window.location.href='{{ url_for('riders.assignment_dashboard') }}'">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Ready for Dispatch
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ packed_orders|length }}</div>
                            {% if packed_orders %}
                            <small class="text-muted">
                                <i class="fas fa-mouse-pointer"></i> Click to assign riders
                            </small>
                            {% endif %}
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shipping-fast fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                High Priority Orders
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ pending_orders|selectattr('priority_level', 'gt', 1)|list|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Value (Pending)
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                Rs.{{ "{:,.0f}".format(pending_orders|sum(attribute='order_amount') or 0) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Orders for Packing -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 bg-warning text-white">
            <h6 class="m-0 font-weight-bold">
                <i class="fas fa-box-open"></i> Orders Pending Packing ({{ pending_orders|length }})
            </h6>
        </div>
        <div class="card-body">
            {% if pending_orders %}
            <div class="table-responsive">
                <table class="table table-bordered" id="pendingPackingTable">
                    <thead>
                        <tr>
                            <th>Order ID</th>
                            <th>Customer Details</th>
                            <th>Invoice Info</th>
                            <th>Items</th>
                            <th>Priority</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for order in pending_orders %}
                        <tr class="{% if order.priority_level > 1 %}table-warning{% endif %}">
                            <td>
                                <strong class="text-primary">{{ order.order_id }}</strong>
                                <br><small class="text-muted">{{ order.order_date|format_datetime }}</small>
                            </td>
                            <td>
                                <strong>{{ order.customer_name }}</strong>
                                <br><small class="text-muted">{{ order.customer_phone or 'No phone' }}</small>
                                <br><small class="text-success">Rs.{{ "{:,.0f}".format(order.order_amount) }}</small>
                            </td>
                            <td>
                                <strong>{{ order.invoice_number or 'No Invoice' }}</strong>
                                <br><small class="text-muted">{{ order.invoice_date|format_datetime if order.invoice_date else 'N/A' }}</small>
                            </td>
                            <td>
                                <span class="badge badge-info">{{ order.total_items }} items</span>
                                <span class="badge badge-secondary">{{ order.total_quantity }} qty</span>
                            </td>
                            <td>
                                {% if order.priority_level > 2 %}
                                    <span class="badge badge-danger">High</span>
                                {% elif order.priority_level > 1 %}
                                    <span class="badge badge-warning">Medium</span>
                                {% else %}
                                    <span class="badge badge-secondary">Normal</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group-vertical btn-group-sm">
                                    <button class="btn btn-info btn-sm" onclick="viewOrderDetails('{{ order.order_id }}')">
                                        <i class="fas fa-eye"></i> View Details
                                    </button>

                                    <!-- COMPLETELY REBUILT PACK BUTTON SYSTEM -->
                                    <button class="btn btn-secondary btn-sm" onclick="printOrderAddress('{{ order.order_id }}')">
                                        <i class="fas fa-print"></i> Print Address
                                    </button>

                                    <!-- NEW SIMPLE PACK BUTTON - REBUILT FROM SCRATCH -->
                                    <button class="btn btn-success btn-sm"
                                            onclick="openPackModal('{{ order.order_id }}')"
                                            id="pack-btn-{{ order.order_id }}"
                                            data-order="{{ order.order_id }}">
                                        <i class="fas fa-box"></i> Mark Packed
                                    </button>


                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                <h4>No Orders Pending Packing</h4>
                <p class="text-muted">All orders are packed and ready for dispatch!</p>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Packed Orders Ready for Dispatch -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 bg-success text-white">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-shipping-fast"></i> Orders Ready for Dispatch ({{ packed_orders|length }})
                </h6>
                {% if packed_orders %}
                <a href="{{ url_for('riders.assignment_dashboard') }}" class="btn btn-light btn-sm">
                    <i class="fas fa-motorcycle"></i> Assign to Riders
                </a>
                {% endif %}
            </div>
        </div>
        <div class="card-body">
            {% if packed_orders %}
            <div class="table-responsive">
                <table class="table table-bordered" id="packedOrdersTable">
                    <thead>
                        <tr>
                            <th>Order ID</th>
                            <th>Customer</th>
                            <th>Packed Info</th>
                            <th>Items</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for order in packed_orders %}
                        <tr>
                            <td>
                                <strong class="text-primary">{{ order.order_id }}</strong>
                                <br><small class="text-muted">{{ order.order_date|format_datetime }}</small>
                            </td>
                            <td>
                                <strong>{{ order.customer_name }}</strong>
                                <br><small class="text-muted">{{ order.customer_phone or 'No phone' }}</small>
                                <br><small class="text-success">Rs.{{ "{:,.0f}".format(order.order_amount) }}</small>
                            </td>
                            <td>
                                <strong>Packed by: {{ order.packed_by }}</strong>
                                <br><small class="text-muted">{{ order.packed_at|format_datetime }}</small>
                                {% if order.packing_notes %}
                                <br><small class="text-info">{{ order.packing_notes }}</small>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge badge-info">{{ order.total_items }} items</span>
                                <span class="badge badge-secondary">{{ order.total_quantity }} qty</span>
                            </td>
                            <td>
                                <div class="btn-group-vertical btn-group-sm">
                                    <button class="btn btn-info btn-sm" onclick="viewOrderDetails('{{ order.order_id }}')">
                                        <i class="fas fa-eye"></i> View Details
                                    </button>
                                    <button class="btn btn-primary btn-sm" onclick="dispatchOrder('{{ order.order_id }}')">
                                        <i class="fas fa-truck"></i> Dispatch to Rider
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-truck fa-4x text-muted mb-3"></i>
                <h4>No Orders Ready for Dispatch</h4>
                <p class="text-muted">Pack some orders to see them here.</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Order Details Modal - Working Version -->
<div class="modal fade" id="orderDetailsModal" tabindex="-1" role="dialog" aria-labelledby="orderDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header bg-primary text-white">
                <h4 class="modal-title" id="orderDetailsModalLabel">
                    <i class="fas fa-file-alt mr-2"></i>
                    <span id="modalOrderTitle">Order Details</span>
                </h4>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>

            <!-- Modal Body -->
            <div class="modal-body p-0">
                <!-- Loading State -->
                <div id="modalLoadingState" class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                    <p class="mt-3 text-muted">Loading order details...</p>
                </div>

                <!-- Content Container -->
                <div id="modalContent" style="display: none;">
                    <!-- Order Information Section -->
                    <div class="container-fluid p-4">
                        <div class="row">
                            <!-- Order Summary -->
                            <div class="col-md-6">
                                <div class="card border-primary">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0"><i class="fas fa-info-circle mr-2"></i>Order Summary</h5>
                                    </div>
                                    <div class="card-body">
                                        <table class="table table-sm table-borderless">
                                            <tr>
                                                <td><strong>Order ID:</strong></td>
                                                <td id="orderIdDisplay">-</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Customer:</strong></td>
                                                <td id="customerNameDisplay">-</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Phone:</strong></td>
                                                <td id="customerPhoneDisplay">-</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Status:</strong></td>
                                                <td id="orderStatusDisplay">-</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Date:</strong></td>
                                                <td id="orderDateDisplay">-</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Amount:</strong></td>
                                                <td id="orderAmountDisplay">-</td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <!-- Delivery Information -->
                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <h5 class="mb-0"><i class="fas fa-truck mr-2"></i>Delivery Information</h5>
                                    </div>
                                    <div class="card-body">
                                        <table class="table table-sm table-borderless">
                                            <tr>
                                                <td><strong>Address:</strong></td>
                                                <td id="deliveryAddressDisplay">-</td>
                                            </tr>
                                            <tr>
                                                <td><strong>City:</strong></td>
                                                <td id="deliveryCityDisplay">-</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Postal Code:</strong></td>
                                                <td id="deliveryPostalDisplay">-</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Priority:</strong></td>
                                                <td id="orderPriorityDisplay">-</td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Order Items Section -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h5 class="mb-0"><i class="fas fa-list mr-2"></i>Order Items</h5>
                                    </div>
                                    <div class="card-body p-0">
                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover mb-0">
                                                <thead class="thead-light">
                                                    <tr>
                                                        <th>Product</th>
                                                        <th>Quantity</th>
                                                        <th>Unit Price</th>
                                                        <th>Total</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="orderItemsTableBody">
                                                    <!-- Items loaded dynamically -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Summary Section -->
                        <div class="row mt-4">
                            <div class="col-md-8">
                                <div class="card border-warning">
                                    <div class="card-header bg-warning text-dark">
                                        <h5 class="mb-0"><i class="fas fa-calculator mr-2"></i>Order Summary</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4 text-center">
                                                <h6>Total Items</h6>
                                                <h4 class="text-primary" id="totalItemsDisplay">-</h4>
                                            </div>
                                            <div class="col-md-4 text-center">
                                                <h6>Total Quantity</h6>
                                                <h4 class="text-info" id="totalQuantityDisplay">-</h4>
                                            </div>
                                            <div class="col-md-4 text-center">
                                                <h6>Total Amount</h6>
                                                <h4 class="text-success" id="totalAmountDisplay">-</h4>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- QR Code section removed as requested -->
                        </div>
                    </div>
                </div>

                <!-- Error State -->
                <div id="modalErrorState" style="display: none;" class="text-center py-5">
                    <div class="alert alert-danger mx-4">
                        <h5><i class="fas fa-exclamation-triangle mr-2"></i>Error Loading Order Details</h5>
                        <p id="errorMessage">Unable to load order details. Please try again.</p>
                    </div>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times mr-2"></i>Close
                </button>
                <button type="button" class="btn btn-info" id="printAddressBtn" onclick="printOrderAddress()">
                    <i class="fas fa-print mr-2"></i>Print Address
                </button>
                <button type="button" class="btn btn-success" id="viewFullDetailsBtn" onclick="viewFullOrderDetails()">
                    <i class="fas fa-external-link-alt mr-2"></i>View Full Details
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Pack Order Modal -->
<div class="modal fade" id="packOrderModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Pack Order</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="packOrderForm">
                    <input type="hidden" id="packOrderId" name="order_id">
                    <div class="form-group">
                        <label for="packedBy">Packed By:</label>
                        <input type="text" class="form-control" id="packedBy" name="packed_by" value="{{ current_user.username }}" required>
                    </div>
                    <div class="form-group">
                        <label for="packingNotes">Packing Notes:</label>
                        <textarea class="form-control" id="packingNotes" name="packing_notes" rows="3" placeholder="Any special notes about packing..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" onclick="confirmPackOrder()">
                    <i class="fas fa-box"></i> Mark as Packed
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Dispatch Order Modal -->
<div class="modal fade" id="dispatchOrderModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Dispatch Order to Rider Management</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="dispatchOrderForm">
                    <input type="hidden" id="dispatchOrderId" name="order_id">
                    <div class="form-group">
                        <label for="dispatchNotes">Dispatch Notes:</label>
                        <textarea class="form-control" id="dispatchNotes" name="dispatch_notes" rows="3" placeholder="Any special instructions for dispatch..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="confirmDispatchOrder()">
                    <i class="fas fa-truck"></i> Dispatch to Rider
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Order Details Modal included above directly in template -->

{% endblock %}

{% block scripts %}
<!-- Order Details Modal JavaScript -->
<script src="{{ url_for('static', filename='js/order_details_modal.js') }}"></script>

<script>
// Global variables - currentOrderId is declared in order_details_modal.js

$(document).ready(function() {
    // Initialize DataTables
    $('#pendingPackingTable').DataTable({
        "order": [[ 4, "desc" ], [ 0, "asc" ]], // Sort by priority desc, then order ID asc
        "pageLength": 25,
        "responsive": true
    });

    $('#packedOrdersTable').DataTable({
        "order": [[ 2, "desc" ]], // Sort by packed date desc
        "pageLength": 25,
        "responsive": true
    });
});

function refreshDashboard() {
    location.reload();
}

// viewOrderDetails function is defined in order_details_modal.js

// GUARANTEED WORKING FALLBACK FUNCTIONS (Same pattern as viewOrderDetails)
function printOrderAddress(orderId) {
    console.log('🖨️ printOrderAddress called with orderId:', orderId);

    try {
        if (!orderId) {
            console.error('❌ No order ID provided');
            alert('Order ID is required for printing address');
            return;
        }

        // Use the exact same pattern as viewOrderDetails
        const printUrl = `/orders/${orderId}/print-address`;
        console.log('🖨️ Opening print URL:', printUrl);

        // Open address printing in new window
        const newWindow = window.open(printUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');

        if (!newWindow) {
            console.error('❌ Failed to open new window - popup blocked?');
            alert('Failed to open print window. Please check popup blocker settings.');
        } else {
            console.log('✅ Print window opened successfully');
        }

    } catch (error) {
        console.error('❌ Error in printOrderAddress function:', error);
        alert('Error opening print window: ' + error.message);
    }
}

// COMPLETELY REBUILT PACK BUTTON SYSTEM - SIMPLE AND CLEAN
function openPackModal(orderId) {
    console.log('📦 openPackModal called with:', orderId);

    // Simple validation
    if (!orderId) {
        alert('❌ Order ID is required');
        return;
    }

    // Set order ID in hidden input
    document.getElementById('packOrderId').value = orderId;

    // Show modal using Bootstrap
    $('#packOrderModal').modal('show');

    console.log('✅ Pack modal opened for order:', orderId);
}

function loadOrderDetailsFromAPI(orderId) {
    console.log('📡 Loading order details from API for:', orderId);

    // Try primary API endpoint first
    fetch(`/api/order-details/${orderId}`)
        .then(response => {
            console.log('📡 Primary API response status:', response.status);

            if (!response.ok) {
                console.log('📡 Primary API failed, trying fallback...');
                throw new Error(`Primary API failed: HTTP ${response.status}`);
            }

            return response.json();
        })
        .then(data => {
            console.log('📊 Primary API response data:', data);

            if (data.success) {
                populateOrderDetailsFromAPI(data);
                // QR code loading removed as requested
            } else {
                throw new Error(data.message || 'Primary API failed');
            }
        })
        .catch(primaryError => {
            console.log('📡 Primary API failed, trying fallback route...');

            // Try fallback route
            fetch(`/orders/${orderId}/details`)
                .then(response => {
                    console.log('📡 Fallback API response status:', response.status);

                    if (!response.ok) {
                        throw new Error(`Fallback API failed: HTTP ${response.status}`);
                    }

                    return response.json();
                })
                .then(data => {
                    console.log('📊 Fallback API response data:', data);

                    if (data.success) {
                        populateOrderDetailsFromAPI(data);
                        // QR code loading removed as requested
                    } else {
                        throw new Error(data.message || 'Fallback API failed');
                    }
                })
                .catch(fallbackError => {
                    console.error('❌ Both API endpoints failed:', fallbackError);
                    showErrorState('Unable to load order details. Please refresh the page and try again.');
                });
        });
}

function populateOrderDetailsFromAPI(data) {
    console.log('✅ Populating modal with API data...');

    const order = data.order || {};
    const orderItems = data.order_items || [];
    const summary = data.summary || {};

    // Store current order ID for button functions
    currentOrderId = order.order_id;

    // Populate order summary
    $('#customerNameDisplay').text(order.customer_name || 'Unknown Customer');
    $('#customerPhoneDisplay').text(order.customer_phone || '-');
    $('#orderStatusDisplay').html(`<span class="badge badge-warning">${order.status || 'Unknown'}</span>`);
    $('#orderDateDisplay').text(order.order_date || new Date().toLocaleDateString());
    $('#orderAmountDisplay').text(`Rs. ${parseFloat(order.order_amount || 0).toFixed(2)}`);

    // Populate delivery information - handle multiple address field names
    const address = order.delivery_address || order.customer_address || order.shipping_address || 'Address not available';
    const city = order.delivery_city || order.customer_city || 'City not available';
    const postal = order.delivery_postal_code || order.postal_code || 'Postal code not available';

    $('#deliveryAddressDisplay').text(address);
    $('#deliveryCityDisplay').text(city);
    $('#deliveryPostalDisplay').text(postal);

    // Set priority badge
    const priority = order.priority || 'Normal';
    let priorityClass = 'badge-secondary';
    if (priority.toLowerCase() === 'high') priorityClass = 'badge-danger';
    else if (priority.toLowerCase() === 'medium') priorityClass = 'badge-warning';
    $('#orderPriorityDisplay').html(`<span class="badge ${priorityClass}">${priority}</span>`);

    // Populate order items table
    let itemsHtml = '';
    let totalQuantity = 0;
    let totalAmount = 0;

    if (orderItems.length > 0) {
        orderItems.forEach(item => {
            const quantity = parseInt(item.quantity || 0);
            const unitPrice = parseFloat(item.unit_price || 0);
            const itemTotal = quantity * unitPrice;

            totalQuantity += quantity;
            totalAmount += itemTotal;

            itemsHtml += `
                <tr>
                    <td>
                        <strong>${item.product_name || 'Unknown Product'}</strong>
                        ${item.strength ? `<br><small class="text-muted">${item.strength}</small>` : ''}
                        ${item.manufacturer ? `<br><small class="text-muted">by ${item.manufacturer}</small>` : ''}
                    </td>
                    <td>${quantity}</td>
                    <td>Rs. ${unitPrice.toFixed(2)}</td>
                    <td>Rs. ${itemTotal.toFixed(2)}</td>
                </tr>
            `;
        });
    } else {
        itemsHtml = `
            <tr>
                <td colspan="4" class="text-center text-muted">No items found for this order</td>
            </tr>
        `;
    }

    $('#orderItemsTableBody').html(itemsHtml);

    // Update summary
    $('#totalItemsDisplay').text(orderItems.length);
    $('#totalQuantityDisplay').text(totalQuantity);
    $('#totalAmountDisplay').text(`Rs. ${totalAmount.toFixed(2)}`);

    // Hide loading, show content
    $('#modalLoadingState').hide();
    $('#modalContent').show();

    console.log('✅ Modal populated with real API data');
}

// QR Code loading function removed as requested

function showErrorState(errorMessage) {
    console.error('❌ Showing error state:', errorMessage);

    $('#modalLoadingState').hide();
    $('#modalContent').hide();
    $('#modalErrorState').show();
    $('#errorMessage').text(errorMessage);
}

// Modal button functions are defined in order_details_modal.js

// Global variable to store current order ID for button functions (removed duplicate declaration)



/**
 * MULTIPLE BUTTON APPROACHES - 5+ DIFFERENT METHODS
 * Testing various implementations to ensure functionality
 */



// Warehouse Button Manager Class
class WarehouseButtonManager {
    constructor() {
        this.init();
    }

    init() {
        console.log('🏭 Initializing Warehouse Button Manager...');
        this.bindEventListeners();
        this.validateDependencies();
    }

    validateDependencies() {
        // Check jQuery
        if (typeof $ === 'undefined') {
            console.error('❌ jQuery not loaded');
            return false;
        }

        // Check Bootstrap modal
        if (!$.fn.modal) {
            console.error('❌ Bootstrap modal not loaded');
            return false;
        }

        console.log('✅ All dependencies validated');
        return true;
    }

    bindEventListeners() {
        // Print Address Button Handler
        $(document).on('click', '.warehouse-print-btn', (e) => {
            e.preventDefault();
            const button = $(e.currentTarget);
            const orderId = button.data('order-id');
            this.handlePrintAddress(orderId, button);
        });

        // Pack Order Button Handler
        $(document).on('click', '.warehouse-pack-btn', (e) => {
            e.preventDefault();
            const button = $(e.currentTarget);
            const orderId = button.data('order-id');
            this.handlePackOrder(orderId, button);
        });

        console.log('✅ Event listeners bound successfully');
    }

    handlePrintAddress(orderId, button) {
        console.log('🖨️ Print Address requested for order:', orderId);

        try {
            // Validate order ID
            if (!orderId || orderId.trim() === '') {
                throw new Error('Order ID is required');
            }

            // Show loading state
            this.setButtonLoading(button, true);

            // Construct print URL
            const printUrl = `/orders/${orderId}/print-address`;
            console.log('🖨️ Opening print URL:', printUrl);

            // Open in new window
            const printWindow = window.open(
                printUrl,
                '_blank',
                'width=800,height=600,scrollbars=yes,resizable=yes,toolbar=no,menubar=no'
            );

            // Check if window opened successfully
            if (!printWindow || printWindow.closed || typeof printWindow.closed === 'undefined') {
                throw new Error('Failed to open print window. Please check popup blocker settings.');
            }

            // Reset button state after short delay
            setTimeout(() => {
                this.setButtonLoading(button, false);
            }, 1000);

            console.log('✅ Print window opened successfully');

        } catch (error) {
            console.error('❌ Print Address Error:', error);
            this.setButtonLoading(button, false);
            this.showError('Print Address Error', error.message);
        }
    }

    handlePackOrder(orderId, button) {
        console.log('📦 Pack Order requested for order:', orderId);

        try {
            // Validate order ID
            if (!orderId || orderId.trim() === '') {
                throw new Error('Order ID is required');
            }

            // Check if pack modal exists
            const packModal = $('#packOrderModal');
            if (packModal.length === 0) {
                throw new Error('Pack order modal not found. Please refresh the page.');
            }

            // Check if form elements exist
            const orderIdInput = $('#packOrderId');
            if (orderIdInput.length === 0) {
                throw new Error('Pack order form not found. Please refresh the page.');
            }

            // Set order ID in modal
            orderIdInput.val(orderId);
            console.log('✅ Order ID set in pack modal:', orderId);

            // Show modal
            packModal.modal('show');
            console.log('✅ Pack modal displayed');

        } catch (error) {
            console.error('❌ Pack Order Error:', error);
            this.showError('Pack Order Error', error.message);
        }
    }

    setButtonLoading(button, isLoading) {
        if (isLoading) {
            button.prop('disabled', true);
            button.find('i').removeClass().addClass('fas fa-spinner fa-spin');
        } else {
            button.prop('disabled', false);
            const action = button.data('action');
            if (action === 'print-address') {
                button.find('i').removeClass().addClass('fas fa-print');
            } else if (action === 'pack-order') {
                button.find('i').removeClass().addClass('fas fa-box');
            }
        }
    }

    showError(title, message) {
        // Modern error display
        const errorHtml = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <strong>${title}:</strong> ${message}
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            </div>
        `;

        // Show at top of page
        $('body').prepend(errorHtml);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            $('.alert-danger').fadeOut();
        }, 5000);
    }
}

// APPROACH 3: Direct Event Listeners
function initDirectEventListeners() {
    console.log('🔗 APPROACH 3 - Initializing direct event listeners...');

    // Direct print button listeners
    $('.direct-print-btn').off('click').on('click', function(e) {
        e.preventDefault();
        const orderId = $(this).data('order');
        console.log('🖨️ APPROACH 3 - Direct print clicked for:', orderId);

        try {
            const printUrl = `/orders/${orderId}/print-address`;
            const printWindow = window.open(printUrl, '_blank', 'width=800,height=600');

            if (!printWindow) {
                alert('❌ Failed to open print window. Check popup blocker.');
            } else {
                console.log('✅ APPROACH 3 - Print window opened');
            }
        } catch (error) {
            console.error('❌ APPROACH 3 Print Error:', error);
            alert('❌ Error: ' + error.message);
        }
    });

    // Direct pack button listeners
    $('.direct-pack-btn').off('click').on('click', function(e) {
        e.preventDefault();
        const orderId = $(this).data('order');
        console.log('📦 APPROACH 3 - Direct pack clicked for:', orderId);

        try {
            if ($('#packOrderModal').length === 0) {
                alert('❌ Pack modal not found');
                return;
            }

            $('#packOrderId').val(orderId);
            $('#packOrderModal').modal('show');
            console.log('✅ APPROACH 3 - Pack modal opened');

        } catch (error) {
            console.error('❌ APPROACH 3 Pack Error:', error);
            alert('❌ Error: ' + error.message);
        }
    });

    console.log('✅ APPROACH 3 - Direct event listeners initialized');
}

// APPROACH 4: Vanilla JavaScript Event Listeners
function initVanillaEventListeners() {
    console.log('🔗 APPROACH 4 - Initializing vanilla JS event listeners...');

    // Get all buttons
    const printButtons = document.querySelectorAll('.warehouse-print-btn');
    const packButtons = document.querySelectorAll('.warehouse-pack-btn');

    // Add print listeners
    printButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const orderId = this.getAttribute('data-order-id');
            console.log('🖨️ APPROACH 4 - Vanilla print clicked for:', orderId);

            try {
                const printUrl = `/orders/${orderId}/print-address`;
                const printWindow = window.open(printUrl, '_blank', 'width=800,height=600');

                if (!printWindow) {
                    alert('❌ Failed to open print window');
                } else {
                    console.log('✅ APPROACH 4 - Print window opened');
                }
            } catch (error) {
                console.error('❌ APPROACH 4 Print Error:', error);
                alert('❌ Error: ' + error.message);
            }
        });
    });

    // Add pack listeners
    packButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const orderId = this.getAttribute('data-order-id');
            console.log('📦 APPROACH 4 - Vanilla pack clicked for:', orderId);

            try {
                const modal = document.getElementById('packOrderModal');
                const orderInput = document.getElementById('packOrderId');

                if (!modal || !orderInput) {
                    alert('❌ Pack modal elements not found');
                    return;
                }

                orderInput.value = orderId;
                $(modal).modal('show'); // Using jQuery for modal
                console.log('✅ APPROACH 4 - Pack modal opened');

            } catch (error) {
                console.error('❌ APPROACH 4 Pack Error:', error);
                alert('❌ Error: ' + error.message);
            }
        });
    });

    console.log('✅ APPROACH 4 - Vanilla JS listeners initialized');
}

// Initialize all approaches
let warehouseButtonManager;
$(document).ready(function() {
    console.log('🚀 Initializing ALL button approaches...');

    // Approach 2: Class-based system
    warehouseButtonManager = new WarehouseButtonManager();

    // Approach 3: Direct event listeners
    initDirectEventListeners();

    // Approach 4: Vanilla JavaScript
    initVanillaEventListeners();

    console.log('✅ ALL button approaches initialized');
});

// APPROACH 5: Inline Event Handlers
function handlePrintInline(orderId) {
    console.log('🖨️ APPROACH 5 - Inline print handler for:', orderId);

    try {
        const printUrl = `/orders/${orderId}/print-address`;
        const printWindow = window.open(printUrl, '_blank', 'width=800,height=600');

        if (!printWindow) {
            alert('❌ Failed to open print window');
        } else {
            console.log('✅ APPROACH 5 - Print window opened');
        }
    } catch (error) {
        console.error('❌ APPROACH 5 Print Error:', error);
        alert('❌ Error: ' + error.message);
    }
}

function handlePackInline(orderId) {
    console.log('📦 APPROACH 5 - Inline pack handler for:', orderId);

    try {
        if ($('#packOrderModal').length === 0) {
            alert('❌ Pack modal not found');
            return;
        }

        $('#packOrderId').val(orderId);
        $('#packOrderModal').modal('show');
        console.log('✅ APPROACH 5 - Pack modal opened');

    } catch (error) {
        console.error('❌ APPROACH 5 Pack Error:', error);
        alert('❌ Error: ' + error.message);
    }
}

/**
 * Enhanced Pack Order Confirmation Function
 */
function confirmPackOrder() {
    console.log('📦 Confirming pack order...');

    try {
        // Get form element
        const form = document.getElementById('packOrderForm');
        if (!form) {
            throw new Error('Pack order form not found');
        }

        // Validate form data
        const formData = new FormData(form);
        const orderId = formData.get('order_id');
        const packedBy = formData.get('packed_by');

        if (!orderId) {
            throw new Error('Order ID is required');
        }

        if (!packedBy || packedBy.trim() === '') {
            throw new Error('Packed By field is required');
        }

        // Show loading state
        const confirmBtn = $('#packOrderModal .btn-success');
        const originalText = confirmBtn.html();
        confirmBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Packing...');

        console.log('📦 Submitting pack order request for:', orderId);

        // Submit to server
        fetch('/warehouse/pack_order', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            console.log('📦 Pack order response status:', response.status);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return response.json();
        })
        .then(data => {
            console.log('📦 Pack order response data:', data);

            if (data.success) {
                // Success
                $('#packOrderModal').modal('hide');

                // Show success message
                const successHtml = `
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <strong>Success!</strong> Order ${orderId} packed successfully!
                        <button type="button" class="close" data-dismiss="alert">
                            <span>&times;</span>
                        </button>
                    </div>
                `;
                $('body').prepend(successHtml);

                // Reload page after short delay
                setTimeout(() => {
                    location.reload();
                }, 1500);

            } else {
                throw new Error(data.error || 'Unknown error occurred');
            }
        })
        .catch(error => {
            console.error('❌ Pack order error:', error);

            // Show error message
            const errorHtml = `
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <strong>Pack Order Error:</strong> ${error.message}
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                </div>
            `;
            $('body').prepend(errorHtml);
        })
        .finally(() => {
            // Reset button state
            confirmBtn.prop('disabled', false).html(originalText);
        });

    } catch (error) {
        console.error('❌ Pack order validation error:', error);
        alert('❌ ' + error.message);
    }
}

function dispatchOrder(orderId) {
    console.log('🚚 dispatchOrder called with orderId:', orderId);

    if (!orderId) {
        alert('❌ Order ID is required for dispatch');
        return;
    }

    // Check if modal exists
    if ($('#dispatchOrderModal').length === 0) {
        console.error('❌ dispatchOrderModal not found in DOM');
        alert('Dispatch modal not found. Please refresh the page.');
        return;
    }

    // Check if input field exists
    const orderIdInput = document.getElementById('dispatchOrderId');
    if (!orderIdInput) {
        console.error('❌ dispatchOrderId input not found');
        alert('Dispatch order form not found. Please refresh the page.');
        return;
    }

    orderIdInput.value = orderId;
    $('#dispatchOrderModal').modal('show');
}

function confirmDispatchOrder() {
    const formData = new FormData(document.getElementById('dispatchOrderForm'));

    fetch('/warehouse/dispatch-order', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            $('#dispatchOrderModal').modal('hide');
            alert('✅ Order dispatched to rider management!');
            location.reload();
        } else {
            alert('❌ Error dispatching order: ' + data.error);
        }
    })
    .catch(error => {
        alert('❌ Error dispatching order: ' + error);
    });
}

// Global error handler for debugging
window.addEventListener('error', function(e) {
    console.error('🚨 Global JavaScript Error:', e.error);
    console.error('🚨 Error details:', {
        message: e.message,
        filename: e.filename,
        lineno: e.lineno,
        colno: e.colno
    });
});

/**
 * COMPREHENSIVE DEBUGGING AND TESTING SYSTEM
 */

// Debug function to test all button approaches
function debugAllButtonApproaches() {
    console.log('\n🔍 DEBUGGING ALL BUTTON APPROACHES');
    console.log('=' * 50);

    // Test Approach 1 functions
    console.log('\n📋 APPROACH 1 FUNCTIONS:');
    console.log('openPackModal:', typeof openPackModal);
    console.log('confirmPackOrder:', typeof confirmPackOrder);

    // Test Approach 2 elements
    console.log('\n📋 APPROACH 2 ELEMENTS:');
    console.log('warehouse-print-btn count:', $('.warehouse-print-btn').length);
    console.log('warehouse-pack-btn count:', $('.warehouse-pack-btn').length);

    // Test Approach 3 elements
    console.log('\n📋 APPROACH 3 ELEMENTS:');
    console.log('direct-print-btn count:', $('.direct-print-btn').length);
    console.log('direct-pack-btn count:', $('.direct-pack-btn').length);

    // Test Approach 5 functions
    console.log('\n📋 APPROACH 5 FUNCTIONS:');
    console.log('handlePrintInline:', typeof handlePrintInline);
    console.log('handlePackInline:', typeof handlePackInline);

    // Test dependencies
    console.log('\n📋 DEPENDENCIES:');
    console.log('jQuery ($):', typeof $);
    console.log('Bootstrap modal:', typeof $.fn.modal);

    // Test modals
    console.log('\n📋 MODALS:');
    console.log('packOrderModal:', $('#packOrderModal').length > 0);
    console.log('packOrderForm:', $('#packOrderForm').length > 0);
    console.log('packOrderId:', $('#packOrderId').length > 0);

    console.log('\n✅ Debug complete - check results above');
}

// Enhanced testing system
$(document).ready(function() {
    console.log('📋 Warehouse Packing Dashboard loaded');

    // Immediate tests
    console.log('\n🧪 IMMEDIATE TESTS:');

    // Count all button types
    const allButtons = {
        'View Details (working)': $('button[onclick*="viewOrderDetails"]').length,
        'Pack Main (onclick)': $('button[onclick*="openPackModal"]').length,
        'Print v2 (data-driven)': $('.warehouse-print-btn').length,
        'Pack v2 (data-driven)': $('.warehouse-pack-btn').length,
        'Print v3 (direct)': $('.direct-print-btn').length,
        'Pack v3 (direct)': $('.direct-pack-btn').length,
        'Print v5 (inline)': $('button[onmousedown*="handlePrintInline"]').length,
        'Pack v5 (inline)': $('button[onmousedown*="handlePackInline"]').length
    };

    console.log('\n📊 BUTTON COUNTS:');
    for (const [type, count] of Object.entries(allButtons)) {
        console.log(`   ${type}: ${count}`);
    }

    // Test function availability
    const functions = {
        'viewOrderDetails': typeof viewOrderDetails,
        'openPackModal': typeof openPackModal,
        'handlePrintInline': typeof handlePrintInline,
        'handlePackInline': typeof handlePackInline,
        'confirmPackOrder': typeof confirmPackOrder
    };

    console.log('\n🔧 FUNCTION AVAILABILITY:');
    for (const [func, type] of Object.entries(functions)) {
        console.log(`   ${func}: ${type}`);
    }

    // Delayed comprehensive test
    setTimeout(() => {
        console.log('\n⏰ DELAYED COMPREHENSIVE TEST:');
        debugAllButtonApproaches();

        // Test event listeners
        console.log('\n🔗 EVENT LISTENER TESTS:');

        // Test if any button responds to click
        $('.warehouse-print-btn').first().trigger('click');
        console.log('   Triggered warehouse-print-btn click');

        $('.direct-print-btn').first().trigger('click');
        console.log('   Triggered direct-print-btn click');

    }, 2000);

    console.log('✅ Warehouse packing dashboard loaded');
});

// COMPREHENSIVE MODAL TESTING FUNCTION
function testPackModal() {
    console.log('\n🧪 COMPREHENSIVE PACK MODAL TEST');
    console.log('=' * 50);

    try {
        // Test 1: Check if modal exists in DOM
        console.log('\n📋 TEST 1: Modal DOM Presence');
        const modal = document.getElementById('packOrderModal');
        const modalJQuery = $('#packOrderModal');

        console.log('   Modal (vanilla JS):', modal ? 'Found' : 'NOT FOUND');
        console.log('   Modal (jQuery):', modalJQuery.length > 0 ? 'Found' : 'NOT FOUND');

        if (!modal) {
            console.error('❌ CRITICAL: packOrderModal not found in DOM');
            alert('❌ CRITICAL: Pack modal not found in DOM');
            return;
        }

        // Test 2: Check form elements
        console.log('\n📋 TEST 2: Form Elements');
        const form = document.getElementById('packOrderForm');
        const orderIdInput = document.getElementById('packOrderId');
        const packedByInput = document.getElementById('packedBy');
        const notesInput = document.getElementById('packingNotes');

        console.log('   Form:', form ? 'Found' : 'NOT FOUND');
        console.log('   Order ID Input:', orderIdInput ? 'Found' : 'NOT FOUND');
        console.log('   Packed By Input:', packedByInput ? 'Found' : 'NOT FOUND');
        console.log('   Notes Input:', notesInput ? 'Found' : 'NOT FOUND');

        // Test 3: Check jQuery and Bootstrap
        console.log('\n📋 TEST 3: Dependencies');
        console.log('   jQuery ($):', typeof $ !== 'undefined' ? 'Loaded' : 'NOT LOADED');
        console.log('   Bootstrap modal:', typeof $.fn.modal !== 'undefined' ? 'Loaded' : 'NOT LOADED');

        if (typeof $ === 'undefined') {
            console.error('❌ CRITICAL: jQuery not loaded');
            alert('❌ CRITICAL: jQuery not loaded');
            return;
        }

        if (typeof $.fn.modal === 'undefined') {
            console.error('❌ CRITICAL: Bootstrap modal not loaded');
            alert('❌ CRITICAL: Bootstrap modal not loaded');
            return;
        }

        // Test 4: Try to open modal
        console.log('\n📋 TEST 4: Modal Opening Test');

        // Set test order ID
        if (orderIdInput) {
            orderIdInput.value = 'TEST_ORDER_123';
            console.log('   ✅ Order ID set to: TEST_ORDER_123');
        }

        // Try to show modal
        console.log('   Attempting to show modal...');
        modalJQuery.modal('show');

        // Check if modal is visible after a short delay
        setTimeout(() => {
            const isVisible = modalJQuery.hasClass('show') || modalJQuery.is(':visible');
            console.log('   Modal visible after show():', isVisible ? 'YES' : 'NO');

            if (isVisible) {
                console.log('✅ MODAL TEST PASSED - Modal opened successfully');
                alert('✅ MODAL TEST PASSED - Modal opened successfully');

                // Auto-close after 3 seconds
                setTimeout(() => {
                    modalJQuery.modal('hide');
                    console.log('   Modal auto-closed');
                }, 3000);
            } else {
                console.error('❌ MODAL TEST FAILED - Modal did not open');
                alert('❌ MODAL TEST FAILED - Modal did not open');
            }
        }, 500);

    } catch (error) {
        console.error('❌ MODAL TEST ERROR:', error);
        alert('❌ MODAL TEST ERROR: ' + error.message);
    }
}

// Auto-refresh every 3 minutes
setInterval(function() {
    if (!$('.modal').hasClass('show')) {
        location.reload();
    }
}, 180000);
</script>
{% endblock %}
