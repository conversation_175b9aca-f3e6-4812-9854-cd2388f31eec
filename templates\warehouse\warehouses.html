{% extends "base.html" %}

{% block title %}Warehouse Management{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-warehouse"></i> Warehouse Management & DC Generation
                    </h4>
                    <small>Manage warehouses, generate delivery challans, and track inventory</small>
                </div>
            </div>

            <!-- Warehouse Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Total Warehouses</h6>
                                    <h3 class="mb-0">{{ warehouse_summary.total_warehouses }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-building fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Active DCs</h6>
                                    <h3 class="mb-0">{{ warehouse_summary.active_dcs }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-file-alt fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Pending Dispatch</h6>
                                    <h3 class="mb-0">{{ warehouse_summary.pending_dispatch }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Low Stock Items</h6>
                                    <h3 class="mb-0">{{ warehouse_summary.low_stock_items }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0"><i class="fas fa-bolt"></i> Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-2">
                                    <button class="btn btn-primary btn-block" onclick="generateDC()">
                                        <i class="fas fa-file-alt"></i><br>
                                        Generate DC
                                    </button>
                                </div>
                                <div class="col-md-2">
                                    <a href="{{ url_for('inventory.index') }}" class="btn btn-info btn-block">
                                        <i class="fas fa-boxes"></i><br>
                                        View Inventory
                                    </a>
                                </div>
                                <div class="col-md-2">
                                    <a href="{{ url_for('delivery_challans') }}" class="btn btn-warning btn-block">
                                        <i class="fas fa-truck"></i><br>
                                        All DCs
                                    </a>
                                </div>
                                <div class="col-md-2">
                                    <button class="btn btn-success btn-block" onclick="addWarehouse()">
                                        <i class="fas fa-plus"></i><br>
                                        Add Warehouse
                                    </button>
                                </div>
                                <div class="col-md-2">
                                    <a href="{{ url_for('inventory.index') }}?reports=true" class="btn btn-secondary btn-block">
                                        <i class="fas fa-chart-bar"></i><br>
                                        Reports
                                    </a>
                                </div>
                                <div class="col-md-2">
                                    <button class="btn btn-dark btn-block" onclick="stockTransfer()">
                                        <i class="fas fa-exchange-alt"></i><br>
                                        Stock Transfer
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Warehouses List -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-list"></i> Warehouses</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Warehouse ID</th>
                                            <th>Name</th>
                                            <th>Location</th>
                                            <th>Manager</th>
                                            <th>Total Items</th>
                                            <th>Total Value</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for warehouse in warehouses %}
                                        <tr>
                                            <td><strong>{{ warehouse.warehouse_id }}</strong></td>
                                            <td>{{ warehouse.name }}</td>
                                            <td>
                                                <i class="fas fa-map-marker-alt"></i> 
                                                {{ warehouse.location }}
                                            </td>
                                            <td>{{ warehouse.manager_name or 'Not Assigned' }}</td>
                                            <td>
                                                <span class="badge badge-info">
                                                    {{ warehouse.total_items or 0 }}
                                                </span>
                                            </td>
                                            <td>₹{{ warehouse.total_value | format_currency }}</td>
                                            <td>
                                                <span class="badge badge-{% if warehouse.status == 'active' %}success{% else %}danger{% endif %}">
                                                    {{ warehouse.status|title }}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-sm btn-primary" onclick="viewWarehouse('{{ warehouse.warehouse_id }}')">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-warning" onclick="editWarehouse('{{ warehouse.warehouse_id }}')">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-info" onclick="generateWarehouseDC('{{ warehouse.warehouse_id }}')">
                                                        <i class="fas fa-file-alt"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Delivery Challans -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-warning text-white">
                            <h5 class="mb-0"><i class="fas fa-truck"></i> Recent Delivery Challans</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>DC Number</th>
                                            <th>Order ID</th>
                                            <th>Customer</th>
                                            <th>Warehouse</th>
                                            <th>Date</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for dc in recent_dcs %}
                                        <tr>
                                            <td><strong>{{ dc.dc_number }}</strong></td>
                                            <td>{{ dc.order_id }}</td>
                                            <td>{{ dc.customer_name }}</td>
                                            <td>{{ dc.warehouse_name }}</td>
                                            <td>{{ dc.created_date | format_datetime('%Y-%m-%d') }}</td>
                                            <td>
                                                <span class="badge badge-{% if dc.status == 'delivered' %}success{% elif dc.status == 'dispatched' %}info{% elif dc.status == 'ready' %}warning{% else %}secondary{% endif %}">
                                                    {{ dc.status|title }}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-sm btn-primary" onclick="viewDC('{{ dc.dc_number }}')">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-success" onclick="printDC('{{ dc.dc_number }}')">
                                                        <i class="fas fa-print"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-center">
                                <a href="{{ url_for('delivery_challans') }}" class="btn btn-primary">
                                    View All Delivery Challans
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Low Stock Alert -->
            {% if low_stock_items %}
            <div class="row">
                <div class="col-12">
                    <div class="card border-danger">
                        <div class="card-header bg-danger text-white">
                            <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Low Stock Alert</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Product</th>
                                            <th>Warehouse</th>
                                            <th>Current Stock</th>
                                            <th>Reorder Level</th>
                                            <th>Action Required</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in low_stock_items %}
                                        <tr>
                                            <td>{{ item.product_name }}</td>
                                            <td>{{ item.warehouse_name }}</td>
                                            <td>
                                                <span class="badge badge-danger">
                                                    {{ item.current_stock }}
                                                </span>
                                            </td>
                                            <td>{{ item.reorder_level }}</td>
                                            <td>
                                                <button class="btn btn-sm btn-warning" onclick="reorderStock('{{ item.product_id }}', '{{ item.warehouse_id }}')">
                                                    <i class="fas fa-shopping-cart"></i> Reorder
                                                </button>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Modals -->
<!-- Generate DC Modal - Enhanced with Batch Selection -->
<div class="modal fade" id="generateDCModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-boxes"></i> Generate Delivery Challan - Batch Selection
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>New Batch Selection System:</strong>
                    Select an approved order to start the batch selection process for DC generation.
                </div>

                <form id="generateDCForm">
                    <div class="form-group">
                        <label for="dcOrderId">
                            <i class="fas fa-shopping-cart"></i> Select Approved Order:
                        </label>
                        <select id="dcOrderId" class="form-control" required>
                            <option value="">Choose an order...</option>
                            {% for order in approved_orders %}
                            <option value="{{ order.order_id }}"
                                    data-customer="{{ order.customer_name }}"
                                    data-amount="{{ order.order_amount }}"
                                    data-items="{{ order.total_items or 0 }}">
                                {{ order.order_id }} - {{ order.customer_name }}
                                (₹{{ "%.2f"|format(order.order_amount) }})
                                - {{ order.total_items or 0 }} items
                            </option>
                            {% endfor %}
                        </select>
                        <small class="form-text text-muted">
                            Only approved orders are available for DC generation
                        </small>
                    </div>

                    <div id="orderDetails" style="display: none;">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">Order Details:</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>Customer:</strong> <span id="orderCustomer"></span><br>
                                        <strong>Amount:</strong> <span id="orderAmount" class="text-success"></span>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>Items:</strong> <span id="orderItems"></span><br>
                                        <strong>Status:</strong> <span class="badge badge-success">Approved</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mt-3">
                        <label>
                            <i class="fas fa-cogs"></i> Next Steps:
                        </label>
                        <div class="alert alert-light">
                            <ol class="mb-0">
                                <li>Select batch allocation method (FIFO or Manual)</li>
                                <li>Choose specific batches for each product</li>
                                <li>Verify inventory availability across warehouses</li>
                                <li>Generate final delivery challan with batch details</li>
                            </ol>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times"></i> Cancel
                </button>
                <button type="button" class="btn btn-primary" onclick="startBatchSelection()" disabled id="startBatchBtn">
                    <i class="fas fa-boxes"></i> Start Batch Selection
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Generate DC
function generateDC() {
    $('#generateDCModal').modal('show');
}

function generateWarehouseDC(warehouseId) {
    document.getElementById('dcWarehouse').value = warehouseId;
    $('#generateDCModal').modal('show');
}

// Enhanced DC Generation with Batch Selection
function startBatchSelection() {
    const orderId = document.getElementById('dcOrderId').value;

    if (!orderId) {
        alert('Please select an order first');
        return;
    }

    // Redirect to batch selection interface
    window.location.href = `/orders/${orderId}/batch-selection`;
}

// Order selection handler
document.addEventListener('DOMContentLoaded', function() {
    const orderSelect = document.getElementById('dcOrderId');
    const startBatchBtn = document.getElementById('startBatchBtn');
    const orderDetails = document.getElementById('orderDetails');

    if (orderSelect) {
        orderSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];

            if (this.value) {
                // Show order details
                document.getElementById('orderCustomer').textContent = selectedOption.dataset.customer;
                document.getElementById('orderAmount').textContent = '₹' + parseFloat(selectedOption.dataset.amount).toFixed(2);
                document.getElementById('orderItems').textContent = selectedOption.dataset.items;

                orderDetails.style.display = 'block';
                startBatchBtn.disabled = false;
            } else {
                orderDetails.style.display = 'none';
                startBatchBtn.disabled = true;
            }
        });
    }
});

// Legacy function for backward compatibility
function submitGenerateDC() {
    // Redirect to new batch selection system
    startBatchSelection();
    
    document.body.appendChild(form);
    form.submit();
}

// Other functions
function viewWarehouse(warehouseId) {
    window.location.href = `/warehouse/${warehouseId}/view`;
}

function editWarehouse(warehouseId) {
    window.location.href = `/warehouse/${warehouseId}/edit`;
}

function addWarehouse() {
    window.location.href = '/warehouse/add';
}

function viewDC(dcNumber) {
    window.location.href = `/warehouse/dc/${dcNumber}/view`;
}

function printDC(dcNumber) {
    window.open(`/warehouse/dc/${dcNumber}/print`, '_blank');
}

function stockTransfer() {
    window.location.href = '/warehouse/stock-transfer';
}

function reorderStock(productId, warehouseId) {
    if (confirm('Create reorder request for this product?')) {
        window.location.href = `/warehouse/reorder/${productId}/${warehouseId}`;
    }
}

// Auto-refresh every 2 minutes
setInterval(function() {
    location.reload();
}, 120000);
</script>
{% endblock %}
