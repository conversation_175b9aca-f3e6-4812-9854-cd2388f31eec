{% extends 'base.html' %}

{% block title %}Warehouse - Delivery Challan Generation{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Warehouse - Delivery Challan Generation</h1>
        <div class="btn-group">
            <a href="{{ url_for('warehouse_packing_dashboard') }}" class="btn btn-success btn-sm shadow-sm">
                <i class="fas fa-boxes fa-sm text-white-50"></i> Packing Dashboard
            </a>
            <a href="{{ url_for('warehouse_orders') }}" class="btn btn-info btn-sm shadow-sm">
                <i class="fas fa-list fa-sm text-white-50"></i> Orders Queue
            </a>
            <a href="{{ url_for('dashboard') }}" class="btn btn-primary btn-sm shadow-sm">
                <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Pending DC Generation</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ approved_orders|selectattr('dc_status', 'equalto', 'Pending DC')|list|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">DC Generated</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ approved_orders|selectattr('dc_status', 'equalto', 'DC Generated')|list|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Approved Orders</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ approved_orders|length }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Active Warehouses</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ warehouses|length }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-warehouse fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Approved Orders Waiting for DC Generation -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 bg-warning text-white">
            <h6 class="m-0 font-weight-bold">📋 Orders Approved by Boss - Waiting for Delivery Challan Generation</h6>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>Warehouse Instructions:</strong> These orders have been approved by the boss and are now waiting for you to generate delivery challans. Click "Generate DC" to proceed with batch selection and challan generation.
            </div>

            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="approvedOrdersTable" width="100%" cellspacing="0">
                    <thead class="thead-dark">
                        <tr>
                            <th>Order ID</th>
                            <th>Customer</th>
                            <th>Order Date</th>
                            <th>Approval Date</th>
                            <th>Amount</th>
                            <th>Payment Method</th>
                            <th>DC Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for order in approved_orders %}
                        <tr class="{% if order.dc_status == 'Pending DC' %}table-warning{% else %}table-success{% endif %}">
                            <td><strong>{{ order.order_id }}</strong></td>
                            <td>{{ order.customer_name }}</td>
                            <td>{{ order.order_date }}</td>
                            <td>{{ order.approval_date if order.approval_date else 'N/A' }}</td>
                            <td>₨{{ "{:,.2f}".format(order.order_amount) }}</td>
                            <td>
                                <span class="badge badge-{% if order.payment_method == 'cash' %}success{% elif order.payment_method == 'cheque' %}warning{% else %}info{% endif %}">
                                    {{ order.payment_method|title }}
                                </span>
                            </td>
                            <td>
                                {% if order.dc_status == 'Pending DC' %}
                                <span class="badge badge-warning">
                                    <i class="fas fa-clock"></i> Pending DC
                                </span>
                                {% else %}
                                <span class="badge badge-success">
                                    <i class="fas fa-check"></i> DC Generated
                                </span>
                                {% endif %}
                            </td>
                            <td>
                                {% if order.dc_status == 'Pending DC' %}
                                <a href="{{ url_for('dc_generation.batch_selection', order_id=order.order_id) }}"
                                   class="btn btn-sm btn-warning">
                                    <i class="fas fa-boxes"></i> Generate DC
                                </a>
                                {% else %}
                                <a href="{{ url_for('orders.view_challan', order_id=order.order_id) }}"
                                   class="btn btn-sm btn-success">
                                    <i class="fas fa-eye"></i> View DC
                                </a>
                                {% endif %}
                                <a href="{{ url_for('view_order_history', order_id=order.order_id) }}"
                                   class="btn btn-sm btn-info ml-1">
                                    <i class="fas fa-history"></i> History
                                </a>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="8" class="text-center">
                                <div class="py-4">
                                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">No approved orders waiting for DC generation</h5>
                                    <p class="text-muted">Orders will appear here after boss approval</p>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-2">
                    <a href="{{ url_for('inventory.index') }}" class="btn btn-primary btn-block">
                        <i class="fas fa-boxes"></i> View Inventory
                    </a>
                </div>
                <div class="col-md-2">
                    <a href="{{ url_for('workflow') }}" class="btn btn-info btn-block">
                        <i class="fas fa-tasks"></i> Order Workflow
                    </a>
                </div>
                <div class="col-md-2">
                    <a href="{{ url_for('dc_pending') }}" class="btn btn-warning btn-block">
                        <i class="fas fa-clock"></i> DC Pending
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="{{ url_for('reports') }}" class="btn btn-success btn-block">
                        <i class="fas fa-chart-bar"></i> Reports
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="{{ url_for('delivery_challans') }}" class="btn btn-secondary btn-block">
                        <i class="fas fa-file-alt"></i> All DCs
                    </a>
                </div>
            </div>
        </div>
    </div>

</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // Initialize DataTable for approved orders
    $('#approvedOrdersTable').DataTable({
        "order": [[ 3, "asc" ]], // Sort by approval date ascending (oldest first)
        "pageLength": 25,
        "columnDefs": [
            {
                "targets": [7], // Actions column
                "orderable": false
            }
        ]
    });

    // Auto-refresh every 30 seconds to check for new approved orders
    setInterval(function() {
        // Only refresh if we're still on the warehouse management page
        if (window.location.pathname === '/warehouse-management/manage') {
            location.reload();
        }
    }, 30000);

    // Add click handlers for Generate DC buttons
    $('.btn-warning').on('click', function(e) {
        var orderId = $(this).closest('tr').find('td:first strong').text();
        if (confirm('Generate Delivery Challan for Order ' + orderId + '?')) {
            // Continue with the link
            return true;
        } else {
            e.preventDefault();
            return false;
        }
    });
});

// Function to show notification when new orders arrive
function showNewOrderNotification(count) {
    if (count > 0) {
        // Create a toast notification
        var toast = $('<div class="toast" role="alert" aria-live="assertive" aria-atomic="true" style="position: fixed; top: 20px; right: 20px; z-index: 9999;">' +
            '<div class="toast-header bg-warning text-white">' +
                '<strong class="mr-auto">New Orders</strong>' +
                '<button type="button" class="ml-2 mb-1 close" data-dismiss="toast" aria-label="Close">' +
                    '<span aria-hidden="true">&times;</span>' +
                '</button>' +
            '</div>' +
            '<div class="toast-body">' +
                count + ' new order(s) approved and waiting for DC generation!' +
            '</div>' +
        '</div>');

        $('body').append(toast);
        toast.toast({delay: 5000}).toast('show');

        // Remove toast after it's hidden
        toast.on('hidden.bs.toast', function() {
            $(this).remove();
        });
    }
}
</script>
{% endblock %}
