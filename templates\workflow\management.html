{% extends 'base.html' %}

{% block title %}Workflow Management - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card bg-gradient-dark text-white">
                <div class="card-body">
                    <h3 class="mb-0">
                        <i class="fas fa-project-diagram"></i> Workflow Management
                    </h3>
                    <p class="mb-0">Monitor and manage order workflow across all stages</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Workflow Status Cards -->
    <div class="row mb-4">
        {% for status in statuses %}
        <div class="col-md-2">
            <div class="card 
                {% if status == 'Placed' %}bg-warning
                {% elif status == 'Approved' %}bg-primary
                {% elif status == 'Processing' %}bg-info
                {% elif status == 'Ready for Pickup' %}bg-secondary
                {% elif status == 'Dispatched' %}bg-dark
                {% elif status == 'Delivered' %}bg-success
                {% endif %} text-white">
                <div class="card-body text-center">
                    <h4 class="mb-0">{{ orders_by_status[status]|length }}</h4>
                    <p class="mb-0 small">{{ status }}</p>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Workflow Tabs -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" id="workflowTabs" role="tablist">
                        {% for status in statuses %}
                        <li class="nav-item">
                            <a class="nav-link {% if loop.first %}active{% endif %}" 
                               id="{{ status.lower().replace(' ', '-') }}-tab" 
                               data-toggle="tab" 
                               href="#{{ status.lower().replace(' ', '-') }}" 
                               role="tab">
                                {{ status }} ({{ orders_by_status[status]|length }})
                            </a>
                        </li>
                        {% endfor %}
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="workflowTabContent">
                        {% for status in statuses %}
                        <div class="tab-pane fade {% if loop.first %}show active{% endif %}" 
                             id="{{ status.lower().replace(' ', '-') }}" 
                             role="tabpanel">
                             
                            {% if orders_by_status[status] %}
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="thead-light">
                                        <tr>
                                            <th>Order ID</th>
                                            <th>Customer</th>
                                            <th>Date</th>
                                            <th>Amount</th>
                                            <th>Sales Agent</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for order in orders_by_status[status] %}
                                        <tr>
                                            <td>
                                                <strong>{{ order.order_id }}</strong>
                                                {% if order.invoice_number %}
                                                <br><small class="text-muted">{{ order.invoice_number }}</small>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {{ order.customer_name }}
                                                {% if order.customer_phone %}
                                                <br><small class="text-muted">{{ order.customer_phone }}</small>
                                                {% endif %}
                                            </td>
                                            <td>{{ order.order_date|format_datetime }}</td>
                                            <td>{{ order.order_amount|format_currency }}</td>
                                            <td>{{ order.sales_agent or 'N/A' }}</td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="{{ url_for('orders.view_order', order_id=order.order_id) }}" 
                                                       class="btn btn-sm btn-info">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    {% if status == 'Placed' %}
                                                    <a href="{{ url_for('order_approval', order_id=order.order_id) }}" 
                                                       class="btn btn-sm btn-warning">
                                                        <i class="fas fa-gavel"></i>
                                                    </a>
                                                    {% elif status == 'Approved' %}
                                                    <a href="{{ url_for('dc_generate', order_id=order.order_id) }}" 
                                                       class="btn btn-sm btn-success">
                                                        <i class="fas fa-truck"></i>
                                                    </a>
                                                    {% endif %}
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No {{ status }} Orders</h5>
                                <p class="text-muted">No orders in {{ status.lower() }} status at the moment.</p>
                            </div>
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0"><i class="fas fa-bolt"></i> Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2">
                            <a href="{{ url_for('orders.new_order') }}" class="btn btn-success btn-block">
                                <i class="fas fa-plus"></i> New Order
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{{ url_for('orders.index') }}" class="btn btn-primary btn-block">
                                <i class="fas fa-list"></i> All Orders
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{{ url_for('dc_pending') }}" class="btn btn-warning btn-block">
                                <i class="fas fa-truck"></i> DC Pending
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{{ url_for('warehouses.manage_warehouses') }}" class="btn btn-info btn-block">
                                <i class="fas fa-warehouse"></i> Warehouse
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{{ url_for('reports') }}" class="btn btn-secondary btn-block">
                                <i class="fas fa-chart-bar"></i> Reports
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{{ url_for('dashboard') }}" class="btn btn-outline-dark btn-block">
                                <i class="fas fa-home"></i> Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize all tables with DataTable
    $('.table').each(function() {
        $(this).DataTable({
            responsive: true,
            pageLength: 10,
            order: [[2, "desc"]], // Sort by date
            columnDefs: [
                { orderable: false, targets: [5] } // Disable sorting for actions column
            ]
        });
    });
    
    // Auto-refresh every 60 seconds
    setInterval(function() {
        location.reload();
    }, 60000);
});
</script>
{% endblock %}
