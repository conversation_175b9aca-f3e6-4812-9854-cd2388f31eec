import sqlite3
from datetime import datetime

# Test the activity logging function
def test_log_activity():
    conn = sqlite3.connect('instance/medivent.db')
    cursor = conn.cursor()
    
    # Check activity_logs table structure
    print('=== ACTIVITY_LOGS TABLE STRUCTURE ===')
    cursor.execute('PRAGMA table_info(activity_logs)')
    columns = cursor.fetchall()
    for i, col in enumerate(columns):
        print(f'   Column {i}: {col[1]} ({col[2]})')
    
    # Test inserting an activity log
    print('\n=== TESTING ACTIVITY LOG INSERT ===')
    try:
        cursor.execute('''
            INSERT INTO activity_logs (username, action, entity_id, details, module, timestamp)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', ('test_user', 'TEST_ACTION', 'ORD175397491316416F32', 'Test activity log', 'orders', datetime.now().isoformat()))
        conn.commit()
        print('✅ Activity log inserted successfully')
        
        # Check if it was inserted
        cursor.execute('SELECT * FROM activity_logs WHERE entity_id = ? ORDER BY timestamp DESC LIMIT 1', ('ORD175397491316416F32',))
        log = cursor.fetchone()
        if log:
            print(f'✅ Activity log found: {log}')
        else:
            print('❌ Activity log not found after insert')
            
    except Exception as e:
        print(f'❌ Error inserting activity log: {e}')
    
    # Check all activity logs for this order
    print('\n=== ALL ACTIVITY LOGS FOR ORDER ===')
    cursor.execute('SELECT * FROM activity_logs WHERE entity_id = ?', ('ORD175397491316416F32',))
    logs = cursor.fetchall()
    print(f'Found {len(logs)} activity logs:')
    for log in logs:
        print(f'   {log}')
    
    conn.close()

if __name__ == '__main__':
    test_log_activity()
