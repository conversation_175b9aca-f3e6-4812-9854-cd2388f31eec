#!/usr/bin/env python3
"""
Test only the actual rider routes that exist
"""

import requests
import time

def test_actual_rider_routes():
    """Test only the routes that actually exist"""
    base_url = "http://127.0.0.1:5000"
    
    print("🧪 TESTING ACTUAL RIDER ROUTES")
    print("=" * 60)
    
    # Routes that actually exist based on the codebase analysis
    actual_routes = [
        # Blueprint routes (from routes/modern_riders.py)
        ("/riders/", "Riders Main Page (Blueprint)"),
        ("/riders/dashboard", "Riders Dashboard (Blueprint)"),
        ("/riders/tracking", "Live Tracking (Blueprint)"),
        ("/riders/performance", "Performance Analytics (Blueprint)"),
        ("/riders/analytics", "Advanced Analytics (Blueprint)"),
        ("/riders/reports", "Reports (Blueprint)"),
        ("/riders/assignment-dashboard", "Assignment Dashboard (Blueprint)"),
        ("/riders/self-pickup", "Self Pickup (Blueprint)"),
        
        # App.py routes
        ("/riders/orders", "Rider Orders (App)"),
        ("/riders/register", "Register New Rider (App)"),
        ("/riders/export", "Export Riders (App)"),
        ("/riders/delivery-routes", "Delivery Routes (App)"),
        ("/riders/bulk_approve", "Bulk Approve (App)"),
    ]
    
    print(f"🎯 Testing {len(actual_routes)} actual rider routes...")
    print("-" * 60)
    
    passed = 0
    failed = 0
    
    for route, description in actual_routes:
        try:
            response = requests.get(f"{base_url}{route}", timeout=10)
            status = response.status_code
            
            if status == 200:
                print(f"✅ {route:<35} | {description:<35} | HTTP {status}")
                passed += 1
            elif status == 302:
                print(f"🔄 {route:<35} | {description:<35} | HTTP {status} (Redirect)")
                passed += 1  # Redirects are often expected (login required)
            elif status == 404:
                print(f"❌ {route:<35} | {description:<35} | HTTP {status} (Not Found)")
                failed += 1
            elif status == 405:
                print(f"⚠️ {route:<35} | {description:<35} | HTTP {status} (Method Not Allowed)")
                # This might be expected for POST-only routes
                passed += 1
            else:
                print(f"⚠️ {route:<35} | {description:<35} | HTTP {status}")
                failed += 1
                
        except requests.exceptions.ConnectionError:
            print(f"❌ {route:<35} | {description:<35} | Connection Error")
            failed += 1
        except requests.exceptions.Timeout:
            print(f"❌ {route:<35} | {description:<35} | Timeout")
            failed += 1
        except Exception as e:
            print(f"❌ {route:<35} | {description:<35} | Error: {e}")
            failed += 1
    
    print("-" * 60)
    print(f"📊 RESULTS: {passed} passed, {failed} failed")
    
    return passed, failed

def test_main_navigation():
    """Test main navigation pages for BuildError"""
    base_url = "http://127.0.0.1:5000"
    
    print("\n🧭 TESTING MAIN NAVIGATION")
    print("=" * 60)
    
    main_pages = [
        ("/", "Home Page"),
        ("/dashboard", "Main Dashboard"),
    ]
    
    all_good = True
    
    for route, description in main_pages:
        try:
            response = requests.get(f"{base_url}{route}", timeout=10)
            
            if response.status_code == 200:
                content = response.text.lower()
                
                # Check for BuildError
                if "builderror" in content:
                    print(f"❌ {description:<20} | BuildError found!")
                    all_good = False
                    
                    # Try to extract the specific error
                    if "could not build url for endpoint" in content:
                        lines = response.text.split('\n')
                        for line in lines:
                            if "could not build url for endpoint" in line.lower():
                                print(f"   Error: {line.strip()}")
                                break
                else:
                    print(f"✅ {description:<20} | No BuildError")
            else:
                print(f"⚠️ {description:<20} | HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ {description:<20} | Exception: {e}")
            all_good = False
    
    return all_good

def test_rider_navigation_links():
    """Test that rider navigation links work"""
    base_url = "http://127.0.0.1:5000"
    
    print("\n🔗 TESTING RIDER NAVIGATION LINKS")
    print("=" * 60)
    
    try:
        # Get the main page and check for rider links
        response = requests.get(f"{base_url}/", timeout=10)
        
        if response.status_code == 200:
            content = response.text
            
            # Check for rider-related navigation elements
            rider_nav_elements = [
                "riders.dashboard",
                "register_rider", 
                "rider_delivery_routes",
                "rider_performance",
                "riders.reports"
            ]
            
            found_elements = []
            for element in rider_nav_elements:
                if element in content:
                    found_elements.append(element)
            
            print(f"✅ Found {len(found_elements)} rider navigation elements:")
            for element in found_elements:
                print(f"   - {element}")
            
            if len(found_elements) >= 3:
                print("✅ Rider navigation appears to be working")
                return True
            else:
                print("⚠️ Limited rider navigation found")
                return False
                
        else:
            print(f"❌ Cannot access main page (HTTP {response.status_code})")
            return False
            
    except Exception as e:
        print(f"❌ Error testing navigation: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 TESTING ACTUAL RIDER SYSTEM")
    print("=" * 60)
    
    # Wait for server
    print("⏳ Waiting for server to be ready...")
    time.sleep(2)
    
    # Run tests
    passed_routes, failed_routes = test_actual_rider_routes()
    nav_ok = test_main_navigation()
    links_ok = test_rider_navigation_links()
    
    # Final summary
    print("\n🏁 FINAL TEST SUMMARY")
    print("=" * 60)
    print(f"📊 Route Tests: {passed_routes} passed, {failed_routes} failed")
    print(f"🧭 Main Navigation: {'✅ PASS' if nav_ok else '❌ FAIL'}")
    print(f"🔗 Rider Links: {'✅ PASS' if links_ok else '❌ FAIL'}")
    
    overall_success = (failed_routes == 0) and nav_ok and links_ok
    
    if overall_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Rider routing system is working perfectly!")
        print("✅ No BuildError exceptions found!")
        print("✅ All navigation links are functional!")
    else:
        print("\n⚠️ SOME ISSUES FOUND")
        if failed_routes > 0:
            print(f"❌ {failed_routes} routes failed")
        if not nav_ok:
            print("❌ Navigation issues found")
        if not links_ok:
            print("❌ Rider link issues found")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
