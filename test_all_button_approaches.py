#!/usr/bin/env python3
"""
Comprehensive test script for ALL 5+ button approaches
Tests each method individually and provides detailed analysis
"""

import requests
import time
from datetime import datetime

def test_page_loading():
    """Test if the page loads with all button approaches"""
    print("\n📄 TESTING PAGE LOADING WITH ALL APPROACHES")
    print("=" * 60)
    
    try:
        response = requests.get('http://127.0.0.1:5001/warehouse/packing', timeout=15)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text
            
            # Test for all 5 approaches
            approaches = {
                "Approach 1 (onclick)": [
                    'onclick="printAddressV1(',
                    'onclick="packOrderV1(',
                    'function printAddressV1(',
                    'function packOrderV1('
                ],
                "Approach 2 (data-driven)": [
                    'warehouse-print-btn',
                    'warehouse-pack-btn',
                    'WarehouseButtonManager',
                    'data-order-id'
                ],
                "Approach 3 (direct listeners)": [
                    'direct-print-btn',
                    'direct-pack-btn',
                    'initDirectEventListeners',
                    'data-order='
                ],
                "Approach 4 (vanilla JS)": [
                    'initVanillaEventListeners',
                    'querySelectorAll',
                    'addEventListener'
                ],
                "Approach 5 (inline handlers)": [
                    'onmousedown="handlePrintInline(',
                    'onmousedown="handlePackInline(',
                    'function handlePrintInline(',
                    'function handlePackInline('
                ]
            }
            
            print("\n🔍 CHECKING ALL APPROACHES:")
            for approach, patterns in approaches.items():
                print(f"\n   {approach}:")
                all_found = True
                for pattern in patterns:
                    if pattern in content:
                        print(f"      ✅ {pattern}")
                    else:
                        print(f"      ❌ {pattern} - MISSING")
                        all_found = False
                
                if all_found:
                    print(f"      🎉 {approach} - COMPLETE")
                else:
                    print(f"      ⚠️ {approach} - INCOMPLETE")
            
            # Check for JavaScript errors
            print("\n🔍 CHECKING FOR POTENTIAL ISSUES:")
            
            # Check for duplicate declarations
            if content.count('let currentOrderId') > 1:
                print("      ❌ Multiple currentOrderId declarations found")
            else:
                print("      ✅ No duplicate currentOrderId declarations")
            
            # Check for jQuery
            if 'jquery' in content.lower() or '$(' in content:
                print("      ✅ jQuery usage detected")
            else:
                print("      ❌ jQuery not detected")
            
            # Check for Bootstrap
            if 'bootstrap' in content.lower() or '.modal(' in content:
                print("      ✅ Bootstrap modal usage detected")
            else:
                print("      ❌ Bootstrap modal not detected")
            
            return True
            
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_print_routes():
    """Test print address routes"""
    print("\n🖨️ TESTING PRINT ADDRESS ROUTES")
    print("=" * 60)
    
    test_orders = ['ORD00000155', 'ORD00000157', 'ORD00000150']
    
    for order_id in test_orders:
        try:
            url = f'http://127.0.0.1:5001/orders/{order_id}/print-address'
            print(f"\n   🧪 Testing: {order_id}")
            print(f"      URL: {url}")
            
            response = requests.get(url, timeout=10)
            print(f"      Status: {response.status_code}")
            
            if response.status_code == 200:
                print("      ✅ Print route accessible")
            elif response.status_code == 404:
                print("      ⚠️ Order not found (may be expected)")
            else:
                print(f"      ❌ Unexpected status: {response.status_code}")
                
        except Exception as e:
            print(f"      ❌ Error: {e}")

def test_pack_order_route():
    """Test pack order POST route"""
    print("\n📦 TESTING PACK ORDER ROUTE")
    print("=" * 60)
    
    try:
        url = 'http://127.0.0.1:5001/warehouse/pack_order'
        print(f"   Testing: {url}")
        
        # Test with GET (should return 405 Method Not Allowed)
        response = requests.get(url, timeout=10)
        print(f"   GET Status: {response.status_code}")
        
        if response.status_code == 405:
            print("   ✅ Pack order route exists (405 expected for GET)")
        else:
            print(f"   ❌ Unexpected status: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")

def test_modal_elements():
    """Test if modal elements exist in the page"""
    print("\n🪟 TESTING MODAL ELEMENTS")
    print("=" * 60)
    
    try:
        response = requests.get('http://127.0.0.1:5001/warehouse/packing', timeout=15)
        
        if response.status_code == 200:
            content = response.text
            
            modal_elements = {
                'Pack Order Modal': 'id="packOrderModal"',
                'Pack Order Form': 'id="packOrderForm"',
                'Pack Order ID Input': 'id="packOrderId"',
                'Dispatch Order Modal': 'id="dispatchOrderModal"'
            }
            
            for element, pattern in modal_elements.items():
                if pattern in content:
                    print(f"   ✅ {element} found")
                else:
                    print(f"   ❌ {element} missing")
        else:
            print(f"   ❌ Failed to load page: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")

def create_browser_test_instructions():
    """Create detailed browser testing instructions"""
    print("\n🌐 BROWSER TESTING INSTRUCTIONS")
    print("=" * 60)
    
    instructions = """
    1. Open browser to: http://127.0.0.1:5001/warehouse/packing
    
    2. Open browser console (F12 → Console tab)
    
    3. Look for initialization messages:
       - "🏭 Initializing Warehouse Button Manager..."
       - "🔗 APPROACH 3 - Initializing direct event listeners..."
       - "🔗 APPROACH 4 - Initializing vanilla JS event listeners..."
       - "🚀 Initializing ALL button approaches..."
       - "✅ ALL button approaches initialized"
    
    4. Test each button approach for ANY order:
    
       APPROACH 1 (Traditional onclick):
       - Click "Print Address (v1)" button
       - Click "Mark Packed (v1)" button
       - Should see console: "🖨️ APPROACH 1 - printAddressV1 called with: ORD..."
       
       APPROACH 2 (Data-driven):
       - Click "Print (v2)" button
       - Click "Pack (v2)" button
       - Should see console: "🖨️ Print Address requested for order: ORD..."
       
       APPROACH 3 (Direct listeners):
       - Click "Print (v3)" button
       - Click "Pack (v3)" button
       - Should see console: "🖨️ APPROACH 3 - Direct print clicked for: ORD..."
       
       APPROACH 4 (Vanilla JS):
       - Same buttons as Approach 2, but different console messages
       - Should see console: "🖨️ APPROACH 4 - Vanilla print clicked for: ORD..."
       
       APPROACH 5 (Inline handlers):
       - Click "Print (v5)" button
       - Click "Pack (v5)" button
       - Should see console: "🖨️ APPROACH 5 - Inline print handler for: ORD..."
    
    5. Verify functionality:
       - Print buttons should open new window
       - Pack buttons should open modal
       - No JavaScript errors in console
       - All approaches should work independently
    
    6. If ANY approach works, the system is functional!
    """
    
    print(instructions)

def main():
    """Main testing function"""
    print("🚀 COMPREHENSIVE BUTTON APPROACHES TEST")
    print("=" * 80)
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Wait for server
    print("\n⏳ Waiting for server...")
    time.sleep(2)
    
    # Run all tests
    page_ok = test_page_loading()
    
    if page_ok:
        test_print_routes()
        test_pack_order_route()
        test_modal_elements()
    
    create_browser_test_instructions()
    
    print("\n" + "="*80)
    print("🏁 COMPREHENSIVE TESTING COMPLETED")
    print(f"⏰ Test finished at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    print("\n📋 SUMMARY:")
    print("✅ 5 different button approaches implemented")
    print("✅ Traditional onclick (proven working)")
    print("✅ Data-driven jQuery approach")
    print("✅ Direct event listeners")
    print("✅ Vanilla JavaScript")
    print("✅ Inline event handlers")
    print("\n🎯 At least ONE approach should work!")

if __name__ == "__main__":
    main()
