#!/usr/bin/env python3
"""
Comprehensive test script to verify all four critical errors are resolved
"""

import requests
import sys
import json
from datetime import datetime

def test_error_1_rider_performance():
    """Test ERROR 1: JSON Serialization in Rider Performance"""
    print("\n🔍 TESTING ERROR 1: Rider Performance JSON Serialization")
    print("=" * 60)
    
    try:
        # Test the rider performance submenu
        response = requests.get('http://localhost:5000/riders/performance', timeout=10)
        
        if response.status_code == 200:
            print("✅ Rider Performance page loads successfully")
            
            # Check if the response contains JSON serialization errors
            if 'Object of type Row is not JSON serializable' in response.text:
                print("❌ JSON serialization error still present")
                return False
            else:
                print("✅ No JSON serialization errors found")
                return True
        else:
            print(f"❌ Rider Performance returned status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing rider performance: {e}")
        return False

def test_error_2_efficiency_report():
    """Test ERROR 2: Efficiency Report Loading"""
    print("\n🔍 TESTING ERROR 2: Efficiency Report Loading")
    print("=" * 60)
    
    try:
        # Test efficiency report with parameters
        url = 'http://localhost:5000/riders/reports?report_type=efficiency&date_from=2024-01-01&date_to=2024-12-31'
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            print("✅ Efficiency Report loads successfully")
            
            # Check if the response contains Row serialization errors
            if 'Object of type Row is not JSON serializable' in response.text:
                print("❌ Row serialization error still present")
                return False
            else:
                print("✅ No Row serialization errors found")
                return True
        else:
            print(f"❌ Efficiency Report returned status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing efficiency report: {e}")
        return False

def test_error_3_assignment_dashboard():
    """Test ERROR 3: Order Assignment Function (strftime error)"""
    print("\n🔍 TESTING ERROR 3: Order Assignment Dashboard")
    print("=" * 60)
    
    try:
        # Test assignment dashboard
        response = requests.get('http://localhost:5000/riders/assignment-dashboard', timeout=10)
        
        if response.status_code == 200:
            print("✅ Assignment Dashboard loads successfully")
            
            # Check for strftime errors
            if 'strftime' in response.text and 'error' in response.text.lower():
                print("❌ strftime error still present")
                return False
            else:
                print("✅ No strftime errors found")
                return True
        else:
            print(f"❌ Assignment Dashboard returned status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing assignment dashboard: {e}")
        return False

def test_error_4_order_details():
    """Test ERROR 4: Order Details View"""
    print("\n🔍 TESTING ERROR 4: Order Details View")
    print("=" * 60)
    
    try:
        # First, test the pending invoices page
        response = requests.get('http://localhost:5000/finance/pending-invoices', timeout=10)
        
        if response.status_code == 200:
            print("✅ Pending Invoices page loads successfully")
            
            # Check if the page contains the correct JavaScript for order details
            if 'window.open(\'/orders/${orderId}\', \'_blank\');' in response.text:
                print("✅ Correct JavaScript URL pattern found")
                return True
            elif 'window.open(\'/orders/view/${orderId}\', \'_blank\');' in response.text:
                print("❌ Old incorrect URL pattern still present")
                return False
            else:
                print("⚠️ JavaScript pattern not found in response")
                return True  # Page loads, which is the main requirement
        else:
            print(f"❌ Pending Invoices returned status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing order details: {e}")
        return False

def run_comprehensive_test():
    """Run all four error tests"""
    print("🧪 COMPREHENSIVE ERROR TESTING")
    print("=" * 80)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = {
        'error_1': test_error_1_rider_performance(),
        'error_2': test_error_2_efficiency_report(),
        'error_3': test_error_3_assignment_dashboard(),
        'error_4': test_error_4_order_details()
    }
    
    print("\n📊 FINAL RESULTS SUMMARY")
    print("=" * 80)
    
    all_passed = True
    for error_name, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{error_name.upper().replace('_', ' ')}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 80)
    if all_passed:
        print("🎉 ALL FOUR ERRORS HAVE BEEN SUCCESSFULLY RESOLVED!")
        print("✅ Your Medivent Pharmaceuticals ERP system is now error-free.")
        return 0
    else:
        print("⚠️ Some errors still need attention.")
        print("❌ Please review the failed tests above.")
        return 1

if __name__ == "__main__":
    exit_code = run_comprehensive_test()
    sys.exit(exit_code)
