#!/usr/bin/env python3
"""
Comprehensive test of all rider routes to ensure they return HTTP 200
"""

import requests
import time

def test_rider_routes():
    """Test all rider-related routes"""
    
    base_url = "http://localhost:5000"
    
    # List of all rider routes to test
    rider_routes = [
        "/riders/",                           # Main riders page
        "/riders/dashboard",                  # Professional dashboard
        "/riders/assignment-dashboard",       # Assignment dashboard
        "/riders/reports",                    # Reports page
        "/riders/register",                   # Register rider
        "/riders/performance",                # Performance page
        "/riders/delivery-routes",            # Delivery routes
        "/riders/self-pickup-dashboard",      # Self pickup dashboard
    ]
    
    print("🔍 Testing all rider routes...")
    print("=" * 50)
    
    success_count = 0
    total_routes = len(rider_routes)
    
    for route in rider_routes:
        try:
            response = requests.get(f"{base_url}{route}", timeout=10)
            status = response.status_code
            
            if status == 200:
                print(f"✅ {route:<35} → {status}")
                success_count += 1
            elif status == 302:
                print(f"🔄 {route:<35} → {status} (Redirect)")
                success_count += 1  # Redirects are often expected for auth
            else:
                print(f"❌ {route:<35} → {status}")
                
        except Exception as e:
            print(f"💥 {route:<35} → ERROR: {str(e)[:50]}")
    
    print("=" * 50)
    print(f"📊 RESULTS: {success_count}/{total_routes} routes working")
    
    if success_count == total_routes:
        print("🎉 ALL RIDER ROUTES ARE WORKING PERFECTLY!")
        return True
    else:
        print(f"⚠️  {total_routes - success_count} routes need attention")
        return False

def test_specific_assignment_routes():
    """Test assignment-specific functionality"""
    
    base_url = "http://localhost:5000"
    
    assignment_routes = [
        "/riders/assignment-dashboard",
        "/riders/claim_pickup",
        "/riders/manual-assignment",
    ]
    
    print("\n🎯 Testing Assignment-Specific Routes...")
    print("=" * 50)
    
    for route in assignment_routes:
        try:
            response = requests.get(f"{base_url}{route}", timeout=10)
            status = response.status_code
            
            if status in [200, 302]:
                print(f"✅ {route:<35} → {status}")
            else:
                print(f"❌ {route:<35} → {status}")
                
        except Exception as e:
            print(f"💥 {route:<35} → ERROR: {str(e)[:50]}")

if __name__ == "__main__":
    print("🚀 Starting comprehensive rider route testing...")
    time.sleep(1)  # Give server time
    
    # Test main rider routes
    main_success = test_rider_routes()
    
    # Test assignment routes
    test_specific_assignment_routes()
    
    print("\n" + "=" * 50)
    if main_success:
        print("🎉 SUCCESS: All rider routes are accessible!")
        print("🔗 Assignment Dashboard should now be visible in sidebar!")
    else:
        print("⚠️  Some routes need investigation")
