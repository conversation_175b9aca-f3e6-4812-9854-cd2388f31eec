#!/usr/bin/env python3
"""
Comprehensive route testing for the Medivent ERP application
"""

import requests
import time
import json

def test_route(url, description, expected_status=200):
    """Test a single route and return results"""
    try:
        response = requests.get(url, timeout=10)
        status = response.status_code
        success = status == expected_status
        
        result = {
            'url': url,
            'description': description,
            'status': status,
            'success': success,
            'response_length': len(response.text),
            'error': None
        }
        
        if success:
            print(f"✅ {description}: HTTP {status}")
        else:
            print(f"❌ {description}: HTTP {status} (expected {expected_status})")
            
        return result
        
    except requests.exceptions.ConnectionError:
        print(f"❌ {description}: Connection refused")
        return {
            'url': url,
            'description': description,
            'status': 'Connection Error',
            'success': False,
            'response_length': 0,
            'error': 'Connection refused'
        }
    except requests.exceptions.Timeout:
        print(f"⏰ {description}: Timeout")
        return {
            'url': url,
            'description': description,
            'status': 'Timeout',
            'success': False,
            'response_length': 0,
            'error': 'Timeout'
        }
    except Exception as e:
        print(f"❌ {description}: {str(e)}")
        return {
            'url': url,
            'description': description,
            'status': 'Error',
            'success': False,
            'response_length': 0,
            'error': str(e)
        }

def test_all_routes():
    """Test all major routes in the application"""
    print("=== COMPREHENSIVE ROUTE TESTING ===")
    print("Testing Medivent ERP Application Routes")
    print()
    
    base_url = "http://127.0.0.1:5000"
    
    # Define routes to test
    routes_to_test = [
        # Core Application Routes
        (f"{base_url}/", "Main Dashboard"),
        (f"{base_url}/login", "Login Page"),
        
        # Warehouse Routes (The ones we fixed)
        (f"{base_url}/warehouse/packing", "Warehouse Packing Dashboard"),
        (f"{base_url}/warehouse/orders", "Warehouse Orders"),
        
        # Inventory Routes
        (f"{base_url}/inventory", "Inventory Management"),
        (f"{base_url}/products", "Products Page"),
        
        # Order Routes
        (f"{base_url}/orders", "Orders Page"),
        (f"{base_url}/orders/new", "New Order Page"),
        
        # User Routes
        (f"{base_url}/users", "Users Management"),
        
        # Reports Routes
        (f"{base_url}/reports", "Reports Dashboard"),
        
        # Analytics Routes
        (f"{base_url}/analytics", "Analytics Dashboard"),
        
        # Division Routes
        (f"{base_url}/divisions", "Divisions Management"),
        
        # Warehouse Management
        (f"{base_url}/warehouses", "Warehouses Management"),
        
        # Delivery Routes
        (f"{base_url}/delivery", "Delivery Management"),
        
        # Static Files
        (f"{base_url}/static/css/sb-admin-2.min.css", "CSS File"),
        (f"{base_url}/static/js/sb-admin-2.min.js", "JavaScript File"),
    ]
    
    results = []
    successful_tests = 0
    total_tests = len(routes_to_test)
    
    print(f"🧪 Testing {total_tests} routes...")
    print()
    
    for url, description in routes_to_test:
        result = test_route(url, description)
        results.append(result)
        if result['success']:
            successful_tests += 1
        time.sleep(0.5)  # Small delay between requests
    
    # Summary
    print("\n" + "="*60)
    print("📊 TEST SUMMARY")
    print("="*60)
    print(f"Total Routes Tested: {total_tests}")
    print(f"Successful: {successful_tests}")
    print(f"Failed: {total_tests - successful_tests}")
    print(f"Success Rate: {(successful_tests/total_tests)*100:.1f}%")
    
    # Detailed results
    print("\n📋 DETAILED RESULTS:")
    for result in results:
        status_icon = "✅" if result['success'] else "❌"
        print(f"{status_icon} {result['description']}: {result['status']}")
        if result['error']:
            print(f"   Error: {result['error']}")
    
    # Critical route check
    critical_routes = [
        "Main Dashboard",
        "Warehouse Packing Dashboard", 
        "Warehouse Orders",
        "Inventory Management",
        "Products Page"
    ]
    
    print(f"\n🎯 CRITICAL ROUTES STATUS:")
    critical_success = 0
    for result in results:
        if result['description'] in critical_routes:
            status_icon = "✅" if result['success'] else "❌"
            print(f"{status_icon} {result['description']}")
            if result['success']:
                critical_success += 1
    
    print(f"\nCritical Routes Success: {critical_success}/{len(critical_routes)}")
    
    # Check if the packed_at fix worked
    warehouse_routes_working = any(
        result['success'] and result['description'] in ["Warehouse Packing Dashboard", "Warehouse Orders"]
        for result in results
    )
    
    if warehouse_routes_working:
        print("\n🎉 PACKED_AT FIX VERIFICATION:")
        print("✅ Warehouse routes are working!")
        print("✅ packed_at column error has been resolved!")
    else:
        print("\n❌ PACKED_AT FIX VERIFICATION:")
        print("❌ Warehouse routes still failing")
        print("❌ packed_at column error may not be fully resolved")
    
    return results, successful_tests, total_tests

if __name__ == "__main__":
    print("Waiting 5 seconds for application to start...")
    time.sleep(5)
    
    results, successful, total = test_all_routes()
    
    if successful >= total * 0.8:  # 80% success rate
        print(f"\n🎉 TESTING COMPLETED SUCCESSFULLY!")
        print(f"Application is working well with {successful}/{total} routes functional")
    else:
        print(f"\n⚠️ TESTING COMPLETED WITH ISSUES")
        print(f"Only {successful}/{total} routes are working")
        print("Some routes may need additional fixes")
