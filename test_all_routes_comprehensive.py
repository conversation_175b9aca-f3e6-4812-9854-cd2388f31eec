#!/usr/bin/env python3
"""
Comprehensive route testing to ensure all routes return HTTP 200
"""

import requests
import sys
from datetime import datetime

def test_route(url, route_name, timeout=10):
    """Test a single route"""
    try:
        response = requests.get(url, timeout=timeout)
        if response.status_code == 200:
            print(f"✅ {route_name}: HTTP 200")
            return True
        else:
            print(f"❌ {route_name}: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ {route_name}: ERROR - {e}")
        return False

def test_critical_routes():
    """Test all critical routes"""
    print("🧪 TESTING CRITICAL ROUTES")
    print("=" * 60)
    
    base_url = "http://localhost:5000"
    
    routes = [
        # Main dashboard routes
        (f"{base_url}/", "Main Dashboard"),
        (f"{base_url}/dashboard", "Dashboard"),
        
        # Rider routes
        (f"{base_url}/riders/", "Riders Dashboard"),
        (f"{base_url}/riders/dashboard", "Riders Dashboard Alt"),
        (f"{base_url}/riders/assignment-dashboard", "Assignment Dashboard"),
        (f"{base_url}/riders/performance", "Rider Performance"),
        (f"{base_url}/riders/analytics", "Rider Analytics"),
        (f"{base_url}/riders/tracking", "Rider Tracking"),
        (f"{base_url}/riders/reports", "Rider Reports"),
        
        # Finance routes
        (f"{base_url}/finance/dashboard", "Finance Dashboard"),
        (f"{base_url}/finance/pending-invoices", "Pending Invoices"),
        (f"{base_url}/finance/invoice-generation", "Invoice Generation"),
        (f"{base_url}/finance/payment-collection", "Payment Collection"),
        
        # Orders routes
        (f"{base_url}/orders/", "Orders Dashboard"),
        (f"{base_url}/orders/new", "New Order"),
        (f"{base_url}/orders/pending", "Pending Orders"),
        
        # Inventory routes
        (f"{base_url}/inventory/", "Inventory Dashboard"),
        (f"{base_url}/inventory/products", "Products"),
        (f"{base_url}/inventory/warehouses", "Warehouses"),
        
        # User routes
        (f"{base_url}/users/", "Users Dashboard"),
        (f"{base_url}/users/profile", "User Profile"),
    ]
    
    passed = 0
    total = len(routes)
    
    for url, name in routes:
        if test_route(url, name):
            passed += 1
    
    print(f"\n📊 ROUTE TEST RESULTS: {passed}/{total} passed")
    return passed == total

def test_assignment_form_specifically():
    """Test assignment form functionality specifically"""
    print("\n🧪 TESTING ASSIGNMENT FORM FUNCTIONALITY")
    print("=" * 60)
    
    base_url = "http://localhost:5000"
    
    try:
        # Test assignment dashboard
        response = requests.get(f"{base_url}/riders/assignment-dashboard", timeout=15)
        
        if response.status_code == 200:
            print("✅ Assignment Dashboard loads successfully")
            
            # Check for specific error messages
            error_checks = [
                ("'str' object has no attribute 'strftime'", "strftime error"),
                ("Error loading assignment form", "assignment form error"),
                ("AttributeError", "attribute error"),
                ("TypeError", "type error"),
                ("ValueError", "value error")
            ]
            
            errors_found = []
            for error_text, error_name in error_checks:
                if error_text in response.text:
                    errors_found.append(error_name)
            
            if errors_found:
                print(f"❌ Errors found: {', '.join(errors_found)}")
                return False
            else:
                print("✅ No errors found in assignment form")
                
                # Check for expected content
                expected_content = [
                    "Orders Ready for Rider Assignment",
                    "Available Riders",
                    "Assign",
                    "Auto"
                ]
                
                content_found = []
                for content in expected_content:
                    if content in response.text:
                        content_found.append(content)
                
                print(f"✅ Expected content found: {len(content_found)}/{len(expected_content)}")
                return len(content_found) == len(expected_content)
        else:
            print(f"❌ Assignment Dashboard returned HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing assignment form: {e}")
        return False

def test_datetime_handling():
    """Test datetime handling across the application"""
    print("\n🧪 TESTING DATETIME HANDLING")
    print("=" * 60)
    
    base_url = "http://localhost:5000"
    
    # Routes that use datetime formatting
    datetime_routes = [
        (f"{base_url}/riders/assignment-dashboard", "Assignment Dashboard"),
        (f"{base_url}/riders/performance", "Rider Performance"),
        (f"{base_url}/riders/reports", "Rider Reports"),
        (f"{base_url}/orders/", "Orders Dashboard"),
        (f"{base_url}/finance/pending-invoices", "Pending Invoices"),
    ]
    
    passed = 0
    total = len(datetime_routes)
    
    for url, name in datetime_routes:
        try:
            response = requests.get(url, timeout=15)
            if response.status_code == 200:
                # Check for datetime-related errors
                datetime_errors = [
                    "strftime",
                    "AttributeError",
                    "TypeError: strptime",
                    "ValueError: time data"
                ]
                
                has_error = False
                for error in datetime_errors:
                    if error in response.text:
                        print(f"❌ {name}: Datetime error found - {error}")
                        has_error = True
                        break
                
                if not has_error:
                    print(f"✅ {name}: No datetime errors")
                    passed += 1
                    
            else:
                print(f"❌ {name}: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ {name}: ERROR - {e}")
    
    print(f"\n📊 DATETIME TEST RESULTS: {passed}/{total} passed")
    return passed == total

def main():
    """Main test function"""
    print("🔧 COMPREHENSIVE ROUTE AND DATETIME TESTING")
    print("=" * 80)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test all critical routes
    routes_test = test_critical_routes()
    
    # Test assignment form specifically
    assignment_test = test_assignment_form_specifically()
    
    # Test datetime handling
    datetime_test = test_datetime_handling()
    
    print("\n📊 FINAL COMPREHENSIVE RESULTS")
    print("=" * 50)
    print(f"Critical Routes Test: {'✅ PASSED' if routes_test else '❌ FAILED'}")
    print(f"Assignment Form Test: {'✅ PASSED' if assignment_test else '❌ FAILED'}")
    print(f"Datetime Handling Test: {'✅ PASSED' if datetime_test else '❌ FAILED'}")
    
    if routes_test and assignment_test and datetime_test:
        print("\n🎉 ALL TESTS PASSED! SYSTEM IS FULLY FUNCTIONAL!")
        return 0
    else:
        print("\n⚠️ Some tests failed. Please review the errors above.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
