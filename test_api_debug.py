#!/usr/bin/env python3
"""
Debug API endpoints and QR code issues
"""

import requests
import json
import sys
import time

def test_server_status():
    """Test if server is running"""
    try:
        response = requests.get('http://127.0.0.1:5001/', timeout=5)
        print(f"✅ Server is running - Status: {response.status_code}")
        return True
    except requests.exceptions.ConnectionError:
        print("❌ Server not running on port 5001")
        try:
            response = requests.get('http://127.0.0.1:5002/', timeout=5)
            print(f"✅ Server is running on port 5002 - Status: {response.status_code}")
            return True
        except:
            print("❌ Server not running on port 5002 either")
            return False
    except Exception as e:
        print(f"❌ Server error: {e}")
        return False

def test_order_details_api():
    """Test order details API"""
    print("\n🔍 Testing Order Details API...")
    
    for port in [5001, 5002]:
        try:
            url = f"http://127.0.0.1:{port}/api/order-details/ORD00000155"
            print(f"Testing: {url}")
            
            response = requests.get(url, timeout=10)
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"Success: {data.get('success')}")
                if data.get('success'):
                    print("✅ Order details API working")
                    return True
                else:
                    print(f"❌ API Error: {data.get('message')}")
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                print(f"Response: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ Error testing port {port}: {e}")
    
    return False

def test_qr_code_api():
    """Test QR code API"""
    print("\n🔍 Testing QR Code API...")
    
    for port in [5001, 5002]:
        try:
            url = f"http://127.0.0.1:{port}/api/order-qr-code/ORD00000155?branding=true"
            print(f"Testing: {url}")
            
            response = requests.get(url, timeout=15)
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"Success: {data.get('success')}")
                if data.get('success'):
                    qr_info = data.get('qr_code', {})
                    print(f"✅ QR code generated - Base64 length: {len(qr_info.get('base64', ''))}")
                    return True
                else:
                    print(f"❌ QR Error: {data.get('message')}")
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                print(f"Response: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ Error testing port {port}: {e}")
    
    return False

def main():
    """Main test function"""
    print("🧪 API DEBUG TEST")
    print("=" * 50)
    
    # Test server status
    if not test_server_status():
        print("\n❌ Server is not running. Please start the Flask application first.")
        return False
    
    # Test order details API
    order_api_works = test_order_details_api()
    
    # Test QR code API
    qr_api_works = test_qr_code_api()
    
    print("\n📊 SUMMARY:")
    print(f"Order Details API: {'✅ Working' if order_api_works else '❌ Failed'}")
    print(f"QR Code API: {'✅ Working' if qr_api_works else '❌ Failed'}")
    
    if not order_api_works or not qr_api_works:
        print("\n🔧 NEXT STEPS:")
        if not order_api_works:
            print("- Check if api_endpoints.py is properly imported")
            print("- Verify order exists in database")
            print("- Check Flask logs for errors")
        if not qr_api_works:
            print("- Check if qrcode and PIL dependencies are installed")
            print("- Verify QR code generator utility")
            print("- Check static/qr_codes directory permissions")
    
    return order_api_works and qr_api_works

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
