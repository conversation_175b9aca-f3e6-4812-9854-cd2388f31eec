#!/usr/bin/env python3
"""
Test the API endpoint directly after server restart
"""

import requests
import json
import time
import sys

def test_api_endpoint():
    """Test the API endpoint directly"""
    print("🔍 TESTING API ENDPOINT AFTER SERVER RESTART")
    print("=" * 60)
    
    # Wait for server to start
    time.sleep(3)
    
    try:
        response = requests.get('http://localhost:5000/riders/api/live-tracking-data', timeout=10)
        
        print(f"Status Code: {response.status_code}")
        print(f"Content-Type: {response.headers.get('content-type', 'Unknown')}")
        
        if response.status_code == 200:
            # Check if it's JSON or HTML
            content_type = response.headers.get('content-type', '')
            
            if 'application/json' in content_type:
                try:
                    data = response.json()
                    print("✅ Valid JSON response received!")
                    print(f"Keys: {list(data.keys())}")
                    
                    if 'active_orders' in data:
                        print(f"Active orders: {len(data['active_orders'])}")
                    
                    return True
                    
                except json.JSONDecodeError as e:
                    print(f"❌ JSON decode error: {e}")
                    return False
            else:
                print("❌ Response is HTML, not JSON")
                
                # Check for strftime error
                if "'str' object has no attribute 'strftime'" in response.text:
                    print("🎯 FOUND STRFTIME ERROR IN API!")
                    
                    # Extract error context
                    error_start = response.text.find("'str' object has no attribute 'strftime'")
                    if error_start != -1:
                        context_start = max(0, error_start - 200)
                        context_end = min(len(response.text), error_start + 300)
                        context = response.text[context_start:context_end]
                        print("\n📍 ERROR CONTEXT:")
                        print("-" * 50)
                        print(context)
                        print("-" * 50)
                    
                    return False
                else:
                    print("No strftime error found, but still HTML response")
                    print(f"Response preview: {response.text[:300]}")
                    return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text[:300]}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Server not running")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_assignment_dashboard():
    """Test the main assignment dashboard"""
    print("\n🌐 TESTING ASSIGNMENT DASHBOARD")
    print("=" * 60)
    
    try:
        response = requests.get('http://localhost:5000/riders/assignment-dashboard', timeout=10)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            # Check for error message
            if "Error loading assignment form" in response.text:
                print("❌ Error message found in dashboard")
                
                # Find the error context
                error_start = response.text.find("Error loading assignment form")
                if error_start != -1:
                    context_start = max(0, error_start - 100)
                    context_end = min(len(response.text), error_start + 200)
                    context = response.text[context_start:context_end]
                    print("\n📍 DASHBOARD ERROR CONTEXT:")
                    print("-" * 50)
                    print(context)
                    print("-" * 50)
                
                return False
            else:
                print("✅ No error message in dashboard")
                return True
        else:
            print(f"❌ Dashboard error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Dashboard test error: {e}")
        return False

if __name__ == "__main__":
    # Test both endpoints
    api_success = test_api_endpoint()
    dashboard_success = test_assignment_dashboard()
    
    if api_success and dashboard_success:
        print("\n🎉 SUCCESS: ALL TESTS PASSED!")
        sys.exit(0)
    else:
        print("\n❌ FAILURE: SOME TESTS FAILED")
        if not api_success:
            print("  - API endpoint failed")
        if not dashboard_success:
            print("  - Dashboard failed")
        sys.exit(1)
