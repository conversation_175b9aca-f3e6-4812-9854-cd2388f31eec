#!/usr/bin/env python3
"""
🔍 TEST API ENDPOINTS SCRIPT
Tests the specific API endpoints that were causing JSON serialization errors
"""

import requests
import json
import time
import sys

BASE_URL = "http://localhost:5000"

def test_api_endpoint(endpoint_name, url):
    """Test an API endpoint and check JSON response"""
    print(f"🧪 Testing {endpoint_name}: {url}")
    try:
        response = requests.get(url, timeout=10)
        status = response.status_code
        
        if status == 200:
            try:
                # Try to parse JSON response
                json_data = response.json()
                print(f"✅ {endpoint_name}: HTTP {status} - JSON VALID")
                
                # Check if it's a proper JSON structure
                if isinstance(json_data, (dict, list)):
                    print(f"   📊 Response type: {type(json_data).__name__}")
                    if isinstance(json_data, dict) and 'error' in json_data:
                        print(f"   ⚠️  API returned error: {json_data['error']}")
                    return True
                else:
                    print(f"   ❌ Invalid JSON structure: {type(json_data)}")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"❌ {endpoint_name}: JSON DECODE ERROR - {e}")
                print(f"   Response: {response.text[:200]}...")
                return False
        else:
            print(f"❌ {endpoint_name}: HTTP {status} - FAILED")
            print(f"   Response: {response.text[:200]}...")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ {endpoint_name}: CONNECTION ERROR - {e}")
        return False

def main():
    """Main testing function"""
    print("🚀 TESTING API ENDPOINTS FOR JSON SERIALIZATION")
    print("=" * 60)
    
    # Test API endpoints that were causing JSON serialization issues
    api_endpoints = [
        # Rider Performance API (Error 1)
        ("Rider Performance API", f"{BASE_URL}/riders/api/rider/RID001/performance"),
        ("Live Tracking Data API", f"{BASE_URL}/riders/api/live-tracking-data"),
        
        # Test with different rider IDs if available
        ("Rider Performance API (RID002)", f"{BASE_URL}/riders/api/rider/RID002/performance"),
        ("Rider Performance API (RID003)", f"{BASE_URL}/riders/api/rider/RID003/performance"),
    ]
    
    results = []
    for endpoint_name, url in api_endpoints:
        success = test_api_endpoint(endpoint_name, url)
        results.append((endpoint_name, success))
        time.sleep(1)  # Small delay between requests
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 API TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for endpoint_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} - {endpoint_name}")
    
    print(f"\n🎯 OVERALL: {passed}/{total} API endpoints passed ({passed/total*100:.1f}%)")
    
    if passed >= total * 0.75:  # Allow some failures for non-existent rider IDs
        print("🎉 API ENDPOINTS WORKING CORRECTLY!")
        return 0
    else:
        print("⚠️  API ENDPOINTS NEED ATTENTION")
        return 1

if __name__ == "__main__":
    sys.exit(main())
