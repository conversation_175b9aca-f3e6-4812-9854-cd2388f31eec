#!/usr/bin/env python3
"""
Test API endpoint after permission fix
"""

import requests
import json

def test_api():
    """Test the API endpoint"""
    try:
        url = 'http://127.0.0.1:5001/api/order-details/ORD00000155'
        print(f'🔍 Testing API endpoint: {url}')
        
        response = requests.get(url, timeout=10)
        print(f'📡 Status Code: {response.status_code}')
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f'✅ Success: {data.get("success", False)}')
                if data.get('success'):
                    print(f'📊 Order ID: {data["order"]["order_id"]}')
                    print(f'📊 Customer: {data["order"]["customer_name"]}')
                    print(f'📊 Items: {len(data["order_items"])}')
                    print('✅ API is working correctly!')
                else:
                    print(f'❌ API Error: {data.get("message", "Unknown error")}')
            except json.JSONDecodeError:
                print('❌ Response is not JSON')
                print(f'Response content: {response.text[:200]}')
        else:
            print(f'❌ HTTP Error: {response.status_code}')
            print(f'Response: {response.text[:200]}')
            
    except Exception as e:
        print(f'❌ Connection Error: {e}')

if __name__ == "__main__":
    test_api()
