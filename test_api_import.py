#!/usr/bin/env python3
"""
Test API blueprint import and registration
"""

import sys
import traceback

def test_api_import():
    """Test importing API blueprint"""
    try:
        print("🔍 Testing API blueprint import...")
        
        # Test basic imports
        from flask import Flask
        print("✅ Flask imported")
        
        from api_endpoints import api_bp
        print("✅ API blueprint imported")
        
        # Test blueprint properties
        print(f"   Blueprint name: {api_bp.name}")
        print(f"   Blueprint url_prefix: {api_bp.url_prefix}")
        
        # Test creating Flask app and registering blueprint
        app = Flask(__name__)
        app.register_blueprint(api_bp)
        print("✅ API blueprint registered")
        
        # Test route registration
        with app.app_context():
            rules = list(app.url_map.iter_rules())
            api_routes = [rule for rule in rules if rule.rule.startswith('/api/')]
            
            print(f"✅ Found {len(api_routes)} API routes:")
            for rule in api_routes:
                print(f"   {rule.rule} -> {rule.endpoint}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("Traceback:")
        traceback.print_exc()
        return False

def test_qr_generator():
    """Test QR code generator import"""
    try:
        print("\n🔍 Testing QR code generator...")
        
        from utils.qr_code_generator import OrderQRCodeGenerator, generate_order_qr_code
        print("✅ QR code generator imported")
        
        # Test creating generator instance
        generator = OrderQRCodeGenerator()
        print("✅ QR code generator instance created")
        
        return True
        
    except Exception as e:
        print(f"❌ QR Generator Error: {e}")
        print("Traceback:")
        traceback.print_exc()
        return False

def test_dependencies():
    """Test required dependencies"""
    try:
        print("\n🔍 Testing dependencies...")
        
        import qrcode
        print("✅ qrcode library available")
        
        from PIL import Image
        print("✅ PIL/Pillow library available")
        
        import sqlite3
        print("✅ sqlite3 available")
        
        return True
        
    except Exception as e:
        print(f"❌ Dependency Error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 API IMPORT TEST")
    print("=" * 50)
    
    api_ok = test_api_import()
    qr_ok = test_qr_generator()
    deps_ok = test_dependencies()
    
    print("\n📊 SUMMARY:")
    print(f"API Blueprint: {'✅ OK' if api_ok else '❌ Failed'}")
    print(f"QR Generator: {'✅ OK' if qr_ok else '❌ Failed'}")
    print(f"Dependencies: {'✅ OK' if deps_ok else '❌ Failed'}")
    
    if api_ok and qr_ok and deps_ok:
        print("\n🎉 All tests passed! Ready to start Flask server.")
    else:
        print("\n⚠️ Some tests failed. Check errors above.")
    
    sys.exit(0 if (api_ok and qr_ok and deps_ok) else 1)
