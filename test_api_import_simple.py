#!/usr/bin/env python3
"""
Simple test to check API import
"""

try:
    print("Testing API import...")
    from api_endpoints import api_bp
    print(f"✅ API blueprint imported: {api_bp.name}")
    print(f"   URL prefix: {api_bp.url_prefix}")
    
    # Check if routes are defined
    print(f"   Routes defined: {len(api_bp.deferred_functions)}")
    
except Exception as e:
    print(f"❌ Import error: {e}")
    import traceback
    traceback.print_exc()
