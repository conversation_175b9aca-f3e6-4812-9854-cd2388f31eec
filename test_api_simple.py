import requests
import json

print('Testing Order Details API...')
try:
    response = requests.get('http://127.0.0.1:5001/api/order-details/ORD00000155', timeout=10)
    print(f'Status: {response.status_code}')
    if response.status_code == 200:
        data = response.json()
        print(f'Success: {data.get("success")}')
        if data.get('success'):
            order = data.get('order', {})
            print(f'Customer Address: {order.get("customer_address", "N/A")}')
            print(f'Delivery Address: {order.get("delivery_address", "N/A")}')
            print(f'Customer Name: {order.get("customer_name", "N/A")}')
        else:
            print(f'Error: {data.get("message", "Unknown")}')
    else:
        print(f'HTTP Error: {response.text[:200]}')
except Exception as e:
    print(f'Error: {e}')

print('\nTesting QR Code API...')
try:
    response = requests.get('http://127.0.0.1:5001/api/order-qr-code/ORD00000155', timeout=10)
    print(f'Status: {response.status_code}')
    if response.status_code == 200:
        data = response.json()
        print(f'Success: {data.get("success")}')
        if data.get('success'):
            qr_data = data.get('qr_code', {})
            print(f'QR Base64 length: {len(qr_data.get("base64", ""))}')
        else:
            print(f'Error: {data.get("message", "Unknown")}')
    else:
        print(f'HTTP Error: {response.text[:200]}')
except Exception as e:
    print(f'Error: {e}')
