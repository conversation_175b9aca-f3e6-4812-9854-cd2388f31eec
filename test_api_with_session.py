#!/usr/bin/env python3
"""
Test the live tracking API with proper session handling
"""

import requests
import json
import sys

def test_with_session():
    """Test API with session cookies"""
    print("🔍 TESTING LIVE TRACKING API WITH SESSION")
    print("=" * 60)
    
    # Create a session to maintain cookies
    session = requests.Session()
    
    try:
        # First, try to access the main dashboard to get session cookies
        print("🔐 Getting session cookies from main dashboard...")
        main_response = session.get('http://localhost:5000/riders/assignment-dashboard', timeout=10)
        
        print(f"Main dashboard status: {main_response.status_code}")
        
        if main_response.status_code == 200:
            print("✅ Main dashboard accessible")
            
            # Now test the API with the same session
            print("\n🔄 Testing API with session cookies...")
            api_response = session.get('http://localhost:5000/riders/api/live-tracking-data', timeout=10)
            
            print(f"API status: {api_response.status_code}")
            
            if api_response.status_code == 200:
                try:
                    data = api_response.json()
                    print("✅ API JSON response received successfully")
                    
                    # Check the structure
                    if 'active_orders' in data:
                        print(f"✅ Active orders: {len(data['active_orders'])} found")
                        
                        # Check each active order for datetime formatting
                        for i, order in enumerate(data['active_orders']):
                            dispatch_time = order.get('dispatch_time', 'N/A')
                            print(f"  Order {i+1}: {order.get('order_id')} - Dispatch: {dispatch_time}")
                    
                    if 'recent_updates' in data:
                        print(f"✅ Recent updates: {len(data['recent_updates'])} found")
                    
                    if 'today_stats' in data:
                        print(f"✅ Today stats: {data['today_stats']}")
                    
                    if 'last_updated' in data:
                        print(f"✅ Last updated: {data['last_updated']}")
                    
                    return True
                    
                except json.JSONDecodeError as e:
                    print(f"❌ JSON decode error: {e}")
                    print(f"API response content: {api_response.text[:500]}")
                    
                    # Check for strftime error
                    if "'str' object has no attribute 'strftime'" in api_response.text:
                        print("🎯 FOUND THE STRFTIME ERROR IN API RESPONSE!")
                    
                    return False
            else:
                print(f"❌ API returned status {api_response.status_code}")
                print(f"API response: {api_response.text[:500]}")
                return False
        else:
            print(f"❌ Main dashboard returned status {main_response.status_code}")
            print("This suggests authentication issues")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_direct_api_call():
    """Test API directly without authentication to see the actual error"""
    print("\n🔧 TESTING API DIRECTLY (bypassing auth)")
    print("=" * 60)
    
    try:
        # Import Flask app components to test the function directly
        sys.path.append('.')
        from app import app
        from routes.modern_riders import live_tracking_data
        from utils.db import get_db
        
        with app.app_context():
            print("✅ Flask app context created")
            
            # Try to call the function directly
            try:
                # Mock the login_required decorator by setting a fake user
                from flask import g
                g.user = type('User', (), {'id': 1, 'username': 'test'})()
                
                # Call the function
                result = live_tracking_data()
                print(f"✅ Function executed successfully")
                print(f"Result type: {type(result)}")
                
                # If it's a Response object, get the data
                if hasattr(result, 'get_json'):
                    data = result.get_json()
                    print(f"✅ JSON data: {data}")
                elif hasattr(result, 'data'):
                    print(f"✅ Response data: {result.data}")
                else:
                    print(f"✅ Result: {result}")
                
                return True
                
            except Exception as e:
                print(f"❌ Function execution error: {e}")
                import traceback
                traceback.print_exc()
                return False
                
    except Exception as e:
        print(f"❌ Setup error: {e}")
        return False

if __name__ == "__main__":
    # Test with session first
    session_success = test_with_session()
    
    # Test direct function call
    direct_success = test_direct_api_call()
    
    if session_success and direct_success:
        print("\n🎉 SUCCESS: ALL TESTS PASSED!")
        sys.exit(0)
    else:
        print("\n❌ FAILURE: SOME TESTS FAILED")
        sys.exit(1)
