import sys
print('Testing app import and routes...')

try:
    import app
    print('✅ App imported successfully')
    
    # Check if the routes are registered
    print('\n🔍 Checking chart-data routes:')
    chart_routes = []
    for rule in app.app.url_map.iter_rules():
        if 'chart-data' in rule.rule:
            chart_routes.append(rule.rule)
            print(f'  {rule.rule} -> {rule.endpoint}')
    
    if not chart_routes:
        print('  ❌ No chart-data routes found!')
    else:
        print(f'  ✅ Found {len(chart_routes)} chart-data routes')
    
    print('\n🔍 Checking all finance routes:')
    finance_routes = [rule for rule in app.app.url_map.iter_rules() if 'finance' in rule.rule]
    print(f'Total finance routes found: {len(finance_routes)}')
    
    # Show recent finance routes
    for rule in finance_routes[-15:]:  # Show last 15
        print(f'  {rule.rule} -> {rule.endpoint}')
        
except Exception as e:
    print(f'❌ Import error: {e}')
    import traceback
    traceback.print_exc()
