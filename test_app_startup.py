#!/usr/bin/env python3
"""
Test App Startup - Check for import errors
"""

import sys
import traceback

def test_app_import():
    """Test if the app can be imported without errors"""
    try:
        print("🔍 Testing app import...")
        
        # Add current directory to path
        sys.path.insert(0, '.')
        
        # Try to import the app
        from app import app
        
        print("✅ App imported successfully")
        
        # Test app context
        with app.app_context():
            rules = list(app.url_map.iter_rules())
            print(f"✅ App context works - {len(rules)} routes registered")
            
            # Check for specific routes
            api_routes = [rule for rule in rules if '/api/order-details' in rule.rule]
            order_routes = [rule for rule in rules if '/orders/' in rule.rule and 'details' in rule.rule]
            warehouse_routes = [rule for rule in rules if '/warehouse/packing' in rule.rule]
            
            print(f"📊 API order details routes: {len(api_routes)}")
            for route in api_routes:
                print(f"   • {route.rule} -> {route.endpoint}")
                
            print(f"📊 Order details routes: {len(order_routes)}")
            for route in order_routes:
                print(f"   • {route.rule} -> {route.endpoint}")
                
            print(f"📊 Warehouse packing routes: {len(warehouse_routes)}")
            for route in warehouse_routes:
                print(f"   • {route.rule} -> {route.endpoint}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error importing app: {e}")
        traceback.print_exc()
        return False

def test_api_blueprint():
    """Test if API blueprint can be imported"""
    try:
        print("\n🔍 Testing API blueprint import...")
        
        from api_endpoints import api_bp
        print(f"✅ API blueprint imported: {api_bp.name}")
        print(f"   URL prefix: {api_bp.url_prefix}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error importing API blueprint: {e}")
        traceback.print_exc()
        return False

def main():
    """Main testing function"""
    print("🧪 APP STARTUP TESTING")
    print("=" * 50)
    
    # Test imports
    app_ok = test_app_import()
    api_ok = test_api_blueprint()
    
    print(f"\n📊 RESULTS")
    print("=" * 30)
    print(f"App Import: {'✅ OK' if app_ok else '❌ FAILED'}")
    print(f"API Blueprint: {'✅ OK' if api_ok else '❌ FAILED'}")
    
    if app_ok and api_ok:
        print("\n🎉 All imports successful - app should start properly")
    else:
        print("\n⚠️  Import issues detected - fix before testing routes")

if __name__ == "__main__":
    main()
