import sqlite3
import sys
import os
from datetime import datetime

# Add the current directory to Python path
sys.path.insert(0, os.getcwd())

def test_approve_order_database_operations():
    """Test the database operations that happen during order approval"""
    
    print("🧪 TESTING APPROVE ORDER DATABASE OPERATIONS")
    print("=" * 50)
    
    # Connect to database
    conn = sqlite3.connect('instance/medivent.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    # Test order ID
    order_id = 'ORD1753983391CA9E99E1'
    
    try:
        # 1. Check if order exists and get details
        print("1️⃣ Checking order existence...")
        order = cursor.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
        if order:
            print(f"   ✅ Order found: {order['customer_name']}")
            print(f"   📊 Status: {order['status']}")
        else:
            print("   ❌ Order not found")
            return False
        
        # 2. Test invoice insert (the exact operation from approve_order)
        print("\n2️⃣ Testing invoice insert...")
        test_invoice_number = f"TEST_INV_{int(datetime.now().timestamp())}"
        
        cursor.execute('''
            INSERT INTO invoices (invoice_number, order_id, generated_by, pdf_path, date_generated)
            VALUES (?, ?, ?, ?, ?)
        ''', (test_invoice_number, order_id, 'test_user', '/test/invoice.pdf', datetime.now().isoformat()))
        
        print(f"   ✅ Invoice insert successful: {test_invoice_number}")
        
        # 3. Test challan insert (the exact operation from approve_order)
        print("\n3️⃣ Testing challan insert...")
        test_dc_number = f"TEST_DC_{int(datetime.now().timestamp())}"
        
        cursor.execute('''
            INSERT INTO challans (dc_number, invoice_number, order_id, customer_name, generated_by, pdf_path, date_generated)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (test_dc_number, test_invoice_number, order_id, order['customer_name'], 'test_user', '/test/challan.pdf', datetime.now().isoformat()))
        
        print(f"   ✅ Challan insert successful: {test_dc_number}")
        
        # 4. Test order update
        print("\n4️⃣ Testing order update...")
        cursor.execute('''
            UPDATE orders
            SET status = ?, invoice_number = ?, approval_date = ?, approved_by = ?,
                updated_by = ?, approval_notes = ?, last_updated = ?
            WHERE order_id = ?
        ''', ("Test_Approved", test_invoice_number, datetime.now().isoformat(), 'test_user',
              'test_user', 'Test approval', datetime.now().isoformat(), order_id))
        
        print("   ✅ Order update successful")
        
        # 5. Verify all operations
        print("\n5️⃣ Verifying operations...")
        
        # Check invoice
        invoice = cursor.execute('SELECT * FROM invoices WHERE invoice_number = ?', (test_invoice_number,)).fetchone()
        if invoice:
            print(f"   ✅ Invoice verified: {invoice['invoice_number']}")
        else:
            print("   ❌ Invoice verification failed")
        
        # Check challan
        challan = cursor.execute('SELECT * FROM challans WHERE dc_number = ?', (test_dc_number,)).fetchone()
        if challan:
            print(f"   ✅ Challan verified: {challan['dc_number']}")
        else:
            print("   ❌ Challan verification failed")
        
        # Check order
        updated_order = cursor.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
        if updated_order and updated_order['status'] == 'Test_Approved':
            print(f"   ✅ Order status verified: {updated_order['status']}")
        else:
            print("   ❌ Order status verification failed")
        
        # 6. Clean up test data
        print("\n6️⃣ Cleaning up test data...")
        cursor.execute('DELETE FROM invoices WHERE invoice_number = ?', (test_invoice_number,))
        cursor.execute('DELETE FROM challans WHERE dc_number = ?', (test_dc_number,))
        cursor.execute('UPDATE orders SET status = ?, invoice_number = NULL WHERE order_id = ?', (order['status'], order_id))
        
        conn.commit()
        print("   ✅ Cleanup completed")
        
        print("\n🎉 ALL DATABASE OPERATIONS SUCCESSFUL!")
        return True
        
    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        print(f"Error type: {type(e)}")
        conn.rollback()
        return False
        
    finally:
        conn.close()

if __name__ == "__main__":
    success = test_approve_order_database_operations()
    if success:
        print("\n✅ Database operations are working correctly!")
        print("The approve order function should work without column errors.")
    else:
        print("\n❌ Database operations failed!")
        print("There are still issues that need to be resolved.")
