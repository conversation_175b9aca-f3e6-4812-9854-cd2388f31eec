#!/usr/bin/env python3
"""
Test the new assignment dashboard route
"""

import requests
import time

def test_assignment_dashboard():
    """Test the new assignment dashboard route"""
    base_url = "http://127.0.0.1:5000"
    
    print("🧪 TESTING ASSIGNMENT DASHBOARD ROUTE")
    print("=" * 50)
    
    # Test if server is running
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"✅ Server is running at {base_url}")
    except requests.exceptions.RequestException as e:
        print(f"❌ Server not accessible at {base_url}")
        print(f"   Error: {e}")
        return False
    
    # Test the new assignment dashboard route
    try:
        url = f"{base_url}/riders/assignment-dashboard"
        print(f"\n🔍 Testing: {url}")
        
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            print(f"✅ Assignment Dashboard - HTTP {response.status_code}")
            print("✅ Route is accessible and working!")
            return True
        elif response.status_code == 302:
            print(f"🔄 Assignment Dashboard - HTTP {response.status_code} (Redirect - likely login required)")
            print("✅ Route exists and redirects properly!")
            return True
        else:
            print(f"❌ Assignment Dashboard - HTTP {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Assignment Dashboard - Connection Error: {e}")
        return False

if __name__ == "__main__":
    success = test_assignment_dashboard()
    if success:
        print(f"\n🎉 ASSIGNMENT DASHBOARD TEST PASSED!")
        print("✅ Priority 1 implementation successful!")
    else:
        print(f"\n❌ ASSIGNMENT DASHBOARD TEST FAILED!")
        print("❌ Check server logs for details")
