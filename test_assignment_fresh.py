#!/usr/bin/env python3
"""
Test assignment dashboard after server restart
"""

import requests
import time
import sys

def test_assignment_dashboard():
    """Test assignment dashboard after fresh server start"""
    print("🔄 TESTING ASSIGNMENT DASHBOARD AFTER SERVER RESTART")
    print("=" * 60)
    
    # Wait for server to start
    print("⏳ Waiting for server to start...")
    time.sleep(5)
    
    try:
        response = requests.get('http://localhost:5000/riders/assignment-dashboard', timeout=15)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            # Check for the specific error that was reported
            error_patterns = [
                "'str' object has no attribute 'strftime'",
                "Error loading assignment form",
                "AttributeError",
                "strftime"
            ]
            
            found_errors = []
            for pattern in error_patterns:
                if pattern in response.text:
                    found_errors.append(pattern)
            
            if found_errors:
                print("❌ ERRORS STILL FOUND:")
                for error in found_errors:
                    print(f"  - {error}")
                
                # Show a snippet of the error context
                for error in found_errors:
                    start_idx = response.text.find(error)
                    if start_idx != -1:
                        context_start = max(0, start_idx - 100)
                        context_end = min(len(response.text), start_idx + len(error) + 100)
                        context = response.text[context_start:context_end]
                        print(f"\n📍 Error context for '{error}':")
                        print(f"   ...{context}...")
                
                return False
            else:
                print("✅ NO STRFTIME ERRORS FOUND!")
                print("✅ Assignment dashboard loads successfully")
                
                # Check if the page has expected content
                if "Rider Assignment Dashboard" in response.text:
                    print("✅ Page title found")
                if "Orders Ready for Rider Assignment" in response.text:
                    print("✅ Orders section found")
                if "Available Riders" in response.text:
                    print("✅ Riders section found")
                
                return True
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response preview: {response.text[:500]}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Server might not be running")
        return False
    except Exception as e:
        print(f"❌ Request Error: {e}")
        return False

def test_server_status():
    """Test if server is running"""
    print("\n🌐 TESTING SERVER STATUS")
    print("=" * 60)
    
    try:
        response = requests.get('http://localhost:5000/', timeout=10)
        print(f"Main page status: {response.status_code}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Server not responding: {e}")
        return False

if __name__ == "__main__":
    # Test server first
    if test_server_status():
        # Test assignment dashboard
        success = test_assignment_dashboard()
        if success:
            print("\n🎉 SUCCESS: ASSIGNMENT DASHBOARD WORKING!")
            sys.exit(0)
        else:
            print("\n❌ FAILURE: ASSIGNMENT DASHBOARD STILL HAS ERRORS")
            sys.exit(1)
    else:
        print("\n❌ FAILURE: SERVER NOT RUNNING")
        sys.exit(1)
