#!/usr/bin/env python3
"""
Test authentication and route access
"""

import requests

def test_authentication():
    """Test authentication and route access"""
    
    print("🔐 TESTING AUTHENTICATION")
    print("=" * 50)
    
    try:
        # Test Flask app
        response = requests.get('http://127.0.0.1:5001/')
        print(f'Flask app status: {response.status_code}')
        if 'Login' in response.text:
            print('✅ Flask app is running (showing login page)')
        else:
            print('❌ Flask app response unexpected')
        
        # Test direct route access without redirects
        response = requests.get('http://127.0.0.1:5001/products/product_management/', allow_redirects=False)
        print(f'Direct route status: {response.status_code}')
        
        if response.status_code == 302:
            print('🔄 Route redirects (authentication required)')
            location = response.headers.get('Location', 'Not specified')
            print(f'Redirect location: {location}')
        elif response.status_code == 200:
            print('✅ Route accessible without authentication')
        else:
            print(f'❌ Unexpected status: {response.status_code}')
            
    except Exception as e:
        print(f'❌ Error testing: {e}')

if __name__ == "__main__":
    test_authentication()
