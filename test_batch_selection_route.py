#!/usr/bin/env python3
"""
Test Batch Selection Route
"""

import requests
import sys

def test_batch_selection_route():
    """Test the batch selection route"""
    
    print("🧪 TESTING BATCH SELECTION ROUTE")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5001"
    order_id = "ORD1754153041416935BB"
    
    # Test batch selection page
    url = f"{base_url}/orders/{order_id}/batch-selection"
    
    try:
        print(f"Testing: {url}")
        response = requests.get(url, timeout=10)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Route is accessible")
            
            # Check if the response contains expected content
            content = response.text
            
            if "data-inventory-id" in content:
                print("✅ Template contains data-inventory-id attribute")
            else:
                print("❌ Template missing data-inventory-id attribute")
            
            if "Batch Selection for DC Generation" in content:
                print("✅ Page title found")
            else:
                print("❌ Page title not found")
            
            if "generatePartialDC" in content:
                print("✅ JavaScript function found")
            else:
                print("❌ JavaScript function not found")
                
        elif response.status_code == 302:
            print("⚠️  Route redirected (likely authentication required)")
            print(f"Redirect location: {response.headers.get('Location', 'Unknown')}")
        elif response.status_code == 404:
            print("❌ Route not found")
        elif response.status_code == 500:
            print("❌ Server error")
            print("Response content:")
            print(response.text[:500])
        else:
            print(f"❌ Unexpected status code: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Is the Flask app running?")
        return False
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    return True

def test_partial_dc_route():
    """Test the partial DC generation route"""
    
    print(f"\n🧪 TESTING PARTIAL DC ROUTE")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5001"
    order_id = "ORD1754153041416935BB"
    
    # Test with empty selections
    test_selections = "{}"
    url = f"{base_url}/orders/{order_id}/generate-partial-dc?selections={test_selections}"
    
    try:
        print(f"Testing: {url}")
        response = requests.get(url, timeout=10)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Route is accessible")
        elif response.status_code == 302:
            print("⚠️  Route redirected (expected for empty selections)")
            print(f"Redirect location: {response.headers.get('Location', 'Unknown')}")
        elif response.status_code == 404:
            print("❌ Route not found")
        elif response.status_code == 500:
            print("❌ Server error")
            print("Response content:")
            print(response.text[:500])
        else:
            print(f"❌ Unexpected status code: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Is the Flask app running?")
        return False
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🚀 STARTING ROUTE TESTS")
    print("=" * 70)
    
    success1 = test_batch_selection_route()
    success2 = test_partial_dc_route()
    
    print(f"\n🎯 TEST RESULTS")
    print("=" * 70)
    
    if success1:
        print("✅ Batch selection route test completed")
    else:
        print("❌ Batch selection route test failed")
    
    if success2:
        print("✅ Partial DC route test completed")
    else:
        print("❌ Partial DC route test failed")
    
    if success1 and success2:
        print("\n🎉 ALL ROUTE TESTS COMPLETED!")
    else:
        print("\n💥 SOME TESTS FAILED")
        print("Please check if the Flask server is running and accessible.")
