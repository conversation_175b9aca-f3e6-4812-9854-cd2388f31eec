#!/usr/bin/env python3
"""
Test script to verify blueprint registration
"""

import sys
import os
sys.path.insert(0, os.getcwd())

print("=== BLUEPRINT REGISTRATION TEST ===")

try:
    # Test 1: Import Flask
    print("1. Testing Flask import...")
    from flask import Flask
    print("   ✅ Flask imported successfully")
    
    # Test 2: Create Flask app
    print("2. Creating Flask app...")
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'test'
    app.config['DATABASE'] = 'medivent_erp.db'
    print("   ✅ Flask app created successfully")
    
    # Test 3: Import blueprint
    print("3. Testing blueprint import...")
    from routes.partial_dc import partial_dc_bp
    print(f"   ✅ Blueprint imported: {partial_dc_bp.name}")
    print(f"   ✅ URL prefix: {partial_dc_bp.url_prefix}")
    
    # Test 4: Register blueprint
    print("4. Registering blueprint...")
    app.register_blueprint(partial_dc_bp)
    print("   ✅ Blueprint registered successfully")
    
    # Test 5: Check registered routes
    print("5. Checking registered routes...")
    partial_routes = []
    for rule in app.url_map.iter_rules():
        if 'partial_dc' in rule.endpoint:
            partial_routes.append(f"   {rule.rule} -> {rule.endpoint}")
    
    if partial_routes:
        print(f"   ✅ Found {len(partial_routes)} partial_dc routes:")
        for route in partial_routes:
            print(route)
    else:
        print("   ❌ No partial_dc routes found!")
    
    # Test 6: Test app context
    print("6. Testing app context...")
    with app.app_context():
        print("   ✅ App context working")
    
    print("\n=== ALL TESTS PASSED ===")
    print("Blueprint registration is working correctly!")
    
except ImportError as e:
    print(f"   ❌ Import error: {e}")
    import traceback
    traceback.print_exc()
    
except Exception as e:
    print(f"   ❌ Error: {e}")
    import traceback
    traceback.print_exc()
