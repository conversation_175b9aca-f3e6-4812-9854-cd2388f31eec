#!/usr/bin/env python3
"""
Test blueprint imports one by one to identify the problematic import
"""

print("🔍 TESTING BLUEPRINT IMPORTS ONE BY ONE")

blueprints_to_test = [
    ("routes.sales_analytics", "sales_analytics_bp"),
    ("routes.advanced_payment", "advanced_payment_bp"),
    ("routes.delivery_analytics", "delivery_analytics_bp"),
    ("routes.users", "users_bp"),
    ("routes.products", "products_bp"),
    ("routes.activity_tracking", "activity_bp"),
    ("routes.permission_api", "permission_api"),
    ("routes.orders", "orders_bp"),
    ("routes.orders_enhanced", "orders_enhanced_bp"),
    ("routes.inventory", "inventory_bp"),
    ("routes.warehouses", "warehouses_bp"),
    ("api_endpoints", "api_bp"),
    ("routes.divisions_modern", "divisions_bp"),
    ("routes.modern_riders", "riders_bp"),
    ("routes.notifications", "notifications_bp"),
    ("routes.tracking", "tracking_bp"),
    ("routes.batch_selection", "batch_selection_bp"),
    ("routes.dc_generation", "dc_generation_bp"),
]

for i, (module_name, blueprint_name) in enumerate(blueprints_to_test, 1):
    try:
        print(f"📦 Step {i}: Testing import of {module_name}...")
        module = __import__(module_name, fromlist=[blueprint_name])
        blueprint = getattr(module, blueprint_name)
        print(f"✅ {module_name} imported successfully")
    except Exception as e:
        print(f"❌ ERROR importing {module_name}: {e}")
        import traceback
        traceback.print_exc()
        break

print("🎉 ALL BLUEPRINT IMPORTS TESTED")
