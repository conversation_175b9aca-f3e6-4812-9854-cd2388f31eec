#!/usr/bin/env python3
"""
Simple Blueprint Test
Quick test to verify blueprint functionality
"""

def test_blueprint_import():
    """Test blueprint import"""
    try:
        print("Testing blueprint import...")
        from routes.partial_pending import partial_pending_bp
        print(f"✅ Blueprint imported: {partial_pending_bp.name}")
        print(f"✅ URL prefix: {partial_pending_bp.url_prefix}")
        return True
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def test_app_registration():
    """Test app registration"""
    try:
        print("Testing app registration...")
        
        # Check if app.py has the registration
        with open('app.py', 'r') as f:
            content = f.read()
        
        if 'partial_pending_bp' in content:
            print("✅ Blueprint registration found in app.py")
            return True
        else:
            print("❌ Blueprint registration not found")
            return False
            
    except Exception as e:
        print(f"❌ App test failed: {e}")
        return False

def main():
    print("🔧 SIMPLE BLUEPRINT TEST")
    print("=" * 30)
    
    import_ok = test_blueprint_import()
    app_ok = test_app_registration()
    
    if import_ok and app_ok:
        print("\n✅ Blueprint should work!")
        print("💡 Try accessing: http://localhost:5000/partial-pending/")
    else:
        print("\n❌ Issues found")
        
    print("\n🚀 Starting Flask app...")

if __name__ == "__main__":
    main()
