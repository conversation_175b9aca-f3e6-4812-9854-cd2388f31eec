#!/usr/bin/env python3
"""
Standalone Blueprint Test
Test the partial_pending blueprint in isolation
"""

from flask import Flask

# Create minimal Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'test-key'

# Create minimal blueprint inline
from flask import Blueprint
test_bp = Blueprint('partial_pending', __name__, url_prefix='/partial-pending')

@test_bp.route('/')
def index():
    return "✅ Partial Pending Blueprint Working on Port 5001!"

@test_bp.route('/status')
def status():
    return {"status": "working", "port": 5001, "blueprint": "partial_pending"}

# Register blueprint
app.register_blueprint(test_bp)

@app.route('/')
def home():
    return "Flask App Running - <a href='/partial-pending/'>Test Partial Pending</a>"

if __name__ == '__main__':
    print("🚀 Starting standalone test on port 5001...")
    print("📍 Test URLs:")
    print("   Main: http://127.0.0.1:5001/")
    print("   Partial Pending: http://127.0.0.1:5001/partial-pending/")
    print("   Status: http://127.0.0.1:5001/partial-pending/status")
    
    app.run(host='127.0.0.1', port=5001, debug=True)
