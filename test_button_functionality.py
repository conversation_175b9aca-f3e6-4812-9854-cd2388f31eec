#!/usr/bin/env python3
"""
Test script to verify button functionality and routes
"""

import requests
import time
from datetime import datetime

def test_warehouse_packing_page():
    """Test if warehouse packing page loads"""
    print("\n🧪 Testing Warehouse Packing Page...")
    
    try:
        response = requests.get('http://127.0.0.1:5001/warehouse/packing', timeout=15)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text
            
            # Check for JavaScript functions
            functions_to_check = [
                ('printAddress', 'printAddress function'),
                ('packOrder', 'packOrder function'),
                ('dispatchOrder', 'dispatchOrder function'),
                ('printOrderAddress', 'printOrderAddress function'),
                ('viewOrderDetails', 'viewOrderDetails function')
            ]
            
            print("   📋 Checking JavaScript functions:")
            for func_name, description in functions_to_check:
                if f'function {func_name}(' in content:
                    print(f"      ✅ {description} found")
                else:
                    print(f"      ❌ {description} missing")
            
            # Check for button onclick handlers
            button_checks = [
                ('onclick="printAddress(', 'Print Address button onclick'),
                ('onclick="packOrder(', 'Pack Order button onclick'),
                ('onclick="dispatchOrder(', 'Dispatch Order button onclick'),
                ('onclick="printOrderAddress(', 'Modal Print Address button onclick'),
                ('onclick="viewOrderDetails(', 'View Details button onclick')
            ]
            
            print("   🔘 Checking button onclick handlers:")
            for onclick_pattern, description in button_checks:
                if onclick_pattern in content:
                    print(f"      ✅ {description} found")
                else:
                    print(f"      ❌ {description} missing")
            
            # Check for modal elements
            modal_checks = [
                ('id="packOrderModal"', 'Pack Order Modal'),
                ('id="dispatchOrderModal"', 'Dispatch Order Modal'),
                ('id="orderDetailsModal"', 'Order Details Modal')
            ]
            
            print("   📦 Checking modal elements:")
            for modal_id, description in modal_checks:
                if modal_id in content:
                    print(f"      ✅ {description} found")
                else:
                    print(f"      ❌ {description} missing")
            
            # Check if QR code section is removed
            if 'QR Code' in content or 'qrCode' in content:
                print("   ❌ QR Code section still present (should be removed)")
            else:
                print("   ✅ QR Code section successfully removed")
                
            return True
            
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_print_address_route():
    """Test print address route"""
    print("\n🖨️ Testing Print Address Route...")
    
    test_orders = ['ORD00000155', 'ORD00000157', 'ORD00000150']
    
    for order_id in test_orders:
        try:
            url = f'http://127.0.0.1:5001/orders/{order_id}/print-address'
            print(f"   Testing: {url}")
            
            response = requests.get(url, timeout=10)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                content = response.text
                if 'address_label.html' in content or 'ORDER:' in content or order_id in content:
                    print(f"   ✅ Address label page loaded for {order_id}")
                else:
                    print(f"   ❌ Unexpected content for {order_id}")
            else:
                print(f"   ❌ HTTP Error {response.status_code} for {order_id}")
                
        except Exception as e:
            print(f"   ❌ Error testing {order_id}: {e}")

def test_pack_order_route():
    """Test pack order POST route"""
    print("\n📦 Testing Pack Order Route...")
    
    try:
        url = 'http://127.0.0.1:5001/warehouse/pack_order'
        print(f"   Testing: {url}")
        
        # Test with GET (should return 405 Method Not Allowed)
        response = requests.get(url, timeout=10)
        print(f"   GET Status: {response.status_code}")
        
        if response.status_code == 405:
            print("   ✅ Pack order route exists (405 Method Not Allowed for GET is expected)")
        else:
            print(f"   ❌ Unexpected status for GET: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")

def test_dispatch_order_route():
    """Test dispatch order POST route"""
    print("\n🚚 Testing Dispatch Order Route...")
    
    try:
        url = 'http://127.0.0.1:5001/warehouse/dispatch-order'
        print(f"   Testing: {url}")
        
        # Test with GET (should return 405 Method Not Allowed)
        response = requests.get(url, timeout=10)
        print(f"   GET Status: {response.status_code}")
        
        if response.status_code == 405:
            print("   ✅ Dispatch order route exists (405 Method Not Allowed for GET is expected)")
        else:
            print(f"   ❌ Unexpected status for GET: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")

def main():
    """Main test function"""
    print("🚀 BUTTON FUNCTIONALITY & ROUTE TESTING")
    print("=" * 60)
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Wait for server to be ready
    print("\n⏳ Waiting for server to be ready...")
    time.sleep(3)
    
    # Run tests
    test_warehouse_packing_page()
    test_print_address_route()
    test_pack_order_route()
    test_dispatch_order_route()
    
    print("\n" + "="*60)
    print("🏁 TESTING COMPLETED")
    print(f"⏰ Test finished at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    print("\n📋 NEXT STEPS:")
    print("1. Open browser to http://127.0.0.1:5001/warehouse/packing")
    print("2. Open browser console (F12)")
    print("3. Click buttons and check console for error messages")
    print("4. Verify modal functionality")

if __name__ == "__main__":
    main()
