<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Button Approaches Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>🧪 Button Approaches Test</h1>
        <p>Testing all 5 button approaches with order ID: <strong>ORD00000155</strong></p>
        
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5>All Button Approaches</h5>
                    </div>
                    <div class="card-body">
                        
                        <!-- APPROACH 1: Traditional onclick -->
                        <div class="mb-3">
                            <h6>Approach 1: Traditional onclick</h6>
                            <button class="btn btn-secondary btn-sm" onclick="printAddressV1('ORD00000155')">
                                <i class="fas fa-print"></i> Print Address (v1)
                            </button>
                            <button class="btn btn-success btn-sm" onclick="packOrderV1('ORD00000155')">
                                <i class="fas fa-box"></i> Mark Packed (v1)
                            </button>
                        </div>
                        
                        <!-- APPROACH 2: Data-driven -->
                        <div class="mb-3">
                            <h6>Approach 2: Data-driven with jQuery</h6>
                            <button class="btn btn-secondary btn-sm warehouse-print-btn" 
                                    data-order-id="ORD00000155" 
                                    data-action="print-address">
                                <i class="fas fa-print"></i> Print (v2)
                            </button>
                            <button class="btn btn-success btn-sm warehouse-pack-btn" 
                                    data-order-id="ORD00000155" 
                                    data-action="pack-order">
                                <i class="fas fa-box"></i> Pack (v2)
                            </button>
                        </div>
                        
                        <!-- APPROACH 3: Direct listeners -->
                        <div class="mb-3">
                            <h6>Approach 3: Direct Event Listeners</h6>
                            <button class="btn btn-warning btn-sm direct-print-btn" 
                                    data-order="ORD00000155">
                                <i class="fas fa-print"></i> Print (v3)
                            </button>
                            <button class="btn btn-primary btn-sm direct-pack-btn" 
                                    data-order="ORD00000155">
                                <i class="fas fa-box"></i> Pack (v3)
                            </button>
                        </div>
                        
                        <!-- APPROACH 5: Inline handlers -->
                        <div class="mb-3">
                            <h6>Approach 5: Inline Event Handlers</h6>
                            <button class="btn btn-dark btn-sm" 
                                    onmousedown="handlePrintInline('ORD00000155')">
                                <i class="fas fa-print"></i> Print (v5)
                            </button>
                            <button class="btn btn-light btn-sm" 
                                    onmousedown="handlePackInline('ORD00000155')">
                                <i class="fas fa-box"></i> Pack (v5)
                            </button>
                        </div>
                        
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Console Output</h5>
                    </div>
                    <div class="card-body">
                        <div id="console-output" style="background: #000; color: #0f0; padding: 10px; font-family: monospace; height: 200px; overflow-y: auto;">
                            Console messages will appear here...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pack Order Modal -->
    <div class="modal fade" id="packOrderModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Pack Order</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="packOrderForm">
                        <input type="hidden" id="packOrderId" name="order_id">
                        <div class="form-group">
                            <label>Packed By:</label>
                            <input type="text" class="form-control" name="packed_by" required>
                        </div>
                        <div class="form-group">
                            <label>Notes:</label>
                            <textarea class="form-control" name="packing_notes"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-success" onclick="confirmPackOrder()">Confirm Pack</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Console output function
        function logToConsole(message) {
            console.log(message);
            const output = document.getElementById('console-output');
            output.innerHTML += message + '\n';
            output.scrollTop = output.scrollHeight;
        }

        // APPROACH 1: Traditional onclick Functions
        function printAddressV1(orderId) {
            logToConsole('🖨️ APPROACH 1 - printAddressV1 called with: ' + orderId);
            
            try {
                const printUrl = `http://127.0.0.1:5001/orders/${orderId}/print-address`;
                logToConsole('🖨️ Opening print URL: ' + printUrl);
                
                const printWindow = window.open(printUrl, '_blank', 'width=800,height=600');
                
                if (!printWindow) {
                    alert('❌ Failed to open print window. Check popup blocker.');
                } else {
                    logToConsole('✅ APPROACH 1 - Print window opened successfully');
                }
                
            } catch (error) {
                logToConsole('❌ APPROACH 1 Error: ' + error.message);
                alert('❌ Error: ' + error.message);
            }
        }

        function packOrderV1(orderId) {
            logToConsole('📦 APPROACH 1 - packOrderV1 called with: ' + orderId);
            
            try {
                if ($('#packOrderModal').length === 0) {
                    alert('❌ Pack modal not found. Please refresh the page.');
                    return;
                }
                
                $('#packOrderId').val(orderId);
                $('#packOrderModal').modal('show');
                
                logToConsole('✅ APPROACH 1 - Pack modal opened successfully');
                
            } catch (error) {
                logToConsole('❌ APPROACH 1 Error: ' + error.message);
                alert('❌ Error: ' + error.message);
            }
        }

        // APPROACH 5: Inline Event Handlers
        function handlePrintInline(orderId) {
            logToConsole('🖨️ APPROACH 5 - Inline print handler for: ' + orderId);
            
            try {
                const printUrl = `http://127.0.0.1:5001/orders/${orderId}/print-address`;
                const printWindow = window.open(printUrl, '_blank', 'width=800,height=600');
                
                if (!printWindow) {
                    alert('❌ Failed to open print window');
                } else {
                    logToConsole('✅ APPROACH 5 - Print window opened');
                }
            } catch (error) {
                logToConsole('❌ APPROACH 5 Print Error: ' + error.message);
                alert('❌ Error: ' + error.message);
            }
        }

        function handlePackInline(orderId) {
            logToConsole('📦 APPROACH 5 - Inline pack handler for: ' + orderId);
            
            try {
                if ($('#packOrderModal').length === 0) {
                    alert('❌ Pack modal not found');
                    return;
                }
                
                $('#packOrderId').val(orderId);
                $('#packOrderModal').modal('show');
                logToConsole('✅ APPROACH 5 - Pack modal opened');
                
            } catch (error) {
                logToConsole('❌ APPROACH 5 Pack Error: ' + error.message);
                alert('❌ Error: ' + error.message);
            }
        }

        function confirmPackOrder() {
            logToConsole('📦 Pack order confirmation called');
            alert('Pack order would be submitted here');
        }

        // Initialize other approaches
        $(document).ready(function() {
            logToConsole('🚀 Initializing button test page...');
            
            // APPROACH 2: Data-driven (simplified)
            $('.warehouse-print-btn').on('click', function(e) {
                e.preventDefault();
                const orderId = $(this).data('order-id');
                logToConsole('🖨️ APPROACH 2 - Data-driven print for: ' + orderId);
                
                const printUrl = `http://127.0.0.1:5001/orders/${orderId}/print-address`;
                const printWindow = window.open(printUrl, '_blank', 'width=800,height=600');
                
                if (printWindow) {
                    logToConsole('✅ APPROACH 2 - Print window opened');
                } else {
                    alert('❌ Failed to open print window');
                }
            });
            
            $('.warehouse-pack-btn').on('click', function(e) {
                e.preventDefault();
                const orderId = $(this).data('order-id');
                logToConsole('📦 APPROACH 2 - Data-driven pack for: ' + orderId);
                
                $('#packOrderId').val(orderId);
                $('#packOrderModal').modal('show');
                logToConsole('✅ APPROACH 2 - Pack modal opened');
            });
            
            // APPROACH 3: Direct listeners
            $('.direct-print-btn').on('click', function(e) {
                e.preventDefault();
                const orderId = $(this).data('order');
                logToConsole('🖨️ APPROACH 3 - Direct print for: ' + orderId);
                
                const printUrl = `http://127.0.0.1:5001/orders/${orderId}/print-address`;
                const printWindow = window.open(printUrl, '_blank', 'width=800,height=600');
                
                if (printWindow) {
                    logToConsole('✅ APPROACH 3 - Print window opened');
                } else {
                    alert('❌ Failed to open print window');
                }
            });
            
            $('.direct-pack-btn').on('click', function(e) {
                e.preventDefault();
                const orderId = $(this).data('order');
                logToConsole('📦 APPROACH 3 - Direct pack for: ' + orderId);
                
                $('#packOrderId').val(orderId);
                $('#packOrderModal').modal('show');
                logToConsole('✅ APPROACH 3 - Pack modal opened');
            });
            
            logToConsole('✅ All button approaches initialized');
        });
    </script>
</body>
</html>
