#!/usr/bin/env python3
"""
Test the challan generation fix
"""

def test_challan_import():
    """Test that the challan function can be imported correctly"""
    try:
        print("🧪 TESTING CHALLAN IMPORT FIX")
        print("=" * 50)
        
        # Test the import
        from source_medivent_challan_generator import generate_pdf_challan
        print("✅ Successfully imported generate_pdf_challan")
        
        # Check function signature
        import inspect
        sig = inspect.signature(generate_pdf_challan)
        print(f"✅ Function signature: {sig}")
        
        # Verify it takes 4 parameters
        params = list(sig.parameters.keys())
        print(f"✅ Parameters: {params}")
        
        if len(params) == 4:
            print("✅ Correct number of parameters (4)")
            return True
        else:
            print(f"❌ Wrong number of parameters: expected 4, got {len(params)}")
            return False
            
    except Exception as e:
        print(f"❌ Import test failed: {str(e)}")
        return False

def test_order_approval_route():
    """Test that the order approval route can be accessed"""
    try:
        print("\n🧪 TESTING ORDER APPROVAL ROUTE")
        print("=" * 50)
        
        # Test import of the routes module
        from routes.orders import orders_bp
        print("✅ Successfully imported orders blueprint")
        
        # Check if the route exists
        from app import app
        with app.app_context():
            rules = list(app.url_map.iter_rules())
            approve_routes = [r for r in rules if 'approve' in r.rule and 'orders' in r.endpoint]
            
            if approve_routes:
                for route in approve_routes:
                    print(f"✅ Found approve route: {route.endpoint} → {route.rule}")
                return True
            else:
                print("❌ No approve routes found")
                return False
                
    except Exception as e:
        print(f"❌ Route test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 TESTING CHALLAN GENERATION FIX")
    print("=" * 60)
    
    success1 = test_challan_import()
    success2 = test_order_approval_route()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Challan import fixed")
        print("✅ Function signature correct")
        print("✅ Order approval route accessible")
        print("\n💡 The TypeError should now be resolved!")
    else:
        print("❌ SOME TESTS FAILED")
    print("=" * 60)
