import requests
import time

print('🔍 TESTING CHART API ENDPOINTS AFTER RESTART')
print('=' * 60)

# Wait for app to start
time.sleep(3)

base_url = 'http://127.0.0.1:5001'
endpoints = [
    '/finance/api/chart-data/division-breakdown',
    '/finance/api/chart-data/aging-breakdown', 
    '/finance/api/chart-data/customer-breakdown'
]

for endpoint in endpoints:
    try:
        response = requests.get(base_url + endpoint, timeout=10)
        print(f'\n{endpoint}:')
        print(f'  Status: HTTP {response.status_code}')
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print('  ✅ Success - Data returned')
                if 'data' in data:
                    print(f'  📊 Data keys: {list(data["data"].keys())}')
            else:
                print(f'  ❌ API Error: {data.get("error", "Unknown error")}')
        elif response.status_code == 302:
            print('  🔄 Redirect (login required)')
        else:
            print(f'  ❌ HTTP Error: {response.status_code}')
            print(f'  Response: {response.text[:200]}...')
            
    except requests.exceptions.ConnectionError:
        print(f'{endpoint}: ❌ Connection refused - App not running')
    except Exception as e:
        print(f'{endpoint}: ❌ Error: {e}')

# Test main app
print(f'\n🔍 TESTING MAIN APP:')
try:
    response = requests.get(base_url, timeout=5)
    print(f'Main app: HTTP {response.status_code}')
    if response.status_code in [200, 302]:
        print('✅ Flask app is running')
    else:
        print('⚠️ App responded but with unexpected status')
except Exception as e:
    print(f'❌ App not running: {e}')
