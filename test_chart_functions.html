<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart Function Test</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .chart-container { width: 100%; height: 400px; border: 1px solid #ccc; margin: 20px 0; }
        .test-button { padding: 10px 20px; margin: 10px; background: #007bff; color: white; border: none; cursor: pointer; }
        .test-button:hover { background: #0056b3; }
        .error { color: red; padding: 10px; background: #ffe6e6; border: 1px solid red; margin: 10px 0; }
        .success { color: green; padding: 10px; background: #e6ffe6; border: 1px solid green; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Chart Function Test</h1>
    
    <div>
        <button class="test-button" onclick="testDivisionChart()">Test Division Chart</button>
        <button class="test-button" onclick="testAgingChart()">Test Aging Chart</button>
        <button class="test-button" onclick="testCustomerChart()">Test Customer Chart</button>
        <button class="test-button" onclick="testDataFetch()">Test Data Fetch</button>
    </div>
    
    <div id="messages"></div>
    <div id="chartContainer" class="chart-container"></div>

    <script>
        // Test data
        const testDivisionData = {
            divisions: ['General', 'Aqvida', 'Pharma', 'Surgical'],
            amounts: [250000, 180000, 120000, 80000],
            order_counts: [15, 12, 8, 5],
            total_amount: 630000,
            total_orders: 40
        };

        const testAgingData = [
            {aging_bucket: '0-30 days', order_count: 15, amount: 250000, path: ['Total', '0-30 days', 'General']},
            {aging_bucket: '31-60 days', order_count: 10, amount: 180000, path: ['Total', '31-60 days', 'General']},
            {aging_bucket: '61-90 days', order_count: 8, amount: 120000, path: ['Total', '61-90 days', 'Pharma']},
            {aging_bucket: '90+ days', order_count: 5, amount: 80000, path: ['Total', '90+ days', 'Surgical']}
        ];

        const testCustomerData = [
            {customer: 'Dr Col Umair', division: 'General', order_count: 5, amount: 125000, path: ['Total', 'General', 'Dr Col Umair']},
            {customer: 'ABC Hospital', division: 'Pharma', order_count: 3, amount: 95000, path: ['Total', 'Pharma', 'ABC Hospital']},
            {customer: 'XYZ Clinic', division: 'Surgical', order_count: 2, amount: 75000, path: ['Total', 'Surgical', 'XYZ Clinic']}
        ];

        function showMessage(message, type = 'info') {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = type;
            messageDiv.textContent = message;
            messagesDiv.appendChild(messageDiv);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 5000);
        }

        function clearChart() {
            const container = document.getElementById('chartContainer');
            Plotly.purge(container);
        }

        function testDivisionChart() {
            try {
                clearChart();
                showMessage('Testing Division Chart...', 'info');
                renderDivisionSunburst(document.getElementById('chartContainer'), testDivisionData);
                showMessage('Division Chart rendered successfully!', 'success');
            } catch (error) {
                showMessage(`Division Chart Error: ${error.message}`, 'error');
                console.error('Division chart error:', error);
            }
        }

        function testAgingChart() {
            try {
                clearChart();
                showMessage('Testing Aging Chart...', 'info');
                renderAgingTreemap(document.getElementById('chartContainer'), testAgingData);
                showMessage('Aging Chart rendered successfully!', 'success');
            } catch (error) {
                showMessage(`Aging Chart Error: ${error.message}`, 'error');
                console.error('Aging chart error:', error);
            }
        }

        function testCustomerChart() {
            try {
                clearChart();
                showMessage('Testing Customer Chart...', 'info');
                renderCustomerSunburst(document.getElementById('chartContainer'), testCustomerData);
                showMessage('Customer Chart rendered successfully!', 'success');
            } catch (error) {
                showMessage(`Customer Chart Error: ${error.message}`, 'error');
                console.error('Customer chart error:', error);
            }
        }

        async function testDataFetch() {
            try {
                showMessage('Testing Data Fetch...', 'info');
                const response = await fetch('http://127.0.0.1:5001/finance/api/chart-data-test');
                const data = await response.json();
                showMessage(`Data fetch successful! Keys: ${Object.keys(data).join(', ')}`, 'success');
                console.log('Fetched data:', data);
            } catch (error) {
                showMessage(`Data Fetch Error: ${error.message}`, 'error');
                console.error('Data fetch error:', error);
            }
        }

        // Chart rendering functions (simplified versions for testing)
        function renderDivisionSunburst(container, data) {
            const trace = {
                type: 'sunburst',
                labels: ['Total', ...data.divisions],
                parents: ['', ...Array(data.divisions.length).fill('Total')],
                values: [data.total_amount, ...data.amounts],
                branchvalues: 'total',
                hovertemplate: '<b>%{label}</b><br>Amount: Rs.%{value:,.0f}<extra></extra>',
                maxdepth: 2,
                insidetextorientation: 'radial'
            };

            const layout = {
                title: 'Division Analysis Test',
                margin: { t: 50, l: 0, r: 0, b: 0 }
            };

            const config = {
                responsive: true,
                displayModeBar: true,
                displaylogo: false
            };

            return Plotly.newPlot(container, [trace], layout, config);
        }

        function renderAgingTreemap(container, data) {
            const labels = ['Total Pending'];
            const parents = [''];
            const values = [data.reduce((sum, d) => sum + d.amount, 0)];
            const colors = ['#3498db'];

            // Add aging buckets
            const agingBuckets = [...new Set(data.map(d => d.aging_bucket))];
            agingBuckets.forEach(bucket => {
                labels.push(bucket);
                parents.push('Total Pending');
                values.push(data.filter(d => d.aging_bucket === bucket).reduce((sum, d) => sum + d.amount, 0));
                colors.push(getAgingColor(bucket));
            });

            const trace = {
                type: 'treemap',
                labels: labels,
                parents: parents,
                values: values,
                textinfo: 'label+value',
                hovertemplate: '<b>%{label}</b><br>Amount: Rs.%{value:,.0f}<extra></extra>',
                marker: { colors: colors, line: { width: 2, color: 'white' } }
            };

            const layout = {
                title: 'Aging Analysis Test',
                margin: { t: 50, l: 0, r: 0, b: 0 }
            };

            const config = {
                responsive: true,
                displayModeBar: true,
                displaylogo: false
            };

            return Plotly.newPlot(container, [trace], layout, config);
        }

        function renderCustomerSunburst(container, data) {
            const labels = ['Total'];
            const parents = [''];
            const values = [data.reduce((sum, d) => sum + d.amount, 0)];

            // Add divisions
            const divisions = [...new Set(data.map(d => d.division))];
            divisions.forEach(division => {
                labels.push(division);
                parents.push('Total');
                values.push(data.filter(d => d.division === division).reduce((sum, d) => sum + d.amount, 0));
            });

            // Add customers
            data.forEach(item => {
                labels.push(item.customer);
                parents.push(item.division);
                values.push(item.amount);
            });

            const trace = {
                type: 'sunburst',
                labels: labels,
                parents: parents,
                values: values,
                branchvalues: 'total',
                hovertemplate: '<b>%{label}</b><br>Amount: Rs.%{value:,.0f}<extra></extra>',
                maxdepth: 3,
                insidetextorientation: 'radial'
            };

            const layout = {
                title: 'Customer Analysis Test',
                margin: { t: 50, l: 0, r: 0, b: 0 }
            };

            const config = {
                responsive: true,
                displayModeBar: true,
                displaylogo: false
            };

            return Plotly.newPlot(container, [trace], layout, config);
        }

        function getAgingColor(bucket, alpha = 1) {
            const colors = {
                '0-30 days': `rgba(46, 204, 113, ${alpha})`,
                '31-60 days': `rgba(243, 156, 18, ${alpha})`,
                '61-90 days': `rgba(230, 126, 34, ${alpha})`,
                '90+ days': `rgba(231, 76, 60, ${alpha})`
            };
            return colors[bucket] || `rgba(52, 73, 94, ${alpha})`;
        }

        // Auto-test on load
        window.addEventListener('load', function() {
            showMessage('Chart test page loaded. Click buttons to test individual charts.', 'info');
        });
    </script>
</body>
</html>
