import requests
import time

print('🔍 TESTING CHART API WITHOUT AUTHENTICATION')
print('=' * 60)

time.sleep(2)

base_url = 'http://127.0.0.1:5001'

# Test the no-auth version
endpoint = '/finance/api/chart-data-test'

try:
    response = requests.get(base_url + endpoint, timeout=10)
    print(f'Status: {response.status_code}')
    print(f'Content-Type: {response.headers.get("content-type", "Not set")}')
    
    if response.status_code == 200:
        try:
            data = response.json()
            print('✅ Success - JSON data returned')
            print(f'Data keys: {list(data.keys())}')
            
            # Show detailed data structure
            for key, value in data.items():
                if isinstance(value, dict):
                    print(f'\n{key}:')
                    for subkey, subvalue in value.items():
                        if isinstance(subvalue, list):
                            print(f'  {subkey}: {len(subvalue)} items')
                            if subvalue:
                                print(f'    Sample: {subvalue[0]}')
                        else:
                            print(f'  {subkey}: {subvalue}')
                else:
                    print(f'{key}: {value}')
                    
        except Exception as e:
            print(f'❌ JSON parse error: {e}')
            print(f'Response text: {response.text[:500]}...')
    else:
        print(f'❌ HTTP Error: {response.status_code}')
        print(f'Response: {response.text[:200]}...')
        
except Exception as e:
    print(f'❌ Request error: {e}')
