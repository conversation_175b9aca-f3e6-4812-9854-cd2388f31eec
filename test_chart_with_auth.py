import requests
import time

print('🔍 TESTING CHART API WITH AUTHENTICATION')
print('=' * 60)

time.sleep(2)

base_url = 'http://127.0.0.1:5001'

# Create a session and login
session = requests.Session()

# Login first
login_data = {
    'username': 'admin',
    'password': 'admin123'
}

print('🔐 Logging in...')
login_response = session.post(f'{base_url}/login', data=login_data)
print(f'Login status: {login_response.status_code}')

if login_response.status_code == 200 or login_response.status_code == 302:
    print('✅ Login successful')
    
    # Test chart APIs with authenticated session
    endpoints = [
        '/finance/api/chart-data/division-breakdown',
        '/finance/api/chart-data/aging-breakdown', 
        '/finance/api/chart-data/customer-breakdown'
    ]
    
    for endpoint in endpoints:
        try:
            response = session.get(base_url + endpoint, timeout=10)
            print(f'\n{endpoint}:')
            print(f'  Status: HTTP {response.status_code}')
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if data.get('success'):
                        print('  ✅ Success - Data returned')
                        if 'data' in data:
                            print(f'  📊 Data keys: {list(data["data"].keys())}')
                            # Show sample data
                            sample_data = data['data']
                            if isinstance(sample_data, dict):
                                for key, value in list(sample_data.items())[:3]:
                                    if isinstance(value, list) and len(value) > 0:
                                        print(f'    {key}: {len(value)} items, sample: {value[0] if value else "empty"}')
                                    else:
                                        print(f'    {key}: {value}')
                    else:
                        print(f'  ❌ API Error: {data.get("error", "Unknown error")}')
                except Exception as e:
                    print(f'  ❌ JSON parse error: {e}')
                    print(f'  Response: {response.text[:200]}...')
            else:
                print(f'  ❌ HTTP Error: {response.status_code}')
                
        except Exception as e:
            print(f'{endpoint}: ❌ Error: {e}')
else:
    print('❌ Login failed')
    print(f'Response: {login_response.text[:200]}...')
