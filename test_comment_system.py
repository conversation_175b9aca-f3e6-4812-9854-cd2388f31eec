#!/usr/bin/env python3
"""
Test the complete comment and history system
"""

import requests
import time

def test_comment_system():
    print("🧪 TESTING COMPLETE COMMENT SYSTEM")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:5001"
    
    # Test 1: Check if app is running
    print("\n1️⃣ Testing Application Status")
    print("-" * 40)
    
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            print("✅ Application is running")
        else:
            print(f"⚠️ Application responded with status {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("❌ Application is not running")
        return False
    except Exception as e:
        print(f"❌ Error connecting to application: {e}")
        return False
    
    # Test 2: Check pending invoices page
    print("\n2️⃣ Testing Pending Invoices Page")
    print("-" * 40)
    
    try:
        response = requests.get(f"{base_url}/finance/pending-invoices", timeout=10)
        if response.status_code == 200:
            print("✅ Pending invoices page loads successfully")
            if "order-notes" in response.text or "note-item" in response.text:
                print("✅ Comment sections are present in template")
            else:
                print("⚠️ Comment sections may not be visible")
        elif response.status_code == 302:
            print("🔄 Redirected (likely login required)")
        else:
            print(f"❌ Pending invoices page returned status {response.status_code}")
    except Exception as e:
        print(f"❌ Error testing pending invoices: {e}")
    
    # Test 3: Check held invoices page
    print("\n3️⃣ Testing Held Invoices Page")
    print("-" * 40)
    
    try:
        response = requests.get(f"{base_url}/finance/held-invoices", timeout=10)
        if response.status_code == 200:
            print("✅ Held invoices page loads successfully")
            if "held invoices" in response.text.lower():
                print("✅ Held invoices content is present")
            else:
                print("⚠️ Held invoices content may not be loading")
        elif response.status_code == 302:
            print("🔄 Redirected (likely login required)")
        else:
            print(f"❌ Held invoices page returned status {response.status_code}")
    except Exception as e:
        print(f"❌ Error testing held invoices: {e}")
    
    # Test 4: Check order details page
    print("\n4️⃣ Testing Order Details Page")
    print("-" * 40)
    
    try:
        # Test with a known order ID
        test_order_id = "ORD00000243"
        response = requests.get(f"{base_url}/orders/{test_order_id}", timeout=10)
        if response.status_code == 200:
            print(f"✅ Order details page loads for {test_order_id}")
            if "hold-history" in response.text or "workflow-comments" in response.text:
                print("✅ Comment and history sections are present")
            else:
                print("⚠️ Comment and history sections may not be visible")
        elif response.status_code == 302:
            print("🔄 Redirected (likely login required)")
        elif response.status_code == 404:
            print(f"⚠️ Order {test_order_id} not found")
        else:
            print(f"❌ Order details page returned status {response.status_code}")
    except Exception as e:
        print(f"❌ Error testing order details: {e}")
    
    # Test 5: Check finance dashboard
    print("\n5️⃣ Testing Finance Dashboard")
    print("-" * 40)
    
    try:
        response = requests.get(f"{base_url}/finance", timeout=10)
        if response.status_code == 200:
            print("✅ Finance dashboard loads successfully")
        elif response.status_code == 302:
            print("🔄 Redirected (likely login required)")
        else:
            print(f"❌ Finance dashboard returned status {response.status_code}")
    except Exception as e:
        print(f"❌ Error testing finance dashboard: {e}")
    
    print("\n✅ Comment system testing complete")
    print("\n📋 SUMMARY:")
    print("- Enhanced order details route to include hold/release history")
    print("- Updated pending invoices to show comments")
    print("- Added comprehensive activity logging")
    print("- Improved template display for all comment types")
    
    return True

if __name__ == "__main__":
    test_comment_system()
