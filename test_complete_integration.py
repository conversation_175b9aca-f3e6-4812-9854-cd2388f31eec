#!/usr/bin/env python3
"""
Complete integration test for order details modal
"""

import requests
import json
import time

def test_api_endpoints():
    """Test all API endpoints"""
    print("🧪 TESTING API ENDPOINTS")
    print("=" * 50)
    
    base_url = 'http://127.0.0.1:5001'
    
    # Test 1: Simple test API
    try:
        response = requests.get(f'{base_url}/api/test', timeout=10)
        print(f"1. Test API: HTTP {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success: {data.get('success', False)}")
            print(f"   📊 Message: {data.get('message', 'N/A')}")
        else:
            print(f"   ❌ Error: {response.text[:100]}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    print()
    
    # Test 2: Order details API
    try:
        response = requests.get(f'{base_url}/api/order-details/ORD00000155', timeout=10)
        print(f"2. Order Details API: HTTP {response.status_code}")
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"   ✅ Success: {data.get('success', False)}")
                if data.get('success'):
                    print(f"   📊 Order ID: {data['order']['order_id']}")
                    print(f"   📊 Customer: {data['order']['customer_name']}")
                    print(f"   📊 Items: {len(data['order_items'])}")
                    print("   ✅ API is working correctly!")
                else:
                    print(f"   ❌ API Error: {data.get('message', 'Unknown')}")
            except json.JSONDecodeError:
                print("   ❌ Response is not JSON")
                print(f"   Response: {response.text[:200]}")
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            print(f"   Response: {response.text[:200]}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    print()
    
    # Test 3: QR Code API
    try:
        response = requests.get(f'{base_url}/api/order-qr-code/ORD00000155', timeout=10)
        print(f"3. QR Code API: HTTP {response.status_code}")
        if response.status_code == 200:
            print(f"   ✅ QR Code generated successfully")
            print(f"   📊 Content-Type: {response.headers.get('content-type', 'N/A')}")
            print(f"   📊 Content-Length: {len(response.content)} bytes")
        else:
            print(f"   ❌ Error: {response.text[:100]}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")

def test_warehouse_page():
    """Test warehouse packing page"""
    print("\n🏭 TESTING WAREHOUSE PACKING PAGE")
    print("=" * 50)
    
    try:
        response = requests.get('http://127.0.0.1:5001/warehouse/packing', timeout=10)
        print(f"Warehouse Packing Page: HTTP {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Page loaded successfully")
            
            # Check if the page contains the modal and JavaScript
            content = response.text
            if 'orderDetailsModal' in content:
                print("   ✅ Order details modal found in page")
            else:
                print("   ❌ Order details modal NOT found in page")
                
            if 'order_details_modal.js' in content:
                print("   ✅ Order details JavaScript included")
            else:
                print("   ❌ Order details JavaScript NOT included")
                
            if 'viewOrderDetails' in content:
                print("   ✅ viewOrderDetails function found")
            else:
                print("   ❌ viewOrderDetails function NOT found")
                
        else:
            print(f"   ❌ Error: {response.status_code}")
            print(f"   Response: {response.text[:200]}")
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")

def main():
    """Run complete integration test"""
    print("🚀 COMPLETE INTEGRATION TEST")
    print("=" * 70)
    print("Testing order details modal functionality end-to-end")
    print("=" * 70)
    
    # Wait a moment for server to be ready
    print("⏳ Waiting for server to be ready...")
    time.sleep(2)
    
    # Test API endpoints
    test_api_endpoints()
    
    # Test warehouse page
    test_warehouse_page()
    
    print("\n" + "=" * 70)
    print("🎯 INTEGRATION TEST COMPLETE")
    print("=" * 70)
    print("\n✅ If all tests pass, the order details modal should work correctly!")
    print("📋 Next steps:")
    print("   1. Open browser to http://127.0.0.1:5001/warehouse/packing")
    print("   2. Click 'View Details' button on any order")
    print("   3. Verify modal opens with order details")

if __name__ == "__main__":
    main()
