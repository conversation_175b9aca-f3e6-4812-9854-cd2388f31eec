#!/usr/bin/env python3
"""
Comprehensive test script for all three critical error fixes:
1. JSON Serialization Issue
2. 'max' Undefined Error  
3. DateTime strftime Error
"""

import requests
import json
import sys
import time
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:5000"
TEST_RESULTS = []

def log_test(test_name, status, message=""):
    """Log test results"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    result = {
        'test': test_name,
        'status': status,
        'message': message,
        'timestamp': timestamp
    }
    TEST_RESULTS.append(result)
    
    status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
    print(f"{status_icon} [{timestamp}] {test_name}: {status}")
    if message:
        print(f"    📝 {message}")

def test_server_running():
    """Test if server is running"""
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        if response.status_code == 200:
            log_test("Server Running", "PASS", "Server is accessible")
            return True
        else:
            log_test("Server Running", "FAIL", f"Server returned {response.status_code}")
            return False
    except Exception as e:
        log_test("Server Running", "FAIL", f"Cannot connect to server: {str(e)}")
        return False

def test_rider_performance_api():
    """Test 1: JSON Serialization Issue - Rider Performance API"""
    try:
        # Test with a sample rider ID
        response = requests.get(f"{BASE_URL}/riders/api/rider/R001/performance", timeout=10)
        
        if response.status_code == 200:
            try:
                data = response.json()
                if 'error' in data:
                    log_test("Rider Performance API", "FAIL", f"API returned error: {data['error']}")
                    return False
                else:
                    log_test("Rider Performance API", "PASS", "JSON serialization working correctly")
                    return True
            except json.JSONDecodeError:
                log_test("Rider Performance API", "FAIL", "Response is not valid JSON")
                return False
        elif response.status_code == 404:
            log_test("Rider Performance API", "PASS", "API endpoint working (rider not found is expected)")
            return True
        else:
            log_test("Rider Performance API", "FAIL", f"HTTP {response.status_code}: {response.text[:100]}")
            return False
            
    except Exception as e:
        log_test("Rider Performance API", "FAIL", f"Request failed: {str(e)}")
        return False

def test_reports_max_filter():
    """Test 2: 'max' Undefined Error - Reports Templates"""
    try:
        # Test delivery report
        response = requests.get(f"{BASE_URL}/riders/reports?report_type=delivery", timeout=10)
        
        if response.status_code == 200:
            if "'max' is undefined" in response.text:
                log_test("Reports Max Filter", "FAIL", "Template still has 'max' undefined error")
                return False
            else:
                log_test("Reports Max Filter", "PASS", "Template renders without 'max' errors")
                return True
        else:
            log_test("Reports Max Filter", "FAIL", f"HTTP {response.status_code}: {response.text[:100]}")
            return False
            
    except Exception as e:
        log_test("Reports Max Filter", "FAIL", f"Request failed: {str(e)}")
        return False

def test_assignment_form_datetime():
    """Test 3: DateTime strftime Error - Assignment Form"""
    try:
        # Test assignment dashboard
        response = requests.get(f"{BASE_URL}/riders/assignment-dashboard", timeout=10)
        
        if response.status_code == 200:
            if "'str object' has no attribute 'strftime'" in response.text:
                log_test("Assignment Form DateTime", "FAIL", "Template still has strftime error")
                return False
            else:
                log_test("Assignment Form DateTime", "PASS", "DateTime handling working correctly")
                return True
        else:
            log_test("Assignment Form DateTime", "FAIL", f"HTTP {response.status_code}: {response.text[:100]}")
            return False
            
    except Exception as e:
        log_test("Assignment Form DateTime", "FAIL", f"Request failed: {str(e)}")
        return False

def test_all_rider_routes():
    """Test all rider-related routes for HTTP 200 status"""
    rider_routes = [
        "/riders/dashboard",
        "/riders/performance", 
        "/riders/tracking",
        "/riders/assignment-dashboard",
        "/riders/reports"
    ]
    
    all_passed = True
    for route in rider_routes:
        try:
            response = requests.get(f"{BASE_URL}{route}", timeout=10)
            if response.status_code == 200:
                log_test(f"Route {route}", "PASS", "HTTP 200 OK")
            else:
                log_test(f"Route {route}", "FAIL", f"HTTP {response.status_code}")
                all_passed = False
        except Exception as e:
            log_test(f"Route {route}", "FAIL", f"Request failed: {str(e)}")
            all_passed = False
    
    return all_passed

def generate_summary():
    """Generate test summary"""
    total_tests = len(TEST_RESULTS)
    passed_tests = len([r for r in TEST_RESULTS if r['status'] == 'PASS'])
    failed_tests = len([r for r in TEST_RESULTS if r['status'] == 'FAIL'])
    
    print("\n" + "="*60)
    print("🎯 COMPREHENSIVE TEST SUMMARY")
    print("="*60)
    print(f"📊 Total Tests: {total_tests}")
    print(f"✅ Passed: {passed_tests}")
    print(f"❌ Failed: {failed_tests}")
    print(f"📈 Success Rate: {(passed_tests/total_tests*100):.1f}%")
    
    if failed_tests > 0:
        print("\n❌ FAILED TESTS:")
        for result in TEST_RESULTS:
            if result['status'] == 'FAIL':
                print(f"   • {result['test']}: {result['message']}")
    
    print("\n" + "="*60)
    return failed_tests == 0

def main():
    """Run all tests"""
    print("🚀 STARTING COMPREHENSIVE ERROR FIX VERIFICATION")
    print("="*60)
    print("Testing fixes for:")
    print("1. ❌ Error loading rider performance: Object of type Row is not JSON serializable")
    print("2. ❌ Error loading reports: 'max' is undefined") 
    print("3. ❌ Error loading assignment form: 'str object' has no attribute 'strftime'")
    print("="*60)
    
    # Test server connectivity first
    if not test_server_running():
        print("\n❌ Cannot proceed - server is not running!")
        print("Please start the server with: python app.py")
        return False
    
    print("\n🔍 RUNNING SPECIFIC ERROR TESTS...")
    
    # Test the three specific errors
    test1 = test_rider_performance_api()
    test2 = test_reports_max_filter() 
    test3 = test_assignment_form_datetime()
    
    print("\n🔍 RUNNING COMPREHENSIVE ROUTE TESTS...")
    test4 = test_all_rider_routes()
    
    # Generate summary
    success = generate_summary()
    
    if success:
        print("\n🎉 ALL TESTS PASSED! The three critical errors have been fixed.")
    else:
        print("\n⚠️ Some tests failed. Please review the errors above.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
