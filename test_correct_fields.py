#!/usr/bin/env python3
"""
Test with the correct field names that the template expects
"""

import requests
import sqlite3
import time

def test_with_correct_fields():
    """Test order creation with the correct field names"""
    print("🎯 TESTING WITH CORRECT FIELD NAMES")
    print("=" * 60)
    
    base_url = "http://localhost:5001"
    session = requests.Session()
    
    try:
        # Login
        print("1. Logging in...")
        login_data = {'username': 'admin', 'password': 'admin123'}
        response = session.post(f"{base_url}/login", data=login_data, allow_redirects=False)
        
        if response.status_code != 302:
            print("❌ Login failed")
            return False
        
        print("✅ Login successful")
        
        # Get initial order count
        print("2. Getting initial order count...")
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM orders')
        initial_count = cursor.fetchone()[0]
        print(f"   Initial order count: {initial_count}")
        conn.close()
        
        # Submit order with CORRECT field names
        print("3. Submitting order with correct field names...")
        
        # Use the field names that the template expects
        order_data = {
            'customer_name': 'Correct Fields Test Customer',
            'customer_address': 'Correct Fields Test Address', 
            'customer_phone': '555-CORRECT-FIELDS',
            'payment_mode': 'cash',  # Changed from payment_method to payment_mode
            'po_number': 'CORRECT-FIELDS-001',
            'sales_agent': 'admin',  # Added required sales_agent field
            'product_id[]': ['P001'],
            'quantity[]': ['1'],
            'rate[]': ['25.50'],  # Added required rate field
            'foc_quantity[]': ['0']
        }
        
        print(f"   Form data: {order_data}")
        
        response = session.post(f"{base_url}/orders/new", 
                               data=order_data, 
                               timeout=15,
                               allow_redirects=False)
        
        print(f"   Response status: {response.status_code}")
        
        if response.status_code == 302:
            redirect_url = response.headers.get('Location', '')
            print(f"   Redirect URL: {redirect_url}")
            
            if '/orders/' in redirect_url and redirect_url != '/orders/new':
                print("✅ Order creation successful!")
                
                # Verify order was created
                time.sleep(2)
                conn = sqlite3.connect('instance/medivent.db')
                cursor = conn.cursor()
                cursor.execute('SELECT COUNT(*) FROM orders')
                final_count = cursor.fetchone()[0]
                
                cursor.execute('SELECT order_id, customer_name FROM orders ORDER BY order_date DESC LIMIT 1')
                new_order = cursor.fetchone()
                conn.close()
                
                if final_count > initial_count:
                    order_id, customer_name = new_order
                    print(f"   Order created: {order_id} - {customer_name}")
                    return True
                else:
                    print("⚠️  Redirect successful but order not found in database")
                    return False
            else:
                print("⚠️  Redirected back to form")
                
                # Follow redirect to check for errors
                response = session.get(f"{base_url}{redirect_url}", timeout=10)
                if 'alert-danger' in response.text:
                    import re
                    error_match = re.search(r'alert-danger[^>]*>([^<]+)', response.text)
                    if error_match:
                        print(f"   Error: {error_match.group(1).strip()}")
                
                return False
        else:
            print(f"   Unexpected status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_template_route_mismatch():
    """Verify that there's a mismatch between template and route expectations"""
    print("\n🔍 VERIFYING TEMPLATE-ROUTE MISMATCH")
    print("=" * 60)
    
    try:
        # Check what the blueprint route expects
        print("1. Checking blueprint route expectations...")
        from app import app
        with app.app_context():
            # Import the blueprint route
            from routes.orders import orders_bp
            
            # Check the route function
            import inspect
            from routes.orders import new_order
            source = inspect.getsource(new_order)
            
            print("   Blueprint route expects:")
            if 'payment_method' in source:
                print("   ✅ payment_method")
            if 'payment_mode' in source:
                print("   ✅ payment_mode")
            
            # Check what fields are actually used
            import re
            form_gets = re.findall(r"request\.form\.get\('([^']+)'", source)
            print(f"   Form fields used: {form_gets}")
        
        # Check what the template provides
        print("\n2. Checking template field names...")
        with open('templates/orders/new.html', 'r') as f:
            template_content = f.read()
            
            print("   Template provides:")
            if 'name="payment_method"' in template_content:
                print("   ✅ payment_method")
            if 'name="payment_mode"' in template_content:
                print("   ✅ payment_mode")
            
            # Check for required fields
            import re
            required_fields = re.findall(r'name="([^"]+)"[^>]*required', template_content)
            print(f"   Required fields in template: {required_fields}")
        
        return True
        
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False

def main():
    """Test with correct field names"""
    print("🔧 TESTING WITH CORRECT FIELD NAMES")
    print("=" * 80)
    
    # Verify the mismatch
    mismatch_verified = verify_template_route_mismatch()
    
    # Test with correct fields
    test_success = test_with_correct_fields()
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 CORRECT FIELDS TEST RESULTS")
    print("=" * 80)
    print(f"Template-Route Mismatch Verification: {'✅ SUCCESS' if mismatch_verified else '❌ FAILED'}")
    print(f"Correct Fields Test: {'✅ SUCCESS' if test_success else '❌ FAILED'}")
    
    if test_success:
        print("\n🎉 SUCCESS!")
        print("✅ Order creation working with correct field names")
        print("✅ Template and route are now aligned")
        print("✅ Web interface discrepancy RESOLVED!")
        print("\n💡 ROOT CAUSE:")
        print("   - Template expected 'payment_mode' but route expected 'payment_method'")
        print("   - Template required 'rate[]' field that wasn't being provided")
        print("   - Template required 'sales_agent' field")
        print("   - Using correct field names resolved the issue")
    else:
        print("\n❌ STILL FAILING")
        print("💡 Additional investigation needed")
        print("   The field name mismatch may not be the only issue")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
