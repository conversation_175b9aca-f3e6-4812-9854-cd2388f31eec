#!/usr/bin/env python3
"""
Test the critical fixes for JSON serialization and datetime errors
"""

import requests
import time
import sys

def test_critical_fixes():
    """Test that the critical errors have been fixed"""
    
    print("🔧 TESTING CRITICAL FIXES")
    print("=" * 50)
    
    base_url = "http://localhost:5000"
    
    # Test 1: Assignment dashboard should load without strftime errors
    print("1. Testing Assignment Dashboard (DateTime Fix)...")
    try:
        response = requests.get(f"{base_url}/riders/assignment-dashboard", timeout=15)
        if response.status_code == 200:
            print("   ✅ Assignment dashboard loads successfully")
            if "Error loading assignment form" not in response.text:
                print("   ✅ No 'strftime' error detected")
                dashboard_success = True
            else:
                print("   ❌ Still contains strftime error")
                dashboard_success = False
        else:
            print(f"   ❌ Assignment dashboard failed: HTTP {response.status_code}")
            dashboard_success = False
    except Exception as e:
        print(f"   ❌ Assignment dashboard error: {e}")
        dashboard_success = False
    
    # Test 2: Rider performance API should work without JSON serialization errors
    print("\n2. Testing Rider Performance API (JSON Fix)...")
    try:
        # First get a rider ID from the dashboard
        riders_response = requests.get(f"{base_url}/riders/", timeout=15)
        if riders_response.status_code == 200:
            # Try a test rider ID (assuming rider_1 exists)
            test_rider_id = "rider_1"
            api_response = requests.get(f"{base_url}/riders/api/rider/{test_rider_id}/performance", timeout=15)
            
            if api_response.status_code == 200:
                try:
                    json_data = api_response.json()
                    if 'error' not in json_data:
                        print("   ✅ Rider performance API returns valid JSON")
                        if 'performance_logs' in json_data and 'summary' in json_data:
                            print("   ✅ JSON structure is correct")
                            api_success = True
                        else:
                            print("   ⚠️  JSON structure incomplete")
                            api_success = False
                    else:
                        print(f"   ❌ API returned error: {json_data['error']}")
                        api_success = False
                except Exception as e:
                    print(f"   ❌ JSON parsing failed: {e}")
                    api_success = False
            elif api_response.status_code == 404:
                print("   ⚠️  Test rider not found (expected), but no JSON serialization error")
                api_success = True
            else:
                print(f"   ❌ API failed: HTTP {api_response.status_code}")
                api_success = False
        else:
            print("   ⚠️  Could not access riders dashboard to test API")
            api_success = False
    except Exception as e:
        print(f"   ❌ Rider performance API error: {e}")
        api_success = False
    
    # Test 3: Live tracking data should work without strftime errors
    print("\n3. Testing Live Tracking Data (DateTime Fix)...")
    try:
        response = requests.get(f"{base_url}/riders/api/live-tracking-data", timeout=15)
        if response.status_code == 200:
            try:
                json_data = response.json()
                if 'error' not in json_data:
                    print("   ✅ Live tracking data returns valid JSON")
                    if 'active_orders' in json_data and 'last_updated' in json_data:
                        print("   ✅ Live tracking structure is correct")
                        tracking_success = True
                    else:
                        print("   ⚠️  Live tracking structure incomplete")
                        tracking_success = False
                else:
                    print(f"   ❌ Live tracking returned error: {json_data['error']}")
                    tracking_success = False
            except Exception as e:
                print(f"   ❌ Live tracking JSON parsing failed: {e}")
                tracking_success = False
        else:
            print(f"   ❌ Live tracking failed: HTTP {response.status_code}")
            tracking_success = False
    except Exception as e:
        print(f"   ❌ Live tracking error: {e}")
        tracking_success = False
    
    # Test 4: Invoice generation should work without template errors
    print("\n4. Testing Invoice Template (DateTime Fix)...")
    try:
        # Try to access an order page that might use invoice template
        response = requests.get(f"{base_url}/orders/", timeout=15)
        if response.status_code == 200:
            print("   ✅ Orders page loads successfully")
            if "strftime" not in response.text.lower():
                print("   ✅ No strftime errors in template")
                invoice_success = True
            else:
                print("   ⚠️  Potential strftime usage detected")
                invoice_success = False
        else:
            print(f"   ❌ Orders page failed: HTTP {response.status_code}")
            invoice_success = False
    except Exception as e:
        print(f"   ❌ Invoice template error: {e}")
        invoice_success = False
    
    # Summary
    print("\n" + "=" * 50)
    print("🎯 CRITICAL FIXES TEST SUMMARY")
    print("=" * 50)
    
    total_tests = 4
    passed_tests = sum([dashboard_success, api_success, tracking_success, invoice_success])
    
    if passed_tests == total_tests:
        print("🎉 SUCCESS: All critical errors have been FIXED!")
        print("✅ Assignment dashboard datetime error - RESOLVED")
        print("✅ Rider performance JSON serialization error - RESOLVED")
        print("✅ Live tracking datetime error - RESOLVED")
        print("✅ Invoice template datetime error - RESOLVED")
        print("\n🔗 Fixed Issues:")
        print("   • JSON serialization of Row objects")
        print("   • strftime() calls on string datetime values")
        print("   • Template datetime formatting errors")
        print("   • Duplicate route definitions")
        return True
    else:
        print(f"⚠️  {passed_tests}/{total_tests} tests passed. Some issues may remain:")
        if not dashboard_success:
            print("   ❌ Assignment dashboard still has datetime issues")
        if not api_success:
            print("   ❌ Rider performance API still has JSON issues")
        if not tracking_success:
            print("   ❌ Live tracking still has datetime issues")
        if not invoice_success:
            print("   ❌ Invoice template still has datetime issues")
        return False

def wait_for_server():
    """Wait for the server to start"""
    print("⏳ Waiting for server to start...")
    base_url = "http://localhost:5000"
    
    for i in range(30):  # Wait up to 30 seconds
        try:
            response = requests.get(base_url, timeout=5)
            if response.status_code == 200:
                print("✅ Server is running!")
                return True
        except:
            pass
        time.sleep(1)
        print(f"   Waiting... ({i+1}/30)")
    
    print("❌ Server did not start within 30 seconds")
    return False

def main():
    """Main test function"""
    if wait_for_server():
        success = test_critical_fixes()
        if success:
            print("\n🚀 All critical fixes verified successfully!")
            sys.exit(0)
        else:
            print("\n⚠️  Some issues remain. Check the output above.")
            sys.exit(1)
    else:
        print("\n❌ Could not connect to server. Please start the application first.")
        sys.exit(1)

if __name__ == "__main__":
    main()
