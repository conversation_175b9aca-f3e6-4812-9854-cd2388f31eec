#!/usr/bin/env python3
"""
Test the specific dashboard error
"""

import requests
import time

def test_dashboard_route():
    """Test the dashboard route that's causing the error"""
    
    print("🧪 TESTING DASHBOARD ROUTE ERROR")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    # Wait for server
    print("⏳ Waiting for server...")
    time.sleep(3)
    
    # Test the specific route that's failing
    test_routes = [
        "/dashboard",
        "/",
        "/riders/",
        "/riders/dashboard"
    ]
    
    for route in test_routes:
        print(f"\n🎯 Testing: {route}")
        print("-" * 30)
        
        try:
            response = requests.get(f"{base_url}{route}", timeout=10)
            
            if response.status_code == 200:
                print(f"✅ HTTP 200 - Route accessible")
                
                # Check for BuildError in response
                content = response.text.lower()
                if "builderror" in content:
                    print("❌ BuildError found in response!")
                    
                    # Extract error details
                    if "could not build url for endpoint" in content:
                        print("🔍 Error details found in response")
                        
                        # Find the specific error line
                        lines = response.text.split('\n')
                        for i, line in enumerate(lines):
                            if "could not build url for endpoint" in line.lower():
                                print(f"📍 Error line: {line.strip()}")
                                # Print surrounding lines for context
                                for j in range(max(0, i-2), min(len(lines), i+3)):
                                    marker = ">>> " if j == i else "    "
                                    print(f"{marker}{j+1}: {lines[j]}")
                                break
                else:
                    print("✅ No BuildError found in response")
                    
            elif response.status_code == 500:
                print(f"❌ HTTP 500 - Server Error")
                print("🔍 This likely indicates the BuildError is occurring")
                
            else:
                print(f"⚠️ HTTP {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print("❌ Connection Error - Server may not be running")
        except requests.exceptions.Timeout:
            print("❌ Timeout")
        except Exception as e:
            print(f"❌ Error: {e}")

def check_server_status():
    """Check if server is running"""
    
    print("\n🔍 CHECKING SERVER STATUS")
    print("=" * 50)
    
    try:
        response = requests.get("http://127.0.0.1:5000", timeout=5)
        print(f"✅ Server is running (HTTP {response.status_code})")
        return True
    except:
        print("❌ Server is not responding")
        return False

def main():
    """Main function"""
    
    # Check server status first
    if not check_server_status():
        print("\n❌ Cannot test - server is not running")
        print("Please start the Flask server first with: python app.py")
        return False
    
    # Test dashboard routes
    test_dashboard_route()
    
    print("\n📊 TEST COMPLETE")
    print("=" * 50)
    
    return True

if __name__ == "__main__":
    main()
