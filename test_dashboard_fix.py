#!/usr/bin/env python3
"""
Test if the dashboard loads without the BuildError
"""

import requests

def test_dashboard_fix():
    """Test that the dashboard loads without BuildError"""
    try:
        print("🧪 TESTING DASHBOARD FIX")
        print("=" * 50)
        
        url = "http://192.168.99.34:5001/"
        print(f"Testing dashboard URL: {url}")
        
        response = requests.get(url, timeout=10)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Dashboard loads successfully!")
            
            # Check for BuildError in response
            if "BuildError" in response.text:
                print("❌ BuildError still present in response")
                return False
            else:
                print("✅ No BuildError found")
                
            # Check if it's the login page or actual dashboard
            if "Login - Medivent Pharmaceuticals" in response.text:
                print("ℹ️ Redirected to login page (expected if not logged in)")
            elif "Dashboard" in response.text:
                print("✅ Dashboard page loaded")
            
            return True
        else:
            print(f"❌ Dashboard failed to load: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False

def test_view_all_products():
    """Test the view all products route directly"""
    try:
        print("\n🧪 TESTING VIEW ALL PRODUCTS ROUTE")
        print("=" * 50)
        
        url = "http://192.168.99.34:5001/products/view_all/"
        print(f"Testing view all URL: {url}")
        
        response = requests.get(url, timeout=10)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ View all products route works!")
            return True
        else:
            print(f"❌ View all products failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 TESTING BUILDERROR FIX")
    print("=" * 60)
    
    success1 = test_dashboard_fix()
    success2 = test_view_all_products()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 ALL TESTS PASSED!")
        print("✅ BuildError has been resolved")
        print("✅ Dashboard loads correctly")
        print("✅ View all products route works")
    else:
        print("❌ SOME TESTS FAILED")
    print("=" * 60)
