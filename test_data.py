import requests

try:
    response = requests.get('http://127.0.0.1:5001/orders/ORD175397491316416F32/history-test', timeout=10)
    print(f'Status: {response.status_code}')
    
    if response.status_code == 200:
        data = response.json()
        print('✅ Data fetched successfully!')
        print(f'Order ID: {data["order_id"]}')
        print(f'Status: {data["status"]}')
        print(f'Customer: {data["customer_name"]}')
        print(f'Sales Agent: {data["sales_agent"]}')
        print(f'Rejected By: {data["rejected_by"]}')
        print(f'Rejection Notes: {data["rejection_notes"]}')
        print(f'Order Items: {data["order_items_count"]}')
        print(f'Activity Logs: {data["activity_logs_count"]}')
        
        print('\nProducts:')
        for item in data['order_items']:
            print(f'  - {item["product_name"]} {item["strength"]} (Qty: {item["quantity"]})')
            
        print('\nActivities:')
        for log in data['activity_logs']:
            print(f'  - {log["action"]} by {log["username"]} at {log["timestamp"]}')
    else:
        print(f'Error: {response.status_code}')
        print(response.text[:200])
        
except Exception as e:
    print(f'Error: {e}')
