#!/usr/bin/env python3
"""
Test Database Import Fix - Comprehensive Testing
"""

import os
import sys
import sqlite3
from datetime import datetime

# Add project root to path
sys.path.insert(0, '.')

def test_database_import():
    """Test importing database functions"""
    print("🧪 Testing Database Import...")
    
    try:
        # Test importing from database.py (backward compatibility)
        from database import get_db
        print("  ✅ Successfully imported get_db from database module")
        
        # Test importing from utils.db (centralized)
        from utils.db import get_db as get_db_utils, get_db_direct, test_db_connection
        print("  ✅ Successfully imported from utils.db module")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Import error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_connection():
    """Test database connection functionality"""
    print("\n🧪 Testing Database Connection...")
    
    try:
        from utils.db import test_db_connection, get_db_direct
        
        # Test connection status
        success, message = test_db_connection()
        print(f"  Connection test: {message}")
        
        if success:
            # Test direct connection
            db = get_db_direct()
            cursor = db.execute("SELECT COUNT(*) as count FROM orders")
            order_count = cursor.fetchone()['count']
            print(f"  ✅ Orders table accessible: {order_count} orders found")
            db.close()
            return True
        else:
            return False
        
    except Exception as e:
        print(f"  ❌ Connection error: {e}")
        return False

def test_flask_context_import():
    """Test importing get_db within Flask context"""
    print("\n🧪 Testing Flask Context Import...")
    
    try:
        from app import app
        
        with app.app_context():
            # Test importing from database module
            from database import get_db
            print("  ✅ Successfully imported get_db in Flask context")
            
            # Test using the function
            db = get_db()
            cursor = db.execute("SELECT COUNT(*) as count FROM products")
            product_count = cursor.fetchone()['count']
            print(f"  ✅ Products table accessible: {product_count} products found")
            
            return True
        
    except Exception as e:
        print(f"  ❌ Flask context error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_order_routes_import():
    """Test importing order routes with fixed database"""
    print("\n🧪 Testing Order Routes Import...")
    
    try:
        from app import app
        
        with app.app_context():
            # Test importing order routes
            from routes.orders import generate_order_id
            print("  ✅ Successfully imported generate_order_id from routes.orders")
            
            # Test the function
            order_id = generate_order_id()
            print(f"  ✅ Generated order ID: {order_id}")
            
            # Test orders_minimal import
            from routes.orders_minimal import generate_order_id as gen_id_minimal
            order_id_minimal = gen_id_minimal()
            print(f"  ✅ Generated order ID (minimal): {order_id_minimal}")
            
            return True
        
    except Exception as e:
        print(f"  ❌ Order routes error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_order_creation_workflow():
    """Test complete order creation workflow"""
    print("\n🧪 Testing Order Creation Workflow...")
    
    try:
        from app import app
        
        with app.app_context():
            from database import get_db
            
            db = get_db()
            
            # Test order sequence table creation
            db.execute('''
                CREATE TABLE IF NOT EXISTS order_sequence (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Test generating order ID
            cursor = db.execute('INSERT INTO order_sequence DEFAULT VALUES')
            sequence_id = cursor.lastrowid
            order_id = f"ORD{sequence_id:08d}"
            print(f"  ✅ Generated sequence order ID: {order_id}")
            
            # Test inserting a test order
            db.execute('''
                INSERT INTO orders (
                    order_id, customer_name, customer_address, customer_phone,
                    payment_method, status, sales_agent, updated_by, order_date, last_updated
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                order_id, "Test Customer Import Fix", "Test Address", "123456789",
                "cash", "Placed", "test_user", "test_user", datetime.now(), datetime.now()
            ))
            
            db.commit()
            print(f"  ✅ Successfully created test order: {order_id}")
            
            # Clean up test order
            db.execute("DELETE FROM orders WHERE order_id = ?", (order_id,))
            db.commit()
            print("  🧹 Cleaned up test order")
            
            return True
        
    except Exception as e:
        print(f"  ❌ Order creation error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_concurrent_order_creation():
    """Test concurrent order creation with fixed imports"""
    print("\n🧪 Testing Concurrent Order Creation...")
    
    try:
        from app import app
        import concurrent.futures
        
        def create_test_order(thread_id):
            with app.app_context():
                from database import get_db
                
                db = get_db()
                
                # Generate order ID using sequence
                cursor = db.execute('INSERT INTO order_sequence DEFAULT VALUES')
                sequence_id = cursor.lastrowid
                order_id = f"ORD{sequence_id:08d}"
                
                # Insert order
                db.execute('''
                    INSERT INTO orders (
                        order_id, customer_name, customer_address, customer_phone,
                        payment_method, status, sales_agent, updated_by, order_date, last_updated
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    order_id, f"Concurrent Test {thread_id}", "Test Address", "123456789",
                    "cash", "Placed", "test_user", "test_user", datetime.now(), datetime.now()
                ))
                
                db.commit()
                return {'success': True, 'order_id': order_id, 'thread': thread_id}
        
        # Run concurrent tests
        results = []
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(create_test_order, i) for i in range(10)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        successful = [r for r in results if r['success']]
        print(f"  ✅ Successfully created {len(successful)}/10 concurrent orders")
        
        # Clean up test orders
        with app.app_context():
            from database import get_db
            db = get_db()
            cursor = db.execute("DELETE FROM orders WHERE customer_name LIKE 'Concurrent Test %'")
            deleted = cursor.rowcount
            db.commit()
            print(f"  🧹 Cleaned up {deleted} test orders")
        
        return len(successful) == 10
        
    except Exception as e:
        print(f"  ❌ Concurrent creation error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main testing function"""
    print("🔍 DATABASE IMPORT FIX TESTING")
    print("=" * 50)
    
    tests = [
        ("Database Import", test_database_import),
        ("Database Connection", test_database_connection),
        ("Flask Context Import", test_flask_context_import),
        ("Order Routes Import", test_order_routes_import),
        ("Order Creation Workflow", test_order_creation_workflow),
        ("Concurrent Order Creation", test_concurrent_order_creation),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}")
        print("-" * len(test_name))
        success = test_func()
        results.append((test_name, success))
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"Result: {status}")
    
    print("\n" + "=" * 50)
    print("📊 DATABASE IMPORT FIX SUMMARY")
    print("=" * 50)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name:<30} | {status}")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    if passed == total:
        print(f"\n🎉 ALL TESTS PASSED ({passed}/{total})")
        print("✅ DATABASE IMPORT ISSUE IS FIXED!")
    else:
        print(f"\n⚠️  SOME TESTS FAILED ({passed}/{total})")
        print("❌ Additional fixes may be needed")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
