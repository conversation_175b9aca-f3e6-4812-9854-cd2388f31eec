#!/usr/bin/env python3
"""
Test the datetime strftime fix for assignment dashboard
"""

import requests
import time

def test_assignment_dashboard_fix():
    """Test that the assignment dashboard loads without strftime errors"""
    
    print("🔧 TESTING DATETIME STRFTIME FIX")
    print("=" * 50)
    
    base_url = "http://localhost:5000"
    
    # Test 1: Assignment dashboard should load without errors
    print("1. Testing Assignment Dashboard Route...")
    try:
        response = requests.get(f"{base_url}/riders/assignment-dashboard", timeout=15)
        
        if response.status_code == 200:
            print("   ✅ Assignment dashboard loads successfully (HTTP 200)")
            
            # Check if the response contains expected content
            content = response.text.lower()
            
            # Check for key elements that should be present
            checks = [
                ("assignment dashboard", "Assignment Dashboard title"),
                ("orders ready for assignment", "Orders section"),
                ("available riders", "Riders section"),
                ("packed_at", "Datetime field present"),
            ]
            
            for check_text, description in checks:
                if check_text in content:
                    print(f"   ✅ {description} found")
                else:
                    print(f"   ⚠️  {description} not found")
            
            # Check for error indicators
            error_indicators = [
                "strftime",
                "attributeerror",
                "error loading assignment dashboard",
                "500 internal server error"
            ]
            
            has_errors = False
            for error_text in error_indicators:
                if error_text in content:
                    print(f"   ❌ Error indicator found: {error_text}")
                    has_errors = True
            
            if not has_errors:
                print("   ✅ No error indicators found in response")
                
            return response.status_code == 200 and not has_errors
            
        else:
            print(f"   ❌ Assignment dashboard returned {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"   💥 Error accessing assignment dashboard: {e}")
        return False

def test_datetime_template_filter():
    """Test the datetime template filter functionality"""
    
    print("\n2. Testing Template Filter Functionality...")
    
    # Test the main riders page which should also use datetime formatting
    base_url = "http://localhost:5000"
    
    try:
        response = requests.get(f"{base_url}/riders/dashboard", timeout=10)
        
        if response.status_code == 200:
            print("   ✅ Professional dashboard loads (template filters working)")
            return True
        else:
            print(f"   ❌ Professional dashboard returned {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   💥 Error testing template filters: {e}")
        return False

def main():
    """Run all datetime fix tests"""
    
    print("🚀 Starting Datetime Fix Verification...")
    time.sleep(1)
    
    # Test assignment dashboard
    dashboard_success = test_assignment_dashboard_fix()
    
    # Test template filters
    filter_success = test_datetime_template_filter()
    
    print("\n" + "=" * 50)
    print("📊 DATETIME FIX RESULTS:")
    
    if dashboard_success and filter_success:
        print("🎉 SUCCESS: Datetime strftime error has been FIXED!")
        print("✅ Assignment dashboard loads without errors")
        print("✅ Template filters working correctly")
        print("✅ Datetime formatting is now safe and robust")
        print("\n🔗 Fixed Issues:")
        print("   • strftime() error on string datetime values")
        print("   • Template crashes due to datetime formatting")
        print("   • Assignment dashboard accessibility")
    else:
        print("⚠️  Some issues remain:")
        if not dashboard_success:
            print("   ❌ Assignment dashboard still has issues")
        if not filter_success:
            print("   ❌ Template filter issues detected")

if __name__ == "__main__":
    main()
