#!/usr/bin/env python3
import sqlite3

try:
    # Connect to database
    db = sqlite3.connect('instance/medivent.db')
    db.row_factory = sqlite3.Row
    
    # Test basic query
    cursor = db.execute('SELECT COUNT(*) as count FROM products')
    total = cursor.fetchone()['count']
    print(f"Total products: {total}")
    
    # Test active products
    cursor = db.execute('SELECT COUNT(*) as count FROM products WHERE is_active = 1')
    active = cursor.fetchone()['count']
    print(f"Active products: {active}")
    
    # Test sample product data
    cursor = db.execute('SELECT id, product_id, name, status, is_active FROM products LIMIT 3')
    products = cursor.fetchall()
    print("\nSample products:")
    for product in products:
        print(f"  ID: {product['id']}, Product ID: {product['product_id']}, Name: {product['name']}, Status: {product['status']}, Active: {product['is_active']}")
    
    db.close()
    print("\nDatabase test completed successfully!")
    
except Exception as e:
    print(f"Database test failed: {e}")
