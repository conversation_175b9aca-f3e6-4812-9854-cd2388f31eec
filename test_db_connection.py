#!/usr/bin/env python3
import sqlite3
import os

print("🔍 Testing database connection...")

# Check if database file exists
db_path = 'instance/medivent.db'
if os.path.exists(db_path):
    print(f"✅ Database file exists: {db_path}")
    file_size = os.path.getsize(db_path)
    print(f"   File size: {file_size} bytes")
else:
    print(f"❌ Database file not found: {db_path}")
    exit(1)

# Try to connect
try:
    conn = sqlite3.connect(db_path, timeout=5.0)
    print("✅ Database connection successful")
    
    # Quick test query
    cursor = conn.cursor()
    cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
    table_count = cursor.fetchone()[0]
    print(f"✅ Found {table_count} tables in database")
    
    # Check for invoice_holds table specifically
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='invoice_holds'")
    invoice_holds_exists = cursor.fetchone()
    if invoice_holds_exists:
        print("✅ invoice_holds table exists")
        
        # Quick count
        cursor.execute("SELECT COUNT(*) FROM invoice_holds")
        count = cursor.fetchone()[0]
        print(f"   Records in invoice_holds: {count}")
    else:
        print("❌ invoice_holds table does NOT exist")
    
    conn.close()
    print("✅ Database test completed successfully")
    
except sqlite3.OperationalError as e:
    print(f"❌ Database error: {e}")
    exit(1)
except Exception as e:
    print(f"❌ Unexpected error: {e}")
    exit(1)
