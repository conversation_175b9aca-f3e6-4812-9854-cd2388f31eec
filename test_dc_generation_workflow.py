#!/usr/bin/env python3
"""
Test DC Generation Workflow - Comprehensive Testing
"""

import sqlite3
import json
import os
from datetime import datetime

def test_dc_generation_workflow():
    """Test the complete DC generation workflow"""
    
    print("🧪 TESTING DC GENERATION WORKFLOW")
    print("=" * 60)
    
    # Check if database exists
    db_path = 'instance/medivent.db'
    if not os.path.exists(db_path):
        print(f"❌ Database not found at: {db_path}")
        return False
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        order_id = 'ORD175411154600DAC554'
        
        print(f"\n🔍 STEP 1: VERIFY ORDER EXISTS")
        print("-" * 40)
        
        # Check order
        order = cursor.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
        if not order:
            print(f"❌ Order {order_id} not found")
            print("🔧 Creating test order...")
            cursor.execute('''
                INSERT OR REPLACE INTO orders (order_id, customer_name, customer_phone, customer_address, order_date, order_amount, status)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (order_id, 'Test Customer', '1234567890', 'Test Address', datetime.now(), 1000.0, 'Approved'))
            print(f"✅ Created test order: {order_id}")
        else:
            print(f"✅ Order found: {order['customer_name']} - Status: {order['status']}")
        
        print(f"\n🔍 STEP 2: VERIFY ORDER ITEMS")
        print("-" * 40)
        
        # Check order items
        items = cursor.execute('SELECT * FROM order_items WHERE order_id = ?', (order_id,)).fetchall()
        if not items:
            print("❌ No order items found")
            print("🔧 Creating test order items...")
            
            # Get some products
            products = cursor.execute('SELECT product_id FROM products LIMIT 2').fetchall()
            if not products:
                print("❌ No products found - creating test products...")
                cursor.execute('''
                    INSERT OR REPLACE INTO products (product_id, name, strength, manufacturer, unit_price, status, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', ('PROD001', 'Test Medicine A', '500mg', 'Test Pharma', 10.0, 'active', 1))
                cursor.execute('''
                    INSERT OR REPLACE INTO products (product_id, name, strength, manufacturer, unit_price, status, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', ('PROD002', 'Test Medicine B', '250mg', 'Test Pharma', 15.0, 'active', 1))
                products = [{'product_id': 'PROD001'}, {'product_id': 'PROD002'}]
            
            # Create order items
            for i, product in enumerate(products[:2]):
                item_id = f"ITEM{order_id}{i+1:03d}"
                cursor.execute('''
                    INSERT OR REPLACE INTO order_items (order_item_id, order_id, product_id, quantity, unit_price, line_total)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (item_id, order_id, product['product_id'], 50, 10.0, 500.0))
            print(f"✅ Created test order items")
        else:
            print(f"✅ Found {len(items)} order items")
        
        print(f"\n🔍 STEP 3: VERIFY INVENTORY")
        print("-" * 40)
        
        # Get order items again
        items = cursor.execute('SELECT * FROM order_items WHERE order_id = ?', (order_id,)).fetchall()
        
        for item in items:
            product_id = item['product_id']
            required_qty = item['quantity']
            
            # Check inventory
            inventory = cursor.execute('''
                SELECT inventory_id, batch_number, stock_quantity, 
                       COALESCE(allocated_quantity, 0) as allocated_quantity,
                       (stock_quantity - COALESCE(allocated_quantity, 0)) as available_qty
                FROM inventory 
                WHERE product_id = ? AND status = 'active'
                ORDER BY manufacturing_date ASC
            ''', (product_id,)).fetchall()
            
            if inventory:
                total_available = sum(inv['available_qty'] for inv in inventory)
                print(f"✅ Product {product_id}: {total_available} available (need {required_qty})")
            else:
                print(f"❌ No inventory for {product_id} - creating test inventory...")
                
                # Create test inventory
                inv_id = f"INV{datetime.now().strftime('%Y%m%d%H%M%S')}{product_id[-3:]}"
                cursor.execute('''
                    INSERT OR REPLACE INTO inventory (
                        inventory_id, product_id, batch_number, warehouse_id, 
                        stock_quantity, allocated_quantity, status, manufacturing_date, expiry_date
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    inv_id, product_id, f"BATCH{datetime.now().strftime('%Y%m')}", 'WH001',
                    required_qty + 20, 0, 'active', '2024-01-01', '2026-01-01'
                ))
                print(f"✅ Created test inventory: {inv_id}")
        
        print(f"\n🔍 STEP 4: VERIFY WAREHOUSES")
        print("-" * 40)
        
        # Check warehouses
        warehouses = cursor.execute('SELECT * FROM warehouses WHERE status = "active"').fetchall()
        if not warehouses:
            print("❌ No active warehouses - creating test warehouse...")
            cursor.execute('''
                INSERT OR REPLACE INTO warehouses (warehouse_id, name, city, status)
                VALUES (?, ?, ?, ?)
            ''', ('WH001', 'Main Warehouse', 'Karachi', 'active'))
            print("✅ Created test warehouse: WH001")
        else:
            print(f"✅ Found {len(warehouses)} active warehouses")
        
        print(f"\n🔍 STEP 5: TEST BATCH SELECTION DATA")
        print("-" * 40)
        
        # Test batch selection data retrieval
        items = cursor.execute('''
            SELECT oi.*, p.name as product_name, p.strength
            FROM order_items oi
            JOIN products p ON oi.product_id = p.product_id
            WHERE oi.order_id = ?
            ORDER BY p.name
        ''', (order_id,)).fetchall()
        
        if not items:
            print("❌ No order items found in join query")
            return False
        
        print(f"✅ Order items query successful: {len(items)} items")
        
        # Create test batch selections
        batch_selections = {}
        
        for item in items:
            product_id = item['product_id']
            required_qty = item['quantity']
            
            # Get inventory for this product
            inventory = cursor.execute('''
                SELECT inventory_id, stock_quantity, 
                       COALESCE(allocated_quantity, 0) as allocated_quantity,
                       (stock_quantity - COALESCE(allocated_quantity, 0)) as available_qty
                FROM inventory 
                WHERE product_id = ? AND status = 'active'
                AND (stock_quantity - COALESCE(allocated_quantity, 0)) > 0
                ORDER BY manufacturing_date ASC
            ''', (product_id,)).fetchall()
            
            if inventory:
                batch_selections[product_id] = []
                remaining = required_qty
                
                for inv in inventory:
                    if remaining <= 0:
                        break
                    
                    allocate_qty = min(remaining, inv['available_qty'])
                    if allocate_qty > 0:
                        batch_selections[product_id].append({
                            'inventory_id': inv['inventory_id'],
                            'quantity': allocate_qty
                        })
                        remaining -= allocate_qty
                
                print(f"✅ Created batch selection for {product_id}: {len(batch_selections[product_id])} batches")
            else:
                print(f"❌ No available inventory for {product_id}")
        
        print(f"\n🔍 STEP 6: TEST DC GENERATION")
        print("-" * 40)
        
        if batch_selections:
            print(f"Batch selections: {json.dumps(batch_selections, indent=2)}")
            
            # Test the DC generation logic manually
            try:
                # Check if delivery_challans table exists
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='delivery_challans'")
                if not cursor.fetchone():
                    print("❌ delivery_challans table missing - creating...")
                    cursor.execute('''
                        CREATE TABLE delivery_challans (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            dc_number TEXT UNIQUE NOT NULL,
                            order_id TEXT NOT NULL,
                            warehouse_id TEXT,
                            customer_name TEXT,
                            status TEXT DEFAULT 'created',
                            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            created_by TEXT,
                            total_items INTEGER DEFAULT 0,
                            total_amount DECIMAL(10,2) DEFAULT 0.00,
                            batch_details TEXT,
                            FOREIGN KEY (order_id) REFERENCES orders (order_id)
                        )
                    ''')
                    print("✅ Created delivery_challans table")
                
                # Check if stock_movements table exists
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='stock_movements'")
                if not cursor.fetchone():
                    print("❌ stock_movements table missing - creating...")
                    cursor.execute('''
                        CREATE TABLE stock_movements (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            inventory_id TEXT NOT NULL,
                            movement_type TEXT NOT NULL,
                            quantity INTEGER NOT NULL,
                            reference_type TEXT,
                            reference_id TEXT,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            created_by TEXT,
                            FOREIGN KEY (inventory_id) REFERENCES inventory (inventory_id)
                        )
                    ''')
                    print("✅ Created stock_movements table")
                
                print("✅ All required tables exist")
                print("✅ Batch selections created successfully")
                print("✅ Ready for DC generation")
                
            except Exception as e:
                print(f"❌ Error in DC generation test: {e}")
                return False
        else:
            print("❌ No batch selections created")
            return False
        
        # Commit all test data
        conn.commit()
        conn.close()
        
        print(f"\n🎯 TEST SUMMARY")
        print("=" * 60)
        print("✅ Order verification completed")
        print("✅ Order items verification completed")
        print("✅ Inventory verification completed")
        print("✅ Warehouse verification completed")
        print("✅ Batch selection data creation completed")
        print("✅ Database tables verification completed")
        
        print(f"\n🚀 READY FOR TESTING")
        print("-" * 40)
        print("1. Navigate to batch selection page")
        print("2. Test FIFO allocation")
        print("3. Test partial DC generation")
        print("4. Verify error handling")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_dc_generation_workflow()
    if success:
        print("\n🎉 TEST SETUP COMPLETED SUCCESSFULLY!")
        print("The system is ready for DC generation testing.")
    else:
        print("\n💥 TEST SETUP FAILED")
        print("Please check errors above and fix issues.")
