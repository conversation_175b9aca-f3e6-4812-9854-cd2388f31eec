#!/usr/bin/env python3
"""
Test script to verify DC generation and invoice workflow fixes
"""

import sqlite3
import sys
import os

def test_database_connection():
    """Test database connection and basic schema"""
    try:
        conn = sqlite3.connect('instance/medivent.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        print("✅ Database connection successful")
        
        # Check required tables exist
        tables = ['orders', 'delivery_challans', 'invoices', 'order_items']
        for table in tables:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            if cursor.fetchone():
                print(f"✅ Table '{table}' exists")
            else:
                print(f"❌ Table '{table}' missing")
                return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def test_order_data():
    """Test if the specific order exists and check its status"""
    try:
        conn = sqlite3.connect('instance/medivent.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Check the specific order mentioned by user
        order_id = 'ORD1753983391CA9E99E1'
        cursor.execute('SELECT order_id, status, customer_name FROM orders WHERE order_id = ?', (order_id,))
        order = cursor.fetchone()
        
        if order:
            print(f"✅ Order {order_id} found")
            print(f"   Status: {order['status']}")
            print(f"   Customer: {order['customer_name']}")
            
            # Check if DC exists
            cursor.execute('SELECT dc_number FROM delivery_challans WHERE order_id = ?', (order_id,))
            dc = cursor.fetchone()

            if dc:
                print(f"✅ DC exists: {dc['dc_number']}")
            else:
                print(f"⚠️  No DC found for order {order_id}")

            # Check if invoice exists
            cursor.execute('SELECT invoice_number FROM invoices WHERE order_id = ?', (order_id,))
            invoice = cursor.fetchone()
            
            if invoice:
                print(f"✅ Invoice exists: {invoice['invoice_number']}")
            else:
                print(f"⚠️  No invoice found for order {order_id}")
                
            return True
        else:
            print(f"❌ Order {order_id} not found")
            
            # Show sample orders
            cursor.execute('SELECT order_id, status FROM orders LIMIT 5')
            orders = cursor.fetchall()
            print("Sample orders in database:")
            for order in orders:
                print(f"   {order['order_id']}: {order['status']}")
            
            return False
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error checking order data: {e}")
        return False

def test_route_mappings():
    """Test if the route mappings are correct in templates"""
    try:
        # Check order view template
        with open('templates/orders/view.html', 'r') as f:
            content = f.read()
            
        if 'dc_generation.batch_selection' in content:
            print("✅ Order view template uses correct DC generation route")
        else:
            print("❌ Order view template still uses old route")
            
        if 'finance_generate_invoice_get' in content:
            print("✅ Order view template uses correct invoice generation route")
        else:
            print("❌ Order view template missing invoice generation route")
            
        # Check workflow template
        with open('templates/orders/workflow.html', 'r') as f:
            workflow_content = f.read()
            
        if 'dc_generation.batch_selection' in workflow_content:
            print("✅ Workflow template uses correct DC generation route")
        else:
            print("❌ Workflow template still uses old route")
            
        # Check warehouse template
        with open('templates/warehouse/warehouses.html', 'r') as f:
            warehouse_content = f.read()
            
        if '/batch-selection' in warehouse_content:
            print("✅ Warehouse template uses correct batch selection URL")
        else:
            print("❌ Warehouse template still uses old URL")
            
        return True
        
    except Exception as e:
        print(f"❌ Error checking route mappings: {e}")
        return False

def test_invoice_logic():
    """Test the invoice generation logic"""
    try:
        # Check if the new GET route exists in app.py
        with open('app.py', 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
            
        if 'finance_generate_invoice_get' in content:
            print("✅ New GET route for invoice generation exists")
        else:
            print("❌ New GET route for invoice generation missing")
            
        if 'delivery_challans WHERE order_id' in content:
            print("✅ Invoice generation checks for DC existence")
        else:
            print("❌ Invoice generation missing DC validation")
            
        return True
        
    except Exception as e:
        print(f"❌ Error checking invoice logic: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 TESTING DC GENERATION AND INVOICE WORKFLOW FIXES")
    print("=" * 60)
    
    tests = [
        ("Database Connection", test_database_connection),
        ("Order Data", test_order_data),
        ("Route Mappings", test_route_mappings),
        ("Invoice Logic", test_invoice_logic)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Testing {test_name}...")
        if test_func():
            passed += 1
            print(f"✅ {test_name} PASSED")
        else:
            print(f"❌ {test_name} FAILED")
    
    print("\n" + "=" * 60)
    print(f"🎯 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! The fixes are working correctly.")
    else:
        print("⚠️  Some tests failed. Please review the issues above.")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
