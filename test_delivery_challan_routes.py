#!/usr/bin/env python3
"""
🧪 COMPREHENSIVE DELIVERY CHALLAN ROUTES TESTING SCRIPT
Tests all delivery challan related routes and verifies HTTP 200 responses
"""

import requests
import sqlite3
import sys
from datetime import datetime

# Configuration
BASE_URL = "http://127.0.0.1:5000"
DB_PATH = "medivent.db"

def test_database_connection():
    """Test database connectivity and get sample data"""
    print("🔍 Testing Database Connection...")
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Check if delivery_challans table exists and has data
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='delivery_challans'")
        if not cursor.fetchone():
            print("❌ delivery_challans table not found!")
            return None, None
            
        # Get sample delivery challan
        cursor.execute("SELECT dc_number, order_id FROM delivery_challans LIMIT 1")
        sample_dc = cursor.fetchone()
        
        if sample_dc:
            print(f"✅ Found sample DC: {sample_dc[0]} (Order: {sample_dc[1]})")
            return sample_dc[0], sample_dc[1]
        else:
            print("⚠️ No delivery challans found in database")
            return None, None
            
    except Exception as e:
        print(f"❌ Database error: {e}")
        return None, None
    finally:
        if 'conn' in locals():
            conn.close()

def test_route(route_name, url, expected_status=200):
    """Test a single route and return result"""
    print(f"🧪 Testing {route_name}: {url}")
    try:
        response = requests.get(url, timeout=10)
        status = response.status_code
        
        if status == expected_status:
            print(f"✅ {route_name}: HTTP {status} - SUCCESS")
            return True
        else:
            print(f"❌ {route_name}: HTTP {status} - FAILED (expected {expected_status})")
            print(f"   Response: {response.text[:200]}...")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ {route_name}: CONNECTION ERROR - {e}")
        return False

def main():
    """Main testing function"""
    print("🚀 DELIVERY CHALLAN ROUTES TESTING")
    print("=" * 50)
    print(f"Base URL: {BASE_URL}")
    print(f"Database: {DB_PATH}")
    print(f"Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test database first
    sample_dc_number, sample_order_id = test_database_connection()
    print()
    
    # Define all routes to test
    routes_to_test = [
        {
            'name': 'Delivery Challans List (Main App)',
            'url': f'{BASE_URL}/delivery_challans',
            'description': 'Lists all delivery challans - main app route'
        },
        {
            'name': 'DC Generation List (Blueprint)',
            'url': f'{BASE_URL}/delivery-challans',
            'description': 'Lists all DCs - blueprint route'
        }
    ]
    
    # Add specific DC routes if we have sample data
    if sample_dc_number:
        routes_to_test.extend([
            {
                'name': 'View Delivery Challan (Main App)',
                'url': f'{BASE_URL}/delivery_challans/{sample_dc_number}/view',
                'description': f'View specific DC {sample_dc_number} - main app route'
            },
            {
                'name': 'View DC (Blueprint)',
                'url': f'{BASE_URL}/delivery-challans/{sample_dc_number}/view',
                'description': f'View specific DC {sample_dc_number} - blueprint route'
            }
        ])
    
    # Test all routes
    results = []
    for route in routes_to_test:
        print(f"📋 {route['description']}")
        success = test_route(route['name'], route['url'])
        results.append({
            'name': route['name'],
            'success': success,
            'url': route['url']
        })
        print()
    
    # Summary
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for r in results if r['success'])
    total = len(results)
    
    for result in results:
        status = "✅ PASS" if result['success'] else "❌ FAIL"
        print(f"{status} - {result['name']}")
    
    print()
    print(f"🎯 OVERALL RESULT: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Delivery challan routes are working correctly.")
        return 0
    else:
        print("⚠️ SOME TESTS FAILED! Please check the failed routes.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
