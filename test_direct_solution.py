#!/usr/bin/env python3
"""
Test the direct solution for order details
"""

import requests
import time

def test_warehouse_page():
    """Test if the warehouse page loads with the updated JavaScript"""
    try:
        print("🧪 Testing warehouse page with updated JavaScript...")
        
        response = requests.get('http://127.0.0.1:5001/warehouse/packing', timeout=10)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text
            
            # Check for key components
            checks = [
                ('Order Details Modal', 'orderDetailsModal' in content),
                ('JavaScript File', 'order_details_modal.js' in content),
                ('View Details Function', 'viewOrderDetails' in content),
                ('Show Order Details Function', 'showOrderDetails' in content),
                ('Extract Table Data Function', 'extractOrderDataFromTable' in content)
            ]
            
            print("\n📋 Component Checks:")
            for name, passed in checks:
                status = "✅" if passed else "❌"
                print(f"   {status} {name}")
            
            # Check if all components are present
            all_passed = all(check[1] for check in checks)
            
            if all_passed:
                print("\n✅ All components are present!")
                print("📋 The order details modal should now work with table data extraction.")
                print("\n🎯 Next steps:")
                print("   1. Refresh the browser page")
                print("   2. Click 'View Details' on any order")
                print("   3. The modal should show order details extracted from the table")
            else:
                print("\n❌ Some components are missing")
                
        else:
            print(f"❌ Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def test_api_fallback():
    """Test if API is working as fallback"""
    try:
        print("\n🧪 Testing API fallback...")
        
        response = requests.get('http://127.0.0.1:5001/api/test', timeout=5)
        print(f"Test API Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Test API working: {data.get('message', 'N/A')}")
        else:
            print(f"❌ Test API failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ API test error: {e}")

if __name__ == "__main__":
    print("🚀 TESTING DIRECT ORDER DETAILS SOLUTION")
    print("=" * 60)
    
    test_warehouse_page()
    test_api_fallback()
    
    print("\n" + "=" * 60)
    print("🎯 SOLUTION SUMMARY")
    print("=" * 60)
    print("✅ Updated JavaScript to extract order data directly from table")
    print("✅ Added fallback to API if table extraction fails")
    print("✅ Added backward compatibility for viewOrderDetails function")
    print("\n📋 The modal should now show order details even if API fails!")
