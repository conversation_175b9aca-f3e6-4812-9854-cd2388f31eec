#!/usr/bin/env python3
"""
Test script for Enhanced Order Modal with QR Code Integration
Validates all components and functionality
"""

import os
import sys
import json
import requests
from datetime import datetime

def test_file_structure():
    """Test if all required files exist"""
    print("🔍 TESTING FILE STRUCTURE")
    print("=" * 50)
    
    required_files = [
        'templates/components/enhanced_order_modal.html',
        'static/css/enhanced_modal.css',
        'static/js/enhanced_modal.js',
        'utils/qr_code_generator.py',
        'templates/examples/enhanced_modal_integration.html'
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️ Missing {len(missing_files)} files")
        return False
    else:
        print(f"\n🎉 All {len(required_files)} files found!")
        return True

def test_qr_dependencies():
    """Test QR code dependencies"""
    print("\n🔧 TESTING QR CODE DEPENDENCIES")
    print("=" * 50)
    
    try:
        import qrcode
        print("✅ qrcode library available")
        
        from PIL import Image
        print("✅ PIL/Pillow library available")
        
        # Test basic QR generation
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data("Test QR Code")
        qr.make(fit=True)
        img = qr.make_image(fill_color="black", back_color="white")
        print("✅ QR code generation working")
        
        return True
        
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("💡 Install with: pip install qrcode[pil] pillow")
        return False
    except Exception as e:
        print(f"❌ QR generation error: {e}")
        return False

def test_qr_generator_module():
    """Test the QR generator module"""
    print("\n🎯 TESTING QR GENERATOR MODULE")
    print("=" * 50)
    
    try:
        # Add utils to path if needed
        if 'utils' not in sys.path:
            sys.path.append('utils')
        
        from qr_code_generator import OrderQRCodeGenerator, generate_order_qr_code
        print("✅ QR generator module imported successfully")
        
        # Test with sample data
        sample_order_data = {
            'order': {
                'order_id': 'TEST001',
                'order_date_formatted': '2025-08-05 10:30',
                'status': 'Completed',
                'order_amount': 1500.00,
                'customer_name': 'Test Customer',
                'customer_phone': '+92-300-1234567',
                'customer_address': 'Test Address, Karachi'
            },
            'items': [
                {
                    'product_name': 'Paracetamol 500mg',
                    'strength': '500mg',
                    'quantity': 10,
                    'unit_price': 25.0,
                    'manufacturer': 'Test Pharma'
                },
                {
                    'product_name': 'Aspirin 100mg',
                    'strength': '100mg',
                    'quantity': 5,
                    'unit_price': 15.0,
                    'manufacturer': 'Test Pharma'
                }
            ],
            'foc_items': [
                {
                    'product_name': 'Vitamin C',
                    'strength': '1000mg',
                    'quantity': 2
                }
            ],
            'customer': {
                'city': 'Karachi',
                'phone': '+92-300-1234567'
            },
            'summary': {
                'total_items': 15,
                'item_types': 2,
                'foc_items_count': 2,
                'total_amount': 1500.00
            }
        }
        
        # Test QR generation
        generator = OrderQRCodeGenerator()
        result = generator.generate_order_qr_code(sample_order_data, include_branding=True)
        
        if result.get('success'):
            print("✅ QR code generated successfully")
            qr_data = result['qr_code']
            print(f"   📁 File saved: {qr_data.get('file_path', 'N/A')}")
            print(f"   📊 File size: {qr_data.get('size', 0)} bytes")
            print(f"   🔗 Base64 length: {len(qr_data.get('base64', ''))}")
            return True
        else:
            print(f"❌ QR generation failed: {result.get('error', 'Unknown error')}")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ QR generator test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_css_structure():
    """Test CSS file structure and key classes"""
    print("\n🎨 TESTING CSS STRUCTURE")
    print("=" * 50)
    
    css_file = 'static/css/enhanced_modal.css'
    if not os.path.exists(css_file):
        print(f"❌ CSS file not found: {css_file}")
        return False
    
    try:
        with open(css_file, 'r', encoding='utf-8') as f:
            css_content = f.read()
        
        required_classes = [
            '.modern-modal',
            '.gradient-header',
            '.info-card',
            '.qr-code-container',
            '.loading-container',
            '.error-container',
            '@media (max-width: 768px)',  # Responsive design
            '@media print'  # Print styles
        ]
        
        missing_classes = []
        for css_class in required_classes:
            if css_class in css_content:
                print(f"✅ {css_class}")
            else:
                print(f"❌ {css_class}")
                missing_classes.append(css_class)
        
        if missing_classes:
            print(f"\n⚠️ Missing {len(missing_classes)} CSS classes")
            return False
        else:
            print(f"\n🎉 All {len(required_classes)} CSS classes found!")
            return True
            
    except Exception as e:
        print(f"❌ Error reading CSS file: {e}")
        return False

def test_javascript_structure():
    """Test JavaScript file structure and key functions"""
    print("\n⚡ TESTING JAVASCRIPT STRUCTURE")
    print("=" * 50)
    
    js_file = 'static/js/enhanced_modal.js'
    if not os.path.exists(js_file):
        print(f"❌ JavaScript file not found: {js_file}")
        return False
    
    try:
        with open(js_file, 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        required_functions = [
            'class EnhancedOrderModal',
            'showOrderDetails',
            'loadOrderData',
            'loadQRCode',
            'populateOrderDetails',
            'displayQRCode',
            'downloadQRCode',
            'printQRCode',
            'showEnhancedOrderDetails'  # Global function
        ]
        
        missing_functions = []
        for func in required_functions:
            if func in js_content:
                print(f"✅ {func}")
            else:
                print(f"❌ {func}")
                missing_functions.append(func)
        
        if missing_functions:
            print(f"\n⚠️ Missing {len(missing_functions)} JavaScript functions")
            return False
        else:
            print(f"\n🎉 All {len(required_functions)} JavaScript functions found!")
            return True
            
    except Exception as e:
        print(f"❌ Error reading JavaScript file: {e}")
        return False

def test_html_template():
    """Test HTML template structure"""
    print("\n📄 TESTING HTML TEMPLATE")
    print("=" * 50)
    
    html_file = 'templates/components/enhanced_order_modal.html'
    if not os.path.exists(html_file):
        print(f"❌ HTML template not found: {html_file}")
        return False
    
    try:
        with open(html_file, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        required_elements = [
            'id="enhancedOrderModal"',
            'id="modalLoadingState"',
            'id="modalErrorState"',
            'id="modalMainContent"',
            'id="qrCodeDisplay"',
            'id="qrCodeImage"',
            'id="modalOrderItems"',
            'id="modalFocItems"',
            'onclick="downloadQRCode()"',
            'onclick="printQRCode()"'
        ]
        
        missing_elements = []
        for element in required_elements:
            if element in html_content:
                print(f"✅ {element}")
            else:
                print(f"❌ {element}")
                missing_elements.append(element)
        
        if missing_elements:
            print(f"\n⚠️ Missing {len(missing_elements)} HTML elements")
            return False
        else:
            print(f"\n🎉 All {len(required_elements)} HTML elements found!")
            return True
            
    except Exception as e:
        print(f"❌ Error reading HTML file: {e}")
        return False

def test_integration_example():
    """Test integration example"""
    print("\n🔗 TESTING INTEGRATION EXAMPLE")
    print("=" * 50)
    
    example_file = 'templates/examples/enhanced_modal_integration.html'
    if not os.path.exists(example_file):
        print(f"❌ Integration example not found: {example_file}")
        return False
    
    try:
        with open(example_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_content = [
            'showEnhancedOrderDetails',
            'enhanced_modal.css',
            'enhanced_modal.js',
            'enhanced_order_modal.html',
            'Integration Guide'
        ]
        
        missing_content = []
        for item in required_content:
            if item in content:
                print(f"✅ {item}")
            else:
                print(f"❌ {item}")
                missing_content.append(item)
        
        if missing_content:
            print(f"\n⚠️ Missing {len(missing_content)} integration elements")
            return False
        else:
            print(f"\n🎉 All {len(required_content)} integration elements found!")
            return True
            
    except Exception as e:
        print(f"❌ Error reading integration example: {e}")
        return False

def generate_test_report():
    """Generate comprehensive test report"""
    print("\n" + "=" * 60)
    print("📊 ENHANCED ORDER MODAL - TEST REPORT")
    print("=" * 60)
    
    tests = [
        ("File Structure", test_file_structure),
        ("QR Dependencies", test_qr_dependencies),
        ("QR Generator Module", test_qr_generator_module),
        ("CSS Structure", test_css_structure),
        ("JavaScript Structure", test_javascript_structure),
        ("HTML Template", test_html_template),
        ("Integration Example", test_integration_example)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 OVERALL RESULT: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Enhanced Order Modal is ready for use.")
        print("\n📝 NEXT STEPS:")
        print("1. Install QR dependencies: pip install qrcode[pil] pillow")
        print("2. Include the modal in your templates")
        print("3. Add CSS and JavaScript files")
        print("4. Replace viewOrderDetails() calls with showEnhancedOrderDetails()")
        print("5. Test with real order data")
        return True
    else:
        print(f"⚠️ {total - passed} tests failed. Please fix issues before deployment.")
        return False

def main():
    """Main test function"""
    print("🚀 ENHANCED ORDER MODAL VALIDATION")
    print("⏰ Starting comprehensive tests...\n")
    
    success = generate_test_report()
    
    print(f"\n{'='*60}")
    print("🏁 TESTING COMPLETE")
    print(f"{'='*60}")
    
    return success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
