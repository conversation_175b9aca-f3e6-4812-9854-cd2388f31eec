#!/usr/bin/env python3
"""
Final Modal Test - Verify order details modal works
"""

import requests

def test_modal():
    """Test the modal functionality"""
    print("🧪 FINAL MODAL TEST")
    print("=" * 40)
    
    try:
        response = requests.get('http://127.0.0.1:5001/warehouse/packing', timeout=10)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text
            
            # Key checks
            checks = [
                ('Modal Found', 'id="orderDetailsModal"' in content),
                ('Modal Structure', 'modal-dialog modal-xl' in content),
                ('Loading State', 'modalLoadingState' in content),
                ('Content Area', 'modalContent' in content),
                ('Order Display', 'orderIdDisplay' in content),
                ('Customer Display', 'customerNameDisplay' in content),
                ('Items Table', 'orderItemsTableBody' in content),
                ('JavaScript Function', 'function viewOrderDetails' in content),
                ('View Details Button', 'viewOrderDetails(' in content)
            ]
            
            print("\nComponent Check:")
            all_good = True
            for name, check in checks:
                found = check
                status = "✅" if found else "❌"
                print(f"   {status} {name}")
                if not found:
                    all_good = False
            
            if all_good:
                print("\n🎉 ALL COMPONENTS FOUND!")
                print("\n📋 READY TO TEST:")
                print("   1. Open: http://127.0.0.1:5001/warehouse/packing")
                print("   2. Click 'View Details' on any order")
                print("   3. Modal should show order details")
                print("   4. Check browser console for debug messages")
                return True
            else:
                print("\n❌ Some components missing")
                return False
        else:
            print(f"❌ Page failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_modal()
    if success:
        print("\n✅ MODAL IS READY TO TEST!")
    else:
        print("\n❌ Modal has issues")
