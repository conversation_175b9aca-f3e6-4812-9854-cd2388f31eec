#!/usr/bin/env python3
"""
Final Order ID Fix Testing - Complete Application Testing
"""

import sqlite3
import os
import time
import requests
import json
from datetime import datetime
import concurrent.futures

def test_order_id_generation():
    """Test the new order ID generation function"""
    print("🧪 Testing New Order ID Generation...")
    
    try:
        # Import the fixed function
        import sys
        sys.path.append('routes')
        from routes.orders import generate_order_id
        
        # Test generating multiple IDs
        ids = []
        for i in range(20):
            order_id = generate_order_id()
            ids.append(order_id)
            print(f"  Generated: {order_id}")
        
        # Check uniqueness
        unique_ids = set(ids)
        print(f"\n  ✅ Generated: {len(ids)}")
        print(f"  ✅ Unique: {len(unique_ids)}")
        print(f"  ❌ Duplicates: {len(ids) - len(unique_ids)}")
        
        return len(ids) == len(unique_ids)
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def test_database_sequence_table():
    """Test the order_sequence table creation and functionality"""
    print("\n🧪 Testing Order Sequence Table...")
    
    db_path = 'instance/medivent.db'
    if not os.path.exists(db_path):
        print("  ❌ Database not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if sequence table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='order_sequence'")
        table_exists = cursor.fetchone() is not None
        
        if table_exists:
            print("  ✅ order_sequence table exists")
            
            # Check table structure
            cursor.execute("PRAGMA table_info(order_sequence)")
            columns = cursor.fetchall()
            print(f"  ✅ Table structure: {[col[1] for col in columns]}")
            
            # Check current sequence value
            cursor.execute("SELECT MAX(id) FROM order_sequence")
            max_id = cursor.fetchone()[0]
            print(f"  ✅ Current sequence: {max_id}")
            
        else:
            print("  ⚠️  order_sequence table does not exist yet")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def test_concurrent_order_creation():
    """Test concurrent order creation with new system"""
    print("\n🧪 Testing Concurrent Order Creation...")
    
    db_path = 'instance/medivent.db'
    if not os.path.exists(db_path):
        print("  ❌ Database not found")
        return False
    
    def create_test_order(thread_id):
        try:
            # Import here to avoid issues
            import sys
            sys.path.append('routes')
            from routes.orders import generate_order_id
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Generate order ID using new system
            order_id = generate_order_id()
            
            # Try to insert
            cursor.execute('''
                INSERT INTO orders (
                    order_id, customer_name, customer_address, customer_phone,
                    payment_method, status, sales_agent, updated_by, order_date, last_updated
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                order_id, f"Test Customer {thread_id}", "Test Address", "123456789",
                "cash", "Placed", "test_user", "test_user", datetime.now(), datetime.now()
            ))
            
            conn.commit()
            conn.close()
            
            return {'success': True, 'order_id': order_id, 'thread': thread_id}
            
        except sqlite3.IntegrityError as e:
            if 'UNIQUE constraint failed' in str(e):
                return {'success': False, 'error': 'UNIQUE_CONSTRAINT', 'thread': thread_id}
            else:
                return {'success': False, 'error': str(e), 'thread': thread_id}
        except Exception as e:
            return {'success': False, 'error': str(e), 'thread': thread_id}
    
    # Run concurrent tests
    results = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        futures = [executor.submit(create_test_order, i) for i in range(20)]
        results = [future.result() for future in concurrent.futures.as_completed(futures)]
    
    # Analyze results
    successful = [r for r in results if r['success']]
    failed = [r for r in results if not r['success']]
    unique_failures = [r for r in failed if r.get('error') == 'UNIQUE_CONSTRAINT']
    
    print(f"  ✅ Successful orders: {len(successful)}")
    print(f"  ❌ Failed orders: {len(failed)}")
    print(f"  🔒 UNIQUE constraint failures: {len(unique_failures)}")
    
    if len(successful) > 0:
        print(f"  📝 Sample successful IDs: {[r['order_id'] for r in successful[:3]]}")
    
    if len(unique_failures) > 0:
        print(f"  ⚠️  UNIQUE constraint failures detected!")
        for failure in unique_failures:
            print(f"    Thread {failure['thread']}: {failure['error']}")
    
    # Clean up test orders
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("DELETE FROM orders WHERE customer_name LIKE 'Test Customer %'")
        deleted = cursor.rowcount
        conn.commit()
        conn.close()
        print(f"  🧹 Cleaned up {deleted} test orders")
    except Exception as e:
        print(f"  ⚠️  Cleanup error: {e}")
    
    return len(unique_failures) == 0

def test_flask_app_startup():
    """Test Flask application startup"""
    print("\n🧪 Testing Flask Application Startup...")
    
    try:
        # Test importing the app
        from app import app
        print("  ✅ Flask app imported successfully")
        
        # Test basic functionality
        with app.test_client() as client:
            response = client.get('/')
            print(f"  ✅ Root route status: {response.status_code}")
            
            # Test orders route
            response = client.get('/orders/')
            print(f"  ✅ Orders route status: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def test_order_creation_simulation():
    """Simulate order creation through the application"""
    print("\n🧪 Testing Order Creation Simulation...")
    
    try:
        from app import app
        
        with app.test_client() as client:
            # Simulate order creation POST request
            order_data = {
                'customer_name': 'Test Customer Simulation',
                'customer_address': 'Test Address',
                'customer_phone': '123456789',
                'payment_method': 'cash',
                'product_id[]': ['P001'],
                'quantity[]': ['10']
            }
            
            # Note: This will likely fail due to authentication, but we can check the error
            response = client.post('/orders/new', data=order_data)
            print(f"  ✅ Order creation response status: {response.status_code}")
            
            # Check if it's a redirect (which is expected for login)
            if response.status_code in [302, 401]:
                print("  ✅ Expected redirect/auth response (normal behavior)")
                return True
            else:
                print(f"  ⚠️  Unexpected response: {response.status_code}")
                return False
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def main():
    """Main testing function"""
    print("🔍 FINAL ORDER ID FIX VERIFICATION")
    print("=" * 50)
    
    tests = [
        ("Order ID Generation", test_order_id_generation),
        ("Database Sequence Table", test_database_sequence_table),
        ("Concurrent Order Creation", test_concurrent_order_creation),
        ("Flask App Startup", test_flask_app_startup),
        ("Order Creation Simulation", test_order_creation_simulation),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}")
        print("-" * len(test_name))
        success = test_func()
        results.append((test_name, success))
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"\nResult: {status}")
    
    print("\n" + "=" * 50)
    print("📊 FINAL TEST SUMMARY")
    print("=" * 50)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name:<30} | {status}")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    if passed == total:
        print(f"\n🎉 ALL TESTS PASSED ({passed}/{total})")
        print("✅ ORDER ID UNIQUE CONSTRAINT ISSUE IS FIXED!")
    else:
        print(f"\n⚠️  SOME TESTS FAILED ({passed}/{total})")
        print("❌ Additional fixes may be needed")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
