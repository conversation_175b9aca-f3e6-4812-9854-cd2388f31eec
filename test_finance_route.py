#!/usr/bin/env python3
"""
Test the finance route directly
"""
import sqlite3
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_finance_route():
    """Test the finance route logic"""
    db_path = 'instance/medivent.db'
    
    if not os.path.exists(db_path):
        print(f"Database file {db_path} not found!")
        return
    
    try:
        db = sqlite3.connect(db_path)
        db.row_factory = sqlite3.Row
        
        # Import the ModernFinanceManager from app.py
        from app import ModernFinanceManager
        
        finance_manager = ModernFinanceManager(db)
        
        print("=== TESTING get_pending_invoices_enhanced ===")
        orders = finance_manager.get_pending_invoices_enhanced()
        
        print(f"Found {len(orders)} pending invoices:")
        for order in orders:
            print(f"  Order {order['order_id']}: {order['customer_name']} - Rs.{order['order_amount']} - Status: {order['status']} - Priority: {order['priority_level']}")
        
        print("\n=== TESTING get_invoice_generation_chart_data ===")
        try:
            chart_data = finance_manager.get_invoice_generation_chart_data()
            print("Chart data retrieved successfully:")
            print(f"  Daily labels: {chart_data['daily']['labels']}")
            print(f"  Daily data: {chart_data['daily']['data']}")
            print(f"  Status data: {chart_data.get('status', {})}")
        except Exception as e:
            print(f"Error getting chart data: {e}")

        print("\n=== TESTING get_pending_invoices_summary ===")
        try:
            summary = finance_manager.get_pending_invoices_summary()
            print("Summary statistics retrieved successfully:")
            print(f"  Pending Orders: {summary['pending_orders']}")
            print(f"  Pending Amount: Rs.{summary['pending_amount']}")
            print(f"  Generated Today: {summary['generated_today']}")
            print(f"  Orders on Hold: {summary['on_hold']}")
        except Exception as e:
            print(f"Error getting summary: {e}")

        db.close()
        
    except Exception as e:
        print(f"Error testing finance route: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_finance_route()
