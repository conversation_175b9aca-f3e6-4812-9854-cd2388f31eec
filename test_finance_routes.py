#!/usr/bin/env python3
"""
Comprehensive testing script for all finance routes
"""

import requests
import time
import sys

def test_finance_routes():
    """Test all finance routes and verify HTTP 200 responses"""
    
    base_url = "http://127.0.0.1:5001"
    
    # List of finance routes to test
    finance_routes = [
        "/finance",
        "/finance/",
        "/finance/dashboard",
        "/finance/pending-invoices",
        "/finance/held-invoices", 
        "/finance/payment-collection",
        "/finance/invoice-generation",
        "/finance/payment-history",
        "/finance/analytics",
        "/test-debug"  # Our test route
    ]
    
    print("🔍 FINANCE ROUTES TESTING")
    print("=" * 60)
    print(f"🌐 Base URL: {base_url}")
    print(f"📋 Testing {len(finance_routes)} routes")
    print("=" * 60)
    
    results = []
    
    for route in finance_routes:
        url = f"{base_url}{route}"
        print(f"\n🔗 Testing: {url}")
        
        try:
            # Make request with timeout
            response = requests.get(url, timeout=10, allow_redirects=True)
            
            status_code = response.status_code
            content_length = len(response.content)
            
            if status_code == 200:
                print(f"✅ SUCCESS - Status: {status_code}, Content: {content_length} bytes")
                results.append((route, "SUCCESS", status_code, content_length))
            elif status_code == 302:
                print(f"🔄 REDIRECT - Status: {status_code}, Location: {response.headers.get('Location', 'Unknown')}")
                results.append((route, "REDIRECT", status_code, response.headers.get('Location', 'Unknown')))
            elif status_code == 404:
                print(f"❌ NOT FOUND - Status: {status_code}")
                results.append((route, "NOT_FOUND", status_code, ""))
            elif status_code == 500:
                print(f"💥 SERVER ERROR - Status: {status_code}")
                # Try to get error details
                error_text = response.text[:200] if response.text else "No error details"
                print(f"   Error preview: {error_text}")
                results.append((route, "SERVER_ERROR", status_code, error_text))
            else:
                print(f"⚠️ UNEXPECTED - Status: {status_code}")
                results.append((route, "UNEXPECTED", status_code, ""))
                
        except requests.exceptions.ConnectionError:
            print(f"🔌 CONNECTION ERROR - Server not running or unreachable")
            results.append((route, "CONNECTION_ERROR", 0, ""))
        except requests.exceptions.Timeout:
            print(f"⏰ TIMEOUT - Request took too long")
            results.append((route, "TIMEOUT", 0, ""))
        except Exception as e:
            print(f"💥 EXCEPTION - {str(e)}")
            results.append((route, "EXCEPTION", 0, str(e)))
        
        # Small delay between requests
        time.sleep(0.5)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TESTING SUMMARY")
    print("=" * 60)
    
    success_count = sum(1 for r in results if r[1] == "SUCCESS")
    redirect_count = sum(1 for r in results if r[1] == "REDIRECT")
    error_count = len(results) - success_count - redirect_count
    
    print(f"✅ Successful: {success_count}")
    print(f"🔄 Redirects: {redirect_count}")
    print(f"❌ Errors: {error_count}")
    
    if error_count > 0:
        print("\n❌ FAILED ROUTES:")
        for route, status, code, details in results:
            if status not in ["SUCCESS", "REDIRECT"]:
                print(f"   {route} - {status} ({code})")
                if details:
                    print(f"      Details: {details}")
    
    if success_count > 0:
        print("\n✅ SUCCESSFUL ROUTES:")
        for route, status, code, details in results:
            if status == "SUCCESS":
                print(f"   {route} - {code} ({details} bytes)")
    
    if redirect_count > 0:
        print("\n🔄 REDIRECT ROUTES:")
        for route, status, code, details in results:
            if status == "REDIRECT":
                print(f"   {route} - {code} -> {details}")
    
    return results

if __name__ == "__main__":
    print("🚀 Starting Finance Routes Test...")
    print("⏰ Please ensure the Flask app is running on port 5001")
    print("⏰ Waiting 3 seconds before starting tests...")
    time.sleep(3)
    
    results = test_finance_routes()
    
    # Exit with appropriate code
    error_count = sum(1 for r in results if r[1] not in ["SUCCESS", "REDIRECT"])
    if error_count == 0:
        print("\n🎉 All tests completed successfully!")
        sys.exit(0)
    else:
        print(f"\n💥 {error_count} tests failed!")
        sys.exit(1)
