#!/usr/bin/env python3
"""
Comprehensive testing script for finance routes and invoice generation
"""

import requests
import time
import sys
import json

def test_finance_routes():
    """Test all finance routes and verify HTTP 200 responses"""
    
    base_url = "http://127.0.0.1:5001"
    
    # List of finance routes to test
    finance_routes = [
        "/finance",
        "/finance/dashboard", 
        "/finance/pending-invoices",
        "/finance/held-invoices",
        "/finance/payment-collection",
        "/finance/customer-ledger",
        "/finance/analytics"
    ]
    
    print("🔍 COMPREHENSIVE FINANCE ROUTES TESTING")
    print("=" * 60)
    print(f"🌐 Base URL: {base_url}")
    print(f"📋 Testing {len(finance_routes)} routes")
    print("=" * 60)
    
    results = []
    
    for route in finance_routes:
        print(f"\n🧪 Testing: {route}")
        try:
            response = requests.get(f"{base_url}{route}", timeout=10)
            status_code = response.status_code
            
            if status_code == 200:
                print(f"   ✅ SUCCESS - HTTP {status_code}")
                results.append({'route': route, 'status': 'PASS', 'code': status_code})
            else:
                print(f"   ❌ FAILED - HTTP {status_code}")
                results.append({'route': route, 'status': 'FAIL', 'code': status_code})
                
        except requests.exceptions.RequestException as e:
            print(f"   💥 ERROR - {str(e)}")
            results.append({'route': route, 'status': 'ERROR', 'error': str(e)})
        
        time.sleep(1)  # Brief pause between requests
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = len([r for r in results if r['status'] == 'PASS'])
    failed = len([r for r in results if r['status'] == 'FAIL'])
    errors = len([r for r in results if r['status'] == 'ERROR'])
    
    print(f"✅ PASSED: {passed}")
    print(f"❌ FAILED: {failed}")
    print(f"💥 ERRORS: {errors}")
    print(f"📈 SUCCESS RATE: {(passed/len(results)*100):.1f}%")
    
    if failed > 0 or errors > 0:
        print("\n🚨 FAILED/ERROR ROUTES:")
        for result in results:
            if result['status'] != 'PASS':
                print(f"   {result['route']}: {result['status']} - {result.get('code', result.get('error', 'Unknown'))}")
    
    return results

def test_invoice_generation_api():
    """Test the specific invoice generation API that was failing"""
    
    print("\n" + "=" * 60)
    print("🧾 TESTING INVOICE GENERATION API")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:5001"
    
    # Test data for invoice generation
    test_data = {
        "order_id": "ORD000000246",  # From the error screenshot
        "customer_name": "Test Customer",
        "order_amount": 255,
        "finance_user_approved": True,
        "timestamp": "2025-01-08T10:00:00Z"
    }
    
    try:
        print(f"📤 Sending POST request to /finance/api/generate-invoice")
        print(f"📋 Test data: {json.dumps(test_data, indent=2)}")
        
        response = requests.post(
            f"{base_url}/finance/api/generate-invoice",
            json=test_data,
            headers={
                'Content-Type': 'application/json',
                'X-Finance-User-Action': 'true'
            },
            timeout=30
        )
        
        print(f"\n📥 Response Status: HTTP {response.status_code}")
        
        if response.status_code == 200:
            print("✅ SUCCESS - Invoice generation API working!")
            try:
                response_data = response.json()
                print(f"📋 Response: {json.dumps(response_data, indent=2)}")
            except:
                print("📋 Response: (Non-JSON response)")
        else:
            print(f"❌ FAILED - HTTP {response.status_code}")
            print(f"📋 Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"💥 ERROR - {str(e)}")
        return False
    
    return response.status_code == 200

def main():
    """Main testing function"""
    print("🚀 STARTING COMPREHENSIVE FINANCE TESTING")
    print("⏰ " + time.strftime("%Y-%m-%d %H:%M:%S"))
    
    # Wait for server to be ready
    print("\n⏳ Waiting for server to be ready...")
    time.sleep(3)
    
    # Test basic routes
    route_results = test_finance_routes()
    
    # Test specific invoice generation API
    invoice_result = test_invoice_generation_api()
    
    # Final summary
    print("\n" + "=" * 60)
    print("🎯 FINAL TEST SUMMARY")
    print("=" * 60)
    
    route_success = len([r for r in route_results if r['status'] == 'PASS']) == len(route_results)
    
    if route_success and invoice_result:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Finance routes working correctly")
        print("✅ Invoice generation API fixed")
        return True
    else:
        print("⚠️ SOME TESTS FAILED")
        if not route_success:
            print("❌ Some finance routes failed")
        if not invoice_result:
            print("❌ Invoice generation API still has issues")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
