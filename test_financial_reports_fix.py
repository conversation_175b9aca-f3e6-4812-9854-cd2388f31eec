#!/usr/bin/env python3
"""
Test that the financial reports fix resolves the original error
"""

def test_financial_reports_fix():
    """Test the specific financial reports functionality that was failing"""
    print("🧪 TESTING FINANCIAL REPORTS FIX")
    print("=" * 60)
    
    try:
        import sqlite3
        from datetime import datetime, timedelta
        
        db = sqlite3.connect('instance/medivent.db')
        db.row_factory = sqlite3.Row
        
        print("🔍 Testing the exact queries that were failing...")
        
        # 1. Test the reconciliation query from advanced_payment.py line 205-207
        print("\n1️⃣ Testing reconciliation query...")
        try:
            today = datetime.now().date()
            month_start = today.replace(day=1)
            
            system_total = db.execute(
                "SELECT COALESCE(SUM(amount), 0) as total FROM payments WHERE DATE(payment_date) >= ?",
                (month_start,)
            ).fetchone()['total']
            
            print(f"   ✅ Reconciliation query successful: Rs. {system_total}")
            
        except Exception as e:
            print(f"   ❌ Reconciliation query failed: {e}")
            return False
        
        # 2. Test payment methods breakdown query from line 210-216
        print("\n2️⃣ Testing payment methods breakdown...")
        try:
            payment_methods = db.execute(
                """SELECT payment_method, COUNT(*) as count, SUM(amount) as total
                   FROM payments
                   WHERE DATE(payment_date) >= ?
                   GROUP BY payment_method
                   ORDER BY total DESC""",
                (month_start,)
            ).fetchall()
            
            print(f"   ✅ Payment methods query successful: {len(payment_methods)} methods found")
            for method in payment_methods:
                print(f"      • {method[0]}: {method[1]} payments, Rs. {method[2]}")
                
        except Exception as e:
            print(f"   ❌ Payment methods query failed: {e}")
            return False
        
        # 3. Test payment trends query from line 255-262
        print("\n3️⃣ Testing payment trends query...")
        try:
            payment_trends = db.execute(
                """SELECT strftime('%Y-%m', payment_date) as month,
                          COUNT(*) as payment_count,
                          SUM(amount) as total_amount
                   FROM payments
                   WHERE payment_date >= DATE('now', '-12 months')
                   GROUP BY strftime('%Y-%m', payment_date)
                   ORDER BY month"""
            ).fetchall()
            
            print(f"   ✅ Payment trends query successful: {len(payment_trends)} months found")
            for trend in payment_trends:
                print(f"      • {trend[0]}: {trend[1]} payments, Rs. {trend[2]}")
                
        except Exception as e:
            print(f"   ❌ Payment trends query failed: {e}")
            return False
        
        # 4. Test payment statistics query from line 365-372
        print("\n4️⃣ Testing payment statistics query...")
        try:
            stats = db.execute(
                """SELECT 
                    COUNT(*) as total_payments,
                    SUM(amount) as total_amount,
                    AVG(amount) as avg_amount
                   FROM payments
                   WHERE DATE(payment_date) >= DATE('now', '-30 days')"""
            ).fetchone()
            
            print(f"   ✅ Payment statistics query successful:")
            print(f"      • Total payments: {stats[0]}")
            print(f"      • Total amount: Rs. {stats[1]}")
            print(f"      • Average amount: Rs. {stats[2]:.2f}")
                
        except Exception as e:
            print(f"   ❌ Payment statistics query failed: {e}")
            return False
        
        # 5. Test that we can simulate the original error scenario
        print("\n5️⃣ Testing error scenario simulation...")
        try:
            # This should work now (previously would fail with "no such table: payments")
            test_query = "SELECT COUNT(*) FROM payments"
            result = db.execute(test_query).fetchone()[0]
            print(f"   ✅ Basic payments table access: {result} records")
            
            # Test the specific error that was reported
            print("   🔍 Simulating original error scenario...")
            print("      Original error: 'Error generating financial reports: no such table: payments'")
            print("      This error should NOT occur anymore...")
            
            # Try to access payments table in various ways
            queries_to_test = [
                "SELECT * FROM payments LIMIT 1",
                "SELECT COUNT(*) FROM payments",
                "SELECT SUM(amount) FROM payments",
                "SELECT DISTINCT payment_method FROM payments"
            ]
            
            for query in queries_to_test:
                try:
                    db.execute(query).fetchall()
                    print(f"      ✅ Query works: {query}")
                except Exception as e:
                    print(f"      ❌ Query failed: {query} - {e}")
                    return False
                    
        except Exception as e:
            print(f"   ❌ Error scenario test failed: {e}")
            return False
        
        db.close()
        
        print(f"\n🎉 ALL FINANCIAL REPORTS TESTS PASSED!")
        print("=" * 60)
        print("✅ The 'no such table: payments' error has been RESOLVED")
        print("✅ All payment-related queries work correctly")
        print("✅ Financial reports should now load without errors")
        print("✅ Advanced payment features should be functional")
        
        print(f"\n🚀 NEXT STEPS:")
        print("   1. Access the Flask app at http://localhost:5000")
        print("   2. Navigate to Advanced Payment features")
        print("   3. Try generating financial reports")
        print("   4. The 'no such table: payments' error should be gone!")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_financial_reports_fix()
