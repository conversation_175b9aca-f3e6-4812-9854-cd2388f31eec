#!/usr/bin/env python3
"""
Simple test to verify the routing fix
"""

print("🧪 Testing Routing Fix...")

try:
    # Test 1: Import the app
    print("1. Testing app import...")
    from app import app
    print("✅ App imported successfully")
    
    # Test 2: Test URL generation
    print("2. Testing URL generation...")
    with app.app_context():
        from flask import url_for
        
        try:
            url = url_for('riders.dashboard')
            print(f"✅ url_for('riders.dashboard') = {url}")
        except Exception as e:
            print(f"❌ URL generation failed: {e}")
            raise
        
        # Test 3: List rider routes
        print("3. Listing rider routes...")
        rider_routes = []
        for rule in app.url_map.iter_rules():
            if 'riders' in rule.endpoint:
                rider_routes.append(f"{rule.rule} -> {rule.endpoint}")
        
        print(f"✅ Found {len(rider_routes)} rider routes:")
        for route in rider_routes[:10]:  # Show first 10
            print(f"   {route}")
    
    print("\n🎉 ALL TESTS PASSED! The routing fix is successful.")
    
except Exception as e:
    print(f"\n❌ ERROR: {e}")
    import traceback
    traceback.print_exc()
