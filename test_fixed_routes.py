#!/usr/bin/env python3
"""
🔍 TEST FIXED ROUTES SCRIPT
Tests all the routes that were fixed for the four specific errors
"""

import requests
import time
import sys

BASE_URL = "http://localhost:5000"

def test_route(route_name, url, expected_status=200):
    """Test a single route and return result"""
    print(f"🧪 Testing {route_name}: {url}")
    try:
        response = requests.get(url, timeout=10)
        status = response.status_code
        
        if status == expected_status:
            print(f"✅ {route_name}: HTTP {status} - SUCCESS")
            return True
        else:
            print(f"❌ {route_name}: HTTP {status} - FAILED (expected {expected_status})")
            print(f"   Response: {response.text[:200]}...")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ {route_name}: CONNECTION ERROR - {e}")
        return False

def main():
    """Main testing function"""
    print("🚀 TESTING FIXED ROUTES FOR MEDIVENT ERP SYSTEM")
    print("=" * 60)
    
    # Wait for server to be ready
    print("⏳ Waiting for server to start...")
    time.sleep(5)
    
    # Test routes related to the four fixed errors
    routes_to_test = [
        # Error 1 & 2: JSON Serialization and Efficiency Reports
        ("Rider Performance Dashboard", f"{BASE_URL}/riders/performance"),
        ("Rider Reports", f"{BASE_URL}/riders/reports"),
        ("Efficiency Report", f"{BASE_URL}/riders/reports?type=efficiency&date_from=2025-07-01&date_to=2025-07-31&rider="),
        
        # Error 3: Order Assignment
        ("Assignment Dashboard", f"{BASE_URL}/riders/assignment-dashboard"),
        
        # Error 4: Order Details (test with a sample order)
        ("Orders List", f"{BASE_URL}/orders"),
        
        # Additional critical routes
        ("Rider Dashboard", f"{BASE_URL}/riders/dashboard"),
        ("Main Dashboard", f"{BASE_URL}/dashboard"),
        ("Finance Dashboard", f"{BASE_URL}/finance/dashboard"),
    ]
    
    results = []
    for route_name, url in routes_to_test:
        success = test_route(route_name, url)
        results.append((route_name, success))
        time.sleep(1)  # Small delay between requests
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for route_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} - {route_name}")
    
    print(f"\n🎯 OVERALL: {passed}/{total} routes passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL ROUTES WORKING CORRECTLY!")
        return 0
    else:
        print("⚠️  SOME ROUTES NEED ATTENTION")
        return 1

if __name__ == "__main__":
    sys.exit(main())
