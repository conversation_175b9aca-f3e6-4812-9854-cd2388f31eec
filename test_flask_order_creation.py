#!/usr/bin/env python3
"""
Test Flask Order Creation - Direct Flask App Testing
"""

import os
import sys
import sqlite3
from datetime import datetime

# Add project root to path
sys.path.insert(0, '.')

def test_flask_app_order_creation():
    """Test order creation through Flask app context"""
    print("🧪 Testing Flask App Order Creation...")
    
    try:
        # Import Flask app
        from app import app
        
        with app.app_context():
            # Import database function
            from database import get_db
            
            # Test the actual order creation logic
            db = get_db()
            
            # Create sequence table if not exists
            db.execute('''
                CREATE TABLE IF NOT EXISTS order_sequence (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Test generating order IDs
            order_ids = []
            for i in range(10):
                # Insert and get auto-increment ID
                cursor = db.execute('INSERT INTO order_sequence DEFAULT VALUES')
                sequence_id = cursor.lastrowid
                
                # Generate order ID with zero-padded sequence
                order_id = f"ORD{sequence_id:08d}"
                order_ids.append(order_id)
            
            print(f"  ✅ Generated {len(order_ids)} order IDs")
            print(f"  📝 Sample IDs: {order_ids[:3]}")
            
            # Test inserting orders
            successful_inserts = 0
            for i, order_id in enumerate(order_ids):
                try:
                    db.execute('''
                        INSERT INTO orders (
                            order_id, customer_name, customer_address, customer_phone,
                            payment_method, status, sales_agent, updated_by, order_date, last_updated
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        order_id, f"Flask Test Customer {i}", "Test Address", "123456789",
                        "cash", "Placed", "test_user", "test_user", datetime.now(), datetime.now()
                    ))
                    successful_inserts += 1
                except Exception as e:
                    print(f"  ❌ Error inserting {order_id}: {e}")
            
            db.commit()
            
            print(f"  ✅ Successfully inserted: {successful_inserts}/{len(order_ids)} orders")
            
            # Check for duplicates
            cursor = db.execute('''
                SELECT order_id, COUNT(*) as count 
                FROM orders 
                WHERE customer_name LIKE 'Flask Test Customer %'
                GROUP BY order_id 
                HAVING COUNT(*) > 1
            ''')
            duplicates = cursor.fetchall()
            
            if duplicates:
                print(f"  ❌ Found {len(duplicates)} duplicate order IDs")
                return False
            else:
                print("  ✅ No duplicate order IDs found")
            
            # Clean up
            db.execute("DELETE FROM orders WHERE customer_name LIKE 'Flask Test Customer %'")
            db.commit()
            print("  🧹 Cleaned up test orders")
            
            return successful_inserts == len(order_ids)
    
    except Exception as e:
        print(f"  ❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_order_routes_import():
    """Test importing order routes"""
    print("\n🧪 Testing Order Routes Import...")
    
    try:
        from routes.orders import generate_order_id
        print("  ✅ Successfully imported generate_order_id from routes.orders")
        
        # Test the function (this will use UUID fallback outside Flask context)
        order_id = generate_order_id()
        print(f"  ✅ Generated order ID: {order_id}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def verify_database_state():
    """Verify the current database state"""
    print("\n🧪 Verifying Database State...")
    
    db_path = 'instance/medivent.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check orders table
        cursor.execute("SELECT COUNT(*) FROM orders")
        total_orders = cursor.fetchone()[0]
        print(f"  ✅ Total orders in database: {total_orders}")
        
        # Check for duplicates
        cursor.execute('''
            SELECT order_id, COUNT(*) as count 
            FROM orders 
            GROUP BY order_id 
            HAVING COUNT(*) > 1
        ''')
        duplicates = cursor.fetchall()
        
        if duplicates:
            print(f"  ❌ Found {len(duplicates)} duplicate order IDs:")
            for order_id, count in duplicates:
                print(f"    {order_id}: {count} occurrences")
            return False
        else:
            print("  ✅ No duplicate order IDs in database")
        
        # Check sequence table
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='order_sequence'")
        sequence_exists = cursor.fetchone() is not None
        
        if sequence_exists:
            cursor.execute("SELECT MAX(id) FROM order_sequence")
            max_sequence = cursor.fetchone()[0]
            print(f"  ✅ Order sequence table exists, current max: {max_sequence}")
        else:
            print("  ⚠️  Order sequence table does not exist yet")
        
        # Show recent orders
        cursor.execute('''
            SELECT order_id, customer_name, order_date 
            FROM orders 
            ORDER BY order_date DESC 
            LIMIT 5
        ''')
        recent_orders = cursor.fetchall()
        
        if recent_orders:
            print(f"  📝 Recent orders:")
            for order_id, customer_name, order_date in recent_orders:
                print(f"    {order_id} - {customer_name}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def main():
    """Main testing function"""
    print("🔍 FLASK ORDER CREATION TESTING")
    print("=" * 40)
    
    tests = [
        ("Order Routes Import", test_order_routes_import),
        ("Database State Verification", verify_database_state),
        ("Flask App Order Creation", test_flask_app_order_creation),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}")
        print("-" * len(test_name))
        success = test_func()
        results.append((test_name, success))
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"Result: {status}")
    
    print("\n" + "=" * 40)
    print("📊 FLASK TESTING SUMMARY")
    print("=" * 40)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name:<30} | {status}")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    if passed == total:
        print(f"\n🎉 ALL FLASK TESTS PASSED ({passed}/{total})")
        print("✅ FLASK ORDER CREATION IS WORKING CORRECTLY!")
    else:
        print(f"\n⚠️  SOME TESTS FAILED ({passed}/{total})")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
