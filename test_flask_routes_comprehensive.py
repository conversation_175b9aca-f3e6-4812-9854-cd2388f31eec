#!/usr/bin/env python3
"""
Comprehensive Flask Routes Testing
Test all routes individually with HTTP 200 verification
"""

import requests
import time
import json
from datetime import datetime

def test_server_startup():
    """Test if Flask server starts correctly"""
    print("🧪 Testing Flask Server Startup...")
    
    try:
        # Test basic import
        from app import app
        print("  ✅ Flask app imported successfully")
        
        # Test app configuration
        print(f"  ✅ Database path: {app.config.get('DATABASE', 'Not set')}")
        print(f"  ✅ Secret key configured: {'Yes' if app.config.get('SECRET_KEY') else 'No'}")
        
        # Test with test client
        with app.test_client() as client:
            response = client.get('/')
            print(f"  ✅ Root route status: {response.status_code}")
            
            # Test orders route
            response = client.get('/orders/')
            print(f"  ✅ Orders route status: {response.status_code}")
            
            # Test products route
            response = client.get('/products/')
            print(f"  ✅ Products route status: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Server startup error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_import_in_flask():
    """Test database import within Flask context"""
    print("\n🧪 Testing Database Import in Flask...")
    
    try:
        from app import app
        
        with app.app_context():
            # Test importing database functions
            from database import get_db
            print("  ✅ Successfully imported get_db from database module")
            
            # Test database connection
            db = get_db()
            cursor = db.execute("SELECT COUNT(*) as count FROM orders")
            order_count = cursor.fetchone()['count']
            print(f"  ✅ Database accessible: {order_count} orders found")
            
            # Test order ID generation
            from routes.orders import generate_order_id
            order_id = generate_order_id()
            print(f"  ✅ Order ID generated: {order_id}")
            
            return True
        
    except Exception as e:
        print(f"  ❌ Database import error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_order_creation_endpoint():
    """Test order creation endpoint"""
    print("\n🧪 Testing Order Creation Endpoint...")
    
    try:
        from app import app
        
        with app.test_client() as client:
            # Test GET request to new order page
            response = client.get('/orders/new')
            print(f"  ✅ GET /orders/new status: {response.status_code}")
            
            # Test order creation with minimal data
            order_data = {
                'customer_name': 'Test Customer API',
                'customer_address': 'Test Address',
                'customer_phone': '123456789',
                'payment_method': 'cash'
            }
            
            response = client.post('/orders/new', data=order_data)
            print(f"  ✅ POST /orders/new status: {response.status_code}")
            
            if response.status_code in [200, 302]:
                print("  ✅ Order creation endpoint working")
                return True
            else:
                print(f"  ⚠️  Unexpected status code: {response.status_code}")
                return False
        
    except Exception as e:
        print(f"  ❌ Order creation error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_all_major_routes():
    """Test all major routes for HTTP 200 responses"""
    print("\n🧪 Testing All Major Routes...")
    
    routes_to_test = [
        ('/', 'Root'),
        ('/orders/', 'Orders List'),
        ('/orders/new', 'New Order'),
        ('/products/', 'Products'),
        ('/inventory/', 'Inventory'),
        ('/users/', 'Users'),
    ]
    
    try:
        from app import app
        
        results = []
        with app.test_client() as client:
            for route, name in routes_to_test:
                try:
                    response = client.get(route)
                    status = response.status_code
                    success = status in [200, 302]  # 302 is redirect, also acceptable
                    results.append((name, route, status, success))
                    
                    status_icon = "✅" if success else "❌"
                    print(f"  {status_icon} {name} ({route}): {status}")
                    
                except Exception as e:
                    results.append((name, route, 'ERROR', False))
                    print(f"  ❌ {name} ({route}): ERROR - {e}")
        
        successful_routes = sum(1 for _, _, _, success in results if success)
        total_routes = len(results)
        
        print(f"\n  📊 Route Test Summary: {successful_routes}/{total_routes} routes working")
        return successful_routes == total_routes
        
    except Exception as e:
        print(f"  ❌ Route testing error: {e}")
        return False

def test_database_operations():
    """Test database operations"""
    print("\n🧪 Testing Database Operations...")
    
    try:
        from app import app
        
        with app.app_context():
            from database import get_db
            
            db = get_db()
            
            # Test reading from various tables
            tables_to_test = ['orders', 'products', 'users', 'inventory']
            
            for table in tables_to_test:
                try:
                    cursor = db.execute(f"SELECT COUNT(*) as count FROM {table}")
                    count = cursor.fetchone()['count']
                    print(f"  ✅ {table} table: {count} records")
                except Exception as e:
                    print(f"  ⚠️  {table} table: {e}")
            
            # Test order sequence table
            try:
                cursor = db.execute("SELECT MAX(id) as max_id FROM order_sequence")
                max_id = cursor.fetchone()['max_id']
                print(f"  ✅ order_sequence table: max ID = {max_id}")
            except Exception as e:
                print(f"  ⚠️  order_sequence table: {e}")
            
            return True
        
    except Exception as e:
        print(f"  ❌ Database operations error: {e}")
        return False

def test_order_id_generation_stress():
    """Stress test order ID generation"""
    print("\n🧪 Testing Order ID Generation (Stress Test)...")
    
    try:
        from app import app
        
        with app.app_context():
            from routes.orders import generate_order_id
            
            # Generate multiple order IDs quickly
            order_ids = []
            for i in range(20):
                order_id = generate_order_id()
                order_ids.append(order_id)
            
            # Check for uniqueness
            unique_ids = set(order_ids)
            duplicates = len(order_ids) - len(unique_ids)
            
            print(f"  ✅ Generated {len(order_ids)} order IDs")
            print(f"  ✅ Unique IDs: {len(unique_ids)}")
            print(f"  ✅ Duplicates: {duplicates}")
            print(f"  📝 Sample IDs: {order_ids[:3]}")
            
            return duplicates == 0
        
    except Exception as e:
        print(f"  ❌ Stress test error: {e}")
        return False

def main():
    """Main testing function"""
    print("🔍 COMPREHENSIVE FLASK ROUTES TESTING")
    print("=" * 50)
    
    tests = [
        ("Flask Server Startup", test_server_startup),
        ("Database Import in Flask", test_database_import_in_flask),
        ("Order Creation Endpoint", test_order_creation_endpoint),
        ("All Major Routes", test_all_major_routes),
        ("Database Operations", test_database_operations),
        ("Order ID Generation Stress", test_order_id_generation_stress),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}")
        print("-" * len(test_name))
        success = test_func()
        results.append((test_name, success))
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"Result: {status}")
    
    print("\n" + "=" * 50)
    print("📊 COMPREHENSIVE TESTING SUMMARY")
    print("=" * 50)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name:<35} | {status}")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    if passed == total:
        print(f"\n🎉 ALL TESTS PASSED ({passed}/{total})")
        print("✅ FLASK APPLICATION IS FULLY WORKING!")
        print("\n🚀 READY FOR PRODUCTION:")
        print("   • Database imports fixed")
        print("   • Order ID generation working")
        print("   • All major routes accessible")
        print("   • No UNIQUE constraint errors")
    else:
        print(f"\n⚠️  SOME TESTS FAILED ({passed}/{total})")
        print("❌ Additional fixes may be needed")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
