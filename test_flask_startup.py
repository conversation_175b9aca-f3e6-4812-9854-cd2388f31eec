#!/usr/bin/env python3
"""
Test Flask startup with minimal imports to isolate the issue
"""

print("🧪 Testing Flask startup with minimal imports...")

try:
    print("📦 Step 1: Testing basic imports...")
    import os
    import sys
    print("✅ Basic imports successful")
    
    print("📦 Step 2: Testing Flask import...")
    from flask import Flask
    print("✅ Flask import successful")
    
    print("📦 Step 3: Testing database path...")
    # Check if database exists
    db_path = os.path.join('instance', 'medivent.db')
    if os.path.exists(db_path):
        print(f"✅ Database found at: {db_path}")
    else:
        print(f"⚠️ Database not found at: {db_path}")
        # Check alternative paths
        alt_paths = ['medivent.db', 'database/medivent.db', 'data/medivent.db']
        for alt_path in alt_paths:
            if os.path.exists(alt_path):
                print(f"✅ Database found at alternative path: {alt_path}")
                break
        else:
            print("❌ No database found in common locations")
    
    print("📦 Step 4: Testing utils.db import...")
    from utils.db import get_db
    print("✅ utils.db import successful")
    
    print("📦 Step 5: Testing blueprint import...")
    from routes.modern_riders import riders_bp
    print("✅ Blueprint import successful")
    
    print("📦 Step 6: Creating minimal Flask app...")
    app = Flask(__name__)
    app.secret_key = 'test-key'
    
    # Set database path
    app.config['DATABASE'] = db_path
    
    print("📦 Step 7: Registering blueprint...")
    app.register_blueprint(riders_bp)
    print("✅ Blueprint registered successfully")
    
    print("📦 Step 8: Testing route listing...")
    with app.app_context():
        print("🛣️ Registered routes:")
        for rule in app.url_map.iter_rules():
            print(f"  {rule.rule} -> {rule.endpoint} [{', '.join(rule.methods)}]")
    
    print("✅ All tests passed! Flask app can be created successfully.")
    print("🚀 The issue might be with the full app.py startup process.")
    
except ImportError as e:
    print(f"❌ Import Error: {e}")
    print("🔍 This suggests a missing dependency or import issue")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
    print("🔍 This suggests an issue with the code structure or dependencies")
