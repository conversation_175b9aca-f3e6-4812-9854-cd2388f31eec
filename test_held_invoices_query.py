#!/usr/bin/env python3
"""
Test the exact query used by the held invoices page
"""

import sqlite3
from datetime import datetime

def test_held_invoices_query():
    print("🧪 TESTING HELD INVOICES QUERY")
    print("=" * 60)
    
    conn = sqlite3.connect('instance/medivent.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    # Step 1: Check basic data
    print("\n📊 Step 1: Basic Data Check")
    print("-" * 40)
    
    cursor.execute('SELECT COUNT(*) as count FROM invoice_holds WHERE status = "active"')
    active_holds = cursor.fetchone()['count']
    print(f"Active holds in database: {active_holds}")
    
    cursor.execute('SELECT COUNT(*) as count FROM orders')
    total_orders = cursor.fetchone()['count']
    print(f"Total orders in database: {total_orders}")
    
    # Step 2: Test the original query
    print("\n🔍 Step 2: Testing Original Query")
    print("-" * 40)
    
    try:
        original_query = '''
            SELECT
                ih.hold_id, ih.order_id, ih.hold_reason, ih.hold_comments,
                ih.hold_date, ih.hold_by, ih.priority_level,
                o.customer_name, o.order_amount, o.order_date, o.status,
                CAST(julianday('now') - julianday(ih.hold_date) AS INTEGER) as days_on_hold
            FROM invoice_holds ih
            JOIN orders o ON ih.order_id = o.order_id
            WHERE ih.status = 'active'
            ORDER BY ih.hold_date DESC
        '''
        
        results = cursor.execute(original_query).fetchall()
        print(f"Original query returned: {len(results)} results")
        
        if results:
            print("Sample results:")
            for result in results[:3]:
                print(f"  {result['order_id']} | {result['customer_name']} | {result['hold_reason']}")
        else:
            print("❌ Original query returned no results")
            
    except Exception as e:
        print(f"❌ Original query failed: {e}")
    
    # Step 3: Test LEFT JOIN query
    print("\n🔍 Step 3: Testing LEFT JOIN Query")
    print("-" * 40)
    
    try:
        left_join_query = '''
            SELECT
                ih.hold_id, ih.order_id, ih.hold_reason, ih.hold_comments,
                ih.hold_date, ih.hold_by, ih.priority_level,
                COALESCE(o.customer_name, 'Unknown Customer') as customer_name, 
                COALESCE(o.order_amount, 0) as order_amount, 
                COALESCE(o.order_date, ih.hold_date) as order_date, 
                COALESCE(o.status, 'Unknown') as status,
                CAST(julianday('now') - julianday(ih.hold_date) AS INTEGER) as days_on_hold
            FROM invoice_holds ih
            LEFT JOIN orders o ON ih.order_id = o.order_id
            WHERE ih.status = 'active'
            ORDER BY ih.hold_date DESC
        '''
        
        results = cursor.execute(left_join_query).fetchall()
        print(f"LEFT JOIN query returned: {len(results)} results")
        
        if results:
            print("Sample results:")
            for result in results[:3]:
                print(f"  {result['order_id']} | {result['customer_name']} | {result['hold_reason']}")
        else:
            print("❌ LEFT JOIN query returned no results")
            
    except Exception as e:
        print(f"❌ LEFT JOIN query failed: {e}")
    
    # Step 4: Check for data mismatches
    print("\n🔍 Step 4: Checking for Data Mismatches")
    print("-" * 40)
    
    # Get all active holds
    cursor.execute('SELECT order_id FROM invoice_holds WHERE status = "active"')
    hold_order_ids = [row['order_id'] for row in cursor.fetchall()]
    print(f"Order IDs in active holds: {hold_order_ids}")
    
    # Check if these orders exist
    for order_id in hold_order_ids:
        cursor.execute('SELECT order_id, customer_name FROM orders WHERE order_id = ?', (order_id,))
        order = cursor.fetchone()
        if order:
            print(f"  ✅ {order_id} exists in orders table: {order['customer_name']}")
        else:
            print(f"  ❌ {order_id} NOT found in orders table")
    
    # Step 5: Test holds-only query
    print("\n🔍 Step 5: Testing Holds-Only Query")
    print("-" * 40)
    
    try:
        holds_only_query = '''
            SELECT hold_id, order_id, hold_reason, hold_comments, hold_date, hold_by, priority_level
            FROM invoice_holds 
            WHERE status = 'active'
            ORDER BY hold_date DESC
        '''
        
        results = cursor.execute(holds_only_query).fetchall()
        print(f"Holds-only query returned: {len(results)} results")
        
        if results:
            print("Sample results:")
            for result in results[:3]:
                print(f"  {result['order_id']} | {result['hold_reason']} | {result['hold_date']}")
                
    except Exception as e:
        print(f"❌ Holds-only query failed: {e}")
    
    conn.close()
    
    print("\n✅ Query testing complete")
    return True

if __name__ == "__main__":
    test_held_invoices_query()
