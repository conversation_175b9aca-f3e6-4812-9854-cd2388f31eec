import sqlite3

# Connect to database and check the specific order
conn = sqlite3.connect('instance/medivent.db')
cursor = conn.cursor()

order_id = 'ORD175397491316416F32'

print('=== DEBUGGING ORDER HISTORY DATA ===')
print(f'Order ID: {order_id}')

# First check the actual column structure
print('\n0. ORDERS TABLE STRUCTURE:')
cursor.execute('PRAGMA table_info(orders)')
columns = cursor.fetchall()
for i, col in enumerate(columns):
    print(f'   Column {i}: {col[1]} ({col[2]})')

# Check order details
print('\n1. ORDER DETAILS:')
cursor.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,))
order = cursor.fetchone()
if order:
    print(f'   Order data length: {len(order)}')
    for i, value in enumerate(order):
        if i < len(columns):
            print(f'   {columns[i][1]}: {value}')
        else:
            print(f'   Column {i}: {value}')
else:
    print('   ❌ Order not found!')

# Check order items table structure
print('\n2A. ORDER_ITEMS TABLE STRUCTURE:')
cursor.execute('PRAGMA table_info(order_items)')
item_columns = cursor.fetchall()
for i, col in enumerate(item_columns):
    print(f'   Column {i}: {col[1]} ({col[2]})')

# Check order items
print('\n2B. ORDER ITEMS:')
cursor.execute('SELECT * FROM order_items WHERE order_id = ?', (order_id,))
items = cursor.fetchall()

if items:
    print(f'   Found {len(items)} order items:')
    for item in items:
        print(f'   Item data length: {len(item)}')
        for i, value in enumerate(item):
            if i < len(item_columns):
                print(f'     {item_columns[i][1]}: {value}')
            else:
                print(f'     Column {i}: {value}')
        print('   ---')
else:
    print('   ❌ No order items found!')

# Check activity logs
print('\n3. ACTIVITY LOGS:')
cursor.execute('''
    SELECT * FROM activity_logs
    WHERE entity_id = ?
    ORDER BY timestamp ASC
''', (order_id,))
logs = cursor.fetchall()

if logs:
    print(f'   Found {len(logs)} activity logs:')
    for log in logs:
        print(f'   - {log[1]}: {log[3]} by {log[2]} - {log[4]}')
else:
    print('   ❌ No activity logs found!')

# Check products table to see if product names are available
print('\n4. PRODUCTS TABLE CHECK:')
cursor.execute('SELECT COUNT(*) FROM products')
product_count = cursor.fetchone()[0]
print(f'   Total products in database: {product_count}')

if items:
    # Check if the products referenced in order items exist
    for item in items:
        product_id = item[5]  # product_id column
        cursor.execute('SELECT name, strength FROM products WHERE product_id = ?', (product_id,))
        product = cursor.fetchone()
        if product:
            print(f'   Product {product_id}: {product[0]} - {product[1]}')
        else:
            print(f'   ❌ Product {product_id} not found in products table!')

conn.close()
