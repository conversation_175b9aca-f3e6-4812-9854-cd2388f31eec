import requests

print('🧪 TESTING ENHANCED ORDER HISTORY PAGE')
print('=' * 50)

try:
    # Test the order history page
    response = requests.get('http://127.0.0.1:5001/orders/ORD175397491316416F32/history', timeout=10)
    print(f'✅ Order history page: {response.status_code}')
    
    if response.status_code == 200:
        content = response.text
        
        # Check for key sections
        checks = [
            ('Order Details', 'Order Details' in content),
            ('Order Status Badge', 'badge-danger' in content),  # Rejected status
            ('Product Details', 'Axinix' in content),
            ('Order Items Section', 'Order Items' in content),
            ('Timeline Section', 'timeline' in content),
            ('Rejection Information', 'Rejected By' in content),
            ('Rejection Date', 'Rejection Date' in content),
            ('Rejection Notes', 'rejeccttttt' in content),
            ('Activity Log Section', 'Activity Log' in content),
            ('Activity Entries', 'Order Placed' in content),
        ]
        
        print('\n📋 CONTENT CHECKS:')
        for check_name, result in checks:
            status = '✅' if result else '❌'
            print(f'   {status} {check_name}')
        
        # Count key elements
        timeline_items = content.count('timeline-item')
        activity_rows = content.count('Order Placed') + content.count('Order Rejected')
        
        print(f'\n📊 STATISTICS:')
        print(f'   Timeline items: {timeline_items}')
        print(f'   Activity entries: {activity_rows}')
        
        # Check if all required information is present
        all_passed = all(result for _, result in checks)
        if all_passed:
            print('\n🎉 SUCCESS: Enhanced history page is fully functional!')
            print('   ✅ Shows product details for rejected orders')
            print('   ✅ Shows complete order timeline with rejection info')
            print('   ✅ Shows activity logs with user actions')
            print('   ✅ Shows rejection details and notes')
        else:
            print('\n⚠️ Some features may need attention')
            
    else:
        print(f'❌ Error: {response.status_code}')
        print(response.text[:500])
        
except requests.exceptions.ConnectionError:
    print('❌ Cannot connect to Flask server - is it running?')
except Exception as e:
    print(f'❌ Error testing history page: {e}')
