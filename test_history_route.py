import requests
import time

# Test the history route
url = "http://127.0.0.1:5001/orders/ORD1753983391CA9E99E1/history"

print("🧪 TESTING HISTORY ROUTE")
print("=" * 30)

try:
    response = requests.get(url, timeout=10)
    print(f"Status Code: {response.status_code}")
    print(f"Content Length: {len(response.text)}")
    
    # Check if it's a redirect
    if response.status_code == 302:
        print(f"Redirected to: {response.headers.get('Location', 'Unknown')}")
    
    # Check for login page
    if "login" in response.text.lower():
        print("⚠️ Response contains login page - authentication required")
    
    # Check for order items
    if "No order items found" in response.text:
        print("❌ 'No order items found' message present")
    
    if "Nilonix" in response.text:
        print("✅ Product 'Nilonix' found in response")
    else:
        print("❌ Product 'Nilonix' NOT found in response")
    
    # Save response for inspection
    with open('history_response.html', 'w', encoding='utf-8') as f:
        f.write(response.text)
    print("📄 Response saved to history_response.html")
    
except Exception as e:
    print(f"❌ Error: {e}")

# Also test the debug route
print("\n🧪 TESTING DEBUG ROUTE")
print("=" * 30)

try:
    debug_url = "http://127.0.0.1:5001/orders/ORD1753983391CA9E99E1/debug-items"
    response = requests.get(debug_url, timeout=10)
    print(f"Status Code: {response.status_code}")
    print(f"Content: {response.text[:200]}...")
    
except Exception as e:
    print(f"❌ Error: {e}")
