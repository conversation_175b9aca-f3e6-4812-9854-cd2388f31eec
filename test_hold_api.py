#!/usr/bin/env python3
"""
Simple test for the hold API functionality
"""

import sqlite3
import json
import time
from datetime import datetime

def test_hold_api_simulation():
    """Simulate the hold API functionality directly"""
    
    print("🔍 TESTING HOLD API FUNCTIONALITY")
    print("=" * 60)
    
    # Connect to database
    try:
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        # Get a test order
        cursor.execute('SELECT order_id, customer_name, status FROM orders WHERE status != "On Hold" LIMIT 1')
        test_order = cursor.fetchone()
        
        if not test_order:
            print("❌ No suitable test order found")
            return False
        
        order_id = test_order[0]
        customer_name = test_order[1]
        original_status = test_order[2]
        
        print(f"📋 Test Order: {order_id}")
        print(f"   Customer: {customer_name}")
        print(f"   Original Status: {original_status}")
        
        # Check current holds count
        cursor.execute('SELECT COUNT(*) FROM invoice_holds WHERE status = "active"')
        initial_holds = cursor.fetchone()[0]
        print(f"📊 Initial active holds: {initial_holds}")
        
        # Simulate the API call logic
        hold_reason = "Test hold for API verification"
        hold_notes = f"Test created at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        # Check if already on hold
        cursor.execute('SELECT hold_id FROM invoice_holds WHERE order_id = ? AND status = "active"', (order_id,))
        existing_hold = cursor.fetchone()
        
        if existing_hold:
            print(f"⚠️ Order {order_id} is already on hold")
            return False
        
        # Generate hold ID
        hold_id = f"HOLD{int(time.time())}"
        current_user_name = 'test_user'
        
        print(f"🔧 Creating hold record...")
        print(f"   Hold ID: {hold_id}")
        print(f"   Reason: {hold_reason}")
        
        # Create hold record
        cursor.execute('''
            INSERT INTO invoice_holds (
                hold_id, order_id, hold_reason, hold_comments,
                hold_date, hold_by, priority_level, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            hold_id, order_id, hold_reason, hold_notes,
            datetime.now().isoformat(), current_user_name, 'normal', 'active'
        ))
        
        # Update order status
        cursor.execute('''
            UPDATE orders
            SET status = 'On Hold', 
                notes = ?, 
                last_updated = datetime('now')
            WHERE order_id = ?
        ''', (f"{hold_reason}. {hold_notes}", order_id))
        
        # Commit changes
        conn.commit()
        
        # Verify the changes
        print(f"✅ Changes committed to database")
        
        # Check new holds count
        cursor.execute('SELECT COUNT(*) FROM invoice_holds WHERE status = "active"')
        final_holds = cursor.fetchone()[0]
        print(f"📊 Final active holds: {final_holds}")
        
        if final_holds > initial_holds:
            print("✅ Hold record created successfully")
        else:
            print("❌ Hold record not created")
            return False
        
        # Verify the specific hold record
        cursor.execute('''
            SELECT hold_id, order_id, hold_reason, status, hold_date
            FROM invoice_holds 
            WHERE order_id = ? AND status = "active"
        ''', (order_id,))
        
        hold_record = cursor.fetchone()
        if hold_record:
            print("✅ Hold record verification:")
            print(f"   Hold ID: {hold_record[0]}")
            print(f"   Order ID: {hold_record[1]}")
            print(f"   Reason: {hold_record[2]}")
            print(f"   Status: {hold_record[3]}")
            print(f"   Date: {hold_record[4]}")
        else:
            print("❌ Hold record not found after creation")
            return False
        
        # Verify order status update
        cursor.execute('SELECT status FROM orders WHERE order_id = ?', (order_id,))
        new_status = cursor.fetchone()
        
        if new_status and new_status[0] == 'On Hold':
            print("✅ Order status updated to 'On Hold'")
        else:
            print(f"❌ Order status not updated correctly: {new_status[0] if new_status else 'Not found'}")
            return False
        
        # Test the held invoices query
        print(f"🔍 Testing held invoices query...")
        cursor.execute('''
            SELECT
                ih.hold_id, ih.order_id, ih.hold_reason, ih.hold_comments,
                ih.hold_date, ih.hold_by, ih.priority_level,
                o.customer_name, o.order_amount, o.order_date, o.status
            FROM invoice_holds ih
            JOIN orders o ON ih.order_id = o.order_id
            WHERE ih.status = 'active' AND ih.order_id = ?
        ''', (order_id,))
        
        held_invoice = cursor.fetchone()
        if held_invoice:
            print("✅ Held invoice query successful:")
            print(f"   Order: {held_invoice[1]}")
            print(f"   Customer: {held_invoice[7]}")
            print(f"   Amount: Rs.{held_invoice[8]}")
            print(f"   Hold Reason: {held_invoice[2]}")
        else:
            print("❌ Held invoice query failed")
            return False
        
        conn.close()
        
        print("\n" + "=" * 60)
        print("🎉 HOLD API SIMULATION SUCCESSFUL!")
        print("✅ Hold record created in invoice_holds table")
        print("✅ Order status updated to 'On Hold'")
        print("✅ Held invoices query returns correct data")
        print("🎯 The fix should work correctly!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_hold_api_simulation()
    if success:
        print("\n🎉 TEST PASSED!")
    else:
        print("\n❌ TEST FAILED!")
