#!/usr/bin/env python3
"""
Test HTTP routes to verify they're working
"""

import requests
import time

def test_routes():
    """Test the critical routes that were failing"""
    
    base_url = "http://127.0.0.1:5001"
    
    routes_to_test = [
        "/product_management",
        "/products/product_management/",
        "/products/view_all/",
        "/orders/ORD175411154600DAC554/generate-partial-dc"
    ]
    
    print("🔍 TESTING HTTP ROUTES")
    print(f"Base URL: {base_url}")
    
    # Wait for server to start
    print("\n⏳ Waiting for server to start...")
    for i in range(10):
        try:
            response = requests.get(f"{base_url}/", timeout=2)
            print("✅ Server is responding")
            break
        except requests.exceptions.RequestException:
            print(f"   Attempt {i+1}/10: Server not ready yet...")
            time.sleep(2)
    else:
        print("❌ Server failed to start within 20 seconds")
        return
    
    print("\n📦 Testing routes...")
    for route in routes_to_test:
        try:
            url = f"{base_url}{route}"
            print(f"\n🧪 Testing: {url}")
            
            response = requests.get(url, timeout=10, allow_redirects=False)
            
            if response.status_code == 200:
                print(f"   ✅ HTTP 200 OK")
            elif response.status_code == 302:
                print(f"   🔄 HTTP 302 Redirect to: {response.headers.get('Location', 'Unknown')}")
            elif response.status_code == 404:
                print(f"   ❌ HTTP 404 Not Found")
            elif response.status_code == 500:
                print(f"   💥 HTTP 500 Internal Server Error")
            else:
                print(f"   ⚠️ HTTP {response.status_code}")
                
        except requests.exceptions.Timeout:
            print(f"   ⏰ Request timed out")
        except requests.exceptions.RequestException as e:
            print(f"   ❌ Request failed: {e}")
    
    print("\n🎉 HTTP ROUTE TESTING COMPLETE")

if __name__ == "__main__":
    test_routes()
