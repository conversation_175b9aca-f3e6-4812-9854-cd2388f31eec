#!/usr/bin/env python3
"""
Test inventory route after Flask app restart
"""

import requests
import time

def test_inventory_route():
    """Test the inventory route with authentication"""
    try:
        print("🚀 TESTING INVENTORY ROUTE AFTER RESTART")
        print("=" * 60)
        
        # Wait for Flask app to start
        print("⏳ Waiting for Flask app to start...")
        time.sleep(3)
        
        # Create session for authentication
        session = requests.Session()
        
        # Test if server is running
        print("1️⃣ Testing server connectivity...")
        try:
            response = session.get('http://127.0.0.1:5001/', timeout=5)
            print(f"✅ Server is running (status: {response.status_code})")
        except requests.exceptions.ConnectionError:
            print("❌ Server is not running or not accessible")
            return False
        except Exception as e:
            print(f"❌ Server test failed: {str(e)}")
            return False
        
        # Login first
        print("\n2️⃣ Logging in as admin...")
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        login_response = session.post('http://127.0.0.1:5001/login', data=login_data, timeout=10)
        
        if login_response.status_code == 200 and 'dashboard' in login_response.url:
            print("✅ Login successful")
        else:
            print(f"❌ Login failed (status: {login_response.status_code})")
            print(f"Response URL: {login_response.url}")
            return False
        
        # Test inventory route
        print("\n3️⃣ Testing inventory route...")
        inventory_response = session.get('http://127.0.0.1:5001/inventory/', timeout=10)
        
        print(f"Status Code: {inventory_response.status_code}")
        print(f"Final URL: {inventory_response.url}")
        print(f"Response Length: {len(inventory_response.text)}")
        
        # Check if we got redirected to dashboard
        if 'dashboard' in inventory_response.url:
            print("❌ REDIRECTED TO DASHBOARD - Issue still exists")
            
            # Check response content for clues
            if 'Access denied' in inventory_response.text:
                print("🔍 Found 'Access denied' in response")
            elif 'Permission' in inventory_response.text:
                print("🔍 Found 'Permission' in response")
            else:
                print("🔍 No obvious permission error in response")
            
            return False
        
        elif inventory_response.status_code == 200:
            print("✅ SUCCESS - Inventory page loaded!")
            
            # Check content
            content = inventory_response.text
            if 'Products Overview' in content:
                print("✅ Found 'Products Overview' section")
            if 'Inventory Records' in content:
                print("✅ Found 'Inventory Records' section")
            if 'Paracetamol' in content:
                print("✅ Found 'Paracetamol' in content")
            if '1435' in content:
                print("✅ Found stock quantity '1435'")
            if '1319' in content:
                print("✅ Found available stock '1319'")
            if 'batch' in content.lower():
                print("✅ Found batch information")
            
            # Save the response for inspection
            with open('inventory_response_success.html', 'w', encoding='utf-8') as f:
                f.write(content)
            print("💾 Response saved to: inventory_response_success.html")
            
            return True
        
        else:
            print(f"❌ Unexpected status code: {inventory_response.status_code}")
            return False
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_other_routes():
    """Test other routes to ensure they still work"""
    try:
        print("\n🔍 TESTING OTHER ROUTES")
        print("=" * 60)
        
        session = requests.Session()
        
        # Login
        login_data = {'username': 'admin', 'password': 'admin123'}
        session.post('http://127.0.0.1:5001/login', data=login_data, timeout=10)
        
        # Test routes
        test_routes = [
            ('/dashboard', 'Dashboard'),
            ('/products/', 'Products'),
            ('/orders/', 'Orders'),
        ]
        
        for route, name in test_routes:
            try:
                response = session.get(f'http://127.0.0.1:5001{route}', timeout=10)
                if response.status_code == 200:
                    print(f"✅ {name}: {response.status_code}")
                else:
                    print(f"❌ {name}: {response.status_code}")
            except Exception as e:
                print(f"❌ {name}: Error - {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Other routes test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 INVENTORY ROUTE TEST AFTER RESTART")
    print("=" * 70)
    
    success1 = test_inventory_route()
    success2 = test_other_routes()
    
    print("\n" + "=" * 70)
    print("📊 TEST RESULTS:")
    print("=" * 70)
    
    if success1:
        print("✅ INVENTORY ROUTE: WORKING!")
        print("🎉 The inventory batch display issue is FIXED!")
    else:
        print("❌ INVENTORY ROUTE: STILL REDIRECTING")
        print("🔍 Need to investigate further...")
    
    if success2:
        print("✅ OTHER ROUTES: Working normally")
    else:
        print("❌ OTHER ROUTES: Some issues detected")
    
    print("=" * 70)
