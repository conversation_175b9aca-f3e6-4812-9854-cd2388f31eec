#!/usr/bin/env python3
"""
Test inventory route directly by importing and calling the function
"""

import sys
import os
import sqlite3
from flask import Flask, g

def test_inventory_route_direct():
    """Test inventory route by calling the function directly"""
    try:
        print("🔍 TESTING INVENTORY ROUTE DIRECTLY")
        print("=" * 50)
        
        # Set up Flask app context
        app = Flask(__name__)
        app.secret_key = 'test-key'
        
        with app.app_context():
            # Set up database connection in Flask g
            g._database = sqlite3.connect('instance/medivent.db')
            g._database.row_factory = sqlite3.Row
            
            # Import the inventory route function
            print("1️⃣ Importing inventory route...")
            from routes.inventory import index as inventory_index
            print("✅ Import successful")
            
            # Call the function directly
            print("\n2️⃣ Calling inventory index function...")
            try:
                result = inventory_index()
                print(f"✅ Function call successful")
                print(f"Result type: {type(result)}")
                
                # Check if it's a template response or redirect
                if hasattr(result, 'status_code'):
                    print(f"Status code: {result.status_code}")
                    if result.status_code == 302:
                        print(f"⚠️ Redirect detected: {result.location}")
                    else:
                        print(f"✅ Normal response")
                elif hasattr(result, 'data'):
                    print(f"✅ Template response with data")
                    content = result.data.decode('utf-8') if isinstance(result.data, bytes) else str(result.data)
                    print(f"Content length: {len(content)}")
                    print(f"Contains 'Products Overview': {'Products Overview' in content}")
                    print(f"Contains 'Inventory Records': {'Inventory Records' in content}")
                else:
                    print(f"✅ Response: {str(result)[:200]}...")
                
                return True
                
            except Exception as e:
                print(f"❌ Function call failed: {str(e)}")
                import traceback
                traceback.print_exc()
                return False
            
            finally:
                # Clean up database connection
                if hasattr(g, '_database'):
                    g._database.close()
        
    except Exception as e:
        print(f"❌ Direct test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_template_rendering():
    """Test template rendering separately"""
    try:
        print("\n🎨 TESTING TEMPLATE RENDERING")
        print("=" * 50)
        
        # Set up Flask app
        app = Flask(__name__, template_folder='templates')
        app.secret_key = 'test-key'
        
        with app.app_context():
            from flask import render_template
            
            # Test data
            test_data = {
                'summary': {
                    'total_inventory_entries': 41,
                    'products_with_inventory': 8,
                    'low_stock_products': 14,
                    'total_stock_value': 194697.5,
                    'system_health': 'Needs Attention'
                },
                'low_stock_products': [],
                'recent_inventory': [],
                'products_summary': [
                    {
                        'product_id': 'P001',
                        'product_name': 'Paracetamol 500mg',
                        'strength': '500mg',
                        'division_name': 'Sales Division',
                        'total_stock': 1435,
                        'available_stock': 1319,
                        'batch_count': 2
                    }
                ]
            }
            
            print("1️⃣ Testing template rendering...")
            result = render_template('inventory/index.html', **test_data)
            print(f"✅ Template rendered successfully")
            print(f"Content length: {len(result)}")
            print(f"Contains 'Products Overview': {'Products Overview' in result}")
            print(f"Contains 'Inventory Records': {'Inventory Records' in result}")
            print(f"Contains 'Paracetamol': {'Paracetamol' in result}")
            print(f"Contains '1435': {'1435' in result}")
            print(f"Contains '1319': {'1319' in result}")
            print(f"Contains '2 batch': {'2 batch' in result}")
            
            # Save rendered template
            with open('inventory_template_test.html', 'w', encoding='utf-8') as f:
                f.write(result)
            print(f"💾 Rendered template saved to: inventory_template_test.html")
            
            return True
            
    except Exception as e:
        print(f"❌ Template test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 INVENTORY DIRECT FUNCTION TEST")
    print("=" * 70)
    
    success1 = test_inventory_route_direct()
    success2 = test_template_rendering()
    
    print("\n" + "=" * 70)
    print("📊 TEST RESULTS:")
    print("=" * 70)
    
    if success1:
        print("✅ Direct function call: PASSED")
    else:
        print("❌ Direct function call: FAILED")
    
    if success2:
        print("✅ Template rendering: PASSED")
    else:
        print("❌ Template rendering: FAILED")
    
    if success1 and success2:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Inventory function works correctly")
        print("✅ Template renders properly")
        print("⚠️ Issue might be with Flask app routing or blueprint registration")
    else:
        print("\n❌ TESTS FAILED - NEED TO FIX COMPONENTS")
    
    print("=" * 70)
