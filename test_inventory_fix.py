#!/usr/bin/env python3
"""
Test inventory batch display fix
"""

import requests
import sqlite3
import sys

def test_inventory_data():
    """Test inventory data directly from database"""
    try:
        print("🔍 TESTING INVENTORY DATA")
        print("=" * 50)
        
        # Connect to database
        db = sqlite3.connect('instance/medivent.db')
        db.row_factory = sqlite3.Row
        
        # Test the new query
        cursor = db.execute('''
            SELECT p.product_id, p.name as product_name, p.strength,
                   d.name as division_name,
                   COALESCE(SUM(i.stock_quantity), 0) as total_stock,
                   COALESCE(SUM(i.stock_quantity - COALESCE(i.allocated_quantity, 0)), 0) as available_stock,
                   COUNT(CASE WHEN i.status = 'active' THEN i.inventory_id END) as batch_count
            FROM products p
            JOIN divisions d ON p.division_id = d.division_id
            LEFT JOIN inventory i ON p.product_id = i.product_id AND i.status = 'active'
            WHERE d.is_active = 1
            GROUP BY p.product_id, p.name, p.strength, d.name
            ORDER BY p.name
        ''')
        
        products = cursor.fetchall()
        
        print(f"Found {len(products)} products:")
        for product in products:
            print(f"  • {product['product_name']} ({product['strength']})")
            print(f"    Total Stock: {product['total_stock']}")
            print(f"    Available: {product['available_stock']}")
            print(f"    Batches: {product['batch_count']}")
            print()
        
        # Focus on Paracetamol
        paracetamol = None
        for product in products:
            if 'Paracetamol' in product['product_name']:
                paracetamol = product
                break
        
        if paracetamol:
            print("🎯 PARACETAMOL DETAILS:")
            print(f"  Product ID: {paracetamol['product_id']}")
            print(f"  Name: {paracetamol['product_name']}")
            print(f"  Strength: {paracetamol['strength']}")
            print(f"  Division: {paracetamol['division_name']}")
            print(f"  Total Stock: {paracetamol['total_stock']}")
            print(f"  Available Stock: {paracetamol['available_stock']}")
            print(f"  Batch Count: {paracetamol['batch_count']}")
            
            # Get individual batches for Paracetamol
            batches = db.execute('''
                SELECT batch_number, stock_quantity, allocated_quantity,
                       (stock_quantity - COALESCE(allocated_quantity, 0)) as available,
                       warehouse_id, status
                FROM inventory 
                WHERE product_id = ? AND status = 'active'
                ORDER BY batch_number
            ''', (paracetamol['product_id'],)).fetchall()
            
            print(f"\n  Individual Batches ({len(batches)}):")
            for batch in batches:
                print(f"    - {batch['batch_number']}: {batch['stock_quantity']} total, {batch['available']} available")
        else:
            print("❌ Paracetamol not found in products")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_inventory_route():
    """Test inventory route response"""
    try:
        print("\n🌐 TESTING INVENTORY ROUTE")
        print("=" * 50)
        
        response = requests.get('http://127.0.0.1:5001/inventory/', timeout=10)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Route accessible")
            
            # Check for key elements
            content = response.text
            
            if 'Products Overview' in content:
                print("✅ Products Overview section found")
            else:
                print("❌ Products Overview section missing")
            
            if 'Inventory Records' in content:
                print("✅ Inventory Records section found")
            else:
                print("❌ Inventory Records section missing")
            
            if 'batch' in content.lower():
                print("✅ Batch information found in page")
            else:
                print("❌ Batch information missing from page")
            
            if 'available' in content.lower():
                print("✅ Available stock information found")
            else:
                print("❌ Available stock information missing")
            
            # Look for Paracetamol specifically
            if 'Paracetamol' in content:
                print("✅ Paracetamol found in page")
            else:
                print("❌ Paracetamol not found in page")
            
            # Check for error messages
            if 'error' in content.lower() or 'exception' in content.lower():
                print("⚠️ Possible errors detected in page")
                # Find error context
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if 'error' in line.lower() or 'exception' in line.lower():
                        print(f"    Line {i}: {line.strip()}")
            else:
                print("✅ No obvious errors in page")
                
        else:
            print(f"❌ Route failed with status {response.status_code}")
            print("Response content:", response.text[:500])
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ Route test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 INVENTORY BATCH DISPLAY FIX TEST")
    print("=" * 70)
    
    success1 = test_inventory_data()
    success2 = test_inventory_route()
    
    print("\n" + "=" * 70)
    print("📊 TEST RESULTS:")
    print("=" * 70)
    
    if success1:
        print("✅ Database Query: PASSED")
    else:
        print("❌ Database Query: FAILED")
    
    if success2:
        print("✅ Route Response: PASSED")
    else:
        print("❌ Route Response: FAILED")
    
    if success1 and success2:
        print("\n🎉 INVENTORY FIX SUCCESSFUL!")
        print("✅ Batch count information added to inventory display")
        print("✅ Available stock calculation working")
        print("✅ Route responding correctly")
    else:
        print("\n❌ INVENTORY FIX NEEDS ATTENTION")
    
    print("=" * 70)
