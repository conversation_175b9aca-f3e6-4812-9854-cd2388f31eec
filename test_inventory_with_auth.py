#!/usr/bin/env python3
"""
Test inventory with proper authentication
"""

import requests
import sys

def test_inventory_with_auth():
    """Test inventory route with authentication"""
    try:
        print("🔐 TESTING INVENTORY WITH AUTHENTICATION")
        print("=" * 50)
        
        # Create session for maintaining cookies
        session = requests.Session()
        
        # First, get the login page to establish session
        login_page = session.get('http://127.0.0.1:5001/login')
        print(f"Login page status: {login_page.status_code}")
        
        # Login with admin credentials
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        login_response = session.post('http://127.0.0.1:5001/login', data=login_data)
        print(f"Login response status: {login_response.status_code}")
        
        # Check if login was successful (should redirect to dashboard)
        if login_response.status_code == 200 and 'login' not in login_response.url:
            print("✅ Login successful")
        elif login_response.history and login_response.history[0].status_code == 302:
            print("✅ Login successful (redirected)")
        else:
            print("❌ Login failed")
            print(f"Final URL: {login_response.url}")
            return False
        
        # Now test the inventory route
        inventory_response = session.get('http://127.0.0.1:5001/inventory/')
        print(f"Inventory response status: {inventory_response.status_code}")
        print(f"Final URL: {inventory_response.url}")
        
        if inventory_response.status_code == 200:
            content = inventory_response.text
            
            print("\n🔍 INVENTORY CONTENT ANALYSIS:")
            print(f"  • Content Length: {len(content)}")
            print(f"  • Contains 'Products Overview': {'Products Overview' in content}")
            print(f"  • Contains 'Inventory Records': {'Inventory Records' in content}")
            print(f"  • Contains 'Paracetamol': {'Paracetamol' in content}")
            print(f"  • Contains 'batch': {'batch' in content.lower()}")
            print(f"  • Contains 'available': {'available' in content.lower()}")
            print(f"  • Contains 'Total Stock': {'Total Stock' in content}")
            print(f"  • Contains 'Available Stock': {'Available Stock' in content}")
            print(f"  • Contains 'Batches': {'Batches' in content}")
            
            # Look for table headers
            print(f"  • Contains table headers: {'<th>' in content}")
            print(f"  • Contains table data: {'<td>' in content}")
            
            # Check for specific values we expect
            print(f"  • Contains '1435': {'1435' in content}")  # Total stock
            print(f"  • Contains '1319': {'1319' in content}")  # Available stock
            print(f"  • Contains '2 batch': {'2 batch' in content}")  # Batch count
            
            # Save the authenticated response
            with open('inventory_authenticated_response.html', 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"\n💾 Authenticated response saved to: inventory_authenticated_response.html")
            
            # Show a snippet of the content
            if 'Products Overview' in content:
                start = content.find('Products Overview')
                end = content.find('</table>', start) + 8
                section = content[start:end]
                print(f"\n📋 PRODUCTS OVERVIEW SECTION:")
                print("-" * 50)
                print(section[:1000] + "..." if len(section) > 1000 else section)
                print("-" * 50)
            
            return True
        else:
            print(f"❌ Inventory request failed: {inventory_response.status_code}")
            print(f"Response: {inventory_response.text[:500]}")
            return False
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_inventory_with_auth()
    if success:
        print("\n🎉 INVENTORY TEST SUCCESSFUL!")
    else:
        print("\n❌ INVENTORY TEST FAILED!")
