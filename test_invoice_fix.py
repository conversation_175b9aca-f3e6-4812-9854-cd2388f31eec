#!/usr/bin/env python3
"""
Test script to verify the invoice generation fix
"""

import sqlite3
import os
import sys

def test_template_fix():
    """Test if the template has been fixed"""
    try:
        with open('templates/orders/view.html', 'r') as f:
            content = f.read()
        
        print("🧪 TESTING TEMPLATE FIXES")
        print("=" * 50)
        
        # Check if the problematic route is removed
        if 'finance_generate_invoice_get' in content:
            print("❌ Template still contains problematic route 'finance_generate_invoice_get'")
            return False
        else:
            print("✅ Problematic route 'finance_generate_invoice_get' removed from template")
        
        # Check if JavaScript function is added
        if 'generateInvoice(' in content:
            print("✅ JavaScript function 'generateInvoice' added to template")
        else:
            print("❌ JavaScript function 'generateInvoice' missing from template")
            return False
        
        # Check if button uses onclick
        if 'onclick="generateInvoice(' in content:
            print("✅ Invoice button uses JavaScript onclick handler")
        else:
            print("❌ Invoice button missing JavaScript onclick handler")
            return False
        
        # Check if DC generation route is correct
        if 'dc_generation.batch_selection' in content:
            print("✅ DC generation uses correct route 'dc_generation.batch_selection'")
        else:
            print("❌ DC generation route incorrect")
            return False
        
        # Check if conditional logic exists
        if '{% if challan %}' in content:
            print("✅ Invoice button has conditional logic based on challan existence")
        else:
            print("❌ Invoice button missing conditional logic")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing template: {e}")
        return False

def test_database_structure():
    """Test if database has required tables and data"""
    try:
        print("\n🗄️  TESTING DATABASE STRUCTURE")
        print("=" * 50)
        
        if not os.path.exists('instance/medivent.db'):
            print("❌ Database file 'instance/medivent.db' not found")
            return False
        
        conn = sqlite3.connect('instance/medivent.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Check required tables
        required_tables = ['orders', 'delivery_challans', 'invoices', 'order_items']
        for table in required_tables:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            if cursor.fetchone():
                print(f"✅ Table '{table}' exists")
            else:
                print(f"❌ Table '{table}' missing")
                return False
        
        # Check specific order
        order_id = 'ORD1753983391CA9E99E1'
        cursor.execute('SELECT order_id, status FROM orders WHERE order_id = ?', (order_id,))
        order = cursor.fetchone()
        
        if order:
            print(f"✅ Test order {order_id} found with status: {order['status']}")
        else:
            print(f"❌ Test order {order_id} not found")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error testing database: {e}")
        return False

def test_route_files():
    """Test if route files have correct mappings"""
    try:
        print("\n🛣️  TESTING ROUTE FILES")
        print("=" * 50)
        
        # Check batch_selection.py for correct redirects
        with open('routes/batch_selection.py', 'r') as f:
            batch_content = f.read()
        
        if 'dc_generation.batch_selection' in batch_content:
            print("✅ batch_selection.py uses correct redirect route")
        else:
            print("❌ batch_selection.py missing correct redirect route")
            return False
        
        # Check if old route references are removed
        if 'batch_selection.select_batch' in batch_content:
            print("⚠️  batch_selection.py still contains old route references")
        else:
            print("✅ batch_selection.py old route references removed")
        
        # Check app.py for POST route
        with open('app.py', 'r', encoding='utf-8', errors='ignore') as f:
            app_content = f.read()
        
        if '/finance/api/generate-invoice' in app_content and 'methods=[\'POST\']' in app_content:
            print("✅ app.py contains correct POST route for invoice generation")
        else:
            print("❌ app.py missing correct POST route for invoice generation")
            return False
        
        # Check if DC validation exists
        if 'delivery_challans WHERE order_id' in app_content:
            print("✅ app.py contains DC validation logic")
        else:
            print("❌ app.py missing DC validation logic")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing route files: {e}")
        return False

def main():
    """Run all tests"""
    print("🔧 TESTING INVOICE GENERATION FIX")
    print("=" * 60)
    
    tests = [
        ("Template Fix", test_template_fix),
        ("Database Structure", test_database_structure),
        ("Route Files", test_route_files)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        if test_func():
            passed += 1
            print(f"\n✅ {test_name} PASSED")
        else:
            print(f"\n❌ {test_name} FAILED")
    
    print("\n" + "=" * 60)
    print(f"🎯 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("\n📋 NEXT STEPS:")
        print("1. Start the Flask application: python app.py")
        print("2. Open browser: http://127.0.0.1:5001/orders/ORD1753983391CA9E99E1")
        print("3. Verify that 'Generate Invoice' button appears only after DC is generated")
        print("4. Click the button to test invoice generation")
        print("5. Check that proper validation messages appear")
    else:
        print("⚠️  Some tests failed. Please review the issues above.")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
