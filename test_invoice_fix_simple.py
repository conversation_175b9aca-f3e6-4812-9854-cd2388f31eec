#!/usr/bin/env python3
"""
Simple test for invoice generation fix
"""

import requests
import json

def test_invoice_generation_simple():
    """Simple test for invoice generation"""
    
    print("🧾 SIMPLE INVOICE GENERATION TEST")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5001"
    
    # Create session
    session = requests.Session()
    
    # Login
    print("🔐 Logging in...")
    login_data = {'username': 'admin', 'password': 'admin123'}
    login_response = session.post(f'{base_url}/login', data=login_data)
    
    if login_response.status_code != 200 or 'dashboard' not in login_response.url:
        print(f"❌ Login failed: {login_response.status_code}")
        return
    
    print("✅ Login successful")
    
    # Test invoice generation
    print("\n🧾 Testing invoice generation...")
    
    invoice_data = {
        'order_id': 'ORD00000147',
        'customer_name': 'Munir Shah',
        'order_amount': 90000.0,
        'finance_user_approved': True,
        'timestamp': '2025-08-05T16:30:00.000Z'
    }
    
    try:
        response = session.post(
            f'{base_url}/finance/api/generate-invoice',
            json=invoice_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Response: {json.dumps(result, indent=2)}")
            
            if result.get('success'):
                print(f"✅ SUCCESS: Invoice {result.get('invoice_id')} generated!")
            else:
                print(f"❌ FAILED: {result.get('error')}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")

if __name__ == "__main__":
    test_invoice_generation_simple()
