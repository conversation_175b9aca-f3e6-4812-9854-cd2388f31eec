#!/usr/bin/env python3
"""
Test invoice generation functionality
"""
import sqlite3
import sys
import os
import json

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_invoice_generation():
    """Test invoice generation API"""
    db_path = 'instance/medivent.db'
    
    if not os.path.exists(db_path):
        print(f"Database file {db_path} not found!")
        return
    
    try:
        db = sqlite3.connect(db_path)
        db.row_factory = sqlite3.Row
        
        print("=== TESTING Invoice Generation ===")
        
        # Check if invoices table exists
        tables = db.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='invoices'").fetchall()
        if tables:
            print("✅ invoices table exists")
        else:
            print("❌ invoices table missing")
            return
        
        # Get a Finance Pending order
        order = db.execute('''
            SELECT * FROM orders WHERE status = 'Finance Pending' LIMIT 1
        ''').fetchone()
        
        if not order:
            print("❌ No Finance Pending orders found")
            return
        
        print(f"✅ Found Finance Pending order: {order['order_id']}")
        print(f"   Customer: {order['customer_name']}")
        print(f"   Amount: Rs.{order['order_amount']}")
        
        # Test invoice number generation
        invoice_count = db.execute('SELECT COUNT(*) FROM invoices').fetchone()[0]
        invoice_id = f"INV{(invoice_count + 1):06d}"
        print(f"✅ Next invoice ID would be: {invoice_id}")
        
        # Check if order has customer_id (might be missing)
        if 'customer_id' not in order.keys() or not order['customer_id']:
            print("⚠️ Order missing customer_id - this might cause issues")
        
        db.close()
        
    except Exception as e:
        print(f"❌ Error testing invoice generation: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_invoice_generation()
