#!/usr/bin/env python3
"""
Comprehensive test for invoice generation fix
"""

import requests
import json
import time

def test_invoice_generation():
    """Test the fixed invoice generation functionality"""
    
    print("🧾 COMPREHENSIVE INVOICE GENERATION TEST")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:5001"
    
    # Create session for authentication
    session = requests.Session()
    
    # Step 1: Login
    print("\n🔐 STEP 1: AUTHENTICATION")
    print("-" * 30)
    
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    login_response = session.post(f'{base_url}/login', data=login_data)
    
    if login_response.status_code == 200 and 'dashboard' in login_response.url:
        print('✅ Login successful')
    else:
        print(f'❌ Login failed: {login_response.status_code}')
        return
    
    # Step 2: Test invoice generation for different scenarios
    print("\n🧾 STEP 2: TESTING INVOICE GENERATION")
    print("-" * 30)
    
    # Test cases with different order scenarios
    test_cases = [
        {
            'name': 'Order with NULL customer_id',
            'order_id': 'ORD00000147',
            'customer_name': 'Munir Shah',
            'order_amount': 90000.0
        },
        {
            'name': 'Order with existing customer',
            'order_id': 'ORD00000148',
            'customer_name': 'Test Customer',
            'order_amount': 50000.0
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 Test Case {i}: {test_case['name']}")
        print(f"Order ID: {test_case['order_id']}")
        
        invoice_data = {
            'order_id': test_case['order_id'],
            'customer_name': test_case['customer_name'],
            'order_amount': test_case['order_amount'],
            'finance_user_approved': True,
            'timestamp': time.strftime('%Y-%m-%dT%H:%M:%S.000Z')
        }
        
        try:
            response = session.post(
                f'{base_url}/finance/api/generate-invoice',
                json=invoice_data,
                headers={'Content-Type': 'application/json'}
            )
            
            print(f"Response Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"✅ Invoice generated successfully: {result.get('invoice_id')}")
                    print(f"   Message: {result.get('message')}")
                else:
                    print(f"❌ Invoice generation failed: {result.get('error')}")
                    print(f"   Details: {result.get('details', 'No details provided')}")
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"   Error: {error_data.get('error', 'Unknown error')}")
                except:
                    print(f"   Response: {response.text[:200]}...")
                    
        except Exception as e:
            print(f"❌ Request failed: {e}")
    
    # Step 3: Test pending invoices page
    print("\n📊 STEP 3: TESTING PENDING INVOICES PAGE")
    print("-" * 30)
    
    try:
        response = session.get(f'{base_url}/finance/pending-invoices')
        if response.status_code == 200:
            print("✅ Pending invoices page loads successfully")
        else:
            print(f"❌ Pending invoices page error: {response.status_code}")
    except Exception as e:
        print(f"❌ Pending invoices page request failed: {e}")
    
    # Step 4: Test accounts receivable page
    print("\n💰 STEP 4: TESTING ACCOUNTS RECEIVABLE PAGE")
    print("-" * 30)
    
    try:
        response = session.get(f'{base_url}/finance/accounts-receivable')
        if response.status_code == 200:
            print("✅ Accounts receivable page loads successfully")
        else:
            print(f"❌ Accounts receivable page error: {response.status_code}")
    except Exception as e:
        print(f"❌ Accounts receivable page request failed: {e}")
    
    # Step 5: Test finance dashboard
    print("\n📈 STEP 5: TESTING FINANCE DASHBOARD")
    print("-" * 30)
    
    try:
        response = session.get(f'{base_url}/finance/dashboard')
        if response.status_code == 200:
            print("✅ Finance dashboard loads successfully")
        else:
            print(f"❌ Finance dashboard error: {response.status_code}")
    except Exception as e:
        print(f"❌ Finance dashboard request failed: {e}")
    
    print("\n🎉 COMPREHENSIVE TEST COMPLETED!")
    print("=" * 60)

if __name__ == "__main__":
    test_invoice_generation()
