import requests
import time

print('🔍 TESTING INVOICE GENERATION FIX')
print('=' * 50)

time.sleep(2)

base_url = 'http://127.0.0.1:5001'

# Create a session and login
session = requests.Session()

# Login first
login_data = {
    'username': 'admin',
    'password': 'admin123'
}

print('🔐 Logging in...')
try:
    login_response = session.post(f'{base_url}/login', data=login_data)
    print(f'Login status: {login_response.status_code}')
    
    if login_response.status_code == 200:
        print('✅ Login successful')
        
        # Test invoice generation for an order with NULL customer_id
        test_order_id = 'ORD00000147'  # This order has NULL customer_id
        
        invoice_data = {
            'order_id': test_order_id,
            'customer_name': 'Munir Shah',
            'order_amount': 90000.0,
            'finance_user_approved': True,
            'timestamp': '2025-08-05T16:30:00.000Z'
        }
        
        print(f'\n🧾 Testing invoice generation for {test_order_id}...')
        response = session.post(f'{base_url}/finance/api/generate-invoice', 
                               json=invoice_data,
                               headers={'Content-Type': 'application/json'})
        
        print(f'Invoice generation status: {response.status_code}')
        
        if response.status_code == 200:
            try:
                result = response.json()
                if result.get('success'):
                    print('✅ Invoice generation successful!')
                    print(f'   Invoice ID: {result.get("invoice_id")}')
                    print(f'   Message: {result.get("message")}')
                else:
                    print(f'❌ Invoice generation failed: {result.get("error")}')
            except Exception as e:
                print(f'❌ JSON parse error: {e}')
                print(f'Response: {response.text[:300]}...')
        else:
            print(f'❌ HTTP Error: {response.status_code}')
            print(f'Response: {response.text[:300]}...')
            
    else:
        print('❌ Login failed')
        print(f'Response: {login_response.text[:200]}...')
        
except Exception as e:
    print(f'❌ Error: {e}')
