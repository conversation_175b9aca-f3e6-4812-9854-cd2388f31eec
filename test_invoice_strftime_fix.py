#!/usr/bin/env python3
"""
Test script to verify the strftime error fix in invoice functionality
"""

import requests
import sys
import json
from datetime import datetime

def test_invoice_routes():
    """Test invoice-related routes to ensure no strftime errors"""
    
    base_url = "http://localhost:5000"
    
    # Test routes that might trigger the strftime error
    test_routes = [
        "/orders",  # Orders list page
        "/orders/workflow",  # Workflow page
        "/finance/dashboard",  # Finance dashboard
    ]
    
    print("🧪 Testing Invoice strftime Fix")
    print("=" * 50)
    
    session = requests.Session()
    
    # First, try to access the login page to get session
    try:
        login_response = session.get(f"{base_url}/login")
        if login_response.status_code != 200:
            print(f"❌ Cannot access login page: {login_response.status_code}")
            return False
        print("✅ Login page accessible")
    except Exception as e:
        print(f"❌ Error accessing login page: {e}")
        return False
    
    # Test each route
    all_passed = True
    
    for route in test_routes:
        try:
            print(f"\n🔍 Testing route: {route}")
            response = session.get(f"{base_url}{route}")
            
            if response.status_code == 200:
                print(f"✅ Route {route}: HTTP 200 OK")
                
                # Check for strftime error in response
                if "strftime" in response.text.lower() and "error" in response.text.lower():
                    print(f"❌ Route {route}: strftime error detected in response")
                    all_passed = False
                else:
                    print(f"✅ Route {route}: No strftime errors detected")
                    
            elif response.status_code == 302:
                print(f"🔄 Route {route}: Redirected (likely to login) - {response.status_code}")
            else:
                print(f"⚠️  Route {route}: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ Route {route}: Error - {e}")
            all_passed = False
    
    return all_passed

def test_template_rendering():
    """Test template rendering with datetime objects"""
    
    print("\n🎨 Testing Template Rendering")
    print("=" * 50)
    
    try:
        # Import Flask app components
        sys.path.append('.')
        from app import app
        
        with app.test_client() as client:
            with app.app_context():
                # Test template rendering with datetime
                from flask import render_template_string
                
                # Test the fixed template syntax
                test_template = """
                <p>Current time: {{ now | format_datetime('%d-%m-%Y / %H:%M:%S') }}</p>
                <p>Date only: {{ now | format_datetime('%d %b %Y') }}</p>
                """
                
                result = render_template_string(test_template, now=datetime.now())
                
                if "format_datetime" in result:
                    print("❌ Template filter not working - filter name appears in output")
                    return False
                else:
                    print("✅ Template rendering successful")
                    print(f"📄 Rendered output: {result.strip()}")
                    return True
                    
    except Exception as e:
        print(f"❌ Template rendering error: {e}")
        return False

def main():
    """Main test function"""
    
    print("🚀 Invoice strftime Error Fix Verification")
    print("=" * 60)
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test 1: Template rendering
    template_test = test_template_rendering()
    
    # Test 2: Route accessibility
    route_test = test_invoice_routes()
    
    print("\n📊 Test Summary")
    print("=" * 50)
    print(f"Template Rendering: {'✅ PASS' if template_test else '❌ FAIL'}")
    print(f"Route Testing: {'✅ PASS' if route_test else '❌ FAIL'}")
    
    overall_result = template_test and route_test
    print(f"\n🎯 Overall Result: {'✅ ALL TESTS PASSED' if overall_result else '❌ SOME TESTS FAILED'}")
    
    if overall_result:
        print("\n🎉 The strftime error fix appears to be working correctly!")
        print("💡 Invoice viewing should now work without errors.")
    else:
        print("\n⚠️  Some issues detected. Please review the test output above.")
    
    return overall_result

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
