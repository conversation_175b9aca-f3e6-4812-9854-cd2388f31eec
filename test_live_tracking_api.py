#!/usr/bin/env python3
"""
Test the live tracking API endpoint that might be causing the strftime error
"""

import requests
import json
import sys

def test_live_tracking_api():
    """Test the live tracking API endpoint"""
    print("🔍 TESTING LIVE TRACKING API ENDPOINT")
    print("=" * 60)
    
    try:
        # Test the API endpoint that the sidebar calls
        response = requests.get('http://localhost:5000/riders/api/live-tracking-data', timeout=10)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("✅ JSON response received successfully")
                
                # Check the structure
                if 'active_orders' in data:
                    print(f"✅ Active orders: {len(data['active_orders'])} found")
                    
                    # Check each active order for datetime formatting
                    for i, order in enumerate(data['active_orders']):
                        dispatch_time = order.get('dispatch_time', 'N/A')
                        print(f"  Order {i+1}: {order.get('order_id')} - Dispatch: {dispatch_time}")
                
                if 'recent_updates' in data:
                    print(f"✅ Recent updates: {len(data['recent_updates'])} found")
                
                if 'today_stats' in data:
                    print(f"✅ Today stats: {data['today_stats']}")
                
                if 'last_updated' in data:
                    print(f"✅ Last updated: {data['last_updated']}")
                
                return True
                
            except json.JSONDecodeError as e:
                print(f"❌ JSON decode error: {e}")
                print(f"Response content: {response.text[:500]}")
                return False
                
        elif response.status_code == 500:
            print("❌ Internal Server Error - This might be the strftime error!")
            print(f"Response content: {response.text[:1000]}")
            
            # Check for strftime error specifically
            if "'str' object has no attribute 'strftime'" in response.text:
                print("🎯 FOUND THE STRFTIME ERROR IN LIVE TRACKING API!")
                return False
            
            return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response content: {response.text[:500]}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Server might not be running")
        return False
    except Exception as e:
        print(f"❌ Request Error: {e}")
        return False

def test_assignment_dashboard_with_api():
    """Test assignment dashboard and check if API calls are causing errors"""
    print("\n🌐 TESTING ASSIGNMENT DASHBOARD WITH API MONITORING")
    print("=" * 60)
    
    try:
        # First test the main page
        response = requests.get('http://localhost:5000/riders/assignment-dashboard', timeout=10)
        
        print(f"Main page status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Main assignment dashboard loads")
            
            # Check for error messages in the HTML
            if "Error loading assignment form" in response.text:
                print("❌ Error message found in HTML")
                return False
            else:
                print("✅ No error messages in main HTML")
            
            # Now test the API that the sidebar calls
            print("\n🔄 Testing live tracking API (called by sidebar)...")
            api_success = test_live_tracking_api()
            
            if not api_success:
                print("❌ Live tracking API failed - this is likely causing the error!")
                return False
            else:
                print("✅ Live tracking API working correctly")
                return True
        else:
            print(f"❌ Main page error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_assignment_dashboard_with_api()
    if success:
        print("\n🎉 SUCCESS: ALL TESTS PASSED!")
        sys.exit(0)
    else:
        print("\n❌ FAILURE: ERRORS FOUND")
        sys.exit(1)
