#!/usr/bin/env python3
"""
Minimal API test to verify blueprint registration
"""

from flask import Flask, jsonify
import sys
import traceback

def create_test_app():
    """Create minimal Flask app with API blueprint"""
    try:
        app = Flask(__name__)
        app.config['SECRET_KEY'] = 'test-key'
        
        # Import and register API blueprint
        from api_endpoints import api_bp
        app.register_blueprint(api_bp)
        
        print("✅ API blueprint registered successfully")
        
        # List all routes
        with app.app_context():
            rules = list(app.url_map.iter_rules())
            print(f"\n📋 Registered routes ({len(rules)} total):")
            
            api_routes = []
            for rule in rules:
                if rule.rule.startswith('/api/'):
                    api_routes.append(rule)
                    print(f"   ✅ {rule.rule} -> {rule.endpoint}")
            
            if not api_routes:
                print("   ❌ No API routes found!")
                return None
            
            print(f"\n🎯 Found {len(api_routes)} API routes")
        
        return app
        
    except Exception as e:
        print(f"❌ Error creating app: {e}")
        traceback.print_exc()
        return None

def test_api_routes():
    """Test API routes directly"""
    app = create_test_app()
    if not app:
        return False
    
    try:
        with app.test_client() as client:
            print("\n🧪 Testing API endpoints...")
            
            # Test order details API
            print("Testing /api/order-details/ORD00000155...")
            response = client.get('/api/order-details/ORD00000155')
            print(f"   Status: {response.status_code}")
            print(f"   Content-Type: {response.content_type}")
            print(f"   Data length: {len(response.data)}")
            
            if response.status_code == 200:
                try:
                    data = response.get_json()
                    print(f"   JSON Success: {data.get('success') if data else 'No JSON'}")
                except:
                    print(f"   Raw response: {response.data[:100]}")
            
            # Test QR code API
            print("\nTesting /api/order-qr-code/ORD00000155...")
            response = client.get('/api/order-qr-code/ORD00000155?branding=true')
            print(f"   Status: {response.status_code}")
            print(f"   Content-Type: {response.content_type}")
            print(f"   Data length: {len(response.data)}")
            
            if response.status_code == 200:
                try:
                    data = response.get_json()
                    print(f"   JSON Success: {data.get('success') if data else 'No JSON'}")
                except:
                    print(f"   Raw response: {response.data[:100]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing routes: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 MINIMAL API TEST")
    print("=" * 50)
    
    success = test_api_routes()
    
    if success:
        print("\n✅ API test completed")
    else:
        print("\n❌ API test failed")
    
    sys.exit(0 if success else 1)
