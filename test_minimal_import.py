#!/usr/bin/env python3
"""
Minimal test to identify what's causing the Flask app import to hang
"""

print("🔍 TESTING MINIMAL FLASK APP IMPORT")

try:
    print("📦 Step 1: Testing basic imports...")
    import os
    import sqlite3
    from flask import Flask
    print("✅ Basic imports successful")
    
    print("📦 Step 2: Testing Flask app creation...")
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'test-key'
    app.config['DATABASE'] = 'test.db'
    print("✅ Flask app creation successful")
    
    print("📦 Step 3: Testing database helper function...")
    from flask import g
    
    def get_db():
        if 'db' not in g:
            g.db = sqlite3.connect(app.config['DATABASE'])
            g.db.row_factory = sqlite3.Row
        return g.db
    
    print("✅ Database helper function defined")
    
    print("📦 Step 4: Testing app context...")
    with app.app_context():
        print("✅ App context works")
    
    print("📦 Step 5: Testing route registration...")
    @app.route('/')
    def test_route():
        return "Test"
    
    print("✅ Route registration successful")
    
    print("📦 Step 6: Testing URL map...")
    print(f"Number of routes: {len(list(app.url_map.iter_rules()))}")
    print("✅ URL map accessible")
    
    print("🎉 ALL TESTS PASSED - Basic Flask app works fine")
    
except Exception as e:
    print(f"❌ ERROR: {e}")
    import traceback
    traceback.print_exc()
