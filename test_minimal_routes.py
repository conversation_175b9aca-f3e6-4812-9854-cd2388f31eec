#!/usr/bin/env python3
"""
Minimal test to check if rider routes are working
"""

def test_minimal():
    try:
        print("🧪 Testing minimal route setup...")
        
        # Import Flask
        from flask import Flask
        print("✅ Flask imported")
        
        # Import the blueprint
        from routes.modern_riders import riders_bp
        print("✅ riders_bp imported")
        
        # Create test app
        app = Flask(__name__)
        app.secret_key = 'test'
        
        # Register blueprint
        app.register_blueprint(riders_bp)
        print("✅ Blueprint registered")
        
        # Check routes
        with app.app_context():
            routes = []
            for rule in app.url_map.iter_rules():
                if 'riders' in rule.endpoint:
                    routes.append(f"{rule.rule} -> {rule.endpoint}")
            
            print(f"✅ Found {len(routes)} rider routes:")
            for route in routes[:10]:  # Show first 10
                print(f"   {route}")
                
        # Test specific routes
        assignment_found = any('assignment_dashboard' in route for route in routes)
        self_pickup_found = any('self_pickup' in route for route in routes)
        
        print(f"✅ assignment_dashboard found: {assignment_found}")
        print(f"✅ self_pickup found: {self_pickup_found}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 MINIMAL ROUTE TEST")
    print("=" * 40)
    
    success = test_minimal()
    
    print("=" * 40)
    if success:
        print("🎉 Test completed!")
    else:
        print("❌ Test failed!")
