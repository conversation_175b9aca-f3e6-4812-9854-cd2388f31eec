#!/usr/bin/env python3
"""
Minimal test server to verify core functionality
"""

import os
import sqlite3
from flask import Flask, render_template, redirect, url_for, g
from flask_login import LoginManager, login_required

print("🔍 CREATING MINIMAL TEST SERVER")

# Create Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'test-secret-key'
app.config['DATABASE'] = 'instance/medivent.db'

# Database helper
def get_db():
    if 'db' not in g:
        g.db = sqlite3.connect(app.config['DATABASE'])
        g.db.row_factory = sqlite3.Row
    return g.db

def close_db(e=None):
    db = g.pop('db', None)
    if db is not None:
        db.close()

app.teardown_appcontext(close_db)

# Initialize login manager
login_manager = LoginManager(app)
login_manager.login_view = 'login'

@login_manager.user_loader
def load_user(user_id):
    return None  # Simplified for testing

# Test routes
@app.route('/')
def index():
    return "✅ Minimal server is working!"

@app.route('/product_management')
def product_management_redirect():
    """Test the direct product management route"""
    return "✅ Direct product_management route is working!"

@app.route('/test-db')
def test_db():
    """Test database connection"""
    try:
        db = get_db()
        cursor = db.execute("SELECT name FROM sqlite_master WHERE type='table' LIMIT 5")
        tables = cursor.fetchall()
        return f"✅ Database connection working! Found {len(tables)} tables"
    except Exception as e:
        return f"❌ Database error: {e}"

if __name__ == "__main__":
    print("🚀 Starting minimal test server...")
    print("📍 URL: http://127.0.0.1:5001")
    print("🧪 Test routes:")
    print("   • http://127.0.0.1:5001/")
    print("   • http://127.0.0.1:5001/product_management")
    print("   • http://127.0.0.1:5001/test-db")
    
    try:
        app.run(host='127.0.0.1', port=5001, debug=True, use_reloader=False)
    except OSError as e:
        if "Address already in use" in str(e):
            print(">> Port 5001 is busy, trying port 5002...")
            app.run(host='127.0.0.1', port=5002, debug=True, use_reloader=False)
        else:
            raise e
