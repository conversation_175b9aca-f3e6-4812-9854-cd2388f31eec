#!/usr/bin/env python3
"""
Test script to check if pack modal elements are present in the DOM
"""

import requests
import re
from datetime import datetime

def test_modal_dom_presence():
    """Test if modal elements are actually present in the rendered HTML"""
    print("🔍 TESTING MODAL DOM PRESENCE")
    print("=" * 60)
    
    try:
        # Get the warehouse packing page
        response = requests.get('http://127.0.0.1:5001/warehouse/packing', timeout=15)
        print(f"   Page Status: {response.status_code}")
        
        if response.status_code != 200:
            print(f"   ❌ Failed to load page: {response.status_code}")
            return False
        
        content = response.text
        
        # Check for pack modal elements
        modal_checks = {
            'Pack Order Modal': {
                'pattern': r'<div[^>]*id="packOrderModal"[^>]*>',
                'description': 'Main pack order modal container'
            },
            'Pack Order Form': {
                'pattern': r'<form[^>]*id="packOrderForm"[^>]*>',
                'description': 'Pack order form element'
            },
            'Pack Order ID Input': {
                'pattern': r'<input[^>]*id="packOrderId"[^>]*>',
                'description': 'Hidden input for order ID'
            },
            'Packed By Input': {
                'pattern': r'<input[^>]*id="packedBy"[^>]*>',
                'description': 'Input for packed by field'
            },
            'Packing Notes Textarea': {
                'pattern': r'<textarea[^>]*id="packingNotes"[^>]*>',
                'description': 'Textarea for packing notes'
            }
        }
        
        print("\n📋 MODAL ELEMENT CHECKS:")
        all_found = True
        
        for element_name, check_info in modal_checks.items():
            pattern = check_info['pattern']
            description = check_info['description']
            
            matches = re.findall(pattern, content, re.IGNORECASE)
            
            if matches:
                print(f"   ✅ {element_name}: Found")
                print(f"      📝 {description}")
                if len(matches) > 1:
                    print(f"      ⚠️ Multiple matches found: {len(matches)}")
            else:
                print(f"   ❌ {element_name}: NOT FOUND")
                print(f"      📝 {description}")
                all_found = False
        
        # Check for JavaScript function definitions
        print("\n📋 JAVASCRIPT FUNCTION CHECKS:")
        
        js_functions = {
            'packOrderNow': 'function packOrderNow(',
            'packOrderV1': 'function packOrderV1(',
            'handlePackInline': 'function handlePackInline(',
            'confirmPackOrder': 'function confirmPackOrder('
        }
        
        for func_name, pattern in js_functions.items():
            if pattern in content:
                print(f"   ✅ {func_name}: Function defined")
            else:
                print(f"   ❌ {func_name}: Function NOT defined")
                all_found = False
        
        # Check for jQuery and Bootstrap
        print("\n📋 DEPENDENCY CHECKS:")
        
        dependencies = {
            'jQuery': ['jquery', '$'],
            'Bootstrap JS': ['bootstrap.min.js', 'bootstrap.bundle.js'],
            'Bootstrap Modal': ['.modal(', 'modal(\'show\')']
        }
        
        for dep_name, patterns in dependencies.items():
            found = any(pattern in content.lower() for pattern in patterns)
            status = "✅" if found else "❌"
            print(f"   {status} {dep_name}: {'Found' if found else 'NOT Found'}")
        
        # Check for potential conflicts
        print("\n📋 CONFLICT CHECKS:")
        
        # Check for multiple modal definitions
        pack_modal_count = len(re.findall(r'id="pack.*Modal"', content, re.IGNORECASE))
        print(f"   Pack Modal Count: {pack_modal_count}")
        
        if pack_modal_count > 1:
            print("   ⚠️ Multiple pack modals detected - potential conflict!")
            # Find all pack modal IDs
            pack_modal_ids = re.findall(r'id="(pack[^"]*Modal[^"]*)"', content, re.IGNORECASE)
            for modal_id in pack_modal_ids:
                print(f"      📋 Found modal ID: {modal_id}")
        
        # Check for duplicate function definitions
        duplicate_functions = []
        for func_name in js_functions.keys():
            pattern = f'function {func_name}('
            count = content.count(pattern)
            if count > 1:
                duplicate_functions.append(f"{func_name} ({count} times)")
        
        if duplicate_functions:
            print("   ⚠️ Duplicate function definitions:")
            for dup in duplicate_functions:
                print(f"      📋 {dup}")
        else:
            print("   ✅ No duplicate function definitions")
        
        return all_found
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_specific_modal_structure():
    """Test the specific structure of the pack modal"""
    print("\n🔍 TESTING SPECIFIC MODAL STRUCTURE")
    print("=" * 60)
    
    try:
        response = requests.get('http://127.0.0.1:5001/warehouse/packing', timeout=15)
        
        if response.status_code != 200:
            print(f"   ❌ Failed to load page: {response.status_code}")
            return False
        
        content = response.text
        
        # Extract the pack modal HTML
        modal_pattern = r'<div[^>]*id="packOrderModal"[^>]*>.*?</div>\s*</div>\s*</div>'
        modal_matches = re.findall(modal_pattern, content, re.DOTALL | re.IGNORECASE)
        
        if modal_matches:
            modal_html = modal_matches[0]
            print("   ✅ Pack Order Modal HTML found")
            
            # Check modal structure
            structure_checks = {
                'Modal Header': r'<div[^>]*class="[^"]*modal-header[^"]*"',
                'Modal Body': r'<div[^>]*class="[^"]*modal-body[^"]*"',
                'Modal Footer': r'<div[^>]*class="[^"]*modal-footer[^"]*"',
                'Close Button': r'<button[^>]*data-dismiss="modal"',
                'Form Element': r'<form[^>]*id="packOrderForm"'
            }
            
            print("\n   📋 Modal Structure:")
            for check_name, pattern in structure_checks.items():
                if re.search(pattern, modal_html, re.IGNORECASE):
                    print(f"      ✅ {check_name}")
                else:
                    print(f"      ❌ {check_name}")
            
            # Show a snippet of the modal HTML
            print(f"\n   📄 Modal HTML snippet (first 200 chars):")
            print(f"      {modal_html[:200]}...")
            
        else:
            print("   ❌ Pack Order Modal HTML not found")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 MODAL DOM PRESENCE TEST")
    print("=" * 80)
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Wait for server
    print("\n⏳ Waiting for server...")
    import time
    time.sleep(2)
    
    # Run tests
    dom_test = test_modal_dom_presence()
    structure_test = test_specific_modal_structure()
    
    print("\n" + "="*80)
    print("🏁 MODAL DOM PRESENCE TEST COMPLETED")
    print(f"⏰ Test finished at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    if dom_test and structure_test:
        print("✅ ALL MODAL ELEMENTS PRESENT - Issue is likely in JavaScript execution")
        print("\n📋 NEXT STEPS:")
        print("1. Check browser console for JavaScript errors")
        print("2. Test modal opening manually in browser")
        print("3. Verify jQuery and Bootstrap are loaded")
        print("4. Check for JavaScript execution order issues")
    else:
        print("❌ MODAL ELEMENTS MISSING - Issue is in HTML structure")
        print("\n📋 NEXT STEPS:")
        print("1. Check template inheritance")
        print("2. Verify modal HTML is properly included")
        print("3. Check for template rendering errors")

if __name__ == "__main__":
    main()
