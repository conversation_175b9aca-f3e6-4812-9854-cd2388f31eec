#!/usr/bin/env python3
"""
Quick test to verify modal is now included
"""

import requests

def test_modal_inclusion():
    """Test if modal is now properly included"""
    print("🧪 QUICK MODAL INCLUSION TEST")
    print("=" * 40)
    
    try:
        response = requests.get('http://127.0.0.1:5001/warehouse/packing', timeout=10)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text
            
            # Check for modal
            modal_count = content.count('id="orderDetailsModal"')
            print(f"Modal instances: {modal_count}")
            
            # Check for key components
            checks = [
                ('Modal Dialog', 'modal-dialog' in content),
                ('Modal Loading State', 'modalLoadingState' in content),
                ('Order ID Display', 'orderIdDisplay' in content),
                ('Customer Name Display', 'customerNameDisplay' in content),
                ('JavaScript File', 'order_details_modal.js' in content),
                ('View Details Button', 'viewOrderDetails' in content)
            ]
            
            print("\nComponent Check:")
            for name, found in checks:
                status = "✅" if found else "❌"
                print(f"   {status} {name}")
            
            # Check for orders
            if 'ORD00000155' in content:
                print("   ✅ Test order found")
            else:
                print("   ❌ Test order not found")
                
            return modal_count == 1 and all(check[1] for check in checks)
            
        else:
            print(f"❌ Failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_modal_inclusion()
    
    if success:
        print("\n✅ MODAL FIX SUCCESSFUL!")
        print("📋 Next steps:")
        print("   1. Refresh browser page")
        print("   2. Click 'View Details' on any order")
        print("   3. Modal should now show with proper structure")
    else:
        print("\n❌ MODAL STILL HAS ISSUES")
        print("📋 Need further investigation")
