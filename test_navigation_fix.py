#!/usr/bin/env python3
"""
Test the navigation fixes for products
"""

import requests
import time

def test_navigation_routes():
    """Test all product navigation routes"""
    base_url = "http://192.168.99.34:5001"
    
    routes_to_test = [
        ("/products/product_management", "Product Management"),
        ("/products/new", "New Product"),
        ("/products/update_selection", "Update Product Selection"),
        ("/products/view_all", "View All Products"),
    ]
    
    print("🧪 TESTING NAVIGATION ROUTES")
    print("=" * 50)
    
    passed = 0
    total = len(routes_to_test)
    
    for route, name in routes_to_test:
        try:
            url = f"{base_url}{route}"
            print(f"Testing {name}: {url}")
            
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                print(f"✅ {name} - Status: {response.status_code}")
                
                # Check for specific error messages
                content = response.text.lower()
                if 'error loading products' in content and 'stats' in content and 'undefined' in content:
                    print(f"❌ {name} - Still has 'stats is undefined' error")
                    return False
                elif 'error' in content and 'undefined' in content:
                    print(f"⚠️ {name} - May have other undefined errors")
                else:
                    print(f"✅ {name} - No undefined errors detected")
                    passed += 1
            else:
                print(f"❌ {name} - Status: {response.status_code}")
                
        except Exception as e:
            print(f"❌ {name} - Error: {str(e)}")
        
        time.sleep(0.5)
    
    print("\n" + "=" * 50)
    print(f"📊 RESULTS: {passed}/{total} routes passed")
    
    if passed == total:
        print("🎉 ALL NAVIGATION ROUTES ARE WORKING!")
        print("✅ Template URL fixes have been applied")
        print("✅ Blueprint routing is working correctly")
        return True
    else:
        print("❌ Some routes still have issues")
        return False

if __name__ == "__main__":
    success = test_navigation_routes()
    
    if success:
        print("\n🎯 NEXT: Test the specific navigation link that was failing")
        print("Navigate to the main page and click 'Update Product' in the menu")
    else:
        print("\n❌ Navigation routes still need fixing")
