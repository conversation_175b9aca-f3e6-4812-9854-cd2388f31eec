#!/usr/bin/env python3
"""
Network Configuration Test Script for Flask ERP
Tests network connectivity and configuration for multi-device access
"""

import socket
import subprocess
import sys
import platform
import requests
from datetime import datetime

def get_local_ip():
    """Get the local IP address of this machine"""
    try:
        # Connect to a remote address to determine local IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception as e:
        print(f"Error getting local IP: {e}")
        return None

def check_port_availability(port):
    """Check if a port is available for binding"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.bind(('0.0.0.0', port))
        s.close()
        return True
    except OSError:
        return False

def test_flask_connectivity():
    """Test if Flask application is running and accessible"""
    local_ip = get_local_ip()
    if not local_ip:
        return False, "Could not determine local IP"
    
    test_urls = [
        f"http://localhost:5000",
        f"http://127.0.0.1:5000",
        f"http://{local_ip}:5000",
        f"http://0.0.0.0:5000"
    ]
    
    results = {}
    for url in test_urls:
        try:
            response = requests.get(url, timeout=5)
            results[url] = f"✅ Status: {response.status_code}"
        except requests.exceptions.ConnectionError:
            results[url] = "❌ Connection refused (app not running?)"
        except requests.exceptions.Timeout:
            results[url] = "⏱️ Timeout"
        except Exception as e:
            results[url] = f"❌ Error: {str(e)}"
    
    return True, results

def get_network_interfaces():
    """Get network interface information"""
    try:
        if platform.system() == "Windows":
            result = subprocess.run(['ipconfig'], capture_output=True, text=True, timeout=10)
            return result.stdout
        else:
            result = subprocess.run(['ifconfig'], capture_output=True, text=True, timeout=10)
            return result.stdout
    except Exception as e:
        return f"Error getting network interfaces: {e}"

def main():
    """Main test function"""
    print("=" * 60)
    print("🚀 Flask ERP Network Configuration Test")
    print("=" * 60)
    print(f"Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test 1: Get local IP
    print("📍 1. Local IP Address Detection")
    local_ip = get_local_ip()
    if local_ip:
        print(f"   ✅ Local IP: {local_ip}")
    else:
        print("   ❌ Could not determine local IP")
    print()
    
    # Test 2: Check port availability
    print("🔌 2. Port Availability Check")
    ports_to_check = [5000, 8080]
    for port in ports_to_check:
        available = check_port_availability(port)
        status = "✅ Available" if available else "❌ In use"
        print(f"   Port {port}: {status}")
    print()
    
    # Test 3: Network interfaces
    print("🌐 3. Network Interfaces")
    interfaces = get_network_interfaces()
    print("   Network configuration:")
    print("   " + "\n   ".join(interfaces.split('\n')[:10]))  # Show first 10 lines
    print()
    
    # Test 4: Flask connectivity (if app is running)
    print("🔗 4. Flask Application Connectivity Test")
    success, results = test_flask_connectivity()
    if success and isinstance(results, dict):
        for url, status in results.items():
            print(f"   {url}: {status}")
    else:
        print(f"   ❌ {results}")
    print()
    
    # Test 5: Firewall recommendations
    print("🔥 5. Firewall Configuration Recommendations")
    if platform.system() == "Windows":
        print("   Windows Firewall Commands:")
        print("   netsh advfirewall firewall add rule name=\"Flask ERP Port 5000\" dir=in action=allow protocol=TCP localport=5000")
        print("   netsh advfirewall firewall add rule name=\"Flask ERP Port 8080\" dir=in action=allow protocol=TCP localport=8080")
    elif platform.system() == "Darwin":  # macOS
        print("   macOS: Configure firewall in System Preferences > Security & Privacy > Firewall")
    else:  # Linux
        print("   Linux UFW Commands:")
        print("   sudo ufw allow 5000")
        print("   sudo ufw allow 8080")
    print()
    
    # Summary
    print("📋 6. Network Access Summary")
    if local_ip:
        print(f"   🌍 Access URLs for other devices:")
        print(f"   Primary: http://{local_ip}:5000")
        print(f"   Fallback: http://{local_ip}:8080")
        print()
        print(f"   📱 Mobile/Tablet Access:")
        print(f"   Connect to same WiFi, then open: http://{local_ip}:5000")
    else:
        print("   ❌ Cannot provide access URLs - IP detection failed")
    print()
    
    print("=" * 60)
    print("✅ Network configuration test completed!")
    print("💡 Next steps:")
    print("   1. Configure firewall (see recommendations above)")
    print("   2. Start Flask app: python app.py")
    print("   3. Test access from other devices using the URLs above")
    print("=" * 60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"\n\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
