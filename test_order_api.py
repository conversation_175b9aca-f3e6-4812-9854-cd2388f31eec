#!/usr/bin/env python3
"""
Test Order API endpoints directly
"""

import requests
import json
import time

def test_order_api():
    """Test order API endpoints"""
    print("🔍 TESTING ORDER API ENDPOINTS")
    print("=" * 50)
    
    base_url = 'http://127.0.0.1:5001'
    
    # Test endpoints
    endpoints = [
        ('/api/test', 'Test API'),
        ('/api/order-details/ORD00000155', 'Order Details'),
        ('/api/order-qr-code/ORD00000155', 'QR Code'),
    ]
    
    for endpoint, description in endpoints:
        print(f"\n📡 Testing: {description}")
        print(f"   URL: {base_url}{endpoint}")
        
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   Success: {data.get('success', 'Unknown')}")
                    
                    if endpoint == '/api/order-details/ORD00000155' and data.get('success'):
                        order = data.get('order', {})
                        items = data.get('order_items', [])
                        print(f"   Order: {order.get('order_id')} - {order.get('customer_name')}")
                        print(f"   Items: {len(items)} products")
                        
                        if items:
                            for i, item in enumerate(items[:3]):  # Show first 3 items
                                print(f"     {i+1}. {item.get('product_name')} (Qty: {item.get('quantity')})")
                        
                        return True, data
                        
                except json.JSONDecodeError:
                    print(f"   ❌ Invalid JSON response")
                    print(f"   Raw: {response.text[:200]}")
            else:
                print(f"   ❌ HTTP Error: {response.text[:200]}")
                
        except requests.exceptions.ConnectionError:
            print(f"   ❌ Connection Error - Flask app not running?")
        except requests.exceptions.Timeout:
            print(f"   ❌ Timeout")
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    return False, None

def check_flask_app():
    """Check if Flask app is running"""
    print("\n🔍 CHECKING FLASK APP STATUS")
    print("=" * 50)
    
    try:
        response = requests.get('http://127.0.0.1:5001/', timeout=3)
        print(f"✅ Flask app is running (Status: {response.status_code})")
        return True
    except requests.exceptions.ConnectionError:
        print("❌ Flask app is NOT running")
        print("   Please start the Flask app first:")
        print("   python app.py")
        return False
    except Exception as e:
        print(f"❌ Error checking Flask app: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 ORDER API TESTING")
    print("=" * 70)
    
    # Check if Flask app is running
    if not check_flask_app():
        return False
    
    # Test API endpoints
    success, data = test_order_api()
    
    print("\n" + "=" * 70)
    print("📊 TEST RESULTS")
    print("=" * 70)
    
    if success:
        print("✅ API IS WORKING!")
        print("   The issue is in the JavaScript, not the API")
        print("\n🔧 NEXT STEPS:")
        print("   1. Open browser Developer Tools (F12)")
        print("   2. Go to Console tab")
        print("   3. Click 'View Details' on an order")
        print("   4. Check for JavaScript errors")
        print("   5. Check Network tab for failed API calls")
        
        return True
    else:
        print("❌ API ISSUES FOUND")
        print("   Need to fix API or Flask app first")
        return False

if __name__ == "__main__":
    main()
