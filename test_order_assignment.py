#!/usr/bin/env python3
"""
🔍 TEST ORDER ASSIGNMENT FUNCTIONALITY
Tests the specific order assignment route that was causing Error 3
"""

import requests
import time
import sys

BASE_URL = "http://localhost:5000"

def test_assignment_route(order_id):
    """Test the manual assignment route for a specific order"""
    url = f"{BASE_URL}/riders/manual-assign/{order_id}"
    print(f"🧪 Testing Order Assignment: {url}")
    
    try:
        response = requests.get(url, timeout=10)
        status = response.status_code
        
        if status == 200:
            print(f"✅ Order Assignment Route: HTTP {status} - SUCCESS")
            print("   📋 Assignment form loaded successfully")
            return True
        elif status == 302:
            print(f"✅ Order Assignment Route: HTTP {status} - REDIRECT (Expected)")
            print("   🔄 Redirected (likely due to order not meeting criteria)")
            return True
        else:
            print(f"❌ Order Assignment Route: HTTP {status} - FAILED")
            print(f"   Response: {response.text[:200]}...")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Order Assignment Route: CONNECTION ERROR - {e}")
        return False

def main():
    """Main testing function"""
    print("🚀 TESTING ORDER ASSIGNMENT FUNCTIONALITY")
    print("=" * 60)
    
    # Test the specific order that was mentioned in Error 3
    test_order_id = "ORD175355078A5CED085"
    
    print(f"Testing assignment for order: {test_order_id}")
    success = test_assignment_route(test_order_id)
    
    # Test a few more sample order IDs
    sample_orders = ["ORD001", "ORD002", "ORD003"]
    
    results = [(f"Order {test_order_id}", success)]
    
    for order_id in sample_orders:
        print(f"\nTesting assignment for sample order: {order_id}")
        success = test_assignment_route(order_id)
        results.append((f"Order {order_id}", success))
        time.sleep(1)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 ORDER ASSIGNMENT TEST RESULTS")
    print("=" * 60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for order_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} - {order_name}")
    
    print(f"\n🎯 OVERALL: {passed}/{total} assignment tests passed ({passed/total*100:.1f}%)")
    
    if passed >= total * 0.5:  # Allow some failures for non-existent orders
        print("🎉 ORDER ASSIGNMENT FUNCTIONALITY WORKING!")
        print("💡 Note: Redirects are expected for orders that don't meet assignment criteria")
        return 0
    else:
        print("⚠️  ORDER ASSIGNMENT NEEDS ATTENTION")
        return 1

if __name__ == "__main__":
    sys.exit(main())
