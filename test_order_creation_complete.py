#!/usr/bin/env python3
"""
Complete test of order creation with transaction fix
"""

import requests
import json
import sys
from datetime import datetime

def test_order_creation_complete():
    """Test complete order creation workflow"""
    
    print("🧪 Complete Order Creation Test")
    print("=" * 40)
    
    base_url = "http://localhost:5000"
    
    try:
        # Create a session to maintain login
        session = requests.Session()
        
        # Step 1: Login
        print("🔐 Step 1: Logging in...")
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        login_response = session.post(f"{base_url}/login", data=login_data, allow_redirects=False)
        
        if login_response.status_code == 302:
            print("✅ Login successful")
        else:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
        
        # Step 2: Test order creation with P003 (Axinix)
        print("🛒 Step 2: Creating test order with P003 (Axinix)...")
        
        test_data = {
            'customer_name': 'Test Customer Transaction Fix',
            'customer_address': 'Test Address 123',
            'customer_phone': '************',
            'payment_method': 'cash',
            'product_id[]': ['P003'],  # Axinix product
            'quantity[]': ['1'],
            'foc_quantity[]': ['0']
        }
        
        order_response = session.post(f"{base_url}/orders/new", data=test_data, allow_redirects=False)
        
        print(f"📊 Order creation response status: {order_response.status_code}")
        
        if order_response.status_code == 302:
            # Check redirect location
            redirect_url = order_response.headers.get('Location', '')
            print(f"🔄 Redirected to: {redirect_url}")
            
            if '/orders/' in redirect_url and redirect_url != '/orders/new':
                print("✅ Order creation successful - redirected to order view")
                
                # Extract order ID from redirect URL
                order_id = redirect_url.split('/orders/')[-1]
                print(f"📋 Created order ID: {order_id}")
                
                # Step 3: Verify order was created
                print("🔍 Step 3: Verifying order was created...")
                order_view_response = session.get(f"{base_url}{redirect_url}")
                
                if order_view_response.status_code == 200:
                    if 'Test Customer Transaction Fix' in order_view_response.text:
                        print("✅ Order verification successful - customer name found")
                        return True
                    else:
                        print("⚠️ Order created but customer name not found in view")
                        return False
                else:
                    print(f"❌ Order view failed: {order_view_response.status_code}")
                    return False
            else:
                print("⚠️ Order creation may have failed - redirected to unexpected location")
                return False
        else:
            print(f"❌ Order creation failed with status: {order_response.status_code}")
            
            # Try to get error details
            if order_response.status_code == 200:
                # Check if there are error messages in the response
                if 'Error creating order' in order_response.text or 'transaction' in order_response.text.lower():
                    print("❌ Transaction error still present in response")
                else:
                    print("⚠️ Order creation returned 200 but didn't redirect (form validation error?)")
            
            return False
            
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")
        return False

def test_inventory_status():
    """Check inventory status before and after test"""
    
    print("\n🏪 Checking Inventory Status")
    print("=" * 30)
    
    try:
        session = requests.Session()
        
        # Login first
        login_data = {'username': 'admin', 'password': 'admin123'}
        session.post("http://localhost:5000/login", data=login_data)
        
        # Check inventory page
        inventory_response = session.get("http://localhost:5000/inventory")
        
        if inventory_response.status_code == 200:
            print("✅ Inventory page accessible")
            
            # Check if P003 (Axinix) is mentioned
            if 'P003' in inventory_response.text or 'Axinix' in inventory_response.text:
                print("✅ P003 (Axinix) product found in inventory")
                return True
            else:
                print("⚠️ P003 (Axinix) product not found in inventory")
                return False
        else:
            print(f"❌ Inventory page failed: {inventory_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error checking inventory: {str(e)}")
        return False

def main():
    """Main test function"""
    
    print(f"🕒 Test started at: {datetime.now()}")
    print()
    
    # Test 1: Check inventory
    inventory_ok = test_inventory_status()
    
    # Test 2: Test order creation
    order_ok = test_order_creation_complete()
    
    print("\n" + "=" * 50)
    print("📋 Test Results Summary")
    print("=" * 50)
    
    if inventory_ok:
        print("✅ Inventory check: PASSED")
    else:
        print("❌ Inventory check: FAILED")
    
    if order_ok:
        print("✅ Order creation: PASSED")
        print("🎉 Transaction fix is working correctly!")
    else:
        print("❌ Order creation: FAILED")
        print("⚠️ Transaction issue may still exist or other error occurred")
    
    if inventory_ok and order_ok:
        print("\n🎉 ALL TESTS PASSED! The 'cannot start a transaction within a transaction' error is FIXED!")
        return True
    else:
        print("\n⚠️ Some tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
