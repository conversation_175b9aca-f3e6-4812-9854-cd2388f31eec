#!/usr/bin/env python3
"""
Final test for order creation after emergency fix
"""

import requests
import time
import json

def test_order_creation():
    base_url = "http://localhost:5000"
    
    print("🧪 Testing Order Creation After Emergency Fix...")
    
    # Wait for server
    time.sleep(2)
    
    try:
        # Test server connectivity
        print("1. Testing server connectivity...")
        response = requests.get(base_url, timeout=10)
        print(f"   Server status: {response.status_code}")
        
        if response.status_code != 200:
            print("❌ Server not responding correctly")
            return False
        
        # Test order creation page
        print("2. Testing order creation page...")
        response = requests.get(f"{base_url}/orders/new", timeout=10)
        print(f"   Order page status: {response.status_code}")
        
        if response.status_code != 200:
            print("❌ Order creation page not accessible")
            return False
        
        print("✅ Order creation page is accessible!")
        
        # Test order submission (this will likely require authentication)
        print("3. Testing order submission...")
        order_data = {
            'customer_name': 'Emergency Test Customer',
            'customer_address': 'Emergency Test Address',
            'customer_phone': '123456789',
            'payment_method': 'cash',
            'product_id[]': ['P001'],
            'quantity[]': ['1'],
            'foc_quantity[]': ['0']
        }
        
        response = requests.post(f"{base_url}/orders/new", data=order_data, timeout=10, allow_redirects=False)
        print(f"   Order submission status: {response.status_code}")
        
        if response.status_code in [200, 302]:
            print("✅ Order submission endpoint is working!")
            if response.status_code == 302:
                redirect_url = response.headers.get('Location', '')
                print(f"   Redirected to: {redirect_url}")
        else:
            print(f"⚠️  Order submission returned: {response.status_code}")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server - server may not be running")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_direct_order_id_generation():
    """Test order ID generation directly"""
    print("\n🧪 Testing Direct Order ID Generation...")
    
    try:
        import sys
        sys.path.append('.')
        
        from app import app
        with app.app_context():
            from routes.orders import generate_order_id
            
            print("Generating 5 test order IDs:")
            for i in range(5):
                order_id = generate_order_id()
                print(f"  {i+1}. {order_id}")
            
            print("✅ Order ID generation is working correctly!")
            return True
            
    except Exception as e:
        print(f"❌ Error in direct test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("FINAL ORDER CREATION TEST AFTER EMERGENCY FIX")
    print("=" * 60)
    
    # Test direct order ID generation first
    direct_test = test_direct_order_id_generation()
    
    # Test web interface
    web_test = test_order_creation()
    
    print("\n" + "=" * 60)
    print("SUMMARY:")
    print(f"Direct Order ID Generation: {'✅ PASS' if direct_test else '❌ FAIL'}")
    print(f"Web Interface Test: {'✅ PASS' if web_test else '❌ FAIL'}")
    
    if direct_test and web_test:
        print("🎉 ALL TESTS PASSED - ORDER CREATION SHOULD BE WORKING!")
    else:
        print("⚠️  Some tests failed - check the output above")
    print("=" * 60)
