#!/usr/bin/env python3
"""
Test script to verify the order creation transaction fix
"""

import requests
import json
import sys
from datetime import datetime

def test_order_creation():
    """Test order creation to verify transaction fix"""
    
    print("🧪 Testing Order Creation Transaction Fix")
    print("=" * 50)
    
    base_url = "http://localhost:5000"
    
    # Test data
    test_data = {
        'customer_name': 'Test Customer',
        'customer_address': 'Test Address',
        'customer_phone': '************',
        'payment_method': 'cash',
        'product_id[]': ['AXINIX'],  # Using the product mentioned in the error
        'quantity[]': ['1'],
        'foc_quantity[]': ['0']
    }
    
    try:
        # Create a session to maintain login
        session = requests.Session()
        
        # Step 1: Login
        print("🔐 Step 1: Logging in...")
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        login_response = session.post(f"{base_url}/login", data=login_data, allow_redirects=False)
        
        if login_response.status_code == 302:
            print("✅ Login successful")
        else:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
        
        # Step 2: Get new order page to check if it loads
        print("📄 Step 2: Accessing new order page...")
        new_order_response = session.get(f"{base_url}/orders/new")
        
        if new_order_response.status_code == 200:
            print("✅ New order page accessible")
        else:
            print(f"❌ New order page failed: {new_order_response.status_code}")
            return False
        
        # Step 3: Submit order creation
        print("🛒 Step 3: Creating test order...")
        order_response = session.post(f"{base_url}/orders/new", data=test_data, allow_redirects=False)
        
        print(f"📊 Order creation response status: {order_response.status_code}")
        
        if order_response.status_code == 302:
            # Check redirect location
            redirect_url = order_response.headers.get('Location', '')
            print(f"🔄 Redirected to: {redirect_url}")
            
            if '/orders/' in redirect_url:
                print("✅ Order creation successful - redirected to order view")
                return True
            else:
                print("⚠️ Order creation may have failed - redirected elsewhere")
                return False
        else:
            print(f"❌ Order creation failed with status: {order_response.status_code}")
            print(f"Response content preview: {order_response.text[:500]}")
            return False
            
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")
        return False

def test_inventory_check():
    """Check if test product exists in inventory"""
    
    print("\n🏪 Checking Inventory for Test Product")
    print("=" * 40)
    
    try:
        session = requests.Session()
        
        # Login first
        login_data = {'username': 'admin', 'password': 'admin123'}
        session.post("http://localhost:5000/login", data=login_data)
        
        # Check inventory page
        inventory_response = session.get("http://localhost:5000/inventory")
        
        if inventory_response.status_code == 200:
            print("✅ Inventory page accessible")
            
            # Check if AXINIX product is mentioned
            if 'AXINIX' in inventory_response.text or 'Axinix' in inventory_response.text:
                print("✅ AXINIX product found in inventory")
                return True
            else:
                print("⚠️ AXINIX product not found in inventory")
                return False
        else:
            print(f"❌ Inventory page failed: {inventory_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error checking inventory: {str(e)}")
        return False

def main():
    """Main test function"""
    
    print(f"🕒 Test started at: {datetime.now()}")
    print()
    
    # Test 1: Check inventory
    inventory_ok = test_inventory_check()
    
    # Test 2: Test order creation
    order_ok = test_order_creation()
    
    print("\n" + "=" * 50)
    print("📋 Test Results Summary")
    print("=" * 50)
    
    if inventory_ok:
        print("✅ Inventory check: PASSED")
    else:
        print("❌ Inventory check: FAILED")
    
    if order_ok:
        print("✅ Order creation: PASSED")
    else:
        print("❌ Order creation: FAILED")
    
    if inventory_ok and order_ok:
        print("\n🎉 All tests PASSED! Transaction fix is working.")
        return True
    else:
        print("\n⚠️ Some tests FAILED. Check the output above for details.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
