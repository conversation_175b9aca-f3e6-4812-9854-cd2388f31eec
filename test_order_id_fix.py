#!/usr/bin/env python3
"""
Comprehensive test script to verify the order ID UNIQUE constraint fix
"""

import sqlite3
import os
import time
import threading
import concurrent.futures
from datetime import datetime

def test_order_id_generation():
    """Test the improved order ID generation logic"""
    print("🧪 Testing improved order ID generation...")
    
    # Import the fixed function
    import sys
    sys.path.append('routes')
    
    try:
        from routes.orders import generate_order_id
        print("✅ Successfully imported generate_order_id from routes.orders")
    except ImportError as e:
        print(f"❌ Failed to import: {e}")
        return False
    
    # Generate multiple IDs quickly to test for uniqueness
    ids = []
    start_time = time.time()
    
    for i in range(100):
        order_id = generate_order_id()
        ids.append(order_id)
        if i < 5:
            print(f"  Sample ID {i+1}: {order_id}")
    
    end_time = time.time()
    
    # Check for duplicates
    unique_ids = set(ids)
    if len(unique_ids) == len(ids):
        print(f"✅ Generated {len(ids)} unique IDs in {end_time - start_time:.3f} seconds")
        return True
    else:
        print(f"❌ Generated {len(ids)} IDs but only {len(unique_ids)} were unique!")
        duplicates = [id for id in ids if ids.count(id) > 1]
        print(f"  Duplicate IDs: {set(duplicates)}")
        return False

def test_concurrent_order_creation():
    """Test concurrent order creation to simulate race conditions"""
    print("\n⚡ Testing concurrent order creation...")
    
    db_path = 'instance/medivent.db'
    if not os.path.exists(db_path):
        print(f"❌ Database not found: {db_path}")
        return False
    
    def create_test_order(thread_id):
        """Create a test order in a separate thread"""
        try:
            # Import here to avoid import issues in threads
            import sys
            sys.path.append('routes')
            from routes.orders import generate_order_id
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Generate order ID
            order_id = generate_order_id()
            
            # Try to insert with IMMEDIATE transaction
            cursor.execute('BEGIN IMMEDIATE TRANSACTION')
            cursor.execute('''
                INSERT INTO orders (
                    order_id, customer_name, customer_address, customer_phone,
                    payment_method, status, sales_agent, updated_by, order_date, last_updated
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                order_id, f"Test Customer {thread_id}", "Test Address", "123456789",
                "cash", "Placed", "test_user", "test_user", datetime.now(), datetime.now()
            ))
            
            conn.commit()
            conn.close()
            return {'success': True, 'order_id': order_id, 'thread_id': thread_id}
            
        except sqlite3.IntegrityError as e:
            if 'UNIQUE constraint failed: orders.order_id' in str(e):
                return {'success': False, 'error': 'UNIQUE_CONSTRAINT', 'thread_id': thread_id}
            else:
                return {'success': False, 'error': str(e), 'thread_id': thread_id}
        except Exception as e:
            return {'success': False, 'error': str(e), 'thread_id': thread_id}
    
    # Run concurrent order creation
    num_threads = 10
    results = []
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
        futures = [executor.submit(create_test_order, i) for i in range(num_threads)]
        results = [future.result() for future in concurrent.futures.as_completed(futures)]
    
    # Analyze results
    successful = [r for r in results if r['success']]
    failed = [r for r in results if not r['success']]
    unique_constraint_failures = [r for r in failed if r.get('error') == 'UNIQUE_CONSTRAINT']
    
    print(f"  ✅ Successful orders: {len(successful)}")
    print(f"  ❌ Failed orders: {len(failed)}")
    print(f"  🔒 UNIQUE constraint failures: {len(unique_constraint_failures)}")
    
    if len(unique_constraint_failures) > 0:
        print("  ⚠️  UNIQUE constraint failures detected - this indicates the race condition still exists")
        for failure in unique_constraint_failures:
            print(f"    Thread {failure['thread_id']}: {failure['error']}")
    
    # Clean up test orders
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("DELETE FROM orders WHERE customer_name LIKE 'Test Customer %'")
        deleted = cursor.rowcount
        conn.commit()
        conn.close()
        print(f"  🧹 Cleaned up {deleted} test orders")
    except Exception as e:
        print(f"  ⚠️  Error cleaning up test orders: {e}")
    
    return len(unique_constraint_failures) == 0

def test_database_constraints():
    """Test database constraints and schema"""
    print("\n🔍 Testing database constraints...")
    
    db_path = 'instance/medivent.db'
    if not os.path.exists(db_path):
        print(f"❌ Database not found: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check orders table schema
        cursor.execute("PRAGMA table_info(orders)")
        columns = cursor.fetchall()
        order_id_column = None
        
        for col in columns:
            if col[1] == 'order_id':
                order_id_column = col
                break
        
        if order_id_column:
            print(f"  ✅ order_id column found: {order_id_column[1]} {order_id_column[2]} (NOT NULL: {order_id_column[3]})")
        else:
            print("  ❌ order_id column not found")
            return False
        
        # Check for UNIQUE constraint
        cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='orders'")
        schema = cursor.fetchone()
        if schema and 'order_id TEXT UNIQUE' in schema[0]:
            print("  ✅ UNIQUE constraint on order_id confirmed")
        else:
            print("  ❌ UNIQUE constraint on order_id not found")
            return False
        
        # Test constraint enforcement
        test_order_id = "TEST_DUPLICATE_123"
        
        # Insert first order
        cursor.execute('''
            INSERT INTO orders (order_id, customer_name, status)
            VALUES (?, ?, ?)
        ''', (test_order_id, "Test Customer", "Placed"))
        
        # Try to insert duplicate
        try:
            cursor.execute('''
                INSERT INTO orders (order_id, customer_name, status)
                VALUES (?, ?, ?)
            ''', (test_order_id, "Test Customer 2", "Placed"))
            print("  ❌ UNIQUE constraint not enforced - duplicate insert succeeded")
            return False
        except sqlite3.IntegrityError as e:
            if 'UNIQUE constraint failed: orders.order_id' in str(e):
                print("  ✅ UNIQUE constraint properly enforced")
            else:
                print(f"  ⚠️  Unexpected integrity error: {e}")
        
        # Clean up
        cursor.execute("DELETE FROM orders WHERE order_id = ?", (test_order_id,))
        conn.commit()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing database constraints: {e}")
        return False

def main():
    """Main test function"""
    print("🔍 TESTING ORDER ID UNIQUE CONSTRAINT FIX")
    print("=" * 50)
    
    test_results = []
    
    # Test 1: Order ID generation
    test_results.append(test_order_id_generation())
    
    # Test 2: Database constraints
    test_results.append(test_database_constraints())
    
    # Test 3: Concurrent order creation
    test_results.append(test_concurrent_order_creation())
    
    print("\n" + "=" * 50)
    print("🎯 TEST RESULTS SUMMARY")
    
    passed = sum(test_results)
    total = len(test_results)
    
    if passed == total:
        print(f"✅ ALL TESTS PASSED ({passed}/{total})")
        print("🎉 Order ID UNIQUE constraint issue has been FIXED!")
    else:
        print(f"❌ SOME TESTS FAILED ({passed}/{total})")
        print("⚠️  Additional fixes may be needed")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
