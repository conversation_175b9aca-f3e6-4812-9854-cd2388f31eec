#!/usr/bin/env python3
"""
Simple Order ID Strategy Testing
"""

import sqlite3
import os
import time
import uuid
import secrets
import threading
import json
from datetime import datetime
import hashlib

def test_strategy_1_auto_increment():
    """Strategy 1: Database auto-increment"""
    print("🧪 Testing Strategy 1: Auto-Increment...")
    
    db_path = 'instance/medivent.db'
    if not os.path.exists(db_path):
        print("❌ Database not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Create sequence table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS order_sequence (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Test generating 10 IDs
        ids = []
        for i in range(10):
            cursor.execute('INSERT INTO order_sequence DEFAULT VALUES')
            sequence_id = cursor.lastrowid
            order_id = f"ORD{sequence_id:08d}"
            ids.append(order_id)
        
        conn.commit()
        conn.close()
        
        # Check uniqueness
        unique_ids = set(ids)
        print(f"  Generated: {len(ids)}, Unique: {len(unique_ids)}")
        print(f"  Sample IDs: {ids[:3]}")
        
        return len(ids) == len(unique_ids)
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def test_strategy_2_uuid():
    """Strategy 2: UUID-based"""
    print("🧪 Testing Strategy 2: UUID-Based...")
    
    try:
        ids = []
        for i in range(100):
            uuid_str = str(uuid.uuid4()).replace('-', '').upper()[:12]
            order_id = f"ORD{uuid_str}"
            ids.append(order_id)
        
        unique_ids = set(ids)
        print(f"  Generated: {len(ids)}, Unique: {len(unique_ids)}")
        print(f"  Sample IDs: {ids[:3]}")
        
        return len(ids) == len(unique_ids)
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def test_strategy_3_timestamp_hash():
    """Strategy 3: Timestamp + Hash"""
    print("🧪 Testing Strategy 3: Timestamp + Hash...")
    
    try:
        ids = []
        for i in range(100):
            # High precision timestamp
            timestamp = int(time.time() * 1000000)
            
            # Add process info and random
            pid = os.getpid()
            tid = threading.get_ident()
            random_part = secrets.token_hex(3)
            
            # Create hash
            combined = f"{timestamp}{pid}{tid}{random_part}{i}"
            hash_part = hashlib.md5(combined.encode()).hexdigest()[:8].upper()
            
            order_id = f"ORD{hash_part}"
            ids.append(order_id)
            
            # Small delay to ensure timestamp difference
            time.sleep(0.001)
        
        unique_ids = set(ids)
        print(f"  Generated: {len(ids)}, Unique: {len(unique_ids)}")
        print(f"  Sample IDs: {ids[:3]}")
        
        return len(ids) == len(unique_ids)
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def test_database_insertion():
    """Test actual database insertion"""
    print("🧪 Testing Database Insertion...")
    
    db_path = 'instance/medivent.db'
    if not os.path.exists(db_path):
        print("❌ Database not found")
        return False
    
    try:
        # Test Strategy 1 with actual insertion
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Generate order ID using Strategy 1
        cursor.execute('INSERT INTO order_sequence DEFAULT VALUES')
        sequence_id = cursor.lastrowid
        order_id = f"ORD{sequence_id:08d}"
        
        print(f"  Generated Order ID: {order_id}")
        
        # Try to insert into orders table
        cursor.execute('''
            INSERT INTO orders (
                order_id, customer_name, customer_address, customer_phone,
                payment_method, status, sales_agent, updated_by, order_date, last_updated
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            order_id, "Test Customer", "Test Address", "123456789",
            "cash", "Placed", "test_user", "test_user", datetime.now(), datetime.now()
        ))
        
        conn.commit()
        print(f"  ✅ Successfully inserted order: {order_id}")
        
        # Clean up
        cursor.execute("DELETE FROM orders WHERE order_id = ?", (order_id,))
        conn.commit()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"  ❌ Database insertion error: {e}")
        return False

def test_concurrent_insertion():
    """Test concurrent order insertion"""
    print("🧪 Testing Concurrent Insertion...")
    
    db_path = 'instance/medivent.db'
    if not os.path.exists(db_path):
        print("❌ Database not found")
        return False
    
    def create_order(thread_id):
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Strategy: UUID + timestamp
            timestamp = int(time.time() * 1000000)
            uuid_part = str(uuid.uuid4()).replace('-', '')[:8].upper()
            order_id = f"ORD{timestamp}{uuid_part}"
            
            cursor.execute('''
                INSERT INTO orders (
                    order_id, customer_name, customer_address, customer_phone,
                    payment_method, status, sales_agent, updated_by, order_date, last_updated
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                order_id, f"Test Customer {thread_id}", "Test Address", "123456789",
                "cash", "Placed", "test_user", "test_user", datetime.now(), datetime.now()
            ))
            
            conn.commit()
            conn.close()
            
            return {'success': True, 'order_id': order_id, 'thread': thread_id}
            
        except sqlite3.IntegrityError as e:
            if 'UNIQUE constraint failed' in str(e):
                return {'success': False, 'error': 'UNIQUE_CONSTRAINT', 'thread': thread_id}
            else:
                return {'success': False, 'error': str(e), 'thread': thread_id}
        except Exception as e:
            return {'success': False, 'error': str(e), 'thread': thread_id}
    
    # Run concurrent tests
    import concurrent.futures
    
    results = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
        futures = [executor.submit(create_order, i) for i in range(10)]
        results = [future.result() for future in concurrent.futures.as_completed(futures)]
    
    # Analyze results
    successful = [r for r in results if r['success']]
    failed = [r for r in results if not r['success']]
    unique_failures = [r for r in failed if r.get('error') == 'UNIQUE_CONSTRAINT']
    
    print(f"  ✅ Successful: {len(successful)}")
    print(f"  ❌ Failed: {len(failed)}")
    print(f"  🔒 UNIQUE constraint failures: {len(unique_failures)}")
    
    # Clean up test orders
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("DELETE FROM orders WHERE customer_name LIKE 'Test Customer %'")
        deleted = cursor.rowcount
        conn.commit()
        conn.close()
        print(f"  🧹 Cleaned up {deleted} test orders")
    except Exception as e:
        print(f"  ⚠️ Cleanup error: {e}")
    
    return len(unique_failures) == 0

def main():
    """Main testing function"""
    print("🔍 ORDER ID STRATEGY TESTING")
    print("=" * 40)
    
    tests = [
        ("Strategy 1: Auto-Increment", test_strategy_1_auto_increment),
        ("Strategy 2: UUID-Based", test_strategy_2_uuid),
        ("Strategy 3: Timestamp+Hash", test_strategy_3_timestamp_hash),
        ("Database Insertion", test_database_insertion),
        ("Concurrent Insertion", test_concurrent_insertion),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}")
        print("-" * len(test_name))
        success = test_func()
        results.append((test_name, success))
        print(f"Result: {'✅ PASS' if success else '❌ FAIL'}")
    
    print("\n" + "=" * 40)
    print("📊 SUMMARY")
    print("=" * 40)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name:<25} | {status}")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    print(f"\nOverall: {passed}/{total} tests passed")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
