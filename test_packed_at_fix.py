#!/usr/bin/env python3
"""
Test script to verify that the packed_at column fix is working correctly
"""

import sqlite3
import os
from datetime import datetime

def test_packed_at_fix():
    """Test that all packed_at related functionality works"""
    try:
        print("=== TESTING PACKED_AT COLUMN FIX ===")
        print(f"Test Time: {datetime.now()}")
        print()
        
        # Connect to database
        db_path = os.path.join('instance', 'medivent.db')
        if not os.path.exists(db_path):
            print("❌ Database not found")
            return False
            
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Test 1: Verify columns exist
        print("1️⃣ VERIFYING COLUMN EXISTENCE:")
        cursor.execute("PRAGMA table_info(orders)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        required_columns = ['packed_at', 'packed_by', 'packing_notes']
        for col in required_columns:
            if col in column_names:
                print(f"   ✅ {col} column exists")
            else:
                print(f"   ❌ {col} column missing")
                return False
        
        # Test 2: Test SELECT queries that were failing
        print("\n2️⃣ TESTING SELECT QUERIES:")
        
        # Test the warehouse orders query
        try:
            cursor.execute('''
                SELECT o.*, c.name as customer_name
                FROM orders o
                LEFT JOIN customers c ON o.customer_id = c.customer_id
                WHERE o.status = 'Ready for Pickup' AND o.warehouse_status = 'packed'
                ORDER BY o.packed_at DESC
            ''')
            results = cursor.fetchall()
            print(f"   ✅ Warehouse orders query successful - {len(results)} results")
        except Exception as e:
            print(f"   ❌ Warehouse orders query failed: {e}")
            return False
        
        # Test the packing dashboard query
        try:
            cursor.execute('''
                SELECT 
                    o.order_id, o.customer_name, o.order_amount, o.order_date,
                    o.warehouse_status, o.packed_at, o.packed_by, o.packing_notes,
                    COUNT(oi.product_id) as total_items,
                    SUM(oi.quantity) as total_quantity
                FROM orders o
                LEFT JOIN order_items oi ON o.order_id = oi.order_id
                WHERE o.status = 'Ready for Pickup'
                AND o.warehouse_status = 'packed'
                GROUP BY o.order_id
                ORDER BY o.packed_at DESC
            ''')
            results = cursor.fetchall()
            print(f"   ✅ Packing dashboard query successful - {len(results)} results")
        except Exception as e:
            print(f"   ❌ Packing dashboard query failed: {e}")
            return False
        
        # Test 3: Test UPDATE queries
        print("\n3️⃣ TESTING UPDATE QUERIES:")
        
        # Get a test order
        cursor.execute("SELECT order_id FROM orders WHERE status = 'Ready for Pickup' LIMIT 1")
        test_order = cursor.fetchone()
        
        if test_order:
            order_id = test_order[0]
            print(f"   Using test order: {order_id}")
            
            # Test the pack order update
            try:
                cursor.execute('''
                    UPDATE orders
                    SET warehouse_status = 'packed',
                        packed_at = datetime('now'),
                        packed_by = 'test_user',
                        packing_notes = 'Test packing operation',
                        last_updated = datetime('now')
                    WHERE order_id = ?
                ''', (order_id,))
                
                # Check if update worked
                cursor.execute('''
                    SELECT warehouse_status, packed_at, packed_by, packing_notes
                    FROM orders WHERE order_id = ?
                ''', (order_id,))
                result = cursor.fetchone()
                
                if result and result[0] == 'packed':
                    print(f"   ✅ Pack order update successful")
                    print(f"      Status: {result[0]}")
                    print(f"      Packed At: {result[1]}")
                    print(f"      Packed By: {result[2]}")
                    print(f"      Notes: {result[3]}")
                else:
                    print(f"   ❌ Pack order update failed")
                    return False
                    
                # Rollback the test change
                cursor.execute('''
                    UPDATE orders
                    SET warehouse_status = NULL,
                        packed_at = NULL,
                        packed_by = NULL,
                        packing_notes = NULL
                    WHERE order_id = ?
                ''', (order_id,))
                print(f"   ✅ Test changes rolled back")
                
            except Exception as e:
                print(f"   ❌ Pack order update failed: {e}")
                return False
        else:
            print("   ⚠️ No test orders available - skipping UPDATE test")
        
        # Test 4: Test application import
        print("\n4️⃣ TESTING APPLICATION IMPORT:")
        try:
            import sys
            sys.path.append('.')
            import app
            print("   ✅ Application imports successfully")
        except Exception as e:
            print(f"   ❌ Application import failed: {e}")
            return False
        
        conn.commit()
        conn.close()
        
        print("\n" + "="*50)
        print("🎉 ALL TESTS PASSED!")
        print("="*50)
        print("✅ packed_at column fix is working correctly")
        print("✅ All SQL queries execute without errors")
        print("✅ Application should now work properly")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_packed_at_fix()
    if success:
        print("\n🚀 Ready to test the application!")
    else:
        print("\n❌ Issues found - please check the errors above")
