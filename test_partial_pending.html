<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Partial Pending - DC Management</title>
    
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    
    <style>
        /* Main container styling */
        .partial-pending-container {
            padding: 20px;
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        
        /* Header styling */
        .page-header {
            background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .page-header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 600;
        }
        
        .page-header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        /* Statistics cards */
        .stats-row {
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: none;
            height: 100%;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            line-height: 1;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 5px;
        }
        
        .stat-description {
            color: #6c757d;
            font-size: 0.8rem;
        }
        
        /* Main content area */
        .main-content {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f8f9fa;
        }
        
        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
            margin: 0;
        }
        
        /* Table styling */
        .orders-table {
            width: 100%;
            margin-top: 20px;
        }
        
        .orders-table th {
            background-color: #f8f9fa;
            border: none;
            padding: 15px 10px;
            font-weight: 600;
            color: #333;
            text-transform: uppercase;
            font-size: 0.85rem;
            letter-spacing: 0.5px;
        }
        
        .orders-table td {
            padding: 15px 10px;
            border: none;
            border-bottom: 1px solid #f8f9fa;
            vertical-align: middle;
        }
        
        .orders-table tbody tr:hover {
            background-color: #f8f9fa;
        }
        
        /* Action buttons */
        .action-btn {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 2px;
            text-decoration: none;
            display: inline-block;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            text-decoration: none;
        }
        
        .btn-view {
            background: #007bff;
            color: white;
        }
        
        .btn-generate {
            background: #28a745;
            color: white;
        }
        
        .btn-track {
            background: #17a2b8;
            color: white;
        }
        
        /* Priority badges */
        .priority-badge {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .priority-1, .priority-2 {
            background: #d4edda;
            color: #155724;
        }
        
        .priority-3 {
            background: #fff3cd;
            color: #856404;
        }
        
        .priority-4, .priority-5 {
            background: #f8d7da;
            color: #721c24;
        }
        
        /* Sidebar styling */
        .sidebar-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .sidebar-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .sidebar-title i {
            margin-right: 10px;
            color: #007bff;
        }
        
        /* Product cards */
        .product-item {
            padding: 15px;
            border: 1px solid #f8f9fa;
            border-radius: 8px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        
        .product-item:hover {
            background-color: #f8f9fa;
            border-color: #007bff;
        }
        
        .product-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .product-details {
            font-size: 0.85rem;
            color: #6c757d;
        }
        
        /* Stock indicators */
        .stock-badge {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
        }
        
        .stock-high {
            background: #d4edda;
            color: #155724;
        }
        
        .stock-medium {
            background: #fff3cd;
            color: #856404;
        }
        
        .stock-low {
            background: #f8d7da;
            color: #721c24;
        }
        
        /* Notification styling */
        .notification-item {
            padding: 15px;
            border-left: 4px solid #007bff;
            background: #f8f9fa;
            border-radius: 0 8px 8px 0;
            margin-bottom: 10px;
        }
        
        .notification-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .notification-message {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 5px;
        }
        
        .notification-time {
            color: #6c757d;
            font-size: 0.75rem;
        }
        
        /* Utility classes */
        .text-primary { color: #007bff !important; }
        .text-success { color: #28a745 !important; }
        .text-warning { color: #ffc107 !important; }
        .text-danger { color: #dc3545 !important; }
        .text-info { color: #17a2b8 !important; }
    </style>
</head>
<body>
    <div class="partial-pending-container">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1>
                        <i class="fas fa-clock"></i> Partial Pending DC Management
                    </h1>
                    <p>Comprehensive tracking and management of partial delivery challans with real-time insights</p>
                </div>
                <div class="col-md-4 text-right">
                    <button class="btn btn-light btn-lg mr-2">
                        <i class="fas fa-sync-alt"></i> Refresh Data
                    </button>
                    <button class="btn btn-warning btn-lg">
                        <i class="fas fa-route"></i> Workflow
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Statistics Cards -->
        <div class="row stats-row">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card">
                    <div class="stat-number text-primary">3</div>
                    <div class="stat-label">Partial Orders</div>
                    <div class="stat-description">
                        <i class="fas fa-file-alt"></i> Active orders with pending items
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card">
                    <div class="stat-number text-warning">6</div>
                    <div class="stat-label">Pending Items</div>
                    <div class="stat-description">
                        <i class="fas fa-box"></i> Items awaiting fulfillment
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card">
                    <div class="stat-number text-info">155</div>
                    <div class="stat-label">Total Pending Qty</div>
                    <div class="stat-description">
                        <i class="fas fa-cubes"></i> Units to be delivered
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card">
                    <div class="stat-number text-success">₹3,875.00</div>
                    <div class="stat-label">Pending Value</div>
                    <div class="stat-description">
                        <i class="fas fa-rupee-sign"></i> Total pending amount
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Main Content Area -->
        <div class="row">
            <div class="col-lg-8">
                <div class="main-content">
                    <div class="section-header">
                        <h2 class="section-title">
                            <i class="fas fa-list"></i> Orders with Partial DC Status
                        </h2>
                        <div>
                            <button class="btn btn-outline-primary btn-sm mr-2">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                            <button class="btn btn-primary btn-sm">
                                <i class="fas fa-plus"></i> Generate Bulk DC
                            </button>
                        </div>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table orders-table">
                            <thead>
                                <tr>
                                    <th><input type="checkbox"></th>
                                    <th>Order Details</th>
                                    <th>Customer</th>
                                    <th>Pending Status</th>
                                    <th>Priority</th>
                                    <th>Value</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><input type="checkbox"></td>
                                    <td>
                                        <div>
                                            <strong>ORD000001</strong>
                                            <br><small class="text-muted">2024-01-15</small>
                                            <br><span class="badge badge-info">DC: DC-001</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>Test Customer 1</strong>
                                            <br><small class="text-muted">9876543210</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <span class="badge badge-warning">2</span> items
                                            <br><small>60 units pending</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="priority-badge priority-4">Priority 4</span>
                                    </td>
                                    <td><strong>₹1,500.00</strong></td>
                                    <td>
                                        <a href="#" class="action-btn btn-view">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                        <button class="action-btn btn-generate">
                                            <i class="fas fa-file-plus"></i> Generate DC
                                        </button>
                                        <button class="action-btn btn-track">
                                            <i class="fas fa-route"></i> Track
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Top Pending Products -->
                <div class="sidebar-section">
                    <h3 class="sidebar-title">
                        <i class="fas fa-chart-bar"></i> Top Pending Products
                    </h3>
                    
                    <div class="product-item">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="product-name">Paracetamol 500mg</div>
                                <div class="product-details">500mg</div>
                                <div class="product-details">2 orders affected</div>
                            </div>
                            <div class="text-right">
                                <div class="badge badge-warning mb-1">90 pending</div>
                                <br>
                                <span class="stock-badge stock-high">
                                    <i class="fas fa-check"></i> 120 available
                                </span>
                            </div>
                        </div>
                        <div class="mt-2">
                            <button class="btn btn-sm btn-outline-primary mr-1">
                                <i class="fas fa-search"></i> Check Stock
                            </button>
                            <button class="btn btn-sm btn-outline-success">
                                <i class="fas fa-file-plus"></i> Generate DC
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Notifications -->
                <div class="sidebar-section">
                    <h3 class="sidebar-title">
                        <i class="fas fa-bell"></i> Recent Notifications
                    </h3>
                    
                    <div class="notification-item">
                        <div class="notification-title">Low Stock Alert</div>
                        <div class="notification-message">
                            <strong>Amoxicillin 250mg</strong> is running low (15 units remaining)
                        </div>
                        <div class="notification-time">
                            <i class="fas fa-clock"></i> 2024-01-04 10:30
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
