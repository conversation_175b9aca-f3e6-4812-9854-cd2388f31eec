#!/usr/bin/env python3
"""
Test Product Management Functionality
Test filtering and activation/deactivation
"""

import sqlite3
import requests
import json

def test_database_status():
    """Test database status functionality"""
    print("🗄️ Testing Database Status Functionality:")
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Test current status
        cursor.execute("SELECT COUNT(*) as total FROM products")
        total = cursor.fetchone()['total']
        
        cursor.execute("SELECT COUNT(*) as active FROM products WHERE status = 'active' AND is_active = 1")
        active = cursor.fetchone()['active']
        
        cursor.execute("SELECT COUNT(*) as inactive FROM products WHERE status != 'active' OR is_active = 0")
        inactive = cursor.fetchone()['inactive']
        
        print(f"   📊 Total products: {total}")
        print(f"   ✅ Active products: {active}")
        print(f"   ❌ Inactive products: {inactive}")
        
        # Test filter queries
        print("\n🔍 Testing Filter Queries:")
        
        # Active filter
        cursor.execute("SELECT COUNT(*) as count FROM products WHERE (LOWER(status) = 'active' AND is_active = 1)")
        active_filtered = cursor.fetchone()['count']
        print(f"   Active filter result: {active_filtered}")
        
        # Inactive filter
        cursor.execute("SELECT COUNT(*) as count FROM products WHERE (LOWER(status) != 'active' OR is_active = 0)")
        inactive_filtered = cursor.fetchone()['count']
        print(f"   Inactive filter result: {inactive_filtered}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Database test failed: {e}")
        return False

def test_route_accessibility():
    """Test if the product management route is accessible"""
    print("\n🌐 Testing Route Accessibility:")
    
    base_url = "http://127.0.0.1:5001"
    
    routes_to_test = [
        ("/products/product_management/", "Product Management"),
        ("/products/product_management/?status=active", "Active Filter"),
        ("/products/product_management/?status=inactive", "Inactive Filter")
    ]
    
    for route, name in routes_to_test:
        try:
            response = requests.get(f"{base_url}{route}", timeout=10)
            status_ok = response.status_code == 200
            status_symbol = "✅" if status_ok else "❌"
            print(f"   {status_symbol} {name} - Status: {response.status_code}")
            
            if status_ok:
                # Check for template errors
                content = response.text.lower()
                has_errors = any(error in content for error in [
                    'templatesyntaxerror', 'undefinederror', 'error 500'
                ])
                if has_errors:
                    print(f"      ⚠️ Template errors detected")
                else:
                    print(f"      ✅ Template renders correctly")
            
        except requests.exceptions.RequestException as e:
            print(f"   ❌ {name} - Connection Error")

def test_activation_deactivation():
    """Test activation/deactivation functionality"""
    print("\n🔧 Testing Activation/Deactivation:")
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Get a sample product
        cursor.execute("SELECT product_id, name, status, is_active FROM products LIMIT 1")
        sample_product = cursor.fetchone()
        
        if sample_product:
            product_id = sample_product['product_id']
            original_status = sample_product['status']
            original_is_active = sample_product['is_active']
            
            print(f"   📝 Testing with product: {sample_product['name']}")
            print(f"   📊 Original status: {original_status}, is_active: {original_is_active}")
            
            # Test deactivation
            print("   🔄 Testing deactivation...")
            cursor.execute('''
                UPDATE products
                SET status = 'inactive', is_active = 0
                WHERE product_id = ?
            ''', (product_id,))
            conn.commit()
            
            # Verify deactivation
            cursor.execute("SELECT status, is_active FROM products WHERE product_id = ?", (product_id,))
            updated = cursor.fetchone()
            print(f"   📊 After deactivation: status={updated['status']}, is_active={updated['is_active']}")
            
            # Test activation
            print("   🔄 Testing activation...")
            cursor.execute('''
                UPDATE products
                SET status = 'active', is_active = 1
                WHERE product_id = ?
            ''', (product_id,))
            conn.commit()
            
            # Verify activation
            cursor.execute("SELECT status, is_active FROM products WHERE product_id = ?", (product_id,))
            restored = cursor.fetchone()
            print(f"   📊 After activation: status={restored['status']}, is_active={restored['is_active']}")
            
            print("   ✅ Activation/Deactivation functionality works correctly")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Activation test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 TESTING PRODUCT MANAGEMENT FUNCTIONALITY")
    print("=" * 60)
    
    tests = [
        test_database_status,
        test_route_accessibility,
        test_activation_deactivation
    ]
    
    results = []
    
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY:")
    
    passed = sum(results)
    total = len(results)
    
    test_names = ["Database Status", "Route Accessibility", "Activation/Deactivation"]
    
    for i, (test_name, result) in enumerate(zip(test_names, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} {test_name}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Product management functionality is working.")
    else:
        print("⚠️ Some tests failed. Please check the issues above.")

if __name__ == "__main__":
    main()
