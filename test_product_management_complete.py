#!/usr/bin/env python3
"""
Complete Test for Product Management Functionality
Tests KPI cards, filtering, and activation/deactivation
"""

import sqlite3
import requests
import json

def test_database_stats():
    """Test database statistics calculation"""
    print("📊 Testing Database Statistics:")
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Get total products
        cursor.execute("SELECT COUNT(*) as total FROM products")
        total = cursor.fetchone()['total']
        
        # Get active products
        cursor.execute("SELECT COUNT(*) as active FROM products WHERE (LOWER(status) = 'active' AND is_active = 1)")
        active = cursor.fetchone()['active']
        
        # Get inactive products
        cursor.execute("SELECT COUNT(*) as inactive FROM products WHERE (LOWER(status) != 'active' OR is_active = 0)")
        inactive = cursor.fetchone()['inactive']
        
        # Get categories
        cursor.execute("SELECT COUNT(DISTINCT category) as categories FROM products WHERE category IS NOT NULL AND category != 'Uncategorized'")
        categories = cursor.fetchone()['categories']
        
        print(f"   ✅ Total Products: {total}")
        print(f"   ✅ Active Products: {active}")
        print(f"   ✅ Inactive Products: {inactive}")
        print(f"   ✅ Categories: {categories}")
        
        # Verify math
        if active + inactive == total:
            print("   ✅ Active + Inactive = Total (Math checks out)")
        else:
            print("   ⚠️ Active + Inactive ≠ Total (Math issue)")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Database stats test failed: {e}")
        return False

def test_filtering_queries():
    """Test filtering query logic"""
    print("\n🔍 Testing Filtering Queries:")
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Test active filter
        cursor.execute("SELECT COUNT(*) as count FROM products WHERE (LOWER(status) = 'active' AND is_active = 1)")
        active_count = cursor.fetchone()['count']
        print(f"   ✅ Active filter query: {active_count} products")
        
        # Test inactive filter
        cursor.execute("SELECT COUNT(*) as count FROM products WHERE (LOWER(status) != 'active' OR is_active = 0)")
        inactive_count = cursor.fetchone()['count']
        print(f"   ✅ Inactive filter query: {inactive_count} products")
        
        # Test search functionality
        cursor.execute("SELECT COUNT(*) as count FROM products WHERE (name LIKE ? OR generic_name LIKE ? OR manufacturer LIKE ? OR category LIKE ?)", 
                      ('%paracetamol%', '%paracetamol%', '%paracetamol%', '%paracetamol%'))
        search_count = cursor.fetchone()['count']
        print(f"   ✅ Search query (paracetamol): {search_count} products")
        
        # Test category filter
        cursor.execute("SELECT COUNT(*) as count FROM products WHERE category = ?", ('Tablets',))
        category_count = cursor.fetchone()['count']
        print(f"   ✅ Category filter (Tablets): {category_count} products")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Filtering queries test failed: {e}")
        return False

def test_route_responses():
    """Test route responses and template rendering"""
    print("\n🌐 Testing Route Responses:")
    
    base_url = "http://127.0.0.1:5001"
    
    test_routes = [
        ("/products/product_management/", "Main Product Management"),
        ("/products/product_management/?status=active", "Active Filter"),
        ("/products/product_management/?status=inactive", "Inactive Filter"),
        ("/products/product_management/?q=paracetamol", "Search Filter"),
        ("/products/product_management/?category=Tablets", "Category Filter")
    ]
    
    success_count = 0
    
    for route, name in test_routes:
        try:
            response = requests.get(f"{base_url}{route}", timeout=10)
            
            if response.status_code == 200:
                print(f"   ✅ {name} - Status: 200")
                
                # Check for key content
                content = response.text
                checks = [
                    ("Products Management" in content, "Page title"),
                    ("Total Products" in content, "KPI cards"),
                    ("Active Products" in content, "Active KPI"),
                    ("Inactive Products" in content, "Inactive KPI"),
                    ("Categories" in content, "Categories KPI"),
                    ("toggleProductStatus" in content, "Toggle function"),
                    ("badge" in content.lower(), "Status badges")
                ]
                
                for check, desc in checks:
                    if check:
                        print(f"      ✅ {desc} found")
                    else:
                        print(f"      ⚠️ {desc} missing")
                
                success_count += 1
                
            elif response.status_code == 302:
                print(f"   ⚠️ {name} - Redirects (auth required)")
                success_count += 1
            else:
                print(f"   ❌ {name} - Status: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"   ❌ {name} - Connection Error: {e}")
    
    return success_count == len(test_routes)

def test_activation_routes():
    """Test activation/deactivation route structure"""
    print("\n🔧 Testing Activation Routes:")
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        # Get a sample product
        cursor.execute("SELECT product_id, name FROM products LIMIT 1")
        sample = cursor.fetchone()
        
        if sample:
            product_id = sample[0]
            print(f"   📝 Testing with product: {product_id}")
            
            # Test route accessibility (without actually calling)
            base_url = "http://127.0.0.1:5001"
            
            # These would normally require authentication, so we just test structure
            activate_url = f"{base_url}/products/activate/{product_id}"
            deactivate_url = f"{base_url}/products/deactivate/{product_id}"
            
            print(f"   ✅ Activate URL: {activate_url}")
            print(f"   ✅ Deactivate URL: {deactivate_url}")
            print("   ✅ Route structure is correct")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Activation routes test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 COMPLETE PRODUCT MANAGEMENT TESTING")
    print("=" * 60)
    
    tests = [
        ("Database Statistics", test_database_stats),
        ("Filtering Queries", test_filtering_queries),
        ("Route Responses", test_route_responses),
        ("Activation Routes", test_activation_routes)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 COMPLETE TEST SUMMARY:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ KPI cards should show correct active/inactive counts")
        print("✅ Filtering should work for status, search, and category")
        print("✅ Status badges should be clickable")
        print("✅ Activation/deactivation should work via AJAX")
        print("\n🌐 Visit: http://127.0.0.1:5001/products/product_management/")
    else:
        print(f"\n⚠️ {total - passed} tests failed. Check the issues above.")

if __name__ == "__main__":
    main()
