#!/usr/bin/env python3
"""
Test the P003 product route fix
"""

import sqlite3
import os

def test_product_p003_fix():
    """Test the fixed query for product P003"""
    
    print("🧪 TESTING PRODUCT P003 ROUTE FIX")
    print("=" * 50)
    
    if not os.path.exists('instance/medivent.db'):
        print("❌ Database file not found!")
        return False
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        print("\n1️⃣ TESTING FIXED QUERY:")
        # Test the fixed query
        cursor.execute('''
            SELECT p.*, d.name as division_name, d.category as division_category,
                   d.manager_id as division_manager_id
            FROM products p
            LEFT JOIN divisions d ON p.division_id = d.division_id
            WHERE p.product_id = ?
        ''', ('P003',))
        
        product = cursor.fetchone()
        
        if product:
            print(f"   ✅ Query successful for P003")
            print(f"   📦 Product: {product['name']}")
            print(f"   🏢 Division: {product['division_name']}")
            print(f"   👤 Manager ID: {product['division_manager_id']}")
        else:
            print("   ⚠️  Product P003 not found")
            
            # Check what products exist
            cursor.execute('SELECT product_id, name FROM products LIMIT 5')
            products = cursor.fetchall()
            print("   📦 Available products:")
            for p in products:
                print(f"      {p['product_id']}: {p['name']}")
        
        print("\n2️⃣ TESTING DIVISIONS TABLE SCHEMA:")
        cursor.execute('PRAGMA table_info(divisions)')
        columns = cursor.fetchall()
        manager_columns = [col[1] for col in columns if 'manager' in col[1].lower()]
        print(f"   📋 Manager-related columns: {manager_columns}")
        
        conn.close()
        print("\n✅ Database test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    test_product_p003_fix()
