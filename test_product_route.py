#!/usr/bin/env python3
"""
Test script to debug the exact product route issue
"""

import sys
import os
sys.path.insert(0, '.')

def test_product_route():
    """Test the product route directly"""
    try:
        print("🔍 TESTING PRODUCT ROUTE DIRECTLY")
        print("=" * 50)
        
        # Import Flask app
        from app import app
        
        with app.app_context():
            # Get all routes
            rules = list(app.url_map.iter_rules())
            
            # Find product-related routes
            product_routes = []
            for rule in rules:
                if '/products/' in rule.rule and '<product_id>' in rule.rule:
                    product_routes.append({
                        'rule': rule.rule,
                        'endpoint': rule.endpoint,
                        'methods': list(rule.methods)
                    })
            
            print(f"📋 Found {len(product_routes)} product routes:")
            for route in product_routes:
                print(f"   • {route['rule']} → {route['endpoint']} {route['methods']}")
            
            # Test the specific route
            print(f"\n🎯 Testing /products/P003 route...")
            
            # Create test client
            with app.test_client() as client:
                # First login (if needed)
                print("   📝 Attempting to access route...")
                
                response = client.get('/products/P003', follow_redirects=True)
                print(f"   📊 Response status: {response.status_code}")
                print(f"   📊 Response headers: {dict(response.headers)}")
                
                if response.status_code == 200:
                    print("   ✅ Route accessible!")
                    # Check if response contains error
                    response_text = response.get_data(as_text=True)
                    if 'sqlite3.Row' in response_text:
                        print("   ❌ sqlite3.Row error found in response!")
                        # Find the error line
                        lines = response_text.split('\n')
                        for i, line in enumerate(lines):
                            if 'sqlite3.Row' in line:
                                print(f"      Line {i}: {line.strip()}")
                    else:
                        print("   ✅ No sqlite3.Row error in response!")
                else:
                    print(f"   ❌ Route not accessible: {response.status_code}")
                    print(f"   📄 Response: {response.get_data(as_text=True)[:500]}")
                
    except Exception as e:
        print(f"❌ Error testing route: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    test_product_route()
