#!/usr/bin/env python3
"""
Test product routes to ensure they work correctly
"""

import sys
import os
sys.path.append('.')

def test_url_generation():
    """Test URL generation for all product routes"""
    
    print("🧪 TESTING PRODUCT ROUTE URL GENERATION")
    print("=" * 60)
    
    try:
        from app import app
        
        with app.app_context():
            from flask import url_for
            
            # Test all product routes
            routes_to_test = [
                ('products.index', {}, 'Products Index'),
                ('products.product_management', {}, 'Product Management'),
                ('products.new_product', {}, 'New Product'),
                ('products.view_product', {'product_id': 'P001'}, 'View Product'),
                ('products.update_product', {'product_id': 'P001'}, 'Update Product'),
                ('products.update_product_selection', {}, 'Update Product Selection'),
                ('products.view_all_products', {}, 'View All Products'),
                ('products.delete_product', {'product_id': 'P001'}, 'Delete Product'),
                ('products.activate_product', {'product_id': 'P001'}, 'Activate Product'),
                ('products.deactivate_product', {'product_id': 'P001'}, 'Deactivate Product'),
            ]
            
            print("📋 Testing URL generation:")
            print("-" * 40)
            
            success_count = 0
            total_count = len(routes_to_test)
            
            for endpoint, params, description in routes_to_test:
                try:
                    url = url_for(endpoint, **params)
                    print(f"   ✅ {description:<25} → {url}")
                    success_count += 1
                except Exception as e:
                    print(f"   ❌ {description:<25} → ERROR: {str(e)}")
            
            print(f"\n📊 RESULTS: {success_count}/{total_count} routes working")
            
            if success_count == total_count:
                print("🎉 ALL ROUTES WORKING CORRECTLY!")
                return True
            else:
                print("❌ SOME ROUTES STILL HAVE ISSUES")
                return False
                
    except Exception as e:
        print(f"❌ Error testing routes: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_blueprint_registration():
    """Test blueprint registration"""
    
    print("\n🔧 TESTING BLUEPRINT REGISTRATION")
    print("=" * 60)
    
    try:
        from app import app
        
        # Check if products blueprint is registered
        blueprints = [bp.name for bp in app.blueprints.values()]
        print(f"   📋 Registered blueprints: {blueprints}")
        
        if 'products' in blueprints:
            print("   ✅ products blueprint is registered")
            
            # Get products routes
            with app.app_context():
                rules = list(app.url_map.iter_rules())
                products_rules = [rule for rule in rules if 'products.' in rule.endpoint]
                
                print(f"   📋 Found {len(products_rules)} products blueprint routes:")
                for rule in products_rules:
                    print(f"      • {rule.rule:<30} → {rule.endpoint}")
                
                return True
        else:
            print("   ❌ products blueprint is NOT registered")
            return False
            
    except Exception as e:
        print(f"   ❌ Error checking blueprint registration: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    
    print("🔧 COMPREHENSIVE PRODUCT ROUTE TESTING")
    print("=" * 60)
    
    # Test 1: Blueprint registration
    blueprint_ok = test_blueprint_registration()
    
    # Test 2: URL generation
    url_ok = test_url_generation() if blueprint_ok else False
    
    # Summary
    print(f"\n📊 FINAL RESULTS")
    print("=" * 60)
    print(f"Blueprint Registration: {'✅ PASS' if blueprint_ok else '❌ FAIL'}")
    print(f"URL Generation: {'✅ PASS' if url_ok else '❌ FAIL'}")
    
    if blueprint_ok and url_ok:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Product routes should now work without BuildError issues")
        print("\n📝 NEXT STEPS:")
        print("   1. Test the product management page: http://127.0.0.1:5001/product_management")
        print("   2. Test activation/deactivation functionality")
        print("   3. Test KPI cards display")
        print("   4. Test filter functionality")
        return True
    else:
        print("\n❌ TESTS FAILED!")
        print("🔧 Additional fixes may be needed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
