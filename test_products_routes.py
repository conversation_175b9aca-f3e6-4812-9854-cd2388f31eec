#!/usr/bin/env python3
"""
Test products routes and database
"""

import sqlite3
import os
from flask import Flask

def test_products_routes():
    """Test products routes and database"""
    
    print("🔍 TESTING PRODUCTS ROUTES AND DATABASE")
    print("=" * 60)
    
    try:
        # Test database connection
        db_path = 'instance/medivent.db'
        if not os.path.exists(db_path):
            print(f"❌ Database not found at {db_path}")
            return
        
        db = sqlite3.connect(db_path)
        db.row_factory = sqlite3.Row
        cursor = db.cursor()
        
        # Test products table
        print("\n1️⃣ TESTING PRODUCTS TABLE:")
        cursor.execute("SELECT COUNT(*) as count FROM products")
        count = cursor.fetchone()['count']
        print(f"   📊 Total products: {count}")
        
        if count > 0:
            cursor.execute("SELECT product_id, name, unit_price FROM products LIMIT 3")
            products = cursor.fetchall()
            print("   📋 Sample products:")
            for product in products:
                print(f"      • {product['product_id']}: {product['name']} - ${product['unit_price']}")
        else:
            print("   ❌ No products found - creating sample data...")
            create_test_products(cursor, db)
        
        # Test Flask app routes
        print("\n2️⃣ TESTING FLASK ROUTES:")
        try:
            from app import app
            with app.app_context():
                # Get all routes
                rules = list(app.url_map.iter_rules())
                
                # Find product-related routes
                product_routes = []
                for rule in rules:
                    if 'products' in rule.endpoint:
                        product_routes.append({
                            'rule': rule.rule,
                            'endpoint': rule.endpoint,
                            'methods': list(rule.methods)
                        })
                
                print(f"   📋 Found {len(product_routes)} product routes:")
                for route in product_routes:
                    print(f"      • {route['rule']} → {route['endpoint']} {route['methods']}")
        
        except Exception as e:
            print(f"   ❌ Error testing Flask routes: {str(e)}")
        
        db.close()
        print("\n✅ PRODUCTS TESTING COMPLETE")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()

def create_test_products(cursor, db):
    """Create test products"""
    test_products = [
        ('P001', 'Paracetamol 500mg', 'Pain relief tablets', 'Tablets', 'Medivent Pharma', 25.50),
        ('P002', 'Amoxicillin 250mg', 'Antibiotic capsules', 'Capsules', 'Medivent Pharma', 45.00),
        ('P003', 'Cough Syrup', 'Cough relief syrup 100ml', 'Syrup', 'Medivent Pharma', 85.00),
    ]
    
    for product_id, name, description, category, manufacturer, unit_price in test_products:
        try:
            cursor.execute("""
                INSERT OR REPLACE INTO products 
                (product_id, name, description, category, manufacturer, unit_price)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (product_id, name, description, category, manufacturer, unit_price))
            print(f"      ✅ Created: {product_id} - {name}")
        except Exception as e:
            print(f"      ❌ Error creating {product_id}: {str(e)}")
    
    db.commit()

if __name__ == "__main__":
    test_products_routes()
