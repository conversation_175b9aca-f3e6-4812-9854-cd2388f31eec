#!/usr/bin/env python3
"""
Test Python chart generation for finance module
"""
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_python_charts():
    """Test Python chart generation"""
    try:
        from python_chart_generator import PythonChartGenerator
        
        # Sample chart data
        chart_data = {
            'status': {
                'labels': ['Pending', 'Generated', 'On Hold', 'Dispatched'],
                'data': [9, 5, 2, 8]
            },
            'daily': {
                'labels': ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                'data': [2, 4, 3, 6, 8, 5, 7]
            },
            'weekly': {
                'labels': ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
                'data': [15, 22, 18, 25]
            },
            'monthly': {
                'labels': ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                'data': [45, 52, 38, 61, 73, 58]
            }
        }
        
        print("=== TESTING Python Chart Generation ===")
        chart_generator = PythonChartGenerator()
        
        # Generate finance charts
        charts_html = chart_generator.generate_finance_charts(chart_data)

        if charts_html and len(charts_html) > 100:
            print("✅ Full Python charts generated successfully!")
            print(f"   Chart HTML length: {len(charts_html)} characters")
            print("   Contains Plotly elements:", "plotly" in charts_html.lower())
            print("   Contains interactive elements:", "config" in charts_html.lower())
        else:
            print("❌ Full chart generation failed or returned empty result")

        print("\n=== TESTING Simple Status Chart ===")
        # Test simple status chart
        simple_chart_html = chart_generator.generate_simple_status_chart(chart_data)

        if simple_chart_html and len(simple_chart_html) > 100:
            print("✅ Simple status chart generated successfully!")
            print(f"   Chart HTML length: {len(simple_chart_html)} characters")
            print("   Contains Plotly elements:", "plotly" in simple_chart_html.lower())
            print("   Contains responsive config:", "responsive" in simple_chart_html.lower())
        else:
            print("❌ Simple chart generation failed or returned empty result")
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Make sure plotly and other dependencies are installed:")
        print("   pip install plotly pandas matplotlib seaborn")
    except Exception as e:
        print(f"❌ Error testing Python charts: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_python_charts()
