#!/usr/bin/env python3
"""
Test QR Code API endpoint directly
"""

import requests
import json
import sys

def test_qr_api():
    """Test the QR code API endpoint"""
    try:
        print("Testing QR Code API endpoint...")
        
        # Test the API endpoint
        url = "http://127.0.0.1:5001/api/order-qr-code/ORD00000165?branding=true"
        
        print(f"Making request to: {url}")
        
        response = requests.get(url, timeout=30)
        
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("Response JSON:")
                print(json.dumps(data, indent=2))
                
                if data.get('success'):
                    print("✅ QR Code generation successful!")
                    qr_data = data.get('qr_code', {})
                    print(f"   Base64 length: {len(qr_data.get('base64', ''))}")
                    print(f"   File path: {qr_data.get('file_path', 'N/A')}")
                else:
                    print(f"❌ QR Code generation failed: {data.get('error')}")
                    
            except json.JSONDecodeError as e:
                print(f"❌ Invalid JSON response: {e}")
                print(f"Raw response: {response.text[:500]}")
        else:
            print(f"❌ HTTP Error {response.status_code}")
            print(f"Response: {response.text[:500]}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - is the server running on port 5001?")
    except requests.exceptions.Timeout:
        print("❌ Request timeout")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def test_order_details_api():
    """Test the order details API first"""
    try:
        print("\nTesting Order Details API endpoint...")
        
        url = "http://127.0.0.1:5001/api/order-details/ORD00000165"
        
        print(f"Making request to: {url}")
        
        response = requests.get(url, timeout=30)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("Order Details Response:")
                print(json.dumps(data, indent=2)[:1000] + "..." if len(json.dumps(data, indent=2)) > 1000 else json.dumps(data, indent=2))
                
                if data.get('success'):
                    print("✅ Order details retrieved successfully!")
                else:
                    print(f"❌ Order details failed: {data.get('error')}")
                    
            except json.JSONDecodeError as e:
                print(f"❌ Invalid JSON response: {e}")
                print(f"Raw response: {response.text[:500]}")
        else:
            print(f"❌ HTTP Error {response.status_code}")
            print(f"Response: {response.text[:500]}")
            
    except Exception as e:
        print(f"❌ Error testing order details: {e}")

if __name__ == "__main__":
    print("🧪 Testing Enhanced Modal QR Code API")
    print("=" * 50)
    
    # Test order details first
    test_order_details_api()
    
    # Then test QR code generation
    test_qr_api()
    
    print("\n" + "=" * 50)
    print("Test completed!")
