#!/usr/bin/env python3
"""
Test QR code dependencies and functionality
"""

def test_qr_dependencies():
    """Test if QR code libraries are available and working"""
    
    print("🔍 TESTING QR CODE DEPENDENCIES")
    print("=" * 40)
    
    # Test qrcode import
    try:
        import qrcode
        print("✅ qrcode library imported successfully")
        print(f"   Version: {qrcode.__version__ if hasattr(qrcode, '__version__') else 'Unknown'}")
    except ImportError as e:
        print(f"❌ qrcode library not available: {e}")
        return False
    
    # Test PIL/Pillow import
    try:
        from PIL import Image
        print("✅ PIL/Pillow library imported successfully")
    except ImportError as e:
        print(f"❌ PIL/Pillow library not available: {e}")
        return False
    
    # Test QR code generation
    try:
        print("\n🧪 Testing QR code generation...")
        
        # Create QR code instance
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        
        # Add test data
        test_data = "Test QR Code Generation - Medivent ERP System"
        qr.add_data(test_data)
        qr.make(fit=True)
        
        # Create image
        img = qr.make_image(fill_color="black", back_color="white")
        
        print("✅ QR code generated successfully")
        print(f"   Image size: {img.size}")
        print(f"   Image mode: {img.mode}")
        
        return True
        
    except Exception as e:
        print(f"❌ QR code generation failed: {e}")
        return False

def test_advanced_qr_features():
    """Test advanced QR code features for order data"""
    
    print("\n🔧 TESTING ADVANCED QR FEATURES")
    print("=" * 40)
    
    try:
        import qrcode
        import json
        
        # Test complex data structure
        order_data = {
            "order_id": "ORD000000246",
            "customer": "Test Customer",
            "date": "2025-08-05",
            "items": [
                {"product": "Paracetamol 500mg", "qty": 3, "price": 25.5},
                {"product": "Aspirin 100mg", "qty": 2, "price": 15.0}
            ],
            "total": 76.5,
            "company": "Medivent Pharmaceuticals"
        }
        
        # Convert to JSON string
        json_data = json.dumps(order_data, separators=(',', ':'))
        
        print(f"📋 Test data size: {len(json_data)} characters")
        
        # Create QR code with higher error correction for complex data
        qr = qrcode.QRCode(
            version=None,  # Auto-determine version
            error_correction=qrcode.constants.ERROR_CORRECT_M,
            box_size=8,
            border=4,
        )
        
        qr.add_data(json_data)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        
        print(f"✅ Complex QR code generated successfully")
        print(f"   QR Version: {qr.version}")
        print(f"   Image size: {img.size}")
        
        return True
        
    except Exception as e:
        print(f"❌ Advanced QR features test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 QR CODE DEPENDENCIES TEST")
    print("⏰ Starting tests...\n")
    
    # Test basic dependencies
    basic_test = test_qr_dependencies()
    
    # Test advanced features if basic test passes
    advanced_test = False
    if basic_test:
        advanced_test = test_advanced_qr_features()
    
    # Summary
    print("\n" + "=" * 40)
    print("📊 TEST SUMMARY")
    print("=" * 40)
    
    if basic_test and advanced_test:
        print("🎉 ALL TESTS PASSED!")
        print("✅ QR code libraries ready for implementation")
        return True
    else:
        print("⚠️ SOME TESTS FAILED")
        if not basic_test:
            print("❌ Basic QR dependencies missing")
        if not advanced_test:
            print("❌ Advanced QR features not working")
        return False

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
