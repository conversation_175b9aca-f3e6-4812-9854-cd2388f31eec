#!/usr/bin/env python3
"""
Test QR Code generation directly without API
"""

import sys
import os
import json

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_qr_generation_direct():
    """Test QR code generation directly"""
    try:
        print("Testing QR Code generation directly...")
        
        # Import the QR code generator
        from utils.qr_code_generator import OrderQRCodeGenerator
        
        print("✅ QR Code generator imported successfully")
        
        # Create test order data
        test_order_data = {
            'success': True,
            'order': {
                'order_id': 'ORD00000165',
                'order_date_formatted': '2025-08-05 12:00',
                'status': 'Ready for Pickup',
                'order_amount': 76.5,
                'customer_name': 'Test12346999',
                'customer_phone': '02111',
                'customer_address': 'Karachi'
            },
            'items': [
                {
                    'product_name': 'Test Product',
                    'quantity': 1,
                    'unit_price': 76.5,
                    'total_price': 76.5
                }
            ],
            'foc_items': [],
            'customer': {
                'name': 'Test12346999',
                'phone': '02111',
                'address': 'Karachi',
                'city': 'Karachi'
            },
            'summary': {
                'total_items': 1,
                'item_types': 1,
                'foc_items_count': 0,
                'total_amount': 76.5
            }
        }
        
        print("✅ Test order data created")
        
        # Create QR code generator
        generator = OrderQRCodeGenerator()
        print("✅ QR Code generator instance created")
        
        # Generate QR code
        result = generator.generate_order_qr_code(test_order_data, include_branding=True)
        
        print("QR Generation Result:")
        print(json.dumps(result, indent=2, default=str))
        
        if result.get('success'):
            print("✅ QR Code generated successfully!")
            qr_data = result.get('qr_code', {})
            print(f"   File path: {qr_data.get('file_path')}")
            print(f"   Base64 length: {len(qr_data.get('base64', ''))}")
            print(f"   File size: {qr_data.get('size')} bytes")
            
            # Check if file exists
            file_path = qr_data.get('file_path')
            if file_path and os.path.exists(file_path):
                print(f"✅ QR code file exists: {file_path}")
            else:
                print(f"❌ QR code file not found: {file_path}")
                
        else:
            print(f"❌ QR Code generation failed: {result.get('error')}")
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure QR code dependencies are installed:")
        print("pip install qrcode[pil] pillow")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def test_qr_dependencies():
    """Test if QR code dependencies are available"""
    try:
        print("Testing QR code dependencies...")
        
        import qrcode
        print("✅ qrcode library available")
        
        from PIL import Image
        print("✅ PIL (Pillow) library available")
        
        import base64
        print("✅ base64 library available")
        
        from io import BytesIO
        print("✅ BytesIO available")
        
        return True
        
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing QR Code Generation Directly")
    print("=" * 50)
    
    # Test dependencies first
    if test_qr_dependencies():
        print("\n" + "-" * 30)
        test_qr_generation_direct()
    else:
        print("❌ Dependencies not available. Please install:")
        print("pip install qrcode[pil] pillow")
    
    print("\n" + "=" * 50)
    print("Test completed!")
