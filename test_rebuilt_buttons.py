#!/usr/bin/env python3
"""
Comprehensive test script for rebuilt warehouse button system
Tests all routes, database connections, and button functionality
"""

import requests
import time
import json
from datetime import datetime

def test_server_health():
    """Test if server is running and responsive"""
    print("\n🏥 TESTING SERVER HEALTH")
    print("=" * 50)
    
    try:
        response = requests.get('http://127.0.0.1:5001/', timeout=10)
        print(f"   Server Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Server is running and responsive")
            return True
        else:
            print(f"   ❌ Server returned status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Server connection failed: {e}")
        return False

def test_warehouse_packing_page():
    """Test warehouse packing page with new button system"""
    print("\n🏭 TESTING WAREHOUSE PACKING PAGE")
    print("=" * 50)
    
    try:
        response = requests.get('http://127.0.0.1:5001/warehouse/packing', timeout=15)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text
            
            # Test new button system
            print("   🔘 Testing New Button System:")
            
            # Check for new button classes
            if 'warehouse-print-btn' in content:
                print("      ✅ New print address buttons found")
            else:
                print("      ❌ New print address buttons missing")
                
            if 'warehouse-pack-btn' in content:
                print("      ✅ New pack order buttons found")
            else:
                print("      ❌ New pack order buttons missing")
            
            # Check for data attributes
            if 'data-order-id' in content:
                print("      ✅ Data attributes found")
            else:
                print("      ❌ Data attributes missing")
            
            # Check for WarehouseButtonManager
            if 'WarehouseButtonManager' in content:
                print("      ✅ WarehouseButtonManager class found")
            else:
                print("      ❌ WarehouseButtonManager class missing")
            
            # Check for event listeners
            if 'bindEventListeners' in content:
                print("      ✅ Event listeners found")
            else:
                print("      ❌ Event listeners missing")
            
            # Check that old functions are removed
            if 'function printAddress(' in content:
                print("      ❌ Old printAddress function still present")
            else:
                print("      ✅ Old printAddress function removed")
                
            if 'function packOrder(' in content:
                print("      ❌ Old packOrder function still present")
            else:
                print("      ✅ Old packOrder function removed")
            
            # Check for enhanced confirmPackOrder
            if 'Enhanced Pack Order Confirmation' in content:
                print("      ✅ Enhanced confirmPackOrder function found")
            else:
                print("      ❌ Enhanced confirmPackOrder function missing")
            
            return True
            
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_warehouse_routes():
    """Test all warehouse-related routes"""
    print("\n🛣️ TESTING WAREHOUSE ROUTES")
    print("=" * 50)
    
    routes_to_test = [
        ("http://127.0.0.1:5001/warehouse/packing", "GET", "Warehouse Packing Dashboard"),
        ("http://127.0.0.1:5001/warehouse/pack_order", "POST", "Pack Order Route"),
        ("http://127.0.0.1:5001/warehouse/dispatch-order", "POST", "Dispatch Order Route"),
    ]
    
    for url, method, description in routes_to_test:
        try:
            print(f"\n   🧪 Testing: {description}")
            print(f"      URL: {url}")
            print(f"      Method: {method}")
            
            if method == "GET":
                response = requests.get(url, timeout=10)
            else:
                # For POST routes, test with GET to check if route exists (should return 405)
                response = requests.get(url, timeout=10)
            
            print(f"      Status: {response.status_code}")
            
            if method == "GET" and response.status_code == 200:
                print("      ✅ Route accessible")
            elif method == "POST" and response.status_code == 405:
                print("      ✅ Route exists (405 Method Not Allowed expected for GET)")
            else:
                print(f"      ❌ Unexpected status: {response.status_code}")
                
        except Exception as e:
            print(f"      ❌ Error: {e}")

def test_print_address_routes():
    """Test print address routes"""
    print("\n🖨️ TESTING PRINT ADDRESS ROUTES")
    print("=" * 50)
    
    test_orders = ['ORD00000155', 'ORD00000157', 'ORD00000150']
    
    for order_id in test_orders:
        try:
            url = f'http://127.0.0.1:5001/orders/{order_id}/print-address'
            print(f"\n   🧪 Testing: {order_id}")
            print(f"      URL: {url}")
            
            response = requests.get(url, timeout=10)
            print(f"      Status: {response.status_code}")
            
            if response.status_code == 200:
                content = response.text
                if order_id in content and ('address_label' in content or 'ORDER:' in content):
                    print("      ✅ Address label page loads correctly")
                else:
                    print("      ❌ Address label content missing")
            elif response.status_code == 404:
                print("      ⚠️ Order not found (may be expected)")
            else:
                print(f"      ❌ Unexpected status: {response.status_code}")
                
        except Exception as e:
            print(f"      ❌ Error: {e}")

def test_database_connectivity():
    """Test database connectivity through API"""
    print("\n🗄️ TESTING DATABASE CONNECTIVITY")
    print("=" * 50)
    
    try:
        # Test order details API
        url = 'http://127.0.0.1:5001/api/order-details/ORD00000155'
        print(f"   Testing: {url}")
        
        response = requests.get(url, timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            success = data.get('success', False)
            print(f"   API Success: {success}")
            
            if success:
                order = data.get('order', {})
                print(f"   ✅ Database connection working")
                print(f"   📊 Order data retrieved: {order.get('order_id', 'N/A')}")
            else:
                print(f"   ❌ API returned error: {data.get('message', 'Unknown')}")
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Database test error: {e}")

def main():
    """Main test function"""
    print("🚀 COMPREHENSIVE REBUILT BUTTON SYSTEM TEST")
    print("=" * 80)
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Wait for server to be ready
    print("\n⏳ Waiting for server to be ready...")
    time.sleep(2)
    
    # Run all tests
    server_ok = test_server_health()
    
    if server_ok:
        test_warehouse_packing_page()
        test_warehouse_routes()
        test_print_address_routes()
        test_database_connectivity()
    else:
        print("\n❌ Server not accessible - skipping other tests")
    
    print("\n" + "="*80)
    print("🏁 COMPREHENSIVE TESTING COMPLETED")
    print(f"⏰ Test finished at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    print("\n📋 NEXT STEPS:")
    print("1. Open browser to http://127.0.0.1:5001/warehouse/packing")
    print("2. Open browser console (F12)")
    print("3. Look for 'Warehouse Button Manager initialized' message")
    print("4. Test clicking the new Print Address and Pack Order buttons")
    print("5. Verify error handling and loading states")

if __name__ == "__main__":
    main()
