#!/usr/bin/env python3
"""
Comprehensive test for the rebuilt pack button system
Tests all routes individually with HTTP 200 verification
"""

import requests
import time
from datetime import datetime

def test_individual_routes():
    """Test each route individually with HTTP 200 verification"""
    print("🧪 INDIVIDUAL ROUTE TESTING")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:5001"
    
    # Routes to test with expected status codes
    routes_to_test = [
        # Main warehouse routes
        (f"{base_url}/warehouse/packing", "GET", 200, "Warehouse Packing Dashboard"),
        (f"{base_url}/warehouse/orders", "GET", 200, "Warehouse Orders"),
        
        # Pack order route (POST only)
        (f"{base_url}/warehouse/pack_order", "GET", 405, "Pack Order Route (GET should be 405)"),
        (f"{base_url}/warehouse/pack_order", "POST", 400, "Pack Order Route (POST without data should be 400)"),
        
        # Print address routes
        (f"{base_url}/orders/ORD00000155/print-address", "GET", 200, "Print Address"),
        (f"{base_url}/orders/ORD00000157/print-address", "GET", 200, "Print Address Alt"),
        
        # API routes
        (f"{base_url}/api/order-details/ORD00000155", "GET", 200, "API Order Details"),
        (f"{base_url}/orders/ORD00000155/details", "GET", 200, "Fallback Order Details"),
        
        # Dashboard and main routes
        (f"{base_url}/", "GET", 200, "Main Dashboard"),
        (f"{base_url}/dashboard", "GET", 200, "Dashboard"),
    ]
    
    print(f"\n📋 Testing {len(routes_to_test)} routes...")
    
    passed = 0
    failed = 0
    
    for url, method, expected_status, description in routes_to_test:
        try:
            print(f"\n🔗 Testing: {description}")
            print(f"   URL: {url}")
            print(f"   Method: {method}")
            print(f"   Expected: {expected_status}")
            
            if method == "GET":
                response = requests.get(url, timeout=10)
            elif method == "POST":
                response = requests.post(url, timeout=10)
            else:
                print(f"   ❌ Unsupported method: {method}")
                failed += 1
                continue
            
            actual_status = response.status_code
            print(f"   Actual: {actual_status}")
            
            if actual_status == expected_status:
                print(f"   ✅ PASS")
                passed += 1
            else:
                print(f"   ❌ FAIL - Expected {expected_status}, got {actual_status}")
                failed += 1
                
        except Exception as e:
            print(f"   ❌ ERROR: {e}")
            failed += 1
    
    print(f"\n📊 ROUTE TESTING SUMMARY:")
    print(f"   ✅ Passed: {passed}")
    print(f"   ❌ Failed: {failed}")
    print(f"   📈 Success Rate: {(passed/(passed+failed)*100):.1f}%")
    
    return passed, failed

def test_pack_button_functionality():
    """Test the rebuilt pack button functionality"""
    print("\n📦 PACK BUTTON FUNCTIONALITY TEST")
    print("=" * 60)
    
    try:
        # Get the warehouse packing page
        response = requests.get('http://127.0.0.1:5001/warehouse/packing', timeout=15)
        print(f"   Page Status: {response.status_code}")
        
        if response.status_code != 200:
            print(f"   ❌ Failed to load packing page: {response.status_code}")
            return False
        
        content = response.text
        
        # Check for rebuilt pack button
        print("\n🔍 Checking rebuilt pack button:")
        
        button_checks = {
            'New Pack Button': 'onclick="openPackModal(',
            'Pack Modal': 'id="packOrderModal"',
            'Pack Form': 'id="packOrderForm"',
            'Pack Order ID Input': 'id="packOrderId"',
            'Confirm Function': 'onclick="confirmPackOrder()"'
        }
        
        all_found = True
        for check_name, pattern in button_checks.items():
            if pattern in content:
                print(f"   ✅ {check_name}: Found")
            else:
                print(f"   ❌ {check_name}: NOT FOUND")
                all_found = False
        
        # Check for old complex functions (should be removed)
        print("\n🧹 Checking old functions removed:")
        
        old_functions = [
            'packOrderV1',
            'handlePackInline',
            'warehouse-pack-btn',
            'direct-pack-btn'
        ]
        
        for old_func in old_functions:
            if old_func in content:
                print(f"   ⚠️ {old_func}: Still present (should be removed)")
            else:
                print(f"   ✅ {old_func}: Removed")
        
        return all_found
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_pack_order_route_detailed():
    """Test pack order route in detail"""
    print("\n📦 PACK ORDER ROUTE DETAILED TEST")
    print("=" * 60)
    
    try:
        url = 'http://127.0.0.1:5001/warehouse/pack_order'
        
        # Test 1: GET request (should be 405)
        print("🧪 Test 1: GET request (should be 405)")
        response = requests.get(url, timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 405:
            print("   ✅ Correct - GET not allowed")
        else:
            print("   ❌ Unexpected status for GET")
        
        # Test 2: POST without data (should be 400 or error)
        print("\n🧪 Test 2: POST without data")
        response = requests.post(url, timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code in [400, 422, 500]:
            print("   ✅ Correct - POST without data rejected")
        else:
            print("   ❌ Unexpected status for empty POST")
        
        # Test 3: POST with invalid data
        print("\n🧪 Test 3: POST with invalid data")
        data = {'order_id': 'INVALID_ORDER'}
        response = requests.post(url, data=data, timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code in [400, 404, 422, 500]:
            print("   ✅ Correct - Invalid data rejected")
        else:
            print("   ❌ Unexpected status for invalid data")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def main():
    """Main testing function"""
    print("🚀 REBUILT PACK SYSTEM COMPREHENSIVE TEST")
    print("=" * 80)
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Wait for server
    print("\n⏳ Waiting for server...")
    time.sleep(2)
    
    # Run all tests
    passed_routes, failed_routes = test_individual_routes()
    pack_button_ok = test_pack_button_functionality()
    pack_route_ok = test_pack_order_route_detailed()
    
    print("\n" + "="*80)
    print("🏁 COMPREHENSIVE TEST COMPLETED")
    print(f"⏰ Test finished at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    print("\n📋 FINAL SUMMARY:")
    print(f"   🔗 Route Tests: {passed_routes} passed, {failed_routes} failed")
    print(f"   📦 Pack Button: {'✅ OK' if pack_button_ok else '❌ FAIL'}")
    print(f"   🛣️ Pack Route: {'✅ OK' if pack_route_ok else '❌ FAIL'}")
    
    if pack_button_ok and pack_route_ok and failed_routes == 0:
        print("\n🎉 ALL TESTS PASSED - PACK SYSTEM REBUILT SUCCESSFULLY!")
        print("\n📋 NEXT STEPS:")
        print("1. Open browser: http://127.0.0.1:5001/warehouse/packing")
        print("2. Click 'Mark Packed' button on any order")
        print("3. Verify modal opens correctly")
        print("4. Fill form and submit")
        print("5. Verify order is marked as packed")
    else:
        print("\n⚠️ SOME TESTS FAILED - REVIEW ISSUES ABOVE")
        
    print("="*80)

if __name__ == "__main__":
    main()
