#!/usr/bin/env python3
"""
Remote Login Test Script for Flask ERP
Tests login functionality from network access perspective
"""

import requests
import socket
import sys
from datetime import datetime

def get_local_ip():
    """Get the local IP address of this machine"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception as e:
        print(f"Error getting local IP: {e}")
        return None

def test_login_functionality(base_url, username="admin", password="admin123"):
    """Test login functionality with session handling"""
    print(f"🔐 Testing login functionality for: {base_url}")
    print(f"   Username: {username}")
    print(f"   Password: {'*' * len(password)}")
    
    # Create session to maintain cookies
    session = requests.Session()
    
    try:
        # Step 1: Get login page
        print("   📄 Step 1: Getting login page...")
        login_page_response = session.get(f"{base_url}/login", timeout=10)
        
        if login_page_response.status_code == 200:
            print("   ✅ Login page accessible")
        else:
            print(f"   ❌ Login page failed: {login_page_response.status_code}")
            return False
        
        # Step 2: Extract CSRF token if present
        csrf_token = None
        if 'csrf_token' in login_page_response.text:
            # Try to extract CSRF token from the page
            import re
            csrf_match = re.search(r'name="csrf_token"[^>]*value="([^"]*)"', login_page_response.text)
            if csrf_match:
                csrf_token = csrf_match.group(1)
                print(f"   🔑 CSRF token found: {csrf_token[:10]}...")
        
        # Step 3: Prepare login data
        login_data = {
            'username': username,
            'password': password
        }
        
        if csrf_token:
            login_data['csrf_token'] = csrf_token
        
        # Step 4: Attempt login
        print("   🚀 Step 2: Attempting login...")
        login_response = session.post(
            f"{base_url}/login",
            data=login_data,
            timeout=10,
            allow_redirects=False  # Don't follow redirects to see what happens
        )

        print(f"   📊 Login response status: {login_response.status_code}")
        print(f"   🍪 Cookies after login: {len(session.cookies)}")

        # Check for session cookies after login
        for cookie in session.cookies:
            print(f"      - {cookie.name}: {cookie.value[:20]}{'...' if len(cookie.value) > 20 else ''}")

        # Check Set-Cookie headers from login response
        set_cookie_headers = login_response.headers.get('Set-Cookie', '')
        if set_cookie_headers:
            print(f"   🔧 Login Set-Cookie headers: {set_cookie_headers}")
        else:
            print("   ⚠️ No Set-Cookie headers in login response")

        # Step 5: Check login result
        if login_response.status_code == 302:  # Redirect indicates successful login
            redirect_location = login_response.headers.get('Location', '')
            print(f"   ✅ Login successful! Redirecting to: {redirect_location}")

            # Step 6: Test accessing dashboard
            print("   🏠 Step 3: Testing dashboard access...")
            dashboard_response = session.get(f"{base_url}/dashboard", timeout=10)

            print(f"   📊 Dashboard response status: {dashboard_response.status_code}")
            print(f"   🌐 Dashboard final URL: {dashboard_response.url}")

            if dashboard_response.status_code == 200:
                if 'login' not in dashboard_response.url.lower():
                    print("   ✅ Dashboard accessible - login working correctly!")
                    return True
                else:
                    print("   ❌ Dashboard redirected to login - session not maintained")
                    print(f"   🔍 Dashboard response content preview: {dashboard_response.text[:200]}...")
                    return False
            else:
                print(f"   ⚠️ Dashboard access failed: {dashboard_response.status_code}")
                return False
                
        elif login_response.status_code == 200:
            # Check if we're still on login page (login failed)
            if 'login' in login_response.text.lower() or 'username' in login_response.text.lower():
                print("   ❌ Login failed - still on login page")
                if 'invalid' in login_response.text.lower():
                    print("   💡 Reason: Invalid credentials")
                else:
                    print("   💡 Reason: Unknown (check credentials or session handling)")
                return False
            else:
                print("   ✅ Login successful (no redirect)")
                return True
        else:
            print(f"   ❌ Unexpected login response: {login_response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("   ❌ Connection failed - Flask app may not be running")
        return False
    except requests.exceptions.Timeout:
        print("   ❌ Request timeout")
        return False
    except Exception as e:
        print(f"   ❌ Error during login test: {e}")
        return False

def test_session_cookies(base_url):
    """Test session cookie handling"""
    print(f"🍪 Testing session cookie handling for: {base_url}")

    session = requests.Session()

    try:
        # Make a request to get cookies
        response = session.get(f"{base_url}/login", timeout=10)

        print(f"   📊 Response status: {response.status_code}")
        print(f"   🍪 Cookies received: {len(session.cookies)}")
        print(f"   📋 Response headers: {dict(response.headers)}")

        for cookie in session.cookies:
            print(f"      - {cookie.name}: {cookie.value[:20]}{'...' if len(cookie.value) > 20 else ''}")
            print(f"        Domain: {cookie.domain}, Path: {cookie.path}")
            print(f"        Secure: {cookie.secure}, HttpOnly: {cookie.has_nonstandard_attr('HttpOnly')}")

        # Check if Set-Cookie headers are present
        set_cookie_headers = response.headers.get('Set-Cookie', '')
        if set_cookie_headers:
            print(f"   🔧 Set-Cookie headers found: {set_cookie_headers}")
        else:
            print("   ⚠️ No Set-Cookie headers found")

        return len(session.cookies) > 0

    except Exception as e:
        print(f"   ❌ Cookie test failed: {e}")
        return False

def main():
    """Main test function"""
    print("=" * 70)
    print("🧪 Flask ERP Remote Login Test")
    print("=" * 70)
    print(f"Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Get local IP
    local_ip = get_local_ip()
    if not local_ip:
        print("❌ Could not determine local IP address")
        return
    
    print(f"📍 Local IP Address: {local_ip}")
    print()
    
    # Test URLs
    test_urls = [
        f"http://localhost:5000",
        f"http://127.0.0.1:5000",
        f"http://{local_ip}:5000"
    ]
    
    results = {}
    
    for url in test_urls:
        print(f"🌐 Testing URL: {url}")
        print("-" * 50)
        
        # Test basic connectivity
        try:
            response = requests.get(url, timeout=5)
            print(f"   ✅ Basic connectivity: {response.status_code}")
        except Exception as e:
            print(f"   ❌ Basic connectivity failed: {e}")
            results[url] = "Connection Failed"
            print()
            continue
        
        # Test session cookies
        cookie_test = test_session_cookies(url)
        
        # Test login functionality
        login_test = test_login_functionality(url)
        
        results[url] = "✅ Working" if login_test else "❌ Failed"
        print()
    
    # Summary
    print("=" * 70)
    print("📋 Test Results Summary")
    print("=" * 70)
    
    for url, result in results.items():
        print(f"{result} {url}")
    
    print()
    
    # Recommendations
    working_urls = [url for url, result in results.items() if "Working" in result]
    failed_urls = [url for url, result in results.items() if "Failed" in result]
    
    if working_urls:
        print("✅ Working URLs for network access:")
        for url in working_urls:
            print(f"   {url}")
        print()
        print("📱 Share these URLs with other devices on your network:")
        network_urls = [url for url in working_urls if local_ip in url]
        for url in network_urls:
            print(f"   {url}")
    
    if failed_urls:
        print("❌ Failed URLs (troubleshooting needed):")
        for url in failed_urls:
            print(f"   {url}")
        print()
        print("💡 Troubleshooting tips:")
        print("   - Ensure Flask app is running: python app.py")
        print("   - Configure firewall: run configure_firewall.bat as Administrator")
        print("   - Check session cookie settings in app.py")
        print("   - Verify both devices are on same network")
    
    print()
    print("🔧 Next Steps:")
    print("   1. If localhost works but network IP fails: configure firewall")
    print("   2. If login fails on all URLs: check Flask session configuration")
    print("   3. If everything works: test from another device!")
    print()
    print("=" * 70)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"\n\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
