{"timestamp": "2025-08-04T09:29:48.711989", "summary": {"total_tests": 32, "passed_tests": 31, "failed_tests": 1, "success_rate": 96.875}, "detailed_results": {"database_tests": [{"test": "Table partial_dc_tracking exists", "status": "PASS", "details": "Table partial_dc_tracking found"}, {"test": "Table inventory_notifications exists", "status": "PASS", "details": "Table inventory_notifications found"}, {"test": "Table ai_predictions exists", "status": "PASS", "details": "Table ai_predictions found"}, {"test": "Table realtime_inventory_status exists", "status": "PASS", "details": "Table realtime_inventory_status found"}, {"test": "Table partial_dc_analytics exists", "status": "PASS", "details": "Table partial_dc_analytics found"}, {"test": "Table partial_dc_tracking schema", "status": "PASS", "details": "19 columns found"}, {"test": "Table inventory_notifications schema", "status": "PASS", "details": "17 columns found"}, {"test": "Table ai_predictions schema", "status": "PASS", "details": "13 columns found"}, {"test": "Table realtime_inventory_status schema", "status": "PASS", "details": "15 columns found"}, {"test": "Table partial_dc_analytics schema", "status": "PASS", "details": "12 columns found"}, {"test": "Index idx_partial_dc_order_id", "status": "PASS", "details": "Index found"}, {"test": "Index idx_partial_dc_product_id", "status": "PASS", "details": "Index found"}, {"test": "Index idx_inventory_notifications_product", "status": "PASS", "details": "Index found"}, {"test": "Index idx_ai_predictions_product", "status": "PASS", "details": "Index found"}, {"test": "Sample data exists", "status": "PASS", "details": "Tracking: 3, Notifications: 3"}], "route_tests": [{"test": "Partial Pending Dashboard (/partial-pending/)", "status": "PASS", "details": "Blueprint registered successfully"}, {"test": "Order Details (/partial-pending/order/ORD00000001)", "status": "PASS", "details": "Blueprint registered successfully"}, {"test": "Product Status (/partial-pending/product/P001)", "status": "PASS", "details": "Blueprint registered successfully"}, {"test": "Real-time Inventory API (/partial-pending/api/real-time-inventory/P001)", "status": "PASS", "details": "Blueprint registered successfully"}], "template_tests": [{"test": "Template templates/partial_pending/index.html", "status": "PASS", "details": "File found"}, {"test": "Template templates/partial_pending/index.html content", "status": "PASS", "details": "Content score: 3/3"}, {"test": "Template templates/partial_pending/order_details.html", "status": "PASS", "details": "File found"}, {"test": "Template templates/partial_pending/order_details.html content", "status": "PASS", "details": "Content score: 3/3"}, {"test": "Template templates/partial_pending/product_status.html", "status": "PASS", "details": "File found"}, {"test": "Template templates/partial_pending/product_status.html content", "status": "PASS", "details": "Content score: 3/3"}], "api_tests": [{"test": "API function get_real_time_inventory", "status": "PASS", "details": "Function found in routes/partial_pending.py"}, {"test": "API function mark_notifications_read", "status": "PASS", "details": "Function found in routes/partial_pending.py"}, {"test": "API function fulfill_pending_item", "status": "PASS", "details": "Function found in routes/partial_pending.py"}], "integration_tests": [{"test": "Partial DC - Orders integration", "status": "FAIL", "details": "0 integrated records found"}, {"test": "Notifications - Products integration", "status": "PASS", "details": "2 integrated notifications"}, {"test": "AI predictions table", "status": "PASS", "details": "0 AI predictions stored"}, {"test": "Blueprint registration", "status": "PASS", "details": "Blueprint registered in app.py"}]}}