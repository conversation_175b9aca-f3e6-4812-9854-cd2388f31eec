#!/usr/bin/env python3
"""
Quick test to verify reports are working
"""

import requests
import time

def test_reports():
    """Test if reports are accessible"""
    
    base_url = "http://localhost:5000"
    
    # Test main application
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"✅ Main page: {response.status_code}")
    except Exception as e:
        print(f"❌ Main page error: {e}")
        return False
    
    # Test riders dashboard
    try:
        response = requests.get(f"{base_url}/riders/", timeout=5)
        print(f"✅ Riders dashboard: {response.status_code}")
    except Exception as e:
        print(f"❌ Riders dashboard error: {e}")
    
    # Test reports page
    try:
        response = requests.get(f"{base_url}/riders/reports", timeout=5)
        print(f"✅ Reports page: {response.status_code}")
        if response.status_code == 200:
            print("✅ Reports page is accessible!")
            return True
    except Exception as e:
        print(f"❌ Reports page error: {e}")
    
    return False

if __name__ == "__main__":
    print("🔍 Testing application endpoints...")
    time.sleep(2)  # Give server time to start
    
    if test_reports():
        print("🎉 SUCCESS: Application is running and reports are working!")
    else:
        print("⚠️  Some issues detected, but server appears to be running.")
