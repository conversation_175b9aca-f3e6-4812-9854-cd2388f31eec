#!/usr/bin/env python3
"""
Test all rider routes for HTTP 200 status codes
"""

import requests
import sys
import time

def test_rider_routes():
    """Test all rider-related routes"""
    base_url = "http://127.0.0.1:5000"
    
    # Rider routes to test
    rider_routes = [
        "/riders/",
        "/riders/dashboard", 
        "/riders/tracking",
        "/riders/performance",
        "/riders/analytics",
        "/riders/reports"
    ]
    
    # Warehouse routes related to riders
    warehouse_routes = [
        "/warehouse/packing",
        "/warehouse/orders"
    ]
    
    print("🧪 TESTING RIDER ROUTES")
    print("=" * 50)
    
    # Test if server is running
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"✅ Server is running at {base_url}")
    except requests.exceptions.RequestException as e:
        print(f"❌ Server not accessible at {base_url}")
        print(f"   Error: {e}")
        print("   Please start the Flask application first")
        return False
    
    print(f"\n🔍 TESTING RIDER ROUTES:")
    print("-" * 30)
    
    all_passed = True
    
    for route in rider_routes:
        try:
            url = f"{base_url}{route}"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                print(f"✅ {route} - HTTP {response.status_code}")
            elif response.status_code == 302:
                print(f"🔄 {route} - HTTP {response.status_code} (Redirect - likely login required)")
            else:
                print(f"❌ {route} - HTTP {response.status_code}")
                all_passed = False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ {route} - Connection Error: {e}")
            all_passed = False
        
        time.sleep(0.5)  # Small delay between requests
    
    print(f"\n🔍 TESTING WAREHOUSE ROUTES:")
    print("-" * 30)
    
    for route in warehouse_routes:
        try:
            url = f"{base_url}{route}"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                print(f"✅ {route} - HTTP {response.status_code}")
            elif response.status_code == 302:
                print(f"🔄 {route} - HTTP {response.status_code} (Redirect - likely login required)")
            else:
                print(f"❌ {route} - HTTP {response.status_code}")
                all_passed = False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ {route} - Connection Error: {e}")
            all_passed = False
        
        time.sleep(0.5)
    
    print(f"\n📊 ROUTE TESTING SUMMARY:")
    print("=" * 30)
    
    if all_passed:
        print("✅ All routes accessible (200 or 302 redirect)")
        print("🔄 302 redirects are normal for login-protected routes")
    else:
        print("❌ Some routes failed - check server logs for details")
    
    return all_passed

if __name__ == "__main__":
    test_rider_routes()
