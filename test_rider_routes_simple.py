#!/usr/bin/env python3
"""
Simple test to check rider routes without starting the full app
"""

import sys
import os

def test_imports():
    """Test basic imports"""
    print("🧪 Testing Basic Imports...")
    
    try:
        # Test Flask import
        from flask import Flask
        print("✅ Flask imported successfully")
        
        # Test blueprint import
        from routes.modern_riders import riders_bp
        print("✅ riders_bp imported successfully")
        print(f"   Blueprint name: {riders_bp.name}")
        print(f"   URL prefix: {riders_bp.url_prefix}")
        
        # Check blueprint routes
        print(f"   Blueprint has {len(riders_bp.deferred_functions)} deferred functions")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_route_definitions():
    """Test route definitions"""
    print("\n🔍 Testing Route Definitions...")
    
    try:
        from routes.modern_riders import riders_bp
        
        # Create a test app to register the blueprint
        from flask import Flask
        test_app = Flask(__name__)
        test_app.register_blueprint(riders_bp)
        
        # Get all routes
        routes = []
        with test_app.app_context():
            for rule in test_app.url_map.iter_rules():
                if 'riders' in rule.endpoint:
                    routes.append({
                        'endpoint': rule.endpoint,
                        'rule': rule.rule,
                        'methods': list(rule.methods)
                    })
        
        print(f"Found {len(routes)} rider routes:")
        for route in routes:
            print(f"  ✅ {route['rule']} -> {route['endpoint']} {route['methods']}")
            
        # Check for specific routes
        assignment_found = any('assignment_dashboard' in route['endpoint'] for route in routes)
        self_pickup_found = any('self_pickup' in route['endpoint'] for route in routes)
        
        if assignment_found:
            print("✅ assignment_dashboard route found")
        else:
            print("❌ assignment_dashboard route NOT found")
            
        if self_pickup_found:
            print("✅ self_pickup route found")
        else:
            print("❌ self_pickup route NOT found")
            
        return True
        
    except Exception as e:
        print(f"❌ Route definition error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_template_references():
    """Test template URL references"""
    print("\n📄 Testing Template References...")
    
    try:
        # Check specific templates for url_for calls
        templates_to_check = [
            'templates/riders/modern_dashboard.html',
            'templates/warehouse/packing_dashboard.html'
        ]
        
        for template_path in templates_to_check:
            if os.path.exists(template_path):
                with open(template_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # Look for riders.assignment_dashboard references
                if 'riders.assignment_dashboard' in content:
                    print(f"✅ {template_path} contains 'riders.assignment_dashboard'")
                else:
                    print(f"❌ {template_path} does NOT contain 'riders.assignment_dashboard'")
                    
                # Look for any url_for with riders
                import re
                url_for_matches = re.findall(r"url_for\(['\"]([^'\"]*riders[^'\"]*)['\"]", content)
                if url_for_matches:
                    print(f"   Found url_for references: {url_for_matches}")
            else:
                print(f"❌ Template not found: {template_path}")
                
        return True
        
    except Exception as e:
        print(f"❌ Template check error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 SIMPLE RIDER ROUTES TEST")
    print("=" * 50)
    
    success = True
    
    # Test 1: Basic imports
    success &= test_imports()
    
    # Test 2: Route definitions
    success &= test_route_definitions()
    
    # Test 3: Template references
    success &= test_template_references()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 All tests passed!")
    else:
        print("❌ Some tests failed!")
        
    print("🏁 Test complete")
