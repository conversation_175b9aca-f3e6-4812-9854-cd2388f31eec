#!/usr/bin/env python3
import sqlite3
from datetime import datetime

def get_db():
    db = sqlite3.connect('instance/medivent.db')
    db.row_factory = sqlite3.Row
    return db

def test_product_management_logic():
    """Test the product management route logic"""
    try:
        db = get_db()
        cursor = db.cursor()

        # Get filter parameters (simulating empty filters)
        status_filter = ''
        search_query = ''
        category_filter = ''

        # Build query with filters
        where_conditions = []
        params = []

        # Status filter
        if status_filter == 'active':
            where_conditions.append("(LOWER(status) = 'active' AND is_active = 1)")
        elif status_filter == 'inactive':
            where_conditions.append("(LOWER(status) != 'active' OR is_active = 0)")

        # Search filter
        if search_query:
            where_conditions.append("(name LIKE ? OR generic_name LIKE ? OR manufacturer LIKE ? OR category LIKE ?)")
            search_param = f'%{search_query}%'
            params.extend([search_param, search_param, search_param, search_param])

        # Category filter
        if category_filter:
            where_conditions.append("category = ?")
            params.append(category_filter)

        # Build final query
        base_query = "SELECT * FROM products"
        if where_conditions:
            base_query += " WHERE " + " AND ".join(where_conditions)
        base_query += " ORDER BY name"

        cursor.execute(base_query, params)
        raw_products = cursor.fetchall()

        # Convert to enhanced product dictionaries
        products = []
        for row in raw_products:
            try:
                products.append({
                    'id': row['id'] if 'id' in row.keys() else 0,  # Database primary key
                    'product_id': row['product_id'] if 'product_id' in row.keys() else (row['id'] if 'id' in row.keys() else 0),  # Product code
                    'name': row['name'] if 'name' in row.keys() else 'Unnamed Product',
                    'description': row['description'] if 'description' in row.keys() else '',
                    'unit_price': float(row['unit_price']) if 'unit_price' in row.keys() and row['unit_price'] else 0,
                    'category': row['category'] if 'category' in row.keys() else 'Uncategorized',
                    'manufacturer': row['manufacturer'] if 'manufacturer' in row.keys() else 'N/A',
                    'generic_name': row['generic_name'] if 'generic_name' in row.keys() else 'N/A',
                    'strength': row['strength'] if 'strength' in row.keys() else 'N/A',
                    'status': row['status'] if 'status' in row.keys() else 'active',
                    'is_active': bool(row['is_active']) if 'is_active' in row.keys() and row['is_active'] is not None else True,
                    'image_url': row['image_url'] if 'image_url' in row.keys() else '',
                    'created_at': row['created_at'] if 'created_at' in row.keys() else None,
                    'updated_at': row['updated_at'] if 'updated_at' in row.keys() else None
                })
            except Exception as e:
                print(f"Error processing product row: {e}")
                continue

        # Calculate enhanced stats
        total_products = len(products)
        active_products = len([p for p in products if p['is_active']])
        inactive_products = total_products - active_products
        categories = len({p['category'] for p in products if p['category'] != 'Uncategorized'})
        manufacturers = len({p['manufacturer'] for p in products if p['manufacturer'] != 'N/A'})

        stats = {
            'total_products': total_products,
            'active_products': active_products,
            'inactive_products': inactive_products,
            'categories': categories,
            'manufacturers': manufacturers
        }

        print("=== PRODUCT MANAGEMENT ROUTE TEST ===")
        print(f"Total products found: {total_products}")
        print(f"Active products: {active_products}")
        print(f"Inactive products: {inactive_products}")
        print(f"Categories: {categories}")
        print(f"Manufacturers: {manufacturers}")
        print("\nFirst 3 products:")
        for i, product in enumerate(products[:3]):
            print(f"  {i+1}. ID: {product['id']}, Product ID: {product['product_id']}, Name: {product['name']}, Active: {product['is_active']}")
        
        print("\nRoute logic test PASSED!")
        return True

    except Exception as e:
        print(f"Route logic test FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_product_management_logic()
