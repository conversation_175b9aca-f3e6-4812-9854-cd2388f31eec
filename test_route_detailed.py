#!/usr/bin/env python3
"""
Test product management route in detail
"""

import requests
import sys

def test_product_management():
    """Test the product management route in detail"""
    
    print("🧪 TESTING PRODUCT MANAGEMENT ROUTE")
    print("=" * 50)
    
    try:
        # Test the product management route
        url = 'http://127.0.0.1:5001/products/product_management/'
        print(f"Testing URL: {url}")
        
        response = requests.get(url)
        print(f'Status Code: {response.status_code}')
        
        if response.status_code == 200:
            print('✅ Route is accessible')
            
            content = response.text
            print(f'Content Length: {len(content)} characters')
            
            # Check for key elements
            checks = [
                ('Total Products', 'KPI cards'),
                ('confirmDelete', 'Delete functionality'),
                ('toggleProductStatus', 'Toggle functionality'),
                ('product_management.html', 'Template rendering'),
                ('Paracetamol', 'Sample product data'),
                ('Active Products', 'Active products KPI'),
                ('Inactive Products', 'Inactive products KPI'),
                ('Categories', 'Categories KPI'),
                ('btn btn-outline-danger', 'Delete buttons'),
                ('btn btn-outline-success', 'Activate buttons'),
                ('btn btn-outline-warning', 'Deactivate buttons')
            ]
            
            print("\n🔍 Content Analysis:")
            for search_term, description in checks:
                if search_term in content:
                    print(f"  ✅ {description}: Found")
                else:
                    print(f"  ❌ {description}: Missing")
            
            # Check for error messages
            error_indicators = ['Error', 'Exception', 'Traceback', '500', '404']
            print("\n🚨 Error Check:")
            for error in error_indicators:
                if error in content:
                    print(f"  ⚠️  Found '{error}' in response")
                    # Show context around error
                    error_pos = content.find(error)
                    start = max(0, error_pos - 100)
                    end = min(len(content), error_pos + 100)
                    print(f"     Context: ...{content[start:end]}...")
            
            # Show first 500 characters for debugging
            print(f"\n📄 First 500 characters of response:")
            print(content[:500])
            print("...")
            
        else:
            print(f'❌ Route failed with status {response.status_code}')
            print(f'Response headers: {dict(response.headers)}')
            print(f'Response content: {response.text[:1000]}...')
            
    except Exception as e:
        print(f'❌ Error testing route: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_product_management()
