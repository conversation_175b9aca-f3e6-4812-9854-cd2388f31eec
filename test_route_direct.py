#!/usr/bin/env python3
"""
Test the assignment dashboard route directly
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.getcwd())

def test_route_registration():
    """Test if the route is properly registered"""
    try:
        # Import the Flask app
        from app import app
        
        print("🧪 TESTING ROUTE REGISTRATION")
        print("=" * 50)
        
        # Get all registered routes
        routes = []
        for rule in app.url_map.iter_rules():
            routes.append({
                'endpoint': rule.endpoint,
                'methods': list(rule.methods),
                'rule': rule.rule
            })
        
        # Look for rider-related routes
        rider_routes = [route for route in routes if 'riders' in route['endpoint']]
        
        print(f"📊 Found {len(rider_routes)} rider routes:")
        for route in rider_routes:
            print(f"  ✅ {route['rule']} -> {route['endpoint']} {route['methods']}")
        
        # Check if our new route exists
        assignment_route = [route for route in rider_routes if 'assignment' in route['endpoint']]
        
        if assignment_route:
            print(f"\n✅ ASSIGNMENT DASHBOARD ROUTE FOUND!")
            print(f"   Route: {assignment_route[0]['rule']}")
            print(f"   Endpoint: {assignment_route[0]['endpoint']}")
            print(f"   Methods: {assignment_route[0]['methods']}")
            return True
        else:
            print(f"\n❌ ASSIGNMENT DASHBOARD ROUTE NOT FOUND!")
            return False
        
    except Exception as e:
        print(f"❌ Error testing route registration: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_query():
    """Test the database query used in the assignment dashboard"""
    try:
        from utils.db import get_db
        
        print(f"\n🗄️ TESTING DATABASE QUERIES")
        print("=" * 30)
        
        db = get_db()
        
        # Test the ready orders query
        ready_orders = db.execute('''
            SELECT o.*, c.name as customer_name, c.address as customer_address,
                   c.phone as customer_phone, c.city as customer_city
            FROM orders o
            LEFT JOIN customers c ON o.customer_id = c.customer_id
            WHERE o.status = 'Ready for Pickup' AND o.warehouse_status = 'packed'
            AND (o.rider_id IS NULL OR o.rider_id = '')
            ORDER BY o.priority_level DESC, o.packed_at ASC
        ''').fetchall()
        
        print(f"✅ Ready orders query executed successfully")
        print(f"   Found {len(ready_orders)} orders ready for assignment")
        
        # Test the available riders query
        available_riders = db.execute('''
            SELECT rider_id, name, phone, vehicle_type, current_location, rating
            FROM riders
            WHERE status = 'active' AND is_available = 1
            ORDER BY rating DESC
        ''').fetchall()
        
        print(f"✅ Available riders query executed successfully")
        print(f"   Found {len(available_riders)} available riders")
        
        return True
        
    except Exception as e:
        print(f"❌ Database query error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 DIRECT ROUTE TESTING")
    print("=" * 50)
    
    route_test = test_route_registration()
    db_test = test_database_query()
    
    if route_test and db_test:
        print(f"\n🎉 ALL TESTS PASSED!")
        print("✅ Assignment dashboard implementation successful!")
    else:
        print(f"\n❌ SOME TESTS FAILED!")
        print("❌ Check implementation details")
