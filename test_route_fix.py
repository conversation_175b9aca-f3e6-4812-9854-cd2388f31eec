#!/usr/bin/env python3
"""
Test script to verify the route fixes for products
"""

import sys
import os
sys.path.insert(0, '.')

def test_route_registration():
    """Test if routes are properly registered"""
    try:
        from app import app
        
        print("🧪 TESTING ROUTE REGISTRATION")
        print("=" * 50)
        
        with app.app_context():
            rules = list(app.url_map.iter_rules())
            
            # Check for duplicate routes
            update_selection_routes = [rule for rule in rules if 'update_product_selection' in rule.endpoint]
            view_all_routes = [rule for rule in rules if 'view_all_products' in rule.endpoint]
            
            print(f"🎯 Update Selection Routes: {len(update_selection_routes)}")
            for rule in update_selection_routes:
                print(f"  ✅ {rule.rule} -> {rule.endpoint}")
            
            print(f"\n🎯 View All Routes: {len(view_all_routes)}")
            for rule in view_all_routes:
                print(f"  ✅ {rule.rule} -> {rule.endpoint}")
            
            # Check if we have only blueprint routes (no duplicates)
            if len(update_selection_routes) == 1 and 'products.update_product_selection' in [r.endpoint for r in update_selection_routes]:
                print("\n✅ Update selection route is correctly using blueprint")
            else:
                print("\n❌ Update selection route has issues")
            
            if len(view_all_routes) == 1 and 'products.view_all_products' in [r.endpoint for r in view_all_routes]:
                print("✅ View all route is correctly using blueprint")
            else:
                print("❌ View all route has issues")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_database_connection():
    """Test database connection"""
    try:
        from app import app
        from flask import g
        import sqlite3
        
        print("\n🗄️ TESTING DATABASE CONNECTION")
        print("=" * 50)
        
        with app.app_context():
            # Test database connection
            db = sqlite3.connect('instance/medivent.db')
            db.row_factory = sqlite3.Row
            
            # Test products table
            cursor = db.execute("SELECT COUNT(*) as count FROM products")
            result = cursor.fetchone()
            product_count = result['count'] if result else 0
            
            print(f"✅ Database connected successfully")
            print(f"✅ Products table accessible: {product_count} products found")
            
            db.close()
            return True
            
    except Exception as e:
        print(f"❌ Database error: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 STARTING ROUTE FIX VERIFICATION")
    print("=" * 60)
    
    success = True
    
    # Test route registration
    if not test_route_registration():
        success = False
    
    # Test database connection
    if not test_database_connection():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 ALL TESTS PASSED - Routes are fixed!")
    else:
        print("❌ SOME TESTS FAILED - Check errors above")
    print("=" * 60)
