#!/usr/bin/env python3
"""
Test the P003 product route through Flask app
"""

import requests
import time

def test_product_route():
    """Test the product route"""
    
    print("🧪 TESTING PRODUCT P003 ROUTE")
    print("=" * 40)
    
    # Wait for Flask app to be ready
    print("⏳ Waiting for Flask app...")
    time.sleep(2)
    
    # Test the specific route that was failing
    test_urls = [
        'http://127.0.0.1:5001/products/P003',
        'http://127.0.0.1:5001/products/PROD001',
        'http://127.0.0.1:5001/products/PROD002',
        'http://127.0.0.1:5001/product_management'
    ]
    
    for url in test_urls:
        try:
            response = requests.get(url, timeout=10)
            status = '✅' if response.status_code == 200 else '❌'
            print(f"{status} {url}: HTTP {response.status_code}")
            
            if response.status_code != 200:
                print(f"   Error: {response.text[:200]}...")
                
        except Exception as e:
            print(f"❌ {url}: Connection Error - {e}")
    
    print("\n✅ Route testing completed")

if __name__ == "__main__":
    test_product_route()
