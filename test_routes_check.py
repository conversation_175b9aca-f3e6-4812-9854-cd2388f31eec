#!/usr/bin/env python3
"""
Test route registration after fixing the import issue
"""

print("🔍 TESTING ROUTE REGISTRATION")

try:
    print("📦 Step 1: Importing Flask app...")
    from app import app
    print("✅ Flask app imported successfully")
    print(f"   App name: {app.name}")
    
    print("\n📦 Step 2: Checking route registration...")
    with app.app_context():
        rules = list(app.url_map.iter_rules())
        print(f"   Total routes registered: {len(rules)}")
        
        print("\n📦 Step 3: Looking for product_management routes...")
        mgmt_routes = []
        for rule in rules:
            if 'product_management' in rule.rule or 'product_management' in rule.endpoint:
                mgmt_routes.append({
                    'rule': rule.rule,
                    'endpoint': rule.endpoint,
                    'methods': list(rule.methods)
                })
        
        print(f"   Found {len(mgmt_routes)} product_management routes:")
        for route in mgmt_routes:
            print(f"      • {route['rule']} → {route['endpoint']} {route['methods']}")
        
        print("\n📦 Step 4: Looking for direct /product_management route...")
        direct_routes = []
        for rule in rules:
            if rule.rule == '/product_management':
                direct_routes.append({
                    'rule': rule.rule,
                    'endpoint': rule.endpoint,
                    'methods': list(rule.methods)
                })
        
        print(f"   Found {len(direct_routes)} direct /product_management routes:")
        for route in direct_routes:
            print(f"      • {route['rule']} → {route['endpoint']} {route['methods']}")
        
        print("\n📦 Step 5: Looking for DC generation routes...")
        dc_routes = []
        for rule in rules:
            if 'generate-partial-dc' in rule.rule or 'dc' in rule.rule.lower():
                dc_routes.append({
                    'rule': rule.rule,
                    'endpoint': rule.endpoint,
                    'methods': list(rule.methods)
                })
        
        print(f"   Found {len(dc_routes)} DC generation routes:")
        for route in dc_routes[:10]:  # Show first 10
            print(f"      • {route['rule']} → {route['endpoint']} {route['methods']}")
        
        if len(dc_routes) > 10:
            print(f"      ... and {len(dc_routes) - 10} more DC routes")
    
    print("\n🎉 ROUTE ANALYSIS COMPLETE")
    
except Exception as e:
    print(f"❌ ERROR: {e}")
    import traceback
    traceback.print_exc()
