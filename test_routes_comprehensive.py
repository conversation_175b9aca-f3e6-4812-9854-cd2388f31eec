#!/usr/bin/env python3
"""
Comprehensive route testing script to verify all fixes
"""

import requests
import time
import sys

def test_routes():
    """Test all critical routes for HTTP 200 responses"""
    
    base_url = "http://127.0.0.1:5001"
    
    # Critical routes to test
    test_routes = [
        # Finance Dashboard (main focus)
        "/finance/dashboard",
        
        # Product routes (where d.manager error occurred)
        "/products",
        
        # Inventory routes (where m.product_id error occurred)
        "/inventory",
        
        # Division routes
        "/divisions",
        
        # Main dashboard
        "/dashboard",
        
        # Login page
        "/login",
    ]
    
    print("🧪 COMPREHENSIVE ROUTE TESTING")
    print("=" * 60)
    print(f"🌐 Base URL: {base_url}")
    print(f"📋 Testing {len(test_routes)} routes")
    print("=" * 60)
    
    results = []
    
    for route in test_routes:
        url = f"{base_url}{route}"
        print(f"\n🔍 Testing: {route}")
        
        try:
            # Make request with timeout
            response = requests.get(url, timeout=10, allow_redirects=True)
            
            status_code = response.status_code
            
            if status_code == 200:
                print(f"✅ SUCCESS: {route} → HTTP {status_code}")
                results.append({"route": route, "status": "SUCCESS", "code": status_code})
            elif status_code in [302, 301]:
                print(f"🔄 REDIRECT: {route} → HTTP {status_code} (redirected to {response.url})")
                results.append({"route": route, "status": "REDIRECT", "code": status_code})
            else:
                print(f"❌ ERROR: {route} → HTTP {status_code}")
                results.append({"route": route, "status": "ERROR", "code": status_code})
                
        except requests.exceptions.ConnectionError:
            print(f"🔌 CONNECTION ERROR: {route} → Server not running")
            results.append({"route": route, "status": "CONNECTION_ERROR", "code": "N/A"})
        except requests.exceptions.Timeout:
            print(f"⏰ TIMEOUT: {route} → Request timed out")
            results.append({"route": route, "status": "TIMEOUT", "code": "N/A"})
        except Exception as e:
            print(f"💥 EXCEPTION: {route} → {str(e)}")
            results.append({"route": route, "status": "EXCEPTION", "code": "N/A"})
        
        # Small delay between requests
        time.sleep(0.5)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    success_count = len([r for r in results if r["status"] == "SUCCESS"])
    redirect_count = len([r for r in results if r["status"] == "REDIRECT"])
    error_count = len([r for r in results if r["status"] in ["ERROR", "CONNECTION_ERROR", "TIMEOUT", "EXCEPTION"]])
    
    print(f"✅ Successful: {success_count}")
    print(f"🔄 Redirects: {redirect_count}")
    print(f"❌ Errors: {error_count}")
    print(f"📊 Total: {len(results)}")
    
    if error_count == 0:
        print("\n🎉 ALL ROUTES WORKING CORRECTLY!")
        return True
    else:
        print(f"\n⚠️  {error_count} ROUTES NEED ATTENTION")
        return False

def test_specific_database_operations():
    """Test specific database operations that were causing errors"""
    
    print("\n" + "=" * 60)
    print("🗄️  TESTING SPECIFIC DATABASE OPERATIONS")
    print("=" * 60)
    
    # Test finance dashboard specifically
    finance_url = "http://127.0.0.1:5001/finance/dashboard"
    
    try:
        print(f"\n🔍 Testing Finance Dashboard: {finance_url}")
        response = requests.get(finance_url, timeout=15)
        
        if response.status_code == 200:
            content = response.text
            
            # Check for specific issues
            if "₹" in content:
                print("❌ Currency symbols still present")
            else:
                print("✅ Currency symbols removed")
                
            if "TOTAL REVENUE" in content:
                print("✅ Card names are visible")
            else:
                print("❌ Card names missing")
                
            if "Error loading" in content:
                print("❌ Database errors still present")
            else:
                print("✅ No database errors detected")
                
        else:
            print(f"❌ Finance dashboard returned HTTP {response.status_code}")
            
    except Exception as e:
        print(f"💥 Error testing finance dashboard: {e}")

if __name__ == "__main__":
    print("🚀 Starting comprehensive route testing...")
    
    # Test all routes
    routes_ok = test_routes()
    
    # Test specific database operations
    test_specific_database_operations()
    
    if routes_ok:
        print("\n🎯 TESTING COMPLETED SUCCESSFULLY!")
        sys.exit(0)
    else:
        print("\n⚠️  SOME ISSUES DETECTED - CHECK LOGS")
        sys.exit(1)
