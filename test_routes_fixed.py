#!/usr/bin/env python3
"""
Test Routes After Fix
Verify that all routes are working correctly after fixing the dispatch_order conflict
"""

def test_flask_routes():
    """Test Flask routes registration"""
    
    print("🧪 TESTING FLASK ROUTES AFTER FIX")
    print("=" * 50)
    
    try:
        # Import Flask app
        from app import app
        
        print("✅ Flask app imported successfully")
        
        # Get all registered routes
        with app.app_context():
            rules = list(app.url_map.iter_rules())
            endpoints = [rule.endpoint for rule in rules]
            
            print(f"📊 Total routes registered: {len(rules)}")
            
            # Check for our new warehouse routes
            warehouse_routes = [
                'warehouse_packing_dashboard',
                'warehouse_dispatch_order', 
                'get_order_details_json',
                'print_order_address'
            ]
            
            print(f"\n🏭 WAREHOUSE PACKING ROUTES:")
            for route in warehouse_routes:
                if route in endpoints:
                    print(f"   ✅ {route}")
                else:
                    print(f"   ❌ {route} - MISSING")
            
            # Check for dispatch conflicts
            dispatch_routes = [ep for ep in endpoints if 'dispatch' in ep.lower()]
            print(f"\n🚚 ALL DISPATCH ROUTES:")
            for route in dispatch_routes:
                print(f"   • {route}")
            
            # Check for any duplicate endpoints
            from collections import Counter
            endpoint_counts = Counter(endpoints)
            duplicates = {ep: count for ep, count in endpoint_counts.items() if count > 1}
            
            if duplicates:
                print(f"\n⚠️  DUPLICATE ENDPOINTS FOUND:")
                for ep, count in duplicates.items():
                    print(f"   • {ep}: {count} times")
            else:
                print(f"\n✅ NO DUPLICATE ENDPOINTS - All routes are unique!")
            
            # Test specific route patterns
            packing_routes = [rule for rule in rules if '/warehouse/packing' in rule.rule]
            dispatch_routes = [rule for rule in rules if '/warehouse/dispatch-order' in rule.rule]
            
            print(f"\n📋 PACKING DASHBOARD ROUTES:")
            for rule in packing_routes:
                print(f"   • {rule.rule} → {rule.endpoint}")
            
            print(f"\n📦 DISPATCH ORDER ROUTES:")
            for rule in dispatch_routes:
                print(f"   • {rule.rule} → {rule.endpoint}")
            
            print(f"\n✅ ROUTE REGISTRATION TEST PASSED!")
            print(f"🎉 All warehouse packing routes are properly registered!")
            
            return True
            
    except Exception as e:
        print(f"❌ Route test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_route_accessibility():
    """Test that routes can be accessed without errors"""
    
    print(f"\n🌐 TESTING ROUTE ACCESSIBILITY")
    print("-" * 30)
    
    try:
        from app import app
        
        # Create test client
        with app.test_client() as client:
            # Test routes that don't require authentication for basic response
            test_routes = [
                ('/', 'GET'),
                ('/login', 'GET'),
            ]
            
            print("🔍 Testing basic routes...")
            for route, method in test_routes:
                try:
                    if method == 'GET':
                        response = client.get(route)
                    else:
                        response = client.post(route)
                    
                    print(f"   • {method} {route}: {response.status_code}")
                    
                except Exception as e:
                    print(f"   ❌ {method} {route}: Error - {str(e)}")
            
            print(f"\n✅ ROUTE ACCESSIBILITY TEST COMPLETED!")
            
    except Exception as e:
        print(f"❌ Accessibility test failed: {str(e)}")

if __name__ == "__main__":
    success = test_flask_routes()
    if success:
        test_route_accessibility()
        print(f"\n🎯 SUMMARY:")
        print(f"✅ Flask application loads without errors")
        print(f"✅ No duplicate route endpoints")
        print(f"✅ Warehouse packing routes registered")
        print(f"✅ Dispatch order conflict resolved")
        print(f"\n🚀 READY FOR TESTING!")
    else:
        print(f"\n❌ TESTS FAILED - Please check the errors above")
