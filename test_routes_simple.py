#!/usr/bin/env python3
"""
Simple route testing script
"""

import requests
import time

def test_routes():
    """Test key routes"""
    base_url = "http://127.0.0.1:5000"
    
    # Wait for server to start
    print("⏳ Waiting for server to start...")
    time.sleep(3)
    
    # Test routes
    routes_to_test = [
        "/",
        "/riders/",
        "/riders/dashboard",
        "/riders/tracking",
        "/riders/performance",
        "/riders/analytics",
        "/riders/reports"
    ]
    
    print("🧪 Testing Routes...")
    print("=" * 50)
    
    for route in routes_to_test:
        try:
            response = requests.get(f"{base_url}{route}", timeout=5)
            status = response.status_code
            
            if status == 200:
                print(f"✅ {route} - HTTP {status} (OK)")
            elif status == 302:
                print(f"🔄 {route} - HTTP {status} (Redirect - likely login required)")
            elif status == 404:
                print(f"❌ {route} - HTTP {status} (Not Found)")
            else:
                print(f"⚠️ {route} - HTTP {status}")
                
        except requests.exceptions.ConnectionError:
            print(f"❌ {route} - Connection Error (Server not running?)")
        except requests.exceptions.Timeout:
            print(f"❌ {route} - Timeout")
        except Exception as e:
            print(f"❌ {route} - Error: {e}")

if __name__ == "__main__":
    test_routes()
