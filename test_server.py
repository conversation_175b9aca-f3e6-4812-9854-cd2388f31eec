#!/usr/bin/env python3
"""
Test if the server is running
"""

import requests
import time

def test_server():
    urls_to_test = [
        'http://127.0.0.1:5000',
        'http://localhost:5000'
    ]
    
    for url in urls_to_test:
        try:
            print(f"Testing {url}...")
            response = requests.get(url, timeout=5)
            print(f"✅ Server is running! Status: {response.status_code}")
            print(f"Response length: {len(response.text)} characters")
            if response.status_code == 200:
                print("🎉 APPLICATION IS WORKING!")
                return True
        except requests.exceptions.ConnectionError:
            print(f"❌ Connection refused - server not running on {url}")
        except requests.exceptions.Timeout:
            print(f"⏰ Timeout - server may be starting up on {url}")
        except Exception as e:
            print(f"❌ Error: {e}")
    
    return False

if __name__ == "__main__":
    print("=== SERVER CONNECTIVITY TEST ===")
    
    # Test multiple times in case server is starting up
    for attempt in range(3):
        print(f"\nAttempt {attempt + 1}/3:")
        if test_server():
            break
        if attempt < 2:
            print("Waiting 5 seconds before retry...")
            time.sleep(5)
    else:
        print("\n❌ Server is not responding on any tested URL")
        print("The application may not be running or may have startup issues.")
