#!/usr/bin/env python3
"""
Test simple API endpoint
"""

import requests
import json

def test_simple_api():
    """Test the simple test API endpoint"""
    try:
        url = 'http://127.0.0.1:5001/api/test'
        print(f'🔍 Testing simple API endpoint: {url}')
        
        response = requests.get(url, timeout=10)
        print(f'📡 Status Code: {response.status_code}')
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f'✅ Success: {data.get("success", False)}')
                print(f'📊 Message: {data.get("message", "N/A")}')
                print(f'📊 Timestamp: {data.get("timestamp", "N/A")}')
                print('✅ Simple API is working correctly!')
            except json.JSONDecodeError:
                print('❌ Response is not JSON')
                print(f'Response content: {response.text[:200]}')
        else:
            print(f'❌ HTTP Error: {response.status_code}')
            print(f'Response: {response.text[:200]}')
            
    except Exception as e:
        print(f'❌ Connection Error: {e}')

if __name__ == "__main__":
    test_simple_api()
