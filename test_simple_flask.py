#!/usr/bin/env python3
"""
Simple Flask test to isolate the issue
"""

print("🧪 Starting simple Flask test...")

try:
    # Test 1: Basic Flask import
    print("📦 Test 1: Flask import...")
    from flask import Flask
    print("✅ Flask imported successfully")
    
    # Test 2: Create simple app
    print("📦 Test 2: Creating Flask app...")
    app = Flask(__name__)
    app.secret_key = 'test'
    print("✅ Flask app created successfully")
    
    # Test 3: Test simple route
    print("📦 Test 3: Adding simple route...")
    @app.route('/')
    def home():
        return "Hello World"
    print("✅ Simple route added successfully")
    
    # Test 4: Test blueprint import
    print("📦 Test 4: Testing blueprint import...")
    from flask import Blueprint
    test_bp = Blueprint('test', __name__, url_prefix='/test')
    
    @test_bp.route('/hello')
    def test_hello():
        return "Test Blueprint"
    
    app.register_blueprint(test_bp)
    print("✅ Blueprint test successful")
    
    # Test 5: Check routes
    print("📦 Test 5: Checking registered routes...")
    with app.app_context():
        for rule in app.url_map.iter_rules():
            print(f"  🛣️ {rule.rule} -> {rule.endpoint}")
    
    print("✅ All tests passed!")
    
    # Test 6: Try to start server briefly
    print("📦 Test 6: Testing server startup...")
    print("🚀 Starting Flask server on port 5001...")
    app.run(debug=True, port=5001, host='127.0.0.1', use_reloader=False)
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
