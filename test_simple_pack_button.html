<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Pack Button Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>🧪 Simple Pack Button Test</h1>
        <p>Testing the rebuilt pack button system</p>
        
        <div class="card">
            <div class="card-header">
                <h5>Test Pack Button</h5>
            </div>
            <div class="card-body">
                
                <!-- REBUILT PACK BUTTON -->
                <button class="btn btn-success btn-sm" 
                        onclick="openPackModal('ORD00000155')" 
                        id="pack-btn-ORD00000155"
                        data-order="ORD00000155">
                    <i class="fas fa-box"></i> Mark Packed
                </button>
                
                <p class="mt-3">Click the button above to test the pack modal.</p>
                
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5>Console Output</h5>
            </div>
            <div class="card-body">
                <div id="console-output" style="background: #000; color: #0f0; padding: 10px; font-family: monospace; height: 200px; overflow-y: auto;">
                    Console messages will appear here...
                </div>
            </div>
        </div>
    </div>

    <!-- Pack Order Modal -->
    <div class="modal fade" id="packOrderModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Pack Order</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="packOrderForm">
                        <input type="hidden" id="packOrderId" name="order_id">
                        <div class="form-group">
                            <label for="packedBy">Packed By:</label>
                            <input type="text" class="form-control" id="packedBy" name="packed_by" value="test_user" required>
                        </div>
                        <div class="form-group">
                            <label for="packingNotes">Packing Notes:</label>
                            <textarea class="form-control" id="packingNotes" name="packing_notes" rows="3" placeholder="Any special notes about packing..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-success" onclick="confirmPackOrder()">
                        <i class="fas fa-box"></i> Mark as Packed
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Console output function
        function logToConsole(message) {
            console.log(message);
            const output = document.getElementById('console-output');
            output.innerHTML += message + '\n';
            output.scrollTop = output.scrollHeight;
        }

        // COMPLETELY REBUILT PACK BUTTON SYSTEM - SIMPLE AND CLEAN
        function openPackModal(orderId) {
            logToConsole('📦 openPackModal called with: ' + orderId);
            
            // Simple validation
            if (!orderId) {
                alert('❌ Order ID is required');
                return;
            }
            
            // Set order ID in hidden input
            document.getElementById('packOrderId').value = orderId;
            
            // Show modal using Bootstrap
            $('#packOrderModal').modal('show');
            
            logToConsole('✅ Pack modal opened for order: ' + orderId);
        }

        // REBUILT PACK ORDER CONFIRMATION - SIMPLE AND RELIABLE
        function confirmPackOrder() {
            logToConsole('📦 confirmPackOrder called');
            
            // Get form data
            const orderId = document.getElementById('packOrderId').value;
            const packedBy = document.getElementById('packedBy').value;
            const packingNotes = document.getElementById('packingNotes').value;
            
            logToConsole('📦 Order ID: ' + orderId);
            logToConsole('📦 Packed By: ' + packedBy);
            logToConsole('📦 Notes: ' + packingNotes);
            
            // Validate
            if (!orderId) {
                alert('❌ Order ID is missing');
                return;
            }
            
            if (!packedBy.trim()) {
                alert('❌ Please enter who packed the order');
                return;
            }
            
            // Simulate successful submission
            logToConsole('📦 Simulating form submission...');
            
            // Hide modal
            $('#packOrderModal').modal('hide');
            
            // Show success
            alert('✅ Order packed successfully! (This is a test)');
            
            logToConsole('✅ Pack order test completed successfully');
        }

        // Initialize
        $(document).ready(function() {
            logToConsole('🚀 Simple pack button test page loaded');
            logToConsole('📋 Click the "Mark Packed" button to test');
        });
    </script>
</body>
</html>
