#!/usr/bin/env python3
"""
Simple template test without external dependencies
"""

import requests
import time

def test_template_fix():
    """Test if the template fix works"""
    base_url = "http://127.0.0.1:5000"
    
    print("🧪 Testing Template Fix...")
    print("=" * 50)
    
    try:
        # Test the main page
        print("1. Testing main page...")
        response = requests.get(base_url, timeout=10)
        
        if response.status_code == 200:
            print("✅ Main page loads successfully (HTTP 200)")
            
            # Check for URL building errors
            content = response.text.lower()
            
            if "builderror" in content:
                print("❌ Found BuildError in page content")
                return False
            elif "could not build url" in content:
                print("❌ Found URL building error in page content")
                return False
            else:
                print("✅ No URL building errors found in main page")
                
        else:
            print(f"❌ Main page failed to load (HTTP {response.status_code})")
            return False
            
        # Test the riders page
        print("\n2. Testing riders page...")
        riders_response = requests.get(f"{base_url}/riders/", timeout=10)
        
        if riders_response.status_code == 200:
            print("✅ Riders page loads successfully (HTTP 200)")
            
            # Check for errors in riders page
            riders_content = riders_response.text.lower()
            
            if "builderror" in riders_content:
                print("❌ Found BuildError in riders page")
                return False
            elif "could not build url" in riders_content:
                print("❌ Found URL building error in riders page")
                return False
            else:
                print("✅ No errors found in riders page")
                
        else:
            print(f"❌ Riders page failed to load (HTTP {riders_response.status_code})")
            return False
            
        # Test riders dashboard specifically
        print("\n3. Testing riders dashboard...")
        dashboard_response = requests.get(f"{base_url}/riders/dashboard", timeout=10)
        
        if dashboard_response.status_code == 200:
            print("✅ Riders dashboard loads successfully (HTTP 200)")
            
            dashboard_content = dashboard_response.text.lower()
            
            if "builderror" in dashboard_content:
                print("❌ Found BuildError in dashboard")
                return False
            else:
                print("✅ No errors found in dashboard")
                
        else:
            print(f"❌ Dashboard failed to load (HTTP {dashboard_response.status_code})")
            return False
            
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error - Server not running?")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_template_fix()
    if success:
        print("\n🎉 ALL TEMPLATE TESTS PASSED!")
        print("✅ The routing fix is working correctly!")
        print("✅ No BuildError exceptions found!")
        print("✅ All rider routes are accessible!")
    else:
        print("\n❌ TEMPLATE TESTS FAILED!")
