#!/usr/bin/env python3
"""
🔍 TEST SPECIFIC ERRORS SCRIPT
Re-tests the four specific errors mentioned by the user
"""

import requests
import time
import sys
import json

BASE_URL = "http://localhost:5000"

def test_error_1():
    """Test ERROR 1: JSON Serialization in Rider Performance"""
    print("🧪 ERROR 1: Testing Rider Performance submenu...")
    url = f"{BASE_URL}/riders/performance"
    
    try:
        response = requests.get(url, timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            # Check if page loads without JSON serialization errors
            if "Object of type Row is not JSON serializable" in response.text:
                print("   ❌ JSON serialization error still present")
                return False
            elif "Error" in response.text and "serializable" in response.text:
                print("   ❌ Serialization error detected")
                return False
            else:
                print("   ✅ Page loads successfully, no JSON errors detected")
                return True
        else:
            print(f"   ❌ HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_error_2():
    """Test ERROR 2: Efficiency Report Loading"""
    print("🧪 ERROR 2: Testing Efficiency Report...")
    url = f"{BASE_URL}/riders/reports?type=efficiency&date_from=2025-07-01&date_to=2025-07-31&rider="
    
    try:
        response = requests.get(url, timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            if "Error loading" in response.text or "serializable" in response.text:
                print("   ❌ Efficiency report error still present")
                return False
            else:
                print("   ✅ Efficiency report loads successfully")
                return True
        else:
            print(f"   ❌ HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_error_3():
    """Test ERROR 3: Order Assignment Dashboard"""
    print("🧪 ERROR 3: Testing Assignment Dashboard...")
    url = f"{BASE_URL}/riders/assignment-dashboard"
    
    try:
        response = requests.get(url, timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            if "strftime" in response.text or "Error loading assignment" in response.text:
                print("   ❌ Assignment dashboard error still present")
                print("   🔍 Checking for strftime error...")
                if "strftime" in response.text:
                    print("   ❌ strftime error detected")
                return False
            else:
                print("   ✅ Assignment dashboard loads successfully")
                return True
        else:
            print(f"   ❌ HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_error_4():
    """Test ERROR 4: Order Details View (test the route exists)"""
    print("🧪 ERROR 4: Testing Order Details Route...")
    # Test with a sample order ID
    test_order_id = "ORD175355078A5CED085"
    url = f"{BASE_URL}/orders/{test_order_id}"
    
    try:
        response = requests.get(url, timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Order details route works correctly")
            return True
        elif response.status_code == 404:
            print("   ⚠️  Order not found (expected for non-existent order)")
            print("   ✅ Route exists and handles 404 correctly")
            return True
        else:
            print(f"   ❌ Unexpected status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def main():
    """Main testing function"""
    print("🚀 RE-TESTING FOUR SPECIFIC ERRORS")
    print("=" * 60)
    
    # Test each error
    results = []
    
    results.append(("ERROR 1: JSON Serialization", test_error_1()))
    time.sleep(1)
    
    results.append(("ERROR 2: Efficiency Report", test_error_2()))
    time.sleep(1)
    
    results.append(("ERROR 3: Order Assignment", test_error_3()))
    time.sleep(1)
    
    results.append(("ERROR 4: Order Details", test_error_4()))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 ERROR RE-TEST RESULTS")
    print("=" * 60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for error_name, success in results:
        status = "✅ RESOLVED" if success else "❌ STILL FAILING"
        print(f"{status} - {error_name}")
    
    print(f"\n🎯 OVERALL: {passed}/{total} errors resolved ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL ERRORS RESOLVED!")
        return 0
    else:
        print("⚠️  SOME ERRORS STILL NEED FIXING")
        return 1

if __name__ == "__main__":
    sys.exit(main())
