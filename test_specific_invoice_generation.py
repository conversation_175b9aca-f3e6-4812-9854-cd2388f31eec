#!/usr/bin/env python3
"""
Test the specific invoice generation scenario from the error screenshot
"""

import requests
import json
import sqlite3
import time

def test_specific_order_invoice():
    """Test invoice generation for the specific order from the error"""
    
    print("🧾 TESTING SPECIFIC ORDER INVOICE GENERATION")
    print("=" * 60)
    
    # Check if the order exists in database
    try:
        db = sqlite3.connect('instance/medivent.db')
        db.row_factory = sqlite3.Row
        
        order = db.execute('''
            SELECT * FROM orders WHERE order_id = ?
        ''', ('ORD000000246',)).fetchone()
        
        if order:
            print(f"✅ Order found: {order['order_id']}")
            print(f"   Customer: {order['customer_name']}")
            print(f"   Amount: Rs.{order['order_amount']}")
            print(f"   Status: {order['status']}")
        else:
            print("❌ Order ORD000000246 not found in database")
            # Let's find any Finance Pending order
            order = db.execute('''
                SELECT * FROM orders WHERE status = 'Finance Pending' LIMIT 1
            ''').fetchone()
            
            if order:
                print(f"✅ Using alternative order: {order['order_id']}")
                print(f"   Customer: {order['customer_name']}")
                print(f"   Amount: Rs.{order['order_amount']}")
            else:
                print("❌ No Finance Pending orders found")
                return False
        
        db.close()
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False
    
    # Test the invoice generation API
    base_url = "http://127.0.0.1:5001"
    
    test_data = {
        "order_id": order['order_id'],
        "customer_name": order['customer_name'],
        "order_amount": float(order['order_amount']),
        "finance_user_approved": True,
        "timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ")
    }
    
    print(f"\n📤 Testing invoice generation for order: {order['order_id']}")
    print(f"📋 Request data: {json.dumps(test_data, indent=2)}")
    
    try:
        response = requests.post(
            f"{base_url}/finance/api/generate-invoice",
            json=test_data,
            headers={
                'Content-Type': 'application/json',
                'X-Finance-User-Action': 'true'
            },
            timeout=30
        )
        
        print(f"\n📥 Response Status: HTTP {response.status_code}")
        
        if response.status_code == 200:
            print("✅ SUCCESS - Invoice generation completed!")
            
            try:
                response_data = response.json()
                print(f"📋 Response data:")
                for key, value in response_data.items():
                    print(f"   {key}: {value}")
                    
                # Verify invoice was created in database
                db = sqlite3.connect('instance/medivent.db')
                invoice = db.execute('''
                    SELECT * FROM invoices WHERE order_id = ?
                ''', (order['order_id'],)).fetchone()
                
                if invoice:
                    print(f"\n✅ Invoice record created in database:")
                    print(f"   Invoice Number: {invoice[1]}")  # invoice_number
                    print(f"   Order ID: {invoice[2]}")        # order_id
                    print(f"   Generated By: {invoice[4]}")    # generated_by
                else:
                    print("\n⚠️ Invoice record not found in database")
                
                db.close()
                
            except Exception as e:
                print(f"📋 Response: {response.text}")
                print(f"⚠️ Could not parse JSON response: {e}")
                
        else:
            print(f"❌ FAILED - HTTP {response.status_code}")
            print(f"📋 Error response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"💥 Network error: {e}")
        return False
    
    return True

def test_database_integrity():
    """Test database integrity after invoice generation"""
    
    print("\n🔍 TESTING DATABASE INTEGRITY")
    print("=" * 40)
    
    try:
        db = sqlite3.connect('instance/medivent.db')
        db.row_factory = sqlite3.Row
        
        # Check invoices table
        invoices = db.execute('SELECT COUNT(*) as count FROM invoices').fetchone()
        print(f"📊 Total invoices in database: {invoices['count']}")
        
        # Check recent invoices
        recent_invoices = db.execute('''
            SELECT invoice_number, order_id, generated_by, date_generated 
            FROM invoices 
            ORDER BY id DESC 
            LIMIT 5
        ''').fetchall()
        
        print(f"📋 Recent invoices:")
        for inv in recent_invoices:
            print(f"   {inv['invoice_number']} - {inv['order_id']} - {inv['generated_by']}")
        
        # Check orders status
        orders_with_invoices = db.execute('''
            SELECT COUNT(*) as count FROM orders 
            WHERE invoice_number IS NOT NULL
        ''').fetchone()
        print(f"📊 Orders with invoices: {orders_with_invoices['count']}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Database integrity check failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 SPECIFIC INVOICE GENERATION TEST")
    print("⏰ " + time.strftime("%Y-%m-%d %H:%M:%S"))
    print()
    
    # Test specific order invoice generation
    invoice_success = test_specific_order_invoice()
    
    # Test database integrity
    db_success = test_database_integrity()
    
    # Final summary
    print("\n" + "=" * 60)
    print("🎯 TEST SUMMARY")
    print("=" * 60)
    
    if invoice_success and db_success:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Invoice generation working correctly")
        print("✅ Database integrity maintained")
        print("✅ sqlite3.Row .get() error FIXED!")
    else:
        print("⚠️ SOME TESTS FAILED")
        if not invoice_success:
            print("❌ Invoice generation failed")
        if not db_success:
            print("❌ Database integrity issues")
    
    return invoice_success and db_success

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
