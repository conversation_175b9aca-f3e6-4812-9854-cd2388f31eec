import sqlite3

# Test the specific order that's failing
order_id = 'ORD1753983391CA9E99E1'

conn = sqlite3.connect('instance/medivent.db')
conn.row_factory = sqlite3.Row
cursor = conn.cursor()

print(f"=== TESTING ORDER: {order_id} ===")

# Check if order exists
order = cursor.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
if order:
    print(f"✅ Order found: {order['customer_name']}")
    print(f"   Status: {order['status']}")
    print(f"   Sales Agent: {order['sales_agent']}")
else:
    print("❌ Order not found")
    conn.close()
    exit()

# Check order items
items = cursor.execute('SELECT * FROM order_items WHERE order_id = ?', (order_id,)).fetchall()
print(f"✅ Found {len(items)} order items")

# Test the exact invoice insert that should happen
try:
    test_invoice_number = 'TEST_INV_123'
    cursor.execute('''
        INSERT INTO invoices (invoice_number, order_id, generated_by, pdf_path, date_generated)
        VALUES (?, ?, ?, ?, ?)
    ''', (test_invoice_number, order_id, 'test_user', '/test/path.pdf', '2024-07-31T10:00:00'))
    
    print("✅ Invoice insert test successful")
    
    # Clean up
    cursor.execute("DELETE FROM invoices WHERE invoice_number = ?", (test_invoice_number,))
    conn.commit()
    
except Exception as e:
    print(f"❌ Invoice insert failed: {e}")

# Test challan insert with customer_name
try:
    test_dc_number = 'TEST_DC_123'
    cursor.execute('''
        INSERT INTO challans (dc_number, invoice_number, order_id, customer_name, generated_by, pdf_path, date_generated)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ''', (test_dc_number, 'INV123', order_id, order['customer_name'], 'test_user', '/test/challan.pdf', '2024-07-31T10:00:00'))

    print("✅ Challan insert test successful")

    # Clean up
    cursor.execute("DELETE FROM challans WHERE dc_number = ?", (test_dc_number,))
    conn.commit()

except Exception as e:
    print(f"❌ Challan insert failed: {e}")

conn.close()
