#!/usr/bin/env python3
"""
Test standalone API server
"""

import requests
import json
import time

def test_standalone_api():
    """Test the standalone API server"""
    base_url = "http://127.0.0.1:5003"
    
    print("🧪 TESTING STANDALONE API SERVER")
    print("=" * 50)
    
    # Wait a moment for server to start
    time.sleep(2)
    
    try:
        # Test basic endpoint
        print("1. Testing basic endpoint...")
        response = requests.get(f"{base_url}/test", timeout=5)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Success: {data.get('success')}")
            print("   ✅ Basic endpoint working")
        else:
            print("   ❌ Basic endpoint failed")
            return False
        
        # Test order details API
        print("\n2. Testing order details API...")
        response = requests.get(f"{base_url}/api/order-details/ORD00000155", timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Success: {data.get('success')}")
            if data.get('success'):
                print("   ✅ Order details API working")
                order = data.get('order', {})
                print(f"   Customer: {order.get('customer_name')}")
                print(f"   Amount: {order.get('order_amount')}")
            else:
                print(f"   ❌ Order details failed: {data.get('message')}")
        else:
            print(f"   ❌ Order details HTTP error: {response.status_code}")
            print(f"   Response: {response.text[:200]}")
        
        # Test QR code API
        print("\n3. Testing QR code API...")
        response = requests.get(f"{base_url}/api/order-qr-code/ORD00000155", timeout=15)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Success: {data.get('success')}")
            if data.get('success'):
                qr_info = data.get('qr_code', {})
                print(f"   ✅ QR code generated - Base64 length: {len(qr_info.get('base64', ''))}")
            else:
                print(f"   ❌ QR code failed: {data.get('message')}")
        else:
            print(f"   ❌ QR code HTTP error: {response.status_code}")
            print(f"   Response: {response.text[:200]}")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - standalone server not running")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

if __name__ == "__main__":
    success = test_standalone_api()
    if success:
        print("\n✅ Standalone API test completed")
    else:
        print("\n❌ Standalone API test failed")
