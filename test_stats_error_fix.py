#!/usr/bin/env python3
"""
Test specifically for the 'stats is undefined' error fix
"""

import requests
import re

def test_stats_error_fix():
    """Test that the stats variable is properly passed"""
    try:
        print("🧪 TESTING STATS ERROR FIX")
        print("=" * 50)
        
        url = "http://192.168.99.34:5001/products/update_selection"
        print(f"Testing URL: {url}")
        
        response = requests.get(url, timeout=10)
        
        if response.status_code != 200:
            print(f"❌ Route failed with status: {response.status_code}")
            return False
        
        content = response.text
        
        # Check for the specific error message
        if "Error loading products: 'stats' is undefined" in content:
            print("❌ STILL HAS THE STATS ERROR!")
            return False
        
        # Check for any undefined errors
        undefined_errors = re.findall(r"Error[^:]*: '[^']*' is undefined", content)
        if undefined_errors:
            print(f"❌ Found undefined errors: {undefined_errors}")
            return False
        
        # Check if stats variables are present in the HTML
        stats_checks = [
            "stats.total_products",
            "stats.in_stock", 
            "stats.low_stock",
            "stats.out_of_stock"
        ]
        
        missing_stats = []
        for stat in stats_checks:
            if stat not in content:
                missing_stats.append(stat)
        
        if missing_stats:
            print(f"⚠️ Missing stats variables in template: {missing_stats}")
        else:
            print("✅ All stats variables found in template")
        
        # Check for successful page rendering
        if "Select Product to Update" in content:
            print("✅ Page title rendered correctly")
        else:
            print("❌ Page title not found - may not be rendering correctly")
            return False
        
        # Check for products list
        if "products" in content.lower():
            print("✅ Products data appears to be present")
        else:
            print("⚠️ Products data may be missing")
        
        print("\n✅ STATS ERROR HAS BEEN FIXED!")
        print("✅ Navigation link should work correctly now")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        return False

def test_working_update_route():
    """Test the working update route for comparison"""
    try:
        print("\n🧪 TESTING WORKING UPDATE ROUTE")
        print("=" * 50)
        
        url = "http://192.168.99.34:5001/products/update/P001"
        print(f"Testing working URL: {url}")
        
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            print("✅ Individual product update route works correctly")
            return True
        else:
            print(f"❌ Individual product update route failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Working route test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 COMPREHENSIVE STATS ERROR FIX TEST")
    print("=" * 60)
    
    success1 = test_stats_error_fix()
    success2 = test_working_update_route()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 ALL TESTS PASSED!")
        print("✅ The 'stats is undefined' error has been completely resolved")
        print("✅ Navigation link works correctly")
        print("✅ Both update routes are functional")
    else:
        print("❌ SOME TESTS FAILED - Check output above")
    print("=" * 60)
