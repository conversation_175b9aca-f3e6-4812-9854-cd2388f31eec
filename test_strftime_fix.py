#!/usr/bin/env python3
"""
Test the strftime fix for assignment dashboard
"""

import requests
import sys
from datetime import datetime

def test_assignment_dashboard():
    """Test assignment dashboard for strftime errors"""
    print("🧪 TESTING ASSIGNMENT DASHBOARD STRFTIME FIX")
    print("=" * 60)
    
    try:
        # Test assignment dashboard
        response = requests.get('http://localhost:5000/riders/assignment-dashboard', timeout=15)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Assignment Dashboard loads successfully")
            
            # Check for strftime errors
            error_indicators = [
                "'str' object has no attribute 'strftime'",
                "strftime",
                "AttributeError",
                "Error loading assignment form"
            ]
            
            has_error = False
            for indicator in error_indicators:
                if indicator in response.text:
                    print(f"❌ Found error indicator: {indicator}")
                    has_error = True
            
            if not has_error:
                print("✅ No strftime errors found")
                
                # Check if datetime formatting is working
                if 'packed_at' in response.text or 'N/A' in response.text:
                    print("✅ Datetime formatting appears to be working")
                
                return True
            else:
                print("❌ Errors still present")
                return False
        else:
            print(f"❌ Assignment Dashboard returned status {response.status_code}")
            print(f"Response preview: {response.text[:500]}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing assignment dashboard: {e}")
        return False

def test_datetime_filter_directly():
    """Test the datetime filter directly"""
    print("\n🧪 TESTING DATETIME FILTER DIRECTLY")
    print("=" * 60)
    
    try:
        # Import Flask app components
        sys.path.append('.')
        from app import app
        
        with app.app_context():
            # Get the filter function
            format_datetime = app.jinja_env.filters['format_datetime']
            
            # Test various datetime string formats
            test_cases = [
                "2025-07-31 07:38:21",           # Standard format
                "2025-07-26 11:17:39.885061",    # With microseconds
                "2025-07-28",                    # Date only
                "2025-07-31 07:38",              # Without seconds
                None,                            # None value
                "",                              # Empty string
                "invalid_date"                   # Invalid format
            ]
            
            print("Testing datetime filter with various inputs:")
            for i, test_value in enumerate(test_cases):
                try:
                    result = format_datetime(test_value, '%Y-%m-%d %H:%M')
                    print(f"  Test {i+1}: '{test_value}' → '{result}' ✅")
                except Exception as e:
                    print(f"  Test {i+1}: '{test_value}' → ERROR: {e} ❌")
                    return False
            
            print("✅ All datetime filter tests passed")
            return True
            
    except Exception as e:
        print(f"❌ Error testing datetime filter: {e}")
        return False

def main():
    """Main test function"""
    print("🔧 STRFTIME ERROR FIX VERIFICATION")
    print("=" * 80)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test the filter directly first
    filter_test = test_datetime_filter_directly()
    
    # Test the assignment dashboard
    dashboard_test = test_assignment_dashboard()
    
    print("\n📊 FINAL RESULTS")
    print("=" * 40)
    print(f"Datetime Filter Test: {'✅ PASSED' if filter_test else '❌ FAILED'}")
    print(f"Assignment Dashboard Test: {'✅ PASSED' if dashboard_test else '❌ FAILED'}")
    
    if filter_test and dashboard_test:
        print("\n🎉 STRFTIME ERROR SUCCESSFULLY FIXED!")
        return 0
    else:
        print("\n⚠️ Some tests failed. Please review the errors above.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
