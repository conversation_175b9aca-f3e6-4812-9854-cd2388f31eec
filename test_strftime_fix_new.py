#!/usr/bin/env python3
"""
Test to verify strftime fix works
"""

import sys
import os
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_template_filter():
    """Test the format_datetime filter directly"""
    
    print("🧪 Testing format_datetime filter")
    print("=" * 40)
    
    try:
        # Import the Flask app
        from app import app
        
        with app.app_context():
            # Get the format_datetime filter
            format_datetime = app.jinja_env.filters.get('format_datetime')
            
            if not format_datetime:
                print("❌ format_datetime filter not found")
                return False
            
            # Test with datetime object
            test_dt = datetime.now()
            result1 = format_datetime(test_dt, '%d-%m-%Y / %H:%M:%S')
            print(f"✅ DateTime object test: {result1}")
            
            # Test with string
            test_str = "2025-07-31 10:30:45"
            result2 = format_datetime(test_str, '%d %b %Y')
            print(f"✅ String test: {result2}")
            
            # Test with None
            result3 = format_datetime(None, '%d-%m-%Y')
            print(f"✅ None test: {result3}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error testing filter: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_template_rendering():
    """Test template rendering"""
    
    print("\n🎨 Testing Template Rendering")
    print("=" * 40)
    
    try:
        from app import app
        from flask import render_template_string
        
        with app.app_context():
            # Test template with our fixed syntax
            template = """
            <div>
                <p>Now: {{ now | format_datetime('%d-%m-%Y / %H:%M:%S') }}</p>
                <p>Date: {{ test_date | format_datetime('%d %b %Y') if test_date else 'N/A' }}</p>
            </div>
            """
            
            result = render_template_string(
                template, 
                now=datetime.now(),
                test_date="2025-07-31 10:30:45"
            )
            
            print("✅ Template rendered successfully:")
            print(result.strip())
            
            # Check if the filter worked (no filter names should appear in output)
            if "format_datetime" in result:
                print("❌ Filter not applied - filter name appears in output")
                return False
            
            return True
            
    except Exception as e:
        print(f"❌ Template rendering error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    
    print("🚀 strftime Fix Test")
    print("=" * 50)
    print(f"⏰ Started at: {datetime.now()}")
    print()
    
    # Test 1: Filter functionality
    filter_test = test_template_filter()
    
    # Test 2: Template rendering
    template_test = test_template_rendering()
    
    print("\n📊 Results")
    print("=" * 30)
    print(f"Filter Test: {'✅ PASS' if filter_test else '❌ FAIL'}")
    print(f"Template Test: {'✅ PASS' if template_test else '❌ FAIL'}")
    
    overall = filter_test and template_test
    print(f"\n🎯 Overall: {'✅ SUCCESS' if overall else '❌ FAILED'}")
    
    if overall:
        print("\n🎉 The strftime fix is working!")
        print("💡 Invoice templates should now render without errors.")
    
    return overall

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
