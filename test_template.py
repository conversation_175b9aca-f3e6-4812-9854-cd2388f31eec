#!/usr/bin/env python3

"""
Simple test to verify the partial pending template works
"""

from flask import Flask, render_template

app = Flask(__name__)

@app.route('/test')
def test_partial_pending():
    """Test route for partial pending template"""
    try:
        # Sample data for testing
        stats = {
            'total_partial_orders': 3,
            'total_pending_items': 6,
            'total_pending_quantity': 155,
            'total_pending_value': 3875.00
        }
        
        partial_orders = [
            {
                'order_id': 'ORD000001',
                'order_date': '2024-01-15',
                'customer_name': 'Test Customer 1',
                'customer_phone': '9876543210',
                'pending_items_count': 2,
                'total_pending_quantity': 60,
                'max_priority': 4,
                'estimated_pending_value': 1500.00,
                'dc_numbers': 'DC-001'
            },
            {
                'order_id': 'ORD000002',
                'order_date': '2024-01-16',
                'customer_name': 'Direct Database Test Customer',
                'customer_phone': '9876543211',
                'pending_items_count': 2,
                'total_pending_quantity': 45,
                'max_priority': 3,
                'estimated_pending_value': 1125.00,
                'dc_numbers': None
            }
        ]
        
        top_pending_products = [
            {
                'product_name': 'Paracetamol 500mg',
                'strength': '500mg',
                'total_pending': 90,
                'orders_affected': 2,
                'available_stock': 120,
                'product_id': 'PROD001'
            },
            {
                'product_name': 'Amoxicillin 250mg',
                'strength': '250mg',
                'total_pending': 30,
                'orders_affected': 1,
                'available_stock': 0,
                'product_id': 'PROD002'
            }
        ]
        
        return render_template('partial_pending/index_minimal.html',
                             stats=stats,
                             partial_orders=partial_orders,
                             top_pending_products=top_pending_products)
    except Exception as e:
        return f"Template Error: {str(e)}"

if __name__ == '__main__':
    print("Testing partial pending template...")
    with app.test_client() as client:
        response = client.get('/test')
        if response.status_code == 200:
            print("✓ Template renders successfully!")
            print(f"✓ Response length: {len(response.data)} bytes")
            
            # Save the rendered HTML for inspection
            with open('rendered_test.html', 'wb') as f:
                f.write(response.data)
            print("✓ Rendered HTML saved to 'rendered_test.html'")
        else:
            print(f"✗ Template failed with status code: {response.status_code}")
            print(f"✗ Error: {response.data.decode()}")
