#!/usr/bin/env python3
"""
Test template content to verify card names and currency removal
"""

def test_template_content():
    """Test the finance dashboard template content"""
    print("🔍 TESTING TEMPLATE CONTENT")
    print("=" * 50)
    
    template_path = 'templates/finance/modern_dashboard.html'
    
    try:
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📄 Template file size: {len(content)} characters")
        
        # Test for card names
        card_names = [
            'TOTAL REVENUE',
            'MONTHLY REVENUE', 
            'PENDING PAYMENTS',
            'PROFIT MARGIN',
            'ACTIVE CUSTOMERS',
            'DAILY AVERAGE',
            'SUCCESS RATE'
        ]
        
        print("\n🏷️ CHECKING CARD NAMES:")
        found_cards = 0
        for card_name in card_names:
            if card_name in content:
                print(f"   ✅ {card_name} - Found")
                found_cards += 1
            else:
                print(f"   ❌ {card_name} - Missing")
        
        print(f"\n📊 Card Names Summary: {found_cards}/{len(card_names)} found")
        
        # Test for currency symbols
        print("\n💰 CHECKING CURRENCY SYMBOLS:")
        currency_count = content.count('₹')
        if currency_count == 0:
            print("   ✅ No currency symbols found")
        else:
            print(f"   ❌ Found {currency_count} currency symbols")
            
            # Show where they are
            lines = content.split('\n')
            for i, line in enumerate(lines, 1):
                if '₹' in line:
                    print(f"      Line {i}: {line.strip()}")
        
        # Test for stat-card-name class
        print("\n🎨 CHECKING CSS CLASSES:")
        if 'stat-card-name' in content:
            stat_card_count = content.count('stat-card-name')
            print(f"   ✅ stat-card-name class found {stat_card_count} times")
        else:
            print("   ❌ stat-card-name class not found")
        
        # Test for ASP-related content
        print("\n📈 CHECKING ASP REFERENCES:")
        asp_terms = ['asp', 'ASP', 'Average Selling Price']
        for term in asp_terms:
            if term in content:
                print(f"   ✅ Found '{term}' in template")
            else:
                print(f"   ❌ '{term}' not found in template")
        
        print("\n✅ Template content analysis complete!")
        return True
        
    except Exception as e:
        print(f"❌ Error reading template: {e}")
        return False

if __name__ == "__main__":
    test_template_content()
