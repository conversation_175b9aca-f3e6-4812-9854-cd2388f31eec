#!/usr/bin/env python3
"""
Test template rendering to verify the fix
"""

import requests
from bs4 import BeautifulSoup
import time

def test_template_fix():
    """Test if the template fix works"""
    base_url = "http://127.0.0.1:5000"
    
    print("🧪 Testing Template Fix...")
    print("=" * 50)
    
    try:
        # Test the main page to see if it renders without errors
        response = requests.get(base_url, timeout=10)
        
        if response.status_code == 200:
            print("✅ Main page loads successfully (HTTP 200)")
            
            # Parse HTML to check for navigation links
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Look for rider navigation links
            rider_links = soup.find_all('a', href=lambda x: x and '/riders' in x)
            
            if rider_links:
                print(f"✅ Found {len(rider_links)} rider navigation links:")
                for link in rider_links[:5]:  # Show first 5
                    href = link.get('href', '')
                    text = link.get_text(strip=True)
                    print(f"   - {text}: {href}")
            else:
                print("⚠️ No rider navigation links found")
            
            # Check if there are any obvious errors in the page
            if "BuildError" in response.text:
                print("❌ Found BuildError in page content")
                return False
            elif "Could not build url" in response.text:
                print("❌ Found URL building error in page content")
                return False
            else:
                print("✅ No URL building errors found in page content")
                
        else:
            print(f"❌ Main page failed to load (HTTP {response.status_code})")
            return False
            
        # Test the riders page specifically
        print("\n🧪 Testing Riders Page...")
        riders_response = requests.get(f"{base_url}/riders/", timeout=10)
        
        if riders_response.status_code == 200:
            print("✅ Riders page loads successfully (HTTP 200)")
            
            # Check for any errors in riders page
            if "BuildError" in riders_response.text:
                print("❌ Found BuildError in riders page")
                return False
            else:
                print("✅ No errors found in riders page")
                
        else:
            print(f"❌ Riders page failed to load (HTTP {riders_response.status_code})")
            return False
            
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error - Server not running?")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_template_fix()
    if success:
        print("\n🎉 TEMPLATE FIX VERIFICATION PASSED!")
    else:
        print("\n❌ TEMPLATE FIX VERIFICATION FAILED!")
