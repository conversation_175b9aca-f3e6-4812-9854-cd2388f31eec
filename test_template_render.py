#!/usr/bin/env python3
"""
Test script to verify template rendering without errors
"""

import sys
import os
from flask import Flask, render_template

# Add current directory to path
sys.path.append('.')

def test_template_rendering():
    """Test if the finance dashboard template can render without errors"""
    
    print("🔍 TEMPLATE RENDERING TEST")
    print("=" * 50)
    
    try:
        # Create a minimal Flask app
        app = Flask(__name__)
        app.config['TEMPLATES_AUTO_RELOAD'] = True
        
        with app.app_context():
            # Create mock stats data with all required fields
            mock_stats = {
                'total_revenue': 150000,
                'pending_amount': 45000,
                'collected_today': 12000,
                'orders_count': 125,
                'pending_orders': 23,
                'completed_orders': 102,
                'hold_count': 5,  # This is the field causing the error
                'overdue_count': 8,
                'collection_rate': 85.5,
                'avg_order_value': 1200,
                'top_customers': [
                    {'name': 'Customer A', 'amount': 15000},
                    {'name': 'Customer B', 'amount': 12000}
                ],
                'recent_payments': [
                    {'customer': 'Customer C', 'amount': 5000, 'date': '2025-08-04'},
                    {'customer': 'Customer D', 'amount': 3000, 'date': '2025-08-04'}
                ]
            }
            
            print("✅ Mock stats data created")
            print(f"📊 Stats keys: {list(mock_stats.keys())}")
            print(f"📊 hold_count value: {mock_stats['hold_count']}")
            
            # Try to render the template
            print("\n🎨 Attempting to render template...")
            
            try:
                rendered_html = render_template('finance/modern_dashboard.html', 
                                              stats=mock_stats,
                                              title="Finance Dashboard Test")
                
                print("✅ Template rendered successfully!")
                print(f"📏 Rendered HTML length: {len(rendered_html)} characters")
                
                # Check if hold_count is properly used in the template
                if 'hold_count' in rendered_html:
                    print("✅ hold_count found in rendered HTML")
                else:
                    print("⚠️ hold_count not found in rendered HTML")
                
                # Check for any obvious errors in the rendered HTML
                if 'error' in rendered_html.lower() or 'exception' in rendered_html.lower():
                    print("⚠️ Potential errors found in rendered HTML")
                else:
                    print("✅ No obvious errors in rendered HTML")
                
                return True
                
            except Exception as template_error:
                print(f"❌ Template rendering failed: {template_error}")
                import traceback
                traceback.print_exc()
                return False
                
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_template_rendering()
    if success:
        print("\n🎉 Template test PASSED!")
    else:
        print("\n💥 Template test FAILED!")
