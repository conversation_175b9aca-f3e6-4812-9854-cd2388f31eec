#!/usr/bin/env python3
"""
Direct test of the transaction fix for inventory deduction
"""

import sqlite3
import sys
from datetime import datetime
from utils.inventory_validator import InventoryValidator

def test_transaction_fix():
    """Test the transaction fix directly"""
    
    print("🧪 Testing Transaction Fix for Inventory Deduction")
    print("=" * 55)
    
    try:
        # Connect to database
        db = sqlite3.connect('instance/medivent.db')
        db.row_factory = sqlite3.Row
        
        # Check if we have products and inventory
        print("📋 Step 1: Checking available products and inventory...")
        
        products = db.execute('''
            SELECT p.product_id, p.name, 
                   COUNT(i.inventory_id) as inventory_count,
                   SUM(i.stock_quantity - COALESCE(i.allocated_quantity, 0)) as available_stock
            FROM products p
            LEFT JOIN inventory i ON p.product_id = i.product_id AND i.status = 'active'
            GROUP BY p.product_id, p.name
            HAVING available_stock > 0
            LIMIT 3
        ''').fetchall()
        
        if not products:
            print("❌ No products with available inventory found")
            return False
        
        print("✅ Found products with inventory:")
        for product in products:
            print(f"   - {product['product_id']}: {product['name']} (Available: {product['available_stock']})")
        
        # Test the inventory validator
        print("\n🔧 Step 2: Testing inventory validator...")
        
        test_product = products[0]
        product_id = test_product['product_id']
        test_quantity = min(1, int(test_product['available_stock']))
        
        print(f"Testing with product: {product_id}, quantity: {test_quantity}")
        
        # Create inventory validator
        validator = InventoryValidator(db)
        
        # Test 1: Validate stock deduction
        print("\n🔍 Test 1: Validating stock deduction...")
        is_valid, message, allocation_plan = validator.validate_stock_deduction(product_id, test_quantity, 0)
        
        if is_valid:
            print(f"✅ Validation successful: {message}")
            print(f"   Allocation plan: {len(allocation_plan)} batches")
        else:
            print(f"❌ Validation failed: {message}")
            return False
        
        # Test 2: Execute stock deduction WITHOUT transaction (simulating order creation)
        print("\n🔄 Test 2: Testing stock deduction within existing transaction...")
        
        # Start a transaction (simulating order creation)
        db.execute('BEGIN TRANSACTION')
        
        try:
            # Execute stock deduction with use_transaction=False
            success, deduction_message = validator.execute_stock_deduction(
                product_id, test_quantity, 0, "TEST_ORDER_001", "test_user", use_transaction=False
            )
            
            if success:
                print(f"✅ Stock deduction successful: {deduction_message}")
                
                # Verify the allocation was recorded
                allocated = db.execute('''
                    SELECT SUM(allocated_quantity) as total_allocated
                    FROM inventory 
                    WHERE product_id = ? AND status = 'active'
                ''', (product_id,)).fetchone()
                
                print(f"   Total allocated quantity: {allocated['total_allocated']}")
                
                # Rollback to not affect actual data
                db.execute('ROLLBACK')
                print("✅ Transaction rolled back (test mode)")
                
            else:
                print(f"❌ Stock deduction failed: {deduction_message}")
                db.execute('ROLLBACK')
                return False
                
        except Exception as e:
            print(f"❌ Error during stock deduction: {str(e)}")
            db.execute('ROLLBACK')
            return False
        
        # Test 3: Execute stock deduction WITH transaction (standalone mode)
        print("\n🔄 Test 3: Testing stock deduction with its own transaction...")
        
        try:
            success, deduction_message = validator.execute_stock_deduction(
                product_id, test_quantity, 0, "TEST_ORDER_002", "test_user", use_transaction=True
            )
            
            if success:
                print(f"✅ Standalone stock deduction successful: {deduction_message}")
                
                # Rollback the allocation for testing
                db.execute('BEGIN TRANSACTION')
                db.execute('''
                    UPDATE inventory 
                    SET allocated_quantity = allocated_quantity - ?
                    WHERE product_id = ? AND status = 'active'
                ''', (test_quantity, product_id))
                db.execute('COMMIT')
                print("✅ Test allocation cleaned up")
                
            else:
                print(f"❌ Standalone stock deduction failed: {deduction_message}")
                return False
                
        except Exception as e:
            print(f"❌ Error during standalone stock deduction: {str(e)}")
            return False
        
        print("\n🎉 All transaction tests PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Test error: {str(e)}")
        return False
    finally:
        if 'db' in locals():
            db.close()

def main():
    """Main test function"""
    
    print(f"🕒 Test started at: {datetime.now()}")
    print()
    
    success = test_transaction_fix()
    
    print("\n" + "=" * 55)
    print("📋 Test Results Summary")
    print("=" * 55)
    
    if success:
        print("✅ Transaction fix test: PASSED")
        print("\n🎉 The 'cannot start a transaction within a transaction' error is FIXED!")
        print("💡 Order creation should now work correctly.")
    else:
        print("❌ Transaction fix test: FAILED")
        print("\n⚠️ The transaction issue may still exist.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
