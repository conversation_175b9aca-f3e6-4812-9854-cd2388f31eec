import urllib.request
import urllib.error

try:
    with urllib.request.urlopen('http://127.0.0.1:5001/orders/ORD175397491316416F32/history') as response:
        content = response.read().decode('utf-8')
        print(f'Status: {response.status}')
        print(f'Content length: {len(content)}')
        
        # Check for key elements
        if 'Order Details' in content:
            print('✅ Order Details found')
        else:
            print('❌ Order Details not found')
            
        if 'Axinix' in content:
            print('✅ Product name found')
        else:
            print('❌ Product name not found')
            
        if 'Rejected' in content:
            print('✅ Status found')
        else:
            print('❌ Status not found')
            
        # Show first 500 chars
        print('\nFirst 500 characters:')
        print(content[:500])
        
except urllib.error.HTTPError as e:
    print(f'HTTP Error: {e.code} - {e.reason}')
except Exception as e:
    print(f'Error: {e}')
