#!/usr/bin/env python3
"""
Comprehensive test script for warehouse packing dashboard fixes
Tests all button functionality, API endpoints, and address display
"""

import requests
import json
import time
from datetime import datetime

def test_api_endpoint(url, description):
    """Test an API endpoint and return results"""
    try:
        print(f"\n🧪 Testing: {description}")
        print(f"   URL: {url}")
        
        response = requests.get(url, timeout=15)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                success = data.get('success', False)
                print(f"   Success: {success}")
                
                if success:
                    return True, data
                else:
                    error_msg = data.get('message', 'Unknown error')
                    print(f"   ❌ API Error: {error_msg}")
                    return False, data
                    
            except json.JSONDecodeError as e:
                print(f"   ❌ JSON Error: {e}")
                print(f"   Raw response: {response.text[:200]}")
                return False, None
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            print(f"   Response: {response.text[:200]}")
            return False, None
            
    except Exception as e:
        print(f"   ❌ Connection Error: {e}")
        return False, None

def test_order_details_api():
    """Test order details API with address field mapping"""
    print("\n" + "="*60)
    print("🔍 TESTING ORDER DETAILS API")
    print("="*60)
    
    test_orders = ['ORD00000155', 'ORD00000157', 'ORD00000150']
    
    for order_id in test_orders:
        url = f"http://127.0.0.1:5001/api/order-details/{order_id}"
        success, data = test_api_endpoint(url, f"Order Details for {order_id}")
        
        if success and data:
            order = data.get('order', {})
            
            # Check address fields
            customer_address = order.get('customer_address', '')
            delivery_address = order.get('delivery_address', '')
            shipping_address = order.get('shipping_address', '')
            
            print(f"   📍 Address Fields:")
            print(f"      Customer Address: {customer_address or 'MISSING'}")
            print(f"      Delivery Address: {delivery_address or 'MISSING'}")
            print(f"      Shipping Address: {shipping_address or 'MISSING'}")
            
            # Check other important fields
            customer_name = order.get('customer_name', '')
            customer_phone = order.get('customer_phone', '')
            customer_city = order.get('customer_city', '')
            
            print(f"   👤 Customer Info:")
            print(f"      Name: {customer_name or 'MISSING'}")
            print(f"      Phone: {customer_phone or 'MISSING'}")
            print(f"      City: {customer_city or 'MISSING'}")
            
            # Check order items
            order_items = data.get('order_items', [])
            print(f"   📦 Order Items: {len(order_items)} items")
            
            if customer_address or delivery_address or shipping_address:
                print(f"   ✅ Address data available")
            else:
                print(f"   ❌ NO ADDRESS DATA FOUND")
        
        print("-" * 40)

def test_qr_code_api():
    """Test QR code generation API"""
    print("\n" + "="*60)
    print("📱 TESTING QR CODE API")
    print("="*60)
    
    test_orders = ['ORD00000155', 'ORD00000157', 'ORD00000150']
    
    for order_id in test_orders:
        url = f"http://127.0.0.1:5001/api/order-qr-code/{order_id}?branding=true"
        success, data = test_api_endpoint(url, f"QR Code for {order_id}")
        
        if success and data:
            qr_data = data.get('qr_code', {})
            base64_data = qr_data.get('base64', '')
            
            if base64_data:
                print(f"   ✅ QR Code generated: {len(base64_data)} characters")
                print(f"   📊 QR Data: {qr_data.get('data', 'N/A')[:50]}...")
            else:
                print(f"   ❌ No QR code base64 data")
        
        print("-" * 40)

def test_print_address_route():
    """Test print address route"""
    print("\n" + "="*60)
    print("🖨️ TESTING PRINT ADDRESS ROUTES")
    print("="*60)
    
    test_orders = ['ORD00000155', 'ORD00000157', 'ORD00000150']
    
    for order_id in test_orders:
        url = f"http://127.0.0.1:5001/orders/{order_id}/print-address"
        
        try:
            print(f"\n🧪 Testing: Print Address for {order_id}")
            print(f"   URL: {url}")
            
            response = requests.get(url, timeout=15)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                content = response.text
                if 'address_label.html' in content or 'ORDER:' in content:
                    print(f"   ✅ Address label page loaded")
                    
                    # Check for QR code loading script
                    if 'loadQRCode' in content:
                        print(f"   ✅ QR code loading script present")
                    else:
                        print(f"   ❌ QR code loading script missing")
                        
                else:
                    print(f"   ❌ Unexpected content")
                    print(f"   Content preview: {content[:200]}")
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Connection Error: {e}")
        
        print("-" * 40)

def test_warehouse_routes():
    """Test warehouse packing routes"""
    print("\n" + "="*60)
    print("🏭 TESTING WAREHOUSE ROUTES")
    print("="*60)
    
    routes_to_test = [
        ("http://127.0.0.1:5001/warehouse/packing", "Warehouse Packing Dashboard"),
        ("http://127.0.0.1:5001/warehouse/orders", "Warehouse Orders"),
    ]
    
    for url, description in routes_to_test:
        try:
            print(f"\n🧪 Testing: {description}")
            print(f"   URL: {url}")
            
            response = requests.get(url, timeout=15)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                content = response.text
                
                # Check for key elements
                if 'printAddress' in content:
                    print(f"   ✅ printAddress function present")
                else:
                    print(f"   ❌ printAddress function missing")
                    
                if 'packOrder' in content:
                    print(f"   ✅ packOrder function present")
                else:
                    print(f"   ❌ packOrder function missing")
                    
                if 'dispatchOrder' in content:
                    print(f"   ✅ dispatchOrder function present")
                else:
                    print(f"   ❌ dispatchOrder function missing")
                    
                if 'loadQRCodeFromAPI' in content:
                    print(f"   ✅ QR code loading function present")
                else:
                    print(f"   ❌ QR code loading function missing")
                    
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Connection Error: {e}")
        
        print("-" * 40)

def main():
    """Main test function"""
    print("🚀 WAREHOUSE PACKING DASHBOARD COMPREHENSIVE TEST")
    print("=" * 80)
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Wait for server to be ready
    print("\n⏳ Waiting for server to be ready...")
    time.sleep(3)
    
    # Run all tests
    test_order_details_api()
    test_qr_code_api()
    test_print_address_route()
    test_warehouse_routes()
    
    print("\n" + "="*80)
    print("🏁 TEST COMPLETED")
    print(f"⏰ Test finished at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)

if __name__ == "__main__":
    main()
