#!/usr/bin/env python3
"""
Test Warehouse Routes - Comprehensive Testing
"""

import sys
import requests
import json
from datetime import datetime

def test_route(url, description, expected_status=200):
    """Test a single route"""
    try:
        print(f"\n🔍 Testing: {description}")
        print(f"   URL: {url}")
        
        response = requests.get(url, timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == expected_status:
            print(f"   ✅ SUCCESS")
            
            # Try to parse JSON if it's an API endpoint
            if '/api/' in url or '/details' in url:
                try:
                    data = response.json()
                    print(f"   📊 JSON Response: {data.get('success', 'N/A')}")
                    if 'order' in data:
                        print(f"   📋 Order ID: {data['order'].get('order_id', 'N/A')}")
                    if 'items' in data or 'order_items' in data:
                        items = data.get('items', data.get('order_items', []))
                        print(f"   📦 Items Count: {len(items)}")
                except:
                    print(f"   📄 HTML Response (length: {len(response.text)})")
            
            return True
        else:
            print(f"   ❌ FAILED - Expected {expected_status}, got {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"   ❌ CONNECTION ERROR - Server not running?")
        return False
    except Exception as e:
        print(f"   ❌ ERROR: {e}")
        return False

def main():
    """Main testing function"""
    print("🧪 WAREHOUSE ROUTES TESTING")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5001"
    
    # Test routes
    routes_to_test = [
        # Main warehouse routes
        (f"{base_url}/warehouse/packing", "Warehouse Packing Dashboard"),
        (f"{base_url}/warehouse/orders", "Warehouse Orders"),
        
        # API routes
        (f"{base_url}/api/order-details/ORD00000157", "API Order Details"),
        (f"{base_url}/orders/ORD00000157/details", "Fallback Order Details"),
        
        # Other warehouse routes
        (f"{base_url}/warehouse/pack_order", "Pack Order", 405),  # POST only
        (f"{base_url}/warehouse/dispatch-order", "Dispatch Order", 405),  # POST only
        (f"{base_url}/orders/ORD00000157/print-address", "Print Address"),
    ]
    
    results = []
    
    for url, description, *expected in routes_to_test:
        expected_status = expected[0] if expected else 200
        success = test_route(url, description, expected_status)
        results.append((description, success))
    
    # Summary
    print(f"\n📊 TESTING SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for description, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {status} {description}")
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
    else:
        print("⚠️  Some tests failed - check server and routes")

if __name__ == "__main__":
    main()
