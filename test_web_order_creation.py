#!/usr/bin/env python3
"""
Web Order Creation Testing - Test actual order creation through Flask app
"""

import requests
import time
import json
from concurrent.futures import ThreadPoolExecutor
import sqlite3

def test_server_availability():
    """Test if the Flask server is running"""
    print("🧪 Testing Server Availability...")
    
    try:
        response = requests.get('http://localhost:5000', timeout=5)
        print(f"  ✅ Server responding with status: {response.status_code}")
        return True
    except requests.exceptions.ConnectionError:
        print("  ❌ Server not accessible - starting server...")
        return False
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def test_login_and_get_session():
    """Test login and get session for authenticated requests"""
    print("🧪 Testing Login and Session...")
    
    session = requests.Session()
    
    try:
        # Get login page first
        response = session.get('http://localhost:5000/auth/login')
        print(f"  ✅ Login page status: {response.status_code}")
        
        # Try to login (this might fail if we don't have credentials)
        login_data = {
            'username': 'admin',
            'password': 'admin'
        }
        
        response = session.post('http://localhost:5000/auth/login', data=login_data)
        print(f"  ✅ Login attempt status: {response.status_code}")
        
        # Check if we're redirected (successful login)
        if response.status_code == 302:
            print("  ✅ Login successful (redirected)")
            return session
        else:
            print("  ⚠️  Login may have failed, but continuing with session")
            return session
            
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return None

def test_order_creation_endpoint(session):
    """Test order creation through the web endpoint"""
    print("🧪 Testing Order Creation Endpoint...")
    
    if not session:
        print("  ❌ No session available")
        return False
    
    try:
        # Get the order creation page first
        response = session.get('http://localhost:5000/orders/new')
        print(f"  ✅ Order creation page status: {response.status_code}")
        
        # Prepare order data
        order_data = {
            'customer_name': 'Test Customer Web',
            'customer_address': 'Test Address Web',
            'customer_phone': '123456789',
            'payment_method': 'cash',
            'product_id[]': ['P001'],
            'quantity[]': ['10']
        }
        
        # Submit order
        response = session.post('http://localhost:5000/orders/new', data=order_data)
        print(f"  ✅ Order submission status: {response.status_code}")
        
        # Check response
        if response.status_code == 200:
            print("  ✅ Order creation successful")
            return True
        elif response.status_code == 302:
            print("  ✅ Order creation redirected (likely successful)")
            return True
        else:
            print(f"  ⚠️  Unexpected status: {response.status_code}")
            print(f"  Response text: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def test_multiple_concurrent_orders():
    """Test creating multiple orders concurrently through web interface"""
    print("🧪 Testing Concurrent Web Order Creation...")
    
    def create_single_order(order_num):
        session = requests.Session()
        
        try:
            # Login
            login_data = {'username': 'admin', 'password': 'admin'}
            session.post('http://localhost:5000/auth/login', data=login_data)
            
            # Create order
            order_data = {
                'customer_name': f'Concurrent Customer {order_num}',
                'customer_address': f'Address {order_num}',
                'customer_phone': f'12345678{order_num:02d}',
                'payment_method': 'cash',
                'product_id[]': ['P001'],
                'quantity[]': ['5']
            }
            
            response = session.post('http://localhost:5000/orders/new', data=order_data)
            
            return {
                'order_num': order_num,
                'status_code': response.status_code,
                'success': response.status_code in [200, 302],
                'error': None
            }
            
        except Exception as e:
            return {
                'order_num': order_num,
                'status_code': None,
                'success': False,
                'error': str(e)
            }
    
    # Run concurrent order creation
    results = []
    with ThreadPoolExecutor(max_workers=5) as executor:
        futures = [executor.submit(create_single_order, i) for i in range(10)]
        results = [future.result() for future in futures]
    
    # Analyze results
    successful = [r for r in results if r['success']]
    failed = [r for r in results if not r['success']]
    
    print(f"  ✅ Successful orders: {len(successful)}")
    print(f"  ❌ Failed orders: {len(failed)}")
    
    if failed:
        print("  Failed order details:")
        for fail in failed:
            print(f"    Order {fail['order_num']}: {fail['error'] or f'Status {fail['status_code']}'}")
    
    return len(failed) == 0

def check_database_for_unique_constraint_errors():
    """Check database for any remaining unique constraint issues"""
    print("🧪 Checking Database for UNIQUE Constraint Issues...")
    
    db_path = 'instance/medivent.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check for duplicate order IDs
        cursor.execute('''
            SELECT order_id, COUNT(*) as count 
            FROM orders 
            GROUP BY order_id 
            HAVING COUNT(*) > 1
        ''')
        duplicates = cursor.fetchall()
        
        if duplicates:
            print(f"  ❌ Found {len(duplicates)} duplicate order IDs:")
            for order_id, count in duplicates:
                print(f"    {order_id}: {count} occurrences")
            return False
        else:
            print("  ✅ No duplicate order IDs found")
        
        # Check recent orders
        cursor.execute('''
            SELECT order_id, customer_name, order_date 
            FROM orders 
            ORDER BY order_date DESC 
            LIMIT 10
        ''')
        recent_orders = cursor.fetchall()
        
        print(f"  ✅ Recent orders ({len(recent_orders)}):")
        for order_id, customer_name, order_date in recent_orders:
            print(f"    {order_id} - {customer_name} - {order_date}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def cleanup_test_orders():
    """Clean up test orders from database"""
    print("🧪 Cleaning Up Test Orders...")
    
    db_path = 'instance/medivent.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Delete test orders
        cursor.execute('''
            DELETE FROM orders 
            WHERE customer_name LIKE 'Test Customer%' 
               OR customer_name LIKE 'Concurrent Customer%'
        ''')
        deleted = cursor.rowcount
        
        conn.commit()
        conn.close()
        
        print(f"  🧹 Cleaned up {deleted} test orders")
        return True
        
    except Exception as e:
        print(f"  ❌ Cleanup error: {e}")
        return False

def main():
    """Main testing function"""
    print("🔍 WEB ORDER CREATION TESTING")
    print("=" * 40)
    
    # Wait a moment for server to start
    print("⏳ Waiting for server to start...")
    time.sleep(3)
    
    tests = [
        ("Server Availability", test_server_availability),
        ("Database Check", check_database_for_unique_constraint_errors),
    ]
    
    # Test server availability first
    server_available = test_server_availability()
    
    if server_available:
        # Get session
        session = test_login_and_get_session()
        
        # Add more tests if server is available
        tests.extend([
            ("Order Creation Endpoint", lambda: test_order_creation_endpoint(session)),
            ("Concurrent Order Creation", test_multiple_concurrent_orders),
            ("Final Database Check", check_database_for_unique_constraint_errors),
            ("Cleanup", cleanup_test_orders),
        ])
    else:
        print("⚠️  Server not available - running limited tests")
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}")
        print("-" * len(test_name))
        success = test_func()
        results.append((test_name, success))
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"Result: {status}")
    
    print("\n" + "=" * 40)
    print("📊 WEB TESTING SUMMARY")
    print("=" * 40)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name:<30} | {status}")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    if passed == total:
        print(f"\n🎉 ALL WEB TESTS PASSED ({passed}/{total})")
        print("✅ WEB ORDER CREATION IS WORKING!")
    else:
        print(f"\n⚠️  SOME TESTS FAILED ({passed}/{total})")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
