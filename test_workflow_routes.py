#!/usr/bin/env python3
"""
Test the workflow routes specifically
"""

def test_workflow_routes():
    """Test workflow routes"""
    print("🔍 TESTING WORKFLOW ROUTES")
    print("=" * 60)
    
    try:
        from app import app
        
        with app.test_client() as client:
            with app.app_context():
                # Test 1: Check if workflow route exists
                print("1️⃣ Testing workflow route accessibility...")
                
                # Get all routes
                rules = list(app.url_map.iter_rules())
                workflow_routes = [rule for rule in rules if 'workflow' in rule.rule.lower()]
                
                print(f"   Found {len(workflow_routes)} workflow routes:")
                for rule in workflow_routes:
                    print(f"   • {rule.endpoint}: {rule.rule} {list(rule.methods)}")
                
                # Test 2: Check dispatch routes
                print("\n2️⃣ Testing dispatch routes...")
                dispatch_routes = [rule for rule in rules if 'dispatch' in rule.endpoint.lower()]
                
                print(f"   Found {len(dispatch_routes)} dispatch routes:")
                for rule in dispatch_routes:
                    print(f"   • {rule.endpoint}: {rule.rule} {list(rule.methods)}")
                
                # Test 3: Test URL generation
                print("\n3️⃣ Testing URL generation...")
                
                try:
                    from flask import url_for
                    
                    # Test workflow URL
                    workflow_url = url_for('workflow')
                    print(f"   ✅ workflow URL: {workflow_url}")
                    
                    # Test dispatch URL with dummy order_id
                    dispatch_url = url_for('orders.dispatch_order', order_id='TEST123')
                    print(f"   ✅ orders.dispatch_order URL: {dispatch_url}")
                    
                    # Test deliver URL with dummy order_id
                    deliver_url = url_for('orders.deliver_order', order_id='TEST123')
                    print(f"   ✅ orders.deliver_order URL: {deliver_url}")
                    
                except Exception as e:
                    print(f"   ❌ URL generation error: {e}")
                
                # Test 4: Check if routes are from different blueprints
                print("\n4️⃣ Checking blueprint registration...")
                
                # Check workflow endpoint
                workflow_rule = None
                dispatch_rule = None
                
                for rule in rules:
                    if rule.endpoint == 'workflow' and '/orders/workflow' in rule.rule:
                        workflow_rule = rule
                    elif rule.endpoint == 'orders.dispatch_order':
                        dispatch_rule = rule
                
                if workflow_rule:
                    print(f"   ✅ workflow route: {workflow_rule.endpoint} -> {workflow_rule.rule}")
                else:
                    print(f"   ❌ workflow route not found")
                
                if dispatch_rule:
                    print(f"   ✅ dispatch route: {dispatch_rule.endpoint} -> {dispatch_rule.rule}")
                else:
                    print(f"   ❌ dispatch route not found")
                
                # Test 5: Check for blueprint conflicts
                print("\n5️⃣ Checking for blueprint conflicts...")
                
                orders_endpoints = [rule.endpoint for rule in rules if rule.endpoint.startswith('orders.')]
                app_endpoints = [rule.endpoint for rule in rules if not '.' in rule.endpoint and 'orders' in rule.rule]
                
                print(f"   📋 Orders blueprint endpoints: {len(orders_endpoints)}")
                print(f"   📋 App-level order routes: {len(app_endpoints)}")
                
                if len(app_endpoints) > 0:
                    print(f"   ⚠️ App-level routes that might conflict:")
                    for endpoint in app_endpoints[:5]:
                        matching_rule = next((rule for rule in rules if rule.endpoint == endpoint), None)
                        if matching_rule:
                            print(f"      • {endpoint}: {matching_rule.rule}")
                
                return True
                
    except Exception as e:
        print(f"❌ Error testing workflow routes: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_workflow_routes()
