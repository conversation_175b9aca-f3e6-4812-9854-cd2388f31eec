#!/usr/bin/env python3
"""
Comprehensive test suite for invoice strftime error fix
Tests all invoice-related functionality to ensure no datetime formatting errors
"""

import unittest
import sys
import os
from datetime import datetime, date
from unittest.mock import patch, MagicMock

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestInvoiceStrftimeFix(unittest.TestCase):
    """Test suite for invoice strftime error fix"""
    
    def setUp(self):
        """Set up test environment"""
        from app import app
        self.app = app
        self.client = app.test_client()
        self.app_context = app.app_context()
        self.app_context.push()
    
    def tearDown(self):
        """Clean up test environment"""
        self.app_context.pop()
    
    def test_format_datetime_filter_exists(self):
        """Test that format_datetime filter is properly registered"""
        filter_func = self.app.jinja_env.filters.get('format_datetime')
        self.assertIsNotNone(filter_func, "format_datetime filter should be registered")
    
    def test_format_datetime_with_datetime_object(self):
        """Test format_datetime filter with datetime object"""
        filter_func = self.app.jinja_env.filters.get('format_datetime')
        test_dt = datetime(2025, 7, 31, 10, 30, 45)
        
        result = filter_func(test_dt, '%d-%m-%Y / %H:%M:%S')
        expected = "31-07-2025 / 10:30:45"
        self.assertEqual(result, expected)
    
    def test_format_datetime_with_string(self):
        """Test format_datetime filter with string input"""
        filter_func = self.app.jinja_env.filters.get('format_datetime')
        test_str = "2025-07-31 10:30:45"
        
        result = filter_func(test_str, '%d %b %Y')
        self.assertIn("31", result)
        self.assertIn("Jul", result)
        self.assertIn("2025", result)
    
    def test_format_datetime_with_none(self):
        """Test format_datetime filter with None input"""
        filter_func = self.app.jinja_env.filters.get('format_datetime')
        
        result = filter_func(None, '%d-%m-%Y')
        self.assertEqual(result, "N/A")
    
    def test_format_datetime_with_empty_string(self):
        """Test format_datetime filter with empty string"""
        filter_func = self.app.jinja_env.filters.get('format_datetime')
        
        result = filter_func("", '%d-%m-%Y')
        self.assertEqual(result, "N/A")
    
    def test_invoice_template_rendering(self):
        """Test invoice template rendering with datetime objects"""
        from flask import render_template_string
        
        template = """
        <div>
            <p>Now: {{ now | format_datetime('%d-%m-%Y / %H:%M:%S') }}</p>
            <p>Date: {{ invoice_date | format_datetime('%d %b %Y') if invoice_date else 'N/A' }}</p>
        </div>
        """
        
        result = render_template_string(
            template,
            now=datetime.now(),
            invoice_date="2025-07-31 10:30:45"
        )
        
        # Should not contain filter names (indicates successful processing)
        self.assertNotIn("format_datetime", result)
        # Should contain formatted dates
        self.assertIn("31-07-2025", result)
        self.assertIn("31 Jul 2025", result)
    
    def test_invoice_view_template_compatibility(self):
        """Test that invoice_view.html template syntax is compatible"""
        from flask import render_template_string
        
        # Test the specific patterns we fixed
        template_patterns = [
            "{{ invoice.date_generated | format_datetime('%d %b %Y') if invoice.date_generated else 'N/A' }}",
            "{{ order.order_date | format_datetime('%d %b %Y') if order.order_date else 'N/A' }}",
            "{{ invoice.date_generated | format_datetime('%d %b %Y at %I:%M %p') if invoice.date_generated else 'N/A' }}"
        ]
        
        for pattern in template_patterns:
            with self.subTest(pattern=pattern):
                result = render_template_string(
                    pattern,
                    invoice={'date_generated': '2025-07-31 10:30:45'},
                    order={'order_date': '2025-07-31 10:30:45'}
                )
                
                # Should not contain filter names
                self.assertNotIn("format_datetime", result)
                # Should not be empty
                self.assertNotEqual(result.strip(), "")
    
    def test_edge_cases(self):
        """Test edge cases for datetime formatting"""
        filter_func = self.app.jinja_env.filters.get('format_datetime')
        
        # Test various edge cases
        test_cases = [
            (None, "N/A"),
            ("", "N/A"),
            ("undefined", "N/A"),
            ("none", "N/A"),
            ("2025-07-31", "31 Jul 2025"),  # Date only
            ("2025-07-31 10:30:45.123456", "31 Jul 2025"),  # With microseconds
        ]
        
        for input_val, expected_contains in test_cases:
            with self.subTest(input_val=input_val):
                result = filter_func(input_val, '%d %b %Y')
                if expected_contains == "N/A":
                    self.assertEqual(result, "N/A")
                else:
                    self.assertIn("31", result)
                    self.assertIn("Jul", result)
                    self.assertIn("2025", result)

class TestInvoiceRoutes(unittest.TestCase):
    """Test invoice routes for strftime compatibility"""
    
    def setUp(self):
        """Set up test environment"""
        from app import app
        self.app = app
        self.client = app.test_client()
        self.app_context = app.app_context()
        self.app_context.push()
    
    def tearDown(self):
        """Clean up test environment"""
        self.app_context.pop()
    
    @patch('app.get_db')
    def test_view_invoice_route_passes_datetime(self, mock_get_db):
        """Test that view_invoice route passes datetime object correctly"""
        # Mock database responses
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Mock order exists
        mock_db.execute.return_value.fetchone.side_effect = [
            {'order_id': 'TEST001', 'status': 'Dispatched'},  # order
            [],  # order_items
            {'invoice_number': 'INV001', 'pdf_path': None}  # invoice
        ]
        mock_db.execute.return_value.fetchall.return_value = []
        
        # Test the route
        with patch('app.render_template') as mock_render:
            mock_render.return_value = "test response"
            
            response = self.client.get('/orders/TEST001/invoice')
            
            # Check that render_template was called with datetime object
            mock_render.assert_called_once()
            args, kwargs = mock_render.call_args
            
            self.assertIn('now', kwargs)
            self.assertIsInstance(kwargs['now'], datetime)

if __name__ == '__main__':
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()

    # Add test cases
    suite.addTests(loader.loadTestsFromTestCase(TestInvoiceStrftimeFix))
    suite.addTests(loader.loadTestsFromTestCase(TestInvoiceRoutes))

    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print(f"\n{'='*60}")
    print(f"TEST SUMMARY")
    print(f"{'='*60}")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print(f"\nFAILURES:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print(f"\nERRORS:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    print(f"\n🎯 Overall Result: {'✅ ALL TESTS PASSED' if success else '❌ SOME TESTS FAILED'}")
    
    sys.exit(0 if success else 1)
