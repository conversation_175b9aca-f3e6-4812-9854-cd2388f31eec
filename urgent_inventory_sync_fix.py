#!/usr/bin/env python3
"""
URGENT: Fix Inventory Synchronization Issues
Based on user's screenshot showing Products Overview vs Inventory Records conflicts
"""

import sqlite3
import os
from datetime import datetime

def fix_urgent_inventory_sync():
    """Fix the inventory synchronization issue immediately"""
    print("🚨 URGENT INVENTORY SYNCHRONIZATION FIX")
    print("=" * 60)
    
    db_path = 'instance/medivent.db'
    if not os.path.exists(db_path):
        print("❌ Database not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        print("1. ANALYZING THE CONFLICT")
        print("-" * 40)
        
        # Get products with their current stock_quantity
        cursor.execute('''
            SELECT product_id, name, stock_quantity 
            FROM products 
            ORDER BY name
        ''')
        products = cursor.fetchall()
        
        print(f"Found {len(products)} products in products table")
        
        # Get actual inventory totals
        cursor.execute('''
            SELECT product_id, 
                   SUM(stock_quantity - COALESCE(allocated_quantity, 0)) as available_stock,
                   SUM(stock_quantity) as total_stock,
                   COUNT(*) as batch_count
            FROM inventory 
            WHERE status = 'active'
            GROUP BY product_id
        ''')
        inventory_data = cursor.fetchall()
        
        print(f"Found {len(inventory_data)} products with inventory records")
        
        # Create lookup for inventory data
        inventory_lookup = {}
        for inv in inventory_data:
            inventory_lookup[inv['product_id']] = {
                'available': inv['available_stock'],
                'total': inv['total_stock'],
                'batches': inv['batch_count']
            }
        
        print("\n2. IDENTIFYING CONFLICTS")
        print("-" * 40)
        
        conflicts = []
        for product in products:
            product_id = product['product_id']
            product_stock = product['stock_quantity'] or 0
            
            if product_id in inventory_lookup:
                actual_available = inventory_lookup[product_id]['available']
                if product_stock != actual_available:
                    conflicts.append({
                        'product_id': product_id,
                        'name': product['name'],
                        'products_table': product_stock,
                        'actual_available': actual_available,
                        'total_inventory': inventory_lookup[product_id]['total'],
                        'batch_count': inventory_lookup[product_id]['batches']
                    })
        
        print(f"🔍 CONFLICTS FOUND: {len(conflicts)}")
        
        # Show specific conflicts mentioned in screenshot
        priority_products = ['Cough Syrup', 'Paracetamol 500mg']
        for conflict in conflicts:
            if any(priority in conflict['name'] for priority in priority_products):
                print(f"🚨 PRIORITY: {conflict['name']}")
                print(f"   Products Table: {conflict['products_table']} units")
                print(f"   Actual Available: {conflict['actual_available']} units")
                print(f"   Total in Inventory: {conflict['total_inventory']} units")
                print(f"   Batch Count: {conflict['batch_count']}")
                print()
        
        print("\n3. FIXING CONFLICTS")
        print("-" * 40)
        
        fixed_count = 0
        for conflict in conflicts:
            product_id = conflict['product_id']
            correct_stock = max(0, conflict['actual_available'])  # Ensure non-negative
            
            # Update products table with correct stock from inventory
            cursor.execute('''
                UPDATE products 
                SET stock_quantity = ?,
                    updated_at = CURRENT_TIMESTAMP,
                    updated_by = 'urgent_sync_fix'
                WHERE product_id = ?
            ''', (correct_stock, product_id))
            
            print(f"✅ {conflict['name']}: {conflict['products_table']} → {correct_stock}")
            fixed_count += 1
        
        print(f"\n🔧 FIXED {fixed_count} CONFLICTS")
        
        print("\n4. VERIFICATION")
        print("-" * 40)
        
        # Verify the fixes
        cursor.execute('''
            SELECT p.product_id, p.name, p.stock_quantity,
                   COALESCE(SUM(i.stock_quantity - COALESCE(i.allocated_quantity, 0)), 0) as actual_available
            FROM products p
            LEFT JOIN inventory i ON p.product_id = i.product_id AND i.status = 'active'
            GROUP BY p.product_id, p.name, p.stock_quantity
            HAVING p.stock_quantity != COALESCE(SUM(i.stock_quantity - COALESCE(i.allocated_quantity, 0)), 0)
        ''')
        remaining_conflicts = cursor.fetchall()
        
        print(f"Remaining conflicts: {len(remaining_conflicts)}")
        
        if len(remaining_conflicts) == 0:
            print("🎉 ALL CONFLICTS RESOLVED!")
        else:
            print("⚠️ Some conflicts remain:")
            for conflict in remaining_conflicts[:3]:
                print(f"  - {conflict['name']}: Products={conflict['stock_quantity']}, Actual={conflict['actual_available']}")
        
        # Commit the changes
        conn.commit()
        conn.close()
        
        print("\n5. SUMMARY")
        print("-" * 40)
        print(f"✅ Fixed {fixed_count} inventory conflicts")
        print(f"✅ Remaining issues: {len(remaining_conflicts)}")
        print("🔄 Please refresh your browser to see the changes")
        
        return len(remaining_conflicts) == 0
        
    except Exception as e:
        print(f"❌ Fix failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = fix_urgent_inventory_sync()
    if success:
        print("\n🎉 INVENTORY SYNC COMPLETE - REFRESH YOUR BROWSER!")
    else:
        print("\n❌ INVENTORY SYNC FAILED - CHECK ERRORS ABOVE")
