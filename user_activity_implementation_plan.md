# User Activity Tracking & Session Management Implementation Plan

## Overview
Implement comprehensive user activity tracking and session management for Medivent ERP system with auto-logout after 15 minutes of inactivity.

## Database Schema Changes

### 1. New Table: user_sessions
```sql
CREATE TABLE IF NOT EXISTS user_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id TEXT UNIQUE NOT NULL,
    user_id INTEGER NOT NULL,
    username TEXT NOT NULL,
    login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    logout_time TIMESTAMP NULL,
    session_duration INTEGER DEFAULT 0,  -- in seconds
    total_screen_time INTEGER DEFAULT 0, -- in seconds
    total_active_time INTEGER DEFAULT 0, -- in seconds
    total_idle_time INTEGER DEFAULT 0,   -- in seconds
    ip_address TEXT,
    user_agent TEXT,
    status TEXT DEFAULT 'active',  -- active, expired, logged_out
    FOREIGN KEY (user_id) REFERENCES users (id)
);
```

### 2. New Table: user_activity_tracking
```sql
CREATE TABLE IF NOT EXISTS user_activity_tracking (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id TEXT NOT NULL,
    user_id INTEGER NOT NULL,
    username TEXT NOT NULL,
    activity_type TEXT NOT NULL,  -- page_view, click, scroll, typing, idle_start, idle_end
    page_url TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    duration INTEGER DEFAULT 0,  -- duration in seconds for activities
    metadata TEXT,  -- JSON for additional data
    FOREIGN KEY (session_id) REFERENCES user_sessions (session_id),
    FOREIGN KEY (user_id) REFERENCES users (id)
);
```

### 3. Enhanced activity_logs table
```sql
-- Add new columns to existing activity_logs table
ALTER TABLE activity_logs ADD COLUMN session_id TEXT;
ALTER TABLE activity_logs ADD COLUMN activity_duration INTEGER DEFAULT 0;
ALTER TABLE activity_logs ADD COLUMN page_url TEXT;
```

## Backend Implementation

### 1. Session Management Module (utils/session_manager.py)
- Track user sessions with unique session IDs
- Monitor session activity and timeouts
- Handle auto-logout logic
- Calculate screen time, active time, idle time

### 2. Activity Tracking Middleware
- Intercept all requests to log page views
- Track user interactions via AJAX endpoints
- Update session activity timestamps
- Handle session timeout checks

### 3. New API Endpoints
- `/api/activity/heartbeat` - Update last activity timestamp
- `/api/activity/track` - Log specific user activities
- `/api/session/status` - Check session status and remaining time
- `/api/session/extend` - Extend session on user activity

### 4. Enhanced /users/logs Route
- Add session-based filtering
- Display screen time, active time, idle time
- Show session duration and status
- Add real-time activity tracking data

## Frontend Implementation

### 1. Activity Detection JavaScript (static/js/activity-tracker.js)
- Detect mouse movements, clicks, keyboard input
- Track page visibility (tab active/inactive)
- Monitor scroll events and form interactions
- Send heartbeat signals to backend

### 2. Session Management JavaScript (static/js/session-manager.js)
- Monitor session timeout countdown
- Display warning before auto-logout
- Handle automatic logout and redirect
- Manage session extension requests

### 3. Auto-logout Implementation
- 15-minute inactivity timer
- 2-minute warning before logout
- Graceful session termination
- Redirect to login page with message

## Implementation Steps

### Step 1: Database Schema Updates
1. Create new tables for session and activity tracking
2. Add new columns to existing activity_logs table
3. Create database migration script

### Step 2: Backend Core Implementation
1. Create session manager utility
2. Implement activity tracking middleware
3. Add new API endpoints for activity tracking
4. Enhance existing authentication system

### Step 3: Frontend JavaScript Implementation
1. Create activity detection system
2. Implement session timeout management
3. Add auto-logout functionality
4. Create user activity monitoring

### Step 4: Enhanced User Logs Interface
1. Update /users/logs template with new data
2. Add session-based filtering and analytics
3. Display comprehensive activity metrics
4. Add real-time activity indicators

### Step 5: Testing and Integration
1. Test all new routes return HTTP 200
2. Verify database operations work correctly
3. Test session timeout and auto-logout
4. Ensure existing functionality remains intact

## Quality Assurance Requirements
- Incremental implementation with testing at each step
- Maintain backward compatibility
- Ensure no breaking changes to existing features
- Comprehensive testing of all new functionality
- Performance optimization for activity tracking

## Expected Deliverables
1. Working user activity tracking system
2. Functional auto-logout after 15 minutes
3. Enhanced /users/logs interface with comprehensive data
4. Complete testing report
5. Documentation of new features and usage
