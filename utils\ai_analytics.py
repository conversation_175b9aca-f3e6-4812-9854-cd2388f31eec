#!/usr/bin/env python3
"""
AI-Powered Analytics for Partial DC Management
Intelligent inventory predictions and automated recommendations
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from database import get_db
import json
import sqlite3
from typing import Dict, List, Tuple, Optional

try:
    from sklearn.linear_model import LinearRegression
    from sklearn.ensemble import RandomForestRegressor
    from sklearn.preprocessing import StandardScaler
    from sklearn.metrics import mean_absolute_error, r2_score
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("⚠️  scikit-learn not available. AI features will use basic algorithms.")

# Real-time Inventory Service
class RealTimeInventoryService:
    """Service for real-time inventory monitoring and updates"""

    def __init__(self):
        self.db = get_db()

    def update_inventory_status(self, product_id: str, warehouse_id: str = 'MAIN',
                               current_stock: int = None, reserved_stock: int = None) -> Dict:
        """Update real-time inventory status"""
        try:
            # Get current status
            existing = self.db.execute('''
                SELECT * FROM realtime_inventory_status
                WHERE product_id = ? AND warehouse_id = ?
            ''', (product_id, warehouse_id)).fetchone()

            if existing:
                # Update existing record
                updates = []
                params = []

                if current_stock is not None:
                    updates.append('current_stock = ?')
                    params.append(current_stock)

                if reserved_stock is not None:
                    updates.append('reserved_stock = ?')
                    params.append(reserved_stock)

                if updates:
                    updates.append('available_stock = current_stock - reserved_stock')
                    updates.append('last_updated = CURRENT_TIMESTAMP')

                    params.extend([product_id, warehouse_id])

                    self.db.execute(f'''
                        UPDATE realtime_inventory_status
                        SET {', '.join(updates)}
                        WHERE product_id = ? AND warehouse_id = ?
                    ''', params)
            else:
                # Create new record
                self.db.execute('''
                    INSERT INTO realtime_inventory_status (
                        product_id, warehouse_id, current_stock, reserved_stock,
                        available_stock, last_updated
                    ) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                ''', (
                    product_id, warehouse_id,
                    current_stock or 0, reserved_stock or 0,
                    (current_stock or 0) - (reserved_stock or 0)
                ))

            self.db.commit()

            # Check for notifications
            self._check_stock_notifications(product_id, warehouse_id)

            return {'success': True, 'message': 'Inventory updated successfully'}

        except Exception as e:
            self.db.rollback()
            return {'success': False, 'message': f'Error updating inventory: {str(e)}'}

    def _check_stock_notifications(self, product_id: str, warehouse_id: str):
        """Check if stock notifications should be created"""
        try:
            # Get updated inventory status
            inventory = self.db.execute('''
                SELECT * FROM realtime_inventory_status
                WHERE product_id = ? AND warehouse_id = ?
            ''', (product_id, warehouse_id)).fetchone()

            if not inventory:
                return

            # Check for pending orders
            pending_orders = self.db.execute('''
                SELECT COUNT(*) FROM partial_dc_tracking
                WHERE product_id = ? AND status = 'pending'
            ''', (product_id,)).fetchone()[0]

            # Create notifications based on stock status
            if inventory['available_stock'] > 0 and pending_orders > 0:
                # Stock available for pending orders
                self.db.execute('''
                    INSERT OR IGNORE INTO inventory_notifications (
                        product_id, notification_type, title, message, priority, action_required
                    ) VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    product_id,
                    'stock_available',
                    'Stock Available for Pending Orders',
                    f'Product {product_id} now has {inventory["available_stock"]} units available for {pending_orders} pending orders',
                    'high',
                    True
                ))
            elif inventory['available_stock'] <= inventory['alert_threshold']:
                # Low stock alert
                self.db.execute('''
                    INSERT OR IGNORE INTO inventory_notifications (
                        product_id, notification_type, title, message, priority, action_required
                    ) VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    product_id,
                    'low_stock',
                    'Low Stock Alert',
                    f'Product {product_id} is running low: {inventory["available_stock"]} units remaining',
                    'medium',
                    False
                ))

            self.db.commit()

        except Exception as e:
            print(f"Error checking stock notifications: {e}")

    def get_stock_alerts(self, user_id: str = None) -> List[Dict]:
        """Get current stock alerts for user"""
        try:
            query = '''
                SELECT
                    n.*,
                    p.name as product_name,
                    p.strength,
                    ris.available_stock,
                    ris.current_stock
                FROM inventory_notifications n
                JOIN products p ON n.product_id = p.product_id
                LEFT JOIN realtime_inventory_status ris ON n.product_id = ris.product_id
                WHERE n.is_read = FALSE
            '''

            params = []
            if user_id:
                query += ' AND (n.target_user = ? OR n.target_user IS NULL)'
                params.append(user_id)

            query += ' ORDER BY n.priority DESC, n.created_at DESC LIMIT 50'

            alerts = self.db.execute(query, params).fetchall()

            return [dict(alert) for alert in alerts]

        except Exception as e:
            print(f"Error getting stock alerts: {e}")
            return []

class PartialDCAnalytics:
    """AI-powered analytics for partial DC management"""
    
    def __init__(self):
        self.db = get_db()
        self.scaler = StandardScaler() if SKLEARN_AVAILABLE else None
        
    def predict_demand_forecast(self, product_id: str, days_ahead: int = 7) -> Dict:
        """Predict demand for a product over the next N days"""
        try:
            # Get historical data
            historical_data = self.db.execute('''
                SELECT 
                    DATE(pdt.created_at) as date,
                    SUM(pdt.pending_quantity) as daily_demand,
                    COUNT(DISTINCT pdt.order_id) as order_count
                FROM partial_dc_tracking pdt
                WHERE pdt.product_id = ? 
                AND pdt.created_at >= DATE('now', '-90 days')
                GROUP BY DATE(pdt.created_at)
                ORDER BY date
            ''', (product_id,)).fetchall()
            
            if len(historical_data) < 7:
                return {
                    'success': False,
                    'message': 'Insufficient historical data for prediction',
                    'prediction': 0,
                    'confidence': 0
                }
            
            # Convert to pandas DataFrame
            df = pd.DataFrame(historical_data)
            df['date'] = pd.to_datetime(df['date'])
            df['day_number'] = (df['date'] - df['date'].min()).dt.days
            
            if SKLEARN_AVAILABLE:
                # Use machine learning for prediction
                X = df[['day_number', 'order_count']].values
                y = df['daily_demand'].values
                
                # Train model
                model = LinearRegression()
                model.fit(X, y)
                
                # Predict future demand
                future_day = df['day_number'].max() + days_ahead
                avg_order_count = df['order_count'].mean()
                
                prediction = model.predict([[future_day, avg_order_count]])[0]
                confidence = model.score(X, y)
                
            else:
                # Use simple moving average
                recent_demand = df['daily_demand'].tail(7).mean()
                prediction = recent_demand
                confidence = 0.7  # Moderate confidence for simple method
            
            # Store prediction in database
            self.db.execute('''
                INSERT INTO ai_predictions (
                    product_id, prediction_type, prediction_value, confidence_score,
                    prediction_period, model_version, input_features
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                product_id,
                'demand_forecast',
                max(0, prediction),  # Ensure non-negative
                min(1.0, max(0.0, confidence)),  # Clamp between 0 and 1
                f'next_{days_ahead}_days',
                'v1.0_linear_regression' if SKLEARN_AVAILABLE else 'v1.0_moving_average',
                json.dumps({
                    'historical_days': len(historical_data),
                    'avg_daily_demand': float(df['daily_demand'].mean()),
                    'trend': 'increasing' if df['daily_demand'].tail(3).mean() > df['daily_demand'].head(3).mean() else 'decreasing'
                })
            ))
            
            self.db.commit()
            
            return {
                'success': True,
                'prediction': max(0, prediction),
                'confidence': confidence,
                'model_type': 'machine_learning' if SKLEARN_AVAILABLE else 'statistical',
                'historical_points': len(historical_data)
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'Error in demand forecasting: {str(e)}',
                'prediction': 0,
                'confidence': 0
            }
    
    def calculate_optimal_reorder_point(self, product_id: str) -> Dict:
        """Calculate optimal reorder point using AI analysis"""
        try:
            # Get product data
            product_data = self.db.execute('''
                SELECT 
                    p.*,
                    AVG(pdt.pending_quantity) as avg_pending,
                    COUNT(DISTINCT pdt.order_id) as total_orders,
                    AVG(JULIANDAY('now') - JULIANDAY(pdt.created_at)) as avg_pending_days
                FROM products p
                LEFT JOIN partial_dc_tracking pdt ON p.product_id = pdt.product_id
                WHERE p.product_id = ?
                GROUP BY p.product_id
            ''', (product_id,)).fetchone()
            
            if not product_data:
                return {'success': False, 'message': 'Product not found'}
            
            # Get current inventory
            inventory = self.db.execute('''
                SELECT * FROM realtime_inventory_status WHERE product_id = ?
            ''', (product_id,)).fetchone()
            
            # Get demand forecast
            demand_forecast = self.predict_demand_forecast(product_id, 14)
            
            if demand_forecast['success']:
                predicted_demand = demand_forecast['prediction']
                
                # Calculate safety stock (based on demand variability)
                historical_demand = self.db.execute('''
                    SELECT pending_quantity FROM partial_dc_tracking 
                    WHERE product_id = ? AND created_at >= DATE('now', '-30 days')
                ''', (product_id,)).fetchall()
                
                if historical_demand:
                    demand_values = [row[0] for row in historical_demand]
                    demand_std = np.std(demand_values) if len(demand_values) > 1 else 0
                    safety_stock = demand_std * 1.65  # 95% service level
                else:
                    safety_stock = predicted_demand * 0.2  # 20% safety buffer
                
                # Calculate lead time demand (assume 7 days lead time)
                lead_time_demand = predicted_demand * 7
                
                # Optimal reorder point
                reorder_point = lead_time_demand + safety_stock
                
                # Store prediction
                self.db.execute('''
                    INSERT INTO ai_predictions (
                        product_id, prediction_type, prediction_value, confidence_score,
                        prediction_period, model_version, input_features
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    product_id,
                    'reorder_point',
                    reorder_point,
                    demand_forecast['confidence'],
                    'current',
                    'v1.0_safety_stock',
                    json.dumps({
                        'predicted_demand': predicted_demand,
                        'safety_stock': safety_stock,
                        'lead_time_demand': lead_time_demand,
                        'current_stock': inventory['current_stock'] if inventory else 0
                    })
                ))
                
                self.db.commit()
                
                return {
                    'success': True,
                    'reorder_point': reorder_point,
                    'current_stock': inventory['current_stock'] if inventory else 0,
                    'safety_stock': safety_stock,
                    'predicted_demand': predicted_demand,
                    'recommendation': 'reorder_now' if (inventory and inventory['current_stock'] <= reorder_point) else 'stock_sufficient'
                }
            else:
                return {'success': False, 'message': 'Could not calculate reorder point due to demand forecast failure'}
                
        except Exception as e:
            return {'success': False, 'message': f'Error calculating reorder point: {str(e)}'}
    
    def generate_delivery_schedule_recommendations(self, order_id: str) -> Dict:
        """Generate smart delivery scheduling recommendations"""
        try:
            # Get pending items for the order
            pending_items = self.db.execute('''
                SELECT 
                    pdt.*,
                    ris.current_stock,
                    ris.available_stock,
                    ris.incoming_stock,
                    ris.expected_arrival_date
                FROM partial_dc_tracking pdt
                LEFT JOIN realtime_inventory_status ris ON pdt.product_id = ris.product_id
                WHERE pdt.order_id = ? AND pdt.status = 'pending'
                ORDER BY pdt.priority_level DESC
            ''', (order_id,)).fetchall()
            
            if not pending_items:
                return {'success': False, 'message': 'No pending items found for this order'}
            
            recommendations = []
            
            for item in pending_items:
                # Check if item can be fulfilled immediately
                if item['available_stock'] and item['available_stock'] >= item['pending_quantity']:
                    recommendations.append({
                        'product_id': item['product_id'],
                        'product_name': item['product_name'],
                        'action': 'fulfill_immediately',
                        'available_quantity': item['available_stock'],
                        'required_quantity': item['pending_quantity'],
                        'estimated_date': datetime.now().strftime('%Y-%m-%d'),
                        'confidence': 0.95
                    })
                elif item['incoming_stock'] and item['expected_arrival_date']:
                    # Check if incoming stock will cover the requirement
                    total_future_stock = (item['available_stock'] or 0) + (item['incoming_stock'] or 0)
                    if total_future_stock >= item['pending_quantity']:
                        recommendations.append({
                            'product_id': item['product_id'],
                            'product_name': item['product_name'],
                            'action': 'schedule_after_arrival',
                            'available_quantity': total_future_stock,
                            'required_quantity': item['pending_quantity'],
                            'estimated_date': item['expected_arrival_date'],
                            'confidence': 0.8
                        })
                    else:
                        recommendations.append({
                            'product_id': item['product_id'],
                            'product_name': item['product_name'],
                            'action': 'partial_fulfillment_possible',
                            'available_quantity': total_future_stock,
                            'required_quantity': item['pending_quantity'],
                            'estimated_date': item['expected_arrival_date'],
                            'confidence': 0.6
                        })
                else:
                    # Need to reorder
                    demand_forecast = self.predict_demand_forecast(item['product_id'], 14)
                    estimated_arrival = datetime.now() + timedelta(days=7)  # Assume 7-day lead time
                    
                    recommendations.append({
                        'product_id': item['product_id'],
                        'product_name': item['product_name'],
                        'action': 'reorder_required',
                        'available_quantity': item['available_stock'] or 0,
                        'required_quantity': item['pending_quantity'],
                        'estimated_date': estimated_arrival.strftime('%Y-%m-%d'),
                        'confidence': demand_forecast.get('confidence', 0.5)
                    })
            
            # Store the recommendation
            self.db.execute('''
                INSERT INTO ai_predictions (
                    product_id, prediction_type, prediction_value, confidence_score,
                    prediction_period, model_version, input_features
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                'ORDER_' + order_id,  # Use order ID as product ID for order-level predictions
                'delivery_schedule',
                len([r for r in recommendations if r['action'] == 'fulfill_immediately']),
                sum(r['confidence'] for r in recommendations) / len(recommendations) if recommendations else 0,
                'current',
                'v1.0_schedule_optimizer',
                json.dumps({
                    'total_items': len(pending_items),
                    'immediately_fulfillable': len([r for r in recommendations if r['action'] == 'fulfill_immediately']),
                    'requires_reorder': len([r for r in recommendations if r['action'] == 'reorder_required'])
                })
            ))
            
            self.db.commit()
            
            return {
                'success': True,
                'recommendations': recommendations,
                'summary': {
                    'total_items': len(pending_items),
                    'immediately_fulfillable': len([r for r in recommendations if r['action'] == 'fulfill_immediately']),
                    'scheduled_fulfillment': len([r for r in recommendations if r['action'] == 'schedule_after_arrival']),
                    'requires_reorder': len([r for r in recommendations if r['action'] == 'reorder_required'])
                }
            }
            
        except Exception as e:
            return {'success': False, 'message': f'Error generating delivery schedule: {str(e)}'}
    
    def analyze_fulfillment_patterns(self) -> Dict:
        """Analyze patterns in partial DC fulfillment"""
        try:
            # Get fulfillment data
            fulfillment_data = self.db.execute('''
                SELECT 
                    product_id,
                    AVG(JULIANDAY(updated_at) - JULIANDAY(created_at)) as avg_fulfillment_time,
                    COUNT(*) as total_requests,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_requests,
                    AVG(pending_quantity) as avg_pending_quantity,
                    AVG(priority_level) as avg_priority
                FROM partial_dc_tracking
                WHERE created_at >= DATE('now', '-30 days')
                GROUP BY product_id
                HAVING COUNT(*) >= 3
                ORDER BY avg_fulfillment_time DESC
            ''').fetchall()
            
            if not fulfillment_data:
                return {'success': False, 'message': 'Insufficient data for pattern analysis'}
            
            # Calculate overall metrics
            total_requests = sum(row[2] for row in fulfillment_data)
            total_completed = sum(row[3] for row in fulfillment_data)
            overall_completion_rate = (total_completed / total_requests) * 100 if total_requests > 0 else 0
            
            # Identify problematic products
            slow_products = [
                {
                    'product_id': row[0],
                    'avg_fulfillment_time': row[1],
                    'completion_rate': (row[3] / row[2]) * 100 if row[2] > 0 else 0,
                    'total_requests': row[2]
                }
                for row in fulfillment_data
                if row[1] > 7  # More than 7 days average fulfillment time
            ]
            
            return {
                'success': True,
                'overall_completion_rate': overall_completion_rate,
                'total_requests': total_requests,
                'slow_fulfillment_products': slow_products,
                'analysis_period': '30 days',
                'recommendations': self._generate_pattern_recommendations(slow_products)
            }
            
        except Exception as e:
            return {'success': False, 'message': f'Error analyzing fulfillment patterns: {str(e)}'}
    
    def _generate_pattern_recommendations(self, slow_products: List[Dict]) -> List[str]:
        """Generate recommendations based on fulfillment patterns"""
        recommendations = []
        
        if slow_products:
            recommendations.append(f"Consider increasing safety stock for {len(slow_products)} slow-fulfilling products")
            
            high_volume_slow = [p for p in slow_products if p['total_requests'] > 10]
            if high_volume_slow:
                recommendations.append(f"Priority attention needed for {len(high_volume_slow)} high-volume slow products")
            
            very_slow = [p for p in slow_products if p['avg_fulfillment_time'] > 14]
            if very_slow:
                recommendations.append(f"Critical review required for {len(very_slow)} products with >14 day fulfillment time")
        
        return recommendations

# Utility functions for AI analytics
def get_ai_analytics_instance():
    """Get a singleton instance of PartialDCAnalytics"""
    if not hasattr(get_ai_analytics_instance, '_instance'):
        get_ai_analytics_instance._instance = PartialDCAnalytics()
    return get_ai_analytics_instance._instance

def run_daily_ai_analysis():
    """Run daily AI analysis for all products with pending quantities"""
    try:
        analytics = get_ai_analytics_instance()
        db = get_db()
        
        # Get all products with pending quantities
        products = db.execute('''
            SELECT DISTINCT product_id FROM partial_dc_tracking 
            WHERE status = 'pending'
        ''').fetchall()
        
        results = []
        for (product_id,) in products:
            # Run demand forecast
            demand_result = analytics.predict_demand_forecast(product_id)
            
            # Calculate reorder point
            reorder_result = analytics.calculate_optimal_reorder_point(product_id)
            
            results.append({
                'product_id': product_id,
                'demand_forecast': demand_result,
                'reorder_analysis': reorder_result
            })
        
        return {
            'success': True,
            'products_analyzed': len(products),
            'results': results
        }
        
    except Exception as e:
        return {
            'success': False,
            'message': f'Error in daily AI analysis: {str(e)}'
        }
