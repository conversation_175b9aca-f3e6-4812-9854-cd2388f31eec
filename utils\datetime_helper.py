"""
DateTime Helper Utilities
Formats dates and times consistently across the project
"""

from datetime import datetime

def format_datetime(dt):
    """
    Format datetime to 2025-05-03:04:35 format
    """
    if dt is None:
        return ""
    
    if isinstance(dt, str):
        try:
            # Parse string datetime
            if 'T' in dt:
                dt = datetime.fromisoformat(dt.replace('T', ' ').split('.')[0])
            else:
                dt = datetime.strptime(dt, '%Y-%m-%d %H:%M:%S')
        except:
            return dt
    
    if isinstance(dt, datetime):
        return dt.strftime('%Y-%m-%d:%H:%M')
    
    return str(dt)

def format_date_only(dt):
    """
    Format date to 2025-05-03 format
    """
    if dt is None:
        return ""
    
    if isinstance(dt, str):
        try:
            if 'T' in dt:
                dt = datetime.fromisoformat(dt.replace('T', ' ').split('.')[0])
            else:
                dt = datetime.strptime(dt, '%Y-%m-%d %H:%M:%S')
        except:
            return dt
    
    if isinstance(dt, datetime):
        return dt.strftime('%Y-%m-%d')
    
    return str(dt)

def format_time_only(dt):
    """
    Format time to 04:35 format
    """
    if dt is None:
        return ""
    
    if isinstance(dt, str):
        try:
            if 'T' in dt:
                dt = datetime.fromisoformat(dt.replace('T', ' ').split('.')[0])
            else:
                dt = datetime.strptime(dt, '%Y-%m-%d %H:%M:%S')
        except:
            return dt
    
    if isinstance(dt, datetime):
        return dt.strftime('%H:%M')
    
    return str(dt)

def get_current_formatted_datetime():
    """
    Get current datetime in project format
    """
    return format_datetime(datetime.now())

def get_current_formatted_date():
    """
    Get current date in project format
    """
    return format_date_only(datetime.now())

def get_current_formatted_time():
    """
    Get current time in project format
    """
    return format_time_only(datetime.now())

def safe_strftime(dt, format_string='%Y-%m-%d'):
    """
    Ultra-robust datetime formatter - handles ANY input type safely
    """
    # Handle None/empty values
    if dt is None or dt == '' or dt == 'None':
        return ''

    # Handle numeric values (timestamps)
    if isinstance(dt, (int, float)):
        try:
            dt = datetime.fromtimestamp(dt)
        except (ValueError, OSError):
            return str(dt)

    # Handle string values
    if isinstance(dt, str):
        # If it's already in the desired format, return as-is
        if len(dt) == 10 and dt.count('-') == 2:
            try:
                # Validate it's a proper date
                datetime.strptime(dt, '%Y-%m-%d')
                return dt
            except ValueError:
                pass

        # Try multiple parsing strategies
        parsing_strategies = [
            ('%Y-%m-%d %H:%M:%S.%f', lambda x: x.split('.')[0]),  # 2025-07-25 11:19:48.672059
            ('%Y-%m-%d %H:%M:%S', lambda x: x),                   # 2025-07-25 11:19:48
            ('%Y-%m-%dT%H:%M:%S.%f', lambda x: x.replace('T', ' ').split('.')[0]),  # ISO format
            ('%Y-%m-%dT%H:%M:%S', lambda x: x.replace('T', ' ')),  # ISO format without microseconds
            ('%Y-%m-%d', lambda x: x),                            # 2025-07-25
            ('%d/%m/%Y', lambda x: x),                            # 25/07/2025
            ('%d-%m-%Y', lambda x: x),                            # 25-07-2025
            ('%m/%d/%Y', lambda x: x),                            # 07/25/2025
        ]

        for fmt, preprocessor in parsing_strategies:
            try:
                processed_dt = preprocessor(dt)
                parsed_dt = datetime.strptime(processed_dt, fmt)
                return parsed_dt.strftime(format_string)
            except (ValueError, TypeError, AttributeError):
                continue

        # If all parsing fails, return the original string
        return str(dt)

    # Handle datetime objects
    if hasattr(dt, 'strftime'):
        try:
            return dt.strftime(format_string)
        except (ValueError, TypeError, AttributeError):
            return str(dt)

    # Handle date objects
    if hasattr(dt, 'year') and hasattr(dt, 'month') and hasattr(dt, 'day'):
        try:
            return datetime(dt.year, dt.month, dt.day).strftime(format_string)
        except (ValueError, TypeError, AttributeError):
            return str(dt)

    # Fallback: convert to string
    return str(dt)

def safe_datetime_for_template(dt):
    """
    Convert string datetime to datetime object for template use
    """
    if dt is None:
        return None

    if isinstance(dt, datetime):
        return dt

    if isinstance(dt, str):
        try:
            if 'T' in dt:
                return datetime.fromisoformat(dt.replace('T', ' ').split('.')[0])
            elif '.' in dt and len(dt) > 19:
                return datetime.strptime(dt.split('.')[0], '%Y-%m-%d %H:%M:%S')
            elif len(dt) == 19:
                return datetime.strptime(dt, '%Y-%m-%d %H:%M:%S')
            elif len(dt) == 10:
                return datetime.strptime(dt, '%Y-%m-%d')
        except (ValueError, TypeError):
            pass

    return None
