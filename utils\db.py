"""
Database utility functions for Medivent Pharmaceuticals Web Portal
Centralized database connection management
"""

import sqlite3
import os
from flask import g, current_app

def get_db():
    """Get database connection with proper configuration"""
    if 'db' not in g:
        # Try to get database path from app config, fallback to default
        try:
            db_path = current_app.config.get('DATABASE', 'instance/medivent.db')
        except RuntimeError:
            # Outside application context, use default path
            db_path = 'instance/medivent.db'

        # Ensure instance directory exists
        instance_dir = os.path.dirname(db_path)
        if instance_dir and not os.path.exists(instance_dir):
            os.makedirs(instance_dir, exist_ok=True)

        g.db = sqlite3.connect(db_path)
        g.db.row_factory = sqlite3.Row
        g.db.execute("PRAGMA foreign_keys = ON")

        # Ensure orders table has customer_name column (compatibility)
        try:
            cursor = g.db.execute("PRAGMA table_info(orders)")
            columns = [row[1] for row in cursor.fetchall()]
            if 'customer_name' not in columns:
                g.db.execute("ALTER TABLE orders ADD COLUMN customer_name TEXT")
                g.db.commit()
        except Exception:
            pass  # Ignore if table doesn't exist or column already exists

    return g.db

def get_db_direct(db_path=None):
    """Get direct database connection without Flask context"""
    if db_path is None:
        db_path = 'instance/medivent.db'

    # Ensure instance directory exists
    instance_dir = os.path.dirname(db_path)
    if instance_dir and not os.path.exists(instance_dir):
        os.makedirs(instance_dir, exist_ok=True)

    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    conn.execute("PRAGMA foreign_keys = ON")
    return conn

def close_db(e=None):
    """Close database connection"""
    db = g.pop('db', None)
    if db is not None:
        db.close()

def init_db():
    """Initialize the database with schema"""
    db = get_db()

    try:
        with current_app.open_resource('schema.sql') as f:
            db.executescript(f.read().decode('utf8'))
        db.commit()
    except Exception as e:
        print(f"Error initializing database: {e}")

def test_db_connection():
    """Test database connection and return status"""
    try:
        db = get_db_direct()
        cursor = db.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
        table_count = cursor.fetchone()[0]
        db.close()
        return True, f"Database connected successfully. {table_count} tables found."
    except Exception as e:
        return False, f"Database connection failed: {e}"
