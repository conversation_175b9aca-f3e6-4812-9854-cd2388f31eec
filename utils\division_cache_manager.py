"""
Division Cache Manager
Handles cache invalidation for division data to ensure real-time updates
"""

import time
import threading
from typing import Dict, Optional

class DivisionCacheManager:
    """
    Manages division data caching and invalidation
    Ensures real-time updates when division status changes
    """
    
    def __init__(self):
        self._cache = {}
        self._cache_timestamps = {}
        self._cache_lock = threading.Lock()
        self._cache_ttl = 300  # 5 minutes default TTL
        
    def get_cache_key(self, cache_type: str, **kwargs) -> str:
        """Generate cache key based on type and parameters"""
        key_parts = [cache_type]
        for k, v in sorted(kwargs.items()):
            key_parts.append(f"{k}:{v}")
        return "|".join(key_parts)
    
    def get_cached_data(self, cache_key: str) -> Optional[Dict]:
        """Get cached data if still valid"""
        with self._cache_lock:
            if cache_key not in self._cache:
                return None
                
            timestamp = self._cache_timestamps.get(cache_key, 0)
            if time.time() - timestamp > self._cache_ttl:
                # Cache expired
                self._cache.pop(cache_key, None)
                self._cache_timestamps.pop(cache_key, None)
                return None
                
            return self._cache[cache_key]
    
    def set_cached_data(self, cache_key: str, data: Dict) -> None:
        """Store data in cache with timestamp"""
        with self._cache_lock:
            self._cache[cache_key] = data
            self._cache_timestamps[cache_key] = time.time()
    
    def invalidate_cache(self, pattern: Optional[str] = None) -> None:
        """Invalidate cache entries matching pattern or all if pattern is None"""
        with self._cache_lock:
            if pattern is None:
                # Clear all cache
                self._cache.clear()
                self._cache_timestamps.clear()
            else:
                # Clear entries matching pattern
                keys_to_remove = [key for key in self._cache.keys() if pattern in key]
                for key in keys_to_remove:
                    self._cache.pop(key, None)
                    self._cache_timestamps.pop(key, None)
    
    def invalidate_division_cache(self) -> None:
        """Specifically invalidate division-related cache"""
        self.invalidate_cache("divisions")
        self.invalidate_cache("forms")
    
    def get_cache_stats(self) -> Dict:
        """Get cache statistics for debugging"""
        with self._cache_lock:
            return {
                'total_entries': len(self._cache),
                'cache_keys': list(self._cache.keys()),
                'oldest_entry': min(self._cache_timestamps.values()) if self._cache_timestamps else None,
                'newest_entry': max(self._cache_timestamps.values()) if self._cache_timestamps else None
            }

# Global cache manager instance
_division_cache_manager = None

def get_division_cache_manager() -> DivisionCacheManager:
    """Get or create global division cache manager"""
    global _division_cache_manager
    if _division_cache_manager is None:
        _division_cache_manager = DivisionCacheManager()
    return _division_cache_manager

def invalidate_division_cache():
    """Convenience function to invalidate division cache"""
    cache_manager = get_division_cache_manager()
    cache_manager.invalidate_division_cache()

def clear_all_cache():
    """Convenience function to clear all cache"""
    cache_manager = get_division_cache_manager()
    cache_manager.invalidate_cache()
