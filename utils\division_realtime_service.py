#!/usr/bin/env python3
"""
Division Real-time Service
Centralized service for real-time division data across all components
"""

import sqlite3
from typing import List, Dict, Optional, Tuple
import logging
from datetime import datetime, timedelta
from .unified_division_manager import get_unified_division_manager

class DivisionRealtimeService:
    """
    Centralized service for real-time division data synchronization
    Ensures consistent division information across all ERP components
    """
    
    def __init__(self, db_connection):
        self.db = db_connection
        self.logger = logging.getLogger(__name__)
        self._cache = {}
        self._cache_timeout = 30  # 30 seconds for real-time updates
        
    def get_active_divisions_count(self) -> int:
        """Get count of active divisions with caching"""
        cache_key = 'active_divisions_count'
        
        # Check cache first
        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]['data']
        
        try:
            # Use unified manager for consistent filtering
            unified_manager = get_unified_division_manager(self.db)
            active_divisions = unified_manager.get_active_divisions()
            count = len(active_divisions)
            
            # Cache the result
            self._cache[cache_key] = {
                'data': count,
                'timestamp': datetime.now()
            }
            
            return count
            
        except Exception as e:
            self.logger.error(f"Error getting active divisions count: {e}")
            return 0
    
    def get_active_divisions_for_dropdowns(self) -> List[Dict]:
        """Get active divisions formatted for form dropdowns"""
        cache_key = 'active_divisions_dropdown'
        
        # Check cache first
        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]['data']
        
        try:
            # Use unified manager for consistent data
            unified_manager = get_unified_division_manager(self.db)
            divisions = unified_manager.get_divisions_for_dropdown()
            
            # Cache the result
            self._cache[cache_key] = {
                'data': divisions,
                'timestamp': datetime.now()
            }
            
            return divisions
            
        except Exception as e:
            self.logger.error(f"Error getting divisions for dropdowns: {e}")
            return []
    
    def get_divisions_with_analytics(self) -> List[Dict]:
        """Get divisions with analytics data for charts and reports"""
        cache_key = 'divisions_with_analytics'
        
        # Check cache first
        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]['data']
        
        try:
            # Get active divisions with enhanced analytics
            # Note: orders table may not have division_id column, so we'll use product-based division mapping
            query = '''
                SELECT d.division_id, d.name, d.category, d.manager_id,
                       COUNT(DISTINCT oi.order_id) as order_count,
                       COALESCE(SUM(oi.line_total), 0) as total_revenue,
                       COALESCE(AVG(oi.line_total), 0) as avg_order_value,
                       COUNT(DISTINCT o.customer_name) as customer_count,
                       COUNT(DISTINCT p.product_id) as product_count
                FROM divisions d
                LEFT JOIN products p ON d.division_id = p.division_id
                LEFT JOIN order_items oi ON p.product_id = oi.product_id
                LEFT JOIN orders o ON oi.order_id = o.order_id AND o.status != 'Cancelled'
                WHERE d.is_active = 1 AND d.status = 'active'
                GROUP BY d.division_id, d.name, d.category, d.manager_id
                ORDER BY total_revenue DESC, d.name
            '''
            
            cursor = self.db.execute(query)
            divisions = [dict(row) for row in cursor.fetchall()]
            
            # Cache the result
            self._cache[cache_key] = {
                'data': divisions,
                'timestamp': datetime.now()
            }
            
            return divisions
            
        except Exception as e:
            self.logger.error(f"Error getting divisions with analytics: {e}")
            return []
    
    def get_division_sparkline_data(self) -> List[int]:
        """Get division count trend data for sparkline charts"""
        cache_key = 'division_sparkline_data'
        
        # Check cache first
        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]['data']
        
        try:
            # Get division count for last 7 days
            sparkline_data = []
            current_count = self.get_active_divisions_count()
            
            # For now, simulate trend data based on current count
            # In a real system, this would query historical data
            base_count = max(1, current_count - 2)
            trend_data = [
                base_count, base_count + 1, base_count + 1, 
                base_count + 2, current_count - 1, current_count, current_count
            ]
            
            # Cache the result
            self._cache[cache_key] = {
                'data': trend_data,
                'timestamp': datetime.now()
            }
            
            return trend_data
            
        except Exception as e:
            self.logger.error(f"Error getting sparkline data: {e}")
            return [0, 0, 0, 0, 0, 0, 0]
    
    def get_division_summary_stats(self) -> Dict:
        """Get comprehensive division summary statistics"""
        cache_key = 'division_summary_stats'
        
        # Check cache first
        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]['data']
        
        try:
            active_count = self.get_active_divisions_count()
            divisions_with_analytics = self.get_divisions_with_analytics()
            
            total_revenue = sum(div['total_revenue'] for div in divisions_with_analytics)
            total_orders = sum(div['order_count'] for div in divisions_with_analytics)
            total_customers = sum(div['customer_count'] for div in divisions_with_analytics)
            
            stats = {
                'active_divisions': active_count,
                'total_revenue': total_revenue,
                'total_orders': total_orders,
                'total_customers': total_customers,
                'avg_revenue_per_division': total_revenue / max(1, active_count),
                'sparkline_data': self.get_division_sparkline_data()
            }
            
            # Cache the result
            self._cache[cache_key] = {
                'data': stats,
                'timestamp': datetime.now()
            }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Error getting division summary stats: {e}")
            return {
                'active_divisions': 0,
                'total_revenue': 0,
                'total_orders': 0,
                'total_customers': 0,
                'avg_revenue_per_division': 0,
                'sparkline_data': [0, 0, 0, 0, 0, 0, 0]
            }
    
    def invalidate_all_caches(self) -> None:
        """Invalidate all cached division data"""
        self._cache.clear()
        self.logger.info("All division caches invalidated")
    
    def invalidate_cache(self, cache_key: str) -> None:
        """Invalidate specific cache entry"""
        if cache_key in self._cache:
            del self._cache[cache_key]
            self.logger.info(f"Cache invalidated: {cache_key}")
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cache entry is valid and not expired"""
        if cache_key not in self._cache:
            return False
        
        cache_entry = self._cache[cache_key]
        age = datetime.now() - cache_entry['timestamp']
        
        return age.total_seconds() < self._cache_timeout


# Service instance cache per database connection
_division_realtime_services = {}

def get_division_realtime_service(db_connection):
    """Factory function to get division realtime service instance"""
    # Create a new instance for each request to avoid stale cache
    # In production, you might want to use a more sophisticated caching strategy
    return DivisionRealtimeService(db_connection)

def get_active_divisions_count_realtime(db_connection) -> int:
    """Quick function to get active divisions count"""
    service = get_division_realtime_service(db_connection)
    return service.get_active_divisions_count()

def get_divisions_for_forms_realtime(db_connection) -> List[Dict]:
    """Quick function to get divisions for form dropdowns"""
    service = get_division_realtime_service(db_connection)
    return service.get_active_divisions_for_dropdowns()

def invalidate_division_caches(db_connection) -> None:
    """Quick function to invalidate all division caches"""
    service = get_division_realtime_service(db_connection)
    service.invalidate_all_caches()
