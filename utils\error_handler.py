"""
Comprehensive Error Handler for ERP System
Advanced error handling and recovery mechanisms
"""

import sqlite3
import json
import traceback
from functools import wraps
from flask import jsonify, flash, redirect, url_for, request

def safe_float_conversion(value, default=0.0):
    """Safely convert value to float with fallback"""
    if value is None or value == '' or value == 'None':
        return default
    
    try:
        return float(str(value).strip())
    except (ValueError, TypeError):
        return default

def safe_int_conversion(value, default=0):
    """Safely convert value to int with fallback"""
    if value is None or value == '' or value == 'None':
        return default
    
    try:
        return int(str(value).strip())
    except (ValueError, TypeError):
        return default

def safe_json_parse(value, default=None):
    """Safely parse JSON with fallback"""
    if default is None:
        default = {}
    
    if value is None or value == '' or value == 'None':
        return default
    
    try:
        return json.loads(str(value))
    except (json.JSONDecodeError, TypeError):
        return default

def database_error_handler(func):
    """Decorator to handle database errors gracefully"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except sqlite3.OperationalError as e:
            error_msg = str(e).lower()
            
            if 'no such column' in error_msg:
                # Handle missing column errors
                print(f"Database column error: {e}")
                if 'api' in request.path:
                    return jsonify({
                        'error': 'Database schema issue detected',
                        'message': 'Please contact administrator to update database schema',
                        'technical_details': str(e)
                    }), 500
                else:
                    flash('Database schema needs updating. Please contact administrator.', 'warning')
                    return redirect(url_for('dashboard'))
            
            elif 'no such table' in error_msg:
                # Handle missing table errors
                print(f"Database table error: {e}")
                if 'api' in request.path:
                    return jsonify({
                        'error': 'Database table missing',
                        'message': 'Required database table not found',
                        'technical_details': str(e)
                    }), 500
                else:
                    flash('Database table missing. Please contact administrator.', 'danger')
                    return redirect(url_for('dashboard'))
            
            else:
                # Generic database error
                print(f"Database error: {e}")
                if 'api' in request.path:
                    return jsonify({
                        'error': 'Database error',
                        'message': 'A database error occurred',
                        'technical_details': str(e)
                    }), 500
                else:
                    flash('A database error occurred. Please try again.', 'danger')
                    return redirect(url_for('dashboard'))
        
        except ValueError as e:
            # Handle value conversion errors
            print(f"Value conversion error: {e}")
            if 'api' in request.path:
                return jsonify({
                    'error': 'Invalid data format',
                    'message': 'Data format validation failed',
                    'technical_details': str(e)
                }), 400
            else:
                flash('Invalid data format. Please check your input.', 'warning')
                return redirect(request.referrer or url_for('dashboard'))
        
        except Exception as e:
            # Handle all other errors
            print(f"Unexpected error: {e}")
            print(traceback.format_exc())
            if 'api' in request.path:
                return jsonify({
                    'error': 'Internal server error',
                    'message': 'An unexpected error occurred',
                    'technical_details': str(e)
                }), 500
            else:
                flash('An unexpected error occurred. Please try again.', 'danger')
                return redirect(url_for('dashboard'))
    
    return wrapper

def create_fallback_data():
    """Create fallback data structures for missing database records"""
    return {
        'riders': {
            'rider_id': 'FALLBACK_001',
            'name': 'Sample Rider',
            'email': '<EMAIL>',
            'phone': '+92-300-0000000',
            'city': 'Karachi',
            'status': 'active',
            'rating': 4.5,
            'total_deliveries': 0,
            'successful_deliveries': 0,
            'performance_stats': '{"avg_delivery_time": 25, "on_time_percentage": 95.0}'
        },
        'api_keys': {
            'id': 1,
            'service_name': 'sample_service',
            'api_key': 'sample_key_123',
            'description': 'Sample API key for testing',
            'status': 'active',
            'rate_limit': 1000,
            'usage_count': 0,
            'permissions': '{"read": true}'
        },
        'divisions': {
            'division_id': 'DIV001',
            'code': 'SAMPLE',
            'name': 'Sample Division',
            'description': 'Sample division for testing',
            'status': 'active',
            'budget': 0.0,
            'revenue': 0.0
        }
    }

def safe_database_query(db, query, params=None, fallback_data=None):
    """Execute database query with comprehensive error handling"""
    if params is None:
        params = []
    
    try:
        if 'SELECT' in query.upper():
            result = db.execute(query, params).fetchall()
            return result if result else (fallback_data or [])
        else:
            result = db.execute(query, params)
            db.commit()
            return result
    
    except sqlite3.OperationalError as e:
        print(f"Database query error: {e}")
        print(f"Query: {query}")
        print(f"Params: {params}")
        
        if 'SELECT' in query.upper():
            return fallback_data or []
        else:
            raise e
    
    except Exception as e:
        print(f"Unexpected database error: {e}")
        print(f"Query: {query}")
        print(f"Params: {params}")
        raise e

def validate_form_data(data, required_fields=None, numeric_fields=None):
    """Validate and sanitize form data"""
    if required_fields is None:
        required_fields = []
    if numeric_fields is None:
        numeric_fields = []
    
    errors = []
    cleaned_data = {}
    
    # Check required fields
    for field in required_fields:
        if field not in data or not data[field] or str(data[field]).strip() == '':
            errors.append(f"{field.replace('_', ' ').title()} is required")
        else:
            cleaned_data[field] = str(data[field]).strip()
    
    # Process all fields
    for key, value in data.items():
        if key not in cleaned_data:
            if key in numeric_fields:
                # Handle numeric fields
                if key.endswith('budget') or key.endswith('amount') or key.endswith('price'):
                    cleaned_data[key] = safe_float_conversion(value, 0.0)
                elif key.endswith('count') or key.endswith('quantity') or key.endswith('limit'):
                    cleaned_data[key] = safe_int_conversion(value, 0)
                else:
                    cleaned_data[key] = safe_float_conversion(value, 0.0)
            else:
                # Handle text fields
                cleaned_data[key] = str(value).strip() if value is not None else ''
    
    return cleaned_data, errors

def create_error_response(error_type, message, details=None):
    """Create standardized error response"""
    response = {
        'success': False,
        'error_type': error_type,
        'message': message,
        'timestamp': str(datetime.now())
    }
    
    if details:
        response['details'] = details
    
    return response

# Global error handlers for common issues
def handle_float_conversion_error(value, field_name="value"):
    """Handle float conversion errors specifically"""
    try:
        if value is None or value == '' or value == 'None':
            return 0.0
        return float(str(value).strip())
    except (ValueError, TypeError):
        print(f"Float conversion error for {field_name}: {value}")
        return 0.0

def handle_missing_table_error(table_name):
    """Handle missing table errors"""
    print(f"Missing table detected: {table_name}")
    
    # Return appropriate fallback data based on table
    fallback_data = create_fallback_data()
    
    if table_name in fallback_data:
        return [fallback_data[table_name]]
    else:
        return []

def handle_missing_column_error(column_name, table_name):
    """Handle missing column errors"""
    print(f"Missing column detected: {column_name} in table {table_name}")
    
    # Return default value based on column type
    if any(keyword in column_name.lower() for keyword in ['rating', 'budget', 'amount', 'price']):
        return 0.0
    elif any(keyword in column_name.lower() for keyword in ['count', 'quantity', 'limit']):
        return 0
    elif 'stats' in column_name.lower():
        return '{}'
    else:
        return ''
