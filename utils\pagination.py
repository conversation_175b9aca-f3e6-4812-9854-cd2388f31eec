#!/usr/bin/env python3
"""
Simple pagination utility for Flask applications
"""

import math
from flask import request

class Pagination:
    """Simple pagination class"""
    
    def __init__(self, page, per_page, total_count):
        self.page = page
        self.per_page = per_page
        self.total_count = total_count
        
    @property
    def items(self):
        """Calculate items for current page"""
        return self.per_page
    
    @property
    def prev_num(self):
        """Previous page number"""
        return self.page - 1 if self.has_prev else None
    
    @property
    def next_num(self):
        """Next page number"""
        return self.page + 1 if self.has_next else None
    
    @property
    def has_prev(self):
        """Check if there's a previous page"""
        return self.page > 1
    
    @property
    def has_next(self):
        """Check if there's a next page"""
        return self.page < self.pages
    
    @property
    def pages(self):
        """Total number of pages"""
        return math.ceil(self.total_count / self.per_page)
    
    def iter_pages(self, left_edge=2, left_current=2, right_current=3, right_edge=2):
        """Iterate over page numbers for pagination display"""
        last = self.pages
        for num in range(1, last + 1):
            if num <= left_edge or \
               (self.page - left_current - 1 < num < self.page + right_current) or \
               num > last - right_edge:
                yield num

def paginate_query_results(results, page=None, per_page=20):
    """
    Paginate a list of results
    
    Args:
        results: List of items to paginate
        page: Current page number (from request if None)
        per_page: Items per page
    
    Returns:
        tuple: (paginated_items, pagination_object)
    """
    if page is None:
        page = request.args.get('page', 1, type=int)
    
    total_count = len(results)
    
    # Calculate offset
    offset = (page - 1) * per_page
    
    # Get items for current page
    paginated_items = results[offset:offset + per_page]
    
    # Create pagination object
    pagination = Pagination(page, per_page, total_count)
    
    return paginated_items, pagination

def create_simple_pagination(total_items, page=None, per_page=20):
    """
    Create a simple pagination object without actual data slicing
    
    Args:
        total_items: Total number of items
        page: Current page number (from request if None)
        per_page: Items per page
    
    Returns:
        Pagination object
    """
    if page is None:
        page = request.args.get('page', 1, type=int)
    
    return Pagination(page, per_page, total_items)
