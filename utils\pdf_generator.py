#!/usr/bin/env python3
"""
PDF Generation for Delivery Challans
"""

import os
import json
import sqlite3
from datetime import datetime
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.pdfgen import canvas
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT

class DCPDFGenerator:
    """Generate professional PDF for Delivery Challans"""
    
    def __init__(self):
        self.styles = getSampleStyleSheet()
        self.setup_custom_styles()
    
    def setup_custom_styles(self):
        """Setup custom paragraph styles"""
        self.styles.add(ParagraphStyle(
            name='CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.darkblue
        ))
        
        self.styles.add(ParagraphStyle(
            name='CustomHeading',
            parent=self.styles['Heading2'],
            fontSize=14,
            spaceAfter=12,
            textColor=colors.darkblue
        ))
        
        self.styles.add(ParagraphStyle(
            name='CustomNormal',
            parent=self.styles['Normal'],
            fontSize=10,
            spaceAfter=6
        ))
    
    def get_dc_data(self, dc_number):
        """Retrieve DC data from database"""
        try:
            conn = sqlite3.connect('instance/medivent.db')
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # Get DC details
            dc_data = cursor.execute('''
                SELECT dc.*, o.customer_name, o.customer_phone, o.customer_address,
                       o.order_date, o.approval_date
                FROM delivery_challans dc
                JOIN orders o ON dc.order_id = o.order_id
                WHERE dc.dc_number = ?
            ''', (dc_number,)).fetchone()
            
            if not dc_data:
                return None
            
            # Get batch details
            batch_details = []
            if dc_data['batch_details']:
                try:
                    batch_info = json.loads(dc_data['batch_details'])
                    
                    for batch in batch_info:
                        # Get detailed batch information
                        batch_detail = cursor.execute('''
                            SELECT i.*, p.name as product_name, p.strength, p.manufacturer,
                                   p.unit_of_measure, w.name as warehouse_name
                            FROM inventory i
                            JOIN products p ON i.product_id = p.product_id
                            JOIN warehouses w ON i.warehouse_id = w.warehouse_id
                            WHERE i.inventory_id = ?
                        ''', (batch['inventory_id'],)).fetchone()
                        
                        if batch_detail:
                            batch_details.append({
                                'product_name': batch_detail['product_name'],
                                'strength': batch_detail['strength'] or '',
                                'manufacturer': batch_detail['manufacturer'] or '',
                                'batch_number': batch_detail['batch_number'],
                                'manufacturing_date': batch_detail['manufacturing_date'],
                                'expiry_date': batch_detail['expiry_date'],
                                'warehouse_name': batch_detail['warehouse_name'],
                                'location_code': batch_detail['location_code'] or 'N/A',
                                'allocated_quantity': batch['quantity'],
                                'unit_of_measure': batch_detail['unit_of_measure'] or 'Units'
                            })
                except json.JSONDecodeError:
                    pass
            
            conn.close()
            
            return {
                'dc_data': dict(dc_data),
                'batch_details': batch_details
            }
            
        except Exception as e:
            print(f"Error retrieving DC data: {e}")
            return None
    
    def generate_pdf(self, dc_number, output_path=None):
        """Generate PDF for delivery challan"""
        try:
            # Get DC data
            data = self.get_dc_data(dc_number)
            if not data:
                raise Exception(f"DC {dc_number} not found")
            
            dc_data = data['dc_data']
            batch_details = data['batch_details']
            
            # Setup output path
            if not output_path:
                os.makedirs('static/documents/challans', exist_ok=True)
                output_path = f'static/documents/challans/{dc_number}.pdf'
            
            # Create PDF document
            doc = SimpleDocTemplate(output_path, pagesize=A4,
                                  rightMargin=72, leftMargin=72,
                                  topMargin=72, bottomMargin=18)
            
            # Build PDF content
            story = []
            
            # Header
            story.append(Paragraph("MEDIVENT PHARMACEUTICALS", self.styles['CustomTitle']))
            story.append(Paragraph("Delivery Challan", self.styles['CustomHeading']))
            story.append(Spacer(1, 12))
            
            # DC Information Table
            dc_info_data = [
                ['DC Number:', dc_data['dc_number'], 'Date:', self.format_date(dc_data['created_date'])],
                ['Order ID:', dc_data['order_id'], 'Status:', dc_data['status'].title()],
                ['Customer:', dc_data['customer_name'], 'Phone:', dc_data.get('customer_phone', 'N/A')],
                ['Address:', dc_data.get('customer_address', 'N/A'), 'Total Items:', str(dc_data['total_items'])],
                ['Total Amount:', f"₹{dc_data['total_amount']:.2f}", 'Created By:', dc_data['created_by']]
            ]
            
            dc_info_table = Table(dc_info_data, colWidths=[1.2*inch, 2*inch, 1.2*inch, 2*inch])
            dc_info_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
                ('BACKGROUND', (2, 0), (2, -1), colors.lightgrey),
                ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(dc_info_table)
            story.append(Spacer(1, 20))
            
            # Batch Details Section
            story.append(Paragraph("Batch Allocation Details", self.styles['CustomHeading']))
            story.append(Spacer(1, 12))
            
            if batch_details:
                # Batch details table
                batch_headers = ['Product', 'Batch No.', 'Mfg Date', 'Exp Date', 
                               'Warehouse', 'Location', 'Qty', 'Unit']
                
                batch_data = [batch_headers]
                
                for batch in batch_details:
                    batch_data.append([
                        f"{batch['product_name']}\n{batch['strength']}",
                        batch['batch_number'],
                        self.format_date(batch['manufacturing_date']),
                        self.format_date(batch['expiry_date']),
                        batch['warehouse_name'],
                        batch['location_code'],
                        str(batch['allocated_quantity']),
                        batch['unit_of_measure']
                    ])
                
                batch_table = Table(batch_data, colWidths=[1.8*inch, 0.8*inch, 0.7*inch, 0.7*inch, 
                                                         0.8*inch, 0.6*inch, 0.5*inch, 0.6*inch])
                
                batch_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 9),
                    ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                    ('FONTSIZE', (0, 1), (-1, -1), 8),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE')
                ]))
                
                story.append(batch_table)
            else:
                story.append(Paragraph("No batch details available", self.styles['CustomNormal']))
            
            story.append(Spacer(1, 30))
            
            # Footer
            story.append(Paragraph("This is a computer-generated document.", 
                                 self.styles['CustomNormal']))
            story.append(Paragraph(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", 
                                 self.styles['CustomNormal']))
            
            # Build PDF
            doc.build(story)
            
            # Update DC record with PDF path
            self.update_dc_pdf_path(dc_number, output_path)
            
            return output_path
            
        except Exception as e:
            print(f"Error generating PDF: {e}")
            raise e
    
    def format_date(self, date_str):
        """Format date string for display"""
        if not date_str:
            return 'N/A'
        
        try:
            if isinstance(date_str, str):
                # Try different date formats
                for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d', '%d-%m-%Y']:
                    try:
                        date_obj = datetime.strptime(date_str, fmt)
                        return date_obj.strftime('%d-%m-%Y')
                    except ValueError:
                        continue
            return str(date_str)
        except:
            return str(date_str)
    
    def update_dc_pdf_path(self, dc_number, pdf_path):
        """Update DC record with PDF path"""
        try:
            conn = sqlite3.connect('instance/medivent.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE delivery_challans 
                SET pdf_path = ? 
                WHERE dc_number = ?
            ''', (pdf_path, dc_number))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Error updating PDF path: {e}")

# Convenience function
def generate_dc_pdf(dc_number):
    """Generate PDF for a delivery challan"""
    generator = DCPDFGenerator()
    return generator.generate_pdf(dc_number)
