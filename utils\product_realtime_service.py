#!/usr/bin/env python3
"""
Product Real-time Service
Centralized service for real-time product data synchronization across all ERP components
"""

import logging
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from .unified_division_manager import get_unified_division_manager
from .product_validator import get_product_validator

class ProductRealtimeService:
    """
    Centralized service for real-time product data synchronization
    Ensures consistent product information across all ERP components
    """
    
    def __init__(self, db_connection):
        self.db = db_connection
        self.logger = logging.getLogger(__name__)
        self._cache = {}
        self._cache_timeout = 30  # 30 seconds for real-time updates
        
    def get_active_products_count(self) -> int:
        """Get count of active products with valid divisions"""
        cache_key = 'active_products_count'
        
        # Check cache first
        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]['data']
        
        try:
            # Get products with valid divisions only
            query = '''
                SELECT COUNT(*) as count
                FROM products p
                JOIN divisions d ON p.division_id = d.division_id
                WHERE d.is_active = 1 AND d.status = 'active'
            '''
            
            cursor = self.db.execute(query)
            count = cursor.fetchone()['count']
            
            # Cache the result
            self._cache[cache_key] = {
                'data': count,
                'timestamp': datetime.now()
            }
            
            return count
            
        except Exception as e:
            self.logger.error(f"Error getting active products count: {e}")
            return 0
    
    def get_products_for_dropdowns(self) -> List[Dict]:
        """Get active products formatted for form dropdowns"""
        cache_key = 'products_dropdown'
        
        # Check cache first
        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]['data']
        
        try:
            # Get products with valid divisions and inventory - ONLY ACTIVE PRODUCTS
            query = '''
                SELECT DISTINCT p.product_id, p.name, p.strength, p.category,
                       p.unit_price, p.unit_of_measure,
                       d.name as division_name, d.category as division_category,
                       COALESCE(SUM(i.stock_quantity - i.allocated_quantity), 0) as available_stock
                FROM products p
                JOIN divisions d ON p.division_id = d.division_id
                LEFT JOIN inventory i ON p.product_id = i.product_id AND i.status = 'active'
                WHERE d.is_active = 1 AND d.status = 'active'
                  AND p.status = 'active'
                  AND p.is_active = 1
                GROUP BY p.product_id, p.name, p.strength, p.category, p.unit_price,
                         p.unit_of_measure, d.name, d.category
                ORDER BY p.name, p.strength
            '''
            
            cursor = self.db.execute(query)
            products = cursor.fetchall()
            
            # Format for dropdown usage
            dropdown_products = []
            for product in products:
                dropdown_products.append({
                    'id': product['product_id'],
                    'product_id': product['product_id'],
                    'name': product['name'],
                    'strength': product['strength'] or '',
                    'display_name': f"{product['name']} ({product['strength'] or 'No Strength'})",
                    'category': product['category'] or 'Uncategorized',
                    'unit_price': float(product['unit_price'] or 0),
                    'unit_of_measure': product['unit_of_measure'] or 'Unit',
                    'division_name': product['division_name'],
                    'division_category': product['division_category'],
                    'available_stock': int(product['available_stock'] or 0),
                    'in_stock': int(product['available_stock'] or 0) > 0
                })
            
            # Cache the result
            self._cache[cache_key] = {
                'data': dropdown_products,
                'timestamp': datetime.now()
            }
            
            return dropdown_products
            
        except Exception as e:
            self.logger.error(f"Error getting products for dropdowns: {e}")
            return []
    
    def get_products_by_division(self, division_id: str) -> List[Dict]:
        """Get products filtered by specific division"""
        cache_key = f'products_division_{division_id}'
        
        # Check cache first
        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]['data']
        
        try:
            query = '''
                SELECT p.*, d.name as division_name,
                       COALESCE(SUM(i.stock_quantity - i.allocated_quantity), 0) as available_stock
                FROM products p
                JOIN divisions d ON p.division_id = d.division_id
                LEFT JOIN inventory i ON p.product_id = i.product_id AND i.status = 'active'
                WHERE p.division_id = ? AND d.is_active = 1 AND d.status = 'active'
                GROUP BY p.product_id
                ORDER BY p.name
            '''
            
            cursor = self.db.execute(query, (division_id,))
            products = [dict(row) for row in cursor.fetchall()]
            
            # Cache the result
            self._cache[cache_key] = {
                'data': products,
                'timestamp': datetime.now()
            }
            
            return products
            
        except Exception as e:
            self.logger.error(f"Error getting products by division {division_id}: {e}")
            return []
    
    def get_products_with_inventory(self) -> List[Dict]:
        """Get products that have available inventory - ONLY ACTIVE PRODUCTS"""
        cache_key = 'products_with_inventory'

        # Check cache first
        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]['data']

        try:
            query = '''
                SELECT p.*, d.name as division_name,
                       SUM(i.stock_quantity - i.allocated_quantity) as available_stock,
                       COUNT(i.inventory_id) as inventory_entries
                FROM products p
                JOIN divisions d ON p.division_id = d.division_id
                JOIN inventory i ON p.product_id = i.product_id
                WHERE d.is_active = 1 AND d.status = 'active'
                  AND i.status = 'active'
                  AND p.status = 'active'
                  AND p.is_active = 1
                  AND (i.stock_quantity - i.allocated_quantity) > 0
                GROUP BY p.product_id
                HAVING available_stock > 0
                ORDER BY p.name
            '''
            
            cursor = self.db.execute(query)
            products = [dict(row) for row in cursor.fetchall()]
            
            # Cache the result
            self._cache[cache_key] = {
                'data': products,
                'timestamp': datetime.now()
            }
            
            return products
            
        except Exception as e:
            self.logger.error(f"Error getting products with inventory: {e}")
            return []
    
    def get_product_analytics(self) -> Dict:
        """Get product analytics and statistics"""
        cache_key = 'product_analytics'
        
        # Check cache first
        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]['data']
        
        try:
            # Get comprehensive product statistics
            analytics = {}
            
            # Total products count
            total_count = self.db.execute('''
                SELECT COUNT(*) as count FROM products p
                JOIN divisions d ON p.division_id = d.division_id
                WHERE d.is_active = 1 AND d.status = 'active'
            ''').fetchone()['count']
            
            # Products with inventory
            with_inventory = self.db.execute('''
                SELECT COUNT(DISTINCT p.product_id) as count FROM products p
                JOIN divisions d ON p.division_id = d.division_id
                JOIN inventory i ON p.product_id = i.product_id
                WHERE d.is_active = 1 AND d.status = 'active' 
                  AND i.status = 'active' AND i.stock_quantity > 0
            ''').fetchone()['count']
            
            # Low stock products
            low_stock = self.db.execute('''
                SELECT COUNT(DISTINCT p.product_id) as count FROM products p
                JOIN divisions d ON p.division_id = d.division_id
                LEFT JOIN inventory i ON p.product_id = i.product_id AND i.status = 'active'
                WHERE d.is_active = 1 AND d.status = 'active'
                GROUP BY p.product_id
                HAVING COALESCE(SUM(i.stock_quantity), 0) <= p.min_stock_level
            ''').fetchone()
            low_stock_count = low_stock['count'] if low_stock else 0
            
            # Categories count
            categories = self.db.execute('''
                SELECT COUNT(DISTINCT p.category) as count FROM products p
                JOIN divisions d ON p.division_id = d.division_id
                WHERE d.is_active = 1 AND d.status = 'active' AND p.category IS NOT NULL
            ''').fetchone()['count']
            
            analytics = {
                'total_products': total_count,
                'products_with_inventory': with_inventory,
                'low_stock_products': low_stock_count,
                'total_categories': categories,
                'inventory_coverage': (with_inventory / total_count * 100) if total_count > 0 else 0,
                'last_updated': datetime.now().isoformat()
            }
            
            # Cache the result
            self._cache[cache_key] = {
                'data': analytics,
                'timestamp': datetime.now()
            }
            
            return analytics
            
        except Exception as e:
            self.logger.error(f"Error getting product analytics: {e}")
            return {
                'total_products': 0,
                'products_with_inventory': 0,
                'low_stock_products': 0,
                'total_categories': 0,
                'inventory_coverage': 0,
                'last_updated': datetime.now().isoformat()
            }
    
    def invalidate_all_caches(self) -> None:
        """Invalidate all cached product data"""
        self._cache.clear()
        self.logger.info("All product caches invalidated")
    
    def invalidate_cache(self, cache_key: str) -> None:
        """Invalidate specific cache entry"""
        if cache_key in self._cache:
            del self._cache[cache_key]
            self.logger.info(f"Product cache invalidated: {cache_key}")
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cache entry is valid and not expired"""
        if cache_key not in self._cache:
            return False
        
        cache_entry = self._cache[cache_key]
        age = datetime.now() - cache_entry['timestamp']
        
        return age.total_seconds() < self._cache_timeout


# Service instance management
def get_product_realtime_service(db_connection):
    """Factory function to get product realtime service instance"""
    # Create a new instance for each request to avoid stale cache
    return ProductRealtimeService(db_connection)

def get_active_products_count_realtime(db_connection) -> int:
    """Quick function to get active products count"""
    service = get_product_realtime_service(db_connection)
    return service.get_active_products_count()

def get_products_for_forms_realtime(db_connection) -> List[Dict]:
    """Quick function to get products for form dropdowns"""
    service = get_product_realtime_service(db_connection)
    return service.get_products_for_dropdowns()

def get_products_with_inventory_realtime(db_connection) -> List[Dict]:
    """Quick function to get products with available inventory"""
    service = get_product_realtime_service(db_connection)
    return service.get_products_with_inventory()

def get_product_analytics_realtime(db_connection) -> Dict:
    """Quick function to get product analytics"""
    service = get_product_realtime_service(db_connection)
    return service.get_product_analytics()

def invalidate_product_caches(db_connection) -> None:
    """Quick function to invalidate all product caches"""
    service = get_product_realtime_service(db_connection)
    service.invalidate_all_caches()
