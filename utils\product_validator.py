#!/usr/bin/env python3
"""
Product Validation Utilities
Comprehensive validation system for product registration and management
"""

import sqlite3
from typing import List, Dict, Optional, Tuple
from datetime import datetime
from .division_validator import DivisionValidator


class ProductValidator:
    """Comprehensive product validation and management utilities"""
    
    def __init__(self, db_connection):
        self.db = db_connection
        self.division_validator = DivisionValidator(db_connection)
        
    def validate_product_registration_data(self, product_data: Dict) -> Tuple[bool, List[str]]:
        """Validate all product registration data"""
        errors = []
        
        # Required fields validation
        required_fields = ['name', 'division_id']
        for field in required_fields:
            if not product_data.get(field):
                errors.append(f"{field.replace('_', ' ').title()} is required")
        
        # Division validation
        division_id = product_data.get('division_id')
        if division_id:
            is_valid, division_info = self.division_validator.validate_division_exists(division_id)
            if not is_valid:
                errors.append(f"Division ID {division_id} does not exist or is not active")
        
        # Product name uniqueness
        product_name = product_data.get('name', '').strip()
        if product_name and self.is_product_name_duplicate(product_name, product_data.get('product_id')):
            errors.append(f"Product name '{product_name}' already exists")
        
        # Product ID uniqueness (if provided)
        product_id = product_data.get('product_id', '').strip()
        if product_id and self.is_product_id_duplicate(product_id):
            errors.append(f"Product ID '{product_id}' already exists")
        
        # Unit price validation
        unit_price = product_data.get('unit_price')
        if unit_price is not None:
            try:
                price = float(unit_price)
                if price < 0:
                    errors.append("Unit price cannot be negative")
            except (ValueError, TypeError):
                errors.append("Unit price must be a valid number")
        
        # Minimum stock level validation
        min_stock = product_data.get('min_stock_level')
        if min_stock is not None:
            try:
                stock = int(min_stock)
                if stock < 0:
                    errors.append("Minimum stock level cannot be negative")
            except (ValueError, TypeError):
                errors.append("Minimum stock level must be a valid integer")
        
        return len(errors) == 0, errors
    
    def is_product_name_duplicate(self, product_name: str, exclude_product_id: Optional[str] = None) -> bool:
        """Check if product name already exists"""
        try:
            if exclude_product_id:
                cursor = self.db.execute('''
                    SELECT COUNT(*) FROM products 
                    WHERE LOWER(name) = LOWER(?) AND product_id != ?
                ''', (product_name, exclude_product_id))
            else:
                cursor = self.db.execute('''
                    SELECT COUNT(*) FROM products 
                    WHERE LOWER(name) = LOWER(?)
                ''', (product_name,))
            
            return cursor.fetchone()[0] > 0
            
        except Exception as e:
            print(f"Error checking product name duplicate: {e}")
            return False
    
    def is_product_id_duplicate(self, product_id: str) -> bool:
        """Check if product ID already exists"""
        try:
            cursor = self.db.execute('''
                SELECT COUNT(*) FROM products 
                WHERE product_id = ?
            ''', (product_id,))
            
            return cursor.fetchone()[0] > 0
            
        except Exception as e:
            print(f"Error checking product ID duplicate: {e}")
            return False
    
    def get_products_by_division(self, division_id: int) -> List[Dict]:
        """Get all products for a specific division"""
        try:
            cursor = self.db.execute('''
                SELECT p.product_id, p.name, p.strength, p.manufacturer, 
                       p.category, p.unit_price, p.min_stock_level,
                       p.description, p.created_at, p.updated_at,
                       d.name as division_name
                FROM products p
                JOIN divisions d ON p.division_id = d.division_id
                WHERE p.division_id = ? AND d.is_active = 1
                ORDER BY p.name
            ''', (division_id,))
            
            products = []
            for row in cursor.fetchall():
                products.append({
                    'product_id': row[0],
                    'name': row[1],
                    'strength': row[2] or '',
                    'manufacturer': row[3] or '',
                    'category': row[4] or '',
                    'unit_price': row[5] or 0.0,
                    'min_stock_level': row[6] or 0,
                    'description': row[7] or '',
                    'created_at': row[8],
                    'updated_at': row[9],
                    'division_name': row[10]
                })
            
            return products
            
        except Exception as e:
            print(f"Error fetching products for division {division_id}: {e}")
            return []
    
    def get_products_with_valid_divisions(self) -> List[Dict]:
        """Get all products that have valid division references - ONLY ACTIVE PRODUCTS"""
        try:
            cursor = self.db.execute('''
                SELECT p.product_id, p.name, p.strength, p.manufacturer,
                       p.category, p.unit_price, p.division_id,
                       d.name as division_name, d.category as division_category
                FROM products p
                JOIN divisions d ON p.division_id = d.division_id
                WHERE LOWER(d.status) = 'active'
                  AND p.status = 'active'
                  AND p.is_active = 1
                ORDER BY d.name, p.name
            ''')
            
            products = []
            for row in cursor.fetchall():
                products.append({
                    'product_id': row[0],
                    'name': row[1],
                    'strength': row[2] or '',
                    'manufacturer': row[3] or '',
                    'category': row[4] or '',
                    'unit_price': row[5] or 0.0,
                    'division_id': row[6],
                    'division_name': row[7],
                    'division_category': row[8] or 'General'
                })
            
            return products
            
        except Exception as e:
            print(f"Error fetching products with valid divisions: {e}")
            return []
    
    def validate_product_exists(self, product_id: str) -> Tuple[bool, Optional[Dict]]:
        """Validate that a product exists and has valid division"""
        try:
            cursor = self.db.execute('''
                SELECT p.product_id, p.name, p.unit_price, p.division_id,
                       d.name as division_name, d.status as division_status
                FROM products p
                LEFT JOIN divisions d ON p.division_id = d.division_id
                WHERE p.product_id = ?
            ''', (product_id,))
            
            row = cursor.fetchone()
            if row:
                # Check if division is valid (case-insensitive)
                division_status = row[5].lower() if row[5] else ''
                division_valid = division_status == 'active'
                
                return True, {
                    'product_id': row[0],
                    'name': row[1],
                    'unit_price': row[2] or 0.0,
                    'division_id': row[3],
                    'division_name': row[4] or 'No Division',
                    'division_valid': division_valid,
                    'can_create_inventory': division_valid
                }
            else:
                return False, None
                
        except Exception as e:
            print(f"Error validating product {product_id}: {e}")
            return False, None
    
    def get_products_for_inventory_creation(self) -> List[Dict]:
        """Get products that are eligible for inventory creation - ONLY ACTIVE PRODUCTS"""
        try:
            cursor = self.db.execute('''
                SELECT p.product_id, p.name, p.strength, p.manufacturer,
                       p.unit_price, d.name as division_name
                FROM products p
                JOIN divisions d ON p.division_id = d.division_id
                WHERE LOWER(d.status) = 'active'
                  AND p.status = 'active'
                  AND p.is_active = 1
                ORDER BY d.name, p.name
            ''')
            
            products = []
            for row in cursor.fetchall():
                products.append({
                    'product_id': row[0],
                    'name': row[1],
                    'strength': row[2] or '',
                    'manufacturer': row[3] or '',
                    'unit_price': row[4] or 0.0,
                    'division_name': row[5],
                    'display_name': f"{row[1]} ({row[2] or 'No Strength'}) - {row[5]}"
                })
            
            return products
            
        except Exception as e:
            print(f"Error fetching products for inventory creation: {e}")
            return []
    
    def get_products_for_order_placement(self) -> List[Dict]:
        """Get products with inventory that are eligible for order placement - ONLY ACTIVE PRODUCTS"""
        try:
            cursor = self.db.execute('''
                SELECT DISTINCT p.product_id, p.name, p.strength, p.manufacturer,
                       p.unit_price, d.name as division_name,
                       COALESCE(SUM(i.stock_quantity - i.allocated_quantity), 0) as available_stock
                FROM products p
                JOIN divisions d ON p.division_id = d.division_id
                LEFT JOIN inventory i ON p.product_id = i.product_id AND i.status = 'active'
                WHERE d.status = 'Active'
                  AND p.status = 'active'
                  AND p.is_active = 1
                GROUP BY p.product_id, p.name, p.strength, p.manufacturer, p.unit_price, d.name
                HAVING available_stock > 0
                ORDER BY d.name, p.name
            ''')
            
            products = []
            for row in cursor.fetchall():
                products.append({
                    'product_id': row[0],
                    'name': row[1],
                    'strength': row[2] or '',
                    'manufacturer': row[3] or '',
                    'unit_price': row[4] or 0.0,
                    'division_name': row[5],
                    'available_stock': int(row[6]),
                    'display_name': f"{row[1]} ({row[2] or 'No Strength'}) - {row[5]} [Stock: {int(row[6])}]"
                })
            
            return products
            
        except Exception as e:
            print(f"Error fetching products for order placement: {e}")
            return []
    
    def generate_product_id(self, division_id: int) -> str:
        """Generate a unique product ID based on division"""
        try:
            # Get division info
            is_valid, division_info = self.division_validator.validate_division_exists(division_id)
            if not is_valid:
                raise ValueError(f"Invalid division ID: {division_id}")
            
            # Get division name prefix (first 3 characters)
            division_name = division_info['name']
            prefix = ''.join(c.upper() for c in division_name if c.isalpha())[:3]
            if len(prefix) < 3:
                prefix = prefix.ljust(3, 'X')
            
            # Find next sequence number
            cursor = self.db.execute('''
                SELECT product_id FROM products 
                WHERE product_id LIKE ? 
                ORDER BY product_id DESC 
                LIMIT 1
            ''', (f"{prefix}%",))
            
            row = cursor.fetchone()
            if row:
                # Extract number from existing ID
                existing_id = row[0]
                try:
                    number = int(existing_id[3:]) + 1
                except (ValueError, IndexError):
                    number = 1
            else:
                number = 1
            
            return f"{prefix}{number:04d}"
            
        except Exception as e:
            print(f"Error generating product ID: {e}")
            return f"PRD{datetime.now().strftime('%Y%m%d%H%M%S')}"
    
    def get_product_summary(self) -> Dict:
        """Get comprehensive product system summary"""
        try:
            # Count total products
            cursor = self.db.execute('SELECT COUNT(*) FROM products')
            total_products = cursor.fetchone()[0]
            
            # Count products with valid divisions
            cursor = self.db.execute('''
                SELECT COUNT(*) FROM products p
                JOIN divisions d ON p.division_id = d.division_id
                WHERE d.status = 'Active'
            ''')
            products_with_valid_divisions = cursor.fetchone()[0]
            
            # Count products with inventory
            cursor = self.db.execute('''
                SELECT COUNT(DISTINCT p.product_id) FROM products p
                JOIN inventory i ON p.product_id = i.product_id
                WHERE i.stock_quantity > 0
            ''')
            products_with_inventory = cursor.fetchone()[0]
            
            # Count orphaned products
            orphaned_products = len(self.division_validator.get_orphaned_products())
            
            return {
                'total_products': total_products,
                'products_with_valid_divisions': products_with_valid_divisions,
                'products_with_inventory': products_with_inventory,
                'orphaned_products': orphaned_products,
                'system_health': 'Good' if orphaned_products == 0 else 'Needs Attention'
            }
            
        except Exception as e:
            print(f"Error generating product summary: {e}")
            return {
                'total_products': 0,
                'products_with_valid_divisions': 0,
                'products_with_inventory': 0,
                'orphaned_products': 0,
                'system_health': 'Error'
            }


def get_product_validator(db_connection):
    """Factory function to create product validator instance"""
    return ProductValidator(db_connection)


# Utility functions for Flask routes
def validate_product_for_route(db, product_id):
    """Quick validation function for Flask routes"""
    validator = ProductValidator(db)
    return validator.validate_product_exists(product_id)


def get_products_for_forms(db, division_id=None):
    """Get products formatted for form dropdowns"""
    validator = ProductValidator(db)
    
    if division_id:
        products = validator.get_products_by_division(division_id)
    else:
        products = validator.get_products_with_valid_divisions()
    
    return [
        {
            'id': prod['product_id'],
            'name': prod['name'],
            'display_name': f"{prod['name']} ({prod.get('strength', 'No Strength')})"
        }
        for prod in products
    ]
