"""
QR Code Generation Service for Medivent ERP System
Generates QR codes with comprehensive order information
"""

import qrcode
import json
import os
import base64
from io import BytesIO
from datetime import datetime
from PIL import Image, ImageDraw, ImageFont


class OrderQRCodeGenerator:
    """Generate QR codes for orders with comprehensive information"""
    
    def __init__(self):
        self.company_info = {
            'name': 'Medivent Pharmaceuticals',
            'address': 'Karachi, Pakistan',
            'phone': '+92-21-XXXXXXX',
            'email': '<EMAIL>',
            'website': 'www.medivent.com'
        }
        
        # Ensure QR codes directory exists
        self.qr_dir = os.path.join('static', 'qr_codes')
        os.makedirs(self.qr_dir, exist_ok=True)
    
    def generate_order_qr_data(self, order_data):
        """
        Generate comprehensive QR code data from order information
        
        Args:
            order_data: Dictionary containing order, items, customer, and company info
            
        Returns:
            Dictionary with structured QR code data
        """
        try:
            order = order_data.get('order', {})
            items = order_data.get('items', [])
            foc_items = order_data.get('foc_items', [])
            customer = order_data.get('customer', {})
            summary = order_data.get('summary', {})
            
            # Create structured QR data
            qr_data = {
                'type': 'MEDIVENT_ORDER',
                'version': '1.0',
                'generated': datetime.now().isoformat(),
                'order': {
                    'id': order.get('order_id', ''),
                    'date': order.get('order_date_formatted', ''),
                    'status': order.get('status', ''),
                    'amount': float(order.get('order_amount', 0))
                },
                'customer': {
                    'name': order.get('customer_name', ''),
                    'phone': order.get('customer_phone', ''),
                    'address': order.get('customer_address', ''),
                    'city': customer.get('city', '') if customer else ''
                },
                'items': [],
                'foc_items': [],
                'summary': {
                    'total_amount': float(order.get('order_amount', 0)),
                    'total_items': summary.get('total_items', 0),
                    'foc_count': summary.get('foc_items_count', 0),
                    'item_types': summary.get('item_types', 0)
                },
                'company': self.company_info
            }
            
            # Add regular items
            for item in items[:10]:  # Limit to first 10 items to keep QR code readable
                qr_data['items'].append({
                    'product': item.get('product_name', ''),
                    'strength': item.get('strength', ''),
                    'qty': float(item.get('quantity', 0)),
                    'price': float(item.get('unit_price', 0)),
                    'total': float(item.get('quantity', 0)) * float(item.get('unit_price', 0))
                })
            
            # Add FOC items
            for item in foc_items[:5]:  # Limit FOC items
                qr_data['foc_items'].append({
                    'product': item.get('product_name', ''),
                    'strength': item.get('strength', ''),
                    'qty': float(item.get('quantity', 0))
                })
            
            return qr_data
            
        except Exception as e:
            print(f"Error generating QR data: {e}")
            return None
    
    def create_qr_code(self, data, order_id):
        """
        Create QR code image from data
        
        Args:
            data: Dictionary with QR code data
            order_id: Order ID for filename
            
        Returns:
            Tuple of (image_path, base64_string, qr_instance)
        """
        try:
            # Convert data to JSON string
            json_data = json.dumps(data, separators=(',', ':'), ensure_ascii=False)
            
            # Create QR code instance
            qr = qrcode.QRCode(
                version=None,  # Auto-determine version based on data
                error_correction=qrcode.constants.ERROR_CORRECT_M,  # Medium error correction
                box_size=8,
                border=4,
            )
            
            # Add data and optimize
            qr.add_data(json_data)
            qr.make(fit=True)
            
            # Create QR code image
            qr_img = qr.make_image(fill_color="black", back_color="white")
            
            # Save to file
            filename = f"order_{order_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            file_path = os.path.join(self.qr_dir, filename)
            qr_img.save(file_path)
            
            # Convert to base64 for web display
            buffer = BytesIO()
            qr_img.save(buffer, format='PNG')
            img_base64 = base64.b64encode(buffer.getvalue()).decode()
            
            return file_path, img_base64, qr
            
        except Exception as e:
            print(f"Error creating QR code: {e}")
            return None, None, None
    
    def create_enhanced_qr_with_branding(self, data, order_id):
        """
        Create QR code with company branding and additional information
        
        Args:
            data: Dictionary with QR code data
            order_id: Order ID for filename
            
        Returns:
            Tuple of (image_path, base64_string)
        """
        try:
            # Create basic QR code
            file_path, img_base64, qr = self.create_qr_code(data, order_id)
            
            if not file_path:
                return None, None
            
            # Load the QR code image
            qr_img = Image.open(file_path)
            
            # Create a larger canvas for branding
            canvas_width = qr_img.width + 100
            canvas_height = qr_img.height + 150
            
            # Create new image with white background
            branded_img = Image.new('RGB', (canvas_width, canvas_height), 'white')
            
            # Paste QR code in center
            qr_x = (canvas_width - qr_img.width) // 2
            qr_y = 80
            branded_img.paste(qr_img, (qr_x, qr_y))
            
            # Add text information
            draw = ImageDraw.Draw(branded_img)
            
            try:
                # Try to use a better font
                title_font = ImageFont.truetype("arial.ttf", 16)
                info_font = ImageFont.truetype("arial.ttf", 12)
                small_font = ImageFont.truetype("arial.ttf", 10)
            except:
                # Fallback to default font
                title_font = ImageFont.load_default()
                info_font = ImageFont.load_default()
                small_font = ImageFont.load_default()
            
            # Company name at top
            company_text = self.company_info['name']
            bbox = draw.textbbox((0, 0), company_text, font=title_font)
            text_width = bbox[2] - bbox[0]
            draw.text(((canvas_width - text_width) // 2, 20), company_text, 
                     fill='black', font=title_font)
            
            # Order information at top
            order_text = f"Order: {data['order']['id']} | {data['order']['date']}"
            bbox = draw.textbbox((0, 0), order_text, font=info_font)
            text_width = bbox[2] - bbox[0]
            draw.text(((canvas_width - text_width) // 2, 45), order_text, 
                     fill='black', font=info_font)
            
            # Customer and amount at bottom
            customer_text = f"Customer: {data['customer']['name']}"
            amount_text = f"Amount: Rs.{data['summary']['total_amount']:,.2f}"
            
            y_pos = qr_y + qr_img.height + 20
            draw.text((20, y_pos), customer_text, fill='black', font=info_font)
            draw.text((20, y_pos + 20), amount_text, fill='black', font=info_font)
            
            # QR info at bottom
            qr_info = f"QR Version: {qr.version} | Items: {data['summary']['item_types']}"
            bbox = draw.textbbox((0, 0), qr_info, font=small_font)
            text_width = bbox[2] - bbox[0]
            draw.text(((canvas_width - text_width) // 2, y_pos + 50), qr_info, 
                     fill='gray', font=small_font)
            
            # Save branded image
            branded_filename = f"branded_order_{order_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            branded_path = os.path.join(self.qr_dir, branded_filename)
            branded_img.save(branded_path)
            
            # Convert to base64
            buffer = BytesIO()
            branded_img.save(buffer, format='PNG')
            branded_base64 = base64.b64encode(buffer.getvalue()).decode()
            
            return branded_path, branded_base64
            
        except Exception as e:
            print(f"Error creating branded QR code: {e}")
            return file_path, img_base64  # Return basic QR if branding fails
    
    def generate_order_qr_code(self, order_data, include_branding=True):
        """
        Main method to generate QR code for an order
        
        Args:
            order_data: Complete order data from API
            include_branding: Whether to include company branding
            
        Returns:
            Dictionary with QR code information
        """
        try:
            order_id = order_data.get('order', {}).get('order_id', 'unknown')
            
            # Generate QR data
            qr_data = self.generate_order_qr_data(order_data)
            if not qr_data:
                return {'success': False, 'error': 'Failed to generate QR data'}
            
            # Create QR code
            if include_branding:
                file_path, base64_img = self.create_enhanced_qr_with_branding(qr_data, order_id)
            else:
                file_path, base64_img, _ = self.create_qr_code(qr_data, order_id)
            
            if not file_path:
                return {'success': False, 'error': 'Failed to create QR code image'}
            
            # Generate human-readable summary
            summary_text = self.generate_qr_summary(qr_data)
            
            return {
                'success': True,
                'qr_code': {
                    'file_path': file_path,
                    'base64': base64_img,
                    'data': qr_data,
                    'summary': summary_text,
                    'size': os.path.getsize(file_path) if os.path.exists(file_path) else 0
                }
            }
            
        except Exception as e:
            print(f"Error generating order QR code: {e}")
            return {'success': False, 'error': str(e)}
    
    def generate_qr_summary(self, qr_data):
        """Generate human-readable summary of QR code contents"""
        try:
            order = qr_data.get('order', {})
            customer = qr_data.get('customer', {})
            summary = qr_data.get('summary', {})
            
            summary_lines = [
                f"Order ID: {order.get('id', 'N/A')}",
                f"Date: {order.get('date', 'N/A')}",
                f"Customer: {customer.get('name', 'N/A')}",
                f"Total Amount: Rs.{summary.get('total_amount', 0):,.2f}",
                f"Items: {summary.get('item_types', 0)} types, {summary.get('total_items', 0)} total",
            ]
            
            if summary.get('foc_count', 0) > 0:
                summary_lines.append(f"FOC Items: {summary.get('foc_count', 0)}")
            
            summary_lines.append(f"Generated: {qr_data.get('generated', 'N/A')}")
            
            return '\n'.join(summary_lines)
            
        except Exception as e:
            return f"Error generating summary: {e}"


# Convenience function for easy import
def generate_order_qr_code(order_data, include_branding=True):
    """
    Convenience function to generate QR code for an order
    
    Args:
        order_data: Complete order data from API
        include_branding: Whether to include company branding
        
    Returns:
        Dictionary with QR code information
    """
    generator = OrderQRCodeGenerator()
    return generator.generate_order_qr_code(order_data, include_branding)
