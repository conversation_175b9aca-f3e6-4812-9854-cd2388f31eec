"""
Session Manager for User Activity Tracking
Medivent ERP System
"""

import sqlite3
import uuid
import json
from datetime import datetime, timedelta
from flask import g, current_app, request, session
from flask_login import current_user

class SessionManager:
    """Manages user sessions and activity tracking"""
    
    def __init__(self):
        self.session_timeout = 15 * 60  # 15 minutes in seconds
        self.warning_time = 2 * 60      # 2 minutes warning before logout
    
    def get_db(self):
        """Get database connection"""
        if 'db' not in g:
            g.db = sqlite3.connect(current_app.config['DATABASE'])
            g.db.row_factory = sqlite3.Row
        return g.db
    
    def create_session(self, user_id, username, ip_address=None, user_agent=None):
        """Create a new user session"""
        try:
            session_id = str(uuid.uuid4())
            db = self.get_db()
            
            # Create session record
            db.execute('''
                INSERT INTO user_sessions (
                    session_id, user_id, username, login_time, last_activity,
                    ip_address, user_agent, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                session_id, user_id, username, datetime.now(), datetime.now(),
                ip_address or request.remote_addr,
                user_agent or request.headers.get('User-Agent', ''),
                'active'
            ))
            
            db.commit()
            
            # Store session ID in Flask session
            session['activity_session_id'] = session_id
            session['last_activity'] = datetime.now().isoformat()
            
            return session_id
            
        except Exception as e:
            current_app.logger.error(f"Error creating session: {e}")
            return None
    
    def update_activity(self, session_id=None, activity_type='heartbeat', page_url=None, duration=0, metadata=None):
        """Update user activity and session timestamp"""
        try:
            if not session_id:
                session_id = session.get('activity_session_id')
            
            if not session_id:
                return False
            
            db = self.get_db()
            current_time = datetime.now()
            
            # Update session last activity
            db.execute('''
                UPDATE user_sessions 
                SET last_activity = ? 
                WHERE session_id = ? AND status = 'active'
            ''', (current_time, session_id))
            
            # Log activity if it's not just a heartbeat
            if activity_type != 'heartbeat':
                db.execute('''
                    INSERT INTO user_activity_tracking (
                        session_id, user_id, username, activity_type, 
                        page_url, timestamp, duration, metadata
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    session_id,
                    current_user.id if current_user.is_authenticated else None,
                    current_user.username if current_user.is_authenticated else 'anonymous',
                    activity_type,
                    page_url or request.path,
                    current_time,
                    duration,
                    json.dumps(metadata) if metadata else None
                ))
            
            db.commit()
            
            # Update Flask session
            session['last_activity'] = current_time.isoformat()
            
            return True
            
        except Exception as e:
            current_app.logger.error(f"Error updating activity: {e}")
            return False
    
    def check_session_timeout(self, session_id=None):
        """Check if session has timed out"""
        try:
            if not session_id:
                session_id = session.get('activity_session_id')
            
            if not session_id:
                return True  # No session = timed out
            
            db = self.get_db()
            
            # Get session info
            session_data = db.execute('''
                SELECT last_activity, status 
                FROM user_sessions 
                WHERE session_id = ?
            ''', (session_id,)).fetchone()
            
            if not session_data or session_data['status'] != 'active':
                return True
            
            # Check if session has timed out
            last_activity = datetime.fromisoformat(session_data['last_activity'])
            time_since_activity = (datetime.now() - last_activity).total_seconds()
            
            return time_since_activity > self.session_timeout
            
        except Exception as e:
            current_app.logger.error(f"Error checking session timeout: {e}")
            return True
    
    def get_session_status(self, session_id=None):
        """Get current session status and remaining time"""
        try:
            if not session_id:
                session_id = session.get('activity_session_id')
            
            if not session_id:
                return {'status': 'no_session', 'remaining_time': 0}
            
            db = self.get_db()
            
            session_data = db.execute('''
                SELECT last_activity, status 
                FROM user_sessions 
                WHERE session_id = ?
            ''', (session_id,)).fetchone()
            
            if not session_data:
                return {'status': 'invalid_session', 'remaining_time': 0}
            
            if session_data['status'] != 'active':
                return {'status': 'inactive', 'remaining_time': 0}
            
            # Calculate remaining time
            last_activity = datetime.fromisoformat(session_data['last_activity'])
            time_since_activity = (datetime.now() - last_activity).total_seconds()
            remaining_time = max(0, self.session_timeout - time_since_activity)
            
            status = 'active'
            if remaining_time <= self.warning_time:
                status = 'warning'
            elif remaining_time <= 0:
                status = 'expired'
            
            return {
                'status': status,
                'remaining_time': int(remaining_time),
                'warning_time': self.warning_time,
                'session_timeout': self.session_timeout
            }
            
        except Exception as e:
            current_app.logger.error(f"Error getting session status: {e}")
            return {'status': 'error', 'remaining_time': 0}
    
    def end_session(self, session_id=None, reason='logout'):
        """End a user session"""
        try:
            if not session_id:
                session_id = session.get('activity_session_id')
            
            if not session_id:
                return False
            
            db = self.get_db()
            current_time = datetime.now()
            
            # Get session start time to calculate duration
            session_data = db.execute('''
                SELECT login_time, total_screen_time, total_active_time 
                FROM user_sessions 
                WHERE session_id = ?
            ''', (session_id,)).fetchone()
            
            if session_data:
                login_time = datetime.fromisoformat(session_data['login_time'])
                session_duration = int((current_time - login_time).total_seconds())
                
                # Update session with end time and duration
                db.execute('''
                    UPDATE user_sessions 
                    SET logout_time = ?, session_duration = ?, status = ? 
                    WHERE session_id = ?
                ''', (current_time, session_duration, 'logged_out', session_id))
                
                # Log session end activity
                db.execute('''
                    INSERT INTO user_activity_tracking (
                        session_id, user_id, username, activity_type, 
                        timestamp, metadata
                    ) VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    session_id,
                    current_user.id if current_user.is_authenticated else None,
                    current_user.username if current_user.is_authenticated else 'anonymous',
                    'session_end',
                    current_time,
                    json.dumps({'reason': reason, 'duration': session_duration})
                ))
                
                db.commit()
            
            # Clear Flask session data
            session.pop('activity_session_id', None)
            session.pop('last_activity', None)
            
            return True
            
        except Exception as e:
            current_app.logger.error(f"Error ending session: {e}")
            return False
    
    def get_user_sessions(self, user_id, limit=10):
        """Get recent sessions for a user"""
        try:
            db = self.get_db()
            
            sessions = db.execute('''
                SELECT * FROM user_sessions 
                WHERE user_id = ? 
                ORDER BY login_time DESC 
                LIMIT ?
            ''', (user_id, limit)).fetchall()
            
            return [dict(session) for session in sessions]
            
        except Exception as e:
            current_app.logger.error(f"Error getting user sessions: {e}")
            return []
    
    def get_session_activities(self, session_id, limit=50):
        """Get activities for a specific session"""
        try:
            db = self.get_db()
            
            activities = db.execute('''
                SELECT * FROM user_activity_tracking 
                WHERE session_id = ? 
                ORDER BY timestamp DESC 
                LIMIT ?
            ''', (session_id, limit)).fetchall()
            
            return [dict(activity) for activity in activities]
            
        except Exception as e:
            current_app.logger.error(f"Error getting session activities: {e}")
            return []
