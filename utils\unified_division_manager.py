"""
Unified Division Manager
Handles division synchronization across all ERP modules
Ensures consistent division filtering regardless of database schema variations
"""

import sqlite3
from typing import List, Dict, Optional, Tuple
import logging

class UnifiedDivisionManager:
    """
    Centralized division management that handles schema inconsistencies
    and ensures real-time synchronization across all modules
    """
    
    def __init__(self, db_connection):
        self.db = db_connection
        self.logger = logging.getLogger(__name__)
        self._schema_info = None
        self._analyze_schema()
    
    def _analyze_schema(self):
        """Analyze the divisions table schema to understand available columns"""
        try:
            cursor = self.db.execute("PRAGMA table_info(divisions)")
            columns = cursor.fetchall()
            
            self._schema_info = {
                'has_is_active': any(col[1] == 'is_active' for col in columns),
                'has_status': any(col[1] == 'status' for col in columns),
                'has_division_id': any(col[1] == 'division_id' for col in columns),
                'has_id': any(col[1] == 'id' for col in columns),
                'columns': [col[1] for col in columns]
            }
            
            self.logger.info(f"Division schema analyzed: {self._schema_info}")
            
        except Exception as e:
            self.logger.error(f"Schema analysis failed: {e}")
            # Fallback schema assumption
            self._schema_info = {
                'has_is_active': False,
                'has_status': True,
                'has_division_id': True,
                'has_id': False,
                'columns': ['division_id', 'name', 'status']
            }
    
    def _build_active_condition(self) -> Tuple[str, List]:
        """
        Build the WHERE condition for active divisions based on available columns
        Returns: (where_clause, parameters)
        """
        conditions = []
        params = []
        
        # Primary key handling
        if self._schema_info['has_division_id']:
            conditions.append("division_id IS NOT NULL")
        elif self._schema_info['has_id']:
            conditions.append("id IS NOT NULL")
        
        # Status filtering - prioritize is_active column for consistency with Division Management
        if self._schema_info['has_is_active']:
            # Use is_active column (same as Division Management)
            conditions.append("is_active = ?")
            params.append(1)

            # If status column exists, be flexible with status values
            if self._schema_info['has_status']:
                # Accept 'active', 'Active', or NULL status for active divisions
                conditions.append("(status = ? OR status = ? OR status IS NULL)")
                params.extend(['active', 'Active'])

        elif self._schema_info['has_status']:
            # Fallback to status column if is_active doesn't exist
            conditions.append("(status = ? OR status = ?)")
            params.extend(['active', 'Active'])
        
        # Additional safety checks
        conditions.append("name IS NOT NULL")
        conditions.append("name != ''")
        
        where_clause = " AND ".join(conditions)
        return where_clause, params
    
    def get_active_divisions(self, include_metadata: bool = False) -> List[Dict]:
        """
        Get all active divisions with unified filtering
        
        Args:
            include_metadata: Include additional metadata fields
            
        Returns:
            List of active division dictionaries
        """
        try:
            where_clause, params = self._build_active_condition()
            
            # Build SELECT clause based on available columns
            base_columns = []
            
            if self._schema_info['has_division_id']:
                base_columns.append('division_id')
            elif self._schema_info['has_id']:
                base_columns.append('id as division_id')
            
            base_columns.extend(['name', 'description'])
            
            # Add optional columns if they exist
            optional_columns = ['category', 'manager_id', 'budget', 'contact_email',
                              'contact_phone', 'location', 'created_at', 'updated_at']
            
            for col in optional_columns:
                if col in self._schema_info['columns']:
                    base_columns.append(col)
            
            if include_metadata:
                metadata_columns = ['status', 'is_active', 'code', 'target_revenue', 
                                  'achieved_revenue', 'sort_order']
                for col in metadata_columns:
                    if col in self._schema_info['columns']:
                        base_columns.append(col)
            
            select_clause = ", ".join(base_columns)
            
            query = f"""
                SELECT {select_clause}
                FROM divisions 
                WHERE {where_clause}
                ORDER BY name
            """
            
            cursor = self.db.execute(query, params)
            rows = cursor.fetchall()
            
            # Convert to dictionaries
            divisions = []
            for row in rows:
                division = {}
                for i, col_name in enumerate(base_columns):
                    # Handle aliased columns
                    key = col_name.split(' as ')[-1] if ' as ' in col_name else col_name
                    division[key] = row[i]
                
                # Ensure required fields have defaults
                division.setdefault('category', 'General')
                division.setdefault('description', '')
                division.setdefault('manager_id', '')
                division.setdefault('budget', 0.0)
                
                divisions.append(division)
            
            self.logger.info(f"Retrieved {len(divisions)} active divisions")
            return divisions
            
        except Exception as e:
            self.logger.error(f"Error fetching active divisions: {e}")
            return []
    
    def get_divisions_for_dropdown(self) -> List[Dict]:
        """
        Get divisions formatted specifically for dropdown menus
        Optimized for form usage across all modules
        """
        divisions = self.get_active_divisions()
        
        return [
            {
                'id': div['division_id'],
                'division_id': div['division_id'],  # For template compatibility
                'value': div['division_id'],
                'name': div['name'],
                'display_name': f"{div['name']} ({div.get('category', 'General')})",
                'category': div.get('category', 'General')
            }
            for div in divisions
        ]
    
    def validate_division_exists(self, division_id) -> Tuple[bool, Optional[Dict]]:
        """
        Validate that a division exists and is active
        
        Args:
            division_id: Division ID to validate
            
        Returns:
            (is_valid, division_info)
        """
        try:
            where_clause, params = self._build_active_condition()
            
            # Add division ID condition
            if self._schema_info['has_division_id']:
                where_clause += " AND division_id = ?"
            elif self._schema_info['has_id']:
                where_clause += " AND id = ?"
            
            params.append(division_id)
            
            query = f"""
                SELECT division_id, name, category, description, status
                FROM divisions 
                WHERE {where_clause}
                LIMIT 1
            """
            
            cursor = self.db.execute(query, params)
            row = cursor.fetchone()
            
            if row:
                division_info = {
                    'division_id': row[0],
                    'name': row[1],
                    'category': row[2] or 'General',
                    'description': row[3] or '',
                    'status': row[4] if len(row) > 4 else 'Active'
                }
                return True, division_info
            else:
                return False, None
                
        except Exception as e:
            self.logger.error(f"Division validation failed for ID {division_id}: {e}")
            return False, None
    
    def sync_division_status(self, division_id, new_status: str = 'inactive') -> bool:
        """
        Update division status across all relevant columns
        Ensures synchronization between status and is_active columns

        Args:
            division_id: Division to update
            new_status: New status ('active' or 'inactive')

        Returns:
            Success status
        """
        try:
            updates = []
            params = []
            
            # Update status column if it exists
            if self._schema_info['has_status']:
                updates.append("status = ?")
                params.append(new_status)
            
            # Update is_active column if it exists
            if self._schema_info['has_is_active']:
                updates.append("is_active = ?")
                params.append(1 if new_status == 'active' else 0)
            
            # Add updated timestamp if column exists
            if 'updated_at' in self._schema_info['columns']:
                updates.append("updated_at = CURRENT_TIMESTAMP")
            
            if not updates:
                self.logger.warning("No status columns found to update")
                return False
            
            # Build WHERE clause for division ID
            if self._schema_info['has_division_id']:
                where_clause = "division_id = ?"
            elif self._schema_info['has_id']:
                where_clause = "id = ?"
            else:
                self.logger.error("No primary key column found")
                return False
            
            params.append(division_id)
            
            query = f"""
                UPDATE divisions 
                SET {', '.join(updates)}
                WHERE {where_clause}
            """
            
            cursor = self.db.execute(query, params)
            self.db.commit()
            
            affected_rows = cursor.rowcount
            self.logger.info(f"Updated division {division_id} status to {new_status}, affected {affected_rows} rows")
            
            return affected_rows > 0
            
        except Exception as e:
            self.logger.error(f"Failed to sync division status for ID {division_id}: {e}")
            return False
    
    def get_schema_info(self) -> Dict:
        """Get current schema information for debugging"""
        return self._schema_info.copy()


# Utility functions for Flask routes
def get_unified_division_manager(db):
    """Factory function to create UnifiedDivisionManager instance"""
    return UnifiedDivisionManager(db)

def get_active_divisions_unified(db, include_metadata: bool = False) -> List[Dict]:
    """Get active divisions using unified manager"""
    manager = get_unified_division_manager(db)
    return manager.get_active_divisions(include_metadata)

def get_divisions_for_forms_unified(db) -> List[Dict]:
    """Get divisions formatted for form dropdowns using unified manager"""
    manager = get_unified_division_manager(db)
    return manager.get_divisions_for_dropdown()

def validate_division_unified(db, division_id) -> Tuple[bool, Optional[Dict]]:
    """Validate division using unified manager"""
    manager = get_unified_division_manager(db)
    return manager.validate_division_exists(division_id)
