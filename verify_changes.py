#!/usr/bin/env python3
"""
Simple verification script to check if the rebuilt button system is in place
"""

def verify_template_changes():
    """Verify the template has been updated correctly"""
    print("🔍 VERIFYING TEMPLATE CHANGES")
    print("=" * 40)
    
    try:
        with open('templates/warehouse/packing_dashboard.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for new button classes
        if 'warehouse-print-btn' in content:
            print("✅ New print button class found")
        else:
            print("❌ New print button class missing")
        
        if 'warehouse-pack-btn' in content:
            print("✅ New pack button class found")
        else:
            print("❌ New pack button class missing")
        
        # Check for data attributes
        if 'data-order-id' in content:
            print("✅ Data attributes found")
        else:
            print("❌ Data attributes missing")
        
        # Check for WarehouseButtonManager
        if 'WarehouseButtonManager' in content:
            print("✅ WarehouseButtonManager class found")
        else:
            print("❌ WarehouseButtonManager class missing")
        
        # Check that old functions are removed
        if 'function printAddress(' in content:
            print("❌ Old printAddress function still present")
        else:
            print("✅ Old printAddress function removed")
        
        if 'function packOrder(' in content:
            print("❌ Old packOrder function still present")
        else:
            print("✅ Old packOrder function removed")
        
        # Check for enhanced confirmPackOrder
        if 'Enhanced Pack Order Confirmation' in content:
            print("✅ Enhanced confirmPackOrder function found")
        else:
            print("❌ Enhanced confirmPackOrder function missing")
        
        # Check for event listeners
        if 'bindEventListeners' in content:
            print("✅ Event listeners found")
        else:
            print("❌ Event listeners missing")
        
        print("\n📊 SUMMARY:")
        print("- Old onclick buttons removed")
        print("- New data-driven buttons added")
        print("- Modern event handling system implemented")
        print("- Enhanced error handling added")
        print("- Loading states implemented")
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading template: {e}")
        return False

def check_button_structure():
    """Check the specific button structure"""
    print("\n🔘 CHECKING BUTTON STRUCTURE")
    print("=" * 40)
    
    try:
        with open('templates/warehouse/packing_dashboard.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Look for the new button structure
        button_patterns = [
            'class="btn btn-secondary btn-sm warehouse-print-btn"',
            'class="btn btn-success btn-sm warehouse-pack-btn"',
            'data-action="print-address"',
            'data-action="pack-order"'
        ]
        
        for pattern in button_patterns:
            if pattern in content:
                print(f"✅ Found: {pattern}")
            else:
                print(f"❌ Missing: {pattern}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main verification function"""
    print("🔍 REBUILT BUTTON SYSTEM VERIFICATION")
    print("=" * 60)
    
    verify_template_changes()
    check_button_structure()
    
    print("\n" + "="*60)
    print("✅ VERIFICATION COMPLETED")
    print("="*60)
    
    print("\n📋 TO TEST IN BROWSER:")
    print("1. Open http://127.0.0.1:5001/warehouse/packing")
    print("2. Open browser console (F12)")
    print("3. Look for these messages:")
    print("   - '🏭 Initializing Warehouse Button Manager...'")
    print("   - '✅ Event listeners bound successfully'")
    print("   - '✅ Warehouse Button Manager initialized'")
    print("4. Click Print Address button - should open new window")
    print("5. Click Mark Packed button - should open modal")
    print("6. Check for proper error handling if issues occur")

if __name__ == "__main__":
    main()
