#!/usr/bin/env python3
"""
Verify the current state of the database for invoice generation
"""

import sqlite3
import os

def verify_database_state():
    """Verify database state for invoice generation"""
    
    print("🔍 DATABASE STATE VERIFICATION")
    print("=" * 60)
    
    db_path = 'instance/medivent.db'
    if not os.path.exists(db_path):
        print(f"❌ Database not found at: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Check 1: Orders with NULL customer_id
        print("\n📋 CHECK 1: ORDERS WITH NULL CUSTOMER_ID")
        print("-" * 40)
        cursor.execute("SELECT COUNT(*) as count FROM orders WHERE customer_id IS NULL")
        null_count = cursor.fetchone()['count']
        
        if null_count == 0:
            print("✅ No orders with NULL customer_id found")
        else:
            print(f"⚠️  Found {null_count} orders with NULL customer_id")
            
            # Show sample orders
            cursor.execute("""
                SELECT order_id, customer_name, customer_id 
                FROM orders 
                WHERE customer_id IS NULL 
                LIMIT 5
            """)
            sample_orders = cursor.fetchall()
            
            print("Sample orders with NULL customer_id:")
            for order in sample_orders:
                print(f"  - {order['order_id']}: {order['customer_name']}")
        
        # Check 2: Foreign key constraints
        print("\n🔗 CHECK 2: FOREIGN KEY CONSTRAINTS")
        print("-" * 40)
        cursor.execute("PRAGMA foreign_key_list(accounts_receivable)")
        fks = cursor.fetchall()
        
        if fks:
            print("Foreign key constraints in accounts_receivable:")
            for fk in fks:
                print(f"  - {fk[3]} -> {fk[2]}.{fk[4]}")
        else:
            print("ℹ️  No foreign key constraints found")
        
        # Check 3: Customers table
        print("\n👥 CHECK 3: CUSTOMERS TABLE")
        print("-" * 40)
        cursor.execute("SELECT COUNT(*) as count FROM customers")
        customer_count = cursor.fetchone()['count']
        print(f"Total customers: {customer_count}")
        
        # Check 4: Orders referencing non-existent customers
        print("\n🔍 CHECK 4: ORPHANED ORDER REFERENCES")
        print("-" * 40)
        cursor.execute("""
            SELECT COUNT(*) as count
            FROM orders o
            LEFT JOIN customers c ON o.customer_id = c.customer_id
            WHERE o.customer_id IS NOT NULL AND c.customer_id IS NULL
        """)
        orphaned_count = cursor.fetchone()['count']
        
        if orphaned_count == 0:
            print("✅ No orphaned order references found")
        else:
            print(f"⚠️  Found {orphaned_count} orders referencing non-existent customers")
        
        # Check 5: Accounts receivable table
        print("\n💰 CHECK 5: ACCOUNTS RECEIVABLE TABLE")
        print("-" * 40)
        cursor.execute("SELECT COUNT(*) as count FROM accounts_receivable")
        ar_count = cursor.fetchone()['count']
        print(f"Total accounts receivable records: {ar_count}")
        
        # Check 6: Sample order for testing
        print("\n🎯 CHECK 6: SAMPLE ORDER FOR TESTING")
        print("-" * 40)
        cursor.execute("""
            SELECT o.order_id, o.customer_name, o.customer_id, c.name as customer_name_in_table
            FROM orders o
            LEFT JOIN customers c ON o.customer_id = c.customer_id
            WHERE o.order_id = 'ORD00000147'
        """)
        test_order = cursor.fetchone()
        
        if test_order:
            print(f"Test Order ID: {test_order['order_id']}")
            print(f"Customer Name: {test_order['customer_name']}")
            print(f"Customer ID: {test_order['customer_id'] or 'NULL'}")
            print(f"Customer in DB: {test_order['customer_name_in_table'] or 'NOT FOUND'}")
            
            if test_order['customer_id'] and test_order['customer_name_in_table']:
                print("✅ Test order is ready for invoice generation")
            else:
                print("⚠️  Test order needs customer record creation")
        else:
            print("❌ Test order ORD00000147 not found")
        
        # Overall assessment
        print("\n📊 OVERALL ASSESSMENT")
        print("-" * 40)
        
        issues = []
        if null_count > 0:
            issues.append(f"{null_count} orders with NULL customer_id")
        if orphaned_count > 0:
            issues.append(f"{orphaned_count} orphaned order references")
        
        if not issues:
            print("✅ Database is ready for invoice generation")
            print("✅ All foreign key constraints should be satisfied")
            return True
        else:
            print("⚠️  Issues found:")
            for issue in issues:
                print(f"  - {issue}")
            print("\n💡 Run fix_invoice_generation_error.py to resolve these issues")
            return False
        
    except Exception as e:
        print(f"❌ Error verifying database: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    verify_database_state()
