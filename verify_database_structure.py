#!/usr/bin/env python3
"""
Verify Database Structure and Data
"""

import sqlite3
import os

def verify_database_structure():
    """Verify all database tables and their structure"""
    
    print("🔍 VERIFYING DATABASE STRUCTURE")
    print("=" * 60)
    
    # Check if database exists
    db_path = 'instance/medivent.db'
    if not os.path.exists(db_path):
        print(f"❌ Database not found at: {db_path}")
        return False
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        print(f"\n📋 DATABASE TABLES")
        print("-" * 40)
        
        # Get all tables
        tables = cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name NOT LIKE 'sqlite_%'
            ORDER BY name
        """).fetchall()
        
        required_tables = [
            'orders', 'order_items', 'products', 'inventory', 
            'warehouses', 'delivery_challans', 'stock_movements'
        ]
        
        existing_tables = [table['name'] for table in tables]
        
        print(f"Found {len(existing_tables)} tables:")
        for table in existing_tables:
            status = "✅" if table in required_tables else "ℹ️ "
            print(f"   {status} {table}")
        
        # Check missing required tables
        missing_tables = [table for table in required_tables if table not in existing_tables]
        if missing_tables:
            print(f"\n❌ Missing required tables: {', '.join(missing_tables)}")
        else:
            print(f"\n✅ All required tables exist")
        
        print(f"\n📊 TABLE STRUCTURES AND DATA")
        print("-" * 40)
        
        # Check each required table
        for table in required_tables:
            if table in existing_tables:
                print(f"\n🔍 Table: {table}")
                
                # Get table structure
                columns = cursor.execute(f"PRAGMA table_info({table})").fetchall()
                print(f"   Columns ({len(columns)}):")
                for col in columns:
                    print(f"     - {col['name']}: {col['type']} {'(PK)' if col['pk'] else ''}")
                
                # Get row count
                count = cursor.execute(f"SELECT COUNT(*) FROM {table}").fetchone()[0]
                print(f"   Rows: {count}")
                
                # Show sample data for key tables
                if table in ['orders', 'order_items', 'inventory'] and count > 0:
                    sample = cursor.execute(f"SELECT * FROM {table} LIMIT 3").fetchall()
                    print(f"   Sample data:")
                    for row in sample:
                        key_field = 'order_id' if 'order_id' in row.keys() else list(row.keys())[0]
                        print(f"     - {key_field}: {row[key_field]}")
        
        print(f"\n🔍 SPECIFIC ORDER VERIFICATION")
        print("-" * 40)
        
        # Check specific orders from the error
        test_orders = ['ORD1754153041416935BB', 'ORD175411154600DAC554']
        
        for order_id in test_orders:
            print(f"\n📦 Order: {order_id}")
            
            # Check order exists
            order = cursor.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
            if order:
                print(f"   ✅ Order exists: {order['customer_name']} ({order['status']})")
                
                # Check order items
                items = cursor.execute('SELECT COUNT(*) FROM order_items WHERE order_id = ?', (order_id,)).fetchone()[0]
                print(f"   ✅ Order items: {items}")
                
                # Check if any inventory exists for the products in this order
                inventory_check = cursor.execute('''
                    SELECT COUNT(DISTINCT i.inventory_id) as inventory_count
                    FROM order_items oi
                    LEFT JOIN inventory i ON oi.product_id = i.product_id AND i.status = 'active'
                    WHERE oi.order_id = ?
                ''', (order_id,)).fetchone()
                
                print(f"   ✅ Available inventory records: {inventory_check['inventory_count']}")
                
            else:
                print(f"   ❌ Order not found")
        
        print(f"\n🔍 INVENTORY AVAILABILITY CHECK")
        print("-" * 40)
        
        # Check overall inventory health
        inventory_summary = cursor.execute('''
            SELECT 
                COUNT(*) as total_records,
                COUNT(CASE WHEN status = 'active' THEN 1 END) as active_records,
                SUM(CASE WHEN status = 'active' THEN stock_quantity - COALESCE(allocated_quantity, 0) ELSE 0 END) as total_available
            FROM inventory
        ''').fetchone()
        
        print(f"   Total inventory records: {inventory_summary['total_records']}")
        print(f"   Active inventory records: {inventory_summary['active_records']}")
        print(f"   Total available quantity: {inventory_summary['total_available']}")
        
        # Check products with no inventory
        products_no_inventory = cursor.execute('''
            SELECT p.product_id, p.name
            FROM products p
            LEFT JOIN inventory i ON p.product_id = i.product_id AND i.status = 'active'
            WHERE i.product_id IS NULL AND p.is_active = 1
            LIMIT 5
        ''').fetchall()
        
        if products_no_inventory:
            print(f"   ⚠️  Products without inventory ({len(products_no_inventory)}):")
            for prod in products_no_inventory:
                print(f"     - {prod['product_id']}: {prod['name']}")
        else:
            print(f"   ✅ All active products have inventory")
        
        conn.close()
        
        print(f"\n🎯 VERIFICATION SUMMARY")
        print("=" * 60)
        
        if missing_tables:
            print("❌ Database structure incomplete - missing tables")
            return False
        else:
            print("✅ Database structure complete")
            print("✅ All required tables exist")
            print("✅ Data verification completed")
            return True
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = verify_database_structure()
    if success:
        print("\n🎉 DATABASE VERIFICATION COMPLETED!")
    else:
        print("\n💥 DATABASE VERIFICATION FAILED")
