#!/usr/bin/env python3
"""
Verify and create DC records for testing
"""

import sqlite3
import os
from datetime import datetime

def check_and_create_dc_records():
    """Check DC records and create test records if needed"""
    
    if not os.path.exists('instance/medivent.db'):
        print("❌ Database file not found")
        return False
    
    conn = sqlite3.connect('instance/medivent.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    print("🔍 CHECKING DC RECORDS")
    print("=" * 50)
    
    # Check test orders
    test_orders = ['ORD1753983391CA9E99E1', 'ORD17537715172786B57D']
    
    for order_id in test_orders:
        print(f"\n📋 Checking Order: {order_id}")
        
        # Check if order exists
        cursor.execute('SELECT order_id, status FROM orders WHERE order_id = ?', (order_id,))
        order = cursor.fetchone()
        
        if not order:
            print(f"  ❌ Order {order_id} not found")
            continue
        
        print(f"  ✅ Order exists with status: {order['status']}")
        
        # Check if DC exists
        cursor.execute('SELECT dc_number, status FROM delivery_challans WHERE order_id = ?', (order_id,))
        dc = cursor.fetchone()
        
        if dc:
            print(f"  ✅ DC exists: {dc['dc_number']} (Status: {dc['status']})")
        else:
            print(f"  ❌ No DC found for order {order_id}")
            
            # Create DC record for testing
            if order['status'] in ['Approved', 'Processing', 'Ready for Pickup']:
                print(f"  🔧 Creating test DC record...")
                
                # Generate DC number
                cursor.execute('SELECT COUNT(*) FROM delivery_challans')
                dc_count = cursor.fetchone()[0]
                dc_number = f"DC-{(dc_count + 1):03d}"
                
                # Create DC record
                cursor.execute('''
                    INSERT INTO delivery_challans (
                        dc_number, order_id, customer_name, status, 
                        created_date, created_by, total_items, total_amount
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    dc_number,
                    order_id,
                    f"Test Customer for {order_id}",
                    'created',
                    datetime.now(),
                    'system',
                    1,
                    100.0
                ))
                
                print(f"  ✅ Created DC: {dc_number}")
    
    # Commit changes
    conn.commit()
    
    print("\n📊 FINAL DC SUMMARY")
    print("=" * 50)
    
    cursor.execute('''
        SELECT dc.dc_number, dc.order_id, dc.status, dc.created_date,
               o.status as order_status
        FROM delivery_challans dc
        LEFT JOIN orders o ON dc.order_id = o.order_id
        ORDER BY dc.created_date DESC
        LIMIT 10
    ''')
    
    dcs = cursor.fetchall()
    
    if dcs:
        print("Recent DC Records:")
        for dc in dcs:
            print(f"  {dc['dc_number']} | Order: {dc['order_id']} | DC Status: {dc['status']} | Order Status: {dc['order_status']}")
    else:
        print("  ❌ No DC records found")
    
    conn.close()
    return True

def test_template_fixes():
    """Test if template fixes are working"""
    print("\n🎨 TESTING TEMPLATE FIXES")
    print("=" * 50)
    
    # Check if View DC button logic is correct
    template_file = 'templates/orders/view.html'
    
    try:
        with open(template_file, 'r') as f:
            content = f.read()
        
        # Check for correct DC button logic
        if 'not challan' in content and 'elif challan' in content:
            print("✅ DC button logic correctly implemented")
        else:
            print("❌ DC button logic missing or incorrect")
        
        # Check for View DC button
        if 'dc_generation.view_dc' in content:
            print("✅ View DC button route correctly implemented")
        else:
            print("❌ View DC button route missing")
        
        # Check for warehouses route fixes
        if 'warehouse_packing_dashboard' in content:
            print("✅ Warehouse route fixes applied")
        else:
            print("⚠️  Warehouse route fixes may be needed")
            
    except Exception as e:
        print(f"❌ Error checking template: {e}")

def main():
    """Main function"""
    print("🔧 DC RECORDS VERIFICATION AND FIXES")
    print("=" * 60)
    
    # Check and create DC records
    if check_and_create_dc_records():
        print("\n✅ DC records verification completed")
    else:
        print("\n❌ DC records verification failed")
    
    # Test template fixes
    test_template_fixes()
    
    print("\n🎯 NEXT STEPS:")
    print("1. Start Flask application: python app.py")
    print("2. Test order page: http://127.0.0.1:5001/orders/ORD1753983391CA9E99E1")
    print("3. Verify DC buttons show correctly:")
    print("   - 'Generate DC' for approved orders without DC")
    print("   - 'View DC' for orders with existing DC")
    print("4. Test invoice generation after DC creation")

if __name__ == '__main__':
    main()
