#!/usr/bin/env python3
"""
🔍 DELIVERY CHALLAN FIXES VERIFICATION SCRIPT
Verifies that all routing fixes have been applied correctly
"""

import os
import re
import sqlite3
from datetime import datetime

def check_file_exists(filepath):
    """Check if file exists"""
    return os.path.exists(filepath)

def search_in_file(filepath, pattern, description):
    """Search for a pattern in a file"""
    if not check_file_exists(filepath):
        return False, f"File {filepath} not found"
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
            matches = re.findall(pattern, content)
            return len(matches) > 0, f"Found {len(matches)} matches"
    except Exception as e:
        return False, f"Error reading file: {e}"

def verify_database_tables():
    """Verify required database tables exist"""
    print("🗄️ Verifying Database Tables...")
    
    if not check_file_exists("medivent.db"):
        print("❌ Database file medivent.db not found")
        return False
    
    try:
        conn = sqlite3.connect("medivent.db")
        cursor = conn.cursor()
        
        # Check required tables
        required_tables = [
            'delivery_challans',
            'rider_bikes', 
            'rider_performance_logs',
            'bike_documents'
        ]
        
        all_tables_exist = True
        for table in required_tables:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table,))
            if cursor.fetchone():
                print(f"✅ Table '{table}' exists")
            else:
                print(f"❌ Table '{table}' missing")
                all_tables_exist = False
        
        # Check if delivery_challans has data
        cursor.execute("SELECT COUNT(*) FROM delivery_challans")
        count = cursor.fetchone()[0]
        print(f"📊 delivery_challans table has {count} records")
        
        conn.close()
        return all_tables_exist
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def verify_template_fixes():
    """Verify template URL fixes"""
    print("\n🎨 Verifying Template Fixes...")
    
    fixes_verified = True
    
    # Check templates/delivery_challans/index.html
    template_file = "templates/delivery_challans/index.html"
    
    # Should NOT contain 'view_delivery_challan' (without _old)
    has_old_ref, msg = search_in_file(template_file, r"url_for\('view_delivery_challan'[^_]", "Old incorrect reference")
    if has_old_ref:
        print(f"❌ {template_file} still contains incorrect 'view_delivery_challan' reference")
        fixes_verified = False
    else:
        print(f"✅ {template_file} - No incorrect 'view_delivery_challan' references found")
    
    # Should contain 'view_delivery_challan_old'
    has_new_ref, msg = search_in_file(template_file, r"url_for\('view_delivery_challan_old'", "New correct reference")
    if has_new_ref:
        print(f"✅ {template_file} contains correct 'view_delivery_challan_old' reference")
    else:
        print(f"❌ {template_file} missing correct 'view_delivery_challan_old' reference")
        fixes_verified = False
    
    return fixes_verified

def verify_app_py_fixes():
    """Verify app.py routing fixes"""
    print("\n🐍 Verifying app.py Fixes...")
    
    fixes_verified = True
    
    # Check for incorrect redirect in app.py
    has_old_redirect, msg = search_in_file("app.py", r"redirect\(url_for\('view_delivery_challan'[^_]", "Old incorrect redirect")
    if has_old_redirect:
        print("❌ app.py still contains incorrect 'view_delivery_challan' redirect")
        fixes_verified = False
    else:
        print("✅ app.py - No incorrect 'view_delivery_challan' redirects found")
    
    # Check for correct redirect
    has_new_redirect, msg = search_in_file("app.py", r"redirect\(url_for\('view_delivery_challan_old'", "New correct redirect")
    if has_new_redirect:
        print("✅ app.py contains correct 'view_delivery_challan_old' redirect")
    else:
        print("❌ app.py missing correct 'view_delivery_challan_old' redirect")
        fixes_verified = False
    
    # Check route definitions exist
    has_route_def, msg = search_in_file("app.py", r"def view_delivery_challan_old\(", "Route definition")
    if has_route_def:
        print("✅ app.py contains 'view_delivery_challan_old' route definition")
    else:
        print("❌ app.py missing 'view_delivery_challan_old' route definition")
        fixes_verified = False
    
    return fixes_verified

def verify_safe_strftime_usage():
    """Verify safe_strftime is used in templates"""
    print("\n📅 Verifying safe_strftime Usage...")
    
    template_files = [
        "templates/delivery_challans/view.html",
        "templates/delivery_challans/index.html"
    ]
    
    all_good = True
    for template in template_files:
        if check_file_exists(template):
            # Check for unsafe .strftime() usage
            has_unsafe, msg = search_in_file(template, r"\.strftime\(", "Unsafe strftime usage")
            if has_unsafe:
                print(f"⚠️ {template} contains unsafe .strftime() usage")
                all_good = False
            else:
                print(f"✅ {template} - No unsafe .strftime() usage found")
        else:
            print(f"⚠️ {template} not found")
    
    return all_good

def main():
    """Main verification function"""
    print("🔍 DELIVERY CHALLAN FIXES VERIFICATION")
    print("=" * 50)
    print(f"Verification Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Run all verifications
    verifications = [
        ("Database Tables", verify_database_tables),
        ("Template Fixes", verify_template_fixes),
        ("App.py Fixes", verify_app_py_fixes),
        ("Safe strftime Usage", verify_safe_strftime_usage)
    ]
    
    results = []
    for name, func in verifications:
        print(f"\n{'='*20} {name} {'='*20}")
        try:
            result = func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ Error in {name}: {e}")
            results.append((name, False))
    
    # Summary
    print("\n" + "="*50)
    print("📊 VERIFICATION SUMMARY")
    print("="*50)
    
    passed = 0
    for name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {name}")
        if result:
            passed += 1
    
    total = len(results)
    print(f"\n🎯 OVERALL RESULT: {passed}/{total} verifications passed")
    
    if passed == total:
        print("\n🎉 ALL VERIFICATIONS PASSED!")
        print("✅ The delivery challan routing error should now be fixed.")
        print("✅ You can now test the 'All Delivery Challans' menu in the browser.")
        return 0
    else:
        print(f"\n⚠️ {total-passed} VERIFICATIONS FAILED!")
        print("❌ Some issues still need to be addressed.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
