#!/usr/bin/env python3
"""
Final verification of all routing fixes
"""

def verify_final_fixes():
    """Verify all fixes are working"""
    print("🎯 FINAL VERIFICATION OF ALL FIXES")
    print("=" * 60)
    
    fixes_verified = True
    
    # 1. Database Fix Verification
    print("🗄️ VERIFYING DATABASE FIX")
    print("=" * 50)
    
    try:
        import sqlite3
        db = sqlite3.connect('instance/medivent.db')
        db.row_factory = sqlite3.Row
        
        # Check if rating column exists
        cursor = db.execute("PRAGMA table_info(orders)")
        columns = [row[1] for row in cursor.fetchall()]
        
        if 'rating' in columns:
            print("✅ 'rating' column exists in orders table")
            
            # Test rating query
            result = db.execute("SELECT ROUND(AVG(CASE WHEN rating THEN rating ELSE 4.0 END), 2) as avg_rating FROM orders").fetchone()
            print(f"✅ Rating query successful! Result: {result[0]}")
        else:
            print("❌ 'rating' column missing in orders table")
            fixes_verified = False
            
        db.close()
        
    except Exception as e:
        print(f"❌ Database verification error: {e}")
        fixes_verified = False
    
    # 2. Template Routing Fixes Verification
    print(f"\n🚚 VERIFYING TEMPLATE ROUTING FIXES")
    print("=" * 50)
    
    template_files = [
        ('templates/orders/workflow.html', 'orders_dispatch_order', 'deliver_order'),
        ('templates/orders/dispatch.html', 'orders_dispatch_order', None),
        ('templates/orders/deliver.html', 'deliver_order', None)
    ]
    
    for template_file, dispatch_route, deliver_route in template_files:
        try:
            with open(template_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check dispatch route
            if dispatch_route:
                if f"url_for('{dispatch_route}'" in content:
                    print(f"✅ {template_file}: {dispatch_route} route fixed")
                else:
                    print(f"❌ {template_file}: {dispatch_route} route not fixed")
                    fixes_verified = False
            
            # Check deliver route
            if deliver_route:
                if f"url_for('{deliver_route}'" in content:
                    print(f"✅ {template_file}: {deliver_route} route fixed")
                else:
                    print(f"❌ {template_file}: {deliver_route} route not fixed")
                    fixes_verified = False
                    
        except Exception as e:
            print(f"❌ Error checking {template_file}: {e}")
            fixes_verified = False
    
    # 3. Flask App Route Verification
    print(f"\n🔧 VERIFYING FLASK APP ROUTES")
    print("=" * 50)
    
    try:
        from app import app
        
        with app.app_context():
            rules = list(app.url_map.iter_rules())
            endpoints = [rule.endpoint for rule in rules]
            
            # Check for required routes
            required_routes = [
                'workflow',
                'orders_dispatch_order', 
                'deliver_order'
            ]
            
            for route in required_routes:
                if route in endpoints:
                    matching_rule = next((rule for rule in rules if rule.endpoint == route), None)
                    print(f"✅ {route} endpoint registered: {matching_rule.rule}")
                else:
                    print(f"❌ {route} endpoint missing")
                    fixes_verified = False
            
            # Check for conflicts
            dispatch_routes = [ep for ep in endpoints if 'dispatch' in ep.lower() and 'order' in ep.lower()]
            print(f"\n📋 All dispatch-order routes: {dispatch_routes}")
            
    except Exception as e:
        print(f"❌ Flask app verification error: {e}")
        fixes_verified = False
    
    # 4. Summary
    print(f"\n📊 VERIFICATION SUMMARY")
    print("=" * 50)
    
    if fixes_verified:
        print("🎉 ALL FIXES VERIFIED SUCCESSFULLY!")
        print("✅ Database 'rating' column exists and works")
        print("✅ Template routing references updated correctly")
        print("✅ Flask app routes registered properly")
        print("\n🚀 The workflow should now work without BuildError!")
        print("   • http://localhost:5000/orders/workflow")
        print("   • Dispatch and Deliver buttons should work")
        print("   • Reports functionality should work without rating errors")
    else:
        print("❌ SOME FIXES FAILED - MANUAL REVIEW REQUIRED")
    
    return fixes_verified

if __name__ == "__main__":
    verify_final_fixes()
