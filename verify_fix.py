#!/usr/bin/env python3
"""
Verify the rider_dashboard fix by checking the template directly
"""

import os
import re

def verify_template_fix():
    """Verify that the template has been fixed"""
    print("🔍 VERIFYING TEMPLATE FIX")
    print("=" * 50)
    
    template_path = "templates/base.html"
    
    if not os.path.exists(template_path):
        print(f"❌ Template file not found: {template_path}")
        return False
    
    try:
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for the old problematic reference
        old_pattern = r"url_for\(['\"]rider_dashboard['\"]\)"
        old_matches = re.findall(old_pattern, content)
        
        if old_matches:
            print(f"❌ Found {len(old_matches)} old 'rider_dashboard' references:")
            for match in old_matches:
                print(f"   - {match}")
            return False
        else:
            print("✅ No old 'rider_dashboard' references found")
        
        # Check for the new correct reference
        new_pattern = r"url_for\(['\"]riders\.dashboard['\"]\)"
        new_matches = re.findall(new_pattern, content)
        
        if new_matches:
            print(f"✅ Found {len(new_matches)} correct 'riders.dashboard' references:")
            for match in new_matches:
                print(f"   - {match}")
        else:
            print("⚠️ No 'riders.dashboard' references found")
        
        # Check for the endpoint check fix
        old_endpoint_check = "request.endpoint == 'rider_dashboard'"
        new_endpoint_check = "request.endpoint == 'riders.dashboard'"
        
        if old_endpoint_check in content:
            print(f"❌ Found old endpoint check: {old_endpoint_check}")
            return False
        elif new_endpoint_check in content:
            print(f"✅ Found correct endpoint check: {new_endpoint_check}")
        else:
            print("⚠️ No endpoint check found")
        
        # Look for the specific line that was causing the issue
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            if 'Professional Dashboard' in line and 'url_for' in line:
                print(f"✅ Line {i}: {line.strip()}")
                if 'riders.dashboard' in line:
                    print("   ✅ Uses correct endpoint")
                else:
                    print("   ❌ Uses incorrect endpoint")
                    return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading template: {e}")
        return False

def verify_blueprint_structure():
    """Verify that the blueprint structure is correct"""
    print("\n🏗️ VERIFYING BLUEPRINT STRUCTURE")
    print("=" * 50)
    
    blueprint_path = "routes/modern_riders.py"
    
    if not os.path.exists(blueprint_path):
        print(f"❌ Blueprint file not found: {blueprint_path}")
        return False
    
    try:
        with open(blueprint_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for blueprint definition
        if "riders_bp = Blueprint('riders'" in content:
            print("✅ Blueprint 'riders' is defined")
        else:
            print("❌ Blueprint 'riders' not found")
            return False
        
        # Check for dashboard route
        dashboard_patterns = [
            r"@riders_bp\.route\(['\"]\/['\"]\)",
            r"@riders_bp\.route\(['\"]\/dashboard['\"]\)",
            r"def dashboard\(\):"
        ]
        
        for pattern in dashboard_patterns:
            if re.search(pattern, content):
                print(f"✅ Found dashboard route pattern: {pattern}")
            else:
                print(f"⚠️ Dashboard route pattern not found: {pattern}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading blueprint: {e}")
        return False

def verify_app_registration():
    """Verify that the blueprint is registered in app.py"""
    print("\n📝 VERIFYING BLUEPRINT REGISTRATION")
    print("=" * 50)
    
    app_path = "app.py"
    
    if not os.path.exists(app_path):
        print(f"❌ App file not found: {app_path}")
        return False
    
    try:
        with open(app_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for blueprint import
        if "from routes.modern_riders import riders_bp" in content:
            print("✅ Blueprint import found")
        else:
            print("⚠️ Blueprint import not found")
        
        # Check for blueprint registration
        if "app.register_blueprint(riders_bp)" in content:
            print("✅ Blueprint registration found")
        else:
            print("⚠️ Blueprint registration not found")
        
        # Check if old route is commented out
        if "# def rider_dashboard():" in content or "# @app.route('/rider/dashboard')" in content:
            print("✅ Old rider_dashboard route is commented out")
        else:
            print("⚠️ Old rider_dashboard route status unclear")
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading app.py: {e}")
        return False

def create_test_summary():
    """Create a summary of the fix"""
    print("\n📋 FIX SUMMARY")
    print("=" * 50)
    
    print("🔧 CHANGES MADE:")
    print("1. ✅ Updated templates/base.html line 697:")
    print("   - Changed: url_for('rider_dashboard')")
    print("   - To: url_for('riders.dashboard')")
    print("2. ✅ Updated endpoint check:")
    print("   - Changed: request.endpoint == 'rider_dashboard'")
    print("   - To: request.endpoint == 'riders.dashboard'")
    
    print("\n🎯 EXPECTED RESULT:")
    print("- ✅ No more BuildError when accessing /dashboard")
    print("- ✅ Professional Dashboard link works correctly")
    print("- ✅ Navigation active state works properly")
    
    print("\n🧪 TO TEST:")
    print("1. Start Flask server: python app.py")
    print("2. Open browser: http://localhost:5000/dashboard")
    print("3. Verify no BuildError appears")
    print("4. Click 'Professional Dashboard' link")
    print("5. Verify it navigates to /riders/dashboard")

def main():
    """Run all verifications"""
    print("🚀 RIDER DASHBOARD FIX VERIFICATION")
    print("=" * 50)
    
    template_ok = verify_template_fix()
    blueprint_ok = verify_blueprint_structure()
    app_ok = verify_app_registration()
    
    print("\n🏁 VERIFICATION RESULTS")
    print("=" * 50)
    print(f"📄 Template Fix: {'✅ PASS' if template_ok else '❌ FAIL'}")
    print(f"🏗️ Blueprint Structure: {'✅ PASS' if blueprint_ok else '❌ FAIL'}")
    print(f"📝 App Registration: {'✅ PASS' if app_ok else '❌ FAIL'}")
    
    overall_success = template_ok and blueprint_ok and app_ok
    
    if overall_success:
        print("\n🎉 ALL VERIFICATIONS PASSED!")
        print("✅ The BuildError fix has been successfully applied!")
        print("✅ Template references updated correctly!")
        print("✅ Blueprint structure is correct!")
        print("✅ Ready for testing with Flask server!")
    else:
        print("\n⚠️ SOME VERIFICATIONS FAILED")
        print("❌ Please review the issues above")
    
    create_test_summary()
    
    return overall_success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
