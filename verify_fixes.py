#!/usr/bin/env python3
"""
Comprehensive verification of both routing and database fixes
"""

import sqlite3
import os
import re
from flask import <PERSON>lask

def verify_database_fix():
    """Verify that the rating column was added and queries work"""
    print("🗄️ VERIFYING DATABASE FIX")
    print("=" * 50)
    
    db_path = 'instance/medivent.db'
    
    if not os.path.exists(db_path):
        print(f"❌ Database not found: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if rating column exists
        cursor.execute("PRAGMA table_info(orders)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        if 'rating' not in column_names:
            print("❌ 'rating' column missing from orders table")
            conn.close()
            return False
        
        print("✅ 'rating' column exists in orders table")
        
        # Test the problematic query from modern_riders.py
        test_query = """
            SELECT 
                ROUND(AVG(CASE WHEN o.rating THEN o.rating ELSE 4.0 END), 2) as avg_rating
            FROM orders o 
            LIMIT 1
        """
        
        cursor.execute(test_query)
        result = cursor.fetchone()
        
        if result is not None:
            print(f"✅ Rating query successful! Result: {result[0]}")
        else:
            print("⚠️ Rating query returned no results (empty table)")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database verification failed: {e}")
        return False

def verify_routing_fixes():
    """Verify that template routing references are fixed"""
    print("\n🚚 VERIFYING ROUTING FIXES")
    print("=" * 50)
    
    fixes_verified = True
    
    # Check templates/orders/workflow.html
    workflow_file = 'templates/orders/workflow.html'
    try:
        with open(workflow_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Should contain orders.dispatch_order, not dispatch_order
        if "url_for('orders.dispatch_order'" in content:
            print("✅ workflow.html: dispatch_order route fixed")
        else:
            print("❌ workflow.html: dispatch_order route not fixed")
            fixes_verified = False
        
        # Should contain orders.deliver_order, not deliver_order
        if "url_for('orders.deliver_order'" in content:
            print("✅ workflow.html: deliver_order route fixed")
        else:
            print("❌ workflow.html: deliver_order route not fixed")
            fixes_verified = False
        
        # Should NOT contain old references
        if "url_for('dispatch_order'" in content:
            print("❌ workflow.html: still contains old dispatch_order reference")
            fixes_verified = False
        
        if "url_for('deliver_order'" in content:
            print("❌ workflow.html: still contains old deliver_order reference")
            fixes_verified = False
            
    except Exception as e:
        print(f"❌ Error checking workflow.html: {e}")
        fixes_verified = False
    
    # Check templates/orders/dispatch.html
    dispatch_file = 'templates/orders/dispatch.html'
    try:
        with open(dispatch_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if "url_for('orders.dispatch_order'" in content:
            print("✅ dispatch.html: dispatch_order route fixed")
        else:
            print("❌ dispatch.html: dispatch_order route not fixed")
            fixes_verified = False
            
    except Exception as e:
        print(f"❌ Error checking dispatch.html: {e}")
        fixes_verified = False
    
    # Check templates/orders/deliver.html
    deliver_file = 'templates/orders/deliver.html'
    try:
        with open(deliver_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if "url_for('orders.deliver_order'" in content:
            print("✅ deliver.html: deliver_order route fixed")
        else:
            print("❌ deliver.html: deliver_order route not fixed")
            fixes_verified = False
            
    except Exception as e:
        print(f"❌ Error checking deliver.html: {e}")
        fixes_verified = False
    
    return fixes_verified

def verify_blueprint_routes():
    """Verify that the blueprint routes are properly defined"""
    print("\n🛣️ VERIFYING BLUEPRINT ROUTES")
    print("=" * 50)
    
    try:
        # Check routes/orders.py for the dispatch_order and deliver_order functions
        orders_file = 'routes/orders.py'
        with open(orders_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for dispatch_order function
        if "def dispatch_order(order_id):" in content:
            print("✅ dispatch_order function exists in orders.py")
        else:
            print("❌ dispatch_order function missing in orders.py")
            return False
        
        # Check for deliver_order function
        if "def deliver_order(order_id):" in content:
            print("✅ deliver_order function exists in orders.py")
        else:
            print("❌ deliver_order function missing in orders.py")
            return False
        
        # Check for blueprint route decorators
        if "@orders_bp.route('/<order_id>/dispatch'" in content:
            print("✅ dispatch_order route decorator exists")
        else:
            print("❌ dispatch_order route decorator missing")
            return False
        
        if "@orders_bp.route('/<order_id>/deliver'" in content:
            print("✅ deliver_order route decorator exists")
        else:
            print("❌ deliver_order route decorator missing")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking blueprint routes: {e}")
        return False

def test_flask_app_creation():
    """Test creating Flask app to check for import errors"""
    print("\n🔧 TESTING FLASK APP CREATION")
    print("=" * 50)
    
    try:
        # Try to import the main app
        import sys
        sys.path.insert(0, '.')
        
        # This will test if there are any import errors
        from app import app
        print("✅ Flask app imported successfully")
        
        # Test if the orders blueprint is registered
        with app.app_context():
            rules = list(app.url_map.iter_rules())
            endpoints = [rule.endpoint for rule in rules]
            
            # Check for our specific endpoints
            if 'orders.dispatch_order' in endpoints:
                print("✅ orders.dispatch_order endpoint registered")
            else:
                print("❌ orders.dispatch_order endpoint missing")
                return False
            
            if 'orders.deliver_order' in endpoints:
                print("✅ orders.deliver_order endpoint registered")
            else:
                print("❌ orders.deliver_order endpoint missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Flask app creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run comprehensive verification"""
    print("🚀 COMPREHENSIVE FIX VERIFICATION")
    print("=" * 60)
    
    # Test 1: Database fix
    db_ok = verify_database_fix()
    
    # Test 2: Routing fixes
    routing_ok = verify_routing_fixes()
    
    # Test 3: Blueprint routes
    blueprint_ok = verify_blueprint_routes()
    
    # Test 4: Flask app creation
    app_ok = test_flask_app_creation()
    
    # Summary
    print("\n📊 VERIFICATION SUMMARY")
    print("=" * 60)
    print(f"🗄️ Database Fix: {'✅ PASS' if db_ok else '❌ FAIL'}")
    print(f"🚚 Routing Fixes: {'✅ PASS' if routing_ok else '❌ FAIL'}")
    print(f"🛣️ Blueprint Routes: {'✅ PASS' if blueprint_ok else '❌ FAIL'}")
    print(f"🔧 Flask App: {'✅ PASS' if app_ok else '❌ FAIL'}")
    
    all_ok = db_ok and routing_ok and blueprint_ok and app_ok
    
    if all_ok:
        print("\n🎉 ALL FIXES VERIFIED SUCCESSFULLY!")
        print("✅ Both routing and database issues have been resolved")
        print("✅ The Flask application should now work without errors")
        print("\n📋 FIXES APPLIED:")
        print("1. ✅ Added 'rating' column to orders table (default: 4.0)")
        print("2. ✅ Fixed dispatch_order routing in workflow.html")
        print("3. ✅ Fixed dispatch_order routing in dispatch.html")
        print("4. ✅ Fixed deliver_order routing in workflow.html")
        print("5. ✅ Fixed deliver_order routing in deliver.html")
        print("\n🚀 You can now access:")
        print("   - http://localhost:5000/orders/workflow (without BuildError)")
        print("   - Reports functionality (without 'o.rating' column error)")
    else:
        print("\n❌ SOME FIXES FAILED - MANUAL REVIEW REQUIRED")
    
    return all_ok

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
