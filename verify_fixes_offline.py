#!/usr/bin/env python3
"""
Verify Product Management Fixes (Offline)
Tests the fixes without requiring the Flask server to be running
"""

import sqlite3
import os

def verify_template_content():
    """Verify that the template has the correct content"""
    print("🎨 VERIFYING TEMPLATE CONTENT:")
    
    template_path = 'templates/products/product_management.html'
    
    if not os.path.exists(template_path):
        print(f"   ❌ Template not found: {template_path}")
        return False
    
    with open(template_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for key elements that should be present
    checks = [
        ('Products Management', 'Page title'),
        ('stats.total_products', 'Total products KPI'),
        ('stats.active_products', 'Active products KPI'),
        ('stats.inactive_products', 'Inactive products KPI'),
        ('stats.categories', 'Categories KPI'),
        ('toggleProductStatus', 'Toggle function'),
        ('url_for(\'products.product_management\')', 'Correct filter URLs'),
        ('X-Requested-With', 'AJAX headers'),
        ('application/json', 'JSON content type'),
        ('badge-success', 'Active status badge'),
        ('badge-secondary', 'Inactive status badge')
    ]
    
    all_passed = True
    for check_text, description in checks:
        if check_text in content:
            print(f"   ✅ {description}")
        else:
            print(f"   ❌ {description} - Missing: {check_text}")
            all_passed = False
    
    return all_passed

def verify_route_structure():
    """Verify that the routes are properly structured"""
    print("\n🛣️ VERIFYING ROUTE STRUCTURE:")
    
    routes_file = 'routes/products.py'
    
    if not os.path.exists(routes_file):
        print(f"   ❌ Routes file not found: {routes_file}")
        return False
    
    with open(routes_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for key route elements
    checks = [
        ('@products_bp.route(\'/product_management/\')', 'Product management route'),
        ('@products_bp.route(\'/activate/<product_id>\')', 'Activate route'),
        ('@products_bp.route(\'/deactivate/<product_id>\')', 'Deactivate route'),
        ('X-Requested-With', 'AJAX detection'),
        ('jsonify', 'JSON responses'),
        ('is_active = bool(row[\'is_active\'])', 'Correct is_active calculation'),
        ('len([p for p in products if p[\'is_active\']])', 'Active products count'),
        ('render_template(\'products/product_management.html\'', 'Correct template')
    ]
    
    all_passed = True
    for check_text, description in checks:
        if check_text in content:
            print(f"   ✅ {description}")
        else:
            print(f"   ❌ {description} - Missing: {check_text}")
            all_passed = False
    
    return all_passed

def verify_app_conflicts_removed():
    """Verify that conflicting routes in app.py have been removed"""
    print("\n🚫 VERIFYING CONFLICT REMOVAL:")
    
    app_file = 'app.py'
    
    if not os.path.exists(app_file):
        print(f"   ❌ App file not found: {app_file}")
        return False
    
    with open(app_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check that conflicting routes are removed or commented
    conflict_checks = [
        ('@app.route(\'/products\')', 'Conflicting /products route'),
        ('@app.route(\'/products/\')', 'Conflicting /products/ route'),
        ('def products():', 'Conflicting products function')
    ]
    
    conflicts_found = 0
    for check_text, description in conflict_checks:
        if check_text in content and 'REMOVED' not in content[content.find(check_text):content.find(check_text)+200]:
            print(f"   ⚠️ {description} still exists")
            conflicts_found += 1
        else:
            print(f"   ✅ {description} properly removed/commented")
    
    return conflicts_found == 0

def verify_database_structure():
    """Verify database has the required columns"""
    print("\n🗄️ VERIFYING DATABASE STRUCTURE:")
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        # Check products table structure
        cursor.execute("PRAGMA table_info(products)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        required_columns = ['product_id', 'name', 'status', 'is_active', 'category', 'manufacturer']
        
        all_present = True
        for col in required_columns:
            if col in column_names:
                print(f"   ✅ Column '{col}' exists")
            else:
                print(f"   ❌ Column '{col}' missing")
                all_present = False
        
        # Test sample data
        cursor.execute("SELECT COUNT(*) FROM products")
        total_count = cursor.fetchone()[0]
        print(f"   ✅ Total products in database: {total_count}")
        
        cursor.execute("SELECT COUNT(*) FROM products WHERE is_active = 1")
        active_count = cursor.fetchone()[0]
        print(f"   ✅ Active products: {active_count}")
        
        conn.close()
        return all_present and total_count > 0
        
    except Exception as e:
        print(f"   ❌ Database verification failed: {e}")
        return False

def main():
    """Run all offline verification tests"""
    print("🔍 OFFLINE VERIFICATION OF PRODUCT MANAGEMENT FIXES")
    print("=" * 70)
    
    tests = [
        ("Template Content", verify_template_content),
        ("Route Structure", verify_route_structure),
        ("Conflict Removal", verify_app_conflicts_removed),
        ("Database Structure", verify_database_structure)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 VERIFICATION SUMMARY:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} verifications passed")
    
    if passed == total:
        print("\n🎉 ALL FIXES VERIFIED SUCCESSFULLY!")
        print("\n📋 SUMMARY OF FIXES APPLIED:")
        print("   ✅ Fixed KPI calculation logic (is_active boolean conversion)")
        print("   ✅ Updated AJAX headers for proper JSON detection")
        print("   ✅ Enhanced activation/deactivation routes with better AJAX support")
        print("   ✅ Removed conflicting /products route from app.py")
        print("   ✅ Ensured filters use correct product_management route")
        print("\n🌐 Ready to test at: http://127.0.0.1:5001/products/product_management/")
        print("\n🔧 Expected Results:")
        print("   • KPI cards should show real numbers (19 active products)")
        print("   • Filters should stay in Product Management view")
        print("   • Toggle buttons should work via AJAX")
        print("   • No more 'Products Gallery' redirects")
    else:
        print(f"\n⚠️ {total - passed} issues found. Review the details above.")

if __name__ == "__main__":
    main()
