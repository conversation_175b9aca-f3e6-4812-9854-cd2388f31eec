#!/usr/bin/env python3
"""
Verify Partial Pending Blueprint Fix
Test all endpoints and functionality on port 5001
"""

import requests
import time
import json

def test_flask_app_running():
    """Test if Flask app is running on port 5001"""
    print("🔍 1. TESTING FLASK APP STATUS")
    print("-" * 50)
    
    try:
        response = requests.get('http://127.0.0.1:5001/', timeout=5)
        if response.status_code == 200:
            print("✅ Flask app is running on port 5001")
            print(f"   Status Code: {response.status_code}")
            return True
        else:
            print(f"⚠️  Flask app responding but with status: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Flask app is not running on port 5001")
        print("   Please start the app with: python app.py")
        return False
    except Exception as e:
        print(f"❌ Error testing Flask app: {e}")
        return False

def test_partial_pending_routes():
    """Test all partial pending routes"""
    print("\n🔍 2. TESTING PARTIAL PENDING ROUTES")
    print("-" * 50)
    
    routes_to_test = [
        ('/partial-pending/status', 'Status Endpoint (Public)'),
        ('/partial-pending/', 'Main Dashboard (Login Required)'),
        ('/partial-pending/test', 'Test API (Login Required)')
    ]
    
    results = {}
    
    for route, description in routes_to_test:
        try:
            url = f'http://127.0.0.1:5001{route}'
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                print(f"✅ {description}")
                print(f"   URL: {url}")
                print(f"   Status: {response.status_code}")
                
                # For JSON endpoints, show response
                if 'status' in route or 'test' in route:
                    try:
                        data = response.json()
                        print(f"   Response: {json.dumps(data, indent=2)}")
                    except:
                        print(f"   Response: {response.text[:100]}...")
                
                results[route] = True
                
            elif response.status_code == 302:
                print(f"🔄 {description}")
                print(f"   URL: {url}")
                print(f"   Status: {response.status_code} (Redirect - Login Required)")
                print(f"   Location: {response.headers.get('Location', 'Unknown')}")
                results[route] = True  # Redirect is expected for login-required routes
                
            else:
                print(f"❌ {description}")
                print(f"   URL: {url}")
                print(f"   Status: {response.status_code}")
                results[route] = False
                
        except Exception as e:
            print(f"❌ {description}")
            print(f"   URL: {url}")
            print(f"   Error: {e}")
            results[route] = False
        
        print()
    
    return results

def test_blueprint_registration():
    """Test blueprint registration by checking Flask routes"""
    print("🔍 3. TESTING BLUEPRINT REGISTRATION")
    print("-" * 50)
    
    try:
        # Test if we can import the blueprint
        from routes.partial_pending import partial_pending_bp
        print("✅ Blueprint import successful")
        print(f"   Name: {partial_pending_bp.name}")
        print(f"   URL Prefix: {partial_pending_bp.url_prefix}")
        
        # Count routes in blueprint
        route_count = 0
        for rule in partial_pending_bp.url_map.iter_rules():
            route_count += 1
            print(f"   Route: {rule.rule} -> {rule.endpoint}")
        
        print(f"   Total routes: {route_count}")
        
        if route_count >= 3:  # We expect at least 3 routes
            print("✅ Blueprint has expected routes")
            return True
        else:
            print("⚠️  Blueprint has fewer routes than expected")
            return False
            
    except Exception as e:
        print(f"❌ Blueprint registration test failed: {e}")
        return False

def test_navigation_menu():
    """Test navigation menu integration"""
    print("\n🔍 4. TESTING NAVIGATION MENU INTEGRATION")
    print("-" * 50)
    
    try:
        # Test main dashboard to see if navigation loads
        response = requests.get('http://127.0.0.1:5001/', timeout=5)
        
        if response.status_code == 200:
            content = response.text
            
            # Check if partial pending link exists
            if 'partial-pending' in content:
                print("✅ Partial Pending link found in navigation")
                
                # Check if it uses url_for
                if 'url_for' in content or '/partial-pending/' in content:
                    print("✅ Navigation link properly configured")
                    return True
                else:
                    print("⚠️  Navigation link may not be properly configured")
                    return False
            else:
                print("❌ Partial Pending link not found in navigation")
                return False
                
        elif response.status_code == 302:
            print("🔄 Dashboard redirects to login (expected)")
            print("✅ Navigation test skipped - login required")
            return True
        else:
            print(f"❌ Dashboard returned status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Navigation test failed: {e}")
        return False

def create_restoration_guide():
    """Create guide for restoring full functionality"""
    print("\n📋 5. CREATING RESTORATION GUIDE")
    print("-" * 50)
    
    guide_content = """
# 🔧 PARTIAL DC MANAGEMENT - RESTORATION GUIDE

## ✅ CURRENT STATUS
- ✅ Blueprint registered and working on port 5001
- ✅ Basic routes accessible (/partial-pending/, /test, /status)
- ✅ Navigation menu integrated
- ✅ No import or dependency issues

## 🚀 INCREMENTAL RESTORATION STEPS

### Phase 1: Database Integration (Low Risk)
1. Add database import: `from database import get_db`
2. Add simple database query to test connectivity
3. Test route still works

### Phase 2: Basic Template Integration (Medium Risk)
1. Create simple template file: `templates/partial_pending/index.html`
2. Replace HTML string with `render_template('partial_pending/index.html')`
3. Test template rendering

### Phase 3: Order Data Integration (Medium Risk)
1. Add order queries to get partial DC data
2. Pass data to template
3. Display basic order information

### Phase 4: Real-time Features (High Risk)
1. Add inventory status queries
2. Implement notification system
3. Add real-time updates

### Phase 5: AI Analytics (High Risk)
1. Add AI analytics imports
2. Implement prediction functions
3. Integrate with frontend

## 🔧 RESTORATION COMMANDS

### Backup Current Working Version
```bash
copy "routes\\partial_pending.py" "routes\\partial_pending_working.py"
```

### Restore Original Complex Version (if needed)
```bash
copy "routes\\partial_pending_original.py" "routes\\partial_pending.py"
```

### Test After Each Phase
```bash
python verify_partial_pending_fix.py
```

## ⚠️ SAFETY GUIDELINES
1. Always backup working version before changes
2. Test each phase individually
3. If any phase breaks, revert to previous working version
4. Add features incrementally, not all at once
5. Test in browser after each change

## 🎯 SUCCESS CRITERIA
- All routes return 200 or 302 (redirect)
- No import errors in Flask startup
- Navigation menu works correctly
- Blueprint accessible on port 5001
"""
    
    try:
        with open('PARTIAL_DC_RESTORATION_GUIDE.md', 'w', encoding='utf-8') as f:
            f.write(guide_content)
        print("✅ Restoration guide created: PARTIAL_DC_RESTORATION_GUIDE.md")
        return True
    except Exception as e:
        print(f"❌ Error creating guide: {e}")
        return False

def main():
    """Run comprehensive verification"""
    print("🔧 PARTIAL PENDING BLUEPRINT - VERIFICATION")
    print("=" * 60)
    print("Testing simplified blueprint on port 5001...")
    
    # Run all tests
    results = {}
    
    results['flask_app'] = test_flask_app_running()
    results['routes'] = test_partial_pending_routes()
    results['blueprint'] = test_blueprint_registration()
    results['navigation'] = test_navigation_menu()
    results['guide'] = create_restoration_guide()
    
    # Calculate success rate for route tests
    if isinstance(results['routes'], dict):
        route_success = sum(1 for success in results['routes'].values() if success)
        route_total = len(results['routes'])
        route_success_rate = (route_success / route_total) * 100 if route_total > 0 else 0
    else:
        route_success_rate = 100 if results['routes'] else 0
    
    # Summary
    print("\n" + "=" * 60)
    print("🎯 VERIFICATION SUMMARY")
    print("=" * 60)
    
    print(f"Flask App Running: {'✅' if results['flask_app'] else '❌'}")
    print(f"Route Tests: {'✅' if results['routes'] else '❌'} ({route_success_rate:.1f}% success)")
    print(f"Blueprint Registration: {'✅' if results['blueprint'] else '❌'}")
    print(f"Navigation Integration: {'✅' if results['navigation'] else '❌'}")
    print(f"Restoration Guide: {'✅' if results['guide'] else '❌'}")
    
    # Overall status
    critical_tests = [results['flask_app'], results['routes'], results['blueprint']]
    if all(critical_tests):
        print("\n🎉 SUCCESS! Partial Pending Blueprint is working correctly!")
        print("✅ All critical tests passed")
        print("🌐 Access: http://127.0.0.1:5001/partial-pending/")
        print("📋 See PARTIAL_DC_RESTORATION_GUIDE.md for next steps")
    else:
        print("\n⚠️  PARTIAL SUCCESS - Some issues remain")
        print("💡 Check individual test results above")
    
    print("\n🔗 TEST URLS:")
    print("   Main Dashboard: http://127.0.0.1:5001/partial-pending/")
    print("   Test API: http://127.0.0.1:5001/partial-pending/test")
    print("   Status: http://127.0.0.1:5001/partial-pending/status")
    print("=" * 60)

if __name__ == "__main__":
    main()
