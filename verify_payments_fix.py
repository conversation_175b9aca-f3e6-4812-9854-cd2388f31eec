#!/usr/bin/env python3
"""
Verify that the payments table fix resolves the financial reports error
"""

def verify_payments_fix():
    """Verify payments table fix"""
    print("🔍 VERIFYING PAYMENTS TABLE FIX")
    print("=" * 60)
    
    fixes_verified = True
    
    # 1. Database Table Verification
    print("🗄️ VERIFYING PAYMENTS TABLE")
    print("=" * 50)
    
    try:
        import sqlite3
        db = sqlite3.connect('instance/medivent.db')
        db.row_factory = sqlite3.Row
        
        # Check if payments table exists
        tables = db.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='payments'").fetchall()
        
        if tables:
            print("✅ Payments table exists")
            
            # Check schema
            schema = db.execute("PRAGMA table_info(payments)").fetchall()
            required_columns = ['payment_id', 'amount', 'payment_date', 'payment_method', 'status']
            existing_columns = [col[1] for col in schema]
            
            for col in required_columns:
                if col in existing_columns:
                    print(f"✅ Column '{col}' exists")
                else:
                    print(f"❌ Column '{col}' missing")
                    fixes_verified = False
            
            # Check data
            count = db.execute("SELECT COUNT(*) as count FROM payments").fetchone()[0]
            if count > 0:
                print(f"✅ Table has {count} payment records")
            else:
                print("⚠️ Table is empty (but this is OK for testing)")
                
        else:
            print("❌ Payments table does not exist")
            fixes_verified = False
        
        db.close()
        
    except Exception as e:
        print(f"❌ Database verification error: {e}")
        fixes_verified = False
    
    # 2. Financial Report Queries Verification
    print(f"\n💰 VERIFYING FINANCIAL REPORT QUERIES")
    print("=" * 50)
    
    try:
        import sqlite3
        db = sqlite3.connect('instance/medivent.db')
        db.row_factory = sqlite3.Row
        
        # Test queries from advanced_payment.py
        test_queries = [
            {
                'name': 'Monthly payments total',
                'query': "SELECT COALESCE(SUM(amount), 0) as total FROM payments WHERE DATE(payment_date) >= DATE('now', '-30 days')"
            },
            {
                'name': 'Payment methods breakdown',
                'query': """SELECT payment_method, COUNT(*) as count, SUM(amount) as total
                           FROM payments
                           WHERE DATE(payment_date) >= DATE('now', '-30 days')
                           GROUP BY payment_method
                           ORDER BY total DESC"""
            },
            {
                'name': 'Payment statistics',
                'query': """SELECT 
                            COUNT(*) as total_payments,
                            SUM(amount) as total_amount,
                            AVG(amount) as avg_amount
                           FROM payments
                           WHERE DATE(payment_date) >= DATE('now', '-30 days')"""
            },
            {
                'name': 'Payment trends',
                'query': """SELECT strftime('%Y-%m', payment_date) as month,
                                  COUNT(*) as payment_count,
                                  SUM(amount) as total_amount
                           FROM payments
                           WHERE payment_date >= DATE('now', '-12 months')
                           GROUP BY strftime('%Y-%m', payment_date)
                           ORDER BY month"""
            }
        ]
        
        for test in test_queries:
            try:
                result = db.execute(test['query']).fetchall()
                print(f"✅ {test['name']}: Query executed successfully ({len(result)} rows)")
            except Exception as e:
                print(f"❌ {test['name']}: Query failed - {e}")
                fixes_verified = False
        
        db.close()
        
    except Exception as e:
        print(f"❌ Query verification error: {e}")
        fixes_verified = False
    
    # 3. Flask App Integration Test
    print(f"\n🔧 VERIFYING FLASK APP INTEGRATION")
    print("=" * 50)
    
    try:
        # Test if the advanced_payment blueprint can be imported
        from routes.advanced_payment import advanced_payment_bp
        print("✅ Advanced payment blueprint imported successfully")
        
        # Test if the app can be created (this will test if all routes work)
        from app import app
        print("✅ Flask app created successfully")
        
        with app.app_context():
            # Test if we can access the database through the app
            from utils.db import get_db
            db = get_db()
            
            # Test a simple payment query
            result = db.execute("SELECT COUNT(*) as count FROM payments").fetchone()
            print(f"✅ Database accessible through Flask app: {result[0]} payments")
            
    except Exception as e:
        print(f"❌ Flask app integration error: {e}")
        fixes_verified = False
    
    # 4. Summary
    print(f"\n📊 VERIFICATION SUMMARY")
    print("=" * 50)
    
    if fixes_verified:
        print("🎉 ALL PAYMENTS TABLE FIXES VERIFIED SUCCESSFULLY!")
        print("✅ Payments table exists with correct schema")
        print("✅ Financial report queries work without errors")
        print("✅ Flask app integration successful")
        print("\n🚀 Financial reports should now work without 'no such table: payments' error!")
        print("   • Advanced payment dashboard should load")
        print("   • Payment reconciliation should work")
        print("   • Payment analytics should display data")
        print("   • API endpoints should return payment statistics")
    else:
        print("❌ SOME FIXES FAILED - MANUAL REVIEW REQUIRED")
        print("   Please check the errors above and fix them manually")
    
    return fixes_verified

if __name__ == "__main__":
    verify_payments_fix()
