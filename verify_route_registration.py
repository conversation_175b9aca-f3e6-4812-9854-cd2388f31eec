#!/usr/bin/env python3
"""
Verify route registration for product management
"""

def verify_routes():
    """Check if routes are properly registered"""
    try:
        from app import app
        
        print("🔍 VERIFYING ROUTE REGISTRATION")
        print("=" * 50)
        
        # Get all registered routes
        rules = list(app.url_map.iter_rules())
        
        # Check for product_management routes
        mgmt_routes = []
        for rule in rules:
            if 'product_management' in rule.rule or 'product_management' in rule.endpoint:
                mgmt_routes.append({
                    'rule': rule.rule,
                    'endpoint': rule.endpoint,
                    'methods': list(rule.methods)
                })
        
        print(f"📋 Product Management Routes Found: {len(mgmt_routes)}")
        for route in mgmt_routes:
            print(f"   ✅ {route['rule']:<30} → {route['endpoint']} {route['methods']}")
        
        # Check for direct route
        direct_routes = [r for r in mgmt_routes if r['rule'] == '/product_management']
        if direct_routes:
            print(f"\n✅ Direct route '/product_management' is registered")
        else:
            print(f"\n❌ Direct route '/product_management' is NOT registered")
        
        # Check for blueprint route
        blueprint_routes = [r for r in mgmt_routes if 'products.product_management' in r['endpoint']]
        if blueprint_routes:
            print(f"✅ Blueprint route 'products.product_management' is registered")
        else:
            print(f"❌ Blueprint route 'products.product_management' is NOT registered")
        
        return len(mgmt_routes) >= 2  # Should have both direct and blueprint routes
        
    except Exception as e:
        print(f"❌ Error verifying routes: {e}")
        return False

def test_route_accessibility():
    """Test if routes are accessible"""
    print(f"\n🧪 TESTING ROUTE ACCESSIBILITY")
    print("=" * 50)
    
    try:
        import requests
        
        # Test direct route
        try:
            response = requests.get('http://127.0.0.1:5001/product_management', timeout=5)
            if response.status_code == 200:
                print("✅ Direct route '/product_management' is accessible")
            elif response.status_code == 302:
                print("✅ Direct route '/product_management' redirects properly")
            else:
                print(f"⚠️ Direct route returned status: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"❌ Direct route test failed: {e}")
        
        # Test blueprint route
        try:
            response = requests.get('http://127.0.0.1:5001/products/product_management/', timeout=5)
            if response.status_code == 200:
                print("✅ Blueprint route '/products/product_management/' is accessible")
            elif response.status_code == 302:
                print("✅ Blueprint route '/products/product_management/' redirects (likely auth)")
            else:
                print(f"⚠️ Blueprint route returned status: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"❌ Blueprint route test failed: {e}")
        
        return True
        
    except ImportError:
        print("⚠️ requests module not available, skipping accessibility test")
        return True
    except Exception as e:
        print(f"❌ Accessibility test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 ROUTE VERIFICATION TEST")
    print("=" * 60)
    
    registration_ok = verify_routes()
    accessibility_ok = test_route_accessibility()
    
    if registration_ok and accessibility_ok:
        print(f"\n🎉 ALL TESTS PASSED!")
        print("✅ Routes are properly registered and accessible")
    else:
        print(f"\n⚠️ SOME TESTS FAILED")
        if not registration_ok:
            print("❌ Route registration issues detected")
        if not accessibility_ok:
            print("❌ Route accessibility issues detected")
