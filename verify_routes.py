#!/usr/bin/env python3
"""
Verify that the rider routes are properly defined and accessible
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.getcwd())

def test_route_definitions():
    """Test that routes are properly defined in the blueprint"""
    print("🔍 Testing route definitions...")
    
    try:
        # Import the blueprint
        from routes.modern_riders import riders_bp
        print("✅ Successfully imported riders_bp blueprint")
        
        # Check blueprint properties
        print(f"📋 Blueprint name: {riders_bp.name}")
        print(f"📋 Blueprint url_prefix: {riders_bp.url_prefix}")
        
        # Get all routes from the blueprint
        routes = []
        for rule in riders_bp.url_map.iter_rules():
            routes.append({
                'rule': rule.rule,
                'endpoint': rule.endpoint,
                'methods': list(rule.methods)
            })
        
        print(f"📋 Found {len(routes)} routes in blueprint:")
        for route in routes:
            print(f"  🛣️ {route['rule']} -> {route['endpoint']} [{', '.join(route['methods'])}]")
        
        # Check for specific routes we're looking for
        target_routes = ['/assignment-dashboard', '/self-pickup', '/reports']
        found_routes = [route['rule'] for route in routes]
        
        print("\n🎯 Checking target routes:")
        for target in target_routes:
            if target in found_routes:
                print(f"  ✅ {target} - FOUND")
            else:
                print(f"  ❌ {target} - NOT FOUND")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing route definitions: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_flask_app_creation():
    """Test creating a minimal Flask app with the blueprint"""
    print("\n🔍 Testing Flask app creation with blueprint...")
    
    try:
        from flask import Flask
        from routes.modern_riders import riders_bp
        
        # Create minimal Flask app
        app = Flask(__name__)
        app.secret_key = 'test-key'
        
        # Register the blueprint
        app.register_blueprint(riders_bp)
        print("✅ Successfully registered riders_bp blueprint")
        
        # Test route listing
        with app.app_context():
            print("🛣️ All registered routes:")
            for rule in app.url_map.iter_rules():
                print(f"  {rule.rule} -> {rule.endpoint} [{', '.join(rule.methods)}]")
        
        # Check for our specific routes
        target_routes = ['/riders/assignment-dashboard', '/riders/self-pickup', '/riders/reports']
        app_routes = [rule.rule for rule in app.url_map.iter_rules()]
        
        print("\n🎯 Checking target routes in app:")
        all_found = True
        for target in target_routes:
            if target in app_routes:
                print(f"  ✅ {target} - FOUND")
            else:
                print(f"  ❌ {target} - NOT FOUND")
                all_found = False
        
        return all_found
        
    except Exception as e:
        print(f"❌ Error testing Flask app creation: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🧪 Starting route verification tests...\n")
    
    # Test 1: Route definitions
    test1_passed = test_route_definitions()
    
    # Test 2: Flask app creation
    test2_passed = test_flask_app_creation()
    
    # Summary
    print("\n" + "="*50)
    print("📊 TEST SUMMARY:")
    print(f"  Route Definitions: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"  Flask App Creation: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 ALL TESTS PASSED!")
        print("🔧 The routes are properly defined. The issue is likely:")
        print("   1. Wrong port (use http://localhost:5000 instead of 3000)")
        print("   2. Flask app not running")
        print("   3. Database connection issues")
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("🔧 There are issues with the route definitions or imports.")

if __name__ == "__main__":
    main()
