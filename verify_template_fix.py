#!/usr/bin/env python3
"""
Verify that the template syntax fix resolved the hold_count error
"""

import os
import re

def verify_template_fix():
    """Verify the template syntax fix"""
    
    print("🔍 TEMPLATE SYNTAX VERIFICATION")
    print("=" * 50)
    
    template_path = "templates/finance/modern_dashboard.html"
    
    if not os.path.exists(template_path):
        print(f"❌ Template not found: {template_path}")
        return False
    
    print(f"✅ Template found: {template_path}")
    
    # Read the template content
    with open(template_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for the specific line that was causing the error
    lines = content.split('\n')
    
    # Look for the problematic line around line 526
    problem_found = False
    fix_verified = False
    
    for i, line in enumerate(lines, 1):
        # Check for the div tag that was missing the closing >
        if 'clickable-card' in line and 'held-invoices' in line:
            print(f"📍 Found target line {i}: {line.strip()}")
            
            # Check if it has proper closing >
            if line.strip().endswith('">'):
                print("✅ Line properly closed with '>'")
                fix_verified = True
            elif line.strip().endswith('"'):
                print("❌ Line missing closing '>' - SYNTAX ERROR!")
                problem_found = True
            else:
                print(f"⚠️ Unexpected line ending: {line.strip()[-10:]}")
    
    # Check for hold_count usage
    hold_count_usage = []
    for i, line in enumerate(lines, 1):
        if 'hold_count' in line:
            hold_count_usage.append((i, line.strip()))
    
    print(f"\n📊 Found {len(hold_count_usage)} references to hold_count:")
    for line_num, line_content in hold_count_usage:
        print(f"   Line {line_num}: {line_content}")
    
    # Check for any unclosed tags
    unclosed_tags = []
    tag_pattern = r'<(\w+)[^>]*(?<!/)[^>]*$'  # Tags that don't end with > or />
    
    for i, line in enumerate(lines, 1):
        matches = re.findall(tag_pattern, line.strip())
        if matches:
            unclosed_tags.append((i, line.strip()))
    
    if unclosed_tags:
        print(f"\n⚠️ Found {len(unclosed_tags)} potentially unclosed tags:")
        for line_num, line_content in unclosed_tags[:5]:  # Show first 5
            print(f"   Line {line_num}: {line_content}")
    else:
        print("\n✅ No obviously unclosed tags found")
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 VERIFICATION SUMMARY")
    print("=" * 50)
    
    if problem_found:
        print("❌ SYNTAX ERROR STILL EXISTS!")
        return False
    elif fix_verified:
        print("✅ SYNTAX FIX VERIFIED!")
        print("✅ Template should render without 'hold_count' error")
        return True
    else:
        print("⚠️ Target line not found - template may have changed")
        return False

if __name__ == "__main__":
    success = verify_template_fix()
    if success:
        print("\n🎉 Template fix verification PASSED!")
        print("🔧 The 'dict object has no attribute hold_count' error should be resolved")
    else:
        print("\n💥 Template fix verification FAILED!")
        print("🔧 The error may still exist")
