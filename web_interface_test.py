#!/usr/bin/env python3
"""
Test the exact web interface order creation to identify the discrepancy
"""

import requests
import time

def test_web_interface_order_creation():
    """Test order creation exactly as the web interface does it"""
    print("🧪 Testing Web Interface Order Creation")
    print("=" * 60)
    
    base_url = "http://localhost:5001"
    
    try:
        # Step 1: Get the order creation page to establish session
        print("1. Getting order creation page...")
        session = requests.Session()
        response = session.get(f"{base_url}/orders/new", timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code != 200:
            print("❌ Cannot access order creation page")
            return False
        
        # Step 2: Submit order data exactly like the web form
        print("2. Submitting order data...")
        order_data = {
            'customer_name': 'Web Interface Test Customer',
            'customer_address': 'Web Interface Test Address',
            'customer_phone': '555-WEB-TEST',
            'payment_method': 'cash',
            'po_number': 'WEB-TEST-001',
            'product_id[]': ['P001'],
            'quantity[]': ['1'],
            'foc_quantity[]': ['0']
        }
        
        response = session.post(f"{base_url}/orders/new", 
                               data=order_data, 
                               timeout=15,
                               allow_redirects=False)
        
        print(f"   Response Status: {response.status_code}")
        print(f"   Response Headers: {dict(response.headers)}")
        
        if response.status_code == 302:
            redirect_url = response.headers.get('Location', '')
            print(f"   Redirected to: {redirect_url}")
            
            if '/orders/' in redirect_url:
                print("✅ Order created successfully!")
                order_id = redirect_url.split('/orders/')[-1].split('/')[0]
                print(f"   Order ID: {order_id}")
                return True
            else:
                print("⚠️  Redirected but not to order page")
                return False
                
        elif response.status_code == 200:
            # Check if there's an error message in the response
            response_text = response.text
            if "UNIQUE constraint failed" in response_text:
                print("❌ UNIQUE constraint error found in response!")
                print("   This confirms the web interface is still using old code")
                return False
            elif "Error placing order" in response_text:
                print("❌ Order placement error found")
                # Extract error message
                import re
                error_match = re.search(r'Error placing order: ([^"]+)', response_text)
                if error_match:
                    error_msg = error_match.group(1)
                    print(f"   Error: {error_msg}")
                return False
            else:
                print("⚠️  Unexpected 200 response")
                return False
        else:
            print(f"❌ Unexpected status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error during test: {e}")
        return False

def test_order_id_generation_directly():
    """Test order ID generation directly through the web interface"""
    print("\n🔧 Testing Order ID Generation Directly")
    print("=" * 60)
    
    try:
        # Test the generate_order_id function directly
        import sys
        sys.path.append('.')
        
        from app import app
        with app.app_context():
            from routes.orders import generate_order_id
            
            print("Generating 3 test order IDs:")
            for i in range(3):
                order_id = generate_order_id()
                print(f"  {i+1}. {order_id}")
            
            print("✅ Direct order ID generation working!")
            return True
            
    except Exception as e:
        print(f"❌ Error in direct generation: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_database_for_recent_orders():
    """Check database for recent orders to see what's happening"""
    print("\n📊 Checking Database for Recent Orders")
    print("=" * 60)
    
    try:
        import sqlite3
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        # Get recent orders
        cursor.execute('''
            SELECT order_id, customer_name, status, order_date 
            FROM orders 
            ORDER BY order_date DESC 
            LIMIT 10
        ''')
        
        orders = cursor.fetchall()
        print(f"Found {len(orders)} recent orders:")
        
        for order_id, customer, status, date in orders:
            print(f"  {order_id}: {customer} - {status} ({date})")
        
        # Check for any orders with "Web Interface Test" in the name
        cursor.execute('''
            SELECT order_id, customer_name, status, order_date 
            FROM orders 
            WHERE customer_name LIKE '%Web Interface%' OR customer_name LIKE '%Test%'
            ORDER BY order_date DESC
        ''')
        
        test_orders = cursor.fetchall()
        if test_orders:
            print(f"\nFound {len(test_orders)} test orders:")
            for order_id, customer, status, date in test_orders:
                print(f"  {order_id}: {customer} - {status} ({date})")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def main():
    """Run all tests to identify the discrepancy"""
    print("🔍 INVESTIGATING WEB INTERFACE vs TERMINAL DISCREPANCY")
    print("=" * 80)
    
    # Test 1: Direct order ID generation
    direct_test = test_order_id_generation_directly()
    
    # Test 2: Web interface order creation
    web_test = test_web_interface_order_creation()
    
    # Test 3: Check database
    db_test = check_database_for_recent_orders()
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 INVESTIGATION SUMMARY")
    print("=" * 80)
    print(f"Direct Order ID Generation: {'✅ WORKING' if direct_test else '❌ FAILED'}")
    print(f"Web Interface Order Creation: {'✅ WORKING' if web_test else '❌ FAILED'}")
    print(f"Database Check: {'✅ WORKING' if db_test else '❌ FAILED'}")
    
    if direct_test and not web_test:
        print("\n🔍 DIAGNOSIS:")
        print("- Direct order ID generation works (terminal tests pass)")
        print("- Web interface fails (browser shows UNIQUE constraint error)")
        print("- This suggests the Flask server is using cached/old code")
        print("\n💡 RECOMMENDED ACTIONS:")
        print("1. Clear browser cache completely")
        print("2. Restart Flask server with --no-reload flag")
        print("3. Check if there are multiple Flask processes running")
        print("4. Verify the correct routes/orders.py is being imported")
    elif direct_test and web_test:
        print("\n✅ DIAGNOSIS: Both systems working correctly!")
        print("The discrepancy may have been resolved.")
    else:
        print("\n❌ DIAGNOSIS: Deeper investigation needed")
        print("Both direct and web interface have issues")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
